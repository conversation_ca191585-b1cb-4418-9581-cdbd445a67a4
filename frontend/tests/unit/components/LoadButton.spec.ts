import { mount } from "@vue/test-utils";
import { createP<PERSON>, setActive<PERSON><PERSON> } from "pinia";
import { beforeEach, describe, expect, it } from "vitest";
import LoadButton from "@/components/LoadButton.vue";
import globalSetup from "../../playwright/global-setup";

describe("loadButton.vue", () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    globalSetup(false);
  });

  it("should execute handler when the onClick method is called", async () => {
    let called = false;
    const wrapper = mount(LoadButton, {
      propsData: {
        handler: () => {
          called = true;
        },
      },
      global: {
        stubs: ["router-link", "router-view"],
      },
    });
    await wrapper.find("button")?.trigger("click");
    expect(called).toBe(true);
  });

  it("should emit a click event when the onClick method is called", async () => {
    const wrapper = mount(LoadButton, {
      propsData: {
        handler: () => {
          return null;
        },
      },
      global: {
        stubs: ["router-link", "router-view"],
      },
    });
    await wrapper.find("button")?.trigger("click");
    expect(wrapper.emitted()!.click!.length).toBe(1);
  });
});
