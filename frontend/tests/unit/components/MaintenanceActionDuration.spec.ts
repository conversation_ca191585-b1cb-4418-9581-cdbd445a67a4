import { shallowMount } from "@vue/test-utils";
import { describe, expect, it } from "vitest";
import { MaintenanceActionStatus } from "@/api/backend/generated/enums";
import { IGetMaintenanceActionResponse } from "@/api/backend/generated/responses";
import MaintenanceActionDuration from "@/components/MaintenanceActionDuration.vue";

describe("maintenanceActionDuration.vue", async () => {
  it("should show 'Session ended while action was running' when session stops while action is still running", () => {
    const wrapper = shallowMount(MaintenanceActionDuration, {
      propsData: {
        action: {
          status: MaintenanceActionStatus.PerformingAction,
          startTime: new Date().toDateString(),
        } as IGetMaintenanceActionResponse,
        isSessionRunning: false,
      },
    });

    expect(wrapper.text()).contains("Session ended while action was running");
  });
});
