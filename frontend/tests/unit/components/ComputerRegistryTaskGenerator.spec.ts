import { mount, shallowMount } from "@vue/test-utils";
import { createPinia, setActivePinia } from "pinia";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useRouter } from "vue-router";
import { IRegistryValueDto } from "@/api/backend/generated/interfaces";
import { IGetLocalScriptResponse } from "@/api/backend/generated/responses";
import ComputerRegistryTaskGenerator from "@/components/ComputerRegistryTaskGenerator.vue";
import ImmyInput from "@/components/ImmyInput.vue";
import MonacoSimpleView from "@/components/MonacoSimpleView.vue";
import { until } from "@/utils/misc";
import globalSetup from "../../playwright/global-setup";

vi.mock("@/api/backend/v1", (o) => {
  const mockScriptResponse: IGetLocalScriptResponse = {
    owned: false,
    scriptType: 1,
    name: "BCD00000000",
    id: 24,
    action:
      "[CmdletBinding()]\nparam\n(\n  [string]$KeyName = 'BCD00000000',\n  [int]$System = 1,\n  [int]$TreatAsSystem = 1,\n  [byte[]]$GuidCache = @(0x5d, 0x36, 0x7e, 0x2a, 0x55, 0x38, 0xda, 0x01, 0x09, 0x27, 0x00, 0x00, 0x09, 0x9f, 0x00, 0x15, 0x5d, 0x02, 0x35, 0x00, 0x89, 0x00, 0x00, 0x00)\n)\nprocess\n{\n  Get-WindowsRegistryValue -Path 'HKLM:\\BCD00000000\\Description' -Name 'KeyName' | RegistryShould-Be -Value $KeyName;\n  Get-WindowsRegistryValue -Path 'HKLM:\\BCD00000000\\Description' -Name 'System' | RegistryShould-Be -Value $System;\n  Get-WindowsRegistryValue -Path 'HKLM:\\BCD00000000\\Description' -Name 'TreatAsSystem' | RegistryShould-Be -Value $TreatAsSystem;\n  Get-WindowsRegistryValue -Path 'HKLM:\\BCD00000000\\Description' -Name 'GuidCache' | RegistryShould-Be -Value $GuidCache;\n}",
    scriptLanguage: 2,
    scriptExecutionContext: 2,
    scriptCategory: 3,
    outputType: 0,
    updatedDateUTC: "2024-04-10T18:27:05.167326Z",
    createdDateUTC: "2024-04-10T18:27:05.167326Z",
    updatedBy: "",
  };

  return {
    ...o,
    scriptsApi: {
      createLocalScript: vi.fn().mockReturnValue(Promise.resolve(mockScriptResponse)),
    },
  };
});
describe("computerRegistryTaskGenerator.vue", () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    globalSetup(false);
  });

  it("should show instructional text when no values are selected", () => {
    const wrapper = shallowMount(ComputerRegistryTaskGenerator, {
      props: {
        registryValues: [],
      },
    });

    expect(wrapper.text()).toContain("Select at least one registry value to");
    expect(wrapper.findComponent(MonacoSimpleView).exists()).toBe(false);
  });

  it("should generate script when values are selected", () => {
    const wrapper = shallowMount(ComputerRegistryTaskGenerator, {
      props: {
        registryValues: getRegistryValues(),
      },
    });

    expect(wrapper.text()).not.toContain("Select at least one registry value to");
    expect(wrapper.findComponent(MonacoSimpleView).exists()).toBe(true);

    const currentScript = wrapper.vm.getCurrentScript();
    expect(currentScript).toBe(generatedScript);
  });

  it("should show error validation when trying to convert to task with no script name entered", async () => {
    const wrapper = mount(ComputerRegistryTaskGenerator, {
      props: {
        registryValues: getRegistryValues(),
      },
    });

    const router = useRouter();
    const invalidFeedback = wrapper.find(".invalid-feedback");

    expect(invalidFeedback.classes()).not.toContain("d-block");

    const convertButton = wrapper.findAll("button").find(b => b.text() === "Convert to Task");
    await convertButton?.trigger("click");

    expect(invalidFeedback.classes()).toContain("d-block");
    expect(router.push).not.toHaveBeenCalled();
  });

  it("should make api call to create script and navigate to new task when script name is entered and convert button is clicked", async () => {
    const wrapper = mount(ComputerRegistryTaskGenerator, {
      props: {
        registryValues: getRegistryValues(),
      },
    });

    const router = useRouter();

    const immyInput = wrapper.findComponent(ImmyInput);
    const input = wrapper.find("input");

    await input.setValue("Configure BCD00000000 Key");

    await until(() => immyInput.props().state === true);

    const convertButton = wrapper.findAll("button").find(b => b.text() === "Convert to Task");
    await convertButton?.trigger("click");

    const invalidFeedback = wrapper.find(".invalid-feedback");
    expect(invalidFeedback.classes()).not.toContain("d-block");
    expect(router.push).toHaveBeenCalledWith({
      name: "New Task",
      query: {
        maintenanceTaskType: 1,
        setScriptId: 24,
        setScriptType: 1,
        useScriptParamBlock: "true",
      },
    });
  });
});

function getRegistryValues(): IRegistryValueDto[] {
  return [
    {
      binaryValue: undefined,
      dWordValue: undefined,
      kind: 1,
      multiStringValue: undefined,
      parentKeyPath: "HKEY_LOCAL_MACHINE\\BCD00000000\\Description",
      qWordValue: undefined,
      stringValue: "BCD00000000",
      valueName: "KeyName",
    },
    {
      binaryValue: undefined,
      dWordValue: 1,
      kind: 4,
      multiStringValue: undefined,
      parentKeyPath: "HKEY_LOCAL_MACHINE\\BCD00000000\\Description",
      qWordValue: undefined,
      stringValue: undefined,
      valueName: "System",
    },
    {
      binaryValue: undefined,
      dWordValue: 1,
      kind: 4,
      multiStringValue: undefined,
      parentKeyPath: "HKEY_LOCAL_MACHINE\\BCD00000000\\Description",
      qWordValue: undefined,
      stringValue: undefined,
      valueName: "TreatAsSystem",
    },
    {
      binaryValue: "XTZ+KlU42gEJJwAACZ8AFV0CNQCJAAAA",
      dWordValue: undefined,
      kind: 3,
      multiStringValue: undefined,
      parentKeyPath: "HKEY_LOCAL_MACHINE\\BCD00000000\\Description",
      qWordValue: undefined,
      stringValue: undefined,
      valueName: "GuidCache",
    },
  ];
}

const generatedScript = `[CmdletBinding()]
param
(
  [string]$KeyName = 'BCD00000000',
  [int]$System = 1,
  [int]$TreatAsSystem = 1,
  [byte[]]$GuidCache = @(0x5d, 0x36, 0x7e, 0x2a, 0x55, 0x38, 0xda, 0x01, 0x09, 0x27, 0x00, 0x00, 0x09, 0x9f, 0x00, 0x15, 0x5d, 0x02, 0x35, 0x00, 0x89, 0x00, 0x00, 0x00)
)
process
{
  Get-WindowsRegistryValue -Path 'HKLM:\\BCD00000000\\Description' -Name 'KeyName' | RegistryShould-Be -Value $KeyName;
  Get-WindowsRegistryValue -Path 'HKLM:\\BCD00000000\\Description' -Name 'System' | RegistryShould-Be -Value $System;
  Get-WindowsRegistryValue -Path 'HKLM:\\BCD00000000\\Description' -Name 'TreatAsSystem' | RegistryShould-Be -Value $TreatAsSystem;
  Get-WindowsRegistryValue -Path 'HKLM:\\BCD00000000\\Description' -Name 'GuidCache' | RegistryShould-Be -Value $GuidCache;
}`;
