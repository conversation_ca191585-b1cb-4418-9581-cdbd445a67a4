import { mount, shallowMount } from "@vue/test-utils";
import { DxLoadIndicator } from "devextreme-vue/load-indicator";
import { createPinia, setActivePinia } from "pinia";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { RegistrySearchResultType } from "@/api/backend/generated/enums";
import ComputerRegistrySearchResultsPane from "@/components/ComputerRegistrySearchResultsPane.vue";
import { until } from "@/utils/misc";
import globalSetup from "../../playwright/global-setup";

describe("computerRegistrySearchResultsPane.vue", () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    globalSetup(false);

    // Mock useAuthStore to return a valid store object
    vi.mock("@/store/pinia/auth-store", () => ({
      useAuthStore: vi.fn(() => ({
        hasClaim: vi.fn().mockReturnValue(true),
      })),
    }));
  });

  it("should show progress ring if search is in progress", () => {
    const wrapper = shallowMount(ComputerRegistrySearchResultsPane, {
      props: {
        isSearchInProgress: true,
      },
    });

    const loadIndicator = wrapper.findComponent(DxLoadIndicator);
    expect(loadIndicator.exists()).toBe(true);
  });

  it("should hide progress ring if search is not in progress", () => {
    const wrapper = shallowMount(ComputerRegistrySearchResultsPane, {
      props: {
        isSearchInProgress: false,
      },
    });

    const loadIndicator = wrapper.findComponent(DxLoadIndicator);
    expect(loadIndicator.exists()).toBe(false);
  });

  it("should emit closed event when closed button clicked", async () => {
    const wrapper = mount(ComputerRegistrySearchResultsPane, {
      global: {
        stubs: ["router-link", "router-view"],
      },
      props: {
        isSearchInProgress: false,
      },
    });

    const closeButton = wrapper.findAll("button").find(a => a.attributes("title") === "Close");
    expect(closeButton?.exists()).toBe(true);
    await closeButton?.trigger("click");

    expect(wrapper?.emitted("closed")?.length).toBe(1);
  });

  it("should emit selectionChanged event when selection is clicked", async () => {
    const keyPath = "HKEY_LOCAL_MACHINE\\SOFTWARE\\voidtools\\Everything";
    const wrapper = mount(ComputerRegistrySearchResultsPane, {
      props: {
        isSearchInProgress: false,
        searchResults: [
          {
            fullKeyPath: keyPath,
            type: RegistrySearchResultType.Key,
          },
        ],
      },
    });

    await until(() => {
      return !!wrapper.findAll("td").find(x => x.text() === keyPath);
    });

    const cell = wrapper.findAll("td").find(x => x.text() === keyPath);

    expect(cell).not.toBeNull();

    const row = cell?.element.parentElement;
    expect(row).not.toBeNull();
    row!.click();

    const emitted = wrapper.emitted("selectionChanged");
    expect(emitted!.length).toBe(1);
    expect((emitted![0][0] as any).fullKeyPath).toBe(keyPath);
  });
});
