import { shallowMount } from "@vue/test-utils";
import { describe, expect, it } from "vitest";
import ProgressLog from "@/components/ProgressLog.vue";

describe("progressLog.vue", () => {
  it("should not show seconds remaining text when there no seconds remaining", async () => {
    const wrapper = shallowMount(ProgressLog, {
      propsData: {
        log: {
          progressSecondsRemaining: 0,
        },
      },
    });
    const val = wrapper.find(".seconds-remaining-text").exists();
    expect(val).toBe(false);
  });

  it("should show seconds remaining text when there are seconds remaining", async () => {
    const wrapper = shallowMount(ProgressLog, {
      propsData: {
        log: {
          progressSecondsRemaining: 1,
        },
      },
    });
    const val = wrapper.find(".seconds-remaining-text").exists();
    expect(val).toBe(true);
  });

  it("should show progress bar when percent complete is > 0", async () => {
    const wrapper = shallowMount(ProgressLog, {
      propsData: {
        log: {
          progressPercentComplete: 1,
        },
      },
    });
    const val = wrapper.find(".prog-bar").exists();
    expect(val).toBe(true);
  });

  it("should not show progress bar when percent complete is not > 0", async () => {
    const wrapper = shallowMount(ProgressLog, {
      propsData: {
        log: {
          progressPercentComplete: 0,
        },
      },
    });
    const val = wrapper.find(".prog-bar").exists();
    expect(val).toBe(false);
  });
});
