import { mount } from "@vue/test-utils";
import { describe, expect, it, vi } from "vitest";
import SidebarNavDropdown from "@/components/SidebarNavDropdown.vue";

vi.mock("vue-router");

describe("sidebarNavDropdown.vue", () => {
  it("should stay open if child is clicked", async () => {
    const $routeMock = {
      path: "/reporting/computers",
    };

    const wrapper = mount(SidebarNavDropdown, {
      propsData: {
        item: {
          name: "Reporting",
          id: "reporting",
          url: "/reporting",
          permissions: [],
          children: [
            {
              name: "Computers",
              id: "computers",
              url: "/reporting/computers",
              permissions: [],
            },
            {
              name: "Computer Inventory Scripts",
              id: "computer-inventory-scripts",
              url: "/reporting/computer-inventory-scripts",
              permissions: [],
            },
            {
              name: "Computer Software",
              id: "computer-software",
              url: "/reporting/detected-computer-software",
              permissions: [],
            },
          ],
        },
      },
      global: {
        stubs: ["RouterLink"],
        mocks: {
          $route: $routeMock,
        },
      },
    });

    await wrapper.find("li.im-sidebar-nav-item").trigger("click");
    const item = wrapper.get(".im-sidebar-nav-item");
    expect(item.classes()).toContain("dropdown-active");
  });
});
