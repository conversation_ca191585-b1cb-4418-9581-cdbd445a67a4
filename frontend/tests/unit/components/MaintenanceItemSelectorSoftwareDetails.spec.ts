import { createTesting<PERSON><PERSON> } from "@pinia/testing";
import { shallowMount } from "@vue/test-utils";
import flushPromises from "flush-promises";
import { describe, expect, it } from "vitest";
import {
  DesiredSoftwareState,
  SoftwareProviderType,
  SoftwareType,
} from "@/api/backend/generated/contracts";
import SoftwareDetails, { ISoftwareDetails } from "@/components/MaintenanceItemSelectorSoftwareDetails.vue";
import { getLastEventPayload } from "../../helpers";

process.on("unhandledRejection", console.warn);

const defaultProps = {
  softwareIdentifier: "1",
  softwareType: 0,
  softwareVersionSelectorLabel: "version",
  ignoreSoftwareVersion: false,
  ignoreSoftwareProviderType: false,
  ignoreSoftwareDesiredState: false,
  allowVersionRestriction: true,
  modelValue: {
    desiredSoftwareState: DesiredSoftwareState.LatestVersion,
    softwareProviderType: null,
    softwareSemanticVersion: null,
    restrictToMajorVersion: false,
    restrictToSpecificVersion: false,
    providerLinkIdForMaintenanceItem: null,
  } as ISoftwareDetails,
};

describe("maintenanceItemSelectorSoftwareDetails.vue", () => {
  it("should return expected update:modelValue with no initial props", async () => {
    const wrapper = shallowMount(SoftwareDetails, {
      props: {
        softwareIdentifier: "1",
        softwareType: SoftwareType.LocalSoftware,
      },
      global: {
        plugins: [createTestingPinia()],
      },
    });
    await flushPromises(); // wait for async component create to finish

    const inputEvent = getLastEventPayload(wrapper, "update:modelValue");
    expect(inputEvent).not.toBeUndefined();
    expect(inputEvent).toHaveProperty("desiredSoftwareState", DesiredSoftwareState.LatestVersion);
    expect(inputEvent).toHaveProperty("softwareSemanticVersion", null);
    expect(inputEvent).toHaveProperty("softwareProviderType", null);
    expect(inputEvent).toHaveProperty("restrictToMajorVersion", false);
    expect(inputEvent).toHaveProperty("restrictToSpecificVersion", false);
    expect(inputEvent).toHaveProperty("providerLinkIdForMaintenanceItem", null);
  });

  it("should emit expected update:modelValue", async () => {
    const wrapper = shallowMount(SoftwareDetails, {
      props: {
        softwareIdentifier: "1",
        softwareType: SoftwareType.LocalSoftware,
      },
      global: {
        plugins: [createTestingPinia()],
      },
    });
    await flushPromises(); // wait for async component create to finish

    // trigger update of model that will trigger an update:modelValue event;
    await wrapper.setProps({
      modelValue: {
        desiredSoftwareState: DesiredSoftwareState.ThisVersion,
        softwareSemanticVersion: "*******",
        softwareProviderType: null,
        restrictToMajorVersion: false,
        restrictToSpecificVersion: false,
        providerLinkIdForMaintenanceItem: null,
      },
    });

    const prop = wrapper.props().modelValue;
    expect(prop).not.toBeUndefined();
    expect(prop).toHaveProperty("desiredSoftwareState", DesiredSoftwareState.ThisVersion);
    expect(prop).toHaveProperty("softwareSemanticVersion", "*******");
    expect(prop).toHaveProperty("restrictToMajorVersion", false);
    expect(prop).toHaveProperty("restrictToSpecificVersion", false);
  });

  it("should not change desiredSoftwareState to LATEST during initialization", async () => {
    const wrapper = shallowMount(SoftwareDetails, {
      props: Object.assign({}, defaultProps, {
        modelValue: {
          softwareSemanticVersion: "4.5.8.1",
          desiredSoftwareState: DesiredSoftwareState.NewerOrEqualVersion,
          softwareProviderType: null,
        },
      }),
    });
    await flushPromises(); // wait for async component create to finish
    expect(wrapper.vm.model).toMatchObject({
      softwareSemanticVersion: "4.5.8.1",
      desiredSoftwareState: DesiredSoftwareState.NewerOrEqualVersion, // should remain the same as what it was to begin with
      softwareProviderType: null,
    });
  });

  it("should not include softwareSemanticVersion in input event when ignoreSoftwareVersion is true", async () => {
    const wrapper = shallowMount(SoftwareDetails, {
      props: Object.assign({}, defaultProps, {
        ignoreSoftwareVersion: true,
      }),
    });
    await flushPromises(); // wait for async component create to finish

    // trigger update of model that will trigger an input event
    // include softwareSemanticVersion which should get stripped out of the input event
    await wrapper.setData({
      softwareSemanticVersion: "*******",
      desiredSoftwareState: DesiredSoftwareState.NewerOrEqualVersion,
    });
    const inputEvent = getLastEventPayload(wrapper, "update:modelValue");
    expect(inputEvent).not.toBeUndefined();
    expect(inputEvent).toHaveProperty(
      "desiredSoftwareState",
      DesiredSoftwareState.NewerOrEqualVersion,
    );
    expect(inputEvent).toHaveProperty("softwareSemanticVersion", null);
  });

  it("should not include softwareProviderType in input event when ignoreSoftwareProviderType is true", async () => {
    const wrapper = shallowMount(SoftwareDetails, {
      props: Object.assign({}, defaultProps, {
        ignoreSoftwareProviderType: true,
      }),
    });
    await flushPromises(); // wait for async component create to finish

    // trigger update of model that will trigger an input event
    // include softwareProviderType which should get stripped out of the input event
    await wrapper.setData({
      softwareSemanticVersion: "*******",
      desiredSoftwareState: DesiredSoftwareState.NewerOrEqualVersion,
      softwareProviderType: SoftwareProviderType.Inherent,
    });
    const inputEvent = getLastEventPayload(wrapper, "update:modelValue");
    expect(inputEvent).not.toBeUndefined();
    expect(inputEvent).toHaveProperty(
      "desiredSoftwareState",
      DesiredSoftwareState.NewerOrEqualVersion,
    );
    expect(inputEvent).toHaveProperty("softwareProviderType", null);
  });
});
