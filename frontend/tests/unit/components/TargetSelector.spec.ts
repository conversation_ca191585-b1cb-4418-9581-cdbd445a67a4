import { describe, expect, it } from "vitest";

// todo: add a test for "Should show tenant selector options when targetCategory is 1 (tenant)"
// an integration test would be "Should show tenant selector options when a tenant task is selected"
describe("targetSelector.vue", () => {
  it("should show tenant selector options whent the target category is tenant", async () => {
    expect(true).toBe(true);
  });
});
