import { createTesting<PERSON><PERSON> } from "@pinia/testing";
import { mount } from "@vue/test-utils";
import { createPinia, setActivePinia } from "pinia";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { TargetCategory, TargetEnforcement } from "@/api/backend/generated/enums";
import Deployment from "@/components/Deployment.vue";
import DeploymentOptions from "@/components/DeploymentOptions.vue";
import { MaintenanceItemSelectorDetails } from "@/components/MaintenanceItemSelector.vue";
import { TargetSelectorModel } from "@/components/TargetSelector.vue";
import { makeUid } from "@/utils/misc";
import globalSetup from "../../playwright/global-setup";

describe("deployment.vue", () => {
  beforeEach(() => {
    // creates a fresh pinia and make it active so it's automatically picked
    // up by any useStore() call without having to pass it to it:
    // `useStore(pinia)`
    setActivePinia(createPinia());
    globalSetup(false);

    // fully mock the user hub event instance
    vi.mock("@/api/backend/signalr-hubs/UserHubEventInstance", () => {
      const instance = {
        joinSessionGroupGroup: vi.fn(),
        onEvent: vi.fn(),
        onHubReconnected: vi.fn(),
        // ... any other methods you need
      };
      return {
        default: vi.fn(() => instance),
      };
    });
  });

  it.each([true, false])("should hide/show options when specified", (showDeploymentOptions) => {
    const uid = makeUid();
    const wrapper = mount(Deployment, {
      global: {
        stubs: ["router-link", "router-view"],
        plugins: [
          createTestingPinia({
            initialState: {
              [`deploymentStore/${uid}`]: {
                showDeploymentOptions,
              },
            },
          }),
        ],
      },
      props: {
        storeId: uid,
        targetCategory: TargetCategory.Computer,
        panelTitle: "Computers",
        targetEnforcement: TargetEnforcement.Required,
        licenseId: undefined,
        licenseRequirement: undefined,
        deploymentId: undefined,
        deploymentType: undefined,
        targetData: {} as TargetSelectorModel,
        maintenanceItemData: {} as MaintenanceItemSelectorDetails,
        rebootNeeded: false,
      },
    });

    expect(wrapper.findComponent(DeploymentOptions).exists()).toBe(showDeploymentOptions);
  });
});
