import { mount } from "@vue/test-utils";
import { createPinia, setActive<PERSON><PERSON> } from "pinia";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { IGetComputerResponse, IGetUserResponse } from "@/api/backend/generated/responses";
import { computersApi } from "@/api/backend/v1";
import ComputerRegistryDetails from "@/components/ComputerRegistryDetails.vue";
import ImmyInput from "@/components/ImmyInput.vue";
import { DEFAULT_PROMPT_TIMEOUT_MINUTES } from "@/utils/constants";
import globalSetup from "../../playwright/global-setup";

describe("computerRegistryDetails.vue", () => {
  vi.mock("@/api/backend/v1", (o) => {
    return {
      ...o,
      computersApi: {
        loadRegistryKeys: vi.fn(),
      },
      gettingStartedApi: {
        completeChecklist: vi.fn(),
      },
    };
  });

  beforeEach(() => {
    globalSetup();
    vi.restoreAllMocks();
    setActivePinia(createPinia());
  });

  it("should load base registry keys on mount if computer is not null", () => {
    const computer = getComputer();
    mount(ComputerRegistryDetails, {
      propsData: {
        computer,
      },
      global: {
        stubs: ["router-link", "router-view"],
      },
    });

    expect(computersApi.loadRegistryKeys).toHaveBeenCalledOnce();
    expect(computersApi.loadRegistryKeys).toHaveBeenCalledWith(computer.id, { keyPath: "" });
  });

  it("should not load subkeys when nav input is changed", () => {
    const computer = getComputer();
    const wrapper = mount(ComputerRegistryDetails, {
      propsData: {
        computer,
      },
      global: {
        stubs: ["router-link", "router-view"],
      },
    });

    vi.restoreAllMocks();

    const immyInputNav = wrapper.findComponent(ImmyInput);
    const input = immyInputNav.findComponent("input");
    input.element.nodeValue = "HKEY_LOCAL_MACHINE\\SOFTWARE";
    input.trigger("input");

    // This should start failing after we add navigation.
    expect(computersApi.loadRegistryKeys).not.toBeCalled();
  });

  it("should not call computers API if computer is null", () => {
    mount(ComputerRegistryDetails, {
      propsData: {
        computer: undefined,
      },
      global: {
        stubs: ["router-link", "router-view"],
      },
    });

    expect(computersApi.loadRegistryKeys).not.toHaveBeenCalled();
  });
});

function getComputer() {
  const computer: IGetComputerResponse = {
    id: 1118,
    deviceId: "64fcccc7-db0b-4740-95fe-292411033229",
    tenantId: 1,
    computerName: "JG-W11VM",
    primaryPersonId: 1,
    onboardingStatus: 2,
    isOnline: true,
    isDomainController: false,
    isPortable: false,
    isServer: false,
    isDesktop: true,
    tenantName: "Immense Networks",
    isMissingSomeRequiredInventoryResults: false,
    detectionOutdated: false,
    isSandbox: false,
    excludeFromMaintenance: false,
    isDevLab: false,
    devLabVmUnclaimed: false,
    ephemeralAgentConnected: true,
    licensed: false,
    additionalPersons: [],
    inventory: {},
    sessions: [],
    excludedFromUserAffinity: false,
    primaryPerson: {
      id: 1,
      tenantId: 1,
      azurePrincipalId: "a108d115-550d-4521-83d4-e5f0f88868e2",
      firstName: "Jared",
      lastName: "Goodwin",
      emailAddress: "<EMAIL>",
      displayName: "Jared Goodwin",
      updatedDateUTC: "2023-12-27T20:45:40.16164Z",
      createdDateUTC: "2023-12-27T20:45:40.16164Z",
      additionalComputers: [],
      primaryComputers: [],
      userAffinities: [],
      user: {} as IGetUserResponse,
    },
    tenant: {
      id: 1,
      name: "Immense Networks",
      slug: "",
      ownerTenantId: 3,
      active: true,
      azureTenantLink: {
        immyTenantId: 1,
        azTenantId: "b992c3df-0c32-4cdf-abd2-f64e86513c7b",
        azureTenant: {
          principalId: "b992c3df-0c32-4cdf-abd2-f64e86513c7b",
          consentDetails: {
            consentedWith: 0,
            consentDateUtc: "",
          },
          azureTenantType: 1,
          partnerPrincipalId: "",
          infoSyncedFromAzure: {
            tenantName: "Immense Networks",
            defaultDomainName: "immense.net",
            domainNames: ["immense.net"],
          },
          lastGetTenantInfoSyncResult: {
            attemptDateUtc: "2024-04-02T17:25:37.36907Z",
            attemptFailedErrorId: "19887d16-5d3b-4b30-afd7-4cdf6755af87",
          },
          lastGetUsersSyncResult: {
            attemptDateUtc: "2024-04-02T17:25:37.702579Z",
            attemptFailedErrorId: "df8d12c4-fcea-4311-a807-6c5eaa81a00c",
          },
        },
        shouldLimitDomains: false,
        limitToDomains: [],
      },
      isMsp: true,
      updatedBy: 3,
      updatedDateUTC: "2023-12-27T20:41:20.811225Z",
      createdBy: 3,
      createdDateUTC: "2023-12-27T20:41:20.811225Z",
      tenantTagIds: [],
      tenantTagNames: [],
    },
    agents: [
      {
        agentVersion: "*********",
        id: 1119,
        computerId: 1118,
        providerLinkId: 12,
        externalClientName: "Immense Networks",
        externalClientId: "1",
        externalAgentId: "6d81655c-c849-4898-8580-27679c4409c6",
        runScriptPriority: 1,
        providerTypeId: "fcde1b0b-38a2-41b3-86d6-e8bd0ad52171",
        internalData: {
          AgentInstallerID: "4ba55b3e-10d2-4711-a7b0-14a106453bdf",
        },
        isOnline: true,
        supportsRunningScripts: true,
        lastUpdatedUTC: "2024-04-02T15:26:47.577689Z",
        deviceDetails: {
          operatingSystemName: "Microsoft Windows 11 Pro 64-bit",
          manufacturer: "Microsoft Corporation",
          deviceName: "JG-W11VM",
          serialNumber: "0324-2863-6296-2136-3447-2430-45",
          deviceId: "1921145d-4730-416b-aa10-634cde558e0b",
          osInstallDateUTC: "2023-12-26T15:44:44Z",
          machineId: "d414721f-41ca-40c0-9e31-b52d30d81f92",
          isSandbox: false,
          domain: "",
          azureTenantId: "",
          chassisTypes: [9],
        },
        onboardingOptions: {
          automaticallyOnboard: true,
          onboardingCorrelationId: "7dbd4bd7-7b94-4814-a9b9-7915268a9dd3",
          onboardingSessionRebootPreference: 0,
          onboardingSessionSendFollowUpEmail: false,
          primaryPersonId: 1,
          additionalPersonIds: [],
          tags: [],
          isDevLab: false,
          onboardingSessionPromptTimeoutMinutes: DEFAULT_PROMPT_TIMEOUT_MINUTES,
          onboardingSessionAutoConsentToReboots: false,
        },
        requireManualIdentification: false,
      },
    ],
    computerTagIds: [],
  };

  return computer;
}
