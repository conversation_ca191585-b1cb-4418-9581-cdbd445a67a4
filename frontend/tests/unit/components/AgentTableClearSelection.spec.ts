import { beforeEach, describe, expect, it, vi } from "vitest";

const mockClearSelection = vi.fn();
const mockImmyAgentTable = { value: { clearSelection: mockClearSelection } };

function clearSelection() {
  const table = mockImmyAgentTable.value as { clearSelection: () => void } | undefined;
  if (table) {
    table.clearSelection();
  }
}

describe("agentTable clearSelection", () => {
  beforeEach(() => {
    mockClearSelection.mockClear();
  });

  it("should not throw an error when clearSelection is called with undefined immyAgentTable", () => {
    mockImmyAgentTable.value = undefined as any;
    expect(() => {
      clearSelection();
    }).not.toThrow();
  });

  it("should call clearSelection on immyAgentTable when it exists", () => {
    mockImmyAgentTable.value = {
      clearSelection: mockClearSelection,
    };
    clearSelection();
    expect(mockClearSelection).toHaveBeenCalled();
  });
});
