import { describe, expect, it } from "vitest";
import { IGetMaintenanceSessionLogResponse } from "@/api/backend/generated/contracts";

import { CorrelatedLogsLog, receiveUncorrelatedLog } from "@/utils/sessionLogs";

describe("sessionLogs.ts", () => {
  describe(receiveUncorrelatedLog.name, () => {
    it("should combine logs with the same progressCorrelationId", () => {
      const log1 = {
        time: "2",
        progressCorrelationId: "abc",
        scriptOutput: "abc",
      } as IGetMaintenanceSessionLogResponse;
      const log2 = {
        time: "3",
        progressCorrelationId: "abc",
        scriptOutput: "def",
      } as IGetMaintenanceSessionLogResponse;
      const map = new Map<string, CorrelatedLogsLog>();
      const logs: (IGetMaintenanceSessionLogResponse | CorrelatedLogsLog)[] = [];
      receiveUncorrelatedLog(map, logs, log1);
      receiveUncorrelatedLog(map, logs, log2);

      expect(logs).toEqual([
        {
          time: "2",
          progressCorrelationId: "abc",
          scriptOutput: "abcdef",
          orderedLogs: [log1, log2],
          updatedTime: "3",
        },
      ]);
    });
    it("should keep logs in order", () => {
      const log1 = {
        time: "2",
        progressCorrelationId: "abc",
        scriptOutput: "abc",
      } as IGetMaintenanceSessionLogResponse;
      const log2 = {
        time: "3",
        progressCorrelationId: "abc",
        scriptOutput: "def",
      } as IGetMaintenanceSessionLogResponse;
      const log3 = { time: "4", scriptOutput: "foobar" } as IGetMaintenanceSessionLogResponse;
      const log4 = { time: "0", progressCorrelationId: "def" } as IGetMaintenanceSessionLogResponse;
      const log5 = {
        time: "1",
        progressCorrelationId: "abc",
        scriptOutput: "ghi",
      } as IGetMaintenanceSessionLogResponse;
      const map = new Map<string, CorrelatedLogsLog>();
      const logs: (IGetMaintenanceSessionLogResponse | CorrelatedLogsLog)[] = [];
      receiveUncorrelatedLog(map, logs, log1);
      receiveUncorrelatedLog(map, logs, log2);
      receiveUncorrelatedLog(map, logs, log3);
      receiveUncorrelatedLog(map, logs, log4);
      receiveUncorrelatedLog(map, logs, log5);

      expect(logs).toEqual([
        {
          time: "0",
          progressCorrelationId: "def",
          orderedLogs: [log4],
        },
        {
          time: "1",
          progressCorrelationId: "abc",
          updatedTime: "3",
          scriptOutput: "ghiabcdef",
          orderedLogs: [log5, log1, log2],
        },
        { time: "4", scriptOutput: "foobar" },
      ]);
    });
  });
});
