/**
 * Home for unit tests of all functions in misc.js
 */
import {
  describe,
  expect,
  it,
} from "vitest";
import {
  DatabaseType,
  DesiredSoftwareState,
  IOauth2AccessToken,
  MaintenanceType,
  SoftwareType,
} from "@/api/backend/generated/contracts";
import {
  databaseTypeFromMaintenanceType,
  emailRegex,
  getAuthProviderName,
  isGuid,
  isVersionRequiredForDesiredState,
  maintenanceTypeFromMaintenanceTaskType,
  maintenanceTypeFromSoftwareType,
  sleep,
  softwareTypeFromMaintenanceType,
  toHHMMSS,
  wildcardMatch,
} from "@/utils/misc";

describe("utils.js", () => {
  describe(wildcardMatch.name, () => {
    it.each([
      [true, "testing", "test*"],
      [true, "CloudRadial Agent 4", "CloudRadial Agent*"],
    ])("should return '%s' when input is '%s' and rule is '%s'", (expected, input, rule) => {
      const actual = wildcardMatch(input, rule);
      expect(actual).toBe(expected);
    });
  });

  describe(isVersionRequiredForDesiredState.name, () => {
    it.each([
      [true, DesiredSoftwareState.NewerOrEqualVersion],
      [true, DesiredSoftwareState.ThisVersion],
      [true, DesiredSoftwareState.OlderOrEqualVersion],
    ])("should return '%s' when desired state is %s", (expected, desiredState) => {
      const actual = isVersionRequiredForDesiredState(desiredState);
      expect(actual).toBe(expected);
    });
  });

  describe(isGuid.name, () => {
    it.each([
      [true, "1297062b-3718-421c-989f-792191c0411a"],
      [false, "1297062b3718421c989f792191c0411a"],
      [false, "123456"],
    ])("should return true when input is a guid", (expected, input) => {
      const actual = isGuid(input);
      expect(actual).toBe(expected);
    });
  });

  describe(softwareTypeFromMaintenanceType.name, () => {
    it("should return SoftwareType.LocalSoftware given MaintenanceType.LocalSoftware", () => {
      const actual = softwareTypeFromMaintenanceType(MaintenanceType.LocalSoftware);
      expect(actual).toBe(SoftwareType.LocalSoftware);
    });
    it("should return SoftwareType.GlobalSoftware given MaintenanceType.GlobalSoftware", () => {
      const actual = softwareTypeFromMaintenanceType(MaintenanceType.GlobalSoftware);
      expect(actual).toBe(SoftwareType.GlobalSoftware);
    });
    it("should return SoftwareType.Ninite given MaintenanceType.NiniteSoftware", () => {
      const actual = softwareTypeFromMaintenanceType(MaintenanceType.NiniteSoftware);
      expect(actual).toBe(SoftwareType.Ninite);
    });
    it("should return SoftwareType.Chocolatey given MaintenanceType.ChocolateySoftware", () => {
      const actual = softwareTypeFromMaintenanceType(MaintenanceType.ChocolateySoftware);
      expect(actual).toBe(SoftwareType.Chocolatey);
    });
  });

  describe(databaseTypeFromMaintenanceType.name, () => {
    it("should return DatabaeType.Local given MaintenanceType.LocalMaintenanceTask", () => {
      const actual = databaseTypeFromMaintenanceType(MaintenanceType.LocalMaintenanceTask);
      expect(actual).toBe(DatabaseType.Local);
    });
    it("should return DatabaseType.Global given MaintenanceType.GlobalMaintenanceTask", () => {
      const actual = databaseTypeFromMaintenanceType(MaintenanceType.GlobalMaintenanceTask);
      expect(actual).toBe(DatabaseType.Global);
    });
  });

  describe(maintenanceTypeFromSoftwareType.name, () => {
    it("should return MaintenanceType.LocalSoftware given SoftwareType.LocalSoftware", () => {
      const actual = maintenanceTypeFromSoftwareType(SoftwareType.LocalSoftware);
      expect(actual).toBe(MaintenanceType.LocalSoftware);
    });
    it("should return MaintenanceType.GlobalSoftware given SoftwareType.GlobalSoftware", () => {
      const actual = maintenanceTypeFromSoftwareType(SoftwareType.GlobalSoftware);
      expect(actual).toBe(MaintenanceType.GlobalSoftware);
    });
  });

  describe(maintenanceTypeFromMaintenanceTaskType.name, () => {
    it("should return MaintenanceType.LocalMaintenanceTask given DatabaseType.Local", () => {
      const actual = maintenanceTypeFromMaintenanceTaskType(DatabaseType.Local);
      expect(actual).toBe(MaintenanceType.LocalMaintenanceTask);
    });
    it("should return MaintenanceType.GlobalMaintenanceTask given DatabaseType.Global", () => {
      const actual = maintenanceTypeFromMaintenanceTaskType(DatabaseType.Global);
      expect(actual).toBe(MaintenanceType.GlobalMaintenanceTask);
    });
  });

  describe(toHHMMSS.name, () => {
    it.each([
      [0, "00:00:00"],
      [null, "00:00:00"],
      [undefined, "00:00:00"],
      [-2046, "00:00:00"],
      [100, "00:01:40"],
      [333959, "92:45:59"],
      [9.554, "00:00:10"],
      [9.44543345, "00:00:09"],
    ])("when input is '%s', should return '%s'", (input, expected) => {
      const actual = toHHMMSS(input);
      expect(actual).toBe(expected);
    });
  });

  describe("email regex", () => {
    it.each([
      ["<EMAIL>", true],
      ["foo@bar", false],
      ["foo@bar.", false],
      ["foo@bar.c", false],
    ])("when input is '%s', should return '%s'", (input, expected) => {
      const actual = emailRegex.test(input);
      expect(actual).toBe(expected);
    });
  });

  function getOauth2AccessToken(authorizationEndpoint: string) {
    const data: IOauth2AccessToken = {
      id: 0,
      consentData: {
        authorizationEndpoint,
        tokenEndpoint: "",
        clientId: "",
        extraQueryParameters: {},
      },
      allowSilentRefresh: false,
      accessTokenId: "",
      tokenType: "",
      accessTokenExpiresAtUtc: "",
      tenantPrincipalId: "",
      createdDate: "",
      updatedDate: "",
    };
    return data;
  }

  describe(getAuthProviderName.name, () => {
    it("should allow host login.microsoftonline.com", () => {
      const data = getOauth2AccessToken("https://login.microsoftonline.com");
      const actual = getAuthProviderName(data);
      expect(actual).toBe("Entra ID");
    });

    it.each([
      "https://foobar.com",
      "https://login.microsoftonline2.com",
    ])("should return null when input is not login.microsoftonline.com", (authorizationEndpoint) => {
      const data = getOauth2AccessToken(authorizationEndpoint);
      const actual = getAuthProviderName(data);
      expect(actual).toBe(null);
    });
  });

  describe(sleep.name, () => {
    it.each([100, 500, 1000])("should wait at least %s milliseconds before returning", async (ms) => {
      const startTime = Date.now();
      await sleep(ms);
      const endTime = Date.now();

      const elapsedMilliseconds = endTime - startTime;
      console.debug(elapsedMilliseconds);
      expect(elapsedMilliseconds).toBeGreaterThanOrEqual(ms - 20); // May need to adjusted due to flakey test
    });
  });
});
