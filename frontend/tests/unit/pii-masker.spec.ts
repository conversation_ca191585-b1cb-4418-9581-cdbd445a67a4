import { afterEach, beforeEach, describe, expect, it } from "vitest";
import { PIIMasker } from "@/utils/pii-masker";

describe("pIIMasker", () => {
  let masker: PIIMasker;
  let testContainer: HTMLElement;

  beforeEach(() => {
    // Create a fresh test container before each test
    testContainer = document.createElement("div");
    document.body.appendChild(testContainer);
  });

  afterEach(() => {
    // Clean up after each test
    if (masker && masker.isActive()) {
      masker.stop();
    }

    // Remove the test container
    document.body.removeChild(testContainer);
  });

  it("should mask PII content with the default settings", () => {
    // Arrange: Set up the test element with PII class
    testContainer.innerHTML = "<span class=\"pii\"><PERSON></span>";
    const piiElement = testContainer.querySelector(".pii") as HTMLElement;

    // Create a masker with default settings
    masker = new PIIMasker();

    // Initial state assertion
    expect(piiElement.textContent).toBe("<PERSON>");

    // Act: Start the masker
    masker.start();

    // Assert: Check that the content has been masked correctly
    expect(piiElement.textContent).toBe("(PII) Jo** S****");
  });

  it("should use custom prefix when provided", () => {
    // Arrange: Set up the test element with PII class
    testContainer.innerHTML = "<span class=\"pii\">Jane Doe</span>";
    const piiElement = testContainer.querySelector(".pii") as HTMLElement;

    // Create a masker with custom prefix
    const customPrefix = "[REDACTED] ";
    masker = new PIIMasker(["pii"], customPrefix);

    // Act: Start the masker
    masker.start();

    // Assert: Check that the content has been masked with custom prefix
    expect(piiElement.textContent).toBe("[REDACTED] Ja** D**");
  });

  it("should respect custom character masking length", () => {
    // Arrange: Set up the test element with PII class
    testContainer.innerHTML = "<span class=\"pii\">Alice Johnson</span>";
    const piiElement = testContainer.querySelector(".pii") as HTMLElement;

    // Create a masker with custom chars to keep (only show first character)
    masker = new PIIMasker(["pii"], "(PII) ", 1);

    // Act: Start the masker
    masker.start();

    // Assert: Check that only first character is visible
    expect(piiElement.textContent).toBe("(PII) A**** J******");
  });

  it("should handle multiple PII elements on the page", () => {
    // Arrange: Set up multiple test elements with PII class
    testContainer.innerHTML = `
      <div>
        <span class="pii">Bob Williams</span>
        <p>Some regular text</p>
        <span class="pii">Carol Taylor</span>
      </div>
    `;

    const piiElements = testContainer.querySelectorAll(".pii");

    // Create a masker with default settings
    masker = new PIIMasker();

    // Act: Start the masker
    masker.start();

    // Assert: Check that all PII elements are masked
    expect(piiElements[0].textContent).toBe("(PII) Bo* W*******");
    expect(piiElements[1].textContent).toBe("(PII) Ca*** T*****");
  });

  it("should handle custom class names", () => {
    // Arrange: Set up test elements with custom classes
    testContainer.innerHTML = `
      <div>
        <span class="sensitive-info">David Miller</span>
        <span class="pii">This should not be masked</span>
        <span class="confidential">Emily Wilson</span>
      </div>
    `;

    // Create a masker with custom class names
    masker = new PIIMasker(["sensitive-info", "confidential"]);

    // Act: Start the masker
    masker.start();

    // Assert: Check that only elements with specified classes are masked
    expect(testContainer.querySelector(".sensitive-info")?.textContent).toBe("(PII) Da*** M*****");
    expect(testContainer.querySelector(".pii")?.textContent).toBe("This should not be masked");
    expect(testContainer.querySelector(".confidential")?.textContent).toBe("(PII) Em*** W*****");
  });

  it("should handle the mutation observer for dynamically added elements", async () => {
    // Create a masker with default settings
    masker = new PIIMasker();

    // Start the masker before adding elements
    masker.start();

    // Act: Dynamically add an element with PII class
    const newElement = document.createElement("span");
    newElement.className = "pii";
    newElement.textContent = "Frank Thomas";
    testContainer.appendChild(newElement);

    // Wait for mutation observer to process
    await new Promise(resolve => setTimeout(resolve, 0));

    // Assert: Check that dynamically added element is masked
    expect(newElement.textContent).toBe("(PII) Fr*** T*****");
  });

  it("should stop masking when stop() is called", () => {
    // Arrange: Set up the test element with PII class
    testContainer.innerHTML = "<span class=\"pii\">Grace Lee</span>";

    // Create and start masker
    masker = new PIIMasker();
    masker.start();

    // Verify masking is active
    const piiElement = testContainer.querySelector(".pii") as HTMLElement;
    expect(piiElement.textContent).toBe("(PII) Gr*** L**");

    // Act: Stop the masker
    masker.stop();

    // Add a new element - this should not be masked now
    const newElement = document.createElement("span");
    newElement.className = "pii";
    newElement.textContent = "Henry Clark";
    testContainer.appendChild(newElement);

    // Assert: New element should not be masked
    expect(newElement.textContent).toBe("Henry Clark");

    // Also verify masker reports it's not active
    expect(masker.isActive()).toBe(false);
  });

  it("should not re-mask elements that are already masked", () => {
    // Arrange: Set up the test element with PII class
    testContainer.innerHTML = "<span class=\"pii\">Irene Adams</span>";
    const piiElement = testContainer.querySelector(".pii") as HTMLElement;

    // Create and start masker
    masker = new PIIMasker();
    masker.start();

    // Verify first masking
    expect(piiElement.textContent).toBe("(PII) Ir*** A****");

    // Capture current text
    const maskedText = piiElement.textContent;

    // Act: Run masker again
    masker.stop();
    masker.start();

    // Assert: Text should not be double-masked
    expect(piiElement.textContent).toBe(maskedText);
  });

  it("should ignore empty text and whitespace", () => {
    // Arrange: Set up test elements with empty text and whitespace
    testContainer.innerHTML = `
      <span class="pii"></span>
      <span class="pii"> </span>
      <span class="pii">\n</span>
    `;

    const emptyElement = testContainer.querySelectorAll(".pii")[0];
    const shortElement = testContainer.querySelectorAll(".pii")[1];
    const singleCharElement = testContainer.querySelectorAll(".pii")[2];

    // Create and start masker (with 3 chars to keep)
    masker = new PIIMasker();
    masker.start();

    // Assert: Empty text should just get prefix, short text within char limit should remain unmasked
    expect(emptyElement.textContent).toBe("");
    expect(shortElement.textContent).toBe(" ");
    expect(singleCharElement.textContent).toBe("\n");
  });

  it("should handle short text content", () => {
    // Arrange: Set up test elements with short or empty text
    testContainer.innerHTML = `
      <span class="pii"></span>
      <span class="pii">Hi</span>
      <span class="pii">a</span>
    `;

    const emptyElement = testContainer.querySelectorAll(".pii")[0];
    const shortElement = testContainer.querySelectorAll(".pii")[1];
    const singleCharElement = testContainer.querySelectorAll(".pii")[2];

    // Create and start masker (with 3 chars to keep)
    masker = new PIIMasker();
    masker.start();

    // Assert: Empty text should just get prefix, short text within char limit should remain unmasked
    expect(emptyElement.textContent).toBe("");
    expect(shortElement.textContent).toBe("(PII) Hi");
    expect(singleCharElement.textContent).toBe("(PII) a");
  });

  it("should handle words with special characters and numbers", () => {
    // Arrange: Set up test element with special characters
    testContainer.innerHTML = "<span class=\"pii\"><EMAIL> ************</span>";
    const piiElement = testContainer.querySelector(".pii") as HTMLElement;

    // Create and start masker
    masker = new PIIMasker();
    masker.start();

    // Assert: Special characters should be handled properly
    expect(piiElement.textContent).toBe("(PII) Jo****************** 1***********");
  });
});
