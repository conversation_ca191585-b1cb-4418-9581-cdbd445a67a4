import { createP<PERSON>, setActivePinia } from "pinia";
import { beforeEach, describe, expect, it } from "vitest";
import { useDeploymentStore } from "@/store/pinia/deployment-store";
import { makeUid } from "@/utils/misc";
import globalSetup from "../../playwright/global-setup";

describe("deployment Store", () => {
  beforeEach(() => {
    // creates a fresh pinia and make it active so it's automatically picked
    // up by any useStore() call without having to pass it to it:
    // `useStore(pinia)`
    setActivePinia(createPinia());
    globalSetup(false);
  });

  it("should allow for creating multiple stores", () => {
    const uid = makeUid();
    const store = useDeploymentStore(uid);

    const uid2 = makeUid();
    const store2 = useDeploymentStore(uid2);

    expect(store).not.toBe(store2);
    expect(store.$id).toBe(`deploymentStore/${uid}`);
    expect(store2.$id).toBe(`deploymentStore/${uid2}`);
  });
});
