import { create<PERSON><PERSON>, setActive<PERSON><PERSON> } from "pinia";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useComputerDetailsPageStore } from "@/store/pinia/computer-details-page-store";

// Mock the backend API used by the store so we can control when the Promise resolves
vi.mock("@/api/backend/v1", () => {
  return {
    computersApi: {
      // this mock will be replaced per-test to control resolution timing
      getInventoryScriptResult: vi.fn(),
    },
  };
});

const { computersApi } = await import("@/api/backend/v1");

describe("computer-details-page-store navigation race condition", () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it("should not throw when the computer is reset to null before the async inventory request resolves", async () => {
    // Arrange: create store and set an initial computer value
    const store = useComputerDetailsPageStore();
    store.setComputer({ id: 1, inventory: {} } as any);

    // Control the Promise returned from the API call
    let resolveApi!: (value: any) => void;
    const apiPromise = new Promise((resolve) => {
      resolveApi = resolve;
    });
    (computersApi.getInventoryScriptResult as any).mockReturnValue(apiPromise);

    // Act: start refreshing the inventory key (async call in flight)
    const callPromise = store.refreshComputerInventoryKey("TestKey");

    // Simulate navigation away which resets the store state
    store.resetState();

    // Finish the API call after the state has been cleared
    resolveApi({});

    // Assert: the method should resolve without throwing
    await expect(callPromise).resolves.toBeUndefined();
    expect(store.computer).toBeNull();
  });
});
