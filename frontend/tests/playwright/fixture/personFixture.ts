import { faker } from "@faker-js/faker";
import { test as base, expect } from "@playwright/test";
import { IGetSimplePersonResponse } from "../../../src/api/backend/generated/responses";
import { makeUid } from "../../../src/utils/misc";

import {
  expect200Response,
  waitForCreatePersonResponse,
} from "../misc/api";
import {
  clickByTestId,
  clickSidebarPeopleLink,
  clickSidebarShowMoreLink,
} from "../misc/buttons";
import { fillPersonForm } from "../misc/inputs";

interface PersonFixture {
  personOperations: {
    create: (includeAzureId?: boolean) => Promise<IGetSimplePersonResponse>;
  };
}

const test = base.extend<PersonFixture>({
  personOperations: async ({ page }, use) => {
    // implement functions define in the fixture
    const create = async (includeAzureId: boolean = true) => {
      await clickSidebarShowMoreLink(page);
      await clickSidebarPeopleLink(page);
      await clickByTestId(page, "new-person-btn", "Click New Person");

      const randomName = `${faker.person.firstName()} ${faker.person.lastName()}`;
      const email = faker.internet.email();
      const azureId = includeAzureId ? makeUid() : null;

      await fillPersonForm(page, randomName, randomName, email, azureId);

      const wrapper = waitForCreatePersonResponse(page);
      await clickByTestId(page, "create-person-btn", "Click Create");

      const response = await wrapper.response;
      expect200Response(response);

      const res = await wrapper.getBody();
      expect(res.firstName).toBe(randomName);
      expect(res.lastName).toBe(randomName);
      expect(res.emailAddress).toBe(email);
      if (includeAzureId)
        expect(res.azurePrincipalId).toBe(azureId);

      return res;
    };

    // Provide the functions to the test that use the fixture
    await use({ create });
  },
});

export { test };
