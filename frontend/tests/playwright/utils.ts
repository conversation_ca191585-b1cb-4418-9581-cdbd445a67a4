import { test } from "@playwright/test";

export type ApiMethods<T> = keyof T;

export type RequireTestCoverage<T extends Record<string, any>> = {
  [K in keyof T]: (() => void)[];
};

/**
 * This base class forces you to implement the skeleton that covers all the methods in the provided api.
 * If you don't add any tests for a given method, it will show an error in the console.
 */
export abstract class BaseApiTestSuite<T extends Record<string, any>> {
  runTests() {
    const testImpl = this as RequireTestCoverage<Pick<T, ApiMethods<T>>>;
    Object.entries(testImpl).forEach(([methodName, implementation]) => {
      test.describe(methodName, async () => {
        if (implementation.length === 0) {
          test.skip("implement a test for me", () => {

          });
          return;
        }
        implementation.forEach((testFn) => {
          testFn();
        });
      });
    });
  }
}

export function getTestsForApi(api: string) {
  // dynamically load file
  const apiTests = () => import(`./api/${api}.ts`);
  return apiTests() ?? [];
}
