import { expect, Page, test } from "@playwright/test";
import {
  DetectionMethod,
  LicenseType,
  SoftwareLicenseRequirement,
  UpdateActionType,
} from "../../src/api/backend/generated/enums";
import { SoftwareApiRoutes } from "../../src/api/backend/generated/routes";
import { maintenanceTasksApi, softwareApi } from "../../src/api/backend/v1";
import { SoftwareApi } from "../../src/api/backend/v1/software-api";
import globalSetup from "./global-setup";
import {
  expect200Response,
  waitForCreateLocalMaintenanceTask,
  waitForMigrateLocalSoftwareResponse,
  waitForUpdateLocalSoftware,
} from "./misc/api";
import { clickByTestId } from "./misc/buttons";
import { animateCursorToElementAndClick } from "./misc/cursor";
import { addTaskParameter, fillTaskInfo } from "./misc/inputs";

async function createSoftware(name: string) {
  return await softwareApi.createLocalSoftware({
    detectionMethod: DetectionMethod.SoftwareTable,
    hidden: false,
    installOrder: 0,
    licenseRequirement: SoftwareLicenseRequirement.None,
    licenseType: LicenseType.None,
    name,
    rebootNeeded: false,
    recommended: false,
    testRequired: false,
    upgradeStrategy: UpdateActionType.None,
    useDynamicVersions: false,
    softwarePrerequisites: [],
    tenantSoftware: [],
  });
}

async function addPrerequisite(page: Page, softwareId: string, prerequisiteName: string) {
  await page.goto(`/library/software/${softwareId}?softwareType=1`);
  await clickByTestId(page, "new-prerequisite-btn", "Click New");

  // select in the dropdown
  await clickByTestId(page, "maintenance-item-selector-search", "Click Search");
  await animateCursorToElementAndClick(page, `#maintenance-name-option-${prerequisiteName}`, true, 500, "Select software");

  // update button
  await clickByTestId(page, "software-submit-btn", "Click Submit");
}

async function migrateSoftware(page: Page, softwareId: string, migrateButtonTestId: string) {
  const wrapper = waitForMigrateLocalSoftwareResponse(page, softwareId);
  await animateCursorToElementAndClick(page, `[data-testid="${migrateButtonTestId}"]`, true, 500, "Click Migrate");
  const response = await wrapper.response;
  expect200Response(response);
  return await response.text();
}

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("software", () => {
  test("create software", async ({ page }) => {
    const randomSoftwareName = Math.random().toString(36).substring(7);
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-library", true, 500, "Clicking Library");
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-library-software", true, 500, "Clicking Software");
    await animateCursorToElementAndClick(page, "[data-testid='newSoftware-button']", true, 500, "Clicking New Software");
    await animateCursorToElementAndClick(page, "[data-testid='option-None']", true, 500, "Clicking None");
    await animateCursorToElementAndClick(page, "#nextButtonFirstStep", true, 500, "Clicking Next");
    await animateCursorToElementAndClick(page, "[data-testid='addVersionToNewSoftware-button']", true, 500, "Clicking Add Version to New Software");
    await animateCursorToElementAndClick(page, "#software-name-input", true, 500, "Type software name");
    await page.keyboard.type(randomSoftwareName, { delay: 100 });
    await animateCursorToElementAndClick(page, "#nextButtonSecondStep", true, 500, "Clicking Next");
    await animateCursorToElementAndClick(page, "#version-input", true, 500, "Type version");
    await page.keyboard.type("1.0.0", { delay: 100 });

    const postPromise = page.waitForResponse(
      req =>
        req.request().method() === "POST"
        && req.url().endsWith(SoftwareApiRoutes.FastCreateLocalSoftwareVersion),
    );

    await animateCursorToElementAndClick(page, "[data-testid='create-button']", true, 500, "Clicking Create");

    const response = await postPromise;
    expect(response.status()).toBe(200);

    const res = await (response.json() as ReturnType<
      SoftwareApi["fastCreateLocalSoftwareVersion"]
    >);
    await softwareApi.deleteLocalSoftware(res.identifier);
  });

  test("update software with a new configuration task", async ({ page }) => {
    const randomSoftwareName = Math.random().toString(36).substring(7);
    const software = await softwareApi.createLocalSoftware({
      detectionMethod: DetectionMethod.SoftwareTable,
      hidden: false,
      installOrder: 0,
      licenseRequirement: SoftwareLicenseRequirement.None,
      licenseType: LicenseType.None,
      name: randomSoftwareName,
      rebootNeeded: false,
      recommended: false,
      testRequired: false,
      upgradeStrategy: UpdateActionType.None,
      useDynamicVersions: false,
      softwarePrerequisites: [],
      tenantSoftware: [],
    });

    await page.goto(
      `/library/software/${software.identifier}?softwareType=${software.softwareType}`,
    );

    // fill in task details
    await animateCursorToElementAndClick(page, "[data-testid='new-maintenance-task-link']", true, 500, "Clicking Maintenance Task");
    const randomTaskName = Math.random().toString(36).substring(7);
    const parameterName = "foobar";
    await fillTaskInfo(page, randomTaskName, randomTaskName, true, true);
    await animateCursorToElementAndClick(page, "[data-testid='add-task-parameter-btn']", true, 500, "Clicking Add Task Paramater");
    await addTaskParameter(page, parameterName);

    const createTaskWrapper = waitForCreateLocalMaintenanceTask(page);

    await animateCursorToElementAndClick(page, "[data-testid='embedded-maintenance-task-details-form-submit-btn']", true, 500, "Clicking Submit");

    const taskResponse = await createTaskWrapper.getBody();
    expect(taskResponse.name).toBe(randomTaskName);
    expect(taskResponse.parameters.length).toBe(1);
    expect(taskResponse.parameters[0].name).toBe(parameterName);

    const updateSoftwareWrapper = waitForUpdateLocalSoftware(page, software.identifier);
    await animateCursorToElementAndClick(page, "[data-testid='software-submit-btn']", true, 500, "Clicking Submit");

    const softwareResponse = await updateSoftwareWrapper.getBody();
    expect(taskResponse.id).toBe(softwareResponse.maintenanceTaskId);
    expect(taskResponse.databaseType).toBe(softwareResponse.maintenanceTaskType);

    await softwareApi.deleteLocalSoftware(software.identifier);
    await maintenanceTasksApi.deleteLocalMaintenanceTask(taskResponse.id);
  });

  test("delete software", async ({ page }) => {
    const randomSoftwareName = Math.random().toString(36).substring(7);
    const software = await softwareApi.createLocalSoftware({
      detectionMethod: DetectionMethod.SoftwareTable,
      hidden: false,
      installOrder: 0,
      licenseRequirement: SoftwareLicenseRequirement.None,
      licenseType: LicenseType.None,
      name: randomSoftwareName,
      rebootNeeded: false,
      recommended: false,
      testRequired: false,
      upgradeStrategy: UpdateActionType.None,
      useDynamicVersions: false,
      softwarePrerequisites: [],
      tenantSoftware: [],
    });

    await animateCursorToElementAndClick(page, "#sidebar-nav-item-library", true, 500, "Clicking Library");
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-library-software", true, 500, "Clicking Software");

    await animateCursorToElementAndClick(page, "#software-list [aria-label='Search in the data grid']", true, 500, "Clicking search box");
    await page.keyboard.type(randomSoftwareName, { delay: 100 });

    await page.waitForTimeout(1000);

    await animateCursorToElementAndClick(page, "#software-list button .fa-trash-can", true, 500, "Clicking delete button");

    const route = SoftwareApiRoutes.DeleteLocalSoftware.replace(
      "{softwareIdentifier}",
      software.identifier,
    );
    const deletePromise = page.waitForResponse(
      req => req.request().method() === "DELETE" && req.url().endsWith(route),
    );

    await animateCursorToElementAndClick(page, "button:has-text('Yes')", true, 500, "Confirming deletion");

    const response = await deletePromise;
    expect(response.status()).toBe(204);
  });

  test("migrate software", async ({ page }) => {
    const localFirstLevelName = Math.random().toString(36).substring(7);
    const localFirstLevel = await createSoftware(localFirstLevelName);

    const localSecondLevelName = Math.random().toString(36).substring(7);
    const localSecondLevel = await createSoftware(localSecondLevelName);

    const localThirdLevelName = Math.random().toString(36).substring(7);
    const localThirdLevel = await createSoftware(localThirdLevelName);

    // add prerequisites
    await addPrerequisite(page, localSecondLevel.identifier, localThirdLevelName);
    await addPrerequisite(page, localFirstLevel.identifier, localSecondLevelName);

    // open migration modal
    await clickByTestId(page, "more-actions-dropdown");
    await page.locator("text='Migrate to Global'").click();

    // verify that buttons that depends on other software are disabled
    await page.locator("[data-testid='immy-modal-confirm-btn']").isDisabled();
    await page.locator(`[data-testid='migrate-button-${localSecondLevelName}']`).isDisabled();

    // migrate from bottom to top in herarchy
    const globalThirdLevelId = await migrateSoftware(
      page,
      localThirdLevel.identifier,
      `migrate-button-${localThirdLevelName}`,
    );

    await page.locator("[data-testid='immy-modal-confirm-btn']").isDisabled();

    const globalSecondLevelId = await migrateSoftware(
      page,
      localSecondLevel.identifier,
      `migrate-button-${localSecondLevelName}`,
    );

    const globalFirstLevelId = await migrateSoftware(
      page,
      localFirstLevel.identifier,
      "immy-modal-confirm-btn",
    );

    // delete all softwares created
    await softwareApi.deleteGlobalSoftware(globalThirdLevelId);
    await softwareApi.deleteGlobalSoftware(globalSecondLevelId);
    await softwareApi.deleteGlobalSoftware(globalFirstLevelId);
  });
});
