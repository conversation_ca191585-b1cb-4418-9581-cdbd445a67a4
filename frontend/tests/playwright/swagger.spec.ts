import { expect } from "@playwright/test";
import { GetConstants } from "../../src/utils/constants";
import { test } from "./fixture/personFixture";
import globalSetup from "./global-setup";

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
  console.log("he");
});

test.describe("swagger", () => {
  test("should load successfully", async ({ page }) => {
    const constants = GetConstants();
    const res = await page.goto(`${constants.backendURI}/swagger`);
    expect(res?.ok()).toBeTruthy();
  });
});
