import { expect, test } from "@playwright/test";
import { INotification } from "../../src/api/backend/generated/interfaces";
import { TenantApiRoutes } from "../../src/api/backend/generated/routes";
import { tenantsApi } from "../../src/api/backend/v1";
import globalSetup from "./global-setup";
import { clickSidebarNotificationsLink, clickSidebarShowMoreLink } from "./misc/buttons";
import { animateCursorToElementAndClick, animateCursorToElementAndClickAndFill } from "./misc/cursor";

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("tenant", () => {
  test("create tenant", async ({ page }) => {
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-tenants", true, 500, "Clicking Tenants");
    await animateCursorToElementAndClick(page, "[data-testid='new-tenant-button']", true, 500, "Clicking New Tenant");

    const randomTenantName = Math.random().toString(36).substring(7);
    await animateCursorToElementAndClickAndFill(page, "[data-testid='tenant-name-input']", randomTenantName, true, 500, "Filling tenant name");

    // create
    const postPromise = page.waitForResponse(
      req => req.request().method() === "POST" && req.url().endsWith("/api/v1/tenants"),
    );

    await page.getByRole("button", { name: "Create", exact: true }).click({ delay: 500 });
    const response = await postPromise;
    expect(response.status()).toBe(200);

    // delete the tenant
    const res = await response.json();
    await tenantsApi.deleteTenants({
      ids: [res.id],
      permanent: true,
    });
  });

  test("delete tenant", async ({ page }) => {
    // create tenant
    const randomTenantName = Math.random().toString(36).substring(7);
    const createdTenant = await tenantsApi.postTenant({
      isMsp: false,
      name: randomTenantName,
      ownerTenantId: 1,
    });

    await animateCursorToElementAndClick(page, "#sidebar-nav-item-tenants", true, 500, "Clicking Tenants");
    await animateCursorToElementAndClickAndFill(page, "[aria-label='Search in the data grid']", createdTenant.name, true, 500, "Filter");
    await page.waitForTimeout(1000);
    await animateCursorToElementAndClick(page, ".dx-data-row .dx-checkbox-icon", true, 500, "Select tenant");
    await animateCursorToElementAndClick(page, "button:has-text('Batch Actions')", true, 500, "Clicking Batch Actions");

    // Set up notification promise before delete action
    const deletePromise = page.waitForResponse(
      req => req.request().method() === "POST" && req.url().includes(TenantApiRoutes.BulkDelete),
    );
    await animateCursorToElementAndClick(page, "button:has-text('Delete')", true, 500, "Clicking Delete");

    await animateCursorToElementAndClick(page, ".modal-footer .btn-primary", true, 500, "Clicking Delete");

    const response = await deletePromise;
    expect(response.status()).toBe(200);

    // Wait for the deletion and notification to be processed
    await page.waitForTimeout(5000);

    // Navigate to notifications
    await clickSidebarShowMoreLink(page);
    await clickSidebarNotificationsLink(page);

    const notificationPromise = page.waitForResponse(
      req => req.request().method() === "GET" && req.url().includes("/api/v1/notifications/dx"),
    );

    // Wait for and verify notification
    const notificationResponse = await notificationPromise;
    expect(notificationResponse.status()).toBe(200);

    const notifications: { data: INotification[] } = await notificationResponse.json();
    const foundNotification = notifications.data.some(
      notification => notification.description.includes(createdTenant.name),
    );
    expect(foundNotification).toBe(true);
  });
});
