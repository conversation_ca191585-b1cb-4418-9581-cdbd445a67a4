import { expect, test } from "@playwright/test";
import { tagsApi } from "../../src/api/backend/v1";
import globalSetup from "./global-setup";
import { expect200Response, waitForCreateTagResponse, waitForUpdateTagResponse } from "./misc/api";
import {
  clickByTestId,
  clickModalYesButton,
  clickSidebarShowMoreLink,
  clickSidebarTagLink,
} from "./misc/buttons";
import { fillTagForm, getRandomName, SearchDxDataGrid } from "./misc/inputs";

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("tag", () => {
  test("create tag", async ({ page }) => {
    await clickSidebarShowMoreLink(page);
    await clickSidebarTagLink(page);
    await clickByTestId(page, "new-tag-button", "Click New Tag");

    const randomName = getRandomName();
    const randomDescription = getRandomName();

    await fillTagForm(page, randomName, randomDescription);

    const wrapper = waitForCreateTagResponse(page);
    await clickByTestId(page, "save-tag-button", "Click Save");

    const response = await wrapper.response;
    expect200Response(response);

    const res = await wrapper.getBody();
    expect(res.name).toBe(randomName);
    expect(res.description).toBe(randomDescription);

    // delete the tag
    await tagsApi.delete(res.id);
  });

  test("update tag", async ({ page }) => {
    const randomName = getRandomName();
    const randomDescription = getRandomName();
    const tag = await tagsApi.create({
      name: randomName,
      description: randomDescription,
      tenantTagAuthorizations: [],
    });

    await clickSidebarShowMoreLink(page);
    await clickSidebarTagLink(page);
    await SearchDxDataGrid(page, randomName);
    await page.waitForTimeout(1000);
    await clickByTestId(page, "edit-tag-button", "Click Edit");

    // change data
    const newRandomName = getRandomName();
    const newRandomDescription = getRandomName();

    await fillTagForm(page, newRandomName, newRandomDescription);

    const wrapper = waitForUpdateTagResponse(page, tag.id);
    await clickByTestId(page, "save-tag-button", "Click Save");

    const response = await wrapper.response;
    expect200Response(response);

    const body = await wrapper.getBody();
    expect(body.name).toBe(newRandomName);
    expect(body.description).toBe(newRandomDescription);

    await tagsApi.delete(tag.id);
  });

  test("delete tag", async ({ page }) => {
    const randomName = getRandomName();
    const randomDescription = getRandomName();
    await tagsApi.create({
      name: randomName,
      description: randomDescription,
      tenantTagAuthorizations: [],
    });

    await clickSidebarShowMoreLink(page);
    await clickSidebarTagLink(page);
    await SearchDxDataGrid(page, randomName);
    await page.waitForTimeout(1000);
    await clickByTestId(page, "delete-tag-button", "Click Delete");

    await clickModalYesButton(page);

    await page.waitForTimeout(1000);

    const el = page.getByTestId("delete-tag-button");
    await expect(el).toHaveCount(0);
  });
});
