import { gettingStartedApi } from "../../src/api/backend/v1";
import { SetConstants } from "../../src/utils/constants";

async function globalSetup(completeChecklist = true) {
  SetConstants({
    backendURI: process.env.VITE_BACKEND_URI!,
    version: process.env.VITE_VERSION!,
    docsRoot: process.env.VITE_DOCS_ROOT!,
    environmentMode: process.env.MODE as "production" | "development",
    enableAppInsights: false,
    checklistAvailable: process.env.VITE_CHECKLIST_AVAILABLE === "true",
  });

  // complete the getting-started steps
  if (completeChecklist)
    await gettingStartedApi.completeChecklist();
}

export default globalSetup;
