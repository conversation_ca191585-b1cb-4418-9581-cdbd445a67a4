import { expect, test } from "@playwright/test";
import { IApplicationPreferences, IOperation } from "../../src/api/backend/generated/interfaces";
import { UserApiRoutes } from "../../src/api/backend/generated/routes";
import { personsApi, preferencesApi, tenantsApi, usersApi } from "../../src/api/backend/v1";
import { appPreferencePropertyNames, makeUid } from "../../src/utils/misc";
import globalSetup from "./global-setup";
import { animateCursorToElementAndClick, animateCursorToElementAndClickAndFill } from "./misc/cursor";

test.beforeAll(async () => {
  await globalSetup();
  const operations: IOperation<IApplicationPreferences>[] = [
    {
      op: "replace",
      path: `/${appPreferencePropertyNames.enableUserImpersonation}`,
      value: true,
      operationType: 2,
      from: "",
    },
  ];
  await preferencesApi.updateAppPreferences(operations);
  // we shouldn't be impersonating before the test starts
  await usersApi.stopImpersonatingUser();
});

test.afterAll(async () => {
  await usersApi.stopImpersonatingUser();
});

test.describe("user impersonation", async () => {
  test("impersonate should immediately show impersonation alert", async ({ page }) => {
    await page.goto("/");
    // we need to create a fake non-msp tenant and a fake non-admin user
    // then we impersonate that user
    const testTenantName = Math.random().toString(36).substring(7);
    const res = await tenantsApi.bulkCreate({
      ownerTenantId: 1,
      tenants: [
        {
          name: testTenantName,
        },
      ],
    });
    const tenant = res[0];
    const randomEmailName = `${Math.random().toString(36).substring(7)}@immy.bot`;
    const randomPrincipalId = makeUid();
    const person = await personsApi.post({
      tenantId: tenant.id,
      firstName: "Test",
      lastName: "Test",
      emailAddress: randomEmailName,
      azurePrincipalId: randomPrincipalId,
    });
    await usersApi.createFromPerson({
      personId: person.id,
      hasManagementAccess: true,
    });

    // start test

    await animateCursorToElementAndClick(page, "#sidebar-nav-item-show-more", true, 500, "Clicking Show More");
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-show-more-users", true, 500, "Clicking Users");
    await animateCursorToElementAndClickAndFill(page, "input[placeholder='Search...']", randomEmailName, true, 500, "Filter");
    await page.waitForTimeout(1000);
    await animateCursorToElementAndClick(page, "[data-testid='edit-user-button']", true, 500, "Clicking Edit User");

    await animateCursorToElementAndClick(page, `[data-testid='impersonate-${randomEmailName}']`, true, 500, "Clicking Impersonate User");

    expect(page.url().includes("/computers"));

    const stopImpersonatingPromise = page.waitForResponse(
      req =>
        req.request().method() === "POST" && req.url().endsWith(UserApiRoutes.StopImpersonatingUser),
    );

    await animateCursorToElementAndClick(page, "[data-testid='stop-impersonating-button']", true, 500, "Clicking Stop Impersonating");

    const response = await stopImpersonatingPromise;
    expect(response.status()).toBe(200);

    // cleanup

    // delete tenant will delete person and user as well
    await page.waitForTimeout(1000);
    await tenantsApi.deleteTenants({
      ids: [tenant.id],
      permanent: true,
    });
  });
});
