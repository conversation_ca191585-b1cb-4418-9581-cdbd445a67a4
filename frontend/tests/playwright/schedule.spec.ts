import { expect, test } from "@playwright/test";
import {
  ComputerOfflineMaintenanceSessionBehavior,
  PromptTimeoutAction,
  RebootPreference,
  TargetCategory,
  TargetGroupFilter,
  TargetType,
} from "../../src/api/backend/generated/enums";
import { ScheduleApiRoutes } from "../../src/api/backend/generated/routes";
import { schedulesApi } from "../../src/api/backend/v1";
import { DEFAULT_PROMPT_TIMEOUT_MINUTES } from "../../src/utils/constants";
import globalSetup from "./global-setup";
import { animateCursorToElementAndClick } from "./misc/cursor";

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("schedules", () => {
  test("create new schedule", async ({ page }) => {
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-schedules", true, 500, "Click Schedules");
    await animateCursorToElementAndClick(page, "a:has-text(\"New\")", true, 500, "Click New");

    // single tenant target scope
    await animateCursorToElementAndClick(page, "text=\"Single Tenant\"", true, 500, "Click Single Tenant");

    // select a tenant
    await animateCursorToElementAndClick(page, "input[placeholder=\"Select a Tenant\"]", true, 500, "Click Select a Tenant");
    const immyOption = page.getByRole("option", { name: /(^immybot$)|(^immense networks$)/i }).first();
    await animateCursorToElementAndClick(page, immyOption, true, 500, "Select option");

    // target type
    await animateCursorToElementAndClick(page, "text=\"All Computers\"", true, 500, "Click All Computers");

    // create
    const postPromise = page.waitForResponse(
      req => req.request().method() === "POST" && req.url().endsWith("/api/v1/schedules"),
    );
    await animateCursorToElementAndClick(page, "role=button[name=\"Create\"]", true, 500, "Click Create");
    const response = await postPromise;
    expect(response.status()).toBe(200);

    // delete the schedule
    const res = await response.json();
    await schedulesApi.delete(res.id);
  });

  test("delete schedule", async ({ page }) => {
    // create schedule
    const schedule = await schedulesApi.create({
      allowAccessToMSPResources: false,
      allowAccessToParentTenant: false,
      applyWindowsUpdates: false,
      disabled: false,
      offlineBehavior: ComputerOfflineMaintenanceSessionBehavior.Skip,
      rebootPreference: RebootPreference.Normal,
      promptTimeoutAction: PromptTimeoutAction.Suppress,
      autoConsentToReboots: false,
      promptTimeoutMinutes: DEFAULT_PROMPT_TIMEOUT_MINUTES,
      customCronExpression: "* * * * *",
      scheduleExecutionAfterActiveHours: false,
      sendDetectionEmail: false,
      sendDetectionEmailWhenAllActionsAreCompliant: false,
      sendFollowUpEmail: false,
      sendFollowUpOnlyIfActionNeeded: false,
      showMaintenanceActions: false,
      showPostponeButton: false,
      showRunNowButton: false,
      suppressRebootsDuringBusinessHours: false,
      targetCategory: TargetCategory.Computer,
      targetGroupFilter: TargetGroupFilter.All,
      targetType: TargetType.All,
      useComputersTimezoneForExecution: false,
      propagateToChildTenants: false,
    });
    await animateCursorToElementAndClick(page, "a:has-text(\"Schedules\")", true, 500, "Click Schedules");
    await animateCursorToElementAndClick(page, `[data-testid="delete-schedule-button-${schedule.id}"]`, true, 500, "Click delete");
    const route = ScheduleApiRoutes.Delete.replace("{scheduleId}", schedule.id.toString());
    const deletePromise = page.waitForResponse(
      req => req.request().method() === "DELETE" && req.url().endsWith(route),
    );
    await animateCursorToElementAndClick(page, "role=button[name=\"Yes\"]", true, 500, "Click Yes");
    const response = await deletePromise;
    expect(response.status()).toBe(204);
  });
});
