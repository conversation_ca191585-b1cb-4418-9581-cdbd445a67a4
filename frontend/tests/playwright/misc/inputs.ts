import { Page } from "@playwright/test";
import { animateCursorToElementAndClickAndFill } from "./cursor";

export function getRandomName() {
  return Math.random().toString(36).substring(7);
}

export function getRandomEmail() {
  return `${getRandomName()}@foobar.com`;
}

export async function SearchDxDataGrid(page: Page, search: string, annotationText: string = "Filter") {
  await animateCursorToElementAndClickAndFill(page, "[aria-label='Search in the data grid']", search, true, 200, annotationText);
  await page.waitForTimeout(1000);
}

export async function fillPersonForm(
  page: Page,
  firstName: string,
  lastName: string,
  email: string,
  azureId: string | null = null,
) {
  await animateCursorToElementAndClickAndFill(page, "[data-testid='person-first-name-input']", firstName, true, 200, "Fill firstName");
  await animateCursorToElementAndClickAndFill(page, "[data-testid='person-last-name-input']", lastName, true, 200, "Fill lastName");
  await animateCursorToElementAndClickAndFill(page, "[data-testid='person-email-input']", email, true, 200, "Fill email");
  if (azureId)
    await animateCursorToElementAndClickAndFill(page, "[data-testid='person-azure-id-input']", azureId, true, 200, "Fill Azure Id");
}

export async function fillTagForm(page: Page, name: string, description: string) {
  await animateCursorToElementAndClickAndFill(page, "[data-testid='tag-name-input']", name, true, 500, "Fill tag name");
  await animateCursorToElementAndClickAndFill(page, "[data-testid='tag-description-input']", description, true, 500, "Fill tag description");
}

export async function fillTaskInfo(
  page: Page,
  taskName: string,
  notes: string,
  executeSerially: boolean,
  recommended: boolean,
) {
  await animateCursorToElementAndClickAndFill(page, "[data-testid='task-name-input']", taskName, true, 500, "Fill task name");
  await animateCursorToElementAndClickAndFill(page, "[data-testid='task-notes-input']", notes, true, 500, "Fill notes");
  if (executeSerially) {
    await page.getByTestId("task-serial-checkbox").locator("label").click();
  }
  if (recommended) {
    await page.getByTestId("task-recommended-checkbox").locator("label").click();
  }
}

export async function addTaskParameter(page: Page, parameterName: string) {
  await animateCursorToElementAndClickAndFill(page, "[data-testid='task-parameter-name-input']", parameterName, true, 500, "Fill parameter name");
}
