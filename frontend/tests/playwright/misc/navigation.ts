import { BrowserContext, expect, Page } from "@playwright/test";

export async function checkExternalNavigation(
  page: Page,
  context: BrowserContext,
  triggerSelector: string,
  externalUrl: string,
) {
  // Listen for a new page (tab) to open
  const [newPage] = await Promise.all([
    context.waitForEvent("page"), // Wait for a new page to open
    page.click(triggerSelector),
  ]);

  // Wait for the new page to load
  await newPage.waitForLoadState();

  // Verify that the new page's URL is as expected
  const url = newPage.url();
  expect(url).toBe(externalUrl);
}
