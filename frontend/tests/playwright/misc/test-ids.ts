/**
This type includes all the test ids that are used in the application.
 */
export type TestIds
  = | "new-person-btn"
    | "edit-person-btn"
    | "create-person-btn"
    | "delete-person-btn"
    | "update-person-btn"
    | "new-maintenance-task-link"
    | "add-task-parameter-btn"
    | "software-submit-btn"
    | "embedded-maintenance-task-details-form-submit-btn"
    | "embedded-maintenance-task-details-form-cancel-btn"
    | "new-tag-button"
    | "save-tag-button"
    | "edit-tag-button"
    | "delete-tag-button"
    | "check-list-next-button"
    | "skip-setup-link"
    | "reset-training-btn"
    | "new-prerequisite-btn"
    | "maintenance-item-selector-search"
    | "more-actions-dropdown"
    | "immy-modal-cancel-btn"
    | "immy-modal-confirm-btn"
    | "roles-tab"
    | "new-role-btn";
