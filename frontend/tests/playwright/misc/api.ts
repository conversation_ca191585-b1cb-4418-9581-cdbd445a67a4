import { expect, Page, Response } from "@playwright/test";
import { IGetRoleResponse } from "../../../src/api/backend/generated/interfaces";
import {
  IGetLocalMaintenanceTaskResponse,
  IGetLocalSoftwareResponse,
  IGetSimplePersonResponse,
  IGetTagResponse,
  ILocalTargetAssignmentResource,
} from "../../../src/api/backend/generated/responses";
import {
  GettingStartedApiRoutes,
  PersonApiRoutes,
  SoftwareApiRoutes,
  TagApiRoutes,
} from "../../../src/api/backend/generated/routes";

export function expect200Response(response: Response) {
  expect(response.status()).toBe(200);
}

export function expect204Response(response: Response) {
  expect(response.status()).toBe(204);
}

export function expect202Response(response: Response) {
  expect(response.status()).toBe(202);
}

// these waitFor... methods can all be auto-generated by reinforced typings
export function waitForCreatePersonResponse(page: Page) {
  const response = page.waitForResponse(
    req => req.request().method() === "POST" && req.url().endsWith("/api/v1/persons"),
  );

  return makeWaitResponse<IGetSimplePersonResponse>(response);
}

export function waitForUpdatePersonResponse(page: Page, personId: number) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "PUT"
      && req.url().includes(PersonApiRoutes.Put.replace("{id}", personId.toString())),
  );

  return makeWaitResponse<IGetSimplePersonResponse>(response);
}

export function waitForCreateLocalMaintenanceTask(page: Page) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "POST" && req.url().endsWith("/api/v1/maintenance-tasks/local"),
  );

  return makeWaitResponse<IGetLocalMaintenanceTaskResponse>(response);
}

export function waitForUpdateLocalSoftware(page: Page, softwareId: string) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "PATCH"
      && req.url().includes(`/api/v1/software/local/${softwareId}`),
  );

  return makeWaitResponse<IGetLocalSoftwareResponse>(response);
}

export function waitForCreateTagResponse(page: Page) {
  const response = page.waitForResponse(
    req => req.request().method() === "POST" && req.url().endsWith("/api/v1/tags"),
  );

  return makeWaitResponse<IGetTagResponse>(response);
}

export function waitForUpdateTagResponse(page: Page, tagId: number) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "POST"
      && req.url().includes(TagApiRoutes.Update.replace("{tagId}", tagId.toString())),
  );

  return makeWaitResponse<IGetTagResponse>(response);
}

export function waitForResetTrainingResponse(page: Page) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "POST"
      && req.url().includes(GettingStartedApiRoutes.ResetChecklist),
  );

  return makeWaitResponse<IGetTagResponse>(response);
}

export function waitForMigrateLocalSoftwareResponse(page: Page, softwareIdentifier: string) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "POST"
      && req
        .url()
        .includes(
          SoftwareApiRoutes.MigrateLocalToGlobal.replace("{softwareIdentifier}", softwareIdentifier),
        ),
  );

  return makeWaitResponse<number>(response);
}

export function waitForDeployResponse(page: Page) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "POST"
      && req
        .url()
        .includes("run-immy-service"),
  );

  return makeWaitResponse<number>(response);
}

export function waitForCreateRole(page: Page) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "POST"
      && req.url().includes(`/api/v1/roles/create`),
  );

  return makeWaitResponse<IGetRoleResponse>(response);
}

export function waitForUpdateRole(page: Page, roleId: number) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "PUT"
      && req.url().includes(`/api/v1/roles/update/role/${roleId}`),
  );

  return makeWaitResponse<IGetRoleResponse>(response);
}

export function waitForDeleteRole(page: Page, roleId: number) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "DELETE"
      && req.url().includes(`/api/v1/roles/delete/role/${roleId}`),
  );

  return makeWaitResponse<void>(response);
}

export function waitForCloneRole(page: Page, roleId: number) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "POST"
      && req.url().includes(`/api/v1/roles/clone/role/${roleId}`),
  );

  return makeWaitResponse<void>(response);
}

export function waitForAssignRole(page: Page, userId: number) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "POST"
      && req.url().includes(`/api/v1/user-roles/set/user/${userId}/roles`),
  );

  return makeWaitResponse<void>(response);
}

export function waitForTargetAssignmentsResponse(page: Page) {
  const response = page.waitForResponse(
    req =>
      req.request().method() === "GET"
      && req.url().includes(`/api/v1/target-assignments/`),
  );

  return makeWaitResponse<ILocalTargetAssignmentResource>(response);
}

/**
 * The response type used to handle wait responses
 */
export interface IWaitWrapper<T> {
  response: Promise<Response>;
  getBody: () => Promise<T>;
}

/**
 * Helper function to retrieve and cast the response body to the specified type
 * @param response
 */
async function getResponseBody<T>(response: Response) {
  return (await response.json()) as T;
}

/**
 * Helper function to create a wait wrapper around a response
 * @param response
 */
function makeWaitResponse<T>(response: Promise<Response>): IWaitWrapper<T> {
  return {
    response,
    getBody: async () => getResponseBody<T>(await response),
  };
}
