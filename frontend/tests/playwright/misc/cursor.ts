import { Locator, <PERSON> } from "@playwright/test";

const cursorImageId = "mouse_follower";

class BrowserCursorCoordinates {
  private static _X: number = 0;
  private static _Y: number = 0;

  static get X(): number {
    return BrowserCursorCoordinates._X;
  }

  static get Y(): number {
    return BrowserCursorCoordinates._Y;
  }

  static set X(X: number) {
    BrowserCursorCoordinates._X = X;
  }

  static set Y(Y: number) {
    BrowserCursorCoordinates._Y = Y;
  }

  // Optional: Method to reset to initial value
  static reset(): void {
    BrowserCursorCoordinates._X = 0;
    BrowserCursorCoordinates._Y = 0;
  }
}

export async function animateCursorToElementAndClickAndFill(page: Page, selector: string | Locator, textContent: string, executeClick = true, delay: number = 100, annotationText?: string) {
  const element = await animateCursorToElementAndClick(page, selector, executeClick, delay, annotationText);
  await element.clear();
  await page.keyboard.type(textContent, { delay: 100 });
}

export async function animateCursorToElementAndClick(page: Page, selector: string | Locator, executeClick = true, delay: number = 100, annotationText?: string): Promise<Locator> {
  await enableCursor(page);
  const element = typeof selector === "string" ? page.locator(selector) : selector;
  await element.scrollIntoViewIfNeeded();
  if (!element) {
    throw new Error(`Element with selector "${selector}" not found`);
  }

  const box = await element.boundingBox();
  if (!box) {
    throw new Error(`Unable to get bounding box for element with selector "${selector}"`);
  }

  const endX = box.x + box.width / 2;
  const endY = box.y + box.height / 2;
  console.log(`Target coordinates: (${endX}, ${endY})`);

  if (delay === 0) {
    // Skip animation if delay is 0
    console.log("Skipping animation (delay=0)");
    BrowserCursorCoordinates.X = endX;
    BrowserCursorCoordinates.Y = endY;

    const cursorImageHandle = await page.evaluateHandle(cursorImageId => document.getElementById(cursorImageId), cursorImageId);
    await page.evaluateHandle(({ cursorImage, x, y }) => {
      if (cursorImage != null) {
        cursorImage.style.left = `${x}px`;
        cursorImage.style.top = `${y}px`;
      }
    }, { cursorImage: cursorImageHandle, x: endX, y: endY });

    if (executeClick) {
      console.log(`Clicking element immediately`);
      await element.click({ force: true });
    }
    return element;
  }

  const distance = Math.sqrt((endX - BrowserCursorCoordinates.X) ** 2 + (endY - BrowserCursorCoordinates.Y) ** 2);
  const steps = Math.max(1, Math.floor(distance / 20)); // Adjust the divisor to control step size
  console.log(`Moving cursor over ${steps} steps, distance: ${distance}px`);

  const cursorImageHandle = await page.evaluateHandle(cursorImageId => document.getElementById(cursorImageId), cursorImageId);

  // Create annotation element if annotationText is provided
  if (annotationText) {
    await page.evaluate(({ text, x, y }) => {
      const annotation = document.createElement("div");
      annotation.id = "cursor-annotation";
      annotation.style.cssText = `
        position: fixed;
        background-color: red;
        color: white;
        padding: 5px;
        border-radius: 3px;
        font-size: 14px;
        pointer-events: none;
        z-index: 99999999999;
        left: ${x + 20}px;
        top: ${y - 30}px;
      `;
      annotation.textContent = text;
      document.body.appendChild(annotation);
    }, { text: annotationText, x: box.x, y: box.y });
  }

  for (let i = 0; i <= steps; i++) {
    const x = BrowserCursorCoordinates.X + (endX - BrowserCursorCoordinates.X) * (i / steps);
    const y = BrowserCursorCoordinates.Y + (endY - BrowserCursorCoordinates.Y) * (i / steps);

    await page.evaluateHandle(({ cursorImage, x, y }) => {
      if (cursorImage != null) {
        cursorImage.style.left = `${x}px`;
        cursorImage.style.top = `${y}px`;
      }
    }, { cursorImage: cursorImageHandle, x, y });

    await page.waitForTimeout(steps / distance);

    if (i == steps) {
      BrowserCursorCoordinates.X = x;
      BrowserCursorCoordinates.Y = y;
    }
  }

  if (executeClick) {
    console.log(`Clicking ${typeof selector === "object" && "role" in selector ? `getByRole('${selector.role}', { name: ${typeof selector === "string" ? `'${selector}'` : selector} })` : selector}`);
    await page.evaluateHandle(({ cursorImage, delay }) => {
      if (cursorImage != null) {
        cursorImage.style.transition = `transform ${delay / 1000}s`;
        cursorImage.style.transform = "scale(0.5)";
        setTimeout(() => {
          cursorImage.style.transform = "scale(1)";
        }, 100);
      }
    }, { cursorImage: cursorImageHandle, delay });
    await element.click({ delay, timeout: 1000, force: true });
  }
  else {
    console.log(`NOT clicking ${selector} (executeClick=false)`);
    await page.waitForTimeout(delay);
  }
  // Remove annotation element if it was created
  if (annotationText) {
    await page.evaluate(() => {
      const annotation = document.getElementById("cursor-annotation");
      if (annotation) {
        annotation.remove();
      }
    });
  }

  return element;
}

async function existCursorImage(page: Page) {
  const existCursorImage = await page.evaluate(cursorImageId => document.getElementById(cursorImageId) != null, cursorImageId);
  return existCursorImage;
}

async function enableCursor(page: Page) {
  const exist = await existCursorImage(page);
  if (exist)
    return;

  BrowserCursorCoordinates.reset();

  await page.evaluate((cursorImageId) => {
    const seleniumFollowerImg = document.createElement("img");
    seleniumFollowerImg.setAttribute("src", "data:image/png;base64,"
    + "iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAQAAACGG/bgAAAAAmJLR0QA/4ePzL8AAAAJcEhZcwAA"
    + "HsYAAB7GAZEt8iwAAAAHdElNRQfgAwgMIwdxU/i7AAABZklEQVQ4y43TsU4UURSH8W+XmYwkS2I0"
    + "9CRKpKGhsvIJjG9giQmliHFZlkUIGnEF7KTiCagpsYHWhoTQaiUUxLixYZb5KAAZZhbunu7O/PKf"
    + "e+fcA+/pqwb4DuximEqXhT4iI8dMpBWEsWsuGYdpZFttiLSSgTvhZ1W/SvfO1CvYdV1kPghV68a3"
    + "0zzUWZH5pBqEui7dnqlFmLoq0gxC1XfGZdoLal2kea8ahLoqKXNAJQBT2yJzwUTVt0bS6ANqy1ga"
    + "VCEq/oVTtjji4hQVhhnlYBH4WIJV9vlkXLm+10R8oJb79Jl1j9UdazJRGpkrmNkSF9SOz2T71s7M"
    + "SIfD2lmmfjGSRz3hK8l4w1P+bah/HJLN0sys2JSMZQB+jKo6KSc8vLlLn5ikzF4268Wg2+pPOWW6"
    + "ONcpr3PrXy9VfS473M/D7H+TLmrqsXtOGctvxvMv2oVNP+Av0uHbzbxyJaywyUjx8TlnPY2YxqkD"
    + "dAAAAABJRU5ErkJggg==");
    seleniumFollowerImg.setAttribute("id", cursorImageId);
    seleniumFollowerImg.setAttribute("style", "position: absolute; z-index: 99999999999; pointer-events: none; left:0; top:0");
    document.body.appendChild(seleniumFollowerImg);
  }, cursorImageId);
}
