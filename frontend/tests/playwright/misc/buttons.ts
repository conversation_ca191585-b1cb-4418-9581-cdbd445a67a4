import { Page } from "@playwright/test";
import { animateCursorToElementAndClick } from "./cursor";
import { TestIds } from "./test-ids";

export async function clickByTestId(page: Page, testId: TestIds, annotationText?: string) {
  await animateCursorToElementAndClick(page, `[data-testid="${testId}"]`, true, 200, annotationText);
}

export async function click(page: Page, selector: string) {
  await page.click(selector, { delay: 200 });
}

export async function clickSidebarShowMoreLink(page: Page) {
  await animateCursorToElementAndClick(page, "#sidebar-nav-item-show-more", true, 200, "Clicking Show More");
}

export async function clickSidebarPeopleLink(page: Page) {
  await animateCursorToElementAndClick(page, "#sidebar-nav-item-show-more-people", true, 200, "Clicking Show People");
}

export async function clickSidebarUserLink(page: Page) {
  await animateCursorToElementAndClick(page, "#sidebar-nav-item-show-more-users", true, 200, "Clicking Show Users");
}

export async function clickSidebarNotificationsLink(page: Page) {
  await animateCursorToElementAndClick(page, "#sidebar-nav-item-show-more-notifications", true, 500, "Clicking Show Notifications");
}

export async function clickRbacLink(page: Page) {
  await animateCursorToElementAndClick(page, "#sidebar-nav-item-show-more-rbac", true, 500, "Clicking RBAC");
}

export async function clickSidebarTagLink(page: Page) {
  await animateCursorToElementAndClick(page, "#sidebar-nav-item-show-more-tags", true, 200, "Clicking Show Tags");
}

export async function clickSidebarPreferenceLink(page: Page) {
  await animateCursorToElementAndClick(page, "#sidebar-nav-item-show-more-preferences", true, 200, "Clicking Show Preferences");
}

export async function clickModalYesButton(page: Page) {
  await animateCursorToElementAndClick(page, "button:has-text('Yes')", true, 200, "Confirming");
}

export async function clickModalNoButton(page: Page) {
  await page.getByRole("button", { name: "No" }).click({ delay: 200 });
}
