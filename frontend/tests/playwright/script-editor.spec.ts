import { expect, test } from "@playwright/test";
import {
  DatabaseType,
  ScriptCategory,
  ScriptExecutionContext,
  ScriptLanguage,
  ScriptOutputType,
} from "../../src/api/backend/generated/enums";
import { ScriptApiRoutes } from "../../src/api/backend/generated/routes";
import { scriptsApi } from "../../src/api/backend/v1";
import globalSetup from "./global-setup";
import { animateCursorToElementAndClick, animateCursorToElementAndClickAndFill } from "./misc/cursor";

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("script editor", () => {
  test("create script", async ({ page }) => {
    await animateCursorToElementAndClick(page, "#nav-item-script-editor", true, 500, "Clicking Script Editor");
    await animateCursorToElementAndClick(page, "[data-testid=\"open-editors-new-script\"]", true, 500, "Clicking New Script");
    const randomScriptName = Math.random().toString(36).substring(7);
    await animateCursorToElementAndClickAndFill(page, "[data-testid=\"script-name-input\"]", randomScriptName, true, 500, "Filling Script Name");
    const postPromise = page.waitForResponse(
      req => req.request().method() === "POST" && req.url().includes("/api/v1/scripts/local"),
    );
    await animateCursorToElementAndClick(page, "button:has-text(\"Save\")", true, 500, "Clicking Save");
    const response = await postPromise;
    expect(response.status()).toBe(200);

    const json = await response.json();
    scriptsApi.deleteLocalScript(json.id);
  });

  test("delete script", async ({ page }) => {
    const randomName = Math.random().toString(36).substring(7);
    const script = await scriptsApi.createLocalScript({
      action: "",
      name: randomName,
      outputType: ScriptOutputType.Object,
      scriptCategory: ScriptCategory.SoftwareDetection,
      scriptExecutionContext: ScriptExecutionContext.System,
      scriptLanguage: ScriptLanguage.PowerShell,
      scriptType: DatabaseType.Global,
      tenants: [],
    });

    await animateCursorToElementAndClick(page, "#nav-item-script-editor", true, 500, "Clicking Script Editor");
    await animateCursorToElementAndClick(page, "[data-testid=\"search-activity-item\"]", true, 500, "Clicking Search");

    await animateCursorToElementAndClick(page, "[data-testid=\"search-sidebar-pane\"] label:has-text(\"Name\")", true, 500, "Clicking Name label");

    await animateCursorToElementAndClickAndFill(page, "[data-testid=\"search-sidebar-input\"]", randomName, true, 500, "Filling Name");

    await page.waitForTimeout(1000);

    await animateCursorToElementAndClick(page, `[data-testid="search-sidebar-pane"] :text("${randomName}")`, true, 500, "Clicking Script");

    const route = ScriptApiRoutes.DeleteLocalScript.replace("{scriptId}", script.id.toString());
    const deletePromise = page.waitForResponse(
      req => req.request().method() === "DELETE" && req.url().includes(route),
    );
    await animateCursorToElementAndClick(
      page,
      "[data-testid=\"script-editor-configure-view\"] button:has-text(\"Delete\")",
      true,
      500,
      "Clicking Delete button",
    );

    await animateCursorToElementAndClick(page, "button:has-text(\"Yes\")", true, 500, "Clicking Yes");

    const response = await deletePromise;
    expect(response.status()).toBe(204);
  });
});
