import { expect, test } from "@playwright/test";
import globalSetup from "./global-setup";
import { animateCursorToElementAndClick } from "./misc/cursor";

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("computer list", async () => {
  test("batch action exclude from user affinity", async ({ page }) => {
    await page.goto("/");
    await page.waitForTimeout(1500);
    const selectAllCheckBox = page
      .getByTestId("computer-list-page-computer-table")
      .getByTestId("immy-list-select-all-checkbox");

    const listElementSelector = "[data-testid='computer-list-page-computer-table'] [data-testid='computer-list'] .immy-list-ul li";
    const computersAmount = await page.locator(listElementSelector).count();
    if (computersAmount > 0) {
      await animateCursorToElementAndClick(page, selectAllCheckBox, true, 500, "Select All");

      await animateCursorToElementAndClick(page, "[data-testid='computer-list-page-batch-actions-button']", true, 500, "Click Batch Actions");

      const yesRadioButton = page
        .getByTestId("exclude-from-user-affinity-radio")
        .getByText("Yes");
      await animateCursorToElementAndClick(page, yesRadioButton, true, 500, "Click Yes");

      const excludeFromUserAffinityButton = page.getByTestId("exclude-from-user-affinity-button");
      await animateCursorToElementAndClick(page, excludeFromUserAffinityButton, true, 500, "Click Exclude from User Affinity");

      const locator = page.getByText("User affinity preference updated");
      await expect(locator).toBeVisible();
    }
  });
});
