import { faker } from "@faker-js/faker";
import { expect, Page, test } from "@playwright/test";
import { personsApi, rolesApi, usersApi } from "../../src/api/backend/v1";
import { makeUid } from "../../src/utils/misc";
import globalSetup from "./global-setup";
import { expect200Response, expect204Response, waitForAssignRole, waitForCloneRole, waitForCreateRole, waitForDeleteRole, waitForUpdateRole } from "./misc/api";
import {
  click,
  clickByTestId,
  clickRbacLink,
  clickSidebarShowMoreLink,
  clickSidebarUserLink,
} from "./misc/buttons";

import { animateCursorToElementAndClick, animateCursorToElementAndClickAndFill } from "./misc/cursor";

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

async function createRole(page: Page) {
  const randomName = faker.person.firstName();
  const randomDescription = faker.lorem.sentence().slice(0, 10);
  const role = await rolesApi.createRole(
    {
      grantedPermissionIds: [],
      name: randomName,
      description: randomDescription,
    },
  );

  await clickSidebarShowMoreLink(page);
  await clickRbacLink(page);
  await clickByTestId(page, "roles-tab", "Click Roles Tab");
  return role;
}

async function createPerson() {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  const email = faker.internet.email();

  const person = await personsApi.post({
    emailAddress: email,
    firstName,
    lastName,
    tenantId: 1,
    azurePrincipalId: makeUid(),
  });

  return person;
}

async function createUser(personId: number) {
  const user = await usersApi.createFromPerson({
    personId,
    hasManagementAccess: true,
  });

  return user;
}

test.describe("Roles", () => {
  test("create role", async ({ page }) => {
    await clickSidebarShowMoreLink(page);
    await clickRbacLink(page);
    await clickByTestId(page, "roles-tab", "Click Roles Tab");
    await clickByTestId(page, "new-role-btn", "Click New Role");

    // ===========================================================  Name and Description ===========================================================
    const randomName = faker.person.firstName();
    const randomDescription = faker.lorem.sentence().slice(0, 10);

    await animateCursorToElementAndClickAndFill(page, "[data-testid='name-input']", randomName, true, 500, "Name");
    await animateCursorToElementAndClickAndFill(page, "[data-testid='description-input']", randomDescription, true, 500, "Description");

    // ===========================================================  Base Permissions Tab ===========================================================

    await animateCursorToElementAndClick(page, "[data-testid='base-permissions-search-input']", false, 500, "Search Permissions");
    await click(page, "[data-testid='base-permissions-search-input']");
    await page.keyboard.type("ses", { delay: 100 });

    const elements = await page.$$(".content-list-item");
    expect(elements.length).toBe(2);

    // Select all text and delete
    await page.keyboard.press("Control+A");
    await page.keyboard.press("Backspace");

    const firstCanviewCheckBox = page.locator(".content-list-item input").nth(0);
    await animateCursorToElementAndClick(page, firstCanviewCheckBox, true, 500, "Select permission");

    await animateCursorToElementAndClick(page, `[data-testid="select-all-button-deployments"]`, true, 500, "Select all permission");

    const element = page.locator(`[data-testid="activated-permissions"]`);
    await expect(element).toHaveText(/5 activated/);

    await animateCursorToElementAndClick(page, `[data-testid="unselect-all-button-deployments"]`, true, 500, "UnSelect all permission");
    await expect(element).toHaveText(/1 activated/);

    // ===========================================================  Tenant Assigment Tab ===========================================================

    await animateCursorToElementAndClick(page, "#tenant-assigment-tab", true, 500, "Click Tenant Assigment");
    await animateCursorToElementAndClick(page, "#edit-tenants", true, 500, "Edit Tenants");

    let firstRow = page.locator(`#tenantsTable [aria-label="Select row"] .dx-checkbox-icon`).nth(0);
    await animateCursorToElementAndClick(page, firstRow, true, 500, "Select element");

    let secondRow = page.locator(`#tenantsTable [aria-label="Select row"] .dx-checkbox-icon`).nth(1);
    await animateCursorToElementAndClick(page, secondRow, true, 500, "Select element");

    await animateCursorToElementAndClick(page, "#add-tenants-bulk", true, 500, "Add tenants");

    await animateCursorToElementAndClick(page, `[data-testid="option-Excluded"]`, true, 500, "Exclude tenants");
    await animateCursorToElementAndClick(page, `#done-btn`, true, 500, "Done");

    // ===========================================================  User Assigment Tab ===========================================================
    await animateCursorToElementAndClick(page, "#user-assigment-tab", true, 500, "Click User Assigment");

    firstRow = page.locator(`#usersTable [aria-label="Select row"] .dx-checkbox-icon`).nth(0);
    await animateCursorToElementAndClick(page, firstRow, true, 500, "Select element");

    secondRow = page.locator(`#usersTable [aria-label="Select row"] .dx-checkbox-icon`).nth(1);
    await animateCursorToElementAndClick(page, secondRow, true, 500, "Select element");

    await animateCursorToElementAndClick(page, "#add-users-bulk", true, 500, "Add users");

    // ===========================================================  Saving ===========================================================
    const wrapper = waitForCreateRole(page);
    await animateCursorToElementAndClick(page, "#manage-role-btn", true, 500, "Click Create");
    const response = await wrapper.response;
    expect200Response(response);

    const res = await wrapper.getBody();
    await rolesApi.deleteRole(res.id);
  });

  test("update role", async ({ page }) => {
    const role = await createRole(page);
    await animateCursorToElementAndClick(page, `[data-testid="edit-role-btn-${role.id}"]`, true, 500, "Edit Role");

    const randomName = faker.person.firstName();
    const randomDescription = faker.lorem.sentence().slice(0, 10);
    await animateCursorToElementAndClickAndFill(page, "[data-testid='name-input']", randomName, true, 500, "Name");
    await animateCursorToElementAndClickAndFill(page, "[data-testid='description-input']", randomDescription, true, 500, "Description");

    const wrapper = waitForUpdateRole(page, role.id);
    await animateCursorToElementAndClick(page, "#manage-role-btn", true, 500, "Click Update");
    const response = await wrapper.response;
    expect200Response(response);

    const res = await wrapper.getBody();
    expect(res.name).toBe(randomName);
    expect(res.description).toBe(randomDescription);

    await rolesApi.deleteRole(role.id);
  });

  test("delete role", async ({ page }) => {
    const role = await createRole(page);
    const wrapper = waitForDeleteRole(page, role.id);
    await animateCursorToElementAndClick(page, `[data-testid="delete-role-btn-${role.id}"]`, true, 500, "Delete Role");
    const response = await wrapper.response;
    expect204Response(response);
  });

  test("clone role", async ({ page }) => {
    const role = await createRole(page);
    await animateCursorToElementAndClick(page, `[data-testid="clone-role-btn-${role.id}"]`, true, 500, "Clone Role");
    const randomName = faker.person.firstName();
    await animateCursorToElementAndClickAndFill(page, "#swal2-input", randomName, true, 500, "Change Name");

    const wrapper = waitForCloneRole(page, role.id);
    await animateCursorToElementAndClick(page, ".swal2-confirm", true, 500, "Click OK");
    const response = await wrapper.response;
    expect200Response(response);

    await rolesApi.deleteRole(role.id);
  });

  test("validate permissions", async ({ page }) => {
    // =========================================================== create person ===========================================================
    const person = await createPerson();
    const user = await createUser(person.id);

    const randomName = faker.person.firstName();
    const randomDescription = faker.lorem.sentence().slice(0, 10);
    const role = await rolesApi.createRole(
      {
        grantedPermissionIds: [],
        name: randomName,
        description: randomDescription,
      },
    );

    await clickSidebarShowMoreLink(page);
    await clickRbacLink(page);

    await clickByTestId(page, "roles-tab", "Click Roles Tab");
    await animateCursorToElementAndClick(page, `[data-testid="edit-role-btn-${role.id}"]`, true, 500, "Edit Role");
    await animateCursorToElementAndClick(page, "#user-assigment-tab", true, 500, "Click User Assigment");

    const elementSelector = page.locator("#usersTable input[aria-label=\"Filter cell\"]");
    await animateCursorToElementAndClickAndFill(page, elementSelector, user.name ?? "", true, 500, "Filter");
    await page.waitForTimeout(1000);
    const firstRow = page.locator(`#usersTable [aria-label="Select row"] .dx-checkbox-icon`).nth(0);
    await animateCursorToElementAndClick(page, firstRow, true, 500, "Select element");
    await animateCursorToElementAndClick(page, "#add-users-bulk", true, 500, "Add users");
    await animateCursorToElementAndClick(page, "#manage-role-btn", true, 500, "Click Create");

    // =========================================================== go to users list to impersonate ===========================================================
    await clickSidebarUserLink(page);
    await animateCursorToElementAndClickAndFill(page, "input[placeholder='Search...']", user.name ?? "", true, 500, "Filter");
    await page.waitForTimeout(1000);
    await animateCursorToElementAndClick(page, `[data-testid='impersonate-${user.email}']`, true, 500, "Clicking Impersonate User");

    const isVisible = await page.locator("#sidebar-nav-item-deployments").isVisible();
    await page.click("[data-testid=\"stop-impersonating-button\"]");
    expect(isVisible).toBeFalsy();
  });

  test("assign role", async ({ page }) => {
    const person = await createPerson();
    const user = await createUser(person.id);
    await page.goto("/settings/user-people");

    const tableSearcher = page.locator("[aria-label=\"Search in the data grid\"]").nth(0);
    await animateCursorToElementAndClickAndFill(page, tableSearcher, user.name ?? "", true, 500, "Filter");
    await page.waitForTimeout(1000);
    const locator = page.locator(`a[href = "/settings/edit-user/${user.id}"]`);
    await animateCursorToElementAndClick(page, locator, true, 500, "Edit Users");

    await animateCursorToElementAndClick(page, `[placeholder="Select options"]`, true, 500, "Select Role(s)");
    const roleOption = page.locator("span", { hasText: /^MSP Admin$/ });

    const wrapper = waitForAssignRole(page, user.id);
    await animateCursorToElementAndClick(page, roleOption, true, 500, "Select MSP Admin Role");
    const response = await wrapper.response;
    expect204Response(response);
  });

  test("filter users/people", async ({ page }) => {
    // =========================================================== create person ===========================================================
    const person = await createPerson();
    const user = await createUser(person.id);
    const person2 = await createPerson();

    await clickSidebarShowMoreLink(page);
    await clickRbacLink(page);

    const tableSearcher = page.locator("[aria-label=\"Search in the data grid\"]").nth(0);
    await animateCursorToElementAndClickAndFill(page, tableSearcher, user.name ?? "", true, 500, "Filter");
    await page.waitForTimeout(1000);
    let locator = page.locator("span.dx-datagrid-search-text", { hasText: user.name });
    await expect(locator).toBeVisible();

    await tableSearcher.clear();

    await page.keyboard.type(person2.firstName ?? "", { delay: 100 });
    await page.waitForTimeout(1000);
    locator = page.locator("span.dx-datagrid-search-text", { hasText: person2.firstName });
    await expect(locator).toBeVisible();

    await tableSearcher.clear();

    await animateCursorToElementAndClick(page, "[data-testid=\"option-USERS\"]", true, 500, "Select Users");

    await animateCursorToElementAndClickAndFill(page, tableSearcher, person2.firstName ?? "", true, 500, "Filter");
    await page.waitForTimeout(1000);
    locator = page.locator(".dx-datagrid-nodata");
    await expect(locator).toBeVisible();
  });
});
