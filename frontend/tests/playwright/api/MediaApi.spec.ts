import { test } from "@playwright/test";
import { MediaApi } from "../../../src/api/backend/generated/sdk/MediaApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.MediaController */
export class MediaApiTests extends BaseApiTestSuite<MediaApi> implements RequireTestCoverage<Pick<MediaApi, ApiMethods<MediaApi>>> {
  getDownloadUrl = [];
  search = [];
  getLocal = [];
  getLocalById = [];
  updateLocal = [];
  deleteLocal = [];
  uploadLocalMedia = [];
  getLocalDownloadUrl = [];
  getGlobal = [];
  getGlobalById = [];
  updateGlobal = [];
  deleteGlobal = [];
  uploadGlobalMedia = [];
  getGlobalDownloadUrl = [];
  uploadSupportMedia = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(MediaApi.name, async () => new MediaApiTests().runTests());
