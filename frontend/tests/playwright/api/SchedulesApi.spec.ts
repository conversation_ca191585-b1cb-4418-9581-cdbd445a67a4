import { expect, test } from "@playwright/test";
import { SchedulesApi } from "../../../src/api/backend/generated/sdk/SchedulesApi";
import { schedulesApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.SchedulesController */
export class SchedulesApiTests extends BaseApiTestSuite<SchedulesApi> implements RequireTestCoverage<Pick<SchedulesApi, ApiMethods<SchedulesApi>>> {
  get = [() => {
    test("should retrieve schedules without tenantId", async () => {
      const response = await schedulesApi.get();
      expect(Array.isArray(response)).toBeTruthy();
    });
  }];

  getById = [];
  create = [];
  update = [];
  delete = [];
  runScheduleNow = [];
  getRunningScheduleIds = [];
  cancelSchedule = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(SchedulesApi.name, async () => new SchedulesApiTests().runTests());
