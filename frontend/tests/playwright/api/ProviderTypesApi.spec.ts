import { test } from "@playwright/test";
import { ProviderTypesApi } from "../../../src/api/backend/generated/sdk/ProviderTypesApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ProviderTypesController */
export class ProviderTypesApiTests extends BaseApiTestSuite<ProviderTypesApi> implements RequireTestCoverage<Pick<ProviderTypesApi, ApiMethods<ProviderTypesApi>>> {
  bindParameters = [];
  getAllProviderTypes = [];
  getDeviceGroups = [];
  getClientGroups = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(ProviderTypesApi.name, async () => new ProviderTypesApiTests().runTests());
