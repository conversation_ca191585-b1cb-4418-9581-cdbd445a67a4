import { test } from "@playwright/test";
import { ProviderAgentsApi } from "../../../src/api/backend/generated/sdk/ProviderAgentsApi";
import { providerAgentsApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ProviderAgentsController */
export class ProviderAgentsApiTests extends BaseApiTestSuite<ProviderAgentsApi> implements RequireTestCoverage<Pick<ProviderAgentsApi, ApiMethods<ProviderAgentsApi>>> {
  getIdentificationLogs = [];
  bulkDeletePendingAgents = [];
  getPending = [() => {
    test("should retrieve pending agents without parameters", async () => {
      await providerAgentsApi.getPending();
    });
  }];

  getPendingAgentConflictsForComputer = [];
  resolveFailuresForAgents = [];
  retryIdentification = [];
  resolveFailure = [];
  getPendingCounts = [];
  identifyAgents = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(ProviderAgentsApi.name, async () => new ProviderAgentsApiTests().runTests());
