import { expect, test } from "@playwright/test";
import { InventoryTasksApi } from "../../../src/api/backend/generated/sdk/InventoryTasksApi";
import { inventoryTasksApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.InventoryTasksController */
export class InventoryTasksApiTests extends BaseApiTestSuite<InventoryTasksApi> implements RequireTestCoverage<Pick<InventoryTasksApi, ApiMethods<InventoryTasksApi>>> {
  getAll = [() => {
    test("should succeed", async () => {
      const response = await inventoryTasksApi.getAll();
      expect(response).toBeTruthy();
      expect(Array.isArray(response)).toBe(true);
    });
  }];

  createLocal = [];
  updateLocal = [];
  deleteLocal = [];
  addScriptToLocalInventoryTask = [];
  deleteScriptFromLocalInventoryTask = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(InventoryTasksApi.name, async () => new InventoryTasksApiTests().runTests());
