import { test } from "@playwright/test";
import { MaintenanceSessionsApi } from "../../../src/api/backend/generated/sdk/MaintenanceSessionsApi";
import { sessionsApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.MaintenanceSessionsController */
export class MaintenanceSessionsApiTests extends BaseApiTestSuite<MaintenanceSessionsApi> implements RequireTestCoverage<Pick<MaintenanceSessionsApi, ApiMethods<MaintenanceSessionsApi>>> {
  dxGetAll = [];
  get = [() => {
    test("should succeed", async () => {
      const sessionId = 1; // Using a test session ID
      await sessionsApi.get(sessionId, {
        includeActions: true,
        includeStages: true,
        includeActionActivities: true,
      });
    });
  }];

  getSessionLogs = [];
  getOldSessionLogs = [];
  getSessionPhases = [];
  getLastSessionLog = [];
  cancelSession = [];
  cancelAllSessions = [];
  cancelSessions = [];
  rerunSession = [];
  rerunSessions = [];
  resumeSession = [];
  cancelSessionsForSchedule = [];
  getSessionStatusCounts = [];
  rerunAction = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(MaintenanceSessionsApi.name, async () => new MaintenanceSessionsApiTests().runTests());
