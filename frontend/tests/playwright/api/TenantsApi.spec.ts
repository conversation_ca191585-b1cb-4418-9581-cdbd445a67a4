import { expect, test } from "@playwright/test";
import { TenantsApi } from "../../../src/api/backend/generated/sdk/TenantsApi";
import { tenantsApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.TenantsController */
export class TenantsApiTests extends BaseApiTestSuite<TenantsApi> implements RequireTestCoverage<Pick<TenantsApi, ApiMethods<TenantsApi>>> {
  getTenants = [() => {
    test("should retrieve list of tenants without parameters", async () => {
      const response = await tenantsApi.getTenants();
      expect(response).toBeTruthy();
    });
  }];

  getSoftwareFromInventory = [];
  exportSoftware = [];
  getSoftwareFromInventoryDx = [];
  getTenant = [];
  putTenant = [];
  updateAzureTenantLink = [];
  postTenant = [];
  bulkCreate = [];
  deleteTenants = [];
  mergeTenants = [];
  activateTenant = [];
  deactivateTenant = [];
  getAzureGroupsAtTenant = [];
  getProviderLinks = [];
  getComputersExcludedFromMaintenance = [];
  addTags = [];
  removeTags = [];
  setParentTenant = [];
  removeParentTenant = [];
  resolveAssignmentsForMaintenanceItem = [];
  getAzureGroupAtTenant = [];
  getTenantComputerCounts = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(TenantsApi.name, async () => new TenantsApiTests().runTests());
