import { expect, test } from "@playwright/test";
import { UsersApi } from "../../../src/api/backend/generated/sdk/UsersApi";
import { usersApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.UsersController */
export class UsersApiTests extends BaseApiTestSuite<UsersApi> implements RequireTestCoverage<Pick<UsersApi, ApiMethods<UsersApi>>> {
  getAll = [() => {
    test("should retrieve list of users without parameters", async () => {
      const response = await usersApi.getAll();
      expect(response).toBeTruthy();
    });
  }];

  get = [
    () => {
      test("should succeed", async () => {
        const allUsers = await usersApi.getAll();
        const user = await usersApi.get(allUsers[0].id);
        expect(user).toBeDefined();
      });
    },
  ];

  getClaims = [];
  update = [];
  create = [];
  createFromPerson = [];
  delete = [];
  bulkDelete = [];
  submitFeedback = [];
  impersonateUser = [];
  stopImpersonatingUser = [];
  bulkAssignRolesToPeople = [];
  bulkRemoveRolesFromPeople = [];
  grantAccessRbac = [];
  invalidateCache = [];
  updateExpiration = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(UsersApi.name, async () => new UsersApiTests().runTests());
