import { test } from "@playwright/test";
import { MaintenanceActionsApi } from "../../../src/api/backend/generated/sdk/MaintenanceActionsApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.MaintenanceActionsController */
export class MaintenanceActionsApiTests extends BaseApiTestSuite<MaintenanceActionsApi> implements RequireTestCoverage<Pick<MaintenanceActionsApi, ApiMethods<MaintenanceActionsApi>>> {
  dxGetAll = [];
  getLatestNonCompliantMaintenanceActionsForTenant = [];
  getLatestActionForComputers = [];
  getLatestActionForTenants = [];
  getLatestActionsForComputer = [];
  getLatestActionsForTenant = [];
  getActionsForComputer = [];
  getLastSessionLogForAction = [];
  getActionsForMaintenanceItem = [];
  getActionsForSoftwareVersion = [];
  getLogsForAction = [];
  getActionsNeedingAttentionForComputer = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(MaintenanceActionsApi.name, async () => new MaintenanceActionsApiTests().runTests());
