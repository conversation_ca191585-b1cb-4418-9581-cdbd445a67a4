import { test } from "@playwright/test";
import { AzureApi } from "../../../src/api/backend/generated/sdk/AzureApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.AzureController */
export class AzureApiTests extends BaseApiTestSuite<AzureApi> implements RequireTestCoverage<Pick<AzureApi, ApiMethods<AzureApi>>> {
  getDelegatedAdminCustomers = [];
  getPartnerTenantInfos = [];
  syncAzureUsersForTenants = [];
  preconsentCustomerTenants = [];
  syncAzureDetailsForTenants = [];
  handleTenantConsent = [];
  disambiguateAzureTenantType = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(AzureApi.name, async () => new AzureApiTests().runTests());
