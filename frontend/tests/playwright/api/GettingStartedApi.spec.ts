import { test } from "@playwright/test";
import { GettingStartedApi } from "../../../src/api/backend/generated/sdk/GettingStartedApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.GettingStartedController */
export class GettingStartedApiTests extends BaseApiTestSuite<GettingStartedApi> implements RequireTestCoverage<Pick<GettingStartedApi, ApiMethods<GettingStartedApi>>> {
  checklist = [];
  resetChecklist = [];
  completeChecklist = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(GettingStartedApi.name, async () => new GettingStartedApiTests().runTests());
