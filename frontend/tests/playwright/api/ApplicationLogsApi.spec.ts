import { expect, test } from "@playwright/test";
import { ApplicationLogsApi } from "../../../src/api/backend/generated/sdk/ApplicationLogsApi";
import { applicationLogsApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ApplicationLogsController */
export class ApplicationLogsApiTests extends BaseApiTestSuite<ApplicationLogsApi> implements RequireTestCoverage<Pick<ApplicationLogsApi, ApiMethods<ApplicationLogsApi>>> {
  updateSourceContext = [];
  clearSourceContext = [];
  clearAllSourceContexts = [() => {
    test("should succeed", async () => {
      await applicationLogsApi.clearAllSourceContexts();
    });
  }];

  toggleStreaming = [() => {
    test("should succeed", async () => {
      await applicationLogsApi.toggleStreaming({
        enabled: true,
      });
    });
  }];

  getSourceContexts = [
    () => test("should succeed", async () => {
      const contexts = await applicationLogsApi.getSourceContexts();
      expect(contexts).toBeTruthy();
    }),
  ];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(ApplicationLogsApi.name, async () => new ApplicationLogsApiTests().runTests());
