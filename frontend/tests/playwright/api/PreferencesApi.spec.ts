import { test } from "@playwright/test";
import { PreferencesApi } from "../../../src/api/backend/generated/sdk/PreferencesApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.PreferencesController */
export class PreferencesApiTests extends BaseApiTestSuite<PreferencesApi> implements RequireTestCoverage<Pick<PreferencesApi, ApiMethods<PreferencesApi>>> {
  getPreferences = [];
  getTenantPreferences = [];
  updateAppPreferences = [];
  updateTenantPreferences = [];
  updateUserPreferences = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(PreferencesApi.name, async () => new PreferencesApiTests().runTests());
