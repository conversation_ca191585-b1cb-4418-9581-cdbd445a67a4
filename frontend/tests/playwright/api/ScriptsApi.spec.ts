import { expect, test } from "@playwright/test";
import { ScriptsApi } from "../../../src/api/backend/generated/sdk/ScriptsApi";
import { scriptsApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ScriptsController */
export class ScriptsApiTests extends BaseApiTestSuite<ScriptsApi> implements RequireTestCoverage<Pick<ScriptsApi, ApiMethods<ScriptsApi>>> {
  cancelScript = [];
  startEditorServices = [];
  connectLanguageService = [];
  runScript = [];
  dxGetAll = [];
  getAllGlobalScriptNames = [];
  getAllLocalScriptNames = [];
  search = [];
  getAllLocalScripts = [];
  getAllGlobalScripts = [() => {
    test("should retrieve list of global scripts without parameters", async () => {
      const response = await scriptsApi.getAllGlobalScripts();
      expect(response).toBeTruthy();
    });
  }];

  getLocalScript = [];
  getGlobalScript = [];
  updateLocalScript = [];
  updateGlobalScript = [];
  createLocalScript = [];
  createGlobalScript = [];
  deleteLocalScript = [];
  deleteGlobalScript = [];
  syntaxCheck = [];
  getScriptReferenceCounts = [];
  getGlobalScriptReferences = [];
  getLocalScriptReferences = [];
  duplicateScript = [];
  migrateLocalToGlobalWhatIf = [];
  migrateLocalToGlobal = [];
  getScriptVariablesAndParameters = [];
  findFunctions = [];
  getFunctionSyntax = [];
  validateParamBlockParameters = [];
  doesScriptHaveParamBlock = [];
  getDisabledPreflightScripts = [];
  setPreflightScriptEnablement = [];
  getLocalScriptAudits = [];
  getGlobalScriptAudits = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(ScriptsApi.name, async () => new ScriptsApiTests().runTests());
