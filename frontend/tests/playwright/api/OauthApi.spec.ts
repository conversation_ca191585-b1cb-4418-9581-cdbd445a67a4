import { test } from "@playwright/test";
import { <PERSON>auth<PERSON><PERSON> } from "../../../src/api/backend/generated/sdk/OauthApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.OauthController */
export class OauthApiTests extends BaseApiTestSuite<OauthApi> implements RequireTestCoverage<Pick<OauthApi, ApiMethods<OauthApi>>> {
  beginAuthCodeFlow = [];
  failAuthCodeFlow = [];
  receiveAuthCode = [];
  listOauthAccessTokens = [];
  getOauthAccessToken = [];
  refreshOauthAccessToken = [];
  deleteOauthAccessToken = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(OauthApi.name, async () => new OauthApiTests().runTests());
