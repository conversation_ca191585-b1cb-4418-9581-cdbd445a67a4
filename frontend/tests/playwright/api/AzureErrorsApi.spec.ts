import { test } from "@playwright/test";
import { AzureErrorsApi } from "../../../src/api/backend/generated/sdk/AzureErrorsApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.AzureErrorsController */
export class AzureErrorsApiTests extends BaseApiTestSuite<AzureErrorsApi> implements RequireTestCoverage<Pick<AzureErrorsApi, ApiMethods<AzureErrorsApi>>> {
  getForTenantDx = [];
  getAllDx = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(AzureErrorsApi.name, async () => new AzureErrorsApiTests().runTests());
