import { expect, test } from "@playwright/test";
import { ApplicationLocksApi } from "../../../src/api/backend/generated/sdk/ApplicationLocksApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ApplicationLocksController */
export class ApplicationLocksApiTests extends BaseApiTestSuite<ApplicationLocksApi> implements RequireTestCoverage<Pick<ApplicationLocksApi, ApiMethods<ApplicationLocksApi>>> {
  getAll = [
    () => test("should succeed", async () => {
      const locksApi = new ApplicationLocksApi();
      const locks = await locksApi.getAll();
      expect(locks).toBeDefined();
    }),
  ];

  eventStreamAsync = [];
  requestKeyCancellation = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(ApplicationLocksApi.name, async () => new ApplicationLocksApiTests().runTests());
