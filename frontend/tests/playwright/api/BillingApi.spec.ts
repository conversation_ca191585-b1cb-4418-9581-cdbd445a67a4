import { test } from "@playwright/test";
import { Billing<PERSON><PERSON> } from "../../../src/api/backend/generated/sdk/BillingApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.BillingController */
export class BillingApiTests extends BaseApiTestSuite<BillingApi> implements RequireTestCoverage<Pick<BillingApi, ApiMethods<BillingApi>>> {
  createCustomerPortalSession = [];
  getSubscriptionDetails = [];
  getProductCatalogItems = [];
  getBillingPlatformDetails = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(BillingApi.name, async () => new BillingApiTests().runTests());
