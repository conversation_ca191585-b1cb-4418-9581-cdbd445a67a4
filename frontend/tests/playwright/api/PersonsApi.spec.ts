import { expect, test } from "@playwright/test";
import { PersonsApi } from "../../../src/api/backend/generated/sdk/PersonsApi";
import { personsApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.PersonsController */
export class PersonsApiTests extends BaseApiTestSuite<PersonsApi> implements RequireTestCoverage<Pick<PersonsApi, ApiMethods<PersonsApi>>> {
  dxGet = [];
  getAllPersons = [() => {
    test("should retrieve list of persons without parameters", async () => {
      const response = await personsApi.getAllPersons();
      expect(response).toBeTruthy();
    });

    test("should handle pagination parameters", async () => {
      const pageSize = 1;
      const response = await personsApi.getAllPersons({ page: 1, pageSize });
      expect(response).toBeTruthy();
    });
  }];

  get = [];
  delete = [];
  put = [];
  post = [];
  getPersonsRequestingAccess = [];
  grantAccess = [];
  grantAccessRbac = [];
  denyAccess = [];
  addTags = [];
  removeTags = [];
  getSelfServiceItems = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(PersonsApi.name, async () => new PersonsApiTests().runTests());
