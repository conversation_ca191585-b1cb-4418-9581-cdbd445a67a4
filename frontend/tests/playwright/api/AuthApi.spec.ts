import { expect, test } from "@playwright/test";
import { AuthApi } from "../../../src/api/backend/generated/sdk/AuthApi";
import { authApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.AuthController */
export class AuthApiTests extends BaseApiTestSuite<AuthApi> implements RequireTestCoverage<Pick<AuthApi, ApiMethods<AuthApi>>> {
  get = [() => {
    test("should succeed", () => {
      const res = authApi.get();
      expect(res).toBeTruthy();
    });
  }];

  updateAzureTenantAuthDetails = [];
  deleteAzureTenantAuthDetails = [];
  getAzureTenantAuthDetails = [];
  getImmybotIPAddresses = [];
  requestAccess = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(AuthApi.name, async () => new AuthApiTests().runTests());
