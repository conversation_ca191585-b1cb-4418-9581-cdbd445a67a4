import { expect, test } from "@playwright/test";
import { NotificationsApi } from "../../../src/api/backend/generated/sdk/NotificationsApi";
import { notificationsApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.NotificationsController */
export class NotificationsApiTests extends BaseApiTestSuite<NotificationsApi> implements RequireTestCoverage<Pick<NotificationsApi, ApiMethods<NotificationsApi>>> {
  getDx = [];
  acknowledge = [];
  getUnacknowledgedNotifications = [() => {
    test("should retrieve unacknowledged notifications without parameters", async () => {
      const response = await notificationsApi.getUnacknowledgedNotifications();
      expect(response).toBeTruthy();
      expect(Array.isArray(response)).toBe(true);
    });
  }];

  silenceNotification = [];
  getSilencedNotificationsForUser = [];
  removeSilencedNotification = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(NotificationsApi.name, async () => new NotificationsApiTests().runTests());
