import { test } from "@playwright/test";
import { ChangeRequestsApi } from "../../../src/api/backend/generated/sdk/ChangeRequestsApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ChangeRequestsController */
export class ChangeRequestsApiTests extends BaseApiTestSuite<ChangeRequestsApi> implements RequireTestCoverage<Pick<ChangeRequestsApi, ApiMethods<ChangeRequestsApi>>> {
  deleteChangeRequest = [];
  approveChangeRequest = [];
  denyChangeRequest = [];
  requireChanges = [];
  commentOnChangeRequest = [];
  getAllDx = [];
  getOpenCount = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(ChangeRequestsApi.name, async () => new ChangeRequestsApiTests().runTests());
