import { test } from "@playwright/test";
import { AuditsApi } from "../../../src/api/backend/generated/sdk/AuditsApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.AuditsController */
export class AuditsApiTests extends BaseApiTestSuite<AuditsApi> implements RequireTestCoverage<Pick<AuditsApi, ApiMethods<AuditsApi>>> {
  getLocalDx = [];

  getGlobalDx = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(AuditsApi.name, async () => new AuditsApiTests().runTests());
