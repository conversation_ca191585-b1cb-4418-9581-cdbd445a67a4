import { test } from "@playwright/test";
import { SmtpConfigApi } from "../../../src/api/backend/generated/sdk/SmtpConfigApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.SmtpConfigController */
export class SmtpConfigApiTests extends BaseApiTestSuite<SmtpConfigApi> implements RequireTestCoverage<Pick<SmtpConfigApi, ApiMethods<SmtpConfigApi>>> {
  getAll = [];
  get = [];
  create = [];
  update = [];
  delete = [];
  sendTestEmail = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(SmtpConfigApi.name, async () => new SmtpConfigApiTests().runTests());
