import { test } from "@playwright/test";
import { DynamicIntegrationTypesApi } from "../../../src/api/backend/generated/sdk/DynamicIntegrationTypesApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.DynamicIntegrationTypesController */
export class DynamicIntegrationTypesApiTests extends BaseApiTestSuite<DynamicIntegrationTypesApi> implements RequireTestCoverage<Pick<DynamicIntegrationTypesApi, ApiMethods<DynamicIntegrationTypesApi>>> {
  setupTestIntegration = [];
  removeTestIntegration = [];
  testIntegrationMethod = [];
  testIntegrationBindConfigurationForm = [];
  reload = [];
  reloadByGlobalId = [];
  reloadByLocalId = [];
  getAll = [];
  getGlobal = [];
  getLocal = [];
  createGlobal = [];
  createLocal = [];
  updateGlobal = [];
  updateLocal = [];
  deleteGlobal = [];
  deleteLocal = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(DynamicIntegrationTypesApi.name, async () => new DynamicIntegrationTypesApiTests().runTests());
