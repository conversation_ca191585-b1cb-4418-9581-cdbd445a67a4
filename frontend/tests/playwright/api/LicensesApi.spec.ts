import { expect, test } from "@playwright/test";
import { LicensesApi } from "../../../src/api/backend/generated/sdk/LicensesApi";
import { licensesApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.LicensesController */
export class LicensesApiTests extends BaseApiTestSuite<LicensesApi> implements RequireTestCoverage<Pick<LicensesApi, ApiMethods<LicensesApi>>> {
  dxGet = [];
  getAll = [() => {
    test("should succeed", async () => {
      const response = await licensesApi.getAll();
      expect(response).toBeTruthy();
      expect(Array.isArray(response)).toBe(true);
    });
  }];

  get = [];
  update = [];
  create = [];
  delete = [];
  upload = [];
  getDownloadUrl = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(LicensesApi.name, async () => new LicensesApiTests().runTests());
