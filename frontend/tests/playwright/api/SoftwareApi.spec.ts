import { test } from "@playwright/test";
import { SoftwareApi } from "../../../src/api/backend/generated/sdk/SoftwareApi";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.SoftwareController */
export class SoftwareApiTests extends BaseApiTestSuite<SoftwareApi> implements RequireTestCoverage<Pick<SoftwareApi, ApiMethods<SoftwareApi>>> {
  migrateLocalToGlobalWhatIf = [];
  migrateLocalToGlobal = [];
  getAllLocalSoftware = [];
  getLocalSoftware = [];
  createLocalSoftware = [];
  updateLocalSoftware = [];
  deleteLocalSoftware = [];
  getAllGlobalSoftware = [];
  getGlobalSoftware = [];
  createGlobalSoftware = [];
  updateGlobalSoftware = [];
  deleteGlobalSoftware = [];
  getLocalSoftwareVersions = [];
  getLocalSoftwareVersion = [];
  getLatestVersionForLocalSoftware = [];
  createLocalSoftwareVersion = [];
  updateLocalSoftwareVersion = [];
  uploadLocalSoftwareVersionFile = [];
  getDownloadUrlForLocalSoftwareVersion = [];
  fastCreateLocalVersion = [];
  deleteLocalSoftwareVersion = [];
  analyzeLocalPackage = [];
  getGlobalSoftwareVersions = [];
  getGlobalSoftwareVersion = [];
  getLatestVersionForGlobalSoftware = [];
  createGlobalSoftwareVersion = [];
  updateGlobalSoftwareVersion = [];
  uploadGlobalSoftwareVersionFile = [];
  getDownloadUrlForGlobalSoftwareVersion = [];
  fastCreateGlobalVersion = [];
  deleteGlobalSoftwareVersion = [];
  analyzeGlobalPackage = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(SoftwareApi.name, async () => new SoftwareApiTests().runTests());
