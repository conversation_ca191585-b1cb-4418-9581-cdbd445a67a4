import { expect, test } from "@playwright/test";
import { MetricsApi } from "../../../src/api/backend/generated/sdk/MetricsApi";
import { metricsApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.MetricsController */
export class MetricsApiTests extends BaseApiTestSuite<MetricsApi> implements RequireTestCoverage<Pick<MetricsApi, ApiMethods<MetricsApi>>> {
  getAppMetrics = [() => {
    test("should retrieve application metrics", async () => {
      const response = await metricsApi.getAppMetrics();
      expect(response).toBeTruthy();
    });
  }];

  getCircuitBreakers = [];
  isolateCircuitBreaker = [];
  resetCircuitBreaker = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(MetricsApi.name, async () => new MetricsApiTests().runTests());
