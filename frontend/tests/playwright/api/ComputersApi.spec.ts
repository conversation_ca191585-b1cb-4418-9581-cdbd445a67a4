import { expect, test } from "@playwright/test";
import { ComputersApi } from "../../../src/api/backend/generated/sdk/ComputersApi";
import { computersApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.ComputersController */
export class ComputersApiTests extends BaseApiTestSuite<ComputersApi> implements RequireTestCoverage<Pick<ComputersApi, ApiMethods<ComputersApi>>> {
  getComputerAgentStatusReport = [];
  computerInventoryDx = [];
  exportComputerInventory = [];
  getSoftwareFromInventoryDx = [];
  getEphemeralAgent = [];
  deleteEphemeralAgent = [];
  getEphemeralAgentCircuitBreaker = [];
  resetEphemeralAgentCircuitBreaker = [];
  restoreComputers = [];
  getAll = [() => {
    test("should retrieve list of computers without parameters", async () => {
      const response = await computersApi.getAll();
      expect(Array.isArray(response)).toBe(true);
    });

    test("should handle pageSize parameter", async () => {
      const pageSize = 1;
      const response = await computersApi.getAll({ pageSize });
      expect(Array.isArray(response)).toBe(true);
      expect(response.length).toBeLessThanOrEqual(pageSize);
    });

    test("should handle name filter parameter", async () => {
      const response = await computersApi.getAll({ name: "test" });
      expect(Array.isArray(response)).toBe(true);
    });

    test("should handle orderByUpdatedDate parameter", async () => {
      const response = await computersApi.getAll({ orderByUpdatedDate: true });
      expect(Array.isArray(response)).toBe(true);
    });
  }];

  getAllPaged = [];
  dxGet = [];
  setExcludedFromUserAffinity = [];
  setExcludedFromUserAffinityBatch = [];
  get = [];
  exportUserAffinities = [];
  getUserAffinitiesDx = [];
  getInventoryScriptResult = [];
  getDeviceUpdateFormData = [];
  getOnboarding = [];
  getComputerOnlineStatus = [];
  updatePrimaryPerson = [];
  updateAdditionalPersons = [];
  updateComputer = [];
  setToNeedsOnboarding = [];
  changeTenant = [];
  skipOnboarding = [];
  getScreenShareUrl = [];
  reinventoryComputer = [];
  resolveOnboardingOverridableTargetAssignments = [];
  deleteComputers = [];
  getMyComputers = [];
  searchInventorySoftwareByUpgradeCode = [];
  searchInventorySoftwareByName = [];
  searchAllInventorySoftwareByName = [];
  getComputerEvents = [];
  addTags = [];
  removeTags = [];
  excludeFromMaintenance = [];
  exportComputers = [];
  launchEphemeralAgent = [];
  loadRegistryKeys = [];
  loadRegistryKeyValues = [];
  getParentTenantInfo = [];
  updateNotes = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(ComputersApi.name, async () => new ComputersApiTests().runTests());
