import { expect, test } from "@playwright/test";
import { SystemApi } from "../../../src/api/backend/generated/sdk/SystemApi";
import { systemApi } from "../../../src/api/backend/v1";
import globalSetup from "../global-setup";
import { ApiMethods, BaseApiTestSuite, RequireTestCoverage } from "../utils";

/** Generated from: Immybot.Backend.Web.Common.Controllers.V1.SystemController */
export class SystemApiTests extends BaseApiTestSuite<SystemApi> implements RequireTestCoverage<Pick<SystemApi, ApiMethods<SystemApi>>> {
  getReleases = [];
  updateInstance = [];
  getTimezones = [() => {
    test("should retrieve available timezones", async () => {
      const response = await systemApi.getTimezones();
      expect(response).toBeTruthy();
    });
  }];

  restartBackend = [];
  updateReleaseChannel = [];
  getImmySupportAccessGrantDetails = [];
  requestSessionSupport = [];
  requestFormSupport = [];
  isImmySupportAccessGranted = [];
  enableImmySupportAccess = [];
  disableImmySupportAccess = [];
  enqueuePendoTrackEvent = [];
  reset = [];
}

test.beforeAll(async () => {
  await globalSetup();
});

test.describe(SystemApi.name, async () => new SystemApiTests().runTests());
