import { expect, test } from "@playwright/test";
import { DetectionMethod, LicenseType, PackageType, SoftwareLicenseRequirement, SoftwareType, SoftwareVersionInstallerType, UpdateActionType } from "../../src/api/backend/generated/enums";
import { softwareApi } from "../../src/api/backend/v1";
import globalSetup from "./global-setup";

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("global software download", () => {
  test("should be able to download recently uploaded global software", async () => {
    // Create a global software
    const randomSoftwareName = `Test Software ${Math.random().toString(36).substring(7)}`;
    const software = await softwareApi.createGlobalSoftware({
      detectionMethod: DetectionMethod.SoftwareTable,
      hidden: false,
      installOrder: 0,
      licenseRequirement: SoftwareLicenseRequirement.None,
      licenseType: LicenseType.None,
      name: randomSoftwareName,
      rebootNeeded: false,
      recommended: false,
      testRequired: false,
      upgradeStrategy: UpdateActionType.None,
      useDynamicVersions: false,
      softwarePrerequisites: [],
    });

    // Create a version for the software
    // Note: In a real test, we would upload a file, but for this test we'll just create a version
    // with a mock blob name to test the download URL generation
    const version = "1.0.0";
    const mockBlobName = "test-blob-name";
    await softwareApi.fastCreateGlobalSoftwareVersion({
      softwareId: Number.parseInt(software.identifier),
      softwareVersion: {
        semanticVersion: version,
        installerType: SoftwareVersionInstallerType.File,
        blobName: mockBlobName,
        softwareId: Number.parseInt(software.identifier),
        relativeCacheSourcePath: "test-path",
        packageType: PackageType.InstallerFile,
        licenseType: LicenseType.None,
        testRequired: false,
        upgradeStrategy: UpdateActionType.None,
        softwareIdentifier: software.identifier,
        softwareType: SoftwareType.GlobalSoftware,
      },
    });

    try {
      // Get the download URL for the software version
      const downloadUrl = await softwareApi.requestGlobalSoftwareDownloadUrl(software.identifier, version);

      // Verify that the download URL is not empty
      expect(downloadUrl).toBeTruthy();
      // In a real test, we would verify that the URL works by making a request to it
      // However, since we're using a mock blob name, the URL won't actually work
      // Instead, we'll just verify that the URL contains the expected components
      expect(downloadUrl).toContain("blob");
      expect(downloadUrl).toContain("sas");
      // Clean up
      await softwareApi.deleteGlobalSoftwareVersion(software.identifier, version);
      await softwareApi.deleteGlobalSoftware(software.identifier);
    }
    catch (error) {
      // If there's an error, clean up and then rethrow
      try {
        await softwareApi.deleteGlobalSoftwareVersion(software.identifier, version);
        await softwareApi.deleteGlobalSoftware(software.identifier);
      }
      catch (cleanupError) {
        console.error("Error during cleanup:", cleanupError);
      }
      throw error;
    }
  });
});
