import { expect, test } from "@playwright/test";
import {
  DetectionMethod,
  LicenseType,
  SoftwareLicenseRequirement,
  UpdateActionType,
} from "../../src/api/backend/generated/enums";
import { softwareApi } from "../../src/api/backend/v1";
import globalSetup from "./global-setup";
import { animateCursorToElementAndClick, animateCursorToElementAndClickAndFill } from "./misc/cursor";

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("dashboard page", async () => {
  test("selecting a software should add the correct column header", async ({ page }) => {
    const randomSoftwareName = Math.random().toString(36).substring(7);
    const software = await softwareApi.createLocalSoftware({
      detectionMethod: DetectionMethod.SoftwareTable,
      hidden: false,
      installOrder: 0,
      licenseRequirement: SoftwareLicenseRequirement.None,
      licenseType: LicenseType.None,
      name: randomSoftwareName,
      rebootNeeded: false,
      recommended: false,
      testRequired: false,
      upgradeStrategy: UpdateActionType.None,
      useDynamicVersions: false,
      softwarePrerequisites: [],
      tenantSoftware: [],
    });
    await page.goto("/");

    animateCursorToElementAndClick(page, "#sidebar-nav-item-dashboard", true, 500, "Click Dashboard");

    const searchInput = page.locator(`[data-testid="maintenance-item-selector-search"] input`);

    await animateCursorToElementAndClickAndFill(page, searchInput, randomSoftwareName, true, 500, "Filter");

    const softwareNameLabel = page.getByText(randomSoftwareName);
    animateCursorToElementAndClick(page, softwareNameLabel, true, 500, "Click Software Name");

    const colLocator = page.getByLabel(`Column ${randomSoftwareName}`);

    const preview = colLocator.getByRole("button", { name: "Preview" });
    await expect(preview).toBeVisible();
    const deploy = colLocator.getByRole("button", { name: "Deploy" });
    await expect(deploy).toBeVisible();

    const deleteButton = colLocator.locator(".btn-danger");
    animateCursorToElementAndClick(page, deleteButton, true, 500, "Click Delete");

    await expect(preview).toBeHidden();
    await expect(deploy).toBeHidden();

    await softwareApi.deleteLocalSoftware(software.identifier);
  });
});
