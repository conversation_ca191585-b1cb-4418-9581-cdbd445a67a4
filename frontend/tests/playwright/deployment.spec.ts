import { expect, test } from "@playwright/test";
import {
  DesiredSoftwareState,
  DetectionMethod,
  LicenseType,
  MaintenanceType,
  SoftwareLicenseRequirement,
  TargetCategory,
  TargetEnforcement,
  TargetGroupFilter,
  TargetType,
  UpdateActionType,
} from "../../src/api/backend/generated/enums";
import { IGetLocalSoftwareResponse } from "../../src/api/backend/generated/responses";
import { TargetAssignmentApiRoutes } from "../../src/api/backend/generated/routes";
import { softwareApi, targetAssignmentsApiV2 } from "../../src/api/backend/v1";
import globalSetup from "./global-setup";
import { animateCursorToElementAndClick } from "./misc/cursor";

let software: IGetLocalSoftwareResponse;

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();

  software = await softwareApi.createLocalSoftware({
    name: "Test Software",
    detectionMethod: DetectionMethod.SoftwareTable,
    hidden: false,
    installOrder: 0,
    licenseRequirement: SoftwareLicenseRequirement.None,
    licenseType: LicenseType.None,
    rebootNeeded: false,
    recommended: false,
    testRequired: false,
    upgradeStrategy: UpdateActionType.None,
    useDynamicVersions: false,
    softwarePrerequisites: [],
    tenantSoftware: [],
  });
});

test.afterAll(async () => {
  await softwareApi.deleteLocalSoftware(software.identifier);
});

test.describe("deployment", () => {
  test("create new deployment", async ({ page }) => {
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-deployments", true, 500, "Click Deployments");

    // New Button
    await animateCursorToElementAndClick(page, "[data-testid='newDeployment-button']", true, 500, "Click New");

    // select Software/Task
    await animateCursorToElementAndClick(page, "[data-testid='maintenance-item-selector-search']", true, 500, "Click Search");
    await animateCursorToElementAndClick(page, "[data-testid='maintenance-item-selector-search'] ul li:nth-of-type(1)", true, 500, "Select first one");

    // Target Scope -> Single tenant radio button
    await animateCursorToElementAndClick(page, "#target-selector .custom-radio:nth-of-type(1)", true, 500, "Select first one");

    // Tenant select
    await animateCursorToElementAndClick(page, "[data-testid='option-Single-Tenant']", true, 500, "Click Single Tenant");
    await animateCursorToElementAndClick(page, "[placeholder='Select a Tenant']", true, 500, "Select Tenant");

    const immyOption = page.getByRole("option", { name: /(^immybot$)|(^immense networks$)/i }).first();
    await animateCursorToElementAndClick(page, immyOption, true, 500, "Select option");

    // Target Type radio button
    const allComputersOption = page.getByText("All Computers").first();
    await animateCursorToElementAndClick(page, allComputersOption, true, 500, "Select All Computers");

    // create
    const postPromise = page.waitForResponse(
      req => req.request().method() === "POST" && req.url().endsWith("/api/v1/target-assignments"),
    );

    const createButton = page.getByRole("button", { name: "Create", exact: true });
    await animateCursorToElementAndClick(page, createButton, true, 500, "Click Create");
    const response = await postPromise;
    expect(response.status()).toBe(200);

    // delete the deployment
    const res = await response.json();
    await targetAssignmentsApiV2.deleteTargetAssignment(res.id);
  });

  test("delete deployment", async ({ page }) => {
    // create deployment
    const deployment = await targetAssignmentsApiV2.create({
      maintenanceIdentifier: "1",
      maintenanceType: MaintenanceType.LocalSoftware,
      desiredSoftwareState: DesiredSoftwareState.LatestVersion,
      targetType: TargetType.All,
      targetCategory: TargetCategory.Computer,
      targetGroupFilter: TargetGroupFilter.All,
      targetEnforcement: TargetEnforcement.Required,
      excluded: false,
      onboardingOnly: false,
      propagateToChildTenants: false,
      allowAccessToParentTenant: false,
    });

    await page.goto(`/deployments/${deployment.id}/edit?databaseType=1`);
    const moreActionsButton = page.getByRole("button", { name: "More Actions" });
    await animateCursorToElementAndClick(page, moreActionsButton, true, 500, "Click More Actions");
    const deleteButton = page.getByRole("button", { name: /Delete/ });
    await animateCursorToElementAndClick(page, deleteButton, true, 500, "Click Delete");
    const route = TargetAssignmentApiRoutes.DeleteLocal.replace("{id}", deployment.id.toString());
    const deletePromise = page.waitForResponse(
      req => req.request().method() === "DELETE" && req.url().endsWith(route),
    );
    const yesButton = page.getByRole("button", { name: "Yes" });
    await animateCursorToElementAndClick(page, yesButton, true, 500, "Click Yes");
    const response = await deletePromise;
    expect(response.status()).toBe(204);
  });
});
