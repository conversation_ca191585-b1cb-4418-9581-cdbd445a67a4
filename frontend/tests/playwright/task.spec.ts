import { faker } from "@faker-js/faker";
import { test } from "@playwright/test";
import globalSetup from "./global-setup";
import {
  expect202Response,
  waitForDeployResponse,
} from "./misc/api";
import {
  clickModalYesButton,
} from "./misc/buttons";

import { animateCursorToElementAndClick, animateCursorToElementAndClickAndFill } from "./misc/cursor";

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("task", () => {
  test("create task", async ({ page }) => {
    // task page
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-library", true, 500, "Clicking Library");
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-library-tasks", true, 500, "Clicking Tasks");
    await animateCursorToElementAndClick(page, "[data-testid='new-task-button']", true, 500, "Clicking New Task");

    const randomTaskName = faker.git.commitMessage(); // Math.random().toString(36).substring(7);
    await animateCursorToElementAndClickAndFill(page, "[data-testid='task-name-input']", randomTaskName, true, 500, "Filling Task Name");
    await animateCursorToElementAndClick(page, "[data-testid='option-Cloud']", true, 500, "Select Cloud");
    await animateCursorToElementAndClick(page, "#task-script-test [data-testid='option-Enabled']", true, 500, "Select Test Enabled");
    await animateCursorToElementAndClick(page, ".script-selector .fa-plus", true, 500, "Click New");

    await page.waitForSelector(".monaco-editor");
    await page.click(".monaco-editor");
    await page.keyboard.press("Control+A"); // For Mac users, use 'Meta+A' instead of 'Control+A'
    await page.keyboard.press("Backspace");
    await page.keyboard.insertText("$true");

    const saveButton = page.locator("[data-testid='savescript-button']");
    await animateCursorToElementAndClick(page, saveButton, true, 500, "Click Save");
    await animateCursorToElementAndClick(page, ".close-editor-link", true, 500, "Click Close icon");
    await animateCursorToElementAndClick(page, "[data-testid='task-submit-button']", true, 500, "Click Create");
    await clickModalYesButton(page);

    // deployment page
    const optionSingleTenant = page.locator("[data-testid='option-Single-Tenant']");
    await animateCursorToElementAndClick(page, optionSingleTenant, false, 500, "Click Single Tenant");
    await optionSingleTenant.click({ force: true });

    await animateCursorToElementAndClick(page, "[data-testid='tenantSelector-select']", true, 500, "Select Tenant");
    const immyOption = page.getByRole("option", { name: /(^immybot$)|(^immense networks$)/i }).first();
    await animateCursorToElementAndClick(page, immyOption, true, 500, "Select option");

    const wrapper = waitForDeployResponse(page);
    const deployButton = page.locator("div:text-is(\"Deploy\")");
    await animateCursorToElementAndClick(page, deployButton, true, 500, "Click Deploy");
    const response = await wrapper.response;
    expect202Response(response);
  });
});
