import { expect, test } from "@playwright/test";
import globalSetup from "./global-setup";
import { animateCursorToElementAndClick } from "./misc/cursor";

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("account dropdown", async () => {
  test("should contain getting started, preferences, theme, and logout links", async ({ page }) => {
    await page.goto("/");
    await animateCursorToElementAndClick(page, "[data-testid='nav-item-account']", true, 500, "Click Account");

    const gettingStartedLink = page.getByTestId("getting-started-dropdown-link");
    await expect(gettingStartedLink).toBeVisible();

    const preferencesLink = page
      .getByRole("link", { name: "Preferences" });
    await expect(preferencesLink).toBeVisible();

    const themeLink = page
      .getByTestId("nav-item-account-theme");
    await expect(themeLink).toBeVisible();

    const logoutLink = page
      .getByRole("link", { name: "Logout" });
    await expect(logoutLink).toBeVisible();
  });
});
