import { expect, test } from "@playwright/test";
import globalSetup from "./global-setup";
import { clickSidebarPreferenceLink, clickSidebarShowMoreLink } from "./misc/buttons";
import { animateCursorToElementAndClick, animateCursorToElementAndClickAndFill } from "./misc/cursor";

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("preference", () => {
  test("search system preference", async ({ page }) => {
    await clickSidebarShowMoreLink(page);
    await clickSidebarPreferenceLink(page);

    await animateCursorToElementAndClickAndFill(page, "[data-testid='search-system-preference-input']", "require", true, 500, "Filter");

    const count = await page.locator("[data-test-preference-group='system']").count();
    expect(count).toBe(3);

    await page.getByTestId("preference-group-system-remote-control").isVisible();
    await page.getByTestId("preference-title-require-consent-for-remote-sessions").isVisible();

    await page.getByTestId("preference-group-users").isVisible();
    await page.getByTestId("preference-msp-non-admins").isVisible();

    await page.getByTestId("preference-group-system-computers").isVisible();
    await page.getByTestId("preference-title-enable-non-essential").isVisible();
  });

  test("search tenant preference", async ({ page }) => {
    await clickSidebarShowMoreLink(page);
    await clickSidebarPreferenceLink(page);

    await page.getByTestId("tab-preference-tenant").click();

    await animateCursorToElementAndClickAndFill(page, "[data-testid='search-tenant-preference-input']", "cons", true, 500, "Filter");

    const count = await page.locator("[data-test-preference-group='tenant']").count();
    expect(count).toBe(2);

    await page.getByTestId("search-tenant-preference-input").isVisible();
    await page.getByTestId("preference-title-require-consent").isVisible();

    await page.getByTestId("preference-group-computers").isVisible();
    await page.getByTestId("preference-title-exclude-from").isVisible();
  });

  test("search user preference", async ({ page }) => {
    await clickSidebarShowMoreLink(page);
    await clickSidebarPreferenceLink(page);

    await animateCursorToElementAndClick(page, "[data-testid='tab-user-tenant']", true, 500, "Clicking User Tab");

    await animateCursorToElementAndClickAndFill(page, "[data-testid='search-user-preference-input']", "require", true, 500, "Filter");

    const count = await page.locator("[data-test-preference-group='user']").count();
    expect(count).toBe(0);

    await animateCursorToElementAndClickAndFill(page, "[data-testid='search-user-preference-input']", "color", true, 500, "Filter");

    await page.getByTestId("preference-group-theme").isVisible();
    await page.getByTestId("preference-title-color-theme").isVisible();
  });
});
