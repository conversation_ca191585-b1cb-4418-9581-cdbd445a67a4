import { expect } from "@playwright/test";
import { personsApi } from "../../src/api/backend/v1";
import { makeUid } from "../../src/utils/misc";
import { test } from "./fixture/personFixture";
import globalSetup from "./global-setup";
import {
  expect200Response,
  waitForUpdatePersonResponse,
} from "./misc/api";
import {
  clickByTestId,
  clickModalYesButton,
  clickSidebarPeopleLink,
  clickSidebarShowMoreLink,
} from "./misc/buttons";
import { fillPersonForm, getRandomEmail, getRandomName, SearchDxDataGrid } from "./misc/inputs";

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("person", () => {
  test("create person", async ({ personOperations }) => {
    const person = await personOperations.create();
    await personsApi.delete(person.id);
  });

  test("update person", async ({ page }) => {
    const randomName = getRandomName();
    const email = getRandomEmail();
    const person = await personsApi.post({
      emailAddress: email,
      firstName: randomName,
      lastName: randomName,
      tenantId: 1,
    });

    await clickSidebarShowMoreLink(page);
    await clickSidebarPeopleLink(page);
    await SearchDxDataGrid(page, email);
    await page.waitForTimeout(1000);
    await clickByTestId(page, "edit-person-btn", "Click Edit");

    // change data
    const newRandomName = getRandomName();
    const newEmail = getRandomEmail();
    const newAzureId = makeUid();

    await fillPersonForm(page, newRandomName, newRandomName, newEmail, newAzureId);

    const wrapper = waitForUpdatePersonResponse(page, person.id);

    await clickByTestId(page, "update-person-btn", "Click Update");

    const response = await wrapper.response;

    expect200Response(response);

    const body = await wrapper.getBody();
    expect(body.firstName).toBe(newRandomName);
    expect(body.lastName).toBe(newRandomName);
    expect(body.emailAddress).toBe(newEmail);
    expect(body.azurePrincipalId).toBe(newAzureId);

    await personsApi.delete(person.id);
  });

  test("delete person", async ({ page }) => {
    const randomName = getRandomName();
    const email = getRandomEmail();
    await personsApi.post({
      emailAddress: email,
      firstName: randomName,
      lastName: randomName,
      tenantId: 1,
    });

    await clickSidebarShowMoreLink(page);
    await clickSidebarPeopleLink(page);
    await SearchDxDataGrid(page, email);

    await clickByTestId(page, "delete-person-btn", "Click Delete");
    await clickModalYesButton(page);

    await page.waitForTimeout(1000);

    const el = page.getByTestId("delete-person-btn");
    await expect(el).toHaveCount(0);
  });
});
