import { expect, test } from "@playwright/test";
import globalSetup from "./global-setup";
import { animateCursorToElementAndClick } from "./misc/cursor";

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("mobile", async () => {
  test.use({ viewport: { width: 600, height: 800 } });

  test("sidebar is closed on mobile by default", async ({ page }) => {
    await page.goto("/");
    const hasClass = await page
      .locator("body")
      .evaluate(el => el.classList.contains("im-sidebar-hide"));
    expect(hasClass).toBeTruthy();
  });

  test("mobile sidebar closes on navigation", async ({ page }) => {
    await page.goto("/");
    await animateCursorToElementAndClick(page, ".im-sidebar-collapser", true, 500, "Clicking sidebar collapser");
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-dashboard", true, 500, "Clicking dashboard");
    const hasClass = await page
      .locator("body")
      .evaluate(el => el.classList.contains("im-sidebar-hide"));
    expect(hasClass).toBeTruthy();
  });
});

test.describe("desktop", () => {
  test.use({ viewport: { width: 1280, height: 800 } });

  test("sidebar is open on desktop by default", async ({ page }) => {
    await page.goto("/");
    const hasClass = await page
      .locator("body")
      .evaluate(el => el.classList.contains("im-sidebar-show"));
    expect(hasClass).toBeTruthy();
  });
});
