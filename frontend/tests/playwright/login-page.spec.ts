import { expect, test } from "@playwright/test";
import globalSetup from "./global-setup";

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("login page", async () => {
  test("should redirect to /computers if user is already authorized", async ({ page }) => {
    await page.goto("/login");

    await page.waitForURL(/.*computers/, { timeout: 5000 });

    const computersHeader = page.getByRole("heading", { name: "Computers" });

    await expect(computersHeader).toBeVisible();
  });
});
