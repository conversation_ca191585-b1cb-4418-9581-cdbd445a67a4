import { expect, Page, Route, test } from "@playwright/test";
import globalSetup from "./global-setup";
import { expect200Response, waitForResetTrainingResponse } from "./misc/api";
import { clickByTestId } from "./misc/buttons";
import { checkExternalNavigation } from "./misc/navigation";

async function mockApiResponse(page: Page, response: any) {
  await page.route("**/*", async (route: Route, request: any) => {
    if (request.url().includes("getting-started/checklist")) {
      // Change this to the URL you want to intercept
      // Mock response
      const jsonResponse = JSON.stringify(response);
      route.fulfill({
        status: 200,
        contentType: "application/json",
        body: jsonResponse,
      });
    }
    else {
      route.continue();
    }
  });
}

async function checkSideBarLink(page: Page, text: string) {
  await page.locator("#sidebar-nav-item-checklist").isVisible();
  const textContent = await page.locator("#sidebar-nav-item-checklist span").textContent();
  expect(textContent?.trim()).toBe(text);
}

async function checkAmountActiveSteps(page: Page, amount: number) {
  const activeSteps = await page.locator("#checklist-steps .checklist-step.active").count();
  expect(activeSteps).toBe(amount);
}

test.beforeAll(async () => {
  await globalSetup();
});

test.afterEach("Close the page", async ({ page }) => {
  await page.close();
});

test.describe("checklist", () => {
  test("navigating with 'installedImmyAgent' step incompleted", async ({ page }) => {
    await mockApiResponse(page, {
      enableGettingStartedWizard: true,
      activatedTrial: true,
      installedImmyAgent: false,
      ranOnboardingSession: false,
    });

    await page.goto("/checklist");

    await page.waitForTimeout(1000);

    await checkAmountActiveSteps(page, 2);

    await clickByTestId(page, "check-list-next-button", "Click Next");
    await page.locator("div[data-testid='machine-onboarding']").isVisible();
    await clickByTestId(page, "skip-setup-link", "Click Skip");
    await page.locator("div[data-testid='machine-onboarding']").isHidden();

    await checkSideBarLink(page, "Getting Started (1/3)");
  });

  test("navigating with 'ranOnboardingSession' step incompleted", async ({ page }) => {
    await mockApiResponse(page, {
      enableGettingStartedWizard: true,
      activatedTrial: true,
      installedImmyAgent: true,
      ranOnboardingSession: false,
    });

    await page.goto("/checklist");

    await page.waitForTimeout(1000);

    await checkAmountActiveSteps(page, 3);

    await checkSideBarLink(page, "Getting Started (2/3)");

    await clickByTestId(page, "check-list-next-button", "Click Next");

    expect(page.url()).toContain("/computers/new");
  });

  test("navigating with all basic steps completed", async ({ page, context }) => {
    await mockApiResponse(page, {
      enableGettingStartedWizard: true,
      activatedTrial: true,
      installedImmyAgent: true,
      ranOnboardingSession: true,
    });

    await page.goto("/checklist");

    await page.waitForTimeout(1000);

    await checkAmountActiveSteps(page, 3);

    await page.locator("#sidebar-nav-item-checklist").isHidden();

    await checkExternalNavigation(
      page,
      context,
      "button.documentation",
      "https://docs.immy.bot/onboarding.html",
    );
  });

  test("Training reset", async ({ page }) => {
    await mockApiResponse(page, {
      enableGettingStartedWizard: true,
      activatedTrial: true,
      installedImmyAgent: true,
      ranOnboardingSession: true,
    });

    await page.goto("/checklist");

    await page.waitForTimeout(1000);

    await checkAmountActiveSteps(page, 3);

    await page.locator("#sidebar-nav-item-checklist").isHidden();

    const wrapper = waitForResetTrainingResponse(page);
    await clickByTestId(page, "reset-training-btn", "Click Reset Training");
    const response = await wrapper.response;
    expect200Response(response);

    await page.waitForTimeout(2000);
  });
});
