import { expect, test } from "@playwright/test";
import globalSetup from "./global-setup";
import { animateCursorToElementAndClick } from "./misc/cursor";

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("download agent modal", () => {
  test("ISO and PPKG buttons are present", async ({ page }) => {
    await animateCursorToElementAndClick(page, ".im-installer-section > .nav-item > .nav-link", true, 500, "Click Download");
    await animateCursorToElementAndClick(page, "#next-button-download-immyBot-agent-installer-modal", true, 500, "Click Next");
    const ppkgbutton = page.locator("[data-testid='download-ppkg-button']");
    expect(ppkgbutton).not.toBeNull();
    const isobutton = page.locator("[data-testid='download-iso-button']");
    expect(isobutton).not.toBeNull();
  });
});
