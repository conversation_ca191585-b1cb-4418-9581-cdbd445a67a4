import { test } from "./fixture/personFixture";
import globalSetup from "./global-setup";
import {
  clickModalYesButton,
  clickSidebarPeopleLink,
  clickSidebarUserLink,
} from "./misc/buttons";
import { animateCursorToElementAndClick } from "./misc/cursor";
import { SearchDxDataGrid } from "./misc/inputs";

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

test.describe("user", () => {
  test("create user", async ({ page, personOperations }) => {
    const person = await personOperations.create(false);

    await clickSidebarPeopleLink(page);
    await SearchDxDataGrid(page, person.emailAddress);

    const addTechnicianButton = page.locator("button:has-text(\" Add Technician \")");
    await animateCursorToElementAndClick(page, addTechnicianButton, true, 200, "Click Add Technician");
    await clickModalYesButton(page);

    await clickSidebarUserLink(page);
    await SearchDxDataGrid(page, person.emailAddress);

    await page.locator(`span.dx-datagrid-search-text:has-text("${person.emailAddress}")`).isVisible();
  });
});
