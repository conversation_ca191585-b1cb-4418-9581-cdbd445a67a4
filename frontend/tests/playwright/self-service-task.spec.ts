import { faker } from "@faker-js/faker";
import { expect, test } from "@playwright/test";
import { maintenanceTasksApi, scriptsApi, targetAssignmentsApiV2 } from "../../src/api/backend/v1";
import globalSetup from "./global-setup";
import { expect200Response, waitForTargetAssignmentsResponse } from "./misc/api";
import { clickModalYesButton } from "./misc/buttons";
import { animateCursorToElementAndClick, animateCursorToElementAndClickAndFill } from "./misc/cursor";

let targetAssignmentId: number;
let maintenanceTaskId: string;
let scriptId: string;

test.beforeEach(async ({ page }) => {
  await page.goto("/");
});

test.beforeAll(async () => {
  await globalSetup();
});

test.afterAll(async () => {
  // delete deployment, target assignment, and script
  try {
    // target assignment deletion
    if (targetAssignmentId) {
      await targetAssignmentsApiV2.deleteTargetAssignment(targetAssignmentId);
    }
    // maintenance task deletion
    if (maintenanceTaskId) {
      await maintenanceTasksApi.deleteLocalMaintenanceTask(+maintenanceTaskId);
    }
    // script deletion
    if (scriptId) {
      await scriptsApi.deleteLocalScript(+scriptId);
    }
  }
  catch (error) {
    console.error("Error deleting target assignment, maintenance task, or script", error);
  }
});

test.describe("self service task", () => {
  test("task should appear in self service after deployment", async ({ page }) => {
    // Create cloud task
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-library", true, 500);
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-library-tasks", true, 500);
    await animateCursorToElementAndClick(page, "[data-testid='new-task-button']", true, 500);

    const randomTaskName = faker.git.commitMessage();
    await animateCursorToElementAndClickAndFill(page, "[data-testid='task-name-input']", randomTaskName, true, 500);

    // Select Cloud and enable testing
    await animateCursorToElementAndClick(page, "[data-testid='option-Cloud']", true, 500);
    await animateCursorToElementAndClick(page, "#task-script-test [data-testid='option-Enabled']", true, 500);

    // Add script content
    await animateCursorToElementAndClick(page, ".script-selector .fa-plus", true, 500);
    await page.waitForSelector(".monaco-editor");
    await page.click(".monaco-editor");
    await page.keyboard.press("Control+A");
    await page.keyboard.press("Backspace");
    await page.keyboard.insertText("$true");

    // Save script and wait for script creation response
    const saveButton = page.locator("[data-testid='savescript-button']");
    const scriptPromise = page.waitForResponse(
      response => response.url().includes("/api/v1/scripts/local")
        && response.request().method() === "POST"
        && response.status() === 200,
    );
    await animateCursorToElementAndClick(page, saveButton, true, 500);

    // Capture the script ID
    const scriptResponse = await scriptPromise;
    const scriptData = await scriptResponse.json();
    scriptId = scriptData.id;

    await animateCursorToElementAndClick(page, ".close-editor-link", true, 500);
    await animateCursorToElementAndClick(page, "[data-testid='task-submit-button']", true, 500);
    const maintenanceTaskPromise = page.waitForResponse(
      response => response.url().includes("/api/v1/maintenance-tasks/local")
        && response.request().method() === "POST"
        && response.status() === 200,
    );
    await clickModalYesButton(page);

    const maintenanceTaskResponse = await maintenanceTaskPromise;
    maintenanceTaskId = (await maintenanceTaskResponse.json()).id;

    // Configure deployment
    const optionSingleTenant = page.locator("[data-testid='option-Single-Tenant']");
    await animateCursorToElementAndClick(page, optionSingleTenant, false, 500);
    await optionSingleTenant.click({ force: true });

    await animateCursorToElementAndClick(page, "[data-testid='tenantSelector-select']", true, 500);
    const immyOption = page.getByRole("option", { name: /(^immybot$)|(^immense networks$)/i }).first();
    await animateCursorToElementAndClick(page, immyOption, true, 500);

    // Select Self Service visibility
    const visibilitySelector = page.locator("[data-testid='target-visibility-selector']");
    await visibilitySelector.waitFor();
    const selfServiceLabel = visibilitySelector.locator("label.custom-control-label", { hasText: "Self Service" });
    await animateCursorToElementAndClick(page, selfServiceLabel, true, 500);

    // Submit deployment and wait for target assignments response
    const targetWrapper = waitForTargetAssignmentsResponse(page);
    const deployButton = page.locator("[data-testid='deployment-submit-button']");
    await animateCursorToElementAndClick(page, deployButton, true, 500);
    const targetResponse = await targetWrapper.response;
    const body = await targetWrapper.getBody();
    targetAssignmentId = body.id;
    expect200Response(targetResponse);

    // Navigate to self service and verify task
    await animateCursorToElementAndClick(page, "#sidebar-nav-item-self-service", true, 500);

    // Verify task is visible and has correct name
    const taskCard = page.locator(`.d-flex.align-items-center:has(h3:text-is("${randomTaskName}"))`)
      .filter({ has: page.locator(".btn-primary:has-text(\"Deploy\")") });
    const button = await taskCard.locator(".btn-primary:has-text(\"Deploy\")");

    // Wait for and verify the maintenance session API call
    const maintenanceSessionPromise = page.waitForResponse(
      response => response.url().includes("/api/v1/maintenance-sessions/")
        && response.url().includes("includeActions=true")
        && response.url().includes("includeStages=true")
        && response.url().includes("includeActionActivities=true")
        && response.status() === 200,
    );

    await button.click();
    const maintenanceSessionResponse = await maintenanceSessionPromise;
    expect(maintenanceSessionResponse.status()).toBe(200);
  });
});
