/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccessRequest: typeof import('./src/components/AccessRequest.vue')['default']
    AccessRequests: typeof import('./src/components/AccessRequests.vue')['default']
    AccountDropdownNavItem: typeof import('./src/components/AccountDropdownNavItem.vue')['default']
    ActionLogsPanel: typeof import('./src/components/ActionLogsPanel.vue')['default']
    AddNewTag: typeof import('./src/components/AddNewTag.vue')['default']
    AgentIdentificationLogItem: typeof import('./src/components/AgentIdentificationLogItem.vue')['default']
    AgentIdentificationLogsList: typeof import('./src/components/AgentIdentificationLogsList.vue')['default']
    AgentIdentificationLogsStream: typeof import('./src/components/AgentIdentificationLogsStream.vue')['default']
    AgentInstallerOnboardingOptionsForm: typeof import('./src/components/AgentInstallerOnboardingOptionsForm.vue')['default']
    AgentTable: typeof import('./src/components/AgentTable.vue')['default']
    AgentTableRow: typeof import('./src/components/AgentTableRow.vue')['default']
    AppAlert: typeof import('./src/components/AppAlert.vue')['default']
    AppAlertMessage: typeof import('./src/components/AppAlertMessage.vue')['default']
    AppBody: typeof import('./src/components/AppBody.vue')['default']
    AppHeader: typeof import('./src/components/AppHeader.vue')['default']
    ApplicationAuditProperties: typeof import('./src/components/ApplicationAuditProperties.vue')['default']
    AppMetricsContextList: typeof import('./src/components/AppMetricsContextList.vue')['default']
    AppMetricsCounter: typeof import('./src/components/AppMetricsCounter.vue')['default']
    AppMetricsGauge: typeof import('./src/components/AppMetricsGauge.vue')['default']
    AppMetricsHistogram: typeof import('./src/components/AppMetricsHistogram.vue')['default']
    AppMetricsMeter: typeof import('./src/components/AppMetricsMeter.vue')['default']
    AppSidebar: typeof import('./src/components/AppSidebar.vue')['default']
    AssignRoleAlert: typeof import('./src/components/AssignRoleAlert.vue')['default']
    AssignRolesForm: typeof import('./src/components/AssignRolesForm.vue')['default']
    AuditableColumnData: typeof import('./src/components/AuditableColumnData.vue')['default']
    AuditableColumnText: typeof import('./src/components/AuditableColumnText.vue')['default']
    AuditTable: typeof import('./src/components/AuditTable.vue')['default']
    AuthTokenAcquisitionModal: typeof import('./src/components/AuthTokenAcquisitionModal.vue')['default']
    AzureCustomerMapper: typeof import('./src/components/AzureCustomerMapper.vue')['default']
    AzureCustomerPreconsentResult: typeof import('./src/components/AzureCustomerPreconsentResult.vue')['default']
    AzureError: typeof import('./src/components/AzureError.vue')['default']
    AzureGroupSelector: typeof import('./src/components/AzureGroupSelector.vue')['default']
    AzurePermissionLevelForm: typeof import('./src/components/AzurePermissionLevelForm.vue')['default']
    AzureTenantAzureErrorsList: typeof import('./src/components/AzureTenantAzureErrorsList.vue')['default']
    AzureTenantConsentDetails: typeof import('./src/components/AzureTenantConsentDetails.vue')['default']
    AzureTenantLinkedTenants: typeof import('./src/components/AzureTenantLinkedTenants.vue')['default']
    BaseDynamicForm: typeof import('./src/components/BaseDynamicForm.vue')['default']
    BrandingEmailPreview: typeof import('./src/components/BrandingEmailPreview.vue')['default']
    BrandingSelector: typeof import('./src/components/BrandingSelector.vue')['default']
    Breadcrumb: typeof import('./src/components/Breadcrumb.vue')['default']
    BulkDeleteTenantsModal: typeof import('./src/components/BulkDeleteTenantsModal.vue')['default']
    BulkMergeTenantsModal: typeof import('./src/components/BulkMergeTenantsModal.vue')['default']
    BulkOnboardingOptionOverrideForm: typeof import('./src/components/BulkOnboardingOptionOverrideForm.vue')['default']
    CancelSessionButton: typeof import('./src/components/CancelSessionButton.vue')['default']
    CheckListActionContent: typeof import('./src/components/CheckListActionContent.vue')['default']
    CheckListActionContentCreateTenants: typeof import('./src/components/CheckListActionContentCreateTenants.vue')['default']
    CheckListActionContentCustomize: typeof import('./src/components/CheckListActionContentCustomize.vue')['default']
    CheckListActionContentFreeTrial: typeof import('./src/components/CheckListActionContentFreeTrial.vue')['default']
    CheckListActionContentOnboardFirst: typeof import('./src/components/CheckListActionContentOnboardFirst.vue')['default']
    CheckListActionContentQuickDemo: typeof import('./src/components/CheckListActionContentQuickDemo.vue')['default']
    CheckListActionContentRunDeployment: typeof import('./src/components/CheckListActionContentRunDeployment.vue')['default']
    CheckListActionGroup: typeof import('./src/components/CheckListActionGroup.vue')['default']
    CheckListActions: typeof import('./src/components/CheckListActions.vue')['default']
    CheckListFooter: typeof import('./src/components/CheckListFooter.vue')['default']
    ChecklistItem: typeof import('./src/components/ChecklistItem.vue')['default']
    CloneButton: typeof import('./src/components/Base/CloneButton.vue')['default']
    CollapsibleAlert: typeof import('./src/components/CollapsibleAlert.vue')['default']
    Comments: typeof import('./src/components/Comments.vue')['default']
    ComputerActionsSidebar: typeof import('./src/components/ComputerActionsSidebar.vue')['default']
    ComputerDetailsOverviewTab: typeof import('./src/components/ComputerDetailsOverviewTab.vue')['default']
    ComputerDetailsSoftwareTab: typeof import('./src/components/ComputerDetailsSoftwareTab.vue')['default']
    ComputerDetailsSpaceChart: typeof import('./src/components/ComputerDetailsSpaceChart.vue')['default']
    ComputerIcon: typeof import('./src/components/ComputerIcon.vue')['default']
    ComputerInventorySoftwareSearchByName: typeof import('./src/components/ComputerInventorySoftwareSearchByName.vue')['default']
    ComputerInventorySoftwareSearchByUpgradeCode: typeof import('./src/components/ComputerInventorySoftwareSearchByUpgradeCode.vue')['default']
    ComputerInventorySoftwareTable: typeof import('./src/components/ComputerInventorySoftwareTable.vue')['default']
    ComputerInventoryTable: typeof import('./src/components/ComputerInventoryTable.vue')['default']
    ComputerInventoryTableKeyDetails: typeof import('./src/components/ComputerInventoryTableKeyDetails.vue')['default']
    ComputerOfflineBehaviorSelector: typeof import('./src/components/ComputerOfflineBehaviorSelector.vue')['default']
    ComputerOnboardingForm: typeof import('./src/components/ComputerOnboardingForm.vue')['default']
    ComputerOnboardingOverridableTasksForm: typeof import('./src/components/ComputerOnboardingOverridableTasksForm.vue')['default']
    ComputerOnlineStatus: typeof import('./src/components/ComputerOnlineStatus.vue')['default']
    ComputerProviderTypeButton: typeof import('./src/components/ComputerProviderTypeButton.vue')['default']
    ComputerRegistryDetails: typeof import('./src/components/ComputerRegistryDetails.vue')['default']
    ComputerRegistrySearchBox: typeof import('./src/components/ComputerRegistrySearchBox.vue')['default']
    ComputerRegistrySearchResultsPane: typeof import('./src/components/ComputerRegistrySearchResultsPane.vue')['default']
    ComputerRegistryTab: typeof import('./src/components/ComputerRegistryTab.vue')['default']
    ComputerRegistryTaskGenerator: typeof import('./src/components/ComputerRegistryTaskGenerator.vue')['default']
    ComputerSelectBox: typeof import('./src/components/ComputerSelectBox.vue')['default']
    ComputerTable: typeof import('./src/components/ComputerTable.vue')['default']
    ComputerTableAgentIcon: typeof import('./src/components/ComputerTableAgentIcon.vue')['default']
    ComputerTableForSchedulePage: typeof import('./src/components/ComputerTableForSchedulePage.vue')['default']
    ComputerTags: typeof import('./src/components/ComputerTags.vue')['default']
    ComputerTerminal: typeof import('./src/components/ComputerTerminal.vue')['default']
    ComputerTimelineEventsTable: typeof import('./src/components/ComputerTimelineEventsTable.vue')['default']
    ComputerToolBar: typeof import('./src/components/ComputerToolBar.vue')['default']
    CopyToClipboardButton: typeof import('./src/components/CopyToClipboardButton.vue')['default']
    CopyToClipboardInput: typeof import('./src/components/CopyToClipboardInput.vue')['default']
    CorrelatedOnboarding: typeof import('./src/components/CorrelatedOnboarding.vue')['default']
    CreateUsersFromPeopleForm: typeof import('./src/components/CreateUsersFromPeopleForm.vue')['default']
    DashboardSessionCounts: typeof import('./src/components/DashboardSessionCounts.vue')['default']
    DefaultContainer: typeof import('./src/components/DefaultContainer.vue')['default']
    DeleteButton: typeof import('./src/components/Base/DeleteButton.vue')['default']
    DeletedComputerActionsSidebar: typeof import('./src/components/DeletedComputerActionsSidebar.vue')['default']
    Deployment: typeof import('./src/components/Deployment.vue')['default']
    DeploymentActionsSidebar: typeof import('./src/components/DeploymentActionsSidebar.vue')['default']
    DeploymentComputerTargetColumn: typeof import('./src/components/Deployment/DeploymentComputerTargetColumn.vue')['default']
    DeploymentDynamicForm: typeof import('./src/components/DeploymentDynamicForm.vue')['default']
    DeploymentHiddenTargetsAlert: typeof import('./src/components/DeploymentHiddenTargetsAlert.vue')['default']
    DeploymentIntegrationSelector: typeof import('./src/components/DeploymentIntegrationSelector.vue')['default']
    DeploymentList: typeof import('./src/components/DeploymentList.vue')['default']
    DeploymentMigrationNotification: typeof import('./src/components/DeploymentMigrationNotification.vue')['default']
    DeploymentNotes: typeof import('./src/components/DeploymentNotes.vue')['default']
    DeploymentOptions: typeof import('./src/components/DeploymentOptions.vue')['default']
    DeploymentPersonTargetColumn: typeof import('./src/components/Deployment/DeploymentPersonTargetColumn.vue')['default']
    DeploymentTable: typeof import('./src/components/DeploymentTable.vue')['default']
    DeploymentTenantTargetColumn: typeof import('./src/components/Deployment/DeploymentTenantTargetColumn.vue')['default']
    DeploymentToolbar: typeof import('./src/components/DeploymentToolbar.vue')['default']
    DetectedComputerSoftware: typeof import('./src/components/DetectedComputerSoftware.vue')['default']
    DevBanner: typeof import('./src/components/DevBanner.vue')['default']
    DeviceUpdateSchemaForm: typeof import('./src/components/DeviceUpdateSchemaForm.vue')['default']
    DisabledOverlay: typeof import('./src/components/DisabledOverlay.vue')['default']
    DismissableAlertButton: typeof import('./src/components/DismissableAlertButton.vue')['default']
    DismissibleAlert: typeof import('./src/components/DismissibleAlert.vue')['default']
    DownloadAgentBashInstallScriptForm: typeof import('./src/components/DownloadAgentBashInstallScriptForm.vue')['default']
    DownloadAgentExeInstallerForm: typeof import('./src/components/DownloadAgentExeInstallerForm.vue')['default']
    DownloadAgentInstallerForm: typeof import('./src/components/DownloadAgentInstallerForm.vue')['default']
    DownloadAgentInstallerModal: typeof import('./src/components/DownloadAgentInstallerModal.vue')['default']
    DownloadAgentInstallerNavItem: typeof import('./src/components/DownloadAgentInstallerNavItem.vue')['default']
    DownloadAgentPowerShellInstallScriptForm: typeof import('./src/components/DownloadAgentPowerShellInstallScriptForm.vue')['default']
    DownloadAgentPpkgForm: typeof import('./src/components/DownloadAgentPpkgForm.vue')['default']
    DownloadAgentWindowsSandboxForm: typeof import('./src/components/DownloadAgentWindowsSandboxForm.vue')['default']
    DownloadInstallerHelpAlertContent: typeof import('./src/components/DownloadInstallerHelpAlertContent.vue')['default']
    DxActionTable: typeof import('./src/components/DxActionTable.vue')['default']
    DxAgentTable: typeof import('./src/components/DxAgentTable.vue')['default']
    DxAgentTableIntegrationCell: typeof import('./src/components/DxAgentTableIntegrationCell.vue')['default']
    DxComputerTable: typeof import('./src/components/DxComputerTable.vue')['default']
    DxPersonTable: typeof import('./src/components/DxPersonTable.vue')['default']
    DxSessionTable: typeof import('./src/components/DxSessionTable.vue')['default']
    DynamicFormField: typeof import('./src/components/DynamicFormField.vue')['default']
    DynamicFormFieldBooleanInput: typeof import('./src/components/DynamicFormFieldBooleanInput.vue')['default']
    DynamicFormFieldDateTimeInput: typeof import('./src/components/DynamicFormFieldDateTimeInput.vue')['default']
    DynamicFormFieldDefaultOverrideLink: typeof import('./src/components/DynamicFormFieldDefaultOverrideLink.vue')['default']
    DynamicFormFieldDefaultValue: typeof import('./src/components/DynamicFormFieldDefaultValue.vue')['default']
    DynamicFormFieldDropdownInput: typeof import('./src/components/DynamicFormFieldDropdownInput.vue')['default']
    DynamicFormFieldDropdownMultiSelectInput: typeof import('./src/components/DynamicFormFieldDropdownMultiSelectInput.vue')['default']
    DynamicFormFieldHelpText: typeof import('./src/components/DynamicFormFieldHelpText.vue')['default']
    DynamicFormFieldKeyValuePairInput: typeof import('./src/components/DynamicFormFieldKeyValuePairInput.vue')['default']
    DynamicFormFieldMediaInput: typeof import('./src/components/DynamicFormFieldMediaInput.vue')['default']
    DynamicFormFieldNumberArrayInput: typeof import('./src/components/DynamicFormFieldNumberArrayInput.vue')['default']
    DynamicFormFieldNumberInput: typeof import('./src/components/DynamicFormFieldNumberInput.vue')['default']
    DynamicFormFieldOauthConsentInput: typeof import('./src/components/DynamicFormFieldOauthConsentInput.vue')['default']
    DynamicFormFieldOnboardingHelpText: typeof import('./src/components/DynamicFormFieldOnboardingHelpText.vue')['default']
    DynamicFormFieldOnboardingOption: typeof import('./src/components/DynamicFormFieldOnboardingOption.vue')['default']
    DynamicFormFieldOverridableDefaultValue: typeof import('./src/components/DynamicFormFieldOverridableDefaultValue.vue')['default']
    DynamicFormFieldOverrideLink: typeof import('./src/components/DynamicFormFieldOverrideLink.vue')['default']
    DynamicFormFieldParameterSetInput: typeof import('./src/components/DynamicFormFieldParameterSetInput.vue')['default']
    DynamicFormFieldPasswordInput: typeof import('./src/components/DynamicFormFieldPasswordInput.vue')['default']
    DynamicFormFieldPersonInput: typeof import('./src/components/DynamicFormFieldPersonInput.vue')['default']
    DynamicFormFieldSwitchInput: typeof import('./src/components/DynamicFormFieldSwitchInput.vue')['default']
    DynamicFormFieldTextArrayInput: typeof import('./src/components/DynamicFormFieldTextArrayInput.vue')['default']
    DynamicFormFieldTextInput: typeof import('./src/components/DynamicFormFieldTextInput.vue')['default']
    DynamicFormFieldUriInput: typeof import('./src/components/DynamicFormFieldUriInput.vue')['default']
    DynamicFormSimpleView: typeof import('./src/components/DynamicFormSimpleView.vue')['default']
    DynamicFormSimpleViewRemoveButton: typeof import('./src/components/DynamicFormSimpleViewRemoveButton.vue')['default']
    DynamicIntegrationLogo: typeof import('./src/components/DynamicIntegrationLogo.vue')['default']
    DynamicIntegrationTypeBadge: typeof import('./src/components/DynamicIntegrationTypeBadge.vue')['default']
    DynamicInventoryResults: typeof import('./src/components/DynamicInventoryResults.vue')['default']
    DynamicVersionsHelpAlertContent: typeof import('./src/components/DynamicVersionsHelpAlertContent.vue')['default']
    EmbeddedMaintenanceTaskDetailsForm: typeof import('./src/components/EmbeddedMaintenanceTaskDetailsForm.vue')['default']
    EmbeddedScriptSelector: typeof import('./src/components/EmbeddedScriptSelector.vue')['default']
    EnumText: typeof import('./src/components/EnumText.vue')['default']
    EphemeralAgentDetailsPanel: typeof import('./src/components/EphemeralAgentDetailsPanel.vue')['default']
    FeedbackSidebar: typeof import('./src/components/FeedbackSidebar.vue')['default']
    FormGroupWithHelp: typeof import('./src/components/FormGroupWithHelp.vue')['default']
    GettingStartedWizardModal: typeof import('./src/components/GettingStartedWizardModal.vue')['default']
    GridColumnSplitter: typeof import('./src/components/GridColumnSplitter.vue')['default']
    GroupRolesDropdown: typeof import('./src/components/GroupRolesDropdown.vue')['default']
    HeaderNavbar: typeof import('./src/components/HeaderNavbar.vue')['default']
    ImmyAlert: typeof import('./src/components/ImmyAlert.vue')['default']
    ImmyBadge: typeof import('./src/components/ImmyBadge.vue')['default']
    ImmyBotRemoteControlFeatureNotSupportedAlert: typeof import('./src/components/ImmyBotRemoteControlFeatureNotSupportedAlert.vue')['default']
    ImmyButton: typeof import('./src/components/ImmyButton.vue')['default']
    ImmyButtonGroup: typeof import('./src/components/ImmyButtonGroup.vue')['default']
    ImmyCard: typeof import('./src/components/ImmyCard.vue')['default']
    ImmyCardBody: typeof import('./src/components/ImmyCardBody.vue')['default']
    ImmyCardFooter: typeof import('./src/components/ImmyCardFooter.vue')['default']
    ImmyCardHeader: typeof import('./src/components/ImmyCardHeader.vue')['default']
    ImmyCardText: typeof import('./src/components/ImmyCardText.vue')['default']
    ImmyCheckBox: typeof import('./src/components/ImmyCheckBox.vue')['default']
    ImmyCheckBoxGroup: typeof import('./src/components/ImmyCheckBoxGroup.vue')['default']
    ImmyCol: typeof import('./src/components/ImmyCol.vue')['default']
    ImmyCollapse: typeof import('./src/components/ImmyCollapse.vue')['default']
    ImmyDocs: typeof import('./src/components/ImmyDocs.vue')['default']
    ImmyDropdown: typeof import('./src/components/ImmyDropdown.vue')['default']
    ImmyDropdownItem: typeof import('./src/components/ImmyDropdownItem.vue')['default']
    ImmyDxDataGrid: typeof import('./src/components/ImmyDxDataGrid.vue')['default']
    ImmyFileUploader: typeof import('./src/components/ImmyFileUploader.vue')['default']
    ImmyFormGroup: typeof import('./src/components/ImmyFormGroup.vue')['default']
    ImmyFormInvalidFeedBack: typeof import('./src/components/ImmyFormInvalidFeedBack.vue')['default']
    ImmyFormTags: typeof import('./src/components/ImmyFormTags.vue')['default']
    ImmyInput: typeof import('./src/components/ImmyInput.vue')['default']
    ImmyInputGroup: typeof import('./src/components/ImmyInputGroup.vue')['default']
    ImmyInputGroupAddOn: typeof import('./src/components/ImmyInputGroupAddOn.vue')['default']
    ImmyInputGroupAppend: typeof import('./src/components/ImmyInputGroupAppend.vue')['default']
    ImmyInputGroupPrepend: typeof import('./src/components/ImmyInputGroupPrepend.vue')['default']
    ImmyInputGroupText: typeof import('./src/components/ImmyInputGroupText.vue')['default']
    ImmyLink: typeof import('./src/components/ImmyLink.vue')['default']
    ImmyList: typeof import('./src/components/ImmyList.vue')['default']
    ImmyListGroup: typeof import('./src/components/ImmyListGroup.vue')['default']
    ImmyListGroupItem: typeof import('./src/components/ImmyListGroupItem.vue')['default']
    ImmyMediaUploader: typeof import('./src/components/ImmyMediaUploader.vue')['default']
    ImmyModal: typeof import('./src/components/ImmyModal.vue')['default']
    ImmyNavbar: typeof import('./src/components/ImmyNavbar.vue')['default']
    ImmyNavbarBrand: typeof import('./src/components/ImmyNavbarBrand.vue')['default']
    ImmyNavBarToggle: typeof import('./src/components/ImmyNavBarToggle.vue')['default']
    ImmyNavItem: typeof import('./src/components/ImmyNavItem.vue')['default']
    ImmyPagination: typeof import('./src/components/ImmyPagination.vue')['default']
    ImmyPaginationNavButton: typeof import('./src/components/ImmyPaginationNavButton.vue')['default']
    ImmyPaginationPage: typeof import('./src/components/ImmyPaginationPage.vue')['default']
    ImmyRadio: typeof import('./src/components/ImmyRadio.vue')['default']
    ImmyRadioGroup: typeof import('./src/components/ImmyRadioGroup.vue')['default']
    ImmyRow: typeof import('./src/components/ImmyRow.vue')['default']
    ImmySecondarySidebar: typeof import('./src/components/ImmySecondarySidebar.vue')['default']
    ImmySelect: typeof import('./src/components/ImmySelect.vue')['default']
    ImmySystemIPAddressMessageBox: typeof import('./src/components/ImmySystemIPAddressMessageBox.vue')['default']
    ImmyTabBody: typeof import('./src/components/ImmyTabBody.vue')['default']
    ImmyTabHeader: typeof import('./src/components/ImmyTabHeader.vue')['default']
    ImmyTabItem: typeof import('./src/components/ImmyTabItem.vue')['default']
    ImmyTabs: typeof import('./src/components/ImmyTabs.vue')['default']
    ImmyTextArea: typeof import('./src/components/ImmyTextArea.vue')['default']
    IntegrationDynamicForm: typeof import('./src/components/IntegrationDynamicForm.vue')['default']
    IntegrationTypeSelector: typeof import('./src/components/IntegrationTypeSelector.vue')['default']
    InvalidFeedback: typeof import('./src/components/InvalidFeedback.vue')['default']
    LicenseDeploymentReferences: typeof import('./src/components/LicenseDeploymentReferences.vue')['default']
    LicenseFileUploader: typeof import('./src/components/LicenseFileUploader.vue')['default']
    LicenseSelector: typeof import('./src/components/LicenseSelector.vue')['default']
    LoadButton: typeof import('./src/components/LoadButton.vue')['default']
    LoadLink: typeof import('./src/components/LoadLink.vue')['default']
    LocalToGlobalMigrator: typeof import('./src/components/LocalToGlobalMigrator.vue')['default']
    LogsPanel: typeof import('./src/components/LogsPanel.vue')['default']
    LogsPanelItem: typeof import('./src/components/LogsPanelItem.vue')['default']
    LogsPanelItemNew: typeof import('./src/components/newui/LogsPanelItemNew.vue')['default']
    LogsPanelNew: typeof import('./src/components/newui/LogsPanelNew.vue')['default']
    MachineOnboarding: typeof import('./src/components/MachineOnboarding.vue')['default']
    MachineOnboardingCard: typeof import('./src/components/MachineOnboardingCard.vue')['default']
    MachineOnboardingDownload: typeof import('./src/components/MachineOnboardingDownload.vue')['default']
    MachineOnboardingSetup: typeof import('./src/components/MachineOnboardingSetup.vue')['default']
    MachineOnboardingSteps: typeof import('./src/components/MachineOnboardingSteps.vue')['default']
    MaintenanceActionActivityItem: typeof import('./src/components/MaintenanceActionActivityItem.vue')['default']
    MaintenanceActionActivityList: typeof import('./src/components/MaintenanceActionActivityList.vue')['default']
    MaintenanceActionDesiredStatePhrase: typeof import('./src/components/MaintenanceActionDesiredStatePhrase.vue')['default']
    MaintenanceActionDesiredVersionPhrase: typeof import('./src/components/MaintenanceActionDesiredVersionPhrase.vue')['default']
    MaintenanceActionDetectedVersionPhrase: typeof import('./src/components/MaintenanceActionDetectedVersionPhrase.vue')['default']
    MaintenanceActionDuration: typeof import('./src/components/MaintenanceActionDuration.vue')['default']
    MaintenanceActionList: typeof import('./src/components/MaintenanceActionList.vue')['default']
    MaintenanceActionListItem: typeof import('./src/components/MaintenanceActionListItem.vue')['default']
    MaintenanceActionListItemDetails: typeof import('./src/components/MaintenanceActionListItemDetails.vue')['default']
    MaintenanceActionListItemProgress: typeof import('./src/components/MaintenanceActionListItemProgress.vue')['default']
    MaintenanceActionMaintenanceName: typeof import('./src/components/MaintenanceActionMaintenanceName.vue')['default']
    MaintenanceActionReasonPhrase: typeof import('./src/components/MaintenanceActionReasonPhrase.vue')['default']
    MaintenanceActionRelationshipBadges: typeof import('./src/components/MaintenanceActionRelationshipBadges.vue')['default']
    MaintenanceActionResultPhrase: typeof import('./src/components/MaintenanceActionResultPhrase.vue')['default']
    MaintenanceActionResultReasonPhrase: typeof import('./src/components/MaintenanceActionResultReasonPhrase.vue')['default']
    MaintenanceActionRunDetails: typeof import('./src/components/MaintenanceActionRunDetails.vue')['default']
    MaintenanceActionStatusPhrase: typeof import('./src/components/MaintenanceActionStatusPhrase.vue')['default']
    MaintenanceActionTypePhrase: typeof import('./src/components/MaintenanceActionTypePhrase.vue')['default']
    MaintenanceItemAuditTable: typeof import('./src/components/MaintenanceItemAuditTable.vue')['default']
    MaintenanceItemAuditTableDynamicCell: typeof import('./src/components/MaintenanceItemAuditTableDynamicCell.vue')['default']
    MaintenanceItemColumn: typeof import('./src/components/MaintenanceItemColumn.vue')['default']
    MaintenanceItemOrderer: typeof import('./src/components/MaintenanceItemOrderer.vue')['default']
    MaintenanceItemOrderList: typeof import('./src/components/MaintenanceItemOrderList.vue')['default']
    MaintenanceItemSelector: typeof import('./src/components/MaintenanceItemSelector.vue')['default']
    MaintenanceItemSelectorMaintenanceTaskDetails: typeof import('./src/components/MaintenanceItemSelectorMaintenanceTaskDetails.vue')['default']
    MaintenanceItemSelectorSearch: typeof import('./src/components/MaintenanceItemSelectorSearch.vue')['default']
    MaintenanceItemSelectorSoftwareDetails: typeof import('./src/components/MaintenanceItemSelectorSoftwareDetails.vue')['default']
    MaintenanceSessionActionsSidebar: typeof import('./src/components/MaintenanceSessionActionsSidebar.vue')['default']
    MaintenanceSessionDetails: typeof import('./src/components/MaintenanceSessionDetails.vue')['default']
    MaintenanceSessionDetailsActionList: typeof import('./src/components/MaintenanceSessionDetailsActionList.vue')['default']
    MaintenanceSessionDetailsInfoPanel: typeof import('./src/components/MaintenanceSessionDetailsInfoPanel.vue')['default']
    MaintenanceSessionDetailsLogsPanel: typeof import('./src/components/MaintenanceSessionDetailsLogsPanel.vue')['default']
    MaintenanceSessionLink: typeof import('./src/components/MaintenanceSessionLink.vue')['default']
    MaintenanceSessionStageIndicator: typeof import('./src/components/MaintenanceSessionStageIndicator.vue')['default']
    MaintenanceTaskDeploymentModeSelector: typeof import('./src/components/MaintenanceTaskDeploymentModeSelector.vue')['default']
    MaintenanceTaskDetailsForm: typeof import('./src/components/MaintenanceTaskDetailsForm.vue')['default']
    MaintenanceTaskDynamicForm: typeof import('./src/components/MaintenanceTaskDynamicForm.vue')['default']
    MaintenanceTaskParameterDetails: typeof import('./src/components/MaintenanceTaskParameterDetails.vue')['default']
    MaintenanceTaskParameterKeyValuePairValueForm: typeof import('./src/components/MaintenanceTaskParameterKeyValuePairValueForm.vue')['default']
    MaintenanceTaskParameterPasswordValueForm: typeof import('./src/components/MaintenanceTaskParameterPasswordValueForm.vue')['default']
    MaintenanceTaskParametersForm: typeof import('./src/components/MaintenanceTaskParametersForm.vue')['default']
    MaintenanceTaskReferences: typeof import('./src/components/MaintenanceTaskReferences.vue')['default']
    MaintenanceTaskSelector: typeof import('./src/components/MaintenanceTaskSelector.vue')['default']
    MediaDownloader: typeof import('./src/components/MediaDownloader.vue')['default']
    MediaImage: typeof import('./src/components/MediaImage.vue')['default']
    MediaImageLoader: typeof import('./src/components/MediaImageLoader.vue')['default']
    MediaSelector: typeof import('./src/components/MediaSelector.vue')['default']
    MergeRequestList: typeof import('./src/components/MergeRequestList.vue')['default']
    MigrateSoftwarePrerequisites: typeof import('./src/components/MigrateSoftwarePrerequisites.vue')['default']
    MigrateToSupersedingDeployment: typeof import('./src/components/MigrateToSupersedingDeployment.vue')['default']
    MissingComputerInventoryItemsAlert: typeof import('./src/components/MissingComputerInventoryItemsAlert.vue')['default']
    MonacoDiffEditor: typeof import('./src/components/MonacoDiffEditor.vue')['default']
    MonacoSimpleView: typeof import('./src/components/MonacoSimpleView.vue')['default']
    MonacoVue: typeof import('./src/components/MonacoVue.vue')['default']
    MultiSoftwareSelector: typeof import('./src/components/MultiSoftwareSelector.vue')['default']
    NewButton: typeof import('./src/components/Base/NewButton.vue')['default']
    NewFrontendVersionAlert: typeof import('./src/components/NewFrontendVersionAlert.vue')['default']
    NewInventoryTask: typeof import('./src/components/NewInventoryTask.vue')['default']
    NewInventoryTaskScript: typeof import('./src/components/NewInventoryTaskScript.vue')['default']
    NotificationActionsSidebar: typeof import('./src/components/NotificationActionsSidebar.vue')['default']
    NotificationDetails: typeof import('./src/components/NotificationDetails.vue')['default']
    NotificationDetailsCard: typeof import('./src/components/NotificationDetailsCard.vue')['default']
    NotificationGroupDetailsCard: typeof import('./src/components/NotificationGroupDetailsCard.vue')['default']
    NotificationSidebar: typeof import('./src/components/NotificationSidebar.vue')['default']
    NotificationTable: typeof import('./src/components/NotificationTable.vue')['default']
    OauthAccessTokenSelector: typeof import('./src/components/OauthAccessTokenSelector.vue')['default']
    OauthConsentData: typeof import('./src/components/OauthConsentData.vue')['default']
    OrphanedComputerDetails: typeof import('./src/components/OrphanedComputerDetails.vue')['default']
    Overlay: typeof import('./src/components/Overlay.vue')['default']
    OverridableDynamicForm: typeof import('./src/components/OverridableDynamicForm.vue')['default']
    OverrideAssignmentForm: typeof import('./src/components/OverrideAssignmentForm.vue')['default']
    PackageAnalysisResults: typeof import('./src/components/PackageAnalysisResults.vue')['default']
    Page: typeof import('./src/components/Page.vue')['default']
    PageHeader: typeof import('./src/components/PageHeader.vue')['default']
    Panel: typeof import('./src/components/Panel.vue')['default']
    PendingComputerConflictResolver: typeof import('./src/components/PendingComputerConflictResolver.vue')['default']
    PersonActionsSidebar: typeof import('./src/components/PersonActionsSidebar.vue')['default']
    PersonMultiSelect: typeof import('./src/components/PersonMultiSelect.vue')['default']
    PersonSelectBox: typeof import('./src/components/PersonSelectBox.vue')['default']
    PolicyApprovalSelector: typeof import('./src/components/PolicyApprovalSelector.vue')['default']
    PolicyGroupBySelector: typeof import('./src/components/PolicyGroupBySelector.vue')['default']
    PolicyMaintenanceTypeSelector: typeof import('./src/components/PolicyMaintenanceTypeSelector.vue')['default']
    PolicyShowDismissedSelector: typeof import('./src/components/PolicyShowDismissedSelector.vue')['default']
    PowerShellIcon: typeof import('./src/components/PowerShellIcon.vue')['default']
    PpkgDownloadProgress: typeof import('./src/components/PpkgDownloadProgress.vue')['default']
    PreferenceGroup: typeof import('./src/components/PreferenceGroup.vue')['default']
    PreviewListItem: typeof import('./src/components/PreviewListItem.vue')['default']
    ProgressBadges: typeof import('./src/components/ProgressBadges.vue')['default']
    ProgressBar: typeof import('./src/components/newui/ProgressBar.vue')['default']
    ProgressLog: typeof import('./src/components/ProgressLog.vue')['default']
    ProviderAgentDetailsPanel: typeof import('./src/components/ProviderAgentDetailsPanel.vue')['default']
    ProviderAuditLogModal: typeof import('./src/components/ProviderAuditLogModal.vue')['default']
    ProviderAuditLogModalLineItem: typeof import('./src/components/ProviderAuditLogModalLineItem.vue')['default']
    ProviderAuditLogTable: typeof import('./src/components/ProviderAuditLogTable.vue')['default']
    ProviderClientList: typeof import('./src/components/ProviderClientList.vue')['default']
    ProviderClientListLinkedTenantColumn: typeof import('./src/components/ProviderClientListLinkedTenantColumn.vue')['default']
    ProviderClientListLinkedTenantSelector: typeof import('./src/components/ProviderClientListLinkedTenantSelector.vue')['default']
    ProviderHealthBadge: typeof import('./src/components/ProviderHealthBadge.vue')['default']
    ProviderLinkCard: typeof import('./src/components/ProviderLinkCard.vue')['default']
    ProviderLinkDetailsSuggestion: typeof import('./src/components/ProviderLinkDetailsSuggestion.vue')['default']
    ProviderLinkDropdown: typeof import('./src/components/ProviderLinkDropdown.vue')['default']
    ProviderLinkTable: typeof import('./src/components/ProviderLinkTable.vue')['default']
    ProviderTypeDropdown: typeof import('./src/components/ProviderTypeDropdown.vue')['default']
    QuickDeploy: typeof import('./src/components/QuickDeploy.vue')['default']
    RadioButtonGroup: typeof import('./src/components/RadioButtonGroup.vue')['default']
    RadioGroupButtonDropdown: typeof import('./src/components/RadioGroupButtonDropdown.vue')['default']
    RbacListUsersTab: typeof import('./src/components/RbacListUsersTab.vue')['default']
    RbacRolesTab: typeof import('./src/components/RbacRolesTab.vue')['default']
    RbacUsersTab: typeof import('./src/components/RbacUsersTab.vue')['default']
    RbacUsersTable: typeof import('./src/components/RbacUsersTable.vue')['default']
    RebootPreferenceDropdown: typeof import('./src/components/RebootPreferenceDropdown.vue')['default']
    RebootPreferenceSelector: typeof import('./src/components/RebootPreferenceSelector.vue')['default']
    RecommendIntegrationDeploymentModal: typeof import('./src/components/RecommendIntegrationDeploymentModal.vue')['default']
    ReloadIntegrationTypesButton: typeof import('./src/components/ReloadIntegrationTypesButton.vue')['default']
    RemoteControlModal: typeof import('./src/components/RemoteControlModal.vue')['default']
    RerunActionButton: typeof import('./src/components/RerunActionButton.vue')['default']
    RerunSessionButton: typeof import('./src/components/RerunSessionButton.vue')['default']
    Resizer: typeof import('./src/components/Resizer.vue')['default']
    ResumeSessionButton: typeof import('./src/components/ResumeSessionButton.vue')['default']
    RoleAlert: typeof import('./src/components/RoleAlert.vue')['default']
    RolePermissionOptions: typeof import('./src/components/RolePermissionOptions.vue')['default']
    RolePermissions: typeof import('./src/components/RolePermissions.vue')['default']
    RolePermissionsCoreAndAdvanced: typeof import('./src/components/RolePermissionsCoreAndAdvanced.vue')['default']
    RolePermissionSubject: typeof import('./src/components/RolePermissionSubject.vue')['default']
    RolesActionsSidebar: typeof import('./src/components/RolesActionsSidebar.vue')['default']
    RolesBody: typeof import('./src/components/RolesBody.vue')['default']
    RoleUserAssigment: typeof import('./src/components/RoleUserAssigment.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SchedulesTable: typeof import('./src/components/SchedulesTable.vue')['default']
    ScriptEditor: typeof import('./src/components/ScriptEditor.vue')['default']
    ScriptEditorActivityBar: typeof import('./src/components/ScriptEditorActivityBar.vue')['default']
    ScriptEditorActivityBarItem: typeof import('./src/components/ScriptEditorActivityBarItem.vue')['default']
    ScriptEditorComputerSidebarPane: typeof import('./src/components/ScriptEditorComputerSidebarPane.vue')['default']
    ScriptEditorConfigureView: typeof import('./src/components/ScriptEditorConfigureView.vue')['default']
    ScriptEditorDiffViewer: typeof import('./src/components/ScriptEditorDiffViewer.vue')['default']
    ScriptEditorDynamicForm: typeof import('./src/components/ScriptEditorDynamicForm.vue')['default']
    ScriptEditorDynamicFormSidebarPane: typeof import('./src/components/ScriptEditorDynamicFormSidebarPane.vue')['default']
    ScriptEditorEditor: typeof import('./src/components/ScriptEditorEditor.vue')['default']
    ScriptEditorEmptyView: typeof import('./src/components/ScriptEditorEmptyView.vue')['default']
    ScriptEditorFunctionsSidebarPane: typeof import('./src/components/ScriptEditorFunctionsSidebarPane.vue')['default']
    ScriptEditorIntegrationSidebarPane: typeof import('./src/components/ScriptEditorIntegrationSidebarPane.vue')['default']
    ScriptEditorOpenEditorsSidebarPane: typeof import('./src/components/ScriptEditorOpenEditorsSidebarPane.vue')['default']
    ScriptEditorOpenTabActionsToolbar: typeof import('./src/components/ScriptEditorOpenTabActionsToolbar.vue')['default']
    ScriptEditorOpenTabsToolbar: typeof import('./src/components/ScriptEditorOpenTabsToolbar.vue')['default']
    ScriptEditorOutputPanelView: typeof import('./src/components/ScriptEditorOutputPanelView.vue')['default']
    ScriptEditorPanel: typeof import('./src/components/ScriptEditorPanel.vue')['default']
    ScriptEditorPrimarySidebar: typeof import('./src/components/ScriptEditorPrimarySidebar.vue')['default']
    ScriptEditorReferencesView: typeof import('./src/components/ScriptEditorReferencesView.vue')['default']
    ScriptEditorRunScriptSidebarPane: typeof import('./src/components/ScriptEditorRunScriptSidebarPane.vue')['default']
    ScriptEditorScriptDirectorySidebarPane: typeof import('./src/components/ScriptEditorScriptDirectorySidebarPane.vue')['default']
    ScriptEditorSearchResults: typeof import('./src/components/ScriptEditorSearchResults.vue')['default']
    ScriptEditorSearchResultsFolder: typeof import('./src/components/ScriptEditorSearchResultsFolder.vue')['default']
    ScriptEditorSearchResultsFolderItem: typeof import('./src/components/ScriptEditorSearchResultsFolderItem.vue')['default']
    ScriptEditorSearchResultsWrapper: typeof import('./src/components/ScriptEditorSearchResultsWrapper.vue')['default']
    ScriptEditorSearchSidebarPane: typeof import('./src/components/ScriptEditorSearchSidebarPane.vue')['default']
    ScriptEditorSecondarySidebar: typeof import('./src/components/ScriptEditorSecondarySidebar.vue')['default']
    ScriptEditorSidebarPane: typeof import('./src/components/ScriptEditorSidebarPane.vue')['default']
    ScriptEditorSoftwareSidebarPane: typeof import('./src/components/ScriptEditorSoftwareSidebarPane.vue')['default']
    ScriptEditorStatusBar: typeof import('./src/components/ScriptEditorStatusBar.vue')['default']
    ScriptEditorSyntaxErrorsPanelView: typeof import('./src/components/ScriptEditorSyntaxErrorsPanelView.vue')['default']
    ScriptEditorTaskSidebarPane: typeof import('./src/components/ScriptEditorTaskSidebarPane.vue')['default']
    ScriptEditorTenantSidebarPane: typeof import('./src/components/ScriptEditorTenantSidebarPane.vue')['default']
    ScriptEditorToolbar: typeof import('./src/components/ScriptEditorToolbar.vue')['default']
    ScriptEditorVariablesSidebarPane: typeof import('./src/components/ScriptEditorVariablesSidebarPane.vue')['default']
    ScriptHighlight: typeof import('./src/components/ScriptHighlight.vue')['default']
    ScriptReferences: typeof import('./src/components/ScriptReferences.vue')['default']
    ScriptSelector: typeof import('./src/components/ScriptSelector.vue')['default']
    SelfService: typeof import('./src/components/SelfService.vue')['default']
    SessionSupportRequestButton: typeof import('./src/components/SessionSupportRequestButton.vue')['default']
    SharedSoftwareDetailsForm: typeof import('./src/components/SharedSoftwareDetailsForm.vue')['default']
    SideBarContainer: typeof import('./src/components/SideBarContainer.vue')['default']
    SidebarNavDropdown: typeof import('./src/components/SidebarNavDropdown.vue')['default']
    SidebarNavItem: typeof import('./src/components/SidebarNavItem.vue')['default']
    SimplePanel: typeof import('./src/components/SimplePanel.vue')['default']
    SmtpForm: typeof import('./src/components/SmtpForm.vue')['default']
    SoftwareAccessLevelSelector: typeof import('./src/components/SoftwareAccessLevelSelector.vue')['default']
    SoftwareDetailsForm: typeof import('./src/components/SoftwareDetailsForm.vue')['default']
    SoftwareDetailsPostInstallationSelector: typeof import('./src/components/SoftwareDetailsPostInstallationSelector.vue')['default']
    SoftwareDetailsPostUninstallationSelector: typeof import('./src/components/SoftwareDetailsPostUninstallationSelector.vue')['default']
    SoftwareDetailsTestingSelector: typeof import('./src/components/SoftwareDetailsTestingSelector.vue')['default']
    SoftwareLicenseInformation: typeof import('./src/components/SoftwareLicenseInformation.vue')['default']
    SoftwareLicenseListLicenseColumn: typeof import('./src/components/SoftwareLicenseListLicenseColumn.vue')['default']
    SoftwareMaintenceTimeline: typeof import('./src/components/SoftwareMaintenceTimeline.vue')['default']
    SoftwarePrerequisiteBuilder: typeof import('./src/components/SoftwarePrerequisiteBuilder.vue')['default']
    SoftwareSelectBox: typeof import('./src/components/SoftwareSelectBox.vue')['default']
    SoftwareVersionDetailsForm: typeof import('./src/components/SoftwareVersionDetailsForm.vue')['default']
    SoftwareVersionInstallerSelector: typeof import('./src/components/SoftwareVersionInstallerSelector.vue')['default']
    SoftwareVersionUploader: typeof import('./src/components/SoftwareVersionUploader.vue')['default']
    SourceContextFormField: typeof import('./src/components/SourceContextFormField.vue')['default']
    SupportFileUploader: typeof import('./src/components/SupportFileUploader.vue')['default']
    SupportSidebar: typeof import('./src/components/SupportSidebar.vue')['default']
    TableActions: typeof import('./src/components/Base/TableActions.vue')['default']
    Tabs: typeof import('./src/components/newui/Tabs.vue')['default']
    TagPill: typeof import('./src/components/TagPill.vue')['default']
    TagSelectBox: typeof import('./src/components/TagSelectBox.vue')['default']
    TailButton: typeof import('./src/components/TailButton.vue')['default']
    TargetAssignmentChangeRequestComparer: typeof import('./src/components/TargetAssignmentChangeRequestComparer.vue')['default']
    TargetSelector: typeof import('./src/components/TargetSelector.vue')['default']
    TargetTypeColumn: typeof import('./src/components/TargetTypeColumn.vue')['default']
    TargetVisibilitySelector: typeof import('./src/components/TargetVisibilitySelector.vue')['default']
    TargetWindowsSessionCard: typeof import('./src/components/TargetWindowsSessionCard.vue')['default']
    TenantAccessLevelSelector: typeof import('./src/components/TenantAccessLevelSelector.vue')['default']
    TenantActionsSidebar: typeof import('./src/components/TenantActionsSidebar.vue')['default']
    TenantAzureDetails: typeof import('./src/components/TenantAzureDetails.vue')['default']
    TenantDetailsActionsTab: typeof import('./src/components/TenantDetailsActionsTab.vue')['default']
    TenantDetailsAzureTab: typeof import('./src/components/TenantDetailsAzureTab.vue')['default']
    TenantDetailsEditForm: typeof import('./src/components/TenantDetailsEditForm.vue')['default']
    TenantDetailsOverviewTab: typeof import('./src/components/TenantDetailsOverviewTab.vue')['default']
    TenantDetailsProviderLinksTab: typeof import('./src/components/TenantDetailsProviderLinksTab.vue')['default']
    TenantDetailsSoftwareSearchTab: typeof import('./src/components/TenantDetailsSoftwareSearchTab.vue')['default']
    TenantDetailsSoftwareTab: typeof import('./src/components/TenantDetailsSoftwareTab.vue')['default']
    TenantPermissionsSelector: typeof import('./src/components/TenantPermissionsSelector.vue')['default']
    TenantPreferenceOptions: typeof import('./src/components/TenantPreferenceOptions.vue')['default']
    TenantSelectBox: typeof import('./src/components/TenantSelectBox.vue')['default']
    TestIntegrationDynamicForm: typeof import('./src/components/TestIntegrationDynamicForm.vue')['default']
    TimelineEvent: typeof import('./src/components/TimelineEvent.vue')['default']
    TimezoneSelector: typeof import('./src/components/TimezoneSelector.vue')['default']
    Toggle: typeof import('./src/components/Toggle.vue')['default']
    TreeView: typeof import('./src/components/TreeView.vue')['default']
    TreeViewItem: typeof import('./src/components/TreeViewItem.vue')['default']
    TreeViewItemValue: typeof import('./src/components/TreeViewItemValue.vue')['default']
    TrialBanner: typeof import('./src/components/TrialBanner.vue')['default']
    UnifiedComputerSelector: typeof import('./src/components/UnifiedComputerSelector.vue')['default']
    UnifiedOnboardingPage: typeof import('./src/components/UnifiedOnboardingPage.vue')['default']
    UnifiedSessionList: typeof import('./src/components/UnifiedSessionList.vue')['default']
    UnifiedSoftwareDetailsDataRow: typeof import('./src/components/UnifiedSoftwareDetailsDataRow.vue')['default']
    UnifiedSoftwareDetailsList: typeof import('./src/components/UnifiedSoftwareDetailsList.vue')['default']
    UnifiedSoftwareDetailsListComputerInfo: typeof import('./src/components/UnifiedSoftwareDetailsListComputerInfo.vue')['default']
    UnifiedSoftwareDetailsNameLink: typeof import('./src/components/UnifiedSoftwareDetailsNameLink.vue')['default']
    UnifiedSoftwareDetailsTenantDetails: typeof import('./src/components/UnifiedSoftwareDetailsTenantDetails.vue')['default']
    UnifiedSoftwareDetailsTenantDetailsSelector: typeof import('./src/components/UnifiedSoftwareDetailsTenantDetailsSelector.vue')['default']
    UnifiedSoftwareDynamicFormOverride: typeof import('./src/components/UnifiedSoftwareDynamicFormOverride.vue')['default']
    UninstallHelpText: typeof import('./src/components/UninstallHelpText.vue')['default']
    UnlinkAzureTenantConfirmationModal: typeof import('./src/components/UnlinkAzureTenantConfirmationModal.vue')['default']
    UpdatedDateText: typeof import('./src/components/UpdatedDateText.vue')['default']
    UpdateUserForm: typeof import('./src/components/UpdateUserForm.vue')['default']
    UpScope: typeof import('./src/components/UpScope.vue')['default']
    UserAffinityTable: typeof import('./src/components/UserAffinityTable.vue')['default']
    UseReactiveMapTest: typeof import('./src/components/test/useReactiveMapTest.vue')['default']
    UserPeopleBody: typeof import('./src/components/UserPeopleBody.vue')['default']
    VariableAlert: typeof import('./src/components/VariableAlert.vue')['default']
    ViewButton: typeof import('./src/components/Base/ViewButton.vue')['default']
    XtermVue: typeof import('./src/components/XtermVue.vue')['default']
  }
}
