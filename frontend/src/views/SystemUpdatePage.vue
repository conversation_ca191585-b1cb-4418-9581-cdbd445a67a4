<template>
  <Page>
    <template #header>
      <page-header title="System Update" />
    </template>
    <div class="release-channel mb-3">
      <div class="d-flex align-items-center gap-2">
        <span>Release Channel:</span>
        <em>{{ instanceReleaseChannelText }}</em>
        <ImmyButton
          variant="link"
          :class="{ 'd-none': !canManageReleaseChannel }"
          @click="changingInstanceReleaseChannel = !changingInstanceReleaseChannel"
        >
          Change
        </ImmyButton>
      </div>
      <span class="text-muted">{{ instanceReleaseChannelDescription }}</span>
      <div
        v-if="changingInstanceReleaseChannel"
        class="d-flex align-items-start flex-column gap-2 mt-3 mb-3"
      >
        <div class="w-100">
          <v-select
            v-model="changedInstanceReleaseChannel"
            :options="releaseChannelOptions"
            label="text"
            placeholder="Select a release channel"
            :reduce="releaseChannelReduce"
            :clearable="false"
            required
            select-on-tab
          />
          <span class="text-muted fs-85">{{ changedInstanceReleaseChannelDescription }}</span>
        </div>
        <LoadButton
          :disabled="changedInstanceReleaseChannel == null"
          variant="primary"
          :handler="changeInstanceReleaseChannel"
        >
          Update Release Channel
        </LoadButton>
      </div>
      <hr>
    </div>
    <div v-if="updateAvailable && !isInstanceUpdating" class="mb-3">
      <ImmyAlert variant="primary" show>
        <p><strong>Your instance can be updated now.</strong></p>
        <p class="m-0">
          immy.bot will be briefly unavailable while updating.
        </p>
        <p class="m-0">
          You will receive an email when immy.bot is finished updating.
        </p>
      </ImmyAlert>
      <ImmyButton
        class="mb-1"
        variant="primary"
        size="lg"
        :disabled="updating"
        :class="{ 'd-none': !canPullUpdate }"
        @click="pullUpdate"
      >
        <template v-if="updating">
          <i class="fa fa-spin fa-spinner" />
          Updating
        </template>
        <template v-else-if="latestRelease">
          Update to
          <strong>Release {{ latestRelease.tag }}</strong>
        </template>
      </ImmyButton>
    </div>
    <template v-if="isInstanceUpdating">
      <i class="fa fa-spin fa-spinner mr-2" />
      Update in progress
    </template>
    <template v-else-if="instanceUpdateHasFailed">
      <ImmyAlert variant="danger" show>
        There was a problem updating immy.bot. If you continue to experience problems updating,
        please reach out to immy.bot support for assistance.
      </ImmyAlert>
    </template>
    <template v-else />
    <ImmyAlert v-if="!currentReleaseDetails" show variant="warning">
      This instance is currently on an
      <em>unreleased</em>
      version. If you wish to switch to a stable / released version, please contact immy.bot support
      for assistance.
    </ImmyAlert>
    <div v-else>
      <ImmyCard :no-body="true" class="mb-1">
        <ImmyCardHeader header-tag="header" class="p-1" role="tab">
          <ImmyButton v-ImmyToggle:accordion-release-1 block variant="primary" class="w-100">
            <strong>Current version: {{ currentReleaseDetails.tag }} ({{
              getReleaseChannelText(currentReleaseDetails)
            }})</strong>
            <div style="line-height: 1em; font-size: 75%">
              Click to see release notes
            </div>
          </ImmyButton>
        </ImmyCardHeader>
        <ImmyCollapse id="accordion-release-1" accordion="releases-accordion" role="tabpanel">
          <ImmyCardBody>
            <template v-if="upToDate">
              <ImmyCardText>
                <ImmyBadge class="version-badge mt-3" variant="success">
                  Up to date!
                </ImmyBadge>
                You are updated to the latest released version of immy.bot
              </ImmyCardText>
              <hr>
            </template>
            <ImmyCardText v-html="compileMarkdown(currentReleaseDetails.releaseNotes)" />
          </ImmyCardBody>
        </ImmyCollapse>
      </ImmyCard>
      <ImmyCard v-for="(release, index) in latestReleaseDetails" :key="release.tag" :no-body="true">
        <ImmyCardHeader header-tag="header" class="p-1" role="tab">
          <ImmyButton
            v-ImmyToggle:[`accordion-release-tag-${index}`]
            class="w-100"
            block
            variant="primary"
          >
            <strong>New version: {{ release.tag }} ({{ getReleaseChannelText(release) }})</strong>
            <div style="line-height: 1em; font-size: 75%">
              Click to see release notes
            </div>
          </ImmyButton>
        </ImmyCardHeader>
        <ImmyCollapse
          :id="`accordion-release-tag-${index}`"
          accordion="releases-accordion"
          role="tabpanel"
        >
          <ImmyCardBody class="release-notes">
            <template v-if="latestRelease && release.tag === latestRelease.tag">
              <ImmyCardText>
                <ImmyBadge class="version-badge mt-3" variant="primary">
                  Latest version
                </ImmyBadge>
                This is the latest released version of immy.bot
              </ImmyCardText>
              <hr>
            </template>
            <ImmyCardText v-html="compileMarkdown(release.releaseNotes)" />
          </ImmyCardBody>
        </ImmyCollapse>
      </ImmyCard>
    </div>
  </Page>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import { ReleaseChannel } from "@/api/backend/generated/enums";
import { IReleaseDetails } from "@/api/backend/generated/interfaces";
import { systemApi } from "@/api/backend/v1";
import { useViewBase } from "@/composables/ViewBase";
import EnumTextHelpers from "@/helpers/enums/EnumTextHelpers";
import { ReleaseChannelMetadata } from "@/helpers/enums/ReleaseChannelExtended";
import { useSystemStore } from "@/store/pinia";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useAuthStore } from "@/store/pinia/auth-store";
import { usePermissionStore } from "@/store/pinia/permission-store";
import { markdownToHtml } from "@/utils/htmlSanitization";
import { toast } from "@/utils/toast";

const appAlertsStore = useAppAlertsStore();
const systemStore = useSystemStore();
const authStore = useAuthStore();
const permissionStore = usePermissionStore();

const initializing = ref(false);
const updating = ref(false);
const currentReleaseDetails = ref<IReleaseDetails>();
const latestReleaseDetails = ref<IReleaseDetails[]>([]);

const canManageReleaseChannel = computed(() => permissionStore.can("system_operations:get_releases"));
const canPullUpdate = computed(() => permissionStore.can("system_operations:pull_updates"));

async function initialize() {
  try {
    initializing.value = true;
    const { currentRelease, latestReleases } = await systemStore.getReleases();
    currentReleaseDetails.value = currentRelease;
    latestReleaseDetails.value = latestReleases;
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to load the system update page.",
      details: err,
    });
  }
  finally {
    initializing.value = false;
  }
}

useViewBase({
  loading: computed(() => initializing.value),
  breadcrumbs: computed(() => []),
  initialize,
});

const instanceUpdateHasFailed = computed(() => {
  return authStore.instanceUpdateHasFailed;
});
const isInstanceUpdating = computed(() => {
  return authStore.isInstanceUpdating;
});
const upToDate = computed(() => {
  // up to date if the current release matches the latest release
  // or if we have a current release and no latest releases
  // e.g. we have nothing to update to
  return (
    (currentReleaseDetails.value
      && latestRelease.value
      && currentReleaseDetails.value.tag === latestRelease.value.tag)
    || (currentReleaseDetails.value && !latestRelease.value)
  );
});
const updateAvailable = computed(() => {
  return authStore.updateAvailable;
});
const latestRelease = computed(() => {
  return latestReleaseDetails.value[0];
});

function compileMarkdown(md?: string) {
  return markdownToHtml(md || "");
}

async function pullUpdate() {
  try {
    if (updating.value)
      return;
    updating.value = true;
    authStore.setInstanceUpdating();
    await systemStore.pullUpdate();
  }
  catch (err) {
    authStore.setInstanceUpdateFailed();
    appAlertsStore.addAlert({
      text: "An error occurred while updating immy.bot.",
      details: err,
    });
  }
  finally {
    updating.value = false;
  }
}

// release channel

const changingInstanceReleaseChannel = ref(false);
const changedInstanceReleaseChannel = ref<ReleaseChannel>();

function getReleaseChannelText(release: IReleaseDetails) {
  return EnumTextHelpers.ReleaseChannel.GetTextByValue(release.releaseChannel);
}

const instanceReleaseChannelText = computed(() => {
  if (!authStore.instanceReleaseChannel)
    return "";
  return EnumTextHelpers.ReleaseChannel.GetTextByValue(authStore.instanceReleaseChannel);
});

const instanceReleaseChannelDescription = computed(() => {
  if (!authStore.instanceReleaseChannel)
    return "";
  return EnumTextHelpers.ReleaseChannel.GetOptionsByValue(authStore.instanceReleaseChannel)
    .description;
});

const changedInstanceReleaseChannelDescription = computed(() => {
  if (!changedInstanceReleaseChannel.value)
    return "";
  return EnumTextHelpers.ReleaseChannel.GetOptionsByValue(changedInstanceReleaseChannel.value)
    .description;
});

const releaseChannelOptions = computed(() => {
  const options = EnumTextHelpers.ReleaseChannel.options;
  return options.filter(
    a => a.value !== authStore.instanceReleaseChannel && a.value !== ReleaseChannel.Developer,
  );
});
function releaseChannelReduce(option: ReleaseChannelMetadata) {
  return option.value;
}

async function changeInstanceReleaseChannel() {
  try {
    if (!changedInstanceReleaseChannel.value)
      return;
    if (!currentReleaseDetails.value)
      return;

    // update the release channel
    await systemApi.updateReleaseChannel({ releaseChannel: changedInstanceReleaseChannel.value });
    // close the release change form
    changingInstanceReleaseChannel.value = false;
    changedInstanceReleaseChannel.value = undefined;
    // re-fetch auth info which contains latest release information
    await authStore.checkAuthorization();
    // re-initialize the page
    await initialize();
    toast.success("Updated Release Channel");
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while changing the release channel.",
      details: err,
    });
  }
}
</script>

<style lang="scss" scoped>
.version-badge {
  font-size: 100%;
}

.release-note {
  background: #fff;
  padding: 30px;
}

.release-notes {
  img {
    max-width: 100%;
  }
}
</style>
