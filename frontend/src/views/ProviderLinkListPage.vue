<template>
  <Page no-bg no-shadow>
    <template #header>
      <dismissible-alert for="provider-link-list">
        <template #heading>
          Integrations
        </template>
        <template #body>
          <p>
            <strong>
              <i class="fal fa-edit" />
            </strong>
            Edit an integration.
            <br>
            <strong>
              <i class="fal fa-trash-can" />
            </strong>
            Delete an integration and all of it's associated data.
          </p>
        </template>
      </dismissible-alert>
      <page-header title="Integrations">
        <template #primary-action>
          <div class="d-flex flex-wrap align-items-center gap-3">
            <ImmyRadioGroup buttons button-variant="outline-primary">
              <ImmyRadio
                v-for="option in releaseStageOptions"
                :key="option.text"
                button-variant="outline-primary"
                name="release-stage-filter"
                buttons
                :model-value="option.value"
                :selected="dynamicIntegrationTypeFilter"
                @change="dynamicIntegrationTypeFilter = $event"
              >
                {{ option.text }}
              </ImmyRadio>
            </ImmyRadioGroup>

            <ImmyButton
              variant="primary"
              :to="{ name: 'New Integration' }"
            >
              <i class="fa fa-plus mr-1" /> Add integration
            </ImmyButton>
            <ReloadIntegrationTypesButton />
          </div>
        </template>
      </page-header>
    </template>
    <div>
      <h2>Enabled Integrations</h2>
      <small class="text-muted">Integrations which have been previously enabled.</small>
      <ProviderLinkTable enabled-only class="mt-3" :filter="dynamicIntegrationTypeFilter" />
    </div>
    <div class="mt-4 my-3">
      <h2>Disabled Integrations</h2>
      <small class="text-muted">Integrations which have been previously disabled.</small>
      <ProviderLinkTable disabled-only class="mt-3" :filter="dynamicIntegrationTypeFilter" />
    </div>
    <hr>
    <ImmySystemIPAddressMessageBox />
  </Page>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useAuthStore } from "@/store/pinia/auth-store";
import { useBreadcrumbsStore } from "@/store/pinia/breadcrumbs-store";
import { getReleaseStage } from "@/utils/misc";

const authStore = useAuthStore();

const dynamicIntegrationTypeFilter = ref(null);

const releaseStageOptions = getReleaseStage();

const breadcrumbStore = useBreadcrumbsStore();

onMounted(async () => {
  // set breadcrumbs
  authStore.setLoading();
  breadcrumbStore.set([]);
  authStore.loadingComplete();
});
</script>
