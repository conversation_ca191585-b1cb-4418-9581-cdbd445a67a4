<template>
  <Page id="technician-pod-page" no-bg>
    <div v-if="loadingPsaData">
      <i class="fas fa-spinner fa-spin mr-2" /><span>Loading data from PSA</span>
    </div>
    <div v-else-if="!psaResponse?.tenantId">
      <p>Showing for {{ psaResponse?.ticketEmailAddress }}. The client associated with this ticket is not linked to an ImmyBot tenant. Only linked clients can have actions performed.</p>
    </div>
    <div v-else>
      <div class="page-top-line d-flex justify-content-between mb-3">
        <div class="d-flex align-items-center text-muted">
          <span v-if="psaResponse?.tenantName">Showing actions for <span v-if="psaResponse?.personName">{{ psaResponse.personName }}</span> ({{ psaResponse.ticketEmailAddress }}) at {{ psaResponse.tenantName }}</span>
          <ImmyButton variant="transparent" class="ml-0" @click="reloadTechnicianPodPage">
            <i class="text-success fal fa-rotate-right" />
          </ImmyButton>
        </div>
        <a href="/" target="_BLANK" rel="noopener" title="Open ImmyBot">
          <i class="fal fa-arrow-up-right-from-square" />
        </a>
      </div>
      <header class="mb-3">
        <ComputerToolBar
          v-if="showComputerTabs && selectedComputerId !== undefined"
          v-model:selected-computer-id="selectedComputerId"
          class="w-100"
          :page-filter="ComputerToolbarPageFilter.TechnicianPod"
          :unified-computer-info="psaResponse?.computers"
        />
      </header>
      <ImmyTabs>
        <template #tabs>
          <ImmyTabItem v-model="currentTab" :tab-value="Tab.Deployments" title="Deployments" />
          <ImmyTabItem v-if="showComputerTabs" v-model="currentTab" :tab-value="Tab.Sessions" title="Sessions" />
          <ImmyTabItem v-if="showComputerTabs" v-model="currentTab" :tab-value="Tab.Terminal" title="Terminal" />
          <ImmyTabItem v-if="showComputerTabs" v-model="currentTab" :tab-value="Tab.Onboarding" title="Onboarding" />
        </template>
        <template #content>
          <ImmyTabBody v-show="currentTab == Tab.Deployments" class="p-3">
            <UnifiedSoftwareDetailsList
              :computer-id="selectedComputerId"
              :tenant-id="psaResponse?.tenantId"
              :person-id="psaResponse?.personId"
              :unified-computer-info="psaResponse?.computers"
              page-filter="pod"
            />
          </ImmyTabBody>
          <div v-if="selectedComputerId && showComputerTabs">
            <ImmyTabBody v-show="currentTab == Tab.Sessions" class="p-3">
              <UnifiedSessionList
                v-model:open-session-id="activeSessionId"
                :computer-id="selectedComputerId"
                :tenant-id="psaResponse?.tenantId"
                :person-id="psaResponse?.personId"
                :limit-to-selected-session-type="true"
                :hide-info-panel-at-start="true"
              />
            </ImmyTabBody>
            <ImmyTabBody v-show="currentTab == Tab.Terminal" class="p-3">
              <ComputerTerminal :computer-id="selectedComputerId" />
            </ImmyTabBody>
            <ImmyTabBody v-show="currentTab == Tab.Onboarding" class="p-3">
              <UnifiedOnboardingPage v-model:onboarding-session-id="activeSessionId" :computer-id="selectedComputerId" :push-to-onboarding-tab="false" />
            </ImmyTabBody>
          </div>
        </template>
      </ImmyTabs>
    </div>
  </Page>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { ITechnicianPageInfoFromPsaTicket } from "@/api/backend/generated/interfaces";
import { providerLinksApi } from "@/api/backend/v1";
import ImmyTabs from "@/components/ImmyTabs.vue";
import { useViewBase } from "@/composables/ViewBase";
import { ComputerToolbarPageFilter } from "@/helpers/UnifiedSoftwareList";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";

const props = defineProps<{
  providerLinkId: number;
  ticketId: string;
}>();

enum Tab {
  Deployments = 0,
  Sessions = 1,
  Terminal = 2,
  Onboarding = 3,
}

const appAlertsStore = useAppAlertsStore();
const activeSessionId = ref<number>();

const currentTab = ref(Tab.Deployments);
const psaResponse = ref<ITechnicianPageInfoFromPsaTicket>();
const selectedComputerId = ref<number>();
const loadingPsaData = ref(false);

const showComputerTabs = computed(() => psaResponse.value?.computers.length && selectedComputerId.value !== undefined);

onMounted(async () => {
  await reloadTechnicianPodPage();
});

async function reloadTechnicianPodPage() {
  try {
    loadingPsaData.value = true;
    psaResponse.value = await providerLinksApi.getPersonAndTenantFromPsaTicket(props.providerLinkId, props.ticketId);
    // todo: with the person and tenant ids, pass this to the backend to retrieve assignments visible to the technician pod page that resolve to the specified person or tenant
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to get associated ImmyBot person and tenant from PSA ticket.",
      details: err,
    });
  }
  finally {
    // Always select the first computer by default when psaResponse is loaded
    const firstComputerId = psaResponse.value?.computers.find(computer => computer.computerId !== undefined)?.computerId;
    if (firstComputerId) {
      selectedComputerId.value = firstComputerId;
    }
    loadingPsaData.value = false;
  }
}

watch(activeSessionId, (val, oldVal) => {
  if (val !== oldVal) {
    if (val !== undefined)
      currentTab.value = Tab.Sessions;
  }
});

useViewBase({
  breadcrumbs: computed(() => []),
});
</script>

<style scoped lang="scss">
@media (max-width: 930px) {
  #technician-pod-page {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
</style>
