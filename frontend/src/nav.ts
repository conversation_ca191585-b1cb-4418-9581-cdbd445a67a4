import { RbacPermissions } from "@/api/backend/generated/rbac-permissions";

export interface INavItem {
  name: string;
  url: string;
  id: string;
  icon?: string;
  badge?: string;
  requiresSchedulesFeature?: boolean;
  children?: INavItem[];
  requiresManagementAccess?: boolean;
  selfServiceOnly?: boolean;
  requiresRbacFeature?: boolean;
  hiddenWithRbacFeature?: boolean;
  permissions: RbacPermissions[];
}

export interface INav {
  authenticatedItems: INavItem[];
  unauthenticatedItems: INavItem[];
}

const nav: INav = {
  authenticatedItems: [
    {
      name: "Self Service",
      id: "self-service",
      url: "/self-service",
      icon: "fal fa-columns",
      selfServiceOnly: true,
      // todo: add self-service permissions
      permissions: [],
    },
    {
      name: "Getting Started",
      id: "checklist",
      url: "/checklist",
      icon: "fal fa-list-check",
      permissions: ["getting_started:view"],
    },
    {
      requiresManagementAccess: true,
      name: "Dashboard",
      id: "dashboard",
      url: "/dashboard",
      icon: "fal fa-columns",
      permissions: ["computers:manage"],
    },
    {
      name: "Sessions",
      id: "sessions",
      url: "/sessions",
      icon: "fal fa-layer-group",
      permissions: ["maintenance_sessions:view"],
    },
    {
      name: "Deployments",
      id: "deployments",
      url: "/deployments",
      icon: "fal fa-bullseye",
      permissions: [
        "deployments:view_individual",
        "deployments:view_single_tenant",
        "deployments:view_cross_tenant",
      ],
    },
    {
      name: "Computers",
      id: "computers",
      url: "/computers",
      icon: "fal fa-laptop",
      permissions: ["computers:view"],
    },
    {
      name: "Tenants",
      id: "tenants",
      url: "/tenants",
      icon: "fal fa-address-book",
      permissions: ["tenants:view"],
    },
    {
      requiresManagementAccess: true,
      name: "Reporting",
      id: "reporting",
      url: "/reporting",
      icon: "fal fa-book",
      permissions: ["computers:view_inventory_report", "computers:user_affinity"],
      children: [
        {
          name: "Computers",
          id: "computers",
          url: "/reporting/computers",
          permissions: ["computers:view"],
        },
        {
          name: "Computer Inventory Scripts",
          id: "computer-inventory-scripts",
          url: "/reporting/computer-inventory-scripts",
          permissions: ["computers:view_inventory_report"],
        },
        {
          name: "Computer Software",
          id: "computer-software",
          url: "/reporting/detected-computer-software",
          permissions: ["computers:view_inventory_report"],
        },
        {
          name: "User Affinity",
          id: "user-affinity",
          url: "/reporting/user-affinity",
          permissions: ["computers:user_affinity"],
        },
      ],
    },
    {
      name: "Library",
      id: "library",
      url: "/library",
      icon: "fal fa-laptop-arrow-down",
      permissions: [],
      children: [
        {
          name: "Software",
          id: "software",
          url: "/library/software",
          permissions: ["software:view"],
        },
        {
          name: "Tasks",
          id: "tasks",
          url: "/library/tasks",
          permissions: ["software:view"],
        },
        {
          name: "Script Editor",
          id: "script-editor",
          url: "/library/scripts",
          permissions: ["scripts:view"],
        },
        {
          name: "Upload",
          id: "upload",
          url: "/library/software/upload",
          permissions: ["software:view"],
        },
        {
          name: "Licenses",
          id: "licenses",
          url: "/library/software/licenses",
          permissions: ["licenses:view"],
        },
        {
          name: "Deployment Ordering",
          id: "deployment-ordering",
          url: "/library/ordering",
          permissions: ["deployments:manage_maintenance_item_ordering"],
        },
      ],
    },
    {
      name: "Schedules",
      id: "schedules",
      url: "/schedules",
      icon: "fal fa-book",
      requiresSchedulesFeature: true,
      permissions: ["schedules:view"],
    },
    {
      name: "Show more",
      id: "show-more",
      url: "/settings",
      permissions: [],
      children: [
        {
          name: "Billing",
          id: "billing",
          url: "/settings/billing",
          permissions: ["billing:view"],
        },
        {
          requiresManagementAccess: true,
          name: "Notifications",
          id: "notifications",
          url: "/notifications",
          permissions: [],
        },
        {
          name: "Integrations",
          id: "integrations",
          url: "/settings/integrations",
          permissions: ["integrations:view"],
        },
        {
          name: "Integration Types",
          id: "integration-types",
          url: "/settings/dynamic-integration-types",
          permissions: ["dynamic_integration_types:view"],
        },
        {
          name: "Preferences",
          id: "preferences",
          url: "/settings/preferences",
          permissions: [],
        },
        {
          name: "People",
          id: "people",
          url: "/settings/persons",
          permissions: ["persons:view"],
        },
        {
          name: "Media",
          id: "media",
          url: "/settings/media",
          permissions: ["media:view"],
        },
        {
          name: "Users & Roles",
          id: "rbac",
          url: "/settings/rbac",
          requiresRbacFeature: true,
          badge: "NEW",
          permissions: ["rbac:view", "users:view"],
        },
        {
          name: "Users",
          id: "users",
          url: "/settings/users",
          hiddenWithRbacFeature: true,
          permissions: ["users:view"],
        },

        {
          name: "Azure",
          id: "azure",
          url: "/settings/azure",
          permissions: ["azure_operations:view"],
        },
        {
          name: "Tags",
          id: "tags",
          url: "/settings/tags",
          permissions: ["tags:view"],
        },
        {
          name: "Brandings",
          id: "brandings",
          url: "/settings/brandings",
          permissions: ["branding:view"],
        },
        {
          name: "SMTP",
          id: "smtp",
          url: "/settings/smtp",
          permissions: ["smtp_configurations:view"],
        },
        {
          name: "Inventory",
          id: "inventory",
          url: "/settings/inventory",
          permissions: ["inventory_task:view"],
        },
        {
          name: "System Update",
          id: "system-update",
          url: "/settings/system-update",
          permissions: ["system_operations:view"],
        },
        {
          name: "System Status",
          id: "system-status",
          url: "/settings/system-status",
          permissions: ["system_operations:view"],
        },
        {
          requiresManagementAccess: true,
          name: "Logs",
          id: "logs",
          url: "/settings/application-logs",
          permissions: ["global:manage"],
        },
        {
          name: "Audit",
          id: "audit",
          url: "/settings/audit",
          permissions: ["audit:view"],
        },
        {
          name: "OAuth Tokens",
          id: "oauth-tokens",
          url: "/settings/oauth-tokens",
          permissions: ["oauth:manage"],
        },
        {
          name: "Locks",
          id: "locks",
          url: "/settings/locks",
          permissions: ["application_locks:view"],
        },
      ],
    },
  ],
  unauthenticatedItems: [
    {
      name: "Login",
      id: "login",
      url: "/",
      icon: "fal fa-lock",
      permissions: [],
    },
  ],
};

export default nav;
