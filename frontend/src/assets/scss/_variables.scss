$dev-banner-height: 3rem;
$trial-banner-height: 3rem;
$navbar-height: 3rem;

// dark variables
$dark-primary: #01ead1;
$dark-secondary: rgba(255, 255, 255, 1);
$dark-tertiary: rgba(98, 210, 243, 1);
$dark-success: rgba(0, 210, 143, 1);
$dark-warning: rgba(245, 222, 137, 1);
$dark-danger: #fa759f;
$dark-danger-filter: invert(62%) sepia(52%) saturate(1164%) hue-rotate(300deg) brightness(101%) contrast(96%);
$dark-primary-filter: invert(79%) sepia(19%) saturate(4422%) hue-rotate(117deg) brightness(95%) contrast(99%);
$dark-running: #62d2f3;
$dark-bg-primary: rgba(21, 40, 38, 1);
$dark-bg-secondary: rgba(33, 37, 38, 1);
$dark-bg-tertiary: rgba(8, 28, 38, 1);
$dark-bg-success: rgba(8, 38, 28, 1);
$dark-bg-warning: rgba(40, 35, 21, 1);
$dark-bg-danger: rgba(38, 24, 28, 1);
$dark-bg-running: #081c26;
$dark-bg-surface: rgb(0, 0, 0);
$dark-bg-default: rgba(14, 16, 16, 1);
$dark-bg-default2: rgba(33, 37, 38, 1);
$dark-bg-default3: rgba(21, 23, 24, 1);
$dark-stroke-warning: rgba(102, 91, 56, 1);
$dark-stroke-danger: #864055;
$dark-text-primary: rgba(255, 255, 255, 1);
$dark-text-secondary: rgba(141, 141, 141, 1);
$dark-text-disabled: rgba(82, 82, 82, 1);
$dark-icon-default: rgba(115, 115, 115, 1);
$dark-divider: rgba(50, 51, 52, 1);

// light variables
$light-primary: #85f2ca;
$light-bg-primary: rgba(233, 252, 245, 1);
$light-secondary: rgba(255, 255, 255, 1);
$light-bg-secondary: rgba(255, 255, 255, 1);
$light-tertiary: #007ea8;
$light-bg-tertiary: rgba(225, 230, 235, 1);
$light-bg-running: #ccf2ff;
$light-running: #0096cc;
$light-success: rgba(0, 210, 143, 1);
$light-bg-success: rgba(230, 250, 244, 1);
$light-warning: rgba(229, 134, 0, 1);
$light-stroke-warning: rgba(255, 191, 102, 1);
$light-bg-warning: rgba(255, 243, 224, 1);
$light-danger: #ec1344;
$light-primary-filter: invert(0%) sepia(0%) saturate(0%) hue-rotate(268deg) brightness(107%) contrast(107%);
$light-danger-filter: invert(21%) sepia(93%) saturate(7075%) hue-rotate(341deg) brightness(96%) contrast(92%);
$light-stroke-danger: rgba(236, 130, 154, 1);
$light-bg-danger: rgba(253, 247, 247, 1);
$light-bg-surface: rgba(255, 255, 255, 1);
$light-bg-default: rgba(255, 255, 255, 1);
$light-bg-default2: rgba(248, 249, 252, 1);
$light-bg-default3: rgba(225, 230, 235, 1);
$light-stroke: rgba(218, 222, 229, 1);
$light-text-primary: rgba(19, 19, 21, 1);
$light-text-secondary: rgba(107, 115, 123, 1);
$light-text-disabled: rgba(175, 176, 182, 1);
$light-icon-default: rgba(167, 175, 190, 1);
$light-divider: rgba(227, 231, 237, 1);

// Bootstrap overrides
$enable-transitions: true !default;
$font-size-base: 0.875rem !default;
$dropdown-padding-y: 0 !default;
$breadcrumb-margin-bottom: 1.5rem !default;

$black: #000;
// Variable overrides
$input-color: #333 !default;
$enable-gradients: true;

/* Primary colors */
$primary-color: #fff;
$primary-focus-color: #20a8d8;

/* Secondary colors */
$secondary-color: #2f353a;
$secondary-color-light: lighten($secondary-color, 5%);
$secondary-color-dark: darken($secondary-color, 5%);
$secondary-focus-color: #fff;

$app-background-color: #f1f1f1;

$header-height: 3rem;

$max-widths: (
  sm: 100px,
  md: 200px,
  lg: 300px,
  xl: 400px,
  xxl: 500px,
  xxxl: 650px,
);

// Vue-select variables
$vs-dropdown-z-index: 1041;

// Update to 8pt Grid System
$grid-gutter-width: 1rem !default;
$container-max-widths: (
  sm: 480px,
  md: 736px,
  lg: 992px,
  xl: 1200px,
) !default;
$grid-breakpoints: (
  xs: 0,
  sm: 512px,
  md: 768px,
  lg: 1024px,
  xl: 1200px,
) !default;

$breakpoint-sm-max: 511.98px;
$breakpoint-md-max: 767.98px;
$breakpoint-lg-max: 1023.98px;
$breakpoint-xl-max: 1199.98px;

.immy-scrollbar {
  overflow: auto;
  &::-webkit-scrollbar {
    height: 10px;
    width: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--sidebar-scrollbar-thumb, #c1c1c1);
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--sidebar-scrollbar-thumb-hover, #b3b3b3);
  }

  &::-webkit-scrollbar-track {
    background: var(--sidebar-scrollbar-track, #525a61);
  }
}

.roboto-font {
  font-family: "Roboto Mono", monospace;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.4px;
}

.roboto-btn {
  @extend .roboto-font;
  font-size: 0.75rem;
  height: 2rem;
  align-items: center;
  justify-content: center;
  display: flex;
}
