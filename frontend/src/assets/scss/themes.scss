/**
  CONCEPT

  We define the core scss color variables for both the dark and light theme.
  We then create css variables based off of the core colors.

  This provides us multiple benefits.

  1) Define core colors for each theme once
  2) Derive lighter, darker css variables off core colors

**/

// Calculate the effective color achived when layering $topColor over $bottomColor using alpha
@function overlay-color($topColor, $bottomColor) {
  $resultAlpha: calc(alpha($topColor) + (1 - alpha($topColor)) * alpha($bottomColor));
  $topPortion: calc(alpha($topColor) / $resultAlpha);
  $bottomPortion: calc(((1 - alpha($topColor)) * alpha($bottomColor)) / $resultAlpha);

  @return rgb(
    red($topColor) * $topPortion + red($bottomColor) * $bottomPortion,
    green($topColor) * $topPortion + green($bottomColor) * $bottomPortion,
    blue($topColor) * $topPortion + blue($bottomColor) * $bottomPortion,
    $resultAlpha
  );
}

:root {
  --badge-blue-color: #1e3245;
  --badge-blue-bg: #d6e4ee;
  --badge-purple-color: #3d2551;
  --badge-purple-bg: #e6deed;
  --badge-orange-color: #452a13;
  --badge-orange-bg: #f5dfcc;
  --badge-black-bg: #dfdfdf;
  --badge-black-color: #000;

  --icon-filter: invert(99%) sepia(21%) saturate(289%) hue-rotate(243deg) brightness(116%) contrast(87%);
  --dev-banner-height: #{$dev-banner-height};
  --trial-banner-height: #{$trial-banner-height};
  --navbar-height: #{$navbar-height};
  --vs-font-size: 0.875rem;

  --script-editor-color: #cecece;
  --script-editor-muted-color: #8e8e8e;
  --script-editor-activity-bar-bg-color: #333333;
  --script-editor-activity-icon-color: #8e8e8e;
  --script-editor-activity-icon-active-color: #ffffff;
  --script-editor-toolbar-bg-color: #323233;
  --script-editor-toolbar-link-color: #8e8e8e;
  --script-editor-open-tab-toolbar-bg-color: #252526;
  --script-editor-open-tab-link-bg-color: #2d2d2d;
  --script-editor-open-tab-link-active-bg-color: #1e1e1e;
  --script-editor-open-tab-link-color: #969696;
  --script-editor-open-tab-link-active-color: #ffffff;
  --script-editor-primary-sidebar-bg-color: #252526;
  --script-editor-secondary-sidebar-bg-color: #252526;
  --script-editor-primary-sidebar-color: #cecece;
  --script-editor-resizer-hover-bg-color: #{$dark-primary};
  --script-editor-panel-bg-color: #1e1e1e;
  --script-editor-li-hover-color: #2a2d2e;
  --script-editor-li-active-color: #37373d;
  --script-editor-search-result-line-match-bg-color: #724329;

  --script-editor-activity-bar-width: 46px;
  --script-editor-toolbar-height: 32px;
  --script-editor-status-bar-height: 24px;
  --script-editor-sash-hover-size: 4px;
  --script-editor-open-tabs-toolbar-height: 37px;

  --request-immy-support-modal-z-index: 1053;
  --resizer-z-index: 99999;
  --script-editor-z-index: 1052;

  --font-size-10: 10px;
  --font-size-12: 12px;
  --font-size-14: 14px;
  --font-size-16: 16px;
  --font-size-20: 20px;
  --font-size-24: 24px;
  --font-size-28: 28px;
  --font-size-40: 40px;
  --font-size-112: 112px;
}

:root[data-theme="dark"] {
  --overlay-bg-color: rgb(255, 255, 255, 0.15);
  --h1-font-weight: 300;
  --h2-font-weight: 400;
  --h3-font-weight: 300;
  --h4-font-weight: 300;
  --h5-font-weight: 400;
  --h6-font-weight: 300;

  --primary: #{$dark-primary};
  --secondary: #{$dark-secondary};
  --tertiary: #{$dark-tertiary};
  --success: #{$dark-success};
  --warning: #{$dark-warning};
  --danger: #{$dark-danger};
  --danger-filter: #{$dark-danger-filter};
  --primary-filter: #{$dark-primary-filter};
  --running: #{$dark-running};

  --bg-surface: #{$dark-bg-surface};
  --bg-primary: #{$dark-bg-primary};
  --bg-secondary: #{$dark-bg-secondary};
  --bg-tertiary: #{$dark-bg-tertiary};
  --bg-success: #{$dark-bg-success};
  --bg-warning: #{$dark-bg-warning};
  --bg-danger: #{$dark-bg-danger};
  --bg-running: #{$dark-bg-running};

  --bg-default: #{$dark-bg-default};
  --bg-default2: #{$dark-bg-default2};
  --bg-default3: #{$dark-bg-default3};

  --stroke-warning: #{$dark-stroke-warning};
  --stroke-danger: #{$dark-stroke-danger};

  --text-default: rgba(255, 255, 255, 1);
  --text-primary: #{$dark-text-primary};
  --text-secondary: #{$dark-text-secondary};
  --text-disabled: #{$dark-text-disabled};

  --icon-default: #{$dark-icon-default};
  --icon-filter: invert(42%) sepia(59%) saturate(0%) hue-rotate(156deg) brightness(94%) contrast(91%);

  --divider: #{$dark-divider};

  // sidebar
  --sidebar-bg: none;
  --sidebar-item-hover: var(--bg-default3);
  --sidebar-hover-dropdown-items-color: var(--bg-surface);
  --sidebar-active-bg: var(--bg-default2);
  --sidebar-scrollbar-thumb: #{$dark-text-secondary};
  --sidebar-scrollbar-thumb-hover: #{adjust-color($dark-text-secondary, $lightness: 10%)};
  --sidebar-scrollbar-track: #191919;

  // action sublist colors
  --action-sublist-pr-grouper-color: #{overlay-color(
      adjust-color($dark-running, $lightness: -5%, $alpha: -0.5),
      $dark-bg-default
    )};
  --action-sublist-ch-grouper-color: #{$dark-primary};
  --action-sublist-appearance-panel-bg: var(--badge-blue-color);
  --action-sublist-appearance-button-icon-color: var(--btn-running-active-bg);
  --action-sublist-appearance-button-hover-bg: #2b5069;
  --action-sublist-appearance-button-selected-bg: var(--btn-outline-running-bg);

  --bg-main: #{$dark-bg-surface};
  --bg-header: var(--bg-default);
  --bg-input: var(--bg-default2);
  --link: var(--primary);

  --input: var(--text-primary);

  // vue-select
  --vs-controls-color: var(--text-secondary);
  --vs-border-color: var(--divider);
  --vs-dropdown-bg: var(--bg-input);
  --vs-dropdown-color: #fff;
  --vs-dropdown-option-color: #fff;
  --vs-selected-bg: var(--bg-input);
  --vs-selected-color: #fff;
  --vs-search-input-color: #fff;
  --vs-search-input-bg: var(--bg-input);
  --vs-dropdown-option--active-bg: var(--bg-default);
  --vs-dropdown-option--active-color: var(--text-primary);
  --vs-disabled-bg: var(--bg-input);

  --badge-primary-bg: var(--bg-primary);
  --badge-primary-color: var(--primary);
  --badge-secondary-bg: var(--bg-secondary);
  --badge-secondary-color: var(--secondary);
  --badge-success-bg: var(--bg-success);
  --badge-success-color: var(--success);
  --badge-danger-bg: var(--bg-danger);
  --badge-danger-color: var(--danger);
  --badge-warning-bg: var(--bg-warning);
  --badge-warning-color: var(--warning);
  --badge-running-bg: var(--bg-running);
  --badge-running-color: var(--running);

  // buttons
  --btn-link-color: #{$dark-primary};

  --btn-primary-color: #000;
  --btn-primary-bg: #{$dark-primary};
  --btn-primary-border-color: #{$dark-primary};
  --btn-primary-hover-color: #000;
  --btn-primary-hover-bg: #{adjust-color($dark-primary, $lightness: -10%)};
  --btn-primary-hover-border-color: #{adjust-color($dark-primary, $lightness: -10%)};
  --btn-primary-focus-shadow-rgb: #{adjust-color($dark-primary, $lightness: -5%, $alpha: -0.5)};
  --btn-primary-active-color: #000;
  --btn-primary-active-bg: #{adjust-color($dark-primary, $lightness: -12%)};
  --btn-primary-active-border-color: #{adjust-color($dark-primary, $lightness: -12%)};
  --btn-primary-active-shadow-rgb: #{adjust-color($dark-primary, $lightness: -6%, $alpha: -0.5)};
  --btn-primary-disabled-opacity: 0.65;

  --btn-outline-primary-color: #{$dark-primary};
  --btn-outline-primary-bg: #{$dark-bg-primary};
  --btn-outline-primary-border-color: #{$dark-primary};
  --btn-outline-primary-hover-color: #000;
  --btn-outline-primary-hover-bg: #{$dark-primary};
  --btn-outline-primary-hover-border-color: #{$dark-primary};
  --btn-outline-primary-focus-shadow-rgb: #{adjust-color($dark-primary, $lightness: -5%, $alpha: -0.5)};
  --btn-outline-primary-active-color: #000;
  --btn-outline-primary-active-bg: #{$dark-primary};
  --btn-outline-primary-active-border-color: #{$dark-primary};
  --btn-outline-primary-active-shadow-rgb: #{adjust-color($dark-primary, $lightness: -6%, $alpha: -0.5)};
  --btn-outline-primary-disabled-opacity: 0.65;

  --btn-running-color: #000;
  --btn-running-bg: #{$dark-running};
  --btn-running-border-color: #{$dark-running};
  --btn-running-hover-color: #000;
  --btn-running-hover-bg: #{adjust-color($dark-running, $lightness: -10%)};
  --btn-running-hover-border-color: #{adjust-color($dark-running, $lightness: -10%)};
  --btn-running-focus-shadow-rgb: #{adjust-color($dark-running, $lightness: -5%, $alpha: -0.5)};
  --btn-running-active-color: #000;
  --btn-running-active-bg: #{adjust-color($dark-running, $lightness: -12%)};
  --btn-running-active-border-color: #{adjust-color($dark-running, $lightness: -12%)};
  --btn-running-active-shadow-rgb: #{adjust-color($dark-running, $lightness: -6%, $alpha: -0.5)};
  --btn-running-disabled-opacity: 0.65;

  --btn-outline-running-color: #{$dark-running};
  --btn-outline-running-bg: #{$dark-bg-running};
  --btn-outline-running-border-color: #{$dark-running};
  --btn-outline-running-hover-color: #000;
  --btn-outline-running-hover-bg: #{$dark-running};
  --btn-outline-running-hover-border-color: #{$dark-running};
  --btn-outline-running-focus-shadow-rgb: #{adjust-color($dark-running, $lightness: -5%, $alpha: -0.5)};
  --btn-outline-running-active-color: #000;
  --btn-outline-running-active-bg: #{$dark-running};
  --btn-outline-running-active-border-color: #{$dark-running};
  --btn-outline-running-active-shadow-rgb: #{adjust-color($dark-running, $lightness: -6%, $alpha: -0.5)};
  --btn-outline-running-disabled-opacity: 0.65;

  --btn-secondary-color: #{$dark-text-primary};
  --btn-secondary-bg: #{$dark-bg-default2};
  --btn-secondary-border-color: rgba(255, 255, 255, 0.3);
  --btn-secondary-hover-color: #{$dark-text-primary};
  --btn-secondary-hover-bg: #{adjust-color($dark-bg-default2, $lightness: -3%)};
  --btn-secondary-hover-border-color: rgba(255, 255, 255, 0.3);
  --btn-secondary-focus-shadow-rgb: #{adjust-color($dark-bg-default2, $lightness: 10%, $alpha: -0.5)};
  --btn-secondary-active-color: #{$dark-text-primary};
  --btn-secondary-active-bg: #{adjust-color($dark-bg-default2, $lightness: -5%)};
  --btn-secondary-active-border-color: rgba(255, 255, 255, 0.3);
  --btn-secondary-active-shadow-rgb: #{adjust-color($dark-bg-default2, $lightness: 6%, $alpha: -0.5)};
  --btn-secondary-disabled-opacity: 0.65;

  --btn-outline-secondary-color: #{$dark-text-primary};
  --btn-outline-secondary-bg: #{$dark-bg-default2};
  --btn-outline-secondary-border-color: rgba(255, 255, 255, 0.3);
  --btn-outline-secondary-hover-color: #{$dark-text-primary};
  --btn-outline-secondary-hover-bg: #{adjust-color($dark-bg-default2, $lightness: -3%)};
  --btn-outline-secondary-hover-border-color: rgba(255, 255, 255, 0.3);
  --btn-outline-secondary-focus-shadow-rgb: #{adjust-color($dark-bg-default2, $lightness: 10%, $alpha: -0.5)};
  --btn-outline-secondary-active-color: #{$dark-text-primary};
  --btn-outline-secondary-active-bg: #{adjust-color($dark-bg-default2, $lightness: -5%)};
  --btn-outline-secondary-active-border-color: rgba(255, 255, 255, 0.3);
  --btn-outline-secondary-active-shadow-rgb: #{adjust-color($dark-bg-default2, $lightness: 6%, $alpha: -0.5)};
  --btn-outline-secondary-disabled-opacity: 0.65;

  --btn-success-color: #000;
  --btn-success-bg: #{$dark-success};
  --btn-success-border-color: #{$dark-success};
  --btn-success-hover-color: #000;
  --btn-success-hover-bg: #{adjust-color($dark-success, $lightness: -10%)};
  --btn-success-hover-border-color: #{adjust-color($dark-success, $lightness: -10%)};
  --btn-success-focus-shadow-rgb: #{adjust-color($dark-success, $lightness: -5%, $alpha: -0.5)};
  --btn-success-active-color: #000;
  --btn-success-active-bg: #{adjust-color($dark-success, $lightness: -12%)};
  --btn-success-active-border-color: #{adjust-color($dark-success, $lightness: -12%)};
  --btn-success-active-shadow-rgb: #{adjust-color($dark-success, $lightness: -6%, $alpha: -0.5)};
  --btn-success-disabled-opacity: 0.65;

  --btn-outline-success-color: #{$dark-success};
  --btn-outline-success-bg: #{$dark-bg-success};
  --btn-outline-success-border-color: #{$dark-success};
  --btn-outline-success-hover-color: #fff;
  --btn-outline-success-hover-bg: #{$dark-success};
  --btn-outline-success-hover-border-color: #{adjust-color($dark-success, $lightness: -10%)};
  --btn-outline-success-focus-shadow-rgb: #{adjust-color($dark-success, $lightness: -5%, $alpha: -0.5)};
  --btn-outline-success-active-color: #fff;
  --btn-outline-success-active-bg: #{$dark-success};
  --btn-outline-success-active-border-color: #{$dark-success};
  --btn-outline-success-active-shadow-rgb: #{adjust-color($dark-success, $lightness: -6%, $alpha: -0.5)};
  --btn-outline-success-disabled-opacity: 0.65;

  --btn-danger-color: #000;
  --btn-danger-bg: #{$dark-danger};
  --btn-danger-border-color: #{$dark-danger};
  --btn-danger-hover-color: #000;
  --btn-danger-hover-bg: #{adjust-color($dark-danger, $lightness: -10%)};
  --btn-danger-hover-border-color: #{adjust-color($dark-danger, $lightness: -10%)};
  --btn-danger-focus-shadow-rgb: #{adjust-color($dark-danger, $lightness: -5%, $alpha: -0.6)};
  --btn-danger-active-color: #000;
  --btn-danger-active-bg: #{adjust-color($dark-danger, $lightness: -12%)};
  --btn-danger-active-border-color: #{adjust-color($dark-danger, $lightness: -12%)};
  --btn-danger-active-shadow-rgb: #{adjust-color($dark-danger, $lightness: -6%, $alpha: -0.6)};
  --btn-danger-disabled-opacity: 0.65;

  --btn-outline-danger-color: #{$dark-danger};
  --btn-outline-danger-bg: #{$dark-bg-danger};
  --btn-outline-danger-border-color: #{$dark-danger};
  --btn-outline-danger-hover-color: #000;
  --btn-outline-danger-hover-bg: #{$dark-danger};
  --btn-outline-danger-hover-border-color: #{adjust-color($dark-danger, $lightness: -10%)};
  --btn-outline-danger-focus-shadow-rgb: #{adjust-color($dark-danger, $alpha: -0.6)};
  --btn-outline-danger-active-color: #000;
  --btn-outline-danger-active-bg: #{$dark-danger};
  --btn-outline-danger-active-border-color: #{$dark-danger};
  --btn-outline-danger-active-shadow-rgb: #{adjust-color($dark-danger, $alpha: -0.6)};
  --btn-outline-danger-disabled-opacity: 0.65;

  --btn-warning-color: #000;
  --btn-warning-bg: #{$dark-warning};
  --btn-warning-border-color: #{$dark-warning};
  --btn-warning-hover-color: #000;
  --btn-warning-hover-bg: #{adjust-color($dark-warning, $lightness: -10%)};
  --btn-warning-hover-border-color: #{adjust-color($dark-warning, $lightness: -10%)};
  --btn-warning-focus-shadow-rgb: #{adjust-color($dark-warning, $lightness: -5%, $alpha: -0.5)};
  --btn-warning-active-color: #000;
  --btn-warning-active-bg: #{adjust-color($dark-warning, $lightness: -12%)};
  --btn-warning-active-border-color: #{adjust-color($dark-warning, $lightness: -12%)};
  --btn-warning-active-shadow-rgb: #{adjust-color($dark-warning, $lightness: -6%, $alpha: -0.5)};
  --btn-warning-disabled-opacity: 0.65;

  --btn-outline-warning-color: #{$dark-warning};
  --btn-outline-warning-bg: #{$dark-bg-warning};
  --btn-outline-warning-border-color: #{$dark-warning};
  --btn-outline-warning-hover-color: #000;
  --btn-outline-warning-hover-bg: #{$dark-warning};
  --btn-outline-warning-hover-border-color: #{adjust-color($dark-warning, $lightness: -10%)};
  --btn-outline-warning-focus-shadow-rgb: #{adjust-color($dark-warning, $lightness: -5%, $alpha: -0.5)};
  --btn-outline-warning-active-color: #000;
  --btn-outline-warning-active-bg: #{$dark-warning};
  --btn-outline-warning-active-border-color: #{$dark-warning};
  --btn-outline-warning-active-shadow-rgb: #{adjust-color($dark-warning, $lightness: -6%, $alpha: -0.5)};
  --btn-outline-warning-disabled-opacity: 0.65;

  // alerts

  --alert-primary-color: #{$dark-primary};
  --alert-primary-bg: #{$dark-bg-primary};
  --alert-primary-border-color: #{adjust-color($dark-primary, $lightness: -30%)};
  --alert-danger-color: #{$dark-danger};
  --alert-danger-bg: #{$dark-bg-danger};
  --alert-danger-border-color: #{$dark-stroke-danger};
  --alert-warning-color: #{$dark-warning};
  --alert-warning-bg: #{$dark-bg-warning};
  --alert-warning-border-color: #{$dark-stroke-warning};

  --script-highlight-bg: #1e1e1e;
  --script-highlight-color: white;

  --computer-icon-online-bg: linear-gradient(180deg, #08261e 0%, rgba(38, 24, 28, 0) 100%);
  --computer-icon-online-border: 1px solid #364d45;
  --computer-icon-offline-bg: linear-gradient(180deg, #1a1013 0%, rgba(38, 24, 28, 0) 100%);
  --computer-icon-offline-border: 1px solid #4d2e37;
  --computer-icon-na-bg: linear-gradient(180deg, #363117 0%, rgba(38, 24, 28, 0) 100%);
  --computer-icon-na-border: 1px solid #eed6a980;

  --radio-checked-bg: var(--primary);
  --radio-active-bg: #c8ffc8;
  --radio-checked-border-color: var(--primary);
  --radio-checked-box-shadow: 0 0 0 0.2rem rgb(1 234 209 / 14%);
  --radio-checked-filter: brightness(0);
  --radio-color: black;

  --sidebar-actions-bg: var(--bg-default3);

  //First Time Login (dark)

  //colors
  --mo-background: black;
  --mo-title-color: #ffff;
  --mo-header-color: #e1e6eb;
  --mo-offering-color: #8d8d8d;
  --mo-setup-info-color: #fff;
  --mo-offer-button-background: #01ead1;
  --mo-offer-button-passive-background: #171717;
  --mo-offer-button-passive-color: #5c5c5c;
  --mo-machine-card-background: #001c19;
  --mo-machine-card-border-color: #6b737b;
  --mo-machine-card-inner-background: #151718;
  --mo-gradient-from: #c16cff;
  --mo-gradient-to: #ff42a4;
  --mo-step-background: #0a8679;

  //checkList
  --check-list-nav-item-color: #01ead1;
  --check-list-border-color: #323334;
  --check-list-bg-color: #131315;
  --check-list-description-color: #89898a;
  --check-list-step-bg-color: #001715;
  --check-list-step-active-bg-color: #01ead1;
  --check-list-actions-bg-color: #152826;
  --check-list-current-bg-color: #005e54;
  --check-list-doc-border-color: rgba(1, 234, 209, 0.5);
  --check-list-onboarding-list-item-color: #62d2f3;
  --check-list-email-color: #01ead1;
  --check-list--content-description-color: #8d8d8d;
  --check-list-action-button-bg-color: #01ead1;
  --check-list-doc-button-bg-color: #152826;
  --check-list-doc-button-color: #01ead1;
  --check-list-group-title-color: #01ead1;
  --check-list-tab-body-container-bg-color: #212526;
  --check-list-icon-state-pause-color: #005e54;
  --check-list-icon-state-default-color: #85f2ca;
  --checklist-action-group-item-icon-completed-color: #0a8679;
  --checklist-action-group-item-name-completed-color: #005e54;
  --checklist-action-group-item-active-color: #005e54;
}

:root[data-theme="light"] {
  --overlay-bg-color: rgb(0, 0, 0, 0.15);

  --h1-font-weight: 400;
  --h2-font-weight: 600;
  --h3-font-weight: 400;
  --h4-font-weight: 400;
  --h5-font-weight: 600;
  --h6-font-weight: 400;

  --primary: #{$light-primary};
  --secondary: #{$light-secondary};
  --tertiary: #{$light-tertiary};
  --success: #{$light-success};
  --warning: #{$light-warning};
  --danger: #{$light-danger};
  --danger-filter: #{$light-danger-filter};
  --primary-filter: #{$light-primary-filter};
  --running: #{$light-running};

  --bg-primary: #{$light-bg-primary};
  --bg-secondary: #{$light-bg-secondary};
  --bg-tertiary: #{$light-bg-tertiary};
  --bg-success: #{$light-bg-success};
  --bg-warning: #{$light-bg-warning};
  --bg-danger: #{$light-bg-danger};
  --bg-running: #{$light-bg-running};

  --bg-surface: #{$light-bg-surface};
  --bg-default: #{$light-bg-default};
  --bg-default2: #{$light-bg-default2};
  --bg-default3: #{$light-bg-default3};

  --stroke-warning: #{$light-stroke-warning};
  --stroke-danger: #{$light-stroke-danger};

  --text-default: #000;
  --text-primary: #{$light-text-primary};
  --text-secondary: #{$light-text-secondary};
  --text-disabled: #{$light-text-disabled};

  --icon-default: #{$light-icon-default};
  --icon-filter: invert(79%) sepia(6%) saturate(591%) hue-rotate(181deg) brightness(88%) contrast(87%);

  --divider: #{$light-divider};

  // sidebar
  --sidebar-bg: none;
  --sidebar-item-hover: var(--bg-default3);
  --sidebar-hover-dropdown-items-color: #fff;
  --sidebar-active-bg: var(--bg-default3);
  --sidebar-scrollbar-thumb: #caccd0;
  --sidebar-scrollbar-thumb-hover: #{adjust-color($light-text-secondary, $lightness: 25%)};
  --sidebar-scrollbar-track: #e7e7e7;

  // action sublist colors
  --action-sublist-pr-grouper-color: #{overlay-color(
      adjust-color($light-running, $lightness: -5%, $alpha: -0.6),
      $light-bg-default
    )};
  --action-sublist-ch-grouper-color: #{$light-primary};
  --action-sublist-appearance-panel-bg: var(--badge-blue-bg);
  --action-sublist-appearance-button-icon-color: var(--btn-running-active-bg);
  --action-sublist-appearance-button-hover-bg: #e9fbffd2;
  --action-sublist-appearance-button-selected-bg: var(--btn-outline-running-bg);

  --bg-header: #{$light-bg-default};
  --bg-main: #{$light-bg-surface};
  --bg-input: #fff;

  --link: var(--tertiary);

  --input: var(--text-primary);

  // vue-select
  --vs-controls-color: var(--text-secondary);
  --vs-border-color: var(--divider);
  --vs-dropdown-bg: var(--bg-default);
  --vs-dropdown-color: #000;
  --vs-dropdown-option-color: #000;
  --vs-selected-bg: var(--bg-input);
  --vs-selected-color: #000;
  --vs-search-input-color: #000;
  --vs-search-input-bg: var(--bg-input);
  --vs-dropdown-option--active-bg: var(--bg-default3);
  --vs-dropdown-option--active-color: var(--text-primary);
  --vs-disabled-bg: var(--bg-input);

  --badge-primary-bg: var(--bg-primary);
  --badge-primary-color: #0d6042;
  --badge-secondary-bg: #f9f9f9;
  --badge-secondary-color: #4e4e4e;
  --badge-success-bg: var(--bg-success);
  --badge-success-color: #00895d;
  --badge-danger-bg: #fae3de;
  --badge-danger-color: #551c18;
  --badge-warning-bg: #faedcc;
  --badge-warning-color: #7e6248;
  --badge-running-bg: var(--bg-running);
  --badge-running-color: var(--running);

  // buttons
  --btn-link-color: var(--link);

  --btn-primary-color: #000;
  --btn-primary-bg: #{$light-primary};
  --btn-primary-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.05);
  --btn-primary-border-color: #{$light-primary};
  --btn-primary-hover-color: #000;
  --btn-primary-hover-bg: #{adjust-color($light-primary, $lightness: -10%)};
  --btn-primary-hover-border-color: #{adjust-color($light-primary, $lightness: -10%)};
  --btn-primary-focus-shadow-rgb: #{adjust-color($light-primary, $lightness: -5%, $alpha: -0.6)};
  --btn-primary-active-color: #000;
  --btn-primary-active-bg: #{adjust-color($light-primary, $lightness: -12%)};
  --btn-primary-active-border-color: #{adjust-color($light-primary, $lightness: -12%)};
  --btn-primary-active-shadow-rgb: #{adjust-color($light-primary, $lightness: -6%, $alpha: -0.6)};
  --btn-primary-disabled-opacity: 0.65;

  --btn-outline-primary-color: #{$light-text-primary};
  --btn-outline-primary-bg: #{$light-bg-default2};
  --btn-outline-primary-border-color: #{$light-primary};
  --btn-outline-primary-hover-color: #{$light-text-primary};
  --btn-outline-primary-hover-bg: #{$light-primary};
  --btn-outline-primary-hover-border-color: #{$light-primary};
  --btn-outline-primary-focus-shadow-rgb: #{$light-primary};
  --btn-outline-primary-active-color: #000;
  --btn-outline-primary-active-bg: #{$light-primary};
  --btn-outline-primary-active-border-color: #{$light-primary};
  --btn-outline-primary-active-shadow-rgb: #{$light-primary};
  --btn-outline-primary-disabled-opacity: 0.65;

  --btn-secondary-color: #{$light-text-primary};
  --btn-secondary-bg: #{$light-secondary};
  --btn-secondary-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.05);
  --btn-secondary-border-color: #{$light-stroke};
  --btn-secondary-hover-color: #{$light-text-primary};
  --btn-secondary-hover-bg: #{adjust-color($light-secondary, $lightness: -10%)};
  --btn-secondary-hover-border-color: #{adjust-color($light-secondary, $lightness: -10%)};
  --btn-secondary-focus-shadow-rgb: #{adjust-color($light-secondary, $lightness: -5%, $alpha: -0.6)};
  --btn-secondary-active-color: #{$light-text-primary};
  --btn-secondary-active-bg: #{adjust-color($light-secondary, $lightness: -12%)};
  --btn-secondary-active-border-color: #{adjust-color($light-secondary, $lightness: -12%)};
  --btn-secondary-active-shadow-rgb: #{adjust-color($light-secondary, $lightness: -6%, $alpha: -0.6)};
  --btn-secondary-disabled-opacity: 0.65;

  --btn-outline-secondary-color: #{$light-text-secondary};
  --btn-outline-secondary-bg: #{$light-bg-default2};
  --btn-outline-secondary-border-color: #{$light-secondary};
  --btn-outline-secondary-hover-color: #{$light-text-secondary};
  --btn-outline-secondary-hover-bg: #{$light-secondary};
  --btn-outline-secondary-hover-border-color: #{$light-secondary};
  --btn-outline-secondary-focus-shadow-rgb: #{$light-secondary};
  --btn-outline-secondary-active-color: #000;
  --btn-outline-secondary-active-bg: #{$light-secondary};
  --btn-outline-secondary-active-border-color: #{$light-secondary};
  --btn-outline-secondary-active-shadow-rgb: #{$light-secondary};
  --btn-outline-secondary-disabled-opacity: 0.65;

  --btn-running-color: #fff;
  --btn-running-bg: #{$light-running};
  --btn-running-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.05);
  --btn-running-border-color: #{$light-running};
  --btn-running-hover-color: #fff;
  --btn-running-hover-bg: #{adjust-color($light-running, $lightness: -10%)};
  --btn-running-hover-border-color: #{adjust-color($light-running, $lightness: -10%)};
  --btn-running-focus-shadow-rgb: #{adjust-color($light-running, $lightness: -5%, $alpha: -0.6)};
  --btn-running-active-color: #fff;
  --btn-running-active-bg: #{adjust-color($light-running, $lightness: -12%)};
  --btn-running-active-border-color: #{adjust-color($light-running, $lightness: -12%)};
  --btn-running-active-shadow-rgb: #{adjust-color($light-running, $lightness: -6%, $alpha: -0.6)};
  --btn-running-disabled-opacity: 0.65;

  --btn-outline-running-color: #{$light-running};
  --btn-outline-running-bg: #{$light-bg-running};
  --btn-outline-running-border-color: #{$light-running};
  --btn-outline-running-hover-color: #fff;
  --btn-outline-running-hover-bg: #{$light-running};
  --btn-outline-running-hover-border-color: #{$light-running};
  --btn-outline-running-focus-shadow-rgb: #{$light-running};
  --btn-outline-running-active-color: #fff;
  --btn-outline-running-active-bg: #{$light-running};
  --btn-outline-running-active-border-color: #{$light-running};
  --btn-outline-running-active-shadow-rgb: #{$light-running};
  --btn-outline-running-disabled-opacity: 0.65;

  --btn-success-color: #fff;
  --btn-success-bg: #{$light-success};
  --btn-success-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.05);
  --btn-success-border-color: #{$light-success};
  --btn-success-hover-color: #fff;
  --btn-success-hover-bg: #{adjust-color($light-success, $lightness: -10%)};
  --btn-success-hover-border-color: #{adjust-color($light-success, $lightness: -10%)};
  --btn-success-focus-shadow-rgb: #{adjust-color($light-success, $lightness: -12%, $alpha: -0.6)};
  --btn-success-active-color: #fff;
  --btn-success-active-bg: #{adjust-color($light-success, $lightness: -12%)};
  --btn-success-active-border-color: #{adjust-color($light-success, $lightness: -12%)};
  --btn-success-active-shadow-rgb: #{adjust-color($light-success, $lightness: -12%, $alpha: -0.6)};
  --btn-success-disabled-opacity: 0.65;

  --btn-outline-success-color: #{$light-text-primary};
  --btn-outline-success-bg: #{$light-bg-default2};
  --btn-outline-success-border-color: #{$light-success};
  --btn-outline-success-hover-color: #{$light-text-primary};
  --btn-outline-success-hover-bg: #{$light-success};
  --btn-outline-success-hover-border-color: #{$light-success};
  --btn-outline-success-focus-shadow-rgb: #{$light-success};
  --btn-outline-success-active-color: #000;
  --btn-outline-success-active-bg: #{$light-success};
  --btn-outline-success-active-border-color: #{$light-success};
  --btn-outline-success-active-shadow-rgb: #{$light-success};
  --btn-outline-success-disabled-opacity: 0.65;

  --btn-danger-color: #fff;
  --btn-danger-bg: #{$light-danger};
  --btn-danger-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.05);
  --btn-danger-border-color: #{$light-danger};
  --btn-danger-hover-color: #fff;
  --btn-danger-hover-bg: #{adjust-color($light-danger, $lightness: -10%)};
  --btn-danger-hover-border-color: #{adjust-color($light-danger, $lightness: -10%)};
  --btn-danger-focus-shadow-rgb: #{adjust-color($light-danger, $lightness: -5%, $alpha: -0.6)};
  --btn-danger-active-color: #fff;
  --btn-danger-active-bg: #{adjust-color($light-danger, $lightness: -12%)};
  --btn-danger-active-border-color: #{adjust-color($light-danger, $lightness: -12%)};
  --btn-danger-active-shadow-rgb: #{adjust-color($light-danger, $lightness: -6%, $alpha: -0.6)};
  --btn-danger-disabled-opacity: 0.65;

  --btn-outline-danger-color: #{$light-danger};
  --btn-outline-danger-bg: #{$light-bg-danger};
  --btn-outline-danger-border-color: #{$light-danger};
  --btn-outline-danger-hover-color: white;
  --btn-outline-danger-hover-bg: #{$light-danger};
  --btn-outline-danger-hover-border-color: #{$light-danger};
  --btn-outline-danger-focus-shadow-rgb: #{$light-danger};
  --btn-outline-danger-active-color: white;
  --btn-outline-danger-active-bg: #{$light-danger};
  --btn-outline-danger-active-border-color: #{$light-danger};
  --btn-outline-danger-active-shadow-rgb: #{$light-danger};
  --btn-outline-danger-disabled-opacity: 0.65;

  --btn-warning-color: #fff;
  --btn-warning-bg: #{$light-warning};
  --btn-warning-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.05);
  --btn-warning-border-color: #{$light-warning};
  --btn-warning-hover-color: #fff;
  --btn-warning-hover-bg: #{adjust-color($light-warning, $lightness: -10%)};
  --btn-warning-hover-border-color: #{adjust-color($light-warning, $lightness: -10%)};
  --btn-warning-focus-shadow-rgb: #{adjust-color($light-warning, $lightness: -5%, $alpha: -0.6)};
  --btn-warning-active-color: #fff;
  --btn-warning-active-bg: #{adjust-color($light-warning, $lightness: -12%)};
  --btn-warning-active-border-color: #{adjust-color($light-warning, $lightness: -12%)};
  --btn-warning-active-shadow-rgb: #{adjust-color($light-warning, $lightness: -6%, $alpha: -0.6)};
  --btn-warning-disabled-opacity: 0.65;

  --btn-outline-warning-color: #{$light-text-primary};
  --btn-outline-warning-bg: #{$light-bg-default2};
  --btn-outline-warning-border-color: #dadee6;
  --btn-outline-warning-hover-color: white;
  --btn-outline-warning-hover-bg: #{$light-warning};
  --btn-outline-warning-hover-border-color: #{$light-warning};
  --btn-outline-warning-focus-shadow-rgb: #{$light-warning};
  --btn-outline-warning-active-color: white;
  --btn-outline-warning-active-bg: #{$light-warning};
  --btn-outline-warning-active-border-color: #{$light-warning};
  --btn-outline-warning-active-shadow-rgb: #{$light-warning};
  --btn-outline-warning-disabled-opacity: 0.65;

  // alerts
  --alert-primary-color: #{$light-text-primary};
  --alert-primary-bg: #{$light-bg-primary};
  --alert-primary-border-color: #{adjust-color($light-primary, $lightness: 10%)};
  --alert-danger-color: #{$light-danger};
  --alert-danger-bg: #{$light-bg-danger};
  --alert-danger-border-color: #{$light-stroke-danger};
  --alert-warning-color: #{$light-warning};
  --alert-warning-bg: #{$light-bg-warning};
  --alert-warning-border-color: #{$light-stroke-warning};

  --script-highlight-bg: #1e1e1e;
  --script-highlight-color: white;

  --computer-icon-online-bg: linear-gradient(180deg, #ffffff 0%, rgb(77 189 116 / 18%) 100%);
  --computer-icon-online-border: 1px solid #4dbd74;
  --computer-icon-offline-bg: linear-gradient(180deg, #ffffff 0%, rgb(137 27 27 / 18%) 100%);
  --computer-icon-offline-border: 1px solid #9b1817;
  --computer-icon-na-bg: linear-gradient(180deg, #ffffff 0%, #f8d78e 100%);
  --computer-icon-na-border: 1px solid #{$light-warning};

  --radio-checked-bg: black;
  --radio-active-bg: white;
  --radio-checked-border-color: black;
  --radio-checked-box-shadow: 0 0 0 0.2rem rgb(0 0 0 / 48%);
  --radio-checked-filter: none;
  --radio-color: white;

  --sidebar-actions-bg: var(--bg-default);

  --mo-background: black;
  --mo-title-color: #131315;
  --mo-header-color: #e1e6eb;
  --mo-offering-color: #8d8d8d;
  --mo-setup-info-color: #fff;
  --mo-offer-button-background: #01ead1;
  --mo-offer-button-passive-background: #171717;
  --mo-offer-button-passive-color: #5c5c5c;
  --mo-machine-card-background: #001c19;
  --mo-machine-card-border-color: #6b737b;
  --mo-machine-card-inner-background: #151718;
  --mo-gradient-from: #c16cff;
  --mo-gradient-to: #ff42a4;
  --mo-step-background: #0a8679;

  //checkList
  --check-list-nav-item-color: #0a8679;
  --check-list-border-color: #e3e7ed;
  --check-list-bg-color: #ffff;
  --check-list-description-color: #89898a;
  --check-list-step-bg-color: #e7e7e8;
  --check-list-step-active-bg-color: #66fdb0;
  --check-list-actions-bg-color: #f8f9fc;
  --check-list-current-bg-color: #85f2ca;
  --check-list-doc-border-color: #dadee5;
  --check-list-onboarding-list-item-color: #62d2f3;
  --check-list-email-color: #087aff;
  --check-list--content-description-color: black;
  --check-list-action-button-bg-color: #85f2ca;
  --check-list-doc-button-bg-color: white;
  --check-list-doc-button-color: black;
  --check-list-group-title-color: #5c5c5c;
  --check-list-tab-body-container-bg-color: white;
  --check-list-icon-state-pause-color: #89898a;
  --check-list-icon-state-default-color: #005e54;
  --checklist-action-group-item-icon-completed-color: #89898a;
  --checklist-action-group-item-name-completed-color: #89898a;
  --checklist-action-group-item-active-color: #89898a;
}

// bootstrapy stuffs
.bg-info-light {
  background-color: rgb(99, 194, 222, 0.15) !important;
}

.bg-danger-light {
  background-color: rgba(248, 108, 107, 0.15) !important;
}

.bg-success-light {
  background-color: rgba(77, 189, 116, 0.15) !important;
}
