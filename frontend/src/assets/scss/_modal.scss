#getting-started-wizard {
  :deep(.modal-body) {
    padding: 0 !important;
  }

  :deep(.progress-bar) {
    transition: width 0.2s ease;
  }

  :deep(.immy-progress-bar) {
    transition: transform 0.2s ease-out;
  }

  #getting-started-wizard___BV_modal_body_ {
    .card {
      border: none;
      border-radius: 0;
    }

    .wizard-tab-content {
      border-radius: 0;
      border: none;
      border-left: 1px var(--divider);
      border-top: 1px var(--divider);
      // max height: full page height minus modal header/modal footer/modal top-margin/modal body padding
      max-height: calc(100vh - 60px - 70px - 1.75rem - 32px);
      min-height: 300px;
      overflow-y: auto;
    }
  }
}

.download-agent-installer-modal {
  .modal-body {
    padding: 0 !important;
    .card {
      border: none;
      border-radius: 0;
    }

    .agent-installer-tab-content {
      border-radius: 0;
      border: none;
      // max height: full page height minus modal header/modal footer/modal top-margin/modal body padding
      max-height: calc(100vh - 60px - 70px - 1.75rem - 32px);
      min-height: 300px;
      overflow-y: auto;
    }

    .installer-type-icon {
      margin-right: 5px;
    }
  }
}

#provider-audit-log-modal {
  .audit-log-details {
    max-height: 50vh;
    @extend .immy-scrollbar;
  }

  #provider-audit-log-modal___BV_modal_outer_ {
    z-index: 1052 !important;
  }

  .xterm-wrapper {
    border: 1px solid var(--divider);
    margin-top: 0.25rem;
    padding-left: 0.5rem;
    padding-top: 0.25rem;
  }

  .console-text-wrapper {
    padding: 0 1rem 0 0;
  }

  .audit-log-modal-line-item-value pre {
    color: var(--text-primary);
  }
}

#getting-started-wizard___BV_modal_body_ {
  padding: 0 !important;

  .card {
    border: none;
    border-radius: 0;
  }

  .wizard-tab-content {
    border-radius: 0;
    border: none;
    border-left: 1px var(--divider);
    border-top: 1px var(--divider);
    // max height: full page height minus modal header/modal footer/modal top-margin/modal body padding
    max-height: calc(100vh - 60px - 70px - 1.75rem - 32px);
    min-height: 300px;
    overflow-y: auto;
  }
}
