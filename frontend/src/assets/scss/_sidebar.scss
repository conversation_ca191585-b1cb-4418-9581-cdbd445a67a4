.sidebar {
  padding-top: 0.75rem;
  .nav-link {
    &:hover {
      background: $secondary-color-dark;
    }
  }

  .nav-link.active .nav-icon {
    color: $primary-color;
  }

  .nav-link.router-link-exact-active .nav-icon {
    color: $primary-focus-color;
  }

  .nav-dropdown.open {
    background: $secondary-color-light;
  }

  .nav-dropdown-items {
    transition: none;
  }

  .nav-dropdown-toggle::before {
    display: none;
  }

  .nav-dropdown.active {
    color: $primary-color;
    background: $primary-focus-color;

    &:after {
      @extend .active-indicator;
    }
    &:hover {
      background: $primary-focus-color !important;
    }
    .nav-dropdown-toggle:hover {
      background: $primary-focus-color;
    }
    .nav-dropdown-toggle > .nav-icon {
      color: $primary-color;
    }
  }

  .nav-item {
    transition: none;
  }

  .nav-dropdown.open .nav-link.router-link-exact-active {
    color: $primary-focus-color;
  }

  .nav > .nav-item > div > a.router-link-exact-active {
    color: $primary-color;
    background: $primary-focus-color;

    .nav-icon {
      color: $primary-color;
    }

    &:after {
      @extend .active-indicator;
    }
  }

  .nav-dropdown-toggle .badge {
    margin-right: 0px;
  }
}

.sidebar-nav-item-notification {
  list-style: none;
  border: 1px solid var(--divider, #23282c);
  background: var(--bg-default3, #292e32);
  box-shadow: var(--btn-primary-box-shadow, none);
  &:hover {
    background: var(--bg-default2);
  }
}

.sidebar-nav-item-notification-link {
  color: var(--link, #20a8d8);
}

.sidebar-minimized .sidebar {
  .nav-link:hover {
    background: $secondary-color-dark;
  }

  .nav > .nav-dropdown:hover {
    background: $secondary-color-dark;
  }

  .nav-item:hover > .nav-link {
    background: $secondary-color-dark;
  }

  .nav-link.router-link-exact-active .nav-icon {
    color: $primary-focus-color;
  }

  :not(.open).is-over > .nav-dropdown-items {
    margin-top: 0px;
  }
}

:not(.open).is-over > .nav-dropdown-items {
  max-height: 1500px;
  position: fixed;
  left: 220px;
  margin-top: calc(-40px - var(--sidebar-scroll-y));
}

:not(.open).nav-dropdown.is-over {
  &:after {
    @extend .active-indicator;
    @extend .active-indicator-dark;
  }
}

.active-indicator {
  right: 0;
  border: solid 8px transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-right-color: #f1f1f1;
  top: 12px;
}

.active-indicator-dark {
  border-right-color: $secondary-color-light;
}

.ps__rail-y {
  display: none !important;
}

.im-sidebar {
  transition:
    margin-left 0.25s,
    margin-right 0.25s,
    width 0.25s,
    flex 0.25s;
  font-size: 1rem;
  font-weight: 500;
  background: var(--sidebar-bg, #2f353a);
  color: var(--text-secondary, #fff);
  top: 0;
  height: 100%;
  position: sticky;
  width: 240px;

  a {
    color: var(--text-secondary, #fff);
    text-decoration: none;
  }

  @extend .immy-scrollbar;
}

.im-sidebar-collapser {
  padding: 1rem;
  font-size: 1rem;
  height: 100%;
  margin: 0 auto;
  color: var(--text-primary, #fff);
  cursor: pointer;

  &:hover {
    background-color: var(--bg-default3, #3a4248);
  }
}

.im-nav-icon {
  margin-right: 0.75rem;
}

.im-sidebar-nav {
  margin: 0;
  padding: 0;
  padding-top: 1rem;
  flex-wrap: nowrap;
  overflow-y: auto;
}

.im-sidebar-nav-item {
  border-radius: 5px;
  list-style: none;
  cursor: pointer;
  margin-left: 1rem;
  margin-right: 1rem;
  margin-bottom: 0.25rem;

  a {
    display: block;
    width: 100%;
    padding: 0.5rem;
  }
}

.im-sidebar-nav-item {
  &:hover {
    background: var(--sidebar-item-hover, #24282c);

    a {
      color: var(--text-primary);
    }
  }

  &.router-link-exact-active {
    background: var(--sidebar-active-bg, #20a8d8);
    font-weight: 700;

    a {
      color: var(--text-primary);
    }

    .im-nav-icon {
      font-weight: 700;
    }
  }
}

.dropdown-active {
  background: var(--sidebar-item-hover, #20a8d8);
  color: var(--text-primary);
  font-weight: 700;

  .im-nav-icon {
    font-weight: 700;
  }

  &:hover {
    background: var(--sidebar-item-hover, #20a8d8);
  }
}

.im-sidebar-nav-dropdown-item {
  font-size: 0.875rem;
  margin-left: 3rem;

  &.router-link-exact-active {
    background: none;

    a {
      color: var(--text-primary, #20a8d8);
    }
  }
}

.im-sidebar-bottom {
  font-size: 0.75rem;
  padding: 1.5rem;
  padding-top: 1rem;

  .im-company {
    color: var(--text-primary);
    font-weight: 700;
  }
}

.im-sidebar-notifications {
  margin-bottom: 1.5rem;

  & > *:not(:first-child) {
    margin-top: 0.5rem;
  }
}

@media (max-width: 1199.98px) {
  .im-installer-section {
    height: 100%;
  }

  .im-footer {
    height: 100%;
  }
}

@media (min-width: 1199.98px) {
  .im-sidebar {
    margin-left: 0px;
  }
}

.im-sidebar-show {
  .im-sidebar {
    margin-left: 0px;
  }
}

.im-sidebar-hide {
  .im-sidebar {
    margin-left: -240px;
  }
}
