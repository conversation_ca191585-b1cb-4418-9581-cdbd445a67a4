.dropdown.v-select li {
  border-bottom: 1px solid rgba(112, 128, 144, 0.1);
}

.v-select .dropdown-menu > .highlight > a {
  background: #20a8d8;
  color: #fff;
}

.v-select {
  background: $primary-color;

  &.vs--open {
    border: 1px solid var(--primary) !important;
  }
}

.vs__dropdown-toggle {
  border: 1px solid #e4e7ea;
  height: 35px;
  border-radius: 0px;
}

.dropdown.v-select li:last-child {
  border-bottom: none;
}

.dropdown.v-select li:first-child {
  border-top: 1px solid rgba(112, 128, 144, 0.1);
}

.v-select .dropdown-toggle {
  border: 1px solid #e4e7ea !important;
}

.v-select input::placeholder {
  color: #73818f;
}

.v-select .vs__selected-options {
  overflow: hidden;
}

.v-select .vs__selected {
  white-space: nowrap;
}

.v-select {
  @extend .max-width-xxl;
}
