.navbar {
  background-color: var(--bg-header, $secondary-color);
  color: var(--text-primary);
  height: $header-height;
  > a {
    &:hover {
      background-color: $secondary-color-light;
    }
  }
  .navbar-brand {
    @extend .nav-hover-transition;
    padding-top: 0;
    padding-bottom: 0;
    height: $header-height;
    color: #fff;
  }
  .dropdown-item {
    &:hover {
      background-color: #f0f3f5;
    }
  }
}

.header-right-nav {
  .nav-item {
    border-left: 1px solid var(--divider, none);
    font-weight: 300;
  }
}

.nav-tabs {
  border-bottom: 1px solid var(--divider, #c8ded3);
  a {
    color: grey;
  }
}
.navbar-nav {
  height: 100%;
  .nav-item {
    display: flex;
    height: 100%;
    > a {
      display: flex;
      align-items: center;
      &:hover {
        background-color: $secondary-color-light;
      }
    }
  }
}

.navbar-toggler {
  border: none;
  height: 100%;
  border-radius: 0;
  padding: 1rem;
  color: var(--text-primary, #fff) !important;
  &:hover {
    background-color: var(--bg-default3, $secondary-color-light);
    color: var(--text-primary, $primary-focus-color);
  }
}

.navbar-toggler-icon {
  &:hover {
    color: $primary-focus-color;
  }
}

.navbar-nav .nav-link {
  color: $primary-color;
  &:hover {
    color: $primary-focus-color;
  }
  &:focus {
    color: $primary-color;
  }
}

.breadcrumb {
  background-color: inherit;
  border: none;
  margin: 0;
  padding: 0;
}

.nav-dropdown-items .nav-link {
  padding-left: 1.5rem;
}

.nav-dropdown-items {
  background-color: $secondary-color-light;
}

.app-header .navbar-toggler {
  &:focus {
    outline: none;
  }
}

.app-header {
  .nav-item {
    > a {
      @extend .nav-hover-transition;
    }
  }
}

.nav-hover-transition {
  transition:
    background-color 100ms linear,
    color 100ms linear,
    border 100ms linear;
  &:hover {
    color: #fff;
    background-color: $secondary-color-light;
  }
}

.account-dropdown {
  .dropdown-item {
    &.active {
      border-radius: 0px;
    }
  }
}

.header-nav-dropdown-item .nav-link {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
