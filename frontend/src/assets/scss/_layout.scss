.app {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 0;
  display: flex;
  flex-direction: column;
}

.app-header {
  position: relative;
  z-index: 5;
  flex: 0 0 auto;
}

.app-body {
  display: flex;
  flex-flow: row nowrap;
  align-items: stretch;
  position: relative;
  flex: 1 1 auto;
  min-height: 0;
  height: 100%;
  overflow-y: auto;
}

.app-body {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  overflow-x: hidden;
}

.app-header {
  z-index: 10;
}

main {
  flex: 1;
  min-width: 0;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  background: #f1f1f1;
}

.app-header {
  border-bottom: 1px solid var(--divider, #2f353a);
}

.header-fixed .app-body {
  overflow-x: visible;
}

.light-theme .tab-pane {
  border-left: 1px solid var(--divider);
  border-right: 1px solid var(--divider);
  border-bottom: 1px solid var(--divider);
}

.light-theme .i-tab-header {
  border-left: 1px solid var(--divider);
  border-right: 1px solid var(--divider);
}

.dx-action-table {
  .logs-panel {
    margin-bottom: inherit;
    padding: inherit;
    .panel-heading {
      border-top-left-radius: 0px !important;
      border-top-right-radius: 0px !important;
    }
  }
}

.account-dropdown .dropdown-item {
  min-width: unset;
  overflow: hidden;
}

.account-dropdown .dropdown-menu {
  overflow: hidden;
  width: 300px;
}

#media-modal {
  z-index: 100001 !important;
  .modal-body {
    overflow-y: auto;
    max-height: 90vh;
  }
  .modal-xl {
    height: 90vh;
    max-height: 90vh;
    width: 90vw;
    max-width: 90vw;
  }
  .modal-content {
    height: 95%;
  }
  .modal-footer {
    padding: 10px;
  }
}

#script-form-modal {
  z-index: 100001 !important;
  .modal-body {
    overflow-y: auto;
    max-height: 90vh;
  }
  .modal-xl {
    height: 90vh;
    max-height: 90vh;
    width: 90vw;
    max-width: 90vw;
  }
  .modal-content {
    height: 95%;
  }
  .modal-footer {
    padding: 10px;
  }
}

.dropdown-divider {
  border-top-color: var(--divider, #e4e7ea);
}

.secondary-detail-items {
  font-size: 0.75rem;
  font-weight: 400;
  color: var(--text-secondary);

  > * {
    &:not(:last-child) {
      &:after {
        content: "\2022";
        padding: 0 8px;
      }
    }
  }
}

.primary-detail-items {
  font-size: 0.75rem;
  font-weight: 400;
  color: var(--text-primary);

  > * {
    &:not(:last-child) {
      &:after {
        content: "\2022";
        padding: 0 8px;
      }
    }
  }
}

.nav-tabs {
  flex-wrap: nowrap;
  @extend .immy-scrollbar;
  overflow-y: hidden !important;
}

.session-info-panel {
  position: sticky;
  top: calc(1rem + var(--navbar-height));
}

.dev-instance {
  .session-info-panel {
    position: sticky;
    top: calc(1rem + var(--navbar-height) + var(--dev-banner-height));
  }
}

@media (max-width: $breakpoint-lg-max) {
  .dev-instance .session-info-panel,
  .session-info-panel {
    position: relative;
    top: inherit;
  }
}

.table-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

body {
  overflow: hidden;
}

#request-immy-support-modal___BV_modal_outer_ {
  z-index: var(--request-immy-support-modal-z-index) !important;
}

.integration-logo {
  max-width: 56px;
  max-height: 56px;
  font-size: 56px;

  &.img-svg {
    width: 56px;
  }
}

:focus-visible {
  outline: 1px solid var(--primary);
}
