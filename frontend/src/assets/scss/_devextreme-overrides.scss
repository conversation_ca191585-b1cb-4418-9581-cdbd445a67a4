.dx-dropdowneditor-field-template-wrapper > .dx-template-wrapper {
  width: 100%;
}

.dx-toolbar {
  background-color: transparent;
  padding-bottom: 0.5rem;
}

.dx-datagrid {
  background-color: transparent;
}

.dx-datagrid-headers {
  background: #fff;
}

.dx-datagrid-rowsview {
  background: #fff;
}

.dx-datagrid-header-panel .dx-toolbar {
  margin-bottom: 0;
}

.dx-datagrid-content .dx-datagrid-table .dx-row > td {
  vertical-align: middle;
}

.dx-datagrid-rowsview .dx-row > td,
.dx-datagrid-rowsview .dx-row > tr > td {
  overflow: hidden;
  text-overflow: ellipsis;
}

.dx-action-table {
  .dx-datagrid-rowsview .dx-row > .dx-master-detail-cell {
    padding: 0px;
  }
}

.dx-item.dx-list-item {
  display: flex;
  align-items: center;
}

.dx-item-content.dx-list-item-content {
  order: 1;
}

.dx-list-item-after-bag.dx-list-reorder-handle-container {
  order: 0;
  padding-top: 5px;
}

.dx-datagrid-group-closed,
.dx-datagrid-group-opened {
  font: 25px/25px DXIcons;
}

.dx-texteditor-input {
  border-radius: 0;
}

.dx-tag-container {
  .dx-texteditor-input {
    background-color: var(--bg-default) !important;
  }
}

.dx-texteditor.dx-editor-outlined {
  border-radius: 0;
}

.dx-toolbar-after .dx-toolbar-item,
.dx-toolbar-after .dx-toolbar-item:last-child {
  padding: 0 0 0 0.5rem;
}

.dx-toolbar-before {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.dx-toolbar-after {
  display: flex;
  align-items: center;
  overflow: auto;
  @extend .immy-scrollbar;
  overflow-y: hidden;
  position: inherit;
}

.dx-toolbar .dx-toolbar-items-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
  gap: 0.5rem;
}

.dx-header-filter-menu {
  .dx-toolbar-items-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dx-toolbar-after {
    padding-left: 0px !important;
  }
}

.dx-list:not(.dx-list-select-decorator-enabled) .dx-list-item.dx-state-focused,
.dx-list:not(.dx-list-select-decorator-enabled) .dx-list-item.dx-list-item-selected {
  background-color: var(--bg-default3);
  color: var(--text-primary);
}

.dx-datagrid-rowsview .dx-row.dx-group-row:not(.dx-row-focused) {
  background-color: var(--bg-default);
  color: var(--text-primary);
}

.dx-datagrid-rowsview .dx-row.dx-group-row td {
  border-bottom-color: var(--divider);
  border-top-color: var(--divider);
}

.dx-context-menu .dx-submenu {
  background-color: var(--bg-default);
  color: var(--text-primary);
  border: 1px solid var(--divider);
}

.dx-overlay-wrapper {
  color: var(--text-primary);
}

.dx-popup-title {
  color: var(--text-primary);
}

.dx-context-menu-container-border {
  //background-color: var(--bg-default);
  color: var(--text-primary);
  border: 1px solid var(--divider);
}

.dx-datagrid .dx-icon-filter-operation-default::before,
.dx-datagrid-container .dx-icon-filter-operation-default::before {
  color: var(--icon-default);
}

.dx-datagrid-filter-row .dx-menu-item-has-submenu.dx-menu-item-expanded.dx-state-hover {
  background-color: var(--bg-default);
  color: var(--text-primary);
}

.dx-list .dx-empty-message,
.dx-list-item {
  border-top: 1px solid var(--divider);
}

.dx-datagrid-column-chooser {
  color: var(--text-primary);

  .dx-popup-title {
    color: var(--text-primary);
  }

  .dx-button-mode-text .dx-icon {
    color: var(--text-primary);
  }

  .dx-treeview-item-with-checkbox .dx-treeview-item {
    color: var(--text-primary);
  }
}

.dx-texteditor-input {
  max-width: 100%;
}

.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused) > td,
.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused) > tr > td,
.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):hover > td,
.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):hover > tr > td {
  background-color: var(--bg-default3);
  color: inherit;
}

.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused) > td,
.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused) > tr > td,
.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):hover > td,
.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):hover > tr > td {
  border-bottom-color: var(--divider) !important;
}

.dx-checkbox-icon {
  background-color: var(--bg-default);
  border: 1px solid var(--divider);
  border-radius: 0.25rem;
}

.dx-checkbox-checked .dx-checkbox-icon {
  color: var(--radio-color);
  background: var(--radio-checked-bg);
}

.dx-checkbox.dx-state-hover .dx-checkbox-icon {
  border: 1px solid var(--radio-checked-border-color);
}

.dx-checkbox.dx-state-focused .dx-checkbox-icon {
  border: 1px solid var(--radio-checked-border-color);
}

.dx-datagrid-focus-overlay {
  border: 2px solid var(--primary);
}

.dx-fileuploader-input-label {
  color: var(--text-secondary);
}

.dx-loadpanel-content {
  background: var(--bg-default);
  border: 1px solid var(--divider);
  color: var(--text-primary);
}

.dx-widget {
  color: var(--text-primary);
}

.dx-treeview-item-with-checkbox .dx-treeview-item {
  color: var(--text-primary);
}

.dx-treeview-toggle-item-visibility {
  color: var(--text-primary);
}

.dx-treeview-select-all-item {
  border-bottom: 1px solid var(--divider);
}

.dx-treeview-item-with-checkbox.dx-state-focused > .dx-treeview-item .dx-checkbox .dx-checkbox-icon {
  border: 1px solid var(--primary);
}

.dx-treeview-item-with-checkbox.dx-state-focused > .dx-checkbox .dx-checkbox-icon {
  border: 1px solid var(--radio-color);
}

.dx-checkbox-indeterminate .dx-checkbox-icon {
  color: var(--radio-color);
}

.dx-checkbox-indeterminate .dx-checkbox-icon::before {
  background-color: var(--radio-checked-bg);
}

.dx-calendar {
  background: var(--bg-default);
}

.dx-pager .dx-page-sizes .dx-selection,
.dx-pager .dx-pages .dx-selection {
  color: var(--text-primary);
  background-color: var(--bg-default3);
}

.pagination-filter {
  border-radius: 0 !important;
}

// dev extreme
.dx-datagrid {
  color: var(--text-primary);
}

.dx-datagrid .dx-row > td {
  padding: 0.75rem 1rem;
}

.input-group-append {
  z-index: 1;
}

.dx-list {
  background: var(--bg-default);
}

.dx-list-item {
  color: var(--text-primary);
}

.dx-button-mode-contained {
  background-color: var(--bg-default2);
  color: var(--text-primary);
  border-color: var(--divider);
}

.dx-button-mode-contained .dx-icon {
  color: var(--text-primary);
}

.dx-datagrid-headers {
  color: var(--text-primary);
  background: var(--bg-default2);
  border-color: var(--divider);
}

.dx-datagrid-headers .dx-datagrid-table .dx-row > td {
  border-bottom: 1px solid var(--divider);
}

.dx-datagrid .dx-column-lines > td {
  border-left: none;
  border-right: none;
}

.dx-editor-cell .dx-texteditor,
.dx-editor-cell .dx-texteditor .dx-texteditor-input {
  background: var(--bg-input);
  color: var(--input);
}

.dx-texteditor.dx-editor-outlined {
  border-color: var(--divider);
  background: none;
}

.dx-editor-with-menu {
  background: transparent;
}

.dx-menu-item {
  background: none;
  color: var(--input);
}

.dx-datagrid-content {
  background: var(--bg-default);
}

.dx-datagrid .dx-row-lines > td {
  border-bottom-color: var(--divider);
}

.dx-datagrid-borders .dx-datagrid-rowsview,
.dx-datagrid-headers + .dx-datagrid-rowsview {
  border-top-color: var(--divider);
}

.dx-datagrid-borders > .dx-datagrid-rowsview,
.dx-datagrid-borders > .dx-datagrid-total-footer {
  border-bottom-color: var(--divider);
}

.dx-datagrid-borders > .dx-datagrid-headers,
.dx-datagrid-borders > .dx-datagrid-rowsview,
.dx-datagrid-borders > .dx-datagrid-total-footer,
.dx-bordered-bottom-view.dx-datagrid-filter-panel {
  border-right-color: var(--divider);
  border-left-color: var(--divider);
}

.dx-datagrid-borders > .dx-datagrid-filter-panel,
.dx-datagrid-borders > .dx-datagrid-headers,
.dx-datagrid-borders > .dx-datagrid-pager {
  border-top-color: var(--divider);
}

.dx-dropdowneditor-button.dx-state-active .dx-dropdowneditor-icon,
.dx-dropdowneditor.dx-dropdowneditor-active .dx-dropdowneditor-icon {
  background-color: var(--bg-default2);
  color: var(--text-primary);
}

.dx-dropdownbutton-action {
  background-color: var(--bg-default2);
  color: var(--text-primary);
  border-color: var(--divider);
  border-radius: 0;
}

.dx-button-mode-contained.dx-buttongroup-last-item.dx-button,
.dx-button-mode-outlined.dx-buttongroup-last-item.dx-button,
.dx-button-mode-contained.dx-buttongroup-first-item.dx-button {
  border-radius: 0;
}

.dx-dropdowneditor-button,
.dx-dropdowneditor-icon {
  border-radius: 0;
  width: 34px;
}

.dx-dropdowneditor-icon {
  color: var(--text-primary);
}

.dx-dropdowneditor-button {
  padding: 0;
  color: var(--text-primary) !important;
  background: var(--bg-default2);
  background-color: var(--bg-default2) !important;
}

.dx-button-mode-contained.dx-state-hover,
.dx-button-mode-contained.dx-state-focused {
  background-color: var(--btn-secondary-hover-bg);
  border-color: var(--divider);
}

.dx-datagrid-export-button {
  border-radius: 0 !important;
  font-family: "Lato", sans-serif !important;
  font-size: 0.875rem !important;
}

.dx-icon-export-excel-button {
  width: 100% !important;
  height: 100% !important;
  font: inherit !important;
  font-weight: inherit !important;
  border-radius: 0 !important;
  margin: 0 !important;

  &:before {
    content: "Export to Excel" !important;
  }
}

.dx-datagrid .dx-header-filter:not(.dx-header-filter-empty) {
  color: var(--primary);
}

.dx-selectbox-popup-wrapper .dx-list {
  background-color: var(--bg-default);
}

.dx-texteditor.dx-state-active.dx-editor-filled,
.dx-texteditor.dx-state-active.dx-editor-outlined,
.dx-texteditor.dx-state-active.dx-editor-underlined,
.dx-texteditor.dx-state-focused.dx-editor-filled,
.dx-texteditor.dx-state-focused.dx-editor-outlined,
.dx-texteditor.dx-state-focused.dx-editor-underlined {
  border-color: var(--primary);
}

.dx-menu .dx-menu-item-expanded {
  background-color: var(--bg-default2);
}

.dx-datagrid-headers .dx-datagrid-table .dx-row > td:not(:last-child) {
  border-right: 1px solid var(--divider);
}

.dx-datagrid-drag-header {
  background-color: var(--bg-default);
  border-color: var(--divider);
}

.dx-datagrid-group-panel .dx-group-panel-item {
  border: 1px solid var(--divider);
}

.dx-popup-wrapper > .dx-overlay-content {
  background-color: var(--bg-default);
  border: var(--divider);
}

.dx-popup-wrapper .dx-state-focused.dx-overlay-content {
  border: var(--divider);
}

.dx-dropdowneditor.dx-state-active .dx-dropdowneditor-icon,
.dx-dropdowneditor.dx-state-hover:not(.dx-custom-button-hovered) .dx-dropdowneditor-icon {
  color: var(--text-primary) !important;
  background: var(--bg-default2);
  background-color: var(--bg-default2) !important;
}

.dx-datagrid-rowsview .dx-master-detail-row:not(.dx-datagrid-edit-form) .dx-master-detail-cell,
.dx-datagrid-rowsview .dx-master-detail-row:not(.dx-datagrid-edit-form) > .dx-datagrid-group-space {
  background-color: var(--bg-default2);
}

.dx-datagrid-rowsview .dx-master-detail-row .dx-master-detail-cell,
.dx-datagrid-rowsview .dx-master-detail-row > .dx-datagrid-group-space {
  border-bottom-color: var(--divider);
  border-top-color: var(--divider);
}

.dx-calendar-cell {
  color: var(--text-primary);

  &.dx-state-hover span {
    background: var(--btn-outline-primary-hover-bg);
  }

  &.dx-state-active span {
    background: var(--btn-outline-primary-active-bg);
    color: var(--btn-outline-primary-active-color);
  }
}

.dx-calendar-body thead th {
  color: var(--text-primary);
}

.dx-calendar-cell.dx-calendar-empty-cell,
.dx-calendar-cell.dx-calendar-empty-cell.dx-state-active span,
.dx-calendar-cell.dx-calendar-empty-cell.dx-state-hover span,
.dx-calendar-cell.dx-calendar-other-view,
.dx-calendar-cell.dx-calendar-other-view.dx-state-active span,
.dx-calendar-cell.dx-calendar-other-view.dx-state-hover span {
  color: var(--text-disabled);
}

.dx-calendar-cell.dx-calendar-contoured-date span {
  box-shadow: 0 0 0 2px var(--primary);
}

.dx-calendar-cell.dx-calendar-selected-date span {
  color: var(--btn-primary-color);
  background-color: var(--btn-primary-bg);
}

.dx-button-mode-outlined {
  border-color: var(--divider);

  &.dx-state-hover {
    border-color: var(--divider);
    background-color: var(--bg-default2);
  }

  &.dx-state-focused {
    background-color: var(--bg-default2);
  }
}

.dx-datagrid-filter-panel {
  background-color: var(--bg-default);
}

.dx-datagrid-filter-panel-left .dx-datagrid-filter-panel-text,
.dx-datagrid-filter-panel-left .dx-icon-filter {
  color: var(--text-default);
}

.dx-datagrid-filter-panel .dx-datagrid-filter-panel-clear-filter {
  color: var(--primary);
}

.dx-datagrid-filter-panel-left .dx-datagrid-filter-panel-text,
.dx-datagrid-filter-panel-left .dx-icon-filter {
  pointer-events: none;
}

.dx-list:not(.dx-list-select-decorator-enabled) .dx-list-item.dx-state-hover {
  color: var(--text-primary) !important;
  background-color: var(--bg-default2) !important;
}

.dx-button-mode-outlined .dx-icon {
  color: var(--text-primary);
}

.dx-button-mode-contained.dx-state-active {
  background-color: var(--btn-secondary-active-bg);
  color: var(--text-primary);
}

// treeview

.dx-treeview-item.dx-state-hover {
  background-color: var(--bg-default3);
  color: var(--text-primary);
}

.dx-treeview-item-without-checkbox.dx-state-focused > .dx-treeview-item {
  background-color: var(--btn-primary-active-bg);
  color: var(--btn-primary-active-color);
}

.dx-treeview-item-without-checkbox.dx-state-selected > .dx-treeview-item {
  background-color: var(--btn-primary-bg);
  color: var(--btn-primary-active-color);
}

.dx-datagrid-nodata {
  color: var(--text-primary);
}

.dx-button {
  border-radius: 0;
}

.dx-toolbar .dx-toolbar-item {
  padding-inline-end: 0;
}

.dx-datagrid-table
  .dx-data-row.dx-state-hover:not(.dx-selection):not(.dx-row-inserted):not(.dx-row-removed):not(.dx-edit-row):not(
    .dx-row-focused
  )
  > td:not(.dx-focused) {
  background-color: var(--bg-default2);
  color: var(--text-primary);
}

// tagbox

.dx-tagbox.dx-state-focused > .dx-texteditor-label {
  color: var(--text-primary);
}

.dx-tagbox.dx-state-hover > .dx-texteditor-label {
  color: var(--text-primary);
}

.dx-tagbox .dx-tag-content {
  background-color: var(--bg-default2);
  color: var(--text-default);
  border-radius: 0;
}

.dx-editor-outlined.dx-texteditor-with-floating-label {
  & .dx-label,
  .dx-label-before,
  .dx-label-after {
    border-radius: 0;
    border-color: var(--divider);
  }
}

.dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover {
  & .dx-label,
  .dx-label-before,
  .dx-label-after {
    border-color: var(--btn-outline-primary-hover-bg);
  }
}

.dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused {
  & .dx-label,
  .dx-label-before,
  .dx-label-after {
    border-color: var(--btn-outline-primary-hover-bg);
  }
}

.dx-list.dx-list-select-decorator-enabled .dx-list-item.dx-state-hover .dx-checkbox-icon,
.dx-list.dx-list-select-decorator-enabled .dx-list-item.dx-state-focused .dx-checkbox-icon {
  border: 1px solid var(--radio-checked-border-color);
}

.dx-datagrid {
  .dx-scrollable-scrollbars-alwaysvisible {
    background-color: var(--bg-default);
  }
}

.dx-datagrid-bottom-load-panel {
  border-top: 1px solid var(--divider);
  background-color: var(--bg-default);
}
