// overide vue-select border when it has the .invalid class
.v-select.invalid .vs__dropdown-toggle {
  border: none;
}

// add red dot to all required form labels and legends
.form-group.required {
  > label:after,
  > legend:after {
    content: "";
    @extend .red-dot;
  }
}

// add red dot to all labels that have the .required class
label.required,
.form-group label.required {
  &:after {
    content: "";
    font-weight: bold;
    @extend .red-dot;
  }
}

// For non-bootstrap form fields, use this class to show error feedback
.invalid-feedback-message {
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: var(--danger, #fa5849);
}

.invalid-feedback {
  color: var(--danger, #fa5849);
}

.was-validated .form-control:valid,
.form-control.is-valid {
  border-color: var(--success, #4dbd74) !important;
}

// For non-bootstrap form fields, use this class to add a red border around the input
.invalid {
  border: 1px solid var(--danger);
  border-radius: 0px;
  .dropdown-toggle {
    border: none !important;
  }
}

// red dot to indicate required
.red-dot {
  margin-left: 5px;
  display: inline-block;
  background: var(--danger);
  opacity: 0.6;
  width: 5px;
  height: 5px;
  border-radius: 5px;
  text-indent: -9999em;
  vertical-align: super;
}
