.b-sidebar-outer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 0;
  overflow: visible;
  z-index: calc(1030 + 5);
}

.b-sidebar-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  opacity: 0.6;
}

.b-sidebar {
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  width: 320px;
  max-width: 100%;
  height: 100vh;
  max-height: 100%;
  margin: 0;
  outline: 0;
  -webkit-transform: translateX(0);
  transform: translateX(0);
}

.b-sidebar.slide {
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition:
    transform 0.3s ease-in-out,
    -webkit-transform 0.3s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .b-sidebar.slide {
    transition: none;
  }
}

.b-sidebar:not(.b-sidebar-right) {
  left: 0;
  right: auto;
}

.b-sidebar:not(.b-sidebar-right).slide:not(.show) {
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
}

.b-sidebar:not(.b-sidebar-right) > .b-sidebar-header .close {
  margin-left: auto;
}

.b-sidebar.b-sidebar-right {
  left: auto;
  right: 0;
}

.b-sidebar.b-sidebar-right.slide:not(.show) {
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
}

.b-sidebar.b-sidebar-right > .b-sidebar-header .close {
  margin-right: auto;
}

.b-sidebar > .b-sidebar-header {
  font-size: 1.5rem;
  padding: 0.5rem 1rem;
  display: flex;
  flex-direction: row;
  flex-grow: 0;
  align-items: center;
}

[dir="rtl"] .b-sidebar > .b-sidebar-header {
  flex-direction: row-reverse;
}

.b-sidebar > .b-sidebar-header .close {
  float: none;
  font-size: 1.5rem;
}

.b-sidebar > .b-sidebar-body {
  flex-grow: 1;
  height: 100%;
  overflow-y: auto;
}

.b-sidebar > .b-sidebar-footer {
  flex-grow: 0;
}

.modal-backdrop {
  opacity: 0.5;
}

.toast-success {
  background-color: #51a351 !important;
}

.toast-error {
  background-color: #bd362f !important;
}

// bootstrap-vue class that conflicts with toastr's class
.toast {
  background-clip: inherit !important;
  border: none !important;
  font-size: inherit !important;
}

.card {
  border-radius: 0px;
}

.form-control {
  border-radius: 0;
}

.custom-select {
  border-radius: 0;
}

.custom-control-label {
  .badge {
    padding: 0.25em 0.4em;
  }
}

.modal {
  .nav-link:not(.disabled) {
    color: var(--text-primary);
  }

  .nav-link.active {
    background-color: var(--bg-default3) !important;
    color: var(--text-primary) !important;
  }
}

.alert {
  border: 1px solid;
  border-radius: 0;
}

.page-item.active {
  .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
    color: var(--btn-primary-color);
  }
}

.page-item.disabled .page-link {
  background-color: var(--bg-input);
  color: var(--input);
  border-color: var(--divider);
}

.page-link {
  padding: 0.35rem 0.75rem;
  line-height: 1.5;
  z-index: 1 !important;
  background-color: var(--bg-input);
  color: var(--input);
  border-color: var(--divider);

  &:hover {
    background-color: var(--primary);
    border-color: var(--primary);
    color: var(--btn-primary-color);
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem var(--btn-primary-focus-shadow-rgb);
  }
}

.page-item:first-child .page-link {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.page-item:last-child .page-link {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.popover {
  background-color: var(--bg-default2);
  border-radius: 0;
  border-color: var(--divider);
  transition: none;
}

.popover > .arrow:before,
.popover > .arrow:after {
  font-family: "Lato", sans-serif;
  border: none;
}

.popover-body {
  color: var(--text-primary);
}

#migrate-modal___BV_modal_outer_,
#auth-token-acquisition-modal___BV_modal_outer_ {
  z-index: 1060 !important;
}

.b-sidebar {
  top: calc(var(--navbar-height) + 1px);
  height: calc(100vh - var(--navbar-height));
}

.development {
  .b-sidebar {
    top: calc(var(--navbar-height) + var(--dev-banner-height) + 1px);
    height: calc(100vh - var(--navbar-height) - var(--dev-banner-height) - 1px);
  }
}

.development.in-trial {
  .b-sidebar {
    top: calc(var(--navbar-height) + var(--dev-banner-height) + var(--trial-banner-height) + 1px);
    height: calc(100vh - var(--navbar-height) - var(--dev-banner-height) - var(--trial-banner-height) - 1px);
  }
}

.in-trial {
  .b-sidebar {
    top: calc(var(--navbar-height) + var(--trial-banner-height) + 1px);
    height: calc(100vh - var(--navbar-height) - var(--trial-banner-height) - 1px);
  }
}

/* box-shadow for SweetAlert2 buttons */
.swal2-styled.swal2-confirm,
.swal2-styled.swal2-cancel,
.swal2-styled.swal2-deny {
  box-shadow: var(--btn-primary-box-shadow) !important;
}

.dark-theme .form-control.is-invalid {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23fa759f' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23fa759f' stroke='none'/%3e%3c/svg%3e");
}

.light-theme .form-control.is-invalid {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23ec1344' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23ec1344' stroke='none'/%3e%3c/svg%3e");
}
