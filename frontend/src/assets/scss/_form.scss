.btn-group {
  @include media-breakpoint-down(md) {
    display: block;
  }
}

small,
.small {
  .btn-link {
    vertical-align: baseline;
    border-width: 0;
    font-size: inherit;
    line-height: initial;
    padding: 0;
  }
}

small {
  font-size: 75%;
}

.btn:not(:disabled):not(.disabled) {
  cursor: pointer;
}

input,
textarea,
select {
  @extend .max-width-xxl;
}

.search-svg {
  filter: var(--icon-filter);
}

.form-group {
  small {
    font-size: 0.75rem;
  }
}

.cta-button {
  font-family: "Roboto Mono", monospace;
  text-transform: uppercase;
  line-height: 0.75rem;
  letter-spacing: 1px;
  font-size: 0.625rem;
}

.check-list-button {
  margin-top: 32px;
  padding: 12px 24px;
  color: black;
  font-family: "Roboto Mono";
  font-size: 12px;
  font-weight: 700;
  line-height: 14px;
  letter-spacing: 0.6px;
  text-transform: uppercase;
  background: var(--check-list-action-button-bg-color);
  border: none;

  &.documentation {
    border: 0.5px solid var(--check-list-doc-border-color);
    background: var(--check-list-doc-button-bg-color);
    color: var(--check-list-doc-button-color);
  }

  &:first-of-type {
    margin-right: 32px;
  }
}
