// custom theme settings for light/dark
$variants: primary, secondary, success, danger, warning, running;

.themed {
  .text-muted {
    color: var(--text-secondary) !important;
  }

  pre {
    color: var(--text-secondary);
  }

  input::placeholder {
    color: var(--text-secondary);
  }

  .text-link {
    color: var(--btn-link-color) !important;
  }

  #preload-container {
    background: var(--bg-main);
  }
  hr {
    background-color: var(--divider);
  }
  color: var(--text-primary);
  background: var(--bg-surface);

  .app-body {
    background-color: var(--bg-main);
  }

  main {
    background-color: var(--bg-main);
    padding-left: 1.5rem;
  }

  .i-tab {
    .nav-item {
      a {
        background: var(--bg-default2);
      }
    }
  }
  .i-tab-header {
    background: var(--bg-default2);
  }

  .i-tab-content {
    background: var(--bg-default);
  }

  .btn-link {
    color: var(--btn-link-color);
  }

  .app {
    color: var(--text-primary);
    background: var(--bg-main);
  }

  a {
    color: var(--link);
  }

  a:disabled,
  a.disabled {
    opacity: 0.6;
  }

  .text-icon {
    color: var(--icon-default);
  }

  .btn:focus {
    box-shadow: none;
  }

  @each $variant in $variants {
    .text-#{$variant} {
      color: var(--#{$variant}) !important;
      &:hover {
        color: var(--#{$variant}) !important;
      }
    }

    .toast-#{$variant} {
      color: var(--btn-#{$variant}-color) !important;
      background-color: var(--btn-#{$variant}-bg) !important;
      box-shadow: none !important;
    }

    .bg-#{$variant} {
      background-color: var(--bg-#{$variant}) !important;
    }

    .border-#{$variant} {
      border-color: var(--btn-#{$variant}-border-color) !important;
    }

    .bg-#{$variant}-light {
      background: var(--bg-#{$variant}) !important;
    }

    .badge-#{$variant} {
      color: var(--badge-#{$variant}-color);
      background: var(--badge-#{$variant}-bg);
      border-color: none;
    }

    .progress-bar-#{$variant}-halfway {
      color: var(--btn-#{$variant}-color);
    }

    .progress-bar-#{$variant}-empty {
      color: var(--btn-outline-#{$variant}-color);
    }

    .btn-outline-#{$variant} {
      color: var(--btn-outline-#{$variant}-color, #fff);
      background: var(--btn-outline-#{$variant}-bg, #fff);
      border-color: var(--btn-outline-#{$variant}-border-color);
      box-shadow: var(--btn-outline-#{$variant}-box-shadow, none);
      &:hover {
        color: var(--btn-outline-#{$variant}-hover-color);
        background-color: var(--btn-outline-#{$variant}-hover-bg);
        border-color: var(--btn-outline-#{$variant}-hover-border-color);
      }

      &:focus {
        box-shadow: 0 0 0 0.2rem var(--btn-outline-#{$variant}-focus-shadow-rgb);
      }

      .btn-check:focus + &,
      &:focus {
        color: var(--btn-outline-#{$variant}-hover-color);
        background: var(--btn-outline-#{$variant}-hover-bg);
        border-color: var(--btn-outline-#{$variant}-hover-border-color);
      }

      .btn-check:checked + &,
      .btn-check:active + &,
      &:active,
      &.active,
      &.show {
        color: var(--btn-outline-#{$variant}-active-color);
        background-color: var(--btn-outline-#{$variant}-active-bg);
        border-color: var(--btn-outline-#{$variant}-active-border-color);
        box-shadow: 0 0 0 0.2rem var(--btn-outline-#{$variant}-active-shadow-rgb);

        &:focus {
          box-shadow: 0 0 0 0.2rem var(--btn-outline-#{$variant}-focus-shadow-rgb);
        }
      }

      &:disabled,
      &.disabled,
      fieldset:disabled & {
        color: var(--btn-outline-#{$variant}-color, #fff);
        background-color: var(--btn-outline-#{$variant}-bg, #fff);
        border-color: var(--btn-outline-#{$variant}-border-color);
        opacity: var(--btn-outline-#{$variant}-disabled-opacity);
        box-shadow: none;
        cursor: not-allowed;
      }
    }

    .btn-#{$variant} {
      color: var(--btn-#{$variant}-color);
      background: var(--btn-#{$variant}-bg);
      border-color: var(--btn-#{$variant}-border-color);
      box-shadow: var(--btn-#{$variant}-box-shadow, none);
      &:hover {
        color: var(--btn-#{$variant}-hover-color);
        background-color: var(--btn-#{$variant}-hover-bg);
        border-color: var(--btn-#{$variant}-hover-border-color);
      }

      &:focus {
        box-shadow: 0 0 0 0.2rem var(--btn-#{$variant}-focus-shadow-rgb);
      }

      .btn-check:focus + &,
      &:focus {
        color: var(--btn-#{$variant}-hover-color);
        background: var(--btn-#{$variant}-hover-bg);
        border-color: var(--btn-#{$variant}-hover-border-color);
      }

      .btn-check:checked + &,
      .btn-check:active + &,
      &:active,
      &.active,
      &.show {
        color: var(--btn-#{$variant}-active-color);
        background-color: var(--btn-#{$variant}-active-bg);
        border-color: var(--btn-#{$variant}-active-border-color);
        box-shadow: 0 0 0 0.2rem var(--btn-#{$variant}-active-shadow-rgb);

        &:focus {
          box-shadow: 0 0 0 0.2rem var(--btn-#{$variant}-focus-shadow-rgb);
        }
      }

      &:disabled,
      &.disabled,
      fieldset:disabled & {
        color: var(--btn-#{$variant}-color);
        background-color: var(--btn-#{$variant}-bg);
        border-color: var(--btn-#{$variant}-border-color);
        opacity: var(--btn-#{$variant}-disabled-opacity);
        box-shadow: none;
        cursor: not-allowed;
      }
    }

    .alert-#{$variant} {
      color: var(--alert-#{$variant}-color);
      background: var(--alert-#{$variant}-bg);
      border-color: var(--alert-#{$variant}-border-color);

      hr {
        border-top-color: var(--alert-#{$variant}-color);
      }

      .close:hover {
        color: var(--#{variant});
      }
    }

    .btn-#{$variant}:not(:disabled):not(.disabled):active:focus,
    .btn-#{$variant}:not(:disabled):not(.disabled).active:focus,
    .show > .btn-#{$variant}.dropdown-toggle:focus {
      box-shadow: 0 0 0 0.2rem var(--btn-#{$variant}-focus-shadow-rgb);
    }

    .btn-#{$variant}:not(:disabled):not(.disabled):active,
    .btn-#{$variant}:not(:disabled):not(.disabled).active,
    .show > .btn-#{$variant}.dropdown-toggle {
      color: var(--btn-#{$variant}-active-color);
      background-color: var(--btn-#{$variant}-active-bg);
      border-color: var(--btn-#{$variant}-active-border-color);
    }

    .btn-outline-#{$variant}:not(:disabled):not(.disabled):active,
    .btn-outline-#{$variant}:not(:disabled):not(.disabled).active,
    .show > .btn-outline-#{$variant}.dropdown-toggle {
      color: var(--btn-outline-#{$variant}-active-color);
      background-color: var(--btn-outline-#{$variant}-active-bg);
      border-color: var(--btn-outline-#{$variant}-active-border-color);
    }
  }

  .toast-error {
    color: var(--btn-danger-color) !important;
    background-color: var(--btn-danger-bg) !important;
    box-shadow: none !important;
  }

  .btn-group:not(.b-dropdown) {
    .btn {
      border-radius: 0 !important;
      box-shadow: none;
    }
  }

  .form-control {
    background-color: var(--bg-input);
    border: 1px solid var(--divider);
  }

  .form-control:disabled,
  .form-control[readonly] {
    background-color: var(--bg-disabled);
  }

  .form-control:focus,
  .b-form-tags.focus,
  .b-form-tags-focus {
    color: var(--text-primary);
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem var(--bg-primary);
    background-color: var(--bg-input);
  }

  .custom-select {
    color: var(--input);
    border-color: var(--divider);
  }

  .custom-select:invalid,
  .custom-select.is-invalid {
    border-color: var(--danger);
  }

  .custom-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem var(--bg-primary);
  }

  .dropdown-menu {
    font-size: 0.875rem;
    background: var(--bg-default);
    color: var(--text-primary);
    border-color: var(--divider);
    a {
      color: var(--text--primary);
    }
  }

  .dropdown-item {
    color: var(--text-primary);
  }

  .dropdown-item.text-link {
    color: var(--text-primary) !important;
  }

  .dropdown-item.active,
  .dropdown-item:active {
    background: var(--bg-default2);
    color: var(--text-primary);
  }

  .navbar .dropdown-item:hover {
    background: var(--bg-default2);
    color: var(--text-primary);
  }

  .dropdown-item:hover,
  .dropdown-item:focus {
    background: var(--bg-default2);
    color: var(--text-primary);
  }

  // navbar
  .navbar {
    .nav-link {
      color: var(--text-primary) !important;
    }
  }

  .nav-pills .nav-link.active,
  .nav-pills .show > .nav-link {
    background-color: var(--bg-default);
    color: var(--text-primary);
  }

  .nav-hover-transition:hover,
  .navbar .navbar-brand:hover,
  .app-header .nav-item > a:hover {
    color: var(--text-primary);
    background-color: var(--bg-default3);
  }

  .custom-control-label::before {
    background: var(--bg-default);
  }

  .custom-control-input:disabled ~ .custom-control-label::before {
    background-color: var(--bg-default2);
  }

  .custom-control-input:checked ~ .custom-control-label::before {
    color: var(--radio-color);
    background: var(--radio-checked-bg);
    border-color: var(--radio-checked-border-color);
  }

  .custom-checkbox .custom-control-input:checked:focus ~ .custom-control-label::before {
    box-shadow: var(--radio-checked-box-shadow);
    border-color: var(--radio-checked-border-color);
  }

  .custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
    box-shadow: var(--radio-checked-box-shadow);
    border-color: var(--radio-checked-border-color);
  }

  .custom-control-input:focus ~ .custom-control-label::before {
    box-shadow: var(--radio-checked-box-shadow);
    border-color: var(--radio-checked-border-color);
  }

  .custom-checkbox .custom-control-input:focus ~ .custom-control-label::before {
    box-shadow: var(--radio-checked-box-shadow);
    border-color: var(--radio-checked-border-color);
  }

  .custom-checkbox .custom-control-input:active ~ .custom-control-label::before {
    background-color: var(--radio-active-bg);
    border-color: var(--radio-checked-border-color);
  }

  .custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
    filter: var(--radio-checked-filter);
  }

  .custom-radio .custom-control-input:checked ~ .custom-control-label::after {
    filter: var(--radio-checked-filter);
  }

  .custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {
    color: var(--radio-color);
    background: var(--radio-checked-bg);
    border-color: var(--radio-checked-border-color);
  }

  .custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
    color: var(--radio-color);
    background: var(--radio-checked-bg);
    border-color: var(--radio-checked-border-color);
  }

  .custom-control-input[disabled] ~ .custom-control-label,
  .custom-control-input:disabled ~ .custom-control-label {
    cursor: not-allowed;
  }

  .was-validated .custom-control-input:invalid ~ .custom-control-label,
  .custom-control-input.is-invalid ~ .custom-control-label {
    color: var(--danger);
  }

  .was-validated .custom-control-input:invalid ~ .custom-control-label::before,
  .custom-control-input.is-invalid ~ .custom-control-label::before {
    border-color: var(--danger);
  }

  .tab-data {
    background: var(--bg-default);
    border-color: var(--divider);
  }

  .tab-content {
    background: var(--bg-default);
    color: var(--text-primary);
    border-color: var(--divider);
  }

  .nav-tabs {
    .nav-item {
    }
    .nav-link {
      color: var(--text-secondary);
      border-bottom: none;
      border-top-left-radius: none;
      border-top-right-radius: none;
      border-radius: 0;
      border-top: 2px solid transparent;
      &.active {
        color: var(--text-primary);
        background: var(--bg-default2);
        border-color: var(--divider);
        border-top: 2px solid var(--primary, #20a8d8);
      }
    }
  }

  .nav-tabs .nav-link:hover,
  .nav-tabs .nav-link:focus {
    border-color: var(--divider);
    border-top: 2px solid var(--primary, #20a8d8);
  }

  .list-group-item {
    background: var(--bg-default) !important;
    border-color: var(--divider);
  }

  .expand-button {
    background: var(--bg-default2);
  }

  .panel {
    background: var(--bg-default);
  }

  .panel.basic > .panel-heading {
    border-bottom: 1px solid var(--divider);
  }

  .panel:not(.basic) > .panel-heading {
    background: var(--bg-default);
    color: var(--text-primary);
    border-bottom: 1px solid var(--divider);
  }
  .panel:not(.basic) > .panel-body,
  .panel:not(.basic) > .collapse > .panel-body {
    background: var(--bg-default);
  }

  .card {
    background-color: var(--bg-default);
    border-color: var(--divider);
    color: var(--text-primary);
  }

  .card-header {
    background-color: var(--bg-default2);
    border-color: var(--divider);
    color: var(--text-primary);
  }

  .computer-session-details {
    .page-header {
      background: var(--bg-default);
    }
  }

  // logs panel

  .parent-log-item {
    background-color: var(--bg-default);
  }

  input,
  textarea {
    background-color: var(--bg-input);
    color: var(--input);
    border-color: var(--divider);
  }

  .input-group-text {
    background: var(--bg-default3);
    color: var(--text-primary);
    border-color: var(--divider);
  }

  input:checked + .slider {
    background-color: var(--primary);
  }

  .b-sidebar {
    background-color: var(--sidebar-actions-bg) !important;
    color: var(--text-primary) !important;
    .close {
      color: var(--text-primary) !important;
    }
  }

  // vue-select
  .vs__dropdown-option {
    .type {
      color: var(--text-secondary);
    }
  }
  .vs__dropdown-option--highlight {
    .option {
      .type {
        color: var(--text-secondary);
      }
    }
  }
  .vs__dropdown-menu {
    z-index: 9999;
  }
  .vs__dropdown-toggle {
    border-color: var(--divider);
  }
  .v-select {
    background: var(--bg-input);
  }
  .vs__selected {
    color: var(--input);
    background-color: var(--bg-input);
    border: none;
  }
  .vs__search {
    border: none;
    max-width: 500px;
  }
  .vs__clear {
    color: var(--input);
  }

  // modal
  .modal-content {
    background: var(--bg-default);
  }

  .table {
    background: var(--bg-default);
    color: var(--text-primary);
  }
  .table thead th {
    border-color: var(--divider);
    border-bottom: none;
  }
  .table th,
  .table td {
    border-color: var(--divider);
  }

  .page-body {
    &:not(.page-body-no-bg) {
      background-color: var(--bg-default);
      padding: 1rem;
    }
  }

  .modal-header {
    border-bottom: 1px solid var(--divider);
  }

  .modal-footer {
    border-top: 1px solid var(--divider);
  }

  // sweet alert

  .swal2-popup {
    background: var(--bg-default2, #fff);
    color: var(--text-primary, #545454);
  }

  .swal2-styled.swal2-confirm {
    background-color: var(--btn-primary-bg, #7066e0);
    color: var(--btn-primary-color, #fff);
    box-shadow: 0 0 0 3px var(--btn-primary-focus-shadow-rgb);
  }

  .swal2-styled.swal2-cancel {
    background-color: var(--btn-outline-secondary-bg, #7066e0);
    color: var(--btn-outline-secondary-color, #fff);
    border: 1px solid var(--btn-outline-secondary-border-color);
    box-shadow: 0 0 0 3px var(--btn-secondary-focus-shadow-rgb);
  }

  .swal2-styled.swal2-deny {
    background-color: var(--btn-danger-bg, #dc3741);
    color: var(--btn-danger-color, #fff);
    box-shadow: 0 0 0 3px var(--btn-danger-focus-shadow-rgb);
  }

  .swal2-warning {
    border-color: var(--warning);
    color: var(--warning);
  }
}

// custom light theme only

.light-theme {
  .custom-select {
    background: var(--bg-input)
      url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 width=%274%27 height=%275%27 viewBox=%270 0 4 5%27%3e%3cpath fill=%27%23000%27 d=%27M2 0L0 2h4zm0 5L0 3h4z%27/%3e%3c/svg%3e")
      right 0.75rem center/8px 10px no-repeat;
  }
  .nav-link.active {
    color: var(--text-primary);
  }

  .navbar {
    .navbar-brand {
      img {
        filter: none;
      }
    }
  }
}

// dark theme only

.dark-theme {
  .custom-select {
    background: var(--bg-input)
      url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 width=%274%27 height=%275%27 viewBox=%270 0 4 5%27%3e%3cpath fill=%27%23fff%27 d=%27M2 0L0 2h4zm0 5L0 3h4z%27/%3e%3c/svg%3e")
      right 0.75rem center/8px 10px no-repeat;
  }
  .nav-link.active {
    color: var(--primary);
  }

  h1 {
    font-weight: 300;
  }
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
  border: 1px solid var(--danger) !important;
}

.red-dot,
.form-group.required > label:after,
.form-group.required > legend:after,
label.required:after,
.form-group label.required:after {
  background: var(--danger);
}

button[role="tab"]:not(.disabled) {
  color: var(--text-primary);
}
