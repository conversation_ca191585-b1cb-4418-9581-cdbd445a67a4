@import url("https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=Roboto+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&display=swap");

h1 {
  font-size: 1.75rem;
  line-height: 2rem;
  font-weight: var(--h1-font-weight);
}

h2 {
  font-size: 1.5rem;
  line-height: 1.75rem;
  font-weight: var(--h2-font-weight);
}

h3 {
  font-size: 1.125rem;
  line-height: 1.375rem;
  font-weight: var(--h3-font-weight);
}

h4 {
  font-size: 1rem;
  line-height: 1.25rem;
  font-weight: var(--h4-font-weight);
}

h5 {
  font-size: 1.25rem;
  line-height: 1.5rem;
  letter-spacing: -1px;
  font-weight: var(--h5-font-weight);
}

h6 {
  font-size: 1rem;
  line-height: 1.25rem;
  letter-spacing: -0.5px;
  font-weight: var(--h6-font-weight);
}

.b-form-group {
  legend,
  label {
    font-weight: 700;
  }
}

.radio-button-group > span {
  font-weight: 700;
}

.btn {
  padding: 0.25rem 0.75rem;
  border-radius: 0;
}

body {
  font-family: "Lato", sans-serif;
  font-size: 0.875rem;
}

.page-breadcrumb {
  font-size: 0.625rem;
  font-weight: 700;
  text-transform: uppercase;
  font-family: "Roboto Mono", monospace;
  line-height: 0.75rem;
  letter-spacing: 1.5px;
}

code {
  font-family: "Roboto Mono", monospace;
}

hr {
  border-top: 1px solid var(--divider);
}
