// If you want to override variables do it here
@import "variables";
@import "mixins";
@import "utility";
@import "functions";

/* Import Bootstrap Vue Styles */
@import "~bootstrap/scss/bootstrap";

/* add themes */
@import "themes";

@import "assets/tooltip.css";

// Import styles with default layout.
// If you are going to use dark layout please comment next line

@import "~nprogress/nprogress.css";

// ie fixes
@import "ie-fix";

@import "~devextreme/dist/css/dx.common.css";
@import "~devextreme/dist/css/dx.light.css";
@import "vue-select/dist/vue-select.css";
@import "~sweetalert2/dist/sweetalert2.min.css";
@import "~@immense/fontawesome-pro/css/all.min.css";
@import "~toastr/build/toastr.min.css";

@import "bootstrap-overrides";
@import "vue-select-overrides";
@import "devextreme-overrides";

// If you want to add something do it here
@import "layout";
@import "markdown";
@import "navbar";
@import "responsive";
@import "typography";
@import "validations";
@import "form";
@import "xterm";
@import "modal";

// NEW THEMED STYLES
@import "themed/colors";
@import "badges";
@import "sidebar";

// https://www.client9.com/css-system-font-stack-monospace-v2/
// https://css-tricks.com/snippets/css/system-font-stack/#method-2-system-font-stacks
@font-face {
  font-family: immy-mono;
  src: local("SFMono-Regular"), local("Consolas"), local("Liberation Mono"), local("Menlo"), local("Courier"),
    local("monospace");
}
