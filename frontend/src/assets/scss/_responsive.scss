@media (max-width: 576px) {
  main {
    padding: 0 !important;
  }

  .sort-actions,
  input {
    min-width: unset !important;
  }
}

@media (min-width: 992px) {
  .sidebar-fixed .app-header + .app-body .sidebar {
    height: calc(100vh - 39px);
  }
}

@media (max-width: 991.98px) {
  .sidebar-fixed .app-header + .app-body .sidebar {
    height: calc(100vh - 40px);
  }

  .app-header .navbar-brand {
    padding-left: 1rem;
    padding-right: 1rem;
    justify-content: center !important;
  }
}

@each $breakpoint in map-keys($grid-breakpoints) {
  @include media-breakpoint-up($breakpoint) {
    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

    .border#{$infix}-top {
      border-top: $border-width solid var(--divider, $border-color) !important;
      &.border-width-2 {
        border-width: 2px !important;
      }
    }

    .border#{$infix}-right {
      border-right: $border-width solid var(--divider, $border-color) !important;
      &.border-width-2 {
        border-width: 2px !important;
      }
    }

    .border#{$infix}-bottom {
      border-bottom: $border-width solid var(--divider, $border-color) !important;
      &.border-width-2 {
        border-width: 2px !important;
      }
    }

    .border#{$infix}-left {
      border-left: $border-width solid var(--divider, $border-color) !important;
      &.border-width-2 {
        border-width: 2px !important;
      }
    }

    .border#{$infix}-top-0 {
      border-top: 0 !important;
    }

    .border#{$infix}-right-0 {
      border-right: 0 !important;
    }

    .border#{$infix}-bottom-0 {
      border-bottom: 0 !important;
    }

    .border#{$infix}-left-0 {
      border-left: 0 !important;
    }

    .border#{$infix}-x {
      border-left: $border-width solid var(--divider, $border-color) !important;
      border-right: $border-width solid var(--divider, $border-color) !important;
      &.border-width-2 {
        border-width: 2px !important;
      }
    }

    .border#{$infix}-y {
      border-top: $border-width solid var(--divider, $border-color) !important;
      border-bottom: $border-width solid var(--divider, $border-color) !important;
      &.border-width-2 {
        border-width: 2px !important;
      }
    }
  }
}
