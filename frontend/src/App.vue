<template>
  <default-container v-show="authStore.initComplete" />
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted } from "vue";
import { SubscriptionStatus } from "@/api/backend/generated/enums";
import { addSubscriptionStatusDataLayerEvent } from "@/helpers/google-analytics";
import { usePreferencesStore } from "@/store/pinia/preferences-store";
import { PIIMasker } from "@/utils/pii-masker";
import { useAuthStore } from "./store/pinia/auth-store";

const authStore = useAuthStore();
const preferencesStore = usePreferencesStore();
let piiMasker: PIIMasker;

onUnmounted(() => {
  if (piiMasker)
    piiMasker.stop();
});

onMounted(async () => {
  await authStore.init();

  // setup pii masker if enabled
  if (preferencesStore.currentUserPreferences?.maskPiiData) {
    piiMasker = new PIIMasker(["pii"]);
    piiMasker.start();
  }

  /**
   * Add GA subscription status event
   */
  if (authStore.subscriptionPlanId == null)
    return;

  addSubscriptionStatusDataLayerEvent({
    planId: authStore.subscriptionPlanId,
    planName: authStore.subscriptionPlanId,
    planPrice: authStore.subscriptionPlanPrice ?? 0,
    planQuantity: authStore.subscriptionPlanQuantity ?? 0,
    subscriptionStatus: authStore.subscriptionStatus ?? SubscriptionStatus.Inactive,
    addons: authStore.subscriptionAddons,
  });
});
</script>

<style lang="scss">
// Import Main styles for this application
@import "assets/scss/style";
</style>
