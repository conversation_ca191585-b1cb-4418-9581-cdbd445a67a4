import { defineStore } from "pinia";
import { computed, ref } from "vue";
import {
  ICreateProviderLinkRequestBody,
  ICreateProviderLinkWithExternalProviderReferenceRequestBody,
  IGetProviderLinkResponse,
  IUpdateProviderLinkPayload,
} from "@/api/backend/generated/contracts";
import { providerLinksApi } from "@/api/backend/v1";
import { useReactiveMap } from "@/composables/useReactiveMap";
import { IMMY_AGENT_PROVIDER_TYPE_ID } from "@/utils/constants";

export const useProviderLinksStore = defineStore("providerLinks", () => {
  const { setMap, removeItemInMap, addOrUpdateItemInMap } = useReactiveMap();
  const cachedProviderLinks = ref(new Map<number, IGetProviderLinkResponse>());
  const cachedProviderLinksIncludeClients = ref(false);

  const allProviderLinks = computed(() => [...cachedProviderLinks.value.values()]);

  const immyAgentProviderLink = computed(() => {
    return allProviderLinks.value.find(x => x.providerTypeId === IMMY_AGENT_PROVIDER_TYPE_ID);
  });

  async function getAllProviderLinks(
    opts: { force?: boolean; includeClients?: boolean; includeUnlinkedClients?: boolean } = {
      force: false,
      includeClients: false,
      includeUnlinkedClients: false,
    },
  ) {
    if (
      cachedProviderLinks.value.size === 0
      || opts.force
      || (opts.includeClients && !cachedProviderLinksIncludeClients.value)
    ) {
      cachedProviderLinksIncludeClients.value = opts.includeClients ?? false;
      const res = await providerLinksApi.getAll({
        includeClients: opts.includeClients,
        includeUnlinkedClients: opts.includeUnlinkedClients,
      });
      setMap(cachedProviderLinks, res, "id");
    }
    return [...cachedProviderLinks.value.values()];
  }

  async function deleteProviderLink(id: number) {
    await providerLinksApi.delete(id);
    removeItemInMap(cachedProviderLinks, id);
  }

  async function createProviderLink(payload: ICreateProviderLinkRequestBody) {
    const res = await providerLinksApi.create(payload);
    addOrUpdateItemInMap(cachedProviderLinks, res, "id");
    return res;
  }

  async function createProviderLinkWithExternalProviderReference(
    payload: ICreateProviderLinkWithExternalProviderReferenceRequestBody,
  ) {
    const res = await providerLinksApi.createWithExternalProviderReference(payload);
    addOrUpdateItemInMap(cachedProviderLinks, res, "id");
    return res;
  }

  async function updateProviderLink(payload: IUpdateProviderLinkPayload) {
    const res = await providerLinksApi.update(payload);
    addOrUpdateItemInMap(cachedProviderLinks, res, "id");
    return res;
  }

  async function getProviderLink(id: number) {
    const res = await providerLinksApi.get(id);
    addOrUpdateItemInMap(cachedProviderLinks, res, "id");
    return res;
  }

  async function linkProviderClientsToTenant(
    tenantId: number,
    linkId: number,
    clientIds: string[],
  ) {
    await providerLinksApi.linkProviderClientsToTenant(linkId, { clientIds, tenantId });
    if (cachedProviderLinks.value.size !== 0) {
      const link = cachedProviderLinks.value.get(linkId);
      if (link) {
        link.providerClients?.forEach((c) => {
          if (clientIds.includes(c.externalClientId))
            c.linkedToTenantId = tenantId;
        });
        addOrUpdateItemInMap(cachedProviderLinks, link, "id");
      }
    }
  }

  async function unlinkProviderClients(linkId: number, clientIds: string[]) {
    await providerLinksApi.unlinkProviderClients(linkId, { clientIds });
    if (cachedProviderLinks.value.size !== 0) {
      const link = cachedProviderLinks.value.get(linkId);
      if (link) {
        link.providerClients?.forEach((c) => {
          if (clientIds.includes(c.externalClientId))
            c.linkedToTenantId = undefined;
        });
        addOrUpdateItemInMap(cachedProviderLinks, link, "id");
      }
    }
  }

  return {
    getAllProviderLinks,
    deleteProviderLink,
    createProviderLink,
    createProviderLinkWithExternalProviderReference,
    updateProviderLink,
    getProviderLink,
    linkProviderClientsToTenant,
    unlinkProviderClients,
    allProviderLinks,
    immyAgentProviderLink,
    cachedProviderLinks,
  };
});
