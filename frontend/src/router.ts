import { nextTick } from "vue";
import { createRouter, create<PERSON>eb<PERSON><PERSON><PERSON>, NavigationGuardNext, RouteLocationNormalized, RouteParams } from "vue-router";
import { RbacPermissions } from "@/api/backend/generated/rbac-permissions";
import { getAppInsights } from "@/config/app-insights";
import { RbacTab } from "@/interfaces";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useFeaturesStore } from "@/store/pinia/features-store";
import { usePermissionStore } from "@/store/pinia/permission-store";
import AzureSettingsPage from "@/views/AzureSettingsPage.vue";
import BillingPage from "@/views/BillingPage.vue";
import BrandingDetailsPage from "@/views/BrandingDetailsPage.vue";
import BrandingListPage from "@/views/BrandingListPage.vue";
import ComputerDetailsPage from "@/views/ComputerDetailsPage.vue";
import ComputerInventoryPage from "@/views/ComputerInventoryPage.vue";
import ComputerListPage from "@/views/ComputerListPage.vue";
import ConsentCallbackPage from "@/views/ConsentCallbackPage.vue";
import DashboardPage from "@/views/DashboardPage.vue";
import DeploymentDetailsPage from "@/views/DeploymentDetailsPage.vue";
import DeploymentListPage from "@/views/DeploymentListPage.vue";
import Error403Page from "@/views/Error403Page.vue";
import Error404Page from "@/views/Error404Page.vue";
import Error500Page from "@/views/Error500Page.vue";
import InventoryTasksListPage from "@/views/InventoryTasksListPage.vue";
import LandingPage from "@/views/LandingPage.vue";
import LoginPage from "@/views/LoginPage.vue";
import MaintenanceEmailPage from "@/views/MaintenanceEmailPage.vue";
import MaintenancePriorityPage from "@/views/MaintenancePriorityPage.vue";
import MaintenanceSessionListPage from "@/views/MaintenanceSessionListPage.vue";
import MaintenanceTaskDetailsPage from "@/views/MaintenanceTaskDetailsPage.vue";
// Tasks
import MaintenanceTaskListPage from "@/views/MaintenanceTaskListPage.vue";
import MediaDetailsPage from "@/views/MediaDetailsPage.vue";
import MediaListPage from "@/views/MediaListPage.vue";

import NewTenantPage from "@/views/NewTenantPage.vue";

import PersonDetailsPage from "@/views/PersonDetailsPage.vue";
import PersonListPage from "@/views/PersonListPage.vue";
// Settings
import PreferencesPage from "@/views/PreferencesPage.vue";
import ProviderLinkDetailsPage from "@/views/ProviderLinkDetailsPage.vue";
import ProviderLinkListPage from "@/views/ProviderLinkListPage.vue";
import ScheduleDetailsPage from "@/views/ScheduleDetailsPage.vue";
import ScheduleListPage from "@/views/ScheduleListPage.vue";

// Scripts
import ScriptListPage from "@/views/ScriptListPage.vue";

import SelfServicePage from "@/views/SelfServicePage.vue";
import SmtpPage from "@/views/SmtpPage.vue";
import SoftwareDetailsPage from "@/views/SoftwareDetailsPage.vue";
import SoftwareLicenseDetailsPage from "@/views/SoftwareLicenseDetailsPage.vue";
import SoftwareLicenseListPAge from "@/views/SoftwareLicenseListPage.vue";
import SoftwareListPage from "@/views/SoftwareListPage.vue";
import SoftwareVersionDetailsPage from "@/views/SoftwareVersionDetailsPage.vue";
import SoftwareVersionUploadPage from "@/views/SoftwareVersionUploadPage.vue";
import SystemStatusPage from "@/views/SystemStatusPage.vue";
import SystemUpdatePage from "@/views/SystemUpdatePage.vue";

import TagDetailsPage from "@/views/TagDetailsPage.vue";

import TagsListPage from "@/views/TagsListPage.vue";
import TechnicianPodPage from "@/views/TechnicianPodPage.vue";

import TenantDetailsPage from "@/views/TenantDetailsPage.vue";
import TenantListPage from "@/views/TenantListPage.vue";

import UserDetailsPage from "@/views/UserDetailsPage.vue";

import UserListPage from "@/views/UserListPage.vue";

import { FeatureEnum } from "./api/backend/generated/enums";

import { useAuthStore } from "./store/pinia/auth-store";
import { useNavigationStore } from "./store/pinia/navigation-store";

import { useUsersStore } from "./store/pinia/users-store";

import { CONSENT_REDIRECT_PATH } from "./utils/constants";

import AuditPage from "./views/ApplicationAuditPage.vue";
import LocksPage from "./views/ApplicationLocksPage.vue";

import ApplicationLogsPage from "./views/ApplicationLogsPage.vue";

import ChangeRequestListPage from "./views/ChangeRequestListPage.vue";
import CheckListPage from "./views/CheckListPage.vue";

import ComputerReportPage from "./views/ComputerReportPage.vue";

import CWManageLandingPage from "./views/CWManageLandingPage.vue";

// pwa script editor

import DetectedComputerSoftwarePage from "./views/DetectedComputerSoftwarePage.vue";
import DynamicIntegrationDetailsPage from "./views/DynamicIntegrationDetailsPage.vue";
import DynamicIntegrationListPage from "./views/DynamicIntegrationListPage.vue";
import EditUserPage from "./views/EditUserPage.vue";
import NotificationsPage from "./views/NotificationsPage.vue";

import OauthAccessTokensListPage from "./views/OauthAccessTokensListPage.vue";
import PwaScriptEditorPage from "./views/PwaScriptEditorPage.vue";
import RbacPage from "./views/RbacPage.vue";
import RolePage from "./views/RolePage.vue";
import SessionDetailsPage from "./views/SessionDetailsPage.vue";
import TargetAssignmentChangeRequestApprovalPage from "./views/TargetAssignmentChangeRequestApprovalPage.vue";
import UserAffinityReportPage from "./views/UserAffinityReportPage.vue";

enum RouteAuthenticationState {
  NoAuthenticationRequired,
  Authenticated,
  NotAuthenticatedHeadingToLoginPage,
}

function numberProp(val: any) {
  const num = Number.parseInt(val);
  if (Number.isNaN(num))
    return undefined;
  return num;
}

function boolProp(val: any) {
  if (typeof val === "boolean")
    return val;
  if (typeof val === "string")
    return val.toLowerCase() === "true" ? true : val.toLowerCase() === "false" ? false : undefined;
  return undefined;
}

// we do this before the router starts messing with the current URL
const initUrlParams = new URLSearchParams(window.location.search);

function makeRouter() {
  const appInsights = getAppInsights();
  // Let the store know what the initial URL params were in case it needs to do
  // some initial setup
  const url = new URL(window.location.href);
  url.search = initUrlParams.toString();
  window.history.pushState(null, "", url.toString());
  const router = createRouter({
    history: createWebHistory(),
    linkActiveClass: "open active",
    scrollBehavior: () => ({ top: 0 }),
    routes: [
      {
        path: "/error",
        name: "500",
        component: Error500Page,
        meta: {
          permission: null,
          requiresAuthorization: false,
          title: "Error",
        },
      },
      {
        path: `/${CONSENT_REDIRECT_PATH}`,
        name: "Consent Callback",
        component: ConsentCallbackPage,
        meta: {
          permission: null,
          requiresAuthorization: false,
        },
      },
      {
        path: "/",
        redirect: "/computers",
        children: [
          {
            path: "technician-pod/psa/:providerLinkId/ticket/:ticketId",
            name: "Technician Pod",
            component: TechnicianPodPage,
            meta: {
              title: "Technician Pod",
              permission: null,
              hideAppSidebar: true,
              hideAppHeader: true,
            },
            props: route => ({
              providerLinkId: numberProp(route.params.providerLinkId),
              ticketId: route.params.ticketId,
            }),
          },
          {
            path: "technician-pod/cw-manage/:integrationId",
            name: "CW Technician Pod",
            component: CWManageLandingPage,
            meta: {
              title: "Technician Pod",
              permission: null,
              hideAppSidebar: true,
              hideAppHeader: true,
            },
            props: route => ({
              providerLinkId: numberProp(route.params.integrationId),
            }),
          },
          {
            path: "self-service",
            name: "Self Service",
            component: SelfServicePage,
            meta: {
              requiresManagementAccess: false,
              permission: null,
              title: "Self Service",
            },
          },
          {
            path: "/login",
            name: "Login",
            component: LoginPage,
            props: route => ({
              postLoginRedirectUrl: route.query.postLoginRedirectUrl,
            }),
            meta: {
              requiresAuthentication: false,
              requiresAuthorization: false,
              permission: null,
              title: "Login",
            },
          },
          {
            path: "/landing",
            name: "Landing",
            component: LandingPage,
            meta: {
              requiresAuthorization: false,
              permission: null,
              title: "Landing",
            },
          },
          {
            // exists for backwards compatibility
            path: "/api/v1/emails/:id/jobs/:action",
            name: "Maintenance Email Old",
            component: MaintenanceEmailPage,
            meta: {
              title: "Maintenance Email",
              permission: null,
              requiresAuthorization: false,
              requiresAuthentication: false,
            },
            props: route => ({
              id: route.params.id,
              action: route.params.action,
            }),
          },
          {
            path: "/maintenance-emails/:id/jobs/:action",
            name: "Maintenance Email",
            component: MaintenanceEmailPage,
            meta: {
              title: "Maintenance Email",
              permission: null,
              requiresAuthorization: false,
              requiresAuthentication: false,
            },
            props: route => ({
              id: route.params.id,
              action: route.params.action,
            }),
          },
          {
            path: "/checklist",
            name: "Checklist",
            component: CheckListPage,
            meta: {
              title: "Checklist",
              permission: "getting_started:view",
              requiresMsp: true,
            },
          },
          {
            path: "/dashboard",
            name: "Dashboard",
            component: DashboardPage,
            meta: {
              title: "Dashboard",
              permission: null,
            },
            props: route => ({
              targetQuery: route.query.targetQuery?.toString(),
              maintenanceItemQuery: route.query.maintenanceItemQuery?.toString(),
            }),
          },
          {
            path: "/sessions",
            name: "Session List",
            component: MaintenanceSessionListPage,
            meta: {
              title: "Maintenance Sessions",
              permission: "maintenance_sessions:view",
              requiresManagementAccess: false,
            },
          },
          {
            path: "/sessions/:sessionId",
            name: "Session Details",
            component: SessionDetailsPage,
            meta: {
              title: "Maintenance Session",
              permission: "maintenance_sessions:view",
              requiresManagementAccess: false,
              hideAppSidebar: true,
              hideAppHeader: true,
            },
            props: route => ({
              sessionId: numberProp(route.params.sessionId),
            }),
          },
          {
            path: "/computers",
            name: "Computer List",
            component: ComputerListPage,
            meta: {
              title: "Active Computers",
              permission: "computers:view",
            },
            props: route => ({
              filter: route.query.filter,
              includeDisconnected: boolProp(route.query.includeDisconnected),
              tabName: "all",
            }),
          },
          {
            path: "/computers/new",
            name: "New Computer List",
            component: ComputerListPage,
            meta: {
              title: "New Computers",
              permission: "computers:view",
            },
            props: route => ({
              filter: route.query.filter,
              includeDisconnected: boolProp(route.query.includeDisconnected),
              tabName: "new",
            }),
          },
          {
            path: "/computers/pending",
            name: "Pending Computer List",
            component: ComputerListPage,
            meta: {
              title: "Pending Computers",
              permission: "computers:view",
            },
            props: route => ({
              filter: route.query.filter,
              includeDisconnectedAgents: boolProp(route.query.includeDisconnectedAgents),
              tabName: "pending",
              onlyRequiresManualDecision: boolProp(route.query.onlyRequiresManualDecision),
            }),
          },
          {
            path: "/computers/stale",
            name: "Stale Computer List",
            component: ComputerListPage,
            meta: {
              title: "Stale Computers",
              permission: "computers:view",
            },
            props: route => ({
              filter: route.query.filter,
              includeDisconnected: boolProp(route.query.includeDisconnected),
              tabName: "stale",
            }),
          },
          {
            path: "/computers/devlab",
            name: "DevLab Computer List",
            component: ComputerListPage,
            meta: {
              title: "DevLab Computers",
              permission: "computers:view",
            },
            props: route => ({
              filter: route.query.filter,
              includeDisconnected: boolProp(route.query.includeDisconnected),
              tabName: "devlab",
            }),
          },
          {
            path: "/computers/deleted",
            name: "Deleted Computer List",
            component: ComputerListPage,
            meta: {
              title: "Deleted Computers",
              permission: "computers:view",
            },
            props: route => ({
              filter: route.query.filter,
              includeDisconnected: boolProp(route.query.includeDisconnected),
              tabName: "deleted",
            }),
          },
          {
            path: "/computers/agent-status",
            name: "Computer Agent Status List",
            component: ComputerListPage,
            meta: {
              title: "Computer Agents Status",
              permission: "computers:view",
            },
            props: route => ({
              filter: route.query.filter,
              tabName: "agent-status",
            }),
          },
          {
            path: "/computers/:computerId(\\d+)/:tabName?/:childId?",
            name: "Computer Details",
            component: ComputerDetailsPage,
            props: route => ({
              computerId: numberProp(route.params.computerId),
              tabName: route.params.tabName,
              childId: route.params.childId,
            }),
            meta: {
              title: "Computer Details",
              permission: "computers:view",
            },
          },
          {
            // This is not nested under the /tenants children because
            // it needs component reuse
            path: "/tenants/:tenantId(\\d+)/:tabName?/:childId?",
            name: "Tenant Details",
            component: TenantDetailsPage,
            props: route => ({
              tenantId: numberProp(route.params.tenantId),
              tabName: route.params.tabName,
              childId: numberProp(route.params.childId),
            }),
            meta: {
              title: "Tenant Details",
              permission: "tenants:view",
            },
          },
          {
            path: "tenants",
            name: "Tenants",
            redirect: { name: "Tenant List" },
            meta: {
              force: true,
              permission: "tenants:view",
            },
            children: [
              {
                path: "",
                name: "Tenant List",
                component: TenantListPage,
                meta: {
                  title: "Tenants",
                  permission: "tenants:view",
                  requiresMsp: true,
                },
              },
              {
                path: "new",
                name: "New Tenant",
                component: NewTenantPage,
                meta: {
                  title: "New Tenant",
                  permission: "tenants:view",
                  requiresMsp: true,
                },
              },
            ],
          },
          {
            path: "/change-requests",
            name: "Change Requests",
            component: ChangeRequestListPage,
            meta: {
              title: "Change Requests",
              permission: [
                "deployments:manage_cross_tenant_with_change_requests",
                "deployments:deployment_approver",
              ],
              requiresMsp: true,
            },
          },
          {
            path: "/deployments",
            name: "Deployments",
            redirect: { name: "Deployment List" },
            meta: {
              force: true,
              permission: [
                "deployments:view_individual",
                "deployments:view_single_tenant",
                "deployments:view_cross_tenant",
              ],
            },
            children: [
              {
                path: "",
                name: "Deployment List",
                component: DeploymentListPage,
                meta: {
                  title: "Deployments",
                  permission: [
                    "deployments:view_individual",
                    "deployments:view_single_tenant",
                    "deployments:view_cross_tenant",
                  ],
                },
              },
              {
                path: "new",
                name: "New Deployment",
                component: DeploymentDetailsPage,
                props: route => ({
                  maintenanceItemQuery: route.query.maintenanceItemQuery,
                  targetQuery: route.query.targetQuery,
                  databaseType: numberProp(route.query.databaseType),
                  computerId: numberProp(route.query.computerId),
                  maintenanceType: numberProp(route.query.maintenanceType),
                  maintenanceIdentifier: route.query.maintenanceIdentifier
                    ? route.query.maintenanceIdentifier.toString()
                    : null,
                  softwareSemanticVersion: route.query.softwareSemanticVersion
                    ? route.query.softwareSemanticVersion.toString()
                    : null,
                  desiredSoftwareState: numberProp(route.query.desiredSoftwareState),
                  target: route.query.target ? route.query.target.toString() : null,
                  targetCategory: numberProp(route.query.targetCategory),
                  targetName: route.query.targetName,
                  targetType: numberProp(route.query.targetType),
                  tenantId: numberProp(route.query.tenantId),
                  providerLinkId: numberProp(route.query.providerLinkId),
                  duplicate: boolProp(route.query.duplicate),
                  licenseId: numberProp(route.query.licenseId),
                  maintenanceTaskMode: numberProp(route.query.maintenanceTaskMode),
                  changeRequestId: numberProp(route.query.changeRequestId),
                }),
                meta: {
                  title: "New Deployment",
                  permission: [
                    "deployments:manage_individual",
                    "deployments:manage_single_tenant",
                    "deployments:manage_cross_tenant",
                  ],
                },
              },
              {
                path: ":assignmentId(\\d+)/edit",
                name: "Edit Deployment",
                component: DeploymentDetailsPage,
                props: route => ({
                  changeRequestId: numberProp(route.query.changeRequestId),
                  assignmentId: numberProp(route.params.assignmentId),
                  databaseType: numberProp(route.query.databaseType),
                }),
                meta: {
                  title: "Edit Deployment",
                  permission: [
                    "deployments:view_individual",
                    "deployments:view_single_tenant",
                    "deployments:view_cross_tenant",
                  ],
                },
              },
              {
                path: ":assignmentId(\\d+)/change-request/:changeRequestId(\\d+)",
                name: "Existing Deployment Change Request",
                component: TargetAssignmentChangeRequestApprovalPage,
                props: route => ({
                  changeRequestId: numberProp(route.params.changeRequestId),
                  assignmentId: numberProp(route.params.assignmentId),
                  databaseType: numberProp(route.query.databaseType),
                }),
                meta: {
                  title: "Deployment Change Request",
                  permission: [
                    "deployments:manage_cross_tenant_with_change_requests",
                    "deployments:deployment_approver",
                  ],
                },
              },
              {
                path: "change-request/:changeRequestId(\\d+)",
                name: "New Deployment Change Request",
                component: TargetAssignmentChangeRequestApprovalPage,
                props: route => ({
                  changeRequestId: numberProp(route.params.changeRequestId),
                  databaseType: numberProp(route.query.databaseType),
                }),
                meta: {
                  title: "Deployment Change Request",
                  permission: [
                    "deployments:manage_cross_tenant_with_change_requests",
                  ],
                },
              },
            ],
          },
          {
            path: "schedules",
            name: "Schedules",
            redirect: { name: "Schedule List" },
            meta: {
              force: true,
              permission: "schedules:view",
            },
            children: [
              {
                path: "",
                name: "Schedule List",
                component: ScheduleListPage,
                meta: {
                  title: "Schedules",
                  permission: "schedules:view",
                },
              },
              {
                path: "new",
                name: "New Schedule",
                component: ScheduleDetailsPage,
                meta: {
                  title: "New Schedule",
                  permission: "schedules:manage",
                },
              },
              {
                path: ":scheduleId(\\d+)/edit",
                name: "Edit Schedule",
                component: ScheduleDetailsPage,
                props: route => ({
                  scheduleId: numberProp(route.params.scheduleId),
                }),
                meta: {
                  title: "Edit Schedule",
                  permission: "schedules:view",
                },
              },
            ],
          },
          {
            path: "reporting/computer-inventory-scripts",
            name: "Computer Inventory Scripts",
            component: ComputerInventoryPage,
            meta: {
              title: "Computer Inventory Scripts",
              permission: "computers:view_inventory_report",
            },
          },
          {
            path: "reporting/detected-computer-software",
            name: "Detected Computer Software",
            component: DetectedComputerSoftwarePage,
            meta: {
              title: "Detected Computer Software",
              permission: "computers:view_inventory_report",
            },
          },
          {
            path: "reporting/computers",
            name: "Computers Report",
            component: ComputerReportPage,
            meta: {
              title: "Computer Report",
              permission: "computers:view",
            },
          },
          {
            path: "reporting/user-affinity",
            name: "User Affinity Report",
            component: UserAffinityReportPage,
            meta: {
              title: "User Affinity Report",
              permission: "computers:user_affinity",
            },
          },
          {
            path: "library/ordering",
            name: "Maintenance Priority",
            component: MaintenancePriorityPage,
            props: () => ({}),
            meta: {
              title: "Maintenance Priority",
              permission: "deployments:manage_maintenance_item_ordering",
            },
          },
          {
            path: "/library/software",
            name: "Software",
            redirect: { name: "Software List" },
            meta: {
              force: true,
              permission: "software:view",
            },
            children: [
              {
                path: "",
                name: "Software List",
                component: SoftwareListPage,
                meta: {
                  title: "Software",
                  permission: "software:view",
                },
              },
              {
                path: ":softwareId(\\d+)",
                name: "Edit Software",
                component: SoftwareDetailsPage,
                props: route => ({
                  softwareId: numberProp(route.params.softwareId),
                  softwareType: numberProp(route.query.softwareType),
                }),
                meta: {
                  title: "Edit Software",
                  permission: "software:view",
                },
              },
              {
                path: ":softwareId(\\d+)/versions",
                name: "Edit Software Version",
                props: route => ({
                  softwareId: numberProp(route.params.softwareId),
                  semanticVersion: route.query.semanticVersion,
                  softwareType: numberProp(route.query.softwareType),
                }),
                component: SoftwareVersionDetailsPage,
                meta: {
                  title: "Edit Software Version",
                  permission: "software:view",
                },
              },
              {
                path: ":softwareId(\\d+)/versions/new",
                name: "New Software Version",
                props: route => ({
                  softwareId: numberProp(route.params.softwareId),
                  softwareType: numberProp(route.query.softwareType),
                  fromPrevious:
                    route.query.fromPrevious?.toString() === "true"
                    || route.query.fromPrevious === "true",
                }),
                component: SoftwareVersionDetailsPage,
                meta: {
                  title: "New Software Version",
                  permission: "software:manage",
                },
              },
              {
                path: "upload",
                name: "Upload Software",
                component: SoftwareVersionUploadPage,
                props: route => ({
                  softwareType: numberProp(route.query.softwareType),
                  softwareId: route.query.softwareId,
                }),
                meta: {
                  title: "Upload Software",
                  permission: "software:manage",
                },
              },
              {
                path: "new",
                name: "New Software",
                component: SoftwareDetailsPage,
                props: route => ({
                  softwareType: numberProp(route.query.softwareType),
                }),
                meta: {
                  title: "New Software",
                  permission: "software:manage",
                },
              },
              {
                path: "licenses",
                name: "License List",
                component: SoftwareLicenseListPAge,
                meta: {
                  title: "Licenses",
                  permission: "licenses:view",
                },
              },
              {
                path: "licenses/new",
                name: "New License",
                component: SoftwareLicenseDetailsPage,
                meta: {
                  title: "New License",
                  permission: "licenses:view",
                },
                props: route => ({
                  maintenanceIdentifier: route.query.maintenanceIdentifier,
                  maintenanceType: numberProp(route.query.maintenanceType),
                }),
              },
              {
                path: "licenses/:licenseId(\\d+)/edit",
                name: "Edit License",
                component: SoftwareLicenseDetailsPage,
                props: route => ({
                  licenseId: numberProp(route.params.licenseId),
                }),
                meta: {
                  title: "Edit License",
                  permission: "licenses:view",
                },
              },
            ],
          },
          {
            path: "pwa-script-editor",
            name: "ImmyBot Script Editor",
            component: PwaScriptEditorPage,
            meta: {
              permission: "scripts:view",
            },
          },
          {
            path: "library/scripts",
            name: "Scripts",
            redirect: { name: "Script List" },
            meta: {
              label: "Scripts",
              force: true,
              permission: "scripts:view",
            },
            children: [
              {
                path: "",
                name: "Script List",
                component: ScriptListPage,
                props: route => ({
                  scriptId: numberProp(route.query.scriptId),
                  scriptType: numberProp(route.query.scriptType),
                }),
                meta: {
                  label: "List",
                  title: "Scripts",
                  permission: "scripts:view",
                },
              },
            ],
          },
          {
            path: "library/tasks",
            name: "Tasks",
            redirect: { name: "Task List" },
            meta: {
              label: "Tasks",
              force: true,
              permission: "software:view",
            },
            children: [
              {
                path: "",
                name: "Task List",
                component: MaintenanceTaskListPage,
                meta: {
                  label: "List",
                  title: "Tasks",
                  permission: "software:view",
                },
              },
              {
                path: ":maintenanceTaskId(\\d+)",
                name: "Edit Task",
                props: route => ({
                  maintenanceTaskId: numberProp(route.params.maintenanceTaskId),
                  maintenanceTaskType: numberProp(route.query.maintenanceTaskType),
                  showReferences:
                    route.query.showReferences?.toString() === "true"
                    || route.query.showReferences === "true",
                }),
                component: MaintenanceTaskDetailsPage,
                meta: {
                  title: "Edit Task",
                  permission: "software:view",
                },
              },
              {
                path: "new",
                name: "New Task",
                props: route => ({
                  maintenanceTaskType: numberProp(route.query.maintenanceTaskType),
                  setScriptId: numberProp(route.query.setScriptId),
                  setScriptType: numberProp(route.query.setScriptType),
                  useScriptParamBlock: boolProp(route.query.useScriptParamBlock),
                }),
                component: MaintenanceTaskDetailsPage,
                meta: {
                  title: "New Task",
                  permission: "software:view",
                },
              },
            ],
          },
          {
            path: "/settings/billing/:tabName?",
            name: "Billing",
            component: BillingPage,
            meta: {
              requiresMsp: true,
              permission: "billing:view",
            },
            props: route => ({
              tabName: route.params.tabName ?? undefined,
              editSubscription: boolProp(route.query.editSubscription),
            }),
          },
          {
            path: "/notifications",
            name: "Notifications",
            component: NotificationsPage,
            children: [
              {
                path: "/notifications/:id",
                name: "View Notification",
                component: NotificationsPage,
                props: route => ({
                  id: route.params.id,
                }),
              },
            ],
          },
          {
            path: "/settings",
            name: "Settings",
            redirect: { name: "User List" },
            meta: {
              force: true,
              permission: null,
            },
            children: [
              {
                path: "persons",
                name: "Persons",
                redirect: { name: "Person List" },
                meta: {
                  label: "People",
                  force: true,
                  permission: "persons:view",
                },
                children: [
                  {
                    path: "",
                    name: "Person List",
                    component: PersonListPage,
                    meta: {
                      label: "List",
                      title: "People",
                      permission: "persons:view",
                    },
                  },
                  {
                    path: "new",
                    name: "New Person",
                    component: PersonDetailsPage,
                    meta: {
                      label: "New",
                      title: "New Person",
                      permission: "persons:view",
                    },
                  },
                  {
                    path: ":personId(\\d+)/:tabName?/:childId?",
                    name: "Edit Person",
                    props: route => ({
                      personId: numberProp(route.params.personId),
                      tabName: route.params.tabName,
                      childId: numberProp(route.params.childId),
                    }),
                    component: PersonDetailsPage,
                    meta: {
                      label: "Edit",
                      title: "Edit Person",
                      permission: "persons:view",
                    },
                  },
                ],
              },
              {
                path: "system-status",
                name: "System Status",
                component: SystemStatusPage,
                meta: {
                  title: "System Status",
                  requiresMsp: true,
                  permission: "system_operations:view",
                },
              },
              {
                path: "application-logs",
                name: "Application Logs",
                component: ApplicationLogsPage,
                meta: {
                  title: "Application Logs",
                  requiresMsp: true,
                  permission: null,
                },
              },
              {
                path: "audit",
                name: "Audit",
                component: AuditPage,
                meta: {
                  title: "Audit Page",
                  requiresMsp: true,
                  permission: "audit:view",
                },
              },
              {
                path: "locks",
                name: "Locks",
                component: LocksPage,
                meta: {
                  title: "Application Locks",
                  requiresMsp: true,
                  permission: "application_locks:view",
                },
              },
              {
                path: "media",
                name: "Media",
                redirect: { name: "Media List" },
                meta: {
                  force: true,
                  permission: "media:view",
                },
                children: [
                  {
                    path: "",
                    name: "Media List",
                    component: MediaListPage,
                    meta: {
                      title: "Media",
                      permission: "media:view",
                    },
                  },
                  {
                    path: "new",
                    name: "New Media",
                    component: MediaDetailsPage,
                    meta: {
                      title: "New Media",
                      permission: "media:view",
                    },
                  },
                  {
                    path: ":mediaId(\\d+)/edit",
                    name: "Edit Media",
                    component: MediaDetailsPage,
                    props: route => ({
                      databaseType: numberProp(route.query.databaseType),
                      mediaId: numberProp(route.params.mediaId),
                    }),
                    meta: {
                      title: "Edit Media",
                      permission: "media:view",
                    },
                  },
                ],
              },
              {
                path: "users",
                name: "User List",
                beforeEnter: (_, __, next) => handleRBACRedirect(next, "RBAC"),
                component: UserListPage,
                meta: {
                  title: "Users",
                  permission: "users:view",
                },
              },
              {
                path: "rbac",
                name: "RBAC",
                redirect: { name: "RBAC Users" },
                meta: {
                  force: true,
                  permission: "users:view",
                },
                children: [
                  {
                    path: "users",
                    name: "RBAC Users",
                    component: RbacPage,
                    props: () => ({
                      tab: RbacTab.Users,
                    }),
                    meta: {
                      title: "Users",
                      permission: "users:view",
                    },
                  },
                  {
                    path: "roles",
                    name: "RBAC Roles",
                    component: RbacPage,
                    props: () => ({
                      tab: RbacTab.Roles,
                    }),
                    meta: {
                      title: "Roles",
                      requiresMsp: true,
                      permission: "rbac:view",
                    },
                  },
                  {
                    path: "roles/:id(-?\\d+)",
                    name: "Edit Role",
                    component: RolePage,
                    props: route => ({
                      id: route.params.id ? numberProp(route.params.id) : null,
                    }),
                    meta: {
                      title: "Edit Role",
                      requiresMsp: true,
                      permission: "rbac:view",
                    },
                  },
                  {
                    path: "roles/new",
                    name: "New Role",
                    component: RolePage,
                    meta: {
                      title: "New Role",
                      requiresMsp: true,
                      permission: "rbac:manage",
                    },
                  },
                  {
                    path: "users/:id(\\d+)",
                    name: "Edit User2",
                    props: route => ({
                      id: numberProp(route.params.id),
                      userId: numberProp(route.params.id),
                    }),
                    component: EditUserPage,
                    meta: {
                      title: "Edit User",
                      permission: "users:view",
                    },
                  },
                  {
                    path: "users/new",
                    name: "New User",
                    component: EditUserPage,
                    meta: {
                      title: "New User",
                      permission: "users:manage",
                    },
                  },
                ],
              },
              {
                path: "users/:id(\\d+)",
                name: "Edit User",
                props: route => ({
                  userId: numberProp(route.params.id),
                }),
                beforeEnter: async (to, _, next) => {
                  const usersStore = useUsersStore();
                  const { personId } = await usersStore.get(+to.params.id);
                  handleRBACRedirect(next, "Edit User2", personId ? { id: personId?.toString() } : undefined);
                },
                component: UserDetailsPage,
                meta: {
                  title: "Edit User",
                  permission: "users:view",
                },
              },
              {
                path: "tags",
                name: "Tag List",
                component: TagsListPage,
                meta: {
                  title: "Tags",
                  permission: "tags:view",
                },
              },
              {
                path: "tags/:id(\\d+)",
                name: "Edit Tag",
                props: route => ({
                  tagId: numberProp(route.params.id),
                }),
                component: TagDetailsPage,
                meta: {
                  title: "Edit Tag",
                  permission: "tags:view",
                },
              },
              {
                path: "tags/new",
                name: "New Tag",
                component: TagDetailsPage,
                meta: {
                  title: "New Tag",
                  permission: "tags:view",
                },
              },
              {
                path: "brandings",
                name: "Branding List",
                component: BrandingListPage,
                meta: {
                  title: "Brandings",
                  permission: "branding:view",
                },
              },
              {
                path: "brandings/:id(\\d+)",
                name: "Edit Branding",
                props: route => ({
                  brandingId: numberProp(route.params.id),
                }),
                component: BrandingDetailsPage,
                meta: {
                  title: "Edit Branding",
                  permission: "branding:view",
                },
              },
              {
                path: "brandings/new",
                name: "New Branding",
                component: BrandingDetailsPage,
                meta: {
                  title: "New Branding",
                  permission: "branding:view",
                },
              },
              {
                path: "smtp",
                name: "Smtp",
                component: SmtpPage,
                meta: {
                  title: "SMTP",
                  permission: "smtp_configurations:view",
                },
              },
              {
                path: "preferences",
                name: "Preferences",
                component: PreferencesPage,
                meta: {
                  title: "Preferences",
                  permission: null,
                },
                props: route => ({
                  tenantId: numberProp(route.query.tenantId),
                  defaultSearchText: route.query.search,
                }),
              },
              {
                path: "inventory",
                name: "Inventory",
                component: InventoryTasksListPage,
                meta: {
                  title: "Inventory",
                  requiresMsp: true,
                  permission: "inventory_task:view",
                },
              },
              {
                path: "azure",
                name: "Azure Settings",
                component: AzureSettingsPage,
                props: route => ({
                  defaultSelectedPartnerTenantId: route.query.defaultSelectedPartnerTenantId,
                  spotlightCustomerTenantId: route.query.spotlightCustomerTenantId,
                }),
                meta: {
                  title: "Azure Settings",
                  permission: "azure_operations:view",
                },
              },
              {
                path: "integrations",
                name: "Integrations List",
                component: ProviderLinkListPage,
                meta: {
                  title: "Integrations",
                  requiresMsp: true,
                  permission: "integrations:view",
                },
              },
              {
                path: "integrations/new",
                name: "New Integration",
                component: ProviderLinkDetailsPage,
                props: route => ({
                  providerTypeId: route.params.providerTypeId,
                  providerLinkExternalReference: numberProp(
                    route.params.providerLinkExternalReference,
                  ),
                }),
                meta: {
                  title: "New Integration",
                  requiresMsp: true,
                  permission: "integrations:view",
                },
              },
              {
                path: "integrations/:id(\\d+)",
                name: "Edit Integration",
                props: route => ({
                  providerLinkId: numberProp(route.params.id),
                }),
                component: ProviderLinkDetailsPage,
                meta: {
                  title: "Edit Integration",
                  requiresMsp: true,
                  permission: "integrations:view",
                },
              },
              {
                path: "dynamic-integration-types",
                name: "Integration Types List",
                component: DynamicIntegrationListPage,
                meta: {
                  title: "Dynamic Integration Types",
                  permission: "dynamic_integration_types:view",
                },
              },
              {
                path: "dynamic-integration-types/new",
                name: "New Integration Type",
                component: DynamicIntegrationDetailsPage,
                meta: {
                  title: "New Integration Type",
                  permission: "dynamic_integration_types:view",
                },
              },
              {
                path: "dynamic-integration-types/:id(\\d+)",
                name: "Edit Integration Type",
                props: route => ({
                  id: numberProp(route.params.id),
                  databaseType: numberProp(route.query.databaseType),
                }),
                component: DynamicIntegrationDetailsPage,
                meta: {
                  title: "Edit Integration Type",
                  permission: "dynamic_integration_types:view",
                },
              },
              {
                path: "oauth-tokens",
                name: "OAuth Tokens",
                component: OauthAccessTokensListPage,
                meta: {
                  requiresMsp: true,
                  title: "OAuth Tokens",
                  permission: "oauth:manage",
                },
              },
              {
                path: "system-update",
                name: "System Update",
                component: SystemUpdatePage,
                meta: {
                  requiresMsp: true,
                  title: "System Update",
                  permission: "system_operations:pull_updates",
                },
              },
            ],
          },
        ],
      },
      {
        path: "/403", // catch all
        name: "403",
        component: Error403Page,
        meta: {
          title: "403: Permission Denied",
          permission: null,
        },
      },
      {
        path: "/:pathMatch(.*)*", // catch all
        name: "404",
        component: Error404Page,
        meta: {
          title: "Error",
          permission: null,
        },
      },
    ],
  });

  router.onError(() => {
    router.replace("/");
  });

  // if the user is not logged in, then push them to the login page
  // and set the post login redirect url to the page they were
  // attempting to access.  Unless it was a 404 or 500 page.
  async function checkAuthentication(to: RouteLocationNormalized): Promise<RouteAuthenticationState> {
    await nextTick();
    const authStore = useAuthStore();
    if (to.matched.some(record => record.meta.requiresAuthentication === false))
      return Promise.resolve(RouteAuthenticationState.NoAuthenticationRequired);

    if (!authStore.isLoggedIn) {
      if (to.name == "Login")
        return Promise.resolve(RouteAuthenticationState.NotAuthenticatedHeadingToLoginPage);
      let redirectUrl = "";
      if (to.name != "404" && to.name != "500")
        redirectUrl = to.fullPath;
      return Promise.reject({ name: "Login", query: { postLoginRedirectUrl: redirectUrl } });
    }
    return Promise.resolve(RouteAuthenticationState.Authenticated);
  }

  async function checkAuthorization(to: RouteLocationNormalized) {
    await nextTick();
    const authStore = useAuthStore();
    const permissionStore = usePermissionStore();

    if (authStore.authorized) {
      // if authorized but going to the login page,
      // redirect to the computers page since they are already logged in.
      if (to.name === "Login")
        if (permissionStore.can("computers:view"))
          return Promise.reject({ name: "Computer List" });
        else
          // return to landing if you don't have permission to view the computers page
          return Promise.reject({ name: "Landing" });
    }

    if (to.matched.some(record => record.meta.requiresAuthorization === false))
      return Promise.resolve();

    // if authorized to use this application
    if (authStore.authorized) {
      // if user doesn't have management access, then redirect to self-service page
      if (
        !authStore.hasManagementAccess
        && to.name !== "Self Service"
        && (to.meta.requiresManagementAccess === true
          || to.meta.requiresManagementAccess === undefined)
      ) {
        return Promise.reject({ name: "Self Service" });
      }

      for (const record of to.matched) {
        if (!record.meta.permission)
          continue;

        // return 403 if not authorized to view a page.
        const permissions: RbacPermissions[] = typeof record.meta.permission === "string" ? [record.meta.permission] : record.meta.permission;

        // at least one permission must be true
        if (!permissions.some(p => permissionStore.can(p))) {
          if (record.name === "Computer List")
            // return to landing since the computer list page is where you
            // are typically redirected after you log in.
            return Promise.reject({ name: "Landing" });
          else
            // for any other page show 403
            return Promise.reject({ name: "403" });
        }

        // if no claims, call api for value
        if (permissionStore.userClaims == undefined) {
          try {
            await authStore.checkAuthorization();
          }
          catch {
            return Promise.reject("/");
          }
          if (!permissions.some(p => permissionStore.can(p)))
            return Promise.reject({ name: "403" });
        }
      }
      return Promise.resolve();
    }
    else {
      if (authStore.noServer || authStore.initializationFailed) {
        if (to.name == "500")
          return Promise.resolve();
        else return Promise.reject("/error");
      }
      // if not authorized, redirect to public landing page.
      return Promise.reject({ name: "Landing" });
    }
  }

  function handleRBACRedirect(next: NavigationGuardNext, redirectTo: string, params?: RouteParams) {
    const featureFlagStore = useFeaturesStore();
    if (featureFlagStore.isEnabled(FeatureEnum.RBACFeature)) {
      next({ name: redirectTo, params });
    }
    else {
      next();
    }
  }

  function handlePageTitle(to: RouteLocationNormalized, _: RouteLocationNormalized) {
    // This goes through the matched routes from last to first, finding the closest route with a title.
    // e.g. if we have /some/deep/nested/route and /some, /deep, and /nested have titles, nested's will be chosen.
    const toRoutes = to.matched.slice().reverse();
    const nearestWithTitle = toRoutes.find(r => r.meta && r.meta.title);

    // Find the nearest route element with meta tags.
    const nearestWithMeta = toRoutes.find(r => r.meta && r.meta.metaTags);

    // If a route with a title was found, set the document (page) title to that value.
    if (nearestWithTitle)
      document.title = nearestWithTitle.meta.title?.toString() || "";

    // Remove any stale meta tags from the document using the key attribute we set below.
    Array.from(document.querySelectorAll("[data-vue-router-controlled]")).forEach(el =>
      el.parentNode?.removeChild(el),
    );

    // Skip rendering meta tags if there are none.
    if (!nearestWithMeta)
      return;

    // Turn the meta tag definitions into actual elements in the head.
    (nearestWithMeta.meta.metaTags as any).forEach((tagDef: any) => {
      const tag = document.createElement("meta");

      Object.keys(tagDef).forEach((key) => {
        tag.setAttribute(key, tagDef[key]);
      });

      // We use this to track which meta tags we create, so we don't interfere with other ones.
      tag.setAttribute("data-vue-router-controlled", "");

      // Add the meta tags to the document head.
      document.head.appendChild(tag);
    });
  }

  router.beforeEach(async (to, from, next) => {
    if (to?.meta?.externalUrl) {
      window.open(to.meta.externalUrl?.toString(), "_blank");
      return false;
    }
    await nextTick();
    const navigationStore = useNavigationStore();
    const authStore = useAuthStore();
    const appAlertsStore = useAppAlertsStore();
    appAlertsStore.clear();

    appInsights && appInsights.startTrackPage(to.name?.toString());
    // page alerts are not persisted across pages. If we need to show
    // an alert when routing to a new page, route first then display the alert
    try {
      navigationStore.routeUpdating = true;
      handlePageTitle(to, from);
      await authStore.waitForInitComplete();
      const routeState = await checkAuthentication(to);

      // if the user is not logged in, or is heading to an unauthenticated page, then we don't need to check authorization
      // and can just continue to the page.
      if (routeState === RouteAuthenticationState.NoAuthenticationRequired || routeState === RouteAuthenticationState.NotAuthenticatedHeadingToLoginPage) {
        return next();
      }

      await checkAuthorization(to);
      next();
    }
    catch (err) {
      next(err as any);
      navigationStore.routeUpdating = false;
    }
  });

  router.afterEach(async (to, _) => {
    await nextTick();

    appInsights
    && appInsights.stopTrackPage(
      to.name?.toString(),
      `${location.protocol}//${location.host}${to.fullPath}`,
    );
  });

  return router;
}

export default makeRouter;
