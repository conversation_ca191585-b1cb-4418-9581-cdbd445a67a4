import config from "devextreme/core/config";
// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import NProgress from "nprogress";
import { createPinia } from "pinia";
import { createApp } from "vue";
import App from "@/App.vue";
import { setupAppInsights } from "@/config/app-insights";
import registerGlobalVueComponents from "@/config/component-registration";
import setupDevTools from "@/config/dev-tools";
import makeRouter from "@/router";
import { GetConstants, SetConstants } from "@/utils/constants";
import setupGlobalVueErrorHandler from "@/utils/vue-error-handler";

// set up constants for the app
SetConstants({
  backendURI:
    import.meta.env.VITE_BACKEND_URI ?? `${window.location.protocol}//${window.location.host}`,
  version: import.meta.env.VITE_VERSION,
  docsRoot: import.meta.env.VITE_DOCS_ROOT,
  environmentMode: import.meta.env.MODE?.toLowerCase() as "production" | "development",
  enableAppInsights: import.meta.env.VITE_ENABLE_APP_INSIGHTS === "true",
  appInsightsInstrumentationKey: import.meta.env.VITE_APP_INSIGHTS_INSTRUMENTATION_KEY,
  viteHotContext: import.meta.hot,
  devextremeLicense: import.meta.env.VITE_DEVEXTREME_LICENSE,
  checklistAvailable: import.meta.env.VITE_CHECKLIST_AVAILABLE === "true",
});

const { devextremeLicense } = GetConstants();
config({ licenseKey: devextremeLicense });

const pinia = createPinia();
const router = makeRouter();
NProgress.configure({ easing: "ease", speed: 500 });

const app = createApp(App);
app.use(pinia);
setupAppInsights(app);
setupGlobalVueErrorHandler(app);
registerGlobalVueComponents(app);

app.use(router);

setupDevTools(app);

app.mount("#app");
