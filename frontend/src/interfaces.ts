import { IMaintenanceItemOrder } from "./api/backend/generated/interfaces";
import { IGlobalMediaResponse, ILocalMediaResponse } from "./api/backend/generated/responses";

export interface IComputerSelectBoxViewModel {
  id: number;
  name: string;
  tenantName: string;
  isOnline: boolean;
  tenantId: number;
  excludeFromMaintenance: boolean;
}

export type ComplianceFilter = "all" | "compliant" | "non-compliant" | "remaining";

export type WindowExtended = Window & { debugLog: (args: unknown) => void };

export interface IPersonSelectBoxModel {
  id: number;
  name: string;
  emailAddress: string;
  tenantId: number;
}

export type MaintenanceItemOrderWithData = IMaintenanceItemOrder & {
  icon?: IGlobalMediaResponse | ILocalMediaResponse;
  name?: string;
  type?: string;
};

export enum RbacTab {
  Users,
  Roles,
  PermissionOverrides,
}
