import { <PERSON>s<PERSON><PERSON> } from "@/api/backend/generated/sdk/RolesApi";
import { SchedulesApi } from "@/api/backend/generated/sdk/SchedulesApi";
import { UserRolesApi } from "@/api/backend/generated/sdk/UserRolesApi";
import { ApplicationLocksApi } from "../generated/sdk/ApplicationLocksApi";
import { ApplicationLogsApi } from "../generated/sdk/ApplicationLogsApi";
import { AuditsApi } from "../generated/sdk/AuditsApi";
import { AuthApi } from "../generated/sdk/AuthApi";
import { AzureApi } from "../generated/sdk/AzureApi";
import { AzureErrorsApi } from "../generated/sdk/AzureErrorsApi";
import { BillingApi } from "../generated/sdk/BillingApi";
import { ChangeRequestsApi } from "../generated/sdk/ChangeRequestsApi";
import { ComputersApi } from "../generated/sdk/ComputersApi";
import { DynamicIntegrationTypesApi } from "../generated/sdk/DynamicIntegrationTypesApi";
import { GettingStartedApi } from "../generated/sdk/GettingStartedApi";
import { InventoryTasksApi } from "../generated/sdk/InventoryTasksApi";
import { LicensesApi } from "../generated/sdk/LicensesApi";
import { MaintenanceActionsApi } from "../generated/sdk/MaintenanceActionsApi";
import { MaintenanceSessionsApi } from "../generated/sdk/MaintenanceSessionsApi";
import { MediaApi } from "../generated/sdk/MediaApi";
import { MetricsApi } from "../generated/sdk/MetricsApi";
import { NotificationsApi } from "../generated/sdk/NotificationsApi";
import { OauthApi } from "../generated/sdk/OauthApi";
import { PersonsApi } from "../generated/sdk/PersonsApi";
import { PreferencesApi } from "../generated/sdk/PreferencesApi";
import { ProviderAgentsApi } from "../generated/sdk/ProviderAgentsApi";
import { ProviderTypesApi } from "../generated/sdk/ProviderTypesApi";
import { ScriptsApi } from "../generated/sdk/ScriptsApi";
import { SmtpConfigApi } from "../generated/sdk/SmtpConfigApi";
import { SoftwareApi as softwareApiGenerated } from "../generated/sdk/SoftwareApi";
import { SystemApi } from "../generated/sdk/SystemApi";
import { TargetAssignmentsApi as TargetAssignmentsApiV2 } from "../generated/sdk/TargetAssignmentsApi";
import { TenantsApi } from "../generated/sdk/TenantsApi";
import { UsersApi } from "../generated/sdk/UsersApi";
import brandingsApi from "./brandings-api";
import chocolateyApi from "./chocolatey-api";
import immybotApi from "./immybot-api";
import maintenanceTasksApi from "./maintenance-tasks-api";
import providerLinksApi from "./provider-links-api";
import softwareApi from "./software-api";
import syncsApi from "./syncs-api";
import tagsApi from "./tags-api";
import targetAssignmentsApi from "./target-assignments-api";

const authApi = new AuthApi();
const azureApi = new AzureApi();
const azureErrorsApi = new AzureErrorsApi();
const billingApi = new BillingApi();
const computersApi = new ComputersApi();
const dynamicIntegrationTypesApi = new DynamicIntegrationTypesApi();
const inventoryTasksApi = new InventoryTasksApi();
const licensesApi = new LicensesApi();
const locksApi = new ApplicationLocksApi();
const maintenanceActionsApi = new MaintenanceActionsApi();
const mediaApi = new MediaApi();
const metricsApi = new MetricsApi();
const oauthApi = new OauthApi();
const providerAgentsApi = new ProviderAgentsApi();
const providerTypesApi = new ProviderTypesApi();
const sessionsApi = new MaintenanceSessionsApi();
const smtpConfigApi = new SmtpConfigApi();
const scriptsApi = new ScriptsApi();
const tenantsApi = new TenantsApi();
const usersApi = new UsersApi();
const applicationLogsApi = new ApplicationLogsApi();
const auditsApi = new AuditsApi();
const notificationsApi = new NotificationsApi();
const targetAssignmentsApiV2 = new TargetAssignmentsApiV2();
const changeRequestsApi = new ChangeRequestsApi();
const systemApi = new SystemApi();
const personsApi = new PersonsApi();
const gettingStartedApi = new GettingStartedApi();
const schedulesApi = new SchedulesApi();
const preferencesApi = new PreferencesApi();
const rolesApi = new RolesApi();
const userRolesApi = new UserRolesApi();
export {
  applicationLogsApi,
  auditsApi,
  authApi,
  azureApi,
  azureErrorsApi,
  billingApi,
  brandingsApi,
  changeRequestsApi,
  chocolateyApi,
  computersApi,
  dynamicIntegrationTypesApi,
  gettingStartedApi,
  immybotApi,
  inventoryTasksApi,
  licensesApi,
  locksApi,
  maintenanceActionsApi,
  maintenanceTasksApi,
  mediaApi,
  metricsApi,
  notificationsApi,
  oauthApi,
  personsApi,
  preferencesApi,
  providerAgentsApi,
  providerLinksApi,
  providerTypesApi,
  rolesApi,
  schedulesApi,
  scriptsApi,
  sessionsApi,
  smtpConfigApi,
  softwareApi,
  softwareApiGenerated,
  syncsApi,
  systemApi,
  tagsApi,
  targetAssignmentsApi,
  targetAssignmentsApiV2,
  tenantsApi,
  userRolesApi,
  usersApi,
};
