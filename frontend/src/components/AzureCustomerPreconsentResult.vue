<template>
  <ul>
    <template v-for="(m, ind) in preconsentResult.messages">
      <li
        v-if="
          m != null
            && !(
              (('error' in m && m.error != null)
                || ('azureErrorId' in m && m.azureErrorId != null))
              && m.isErrorNonFatal
            )
        "
        :key="ind"
      >
        <pre
          style="white-space: pre-wrap"
          :class="
            (!('error' in m && m.error != null)
              && !('azureErrorId' in m && m.azureErrorId != null))
              || m.isErrorNonFatal
              ? 'text-primary'
              : 'text-danger'
          "
        >{{ m.message }}</pre>
        <AzureError
          v-if="'error' in m && m.error != null && !m.isErrorNonFatal"
          :azure-error="m.error.azureError"
        />
        <AzureError
          v-if="'azureErrorId' in m && m.azureErrorId != null && !m.isErrorNonFatal"
          :azure-error-log-id="m.azureErrorId"
        />
      </li>
    </template>
  </ul>
</template>

<script setup lang="ts">
import {
  IAzureCustomerPreconsentResult,
  IAzureCustomerPreconsentResultNotificationInput,
} from "@/api/backend/generated/contracts";

defineProps<{
  preconsentResult:
    | IAzureCustomerPreconsentResult
    | IAzureCustomerPreconsentResultNotificationInput;
}>();
</script>
