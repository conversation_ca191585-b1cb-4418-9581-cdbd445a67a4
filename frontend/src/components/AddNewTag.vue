<template>
  <div class="new-tag d-flex align-items-center flex-grow-1">
    <v-select
      :id="id"
      class="quick-new-tag-select"
      placeholder="Select tags..."
      :clearable="false"
      :select-on-tab="true"
      label="name"
      :options="filteredTags"
      :reduce="reduceFn"
      :model-value="selection"
      @update:model-value="onTagSelected"
    >
      <template #option="option">
        <TagPill size="lg" class="my-2" :model-value="option" />
      </template>
      <template #selected-option="option">
        {{ option.text }}
      </template>
    </v-select>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import { IAddTagsRequest, IGetTagResponse, TagType } from "@/api/backend/generated/contracts";
import { computersApi, personsApi, tenantsApi } from "@/api/backend/v1";
import { useTagsStore } from "@/store/pinia";

import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { toast } from "@/utils/toast";

export interface TagOption {
  name: string;
  id: number;
  color?: string;
}

interface IProps {
  tagType: TagType;
  entityIds?: number[];
  id?: string;
}

const props = withDefaults(defineProps<IProps>(), {
  entityIds: () => [],
  id: undefined,
});

const emit = defineEmits<{
  (e: "tag:added", tag: TagOption): void;
}>();

const appAlertsStore = useAppAlertsStore();

const tagsStore = useTagsStore();

const selection = ref<number | null>(null);

const filteredTags = computed(() => {
  const tags = tagsStore.all.reduce((agg: TagOption[], cur: IGetTagResponse) => {
    const option: TagOption = {
      name: cur.name,
      id: cur.id,
      color: cur.color,
    };
    agg.push(option);
    return agg;
  }, []);
  return tags;
});

const reduceFn = (tag: TagOption) => tag.id;

async function onTagSelected(tagId: number) {
  try {
    selection.value = null;

    // save tag
    if (props.entityIds.length) {
      const req: IAddTagsRequest = {
        tagIds: [tagId],
        entityIds: props.entityIds,
      };

      switch (props.tagType) {
        case TagType.Computer:
          await computersApi.addTags(req);
          break;
        case TagType.Tenant:
          await tenantsApi.addTags(req);
          break;
        case TagType.Person:
          await personsApi.addTags(req);
          break;
        default:
          throw `Tag type: ${props.tagType} is not allowed`;
      }
      toast.success("Tag added");
    }

    // emit tag
    const tag = filteredTags.value.find(a => a.id === tagId);
    if (tag)
      emit("tag:added", tag);
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while adding the tag",
      details: err,
    });
  }
}

onMounted(async () => {
  await tagsStore.getAll();
});
</script>

<style lang="scss" scoped>
select {
  border-radius: 20px;
  height: 2em;
  line-height: 1;
}

.input-group-text {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  line-height: 1;
}

.quick-new-tag-select {
  width: 100%;
}

.badge {
  font-size: 100%;
}
</style>
