<template>
  <li
    id="nav-item-account"
    ref="root"
    data-testid="nav-item-account"
    class="header-nav-dropdown-item nav-item dropdown"
  >
    <a
      class="nav-link dropdown-toggle"
      data-toggle="dropdown"
      href="#"
      role="button"
      aria-expanded="false"
    >
      <i class="fal fa-user mr-2" />
      {{ currentUser }}
    </a>
    <div class="my-devices-list dropdown-menu">
      <div class="text-center">
        <p class="text-muted m-2">
          <strong>My Devices</strong>
        </p>
        <p v-if="loadingMyDevices" class="m-0">
          <i class="fa fa-spin fa-spinner" />
        </p>
        <p v-else-if="failedToLoadMyDevices" class="text-danger">
          <em> Failed to load devices </em>
        </p>
        <p v-else-if="!myDevices.length">
          <em>No devices</em>
        </p>
      </div>
      <ImmyDropdownItem
        v-for="device in myDevices"
        :key="device.id"
        as-list-item
        :title="`This device is ${device.isOnline ? 'online' : 'offline'}`"
        :to="{ name: 'Computer Details', params: { computerId: device.id } }"
      >
        <i
          class="fas fa-power-off mr-2"
          :class="{ 'text-success': device.isOnline, 'text-danger': !device.isOnline }"
        />
        {{ device.name }}
      </ImmyDropdownItem>
      <hr class="dropdown-divider">
      <ImmyDropdownItem
        v-if="authStore.hasManagementAccess && checklistAvailable && permissionStore.can('getting_started:view')"
        data-testid="getting-started-dropdown-link"
        as-list-item
        to="/checklist"
      >
        Getting Started
      </ImmyDropdownItem>
      <ImmyDropdownItem
        v-else
        data-testid="getting-started-dropdown-link"
        as-list-item
        @click="$emit('show-getting-started-wizard')"
      >
        Getting Started
      </ImmyDropdownItem>
      <ImmyDropdownItem v-if="authStore.hasManagementAccess" as-list-item :to="{ name: 'Preferences' }">
        Preferences
      </ImmyDropdownItem>
      <ImmyDropdownItem
        as-list-item
        title="Light/Dark theme toggle"
        data-testid="nav-item-account-theme"
        @click="onThemeChange"
      >
        <i v-if="isDarkMode" class="fa-solid fa-sun-bright mr-2" />
        <i v-else class="fa-solid fa-moon mr-2" />Theme
      </ImmyDropdownItem>
      <ImmyDropdownItem as-list-item :href="logoutPath">
        Logout
      </ImmyDropdownItem>
    </div>
  </li>
</template>

<script lang="ts" setup>
import type { IMyComputerResponse } from "@/api/backend/generated/responses";
import { computed, onMounted, onUnmounted, ref } from "vue";
import { UserAuthRoutes } from "@/api/backend/generated/contracts";
import { computersApi } from "@/api/backend/v1";
import { useAuthStore } from "@/store/pinia/auth-store";
import { usePermissionStore } from "@/store/pinia/permission-store";
import { useThemeStore } from "@/store/pinia/theme-store";
import { GetConstants } from "@/utils/constants";

defineEmits(["show-getting-started-wizard"]);

const { backendURI, checklistAvailable } = GetConstants();
const logoutPath = `${backendURI}/${UserAuthRoutes.Logout}`;

const authStore = useAuthStore();
const permissionStore = usePermissionStore();
const themeStore = useThemeStore();

const myDevices = ref<IMyComputerResponse[]>([]);
const loadingMyDevices = ref(false);
const failedToLoadMyDevices = ref(false);

const currentUser = computed(() => authStore.emailAddress);
const isDarkMode = computed(() => themeStore.theme === "dark");

let mutationObserver: MutationObserver | null = null;
const root = ref();

onMounted(() => {
  mutationObserver = new MutationObserver(async (mutations) => {
    for (const mutation of mutations) {
      if (
        mutation.attributeName === "class"
        && (mutation.target as HTMLElement).classList.contains("show")
      )
        await loadMyDevices();
    }
  });
  mutationObserver.observe(root.value, {
    attributes: true,
  });
});

onUnmounted(() => {
  mutationObserver?.disconnect();
});

function onThemeChange() {
  themeStore.changeTheme();
}

async function loadMyDevices() {
  try {
    myDevices.value = [];
    loadingMyDevices.value = true;
    failedToLoadMyDevices.value = false;
    myDevices.value = await computersApi.getMyComputers();
  }
  catch {
    failedToLoadMyDevices.value = true;
  }
  finally {
    loadingMyDevices.value = false;
  }
}
</script>

<style lang="scss" scoped>
:deep(.dropdown-menu) {
  .dropdown-item {
    padding: 1rem;
  }
}

@media (max-width: 1200px) {
  :deep(.dropdown-menu) {
    left: 0;
  }
}

@media (max-width: $breakpoint-xl-max) {
  :deep(.dropdown-menu) {
    position: absolute !important;
    width: inherit;
    text-align: center;
    margin: 0px;
  }
}
:deep(.dropdown-menu) {
  position: absolute !important;
}
</style>
