<script setup lang="ts">
import { IDeploymentTarget, IPersonTargetInfo } from "@/store/pinia/deployment-store";

defineProps<{
  data: IDeploymentTarget<IPersonTargetInfo>;
}>();
</script>

<template>
  <div class="d-flex align-items-center pii">
    <router-link :to="`/settings/persons/${data.info.id}`">
      {{ data.info.name }}
    </router-link>
  </div>
</template>

<style scoped lang="scss">

</style>
