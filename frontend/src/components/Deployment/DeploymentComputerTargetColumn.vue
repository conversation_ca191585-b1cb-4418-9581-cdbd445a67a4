<script setup lang="ts">
import { IComputerTargetInfo, IDeploymentTarget } from "@/store/pinia/deployment-store";

defineProps<{
  data: IDeploymentTarget<IComputerTargetInfo>;
}>();
</script>

<template>
  <div>
    <div class="d-flex align-items-center">
      <router-link :to="`/computers/${data.info.id}`">
        {{ data.info.name }}
      </router-link>
      <i
        :title="data.info.isOnline ? 'Online' : 'Offline'"
        class="ml-2 fal fa-bolt"
        :class="{
          'text-success': data.info.isOnline,
          'text-danger': !data.info.isOnline,
        }"
      />
    </div>
    <template v-if="!data.info.onboarded">
      <em class="text-warning font-xxs font-weight-bold">Computer is not onboarded</em>
    </template>
    <template v-else>
      <div class="secondary-detail-items">
        <span v-if="data.info.os" title="OS">{{ data.info.os }}</span>
      </div>
      <div class="secondary-detail-items">
        <span v-if="data.info.serialNumber" title="Serial">{{ data.info.serialNumber }}</span>
      </div>
    </template>
  </div>
  <div class="primary-detail-items">
    <span v-if="data.info.tenantName" class="pii" title="Tenant Name">{{ data.info.tenantName }}</span>
    <span v-if="data.info.primaryPersonName" class="pii" title="Primary User">{{
      data.info.primaryPersonName
    }}</span>
  </div>
  <computer-tags
    class="mt-1 font-xs"
    :computer-id="data.info.id"
    :is-portable="data.info.isPortable"
    :is-desktop="data.info.isDesktop"
    :is-domain-controller="data.info.isDomainController"
    :is-server="data.info.isServer"
    :onboarding-status="data.info.onboardingStatus"
    :is-sandbox="data.info.sb"
  />
</template>

<style scoped lang="scss">

</style>
