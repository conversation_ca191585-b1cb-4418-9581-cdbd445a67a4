<template>
  <div>
    <strong class="d-block text-muted pii">{{ updatedBy }}</strong>
    <template v-if="isNew">
      <strong class="text-success">New</strong>
      &middot;
    </template>
    <em :title="formattedDate">Updated {{ formattedUpdateDate }}</em>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { formatDate, getHumanReadableDate, isWithin24Hours } from "@/utils/misc";

interface IProps {
  updatedBy?: string;
  updatedDateUtc: string;
  createdDateUtc?: string;
}

const props = withDefaults(defineProps<IProps>(), {
  updatedBy: "System",
  createdDateUtc: "",
});

const isNew = computed(() => isWithin24Hours(props.createdDateUtc));
const formattedUpdateDate = computed(() => getHumanReadableDate(props.updatedDateUtc));
const formattedDate = computed(() => formatDate(props.updatedDateUtc));
</script>

<style lang="scss" scoped></style>
