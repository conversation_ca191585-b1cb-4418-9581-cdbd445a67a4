<template>
  <div class="logs-panel-item d-flex flex-column align-items-stretch mb-1">
    <div class="log-timestamp">
      <span class="text-muted" :title="humanReadableTime">{{ formatLogTime }}</span>
      <template v-if="log.updatedTime">
        <span class="text-muted" :title="humanReadableUpdatedTime">&nbsp;&mdash;&nbsp;{{ formatLogUpdatedTime }}</span>
      </template>
      <span v-if="formatDuration" class="text-muted"> ({{ formatDuration }}) </span>
    </div>
    <div class="log-message">
      <CopyToClipboardButton class="p-0" variant="link" :text="log.message" />
      {{ logMessage }}
    </div>
    <ProgressLog v-if="isProgressLog" class="pt-1" :log="log" />
    <div v-else-if="isCommandSessionLog" class="log-scripts mb-2">
      <ScriptHighlight
        v-if="log.script"
        class="mt-2"
        :class="{ 'mx-3': log.isPrimary }"
        :script-id="log.scriptId"
        :script="log.script"
        :script-output="log.scriptOutput"
        :script-type="log.scriptType"
        :script-language="log.scriptLanguage ?? powerShellScriptLanguage"
        :script-parameters="log.scriptParameters"
        :param-block-parameters="log.paramBlockParameters"
        :computer-name="computerName"
        :tenant-name="tenantName"
        :maintenance-session-id="log.maintenanceSessionId"
        :maintenance-action-id="log.maintenanceActionId"
        :session-log-id="log.id"
        :variant="variant"
        background-mode="light"
        expand-text="Script is truncated.  Click to expand..."
      />
      <ScriptHighlight
        v-if="log.scriptOutput != null"
        class="terminal mt-2"
        :class="{ 'mx-3 mb-3': log.isPrimary }"
        is-output
        :script="log.scriptOutput"
        :script-language="log.scriptLanguage ?? powerShellScriptLanguage"
        expand-text="Script result is truncated.  Click to expand.."
        :use-syntax="false"
        :is-truncated="!log.isPrimary"
        :variant="variant"
        background-mode="light"
      />
    </div>
    <div v-else-if="isCommandResultSessionLog" class="log-scripts mb-2">
      <ScriptHighlight
        :script="log.scriptOutput ?? ''"
        is-output
        :script-language="log.scriptLanguage ?? powerShellScriptLanguage"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "vue";
import {
  IGetMaintenanceSessionLogResponse,
  ScriptLanguage,
  SessionLogType,
} from "@/api/backend/generated/contracts";
import { formatDate, getElapsedTime, getHumanReadableDate } from "@/utils/misc";
import { ButtonVariants } from "../ImmyButton.vue";

export default defineComponent({
  name: "LogsPanelItem",
  props: {
    log: {
      type: Object as PropType<IGetMaintenanceSessionLogResponse>,
      required: true,
    },
    computerName: {
      type: String as PropType<string | undefined>,
      required: false,
      default: undefined,
    },
    tenantName: {
      type: String as PropType<string | undefined>,
      required: false,
      default: undefined,
    },
    variant: {
      type: String as PropType<ButtonVariants | undefined>,
      required: false,
      default: undefined,
    },
  },
  computed: {
    powerShellScriptLanguage(): ScriptLanguage {
      return ScriptLanguage.PowerShell;
    },
    humanReadableTime(): string {
      return getHumanReadableDate(this.log.time);
    },
    humanReadableUpdatedTime(): string {
      return getHumanReadableDate(this.log.updatedTime);
    },
    formatLogTime(): string {
      return formatDate(new Date(this.log.time), "h:MM:sstt");
    },
    formatLogUpdatedTime(): string | null {
      return this.log.updatedTime != null
        ? formatDate(new Date(this.log.updatedTime), "h:MM:sstt")
        : null;
    },
    formatDuration(): string | null {
      if (!!this.log.time && !!this.log.updatedTime)
        return getElapsedTime(this.log.time, this.log.updatedTime);

      return null;
    },
    isProgressLog(): boolean {
      return this.log.sessionLogType === SessionLogType.Progress;
    },
    isCommandResultSessionLog(): boolean {
      return this.log.sessionLogType === SessionLogType.CommandResult;
    },
    isCommandSessionLog(): boolean {
      return this.log.sessionLogType === SessionLogType.Command;
    },
    logMessage(): string {
      if (this.isCommandResultSessionLog)
        return "Output";
      return this.log.message ?? "";
    },
  },
});
</script>

<style lang="scss" scoped>
.log-timestamp {
  line-height: 1;
}
.logs-panel-item {
  overflow: auto;
  font-family: "Roboto Mono", monospace;
  font-size: 0.75rem;
}

.log-message {
  white-space: pre-wrap;
}
</style>
