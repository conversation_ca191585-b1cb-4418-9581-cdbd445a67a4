<template>
  <div :class="{ ['no-tabs']: !groupedByPhase }">
    <span v-if="panelTitle != null">{{ panelTitle }}</span>
    <Tabs
      v-else-if="groupedByPhase"
      v-model:active-item="selectedPhaseId"
      :items="logPhases"
      scrollable
    >
      <template #tab-content="{ item }">
        <div class="d-flex flex-column justify-content-center h-100">
          <div
            class="div"
            :style="
              item.stageName
                ? {
                  'line-height': 1.2,
                }
                : undefined
            "
          >
            <span class="mx-2">{{ item.phaseName }}</span>
            <i :class="`mx-2 ${item.icon}`" :title="item.iconTitle" />
          </div>
          <small v-if="item.stageName" style="line-height: 11px" class="mx-2"><em>{{ item.stageName }} stage</em></small>
        </div>
      </template>
    </Tabs>
    <div
      class="auto-scroller-outer log-phase-children-container pt-3 pb-4 px-3"
      :class="phaseChildrenContainerStateClass"
    >
      <span class="auto-scroller-content-aligner" />
      <div v-if="visibleLogs != null" class="auto-scroller-inner" style="">
        <LogsPanelItemNew
          v-for="log in visibleLogs"
          :key="log.time"
          class="child-log"
          :class="
            log && log.isPrimary
              ? 'pl-1 mb-2 border-left border-primary border-width-2 primary-log-item bg-primary-light'
              : undefined
          "
          :title="log.isPrimary ? 'Primary log' : ''"
          :log="log"
          :variant="variant"
          :computer-name="computerName"
          :tenant-name="tenantName"
          @script-reran="$emit('scriptReran')"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "vue";
import {
  IGetMaintenanceSessionLogResponse,
  IGetMaintenanceSessionPhaseResponse,
  IGetMaintenanceSessionStageResponse,
  IUpdateMaintenanceSessionStageResource,
  SessionPhaseStatus,
  SessionStatus,
} from "@/api/backend/generated/contracts";
import EnumTextHelpers from "@/helpers/enums/EnumTextHelpers";
import { formatDate, groupByToMap } from "@/utils/misc";
import { ITabItem, State } from "./Tabs.vue";

class Privates {
  tabsListObserver: MutationObserver | undefined;
  arrowElement: HTMLDivElement | undefined;
  bodyResizeObserver: ResizeObserver | undefined;
}

interface ILogPhase extends ITabItem {
  phaseId: number;
  phaseName: string;
  stageName: string | undefined;
  phaseStatus: SessionPhaseStatus | undefined;
  children: IGetMaintenanceSessionLogResponse[];
  icon: string;
  iconTitle: string;
  active?: boolean;
  state: State;
}

export default defineComponent({
  name: "LogsPanel",
  props: {
    logs: {
      type: Array as PropType<IGetMaintenanceSessionLogResponse[]>,
      required: true,
    },
    stages: {
      type: Array as PropType<
        (IGetMaintenanceSessionStageResponse | IUpdateMaintenanceSessionStageResource)[] | undefined
      >,
      required: false,
      default: undefined,
    },
    phases: {
      type: Array as PropType<IGetMaintenanceSessionPhaseResponse[] | undefined>,
      required: false,
      default: undefined,
    },
    groupedByPhase: {
      type: Boolean,
      default: true,
    },
    panelTitle: {
      type: String as PropType<string | undefined>,
      required: false,
      default: undefined,
    },
    expandedPhaseId: {
      type: Number as PropType<number | undefined>,
      required: false,
      default: undefined,
    },
    computerName: {
      type: String as PropType<string | undefined>,
      required: false,
      default: undefined,
    },
    tenantName: {
      type: String as PropType<string | undefined>,
      required: false,
      default: undefined,
    },
    sessionStatus: {
      type: Number,
      required: false,
      default: null,
    },
  },
  emits: ["scriptReran", "update:expandedPhaseId"],
  setup() {
    const privates = new Privates();
    return {
      privates,
    };
  },
  data() {
    return {
      selectedPhaseId: this.expandedPhaseId,
      activeTabDomRect: null as DOMRect | null,
    };
  },
  computed: {
    stageNames(): Map<number, string> | undefined {
      return this.stages?.reduce((stageMap, stage) => {
        const m = EnumTextHelpers.SessionStageType.GetOptionsByValue(stage.type);
        return stageMap.set(stage.id, m.text);
      }, new Map<number, string>());
    },
    logPhases(): ILogPhase[] {
      return [...this.logsGroupedIntoPhases.values()].filter(
        p => p.phaseStatus !== SessionPhaseStatus.Skipped,
      );
    },
    logsGroupedIntoPhases(): Map<number, ILogPhase> {
      if (this.phases == null || !this.groupedByPhase)
        return new Map<number, ILogPhase>();
      const groupedLogs = groupByToMap(this.logs, l => l.sessionPhaseId ?? undefined);
      const ret = this.phases.reduce((phaseMap, cur) => {
        const stageName
          = cur.maintenanceSessionStageId != null
            ? this.stageNames?.get(cur.maintenanceSessionStageId)
            : undefined;
        const state: Pick<ILogPhase, "icon" | "state" | "iconTitle"> = (() => {
          switch (cur.status) {
            case SessionPhaseStatus.Succeeded:
              return {
                icon: "fal fa-check",
                iconTitle: "Phase finished successfully",
                state: "success",
              };
            case SessionPhaseStatus.Failed:
              return {
                icon: "fal fa-triangle-exclamation",
                iconTitle: "Phase failed",
                state: "danger",
              };
            case SessionPhaseStatus.Running:
              if (this.sessionStatus === SessionStatus.Cancelled) {
                return {
                  icon: "fal fa-ban",
                  iconTitle: "Phase was cancelled",
                  state: "warning",
                };
              }
              return {
                icon: "fal fa-spinner fa-spin",
                iconTitle: "Phase is running",
                state: "primary",
              };
            case SessionPhaseStatus.Skipped:
              return {
                icon: "fal fa-message-slash",
                iconTitle: "Phase was skipped",
                state: "warning",
              };
            case SessionPhaseStatus.Unstarted:
              return {
                icon: "",
                iconTitle: "Phase has not yet started",
                state: "secondary",
              };
          }
        })();
        const tooltip = [
          cur.dateStartedUtc ? `Phase Started: ${formatDate(cur.dateStartedUtc)}` : null,
          cur.dateCompletedUtc
            ? `Phase ${
              cur.status === SessionPhaseStatus.Failed ? "Failed" : "Finished"
            }: ${formatDate(cur.dateCompletedUtc)}`
            : null,
        ]
          .filter(t => t != null)
          .join("\n");
        phaseMap.set(cur.id, {
          phaseId: cur.id,
          phaseName: cur.phaseName,
          phaseStatus: cur.status,
          stageName,
          children: groupedLogs.get(cur.id) ?? [],
          key: cur.id,
          tooltip,
          ...state,
        });
        return phaseMap;
      }, new Map<number, ILogPhase>());
      if (groupedLogs.has(undefined)) {
        ret.set(-1, {
          phaseId: -1,
          phaseName: "Logs without a phase",
          stageName: undefined,
          children: groupedLogs.get(undefined) ?? [],
          key: -1,
          icon: "",
          iconTitle: "",
          state: "secondary",
          phaseStatus: undefined,
        });
      }
      return ret;
    },
    selectedPhase(): ILogPhase | null {
      if (this.selectedPhaseId == null)
        return null;
      return this.logsGroupedIntoPhases.get(this.selectedPhaseId) ?? null;
    },
    visibleLogs(): IGetMaintenanceSessionLogResponse[] | undefined {
      if (this.groupedByPhase) {
        if (this.selectedPhase == null)
          return undefined;
        return this.selectedPhase.children;
      }
      else {
        return this.logs;
      }
    },
    phaseChildrenContainerStateClass(): string {
      if (this.groupedByPhase && this.selectedPhase)
        return `bg-${this.selectedPhase.state}-light border border-${this.selectedPhase.state} border-bottom-0 border-left-0 border-right-0`;

      return "bg-secondary-light";
    },
    arrowColorVar(): string | null {
      if (this.groupedByPhase && this.selectedPhase)
        return `var(--${this.selectedPhase.state})`;

      return null;
    },
    variant(): State | undefined {
      if (this.groupedByPhase && this.selectedPhase)
        return this.selectedPhase.state;

      return undefined;
    },
  },
  watch: {
    selectedPhaseId(val: number | undefined) {
      // let parent know when phase is selected
      this.$emit("update:expandedPhaseId", val);
    },
    expandedPhaseId(val: number | undefined) {
      // select new phase when parent instructs us to
      if (this.groupedByPhase) {
        if (val != null && this.logsGroupedIntoPhases.has(val)) {
          this.selectedPhaseId = val;
        }
        else {
          // if parent instructed us to select phase that doesn't exist, just select the first phase
          this.selectedPhaseId = (this.phases ?? [])[0]?.id;
        }
      }
    },
    activeTabDomRect(val: DOMRect | undefined) {
      const p = this.privates;
      if (val == null && p.arrowElement != null) {
        document.body.removeChild(p.arrowElement);
        p.arrowElement = undefined;
      }
      if (val != null) {
        if (p.arrowElement == null) {
          const arrowEl = document.createElement("div");
          arrowEl.style.position = "absolute";
          arrowEl.style.height = "19px";
          arrowEl.style.width = "0px";
          arrowEl.classList.add("arrow-decoration");
          document.body.appendChild(arrowEl);
          p.arrowElement = arrowEl;
        }
        p.arrowElement.style.top = `${window.scrollY - 10 + val.y + val.height}px`;
        p.arrowElement.style.left = `${window.scrollX + val.x + val.width / 2}px`;
        if (this.arrowColorVar)
          p.arrowElement.style.borderBottomColor = this.arrowColorVar;
      }
    },
    arrowColorVar(val: string | null) {
      const p = this.privates;
      if (val && p.arrowElement != null)
        p.arrowElement.style.borderBottomColor = val;
    },
    logPhases: {
      handler(val: ILogPhase[] | undefined, oldVal: ILogPhase[] | undefined) {
        if (val?.length && (oldVal == null || oldVal.length === 0)) {
          // we went from no parent logs to having some parent logs - select the first parent
          // unless there's already a selected phase
          if (this.selectedPhaseId != null && this.logsGroupedIntoPhases.has(this.selectedPhaseId))
            return;
          this.selectedPhaseId = val[0].phaseId;
        }
      },
      immediate: true,
    },
  },
  unmounted() {
    const p = this.privates;
    p.tabsListObserver?.disconnect();
    p.bodyResizeObserver?.disconnect();
    if (p.arrowElement != null)
      document.body.removeChild(p.arrowElement);
  },
  mounted() {
    if (this.groupedByPhase) {
      const tabsElement = this.$el.querySelector(".immy-tabs");
      if (tabsElement != null) {
        const p = this.privates;

        // when immy tabs changes (e.g. active class, title changes), reposition the active tab arrow
        const o = new MutationObserver(() => {
          const activeTab = tabsElement.querySelector(".immy-tab.active");
          if (activeTab != null)
            this.activeTabDomRect = activeTab.getBoundingClientRect();
        });
        o.observe(tabsElement, { attributes: true, subtree: true, characterData: true });
        p.tabsListObserver = o;

        // when document body size changes, reposition the active tab arrow
        const r = new ResizeObserver(() => {
          const activeTab = tabsElement.querySelector(".immy-tab.active");
          if (activeTab != null)
            this.activeTabDomRect = activeTab.getBoundingClientRect();
        });
        r.observe(document.body);
        p.bodyResizeObserver = r;
        const activeTab = tabsElement.querySelector(".immy-tab.active");
        if (activeTab != null)
          this.activeTabDomRect = activeTab.getBoundingClientRect();
      }
    }
  },
});
</script>

<style lang="scss" scoped>
$primaryLogBgColor: #3cb1da40;
$primaryLogBorderColor: #20a8d8;

.auto-scroller-outer {
  // make the inner scroll container scroll basis start from the end instead of the beginning
  // to auto-scroll when things are added. It's crazy (cool) that this works
  display: flex;
  flex-direction: column-reverse;
}

.auto-scroller-content-aligner {
  // grow & shrink as needed to fill empty space at the beginning (i.e. the bottom) of the scroll
  // container and push everything else in the container to the end (i.e. up)
  flex: 1 1 0%;
}

.log-phase-children-container {
  border-top-width: 2px !important;
  margin-top: 9px;
  max-height: 500px;
  min-height: 100px;
  overflow-y: auto;
}

.log-phase-children-container.state-danger {
  background: rgba(248, 108, 107, 0.15) !important;
}
.log-phase-children-container.state-success {
  background: rgba(77, 189, 116, 0.15) !important;
}

.primary-log-item {
  border-color: var(--primary) !important;
}

:deep(.script-highlight) {
  margin-left: 0.25rem !important;
  margin-right: 0.25rem !important;
  .script-btn {
    display: block !important;
    width: 100% !important;
  }
}

.arrow-decoration {
  border-style: solid;
  border-color: transparent transparent rgb(55, 148, 255);
  border-width: 6px;
  bottom: -6px;
  margin-left: -6px;
}
</style>
