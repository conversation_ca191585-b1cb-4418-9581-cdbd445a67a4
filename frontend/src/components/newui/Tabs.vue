<template>
  <div class="immy-tabs d-flex" :class="{ ['text-nowrap']: scrollable }">
    <div
      v-for="item in items"
      :key="item.key"
      class="immy-tab"
      :class="getItemClass(item)"
      :title="item.tooltip"
      @click="handleItemClick(item)"
    >
      <slot name="tab-content" :item="item">
        <span v-if="contentKey">{{ item[contentKey] }}</span>
      </slot>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "vue";

export type State = "success" | "warning" | "danger" | "primary" | "secondary";

export interface ITabItem {
  key: string | number;
  state?: State;
  tooltip?: string;
  [key: string]: unknown;
}

export default defineComponent({
  name: "Tabs",
  props: {
    items: {
      type: Array as PropType<ITabItem[]>,
      required: true,
    },
    contentKey: {
      type: String as PropType<string | undefined>,
      required: false,
      default: undefined,
    },
    activeItem: {
      type: [String, Number] as PropType<string | number | undefined>,
      required: false,
      default: undefined,
    },
    scrollable: {
      type: Boolean,
      default: true,
    },
  },
  emits: ["update:activeItem", "tabClick"],
  data() {
    return {
      activeItemKey: this.activeItem,
    };
  },
  watch: {
    activeItem(val: string | number | undefined) {
      this.activeItemKey = val;
    },
  },
  methods: {
    handleItemClick(item: ITabItem) {
      this.activeItemKey = item.key;
      this.$emit("update:activeItem", item.key);
      this.$emit("tabClick", item);
    },
    getItemClass(item: ITabItem): (string | object)[] {
      const classes = [`state-${item.state ?? "default"}`];
      if (item.key === this.activeItemKey)
        classes.push("active");
      return classes;
    },
  },
});
</script>

<style lang="scss">
:root[data-theme="dark"] {
  /* basicall mimic outline button styles */
  --tab-primary-color: #{$dark-primary};
  --tab-primary-bg: #{$dark-bg-primary};
  --tab-primary-active-color: #000;
  --tab-primary-active-bg: #{$dark-primary};

  --tab-secondary-color: #{$dark-text-primary};
  --tab-secondary-bg: #{$dark-bg-default2};
  --tab-secondary-active-color: #{$dark-text-primary};
  --tab-secondary-active-bg: #{adjust-color($dark-bg-default2, $lightness: -5%)};

  --tab-warning-color: #{$dark-warning};
  --tab-warning-bg: #{$dark-bg-warning};
  --tab-warning-active-color: #000;
  --tab-warning-active-bg: #{$dark-warning};

  --tab-danger-color: #{$dark-danger};
  --tab-danger-bg: #{$dark-bg-danger};
  --tab-danger-active-color: #000;
  --tab-danger-active-bg: #{$dark-danger};

  --tab-success-color: #{$dark-success};
  --tab-success-bg: #{$dark-bg-success};
  --tab-success-active-color: #000;
  --tab-success-active-bg: #{$dark-success};
}

:root[data-theme="light"] {
  --tab-primary-color: #{$light-text-primary};
  --tab-primary-bg: #{$light-bg-primary};
  --tab-primary-active-color: #000;
  --tab-primary-active-bg: #{$light-primary};

  --tab-secondary-color: #{$light-text-secondary};
  --tab-secondary-bg: #{$light-bg-tertiary};
  --tab-secondary-active-color: #000;
  --tab-secondary-active-bg: #{$light-bg-tertiary};

  --tab-warning-color: #{$light-text-primary};
  --tab-warning-bg: #{$light-bg-warning};
  --tab-warning-active-color: #fff;
  --tab-warning-active-bg: #{$light-warning};

  --tab-danger-color: #{$light-danger};
  --tab-danger-bg: #{$light-bg-danger};
  --tab-danger-active-color: #fff;
  --tab-danger-active-bg: #{$light-danger};

  --tab-success-color: #{$light-text-primary};
  --tab-success-bg: #{$light-bg-success};
  --tab-success-active-color: #000;
  --tab-success-active-bg: #{$light-success};
}
</style>

<style lang="scss" scoped>
$variants: primary, secondary, success, danger, warning;

@each $variant in $variants {
  .immy-tab {
    &.state-#{$variant} {
      background: var(--tab-#{$variant}-bg);
      color: var(--tab-#{$variant}-color);
      &.active {
        background: var(--tab-#{$variant}-active-bg);
        color: var(--tab-#{$variant}-active-color);
        &,
        i {
          color: var(--tab-#{$variant}-active-color);
        }
      }
    }
  }
}

.immy-tabs {
  @extend .immy-scrollbar;
}

.immy-tab {
  i {
    font-weight: bold;
  }
  // background: red;
  cursor: pointer;
  margin-left: -4px;
  padding: 3px 9px;
  clip-path: polygon(0 0, 8px 50%, 0 100%, calc(100% - 8px) 100%, 100% 50%, calc(100% - 8px) 0);

  &:first-child {
    clip-path: polygon(0 0, 0px 50%, 0 100%, calc(100% - 8px) 100%, 100% 50%, calc(100% - 8px) 0);
  }

  &:last-child {
    clip-path: polygon(0 0, 8px 50%, 0 100%, calc(100% - 0px) 100%, 100% 50%, calc(100% - 0px) 0);
  }

  &:only-child {
    clip-path: none;
  }
}
</style>
