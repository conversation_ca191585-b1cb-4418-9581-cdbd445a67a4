<template>
  <div
    class="progress-bar-stacking-grid rounded-0 position-relative"
    :class="[`bg-${variant}-light`]"
    @click="$emit('click', $event)"
  >
    <div
      class="progress-bar-stacking-grid immy-progress-bar h-100 font-weight-bold"
      role="progressbar"
      :aria-valuenow="modelValue"
      aria-valuemin="0"
      :aria-valuemax="max"
    >
      <span
        class="w-100 h-100 no-transition-if-reduced-motion"
        :style="progressBarStyle"
        :class="{
          [`progress-bar-${variant}-empty`]: modelValue < 52,
          [`progress-bar-${variant}-halfway`]: modelValue >= 52,
          ['progress-bar-striped']: striped,
          ['progress-bar-animated']: animated,
        }"
      />
      <span
        class="progress-bar-label d-flex align-items-center justify-content-center w-100 h-100"
        :class="{
          [`progress-bar-${variant}-empty`]: modelValue < 52,
          [`progress-bar-${variant}-halfway`]: modelValue >= 52,
        }"
      >
        <template v-if="showValue">{{ modelValue }}%</template>
        <slot v-else />
      </span>
    </div>
    <div :style="borderStyle" />
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { DevicePx } from "@/utils/DevicePx";

export default defineComponent({
  name: "ProgressBar",
  props: {
    max: {
      type: Number,
      required: true,
    },
    modelValue: {
      type: Number,
      required: true,
    },
    variant: {
      type: String,
      default: "primary",
    },
    striped: {
      type: Boolean,
      default: false,
    },
    animated: {
      type: Boolean,
      default: false,
    },
    showValue: {
      type: Boolean,
      default: false,
    },
    bordered: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["click"],
  data() {
    return {
      ...DevicePx.integration,
    };
  },
  computed: {
    progressBarStyle() {
      return {
        background: `var(--${this.variant})`,
        transform: `scaleX(${this.modelValue / 100.0})`,
        transformOrigin: "0 0",
      };
    },
    borderStyle() {
      if (this.bordered) {
        return {
          background: "transparent",
          width: "100%",
          border: `${this.alignPxWithDpx(1)}px solid var(--btn-${this.variant}-border-color)`,
          zIndex: "3",
        };
      }
      else {
        return {
          background: "transparent",
          width: "100%",
        };
      }
    },
  },
  mounted() {
    this.watchDevicePxRatio();
  },
  unmounted() {
    this.watchDevicePxRatio(false);
  },
});
</script>

<style lang="scss" scoped>
.immy-progress-bar {
  font-family: "Roboto Mono", monospace;
  font-size: 11.75px;
  transform-origin: 0 0;
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
  color: #fff;
}

.progress-bar-stacking-grid {
  display: grid !important;
  grid-template: 100% / 100%;
  > * {
    grid-area: 1 / 1;
  }
}

.progress-bar-animated {
  transition: transform 0.45s ease;
}

.progress-bar-label {
  z-index: 2;
}
</style>
