<template>
  <div class="branding-selector">
    <i v-if="loading" class="fa fa-spin fa-spinner" />
    <v-select
      v-else
      v-model="selectedBrandingId"
      :clearable="false"
      :select-on-tab="true"
      label="description"
      :options="brandings"
      :reduce="brandingReduceFn"
    >
      <template #option="option">
        {{ option.description }}
      </template>
      <template #selected-option="option">
        {{ option.description }}
      </template>
    </v-select>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import { IGetBrandingResponse } from "@/api/backend/generated/contracts";
import { useBrandingsStore } from "@/store/pinia";

interface IProps {
  modelValue?: number;
}

const props = defineProps<IProps>();
const emit = defineEmits<{
  (e: "update:modelValue", brandingId: number): void;
}>();
const brandingsStore = useBrandingsStore();
const brandings = ref<Array<IGetBrandingResponse>>([]);
const loading = ref<boolean>(false);
const selectedBrandingId = ref<number | undefined>(props.modelValue);

watch(
  () => props.modelValue,
  (val) => {
    if (val === selectedBrandingId.value)
      return;
    selectedBrandingId.value = val;
  },
);

watch(selectedBrandingId, (val) => {
  emit("update:modelValue", val as number);
});

onMounted(async () => {
  try {
    loading.value = true;
    await loadBrandings();
  }
  finally {
    loading.value = false;
  }
});

function brandingReduceFn(branding: { id: number }) {
  return branding.id;
}
async function loadBrandings() {
  brandings.value = await brandingsStore.getAll();
}
</script>

<style lang="scss" scoped></style>
