<template>
  <div
    v-if="showAlert"
    role="alert"
    class="alert"
    :class="classes"
    aria-live="polite"
    aria-atomic="true"
  >
    <button
      v-if="dismissible"
      type="button"
      class="close"
      data-dismiss="alert"
      aria-label="Close"
      @click="dismiss"
    >
      <span aria-hidden="true">&times;</span>
    </button>
    <slot />
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    show: {
      type: Boolean,
      default: false,
    },
    fade: {
      type: Boolean,
      default: false,
    },
    dismissible: {
      type: Boolean,
      default: false,
    },
    variant: {
      type: String,
      default: "primary",
    },
  },
  emits: ["dismissed"],
  data() {
    return {
      showAlert: this.modelValue || this.show,
    };
  },
  computed: {
    classes(): string {
      let ret = `alert-${this.variant}`;

      if (this.dismissible)
        ret += " alert-dismissible";

      if (this.showAlert)
        ret += " show";

      if (this.fade)
        ret += " fade";

      return ret;
    },
  },
  watch: {
    modelValue(val) {
      this.showAlert = val;
    },
    show(val) {
      this.showAlert = val;
    },
  },
  methods: {
    dismiss() {
      this.$emit("dismissed");
      this.showAlert = false;
    },
  },
});
</script>

<style lang="scss" scoped></style>
