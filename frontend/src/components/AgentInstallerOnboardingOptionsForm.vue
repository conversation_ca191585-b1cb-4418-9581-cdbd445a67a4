<template>
  <div>
    <ImmyAlert v-if="isOnboardingDisabled" show variant="warning">
      <template v-if="isOnboardingGloballyDisabled">
        Onboarding is globally disabled. To allow automatic onboarding, enable onboarding under
        system preferences.
      </template>
      <template v-else-if="isOnboardingDisabledForTenant">
        Onboarding is disabled for the selected tenant. To allow automatic onboarding, enable
        onboarding under the tenant's preferences.
      </template>
    </ImmyAlert>
    <ImmyCheckBox
      id="ppkg-autoonboard-checkbox"
      v-model="automaticallyOnboard"
      name="ppkg-autoonboard-checkbox"
      :disabled="disableChangingAutoOnboarding || isOnboardingDisabled"
    >
      Enable Automatic Onboarding
    </ImmyCheckBox>
    <div v-if="automaticallyOnboard">
      <ImmyFormGroup label="Reboot:" label-cols-sm="1" class="mb-0">
        <RebootPreferenceSelector
          v-model="onboardingSessionRebootPreference"
          v-model:prompt-timeout-minutes="onboardingSessionPromptTimeoutMinutes"
          v-model:prompt-timeout-action="onboardingSessionPromptTimeoutAction"
          v-model:auto-consent-to-reboots="onboardingSessionAutoConsentToReboots"
          class="pt-2"
          :show-reboot-preference-label="false"
          :add-top-margin="true"
        />
      </ImmyFormGroup>
      <ImmyCheckBox
        id="send-follow-up-email-checkbox"
        v-model="onboardingSessionSendFollowUpEmail"
        name="send-follow-up-email-checkbox"
      >
        Send Follow-Up Email
      </ImmyCheckBox>
    </div>
    <ImmyCheckBox
      id="set-primary-person-checkbox"
      v-model="setPrimaryUser"
      name="set-primary-person-checkbox"
    >
      Set Primary User
    </ImmyCheckBox>
    <ImmyFormGroup v-if="setPrimaryUser" label-cols="1">
      <div class="d-flex align-items-center mt-3">
        <i class="fas fa-user mr-3 fs-2" />
        <PersonSelectBox
          v-model="primaryPersonId"
          class="flex-grow-1"
          placeholder="Select a primary user"
          invalid-feedback="Please choose a primary user for this computer"
          :tenant-id="tenantId"
        />
      </div>
    </ImmyFormGroup>
    <ImmyCheckBox
      id="set-additional-persons-checkbox"
      v-model="setAdditionalPersonIds"
      name="set-additional-persons-checkbox"
    >
      Set Additional Persons
    </ImmyCheckBox>
    <ImmyFormGroup v-if="setAdditionalPersonIds" label-cols="1">
      <div class="d-flex align-items-center mt-3">
        <i class="fas fa-user mr-3 fs-2" />
        <PersonMultiSelect
          v-model="additionalPersonIds"
          :ignore-ids="ignoredPersonIds"
          class="flex-grow-1"
          select-box-placeholder="Select additional users"
        />
      </div>
    </ImmyFormGroup>
    <ImmyFormGroup label="Tags" label-cols="1" class="mt-2">
      <AddNewTag id="computer-tags-select" :tag-type="TagType.Computer" @tag:added="onTagAdded" />
    </ImmyFormGroup>
    <TagPill
      v-for="tag in selectedTags"
      :key="tag.id"
      class="mr-1"
      size="lg"
      :model-value="tag"
      can-remove
      @click="onTagRemove(tag)"
    />
  </div>
</template>

<script lang="ts" setup>
import { equals } from "ramda";
import { computed, nextTick, ref, watch } from "vue";
import {
  IAgentOnboardingOptions,
  IGetTenantPreferencesResponse,
  PromptTimeoutAction,
  RebootPreference,
  TagType,
} from "@/api/backend/generated/contracts";
import { preferencesApi } from "@/api/backend/v1";

import { usePreferencesStore } from "@/store/pinia/preferences-store";
import { ITag } from "./TagPill.vue";

const props = defineProps<{
  modelValue: IAgentOnboardingOptions;
  tenantId: number;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", val: IAgentOnboardingOptions): void;
}>();

const preferencesStore = usePreferencesStore();
const setPrimaryUser = ref(props.modelValue.primaryPersonId != null);
const setAdditionalPersonIds = ref(
  props.modelValue.additionalPersonIds != null && props.modelValue.additionalPersonIds.length > 0,
);
const disableChangingAutoOnboarding = ref(false);
const selectedTags = ref<ITag[]>([]);
const selectedTenantPreferences = ref<IGetTenantPreferencesResponse>();
const primaryPersonId = ref<number | undefined>(props.modelValue.primaryPersonId);
const additionalPersonIds = ref<number[]>(props.modelValue.additionalPersonIds ?? []);
const tags = ref<number[]>(props.modelValue.tags ?? []);
const automaticallyOnboard = ref(props.modelValue.automaticallyOnboard);
const onboardingSessionSendFollowUpEmail = ref(props.modelValue.onboardingSessionSendFollowUpEmail);
const onboardingCorrelationId = ref(props.modelValue.onboardingCorrelationId);
const onboardingSessionRebootPreference = ref(
  props.modelValue.onboardingSessionRebootPreference ?? RebootPreference.Normal,
);
const onboardingSessionPromptTimeoutAction = ref(
  props.modelValue.onboardingSessionPromptTimeoutAction ?? PromptTimeoutAction.Reboot,
);
const onboardingSessionPromptTimeoutMinutes = ref(
  props.modelValue.onboardingSessionPromptTimeoutMinutes,
);
const onboardingSessionAutoConsentToReboots = ref(
  props.modelValue.onboardingSessionAutoConsentToReboots ?? false,
);

function updateFromValue(val: IAgentOnboardingOptions) {
  primaryPersonId.value = val.primaryPersonId;
  additionalPersonIds.value = val.additionalPersonIds ?? [];
  tags.value = val.tags ?? [];
  automaticallyOnboard.value = val.automaticallyOnboard ?? false;
  onboardingSessionSendFollowUpEmail.value = val.onboardingSessionSendFollowUpEmail ?? false;
  onboardingCorrelationId.value = val.onboardingCorrelationId;
  onboardingSessionRebootPreference.value
    = val.onboardingSessionRebootPreference ?? RebootPreference.Normal;
  onboardingSessionPromptTimeoutAction.value
    = val.onboardingSessionPromptTimeoutAction ?? PromptTimeoutAction.Reboot;
  onboardingSessionAutoConsentToReboots.value = val.onboardingSessionAutoConsentToReboots ?? false;
  onboardingSessionPromptTimeoutMinutes.value = val.onboardingSessionPromptTimeoutMinutes;
}

const serialized = computed(() => {
  const options: IAgentOnboardingOptions = {
    primaryPersonId: primaryPersonId.value,
    additionalPersonIds: additionalPersonIds.value,
    tags: tags.value,
    automaticallyOnboard: automaticallyOnboard.value,
    onboardingSessionSendFollowUpEmail: onboardingSessionSendFollowUpEmail.value,
    onboardingCorrelationId: onboardingCorrelationId.value,
    onboardingSessionRebootPreference: onboardingSessionRebootPreference.value,
    onboardingSessionPromptTimeoutAction: onboardingSessionPromptTimeoutAction.value,
    onboardingSessionAutoConsentToReboots: onboardingSessionAutoConsentToReboots.value,
    onboardingSessionPromptTimeoutMinutes: onboardingSessionPromptTimeoutMinutes.value,
    isDevLab: false,
  };
  return options;
});

const isOnboardingGloballyDisabled = computed(() => {
  return !preferencesStore.appPreferences?.enableOnboarding;
});
const isOnboardingDisabledForTenant = computed(() => {
  // allow it if we don't have tenant preferences
  return selectedTenantPreferences.value?.enableOnboarding !== true;
});
const isOnboardingDisabled = computed(() => {
  return isOnboardingGloballyDisabled.value || isOnboardingDisabledForTenant.value;
});

function onTagRemove(val: ITag) {
  selectedTags.value = selectedTags.value.filter(a => a !== val);
  tags.value = tags.value.filter(a => a !== val.id);
}

function onTagAdded(val: ITag) {
  if (selectedTags.value.some(a => a.id === val.id))
    return;
  selectedTags.value.push(val);

  if (!tags.value)
    tags.value = [val.id];
  else tags.value.push(val.id);
}

const ignoredPersonIds = computed(() => {
  if (primaryPersonId.value != null)
    return [primaryPersonId.value];

  return [];
});

watch([primaryPersonId, additionalPersonIds], ([primary, additionals]) => {
  if (primary && additionals?.includes(primary)) {
    // if the selected primary person id is one of the additional person ids, remove it from
    // the additional person ids
    additionalPersonIds.value = additionals.filter(i => i !== primary);
  }
});

watch(
  () => props.tenantId,
  async (val) => {
    if (val)
      selectedTenantPreferences.value = await preferencesApi.getTenantPreferences(val);
    else
      selectedTenantPreferences.value = undefined;
  },
  { immediate: true },
);

watch(serialized, (val) => {
  if (!equals(val, props.modelValue))
    emit("update:modelValue", val);
});

watch(
  () => props.modelValue,
  (val) => {
    if (!equals(serialized.value, val))
      updateFromValue(val);
  },
);

let primaryPersonIdCached: number | undefined;
watch(setPrimaryUser, (val) => {
  if (!val) {
    if (primaryPersonId.value != null) {
      // strip selected primary user from model, but keep the prev selection around in case
      // the user reselects setPrimaryUser
      primaryPersonIdCached = primaryPersonId.value;
      primaryPersonId.value = undefined;
    }
  }
  else {
    nextTick(() => {
      if (primaryPersonIdCached != undefined) {
        // if the person id previously selected has since been selected as an additional person
        // then reset the primary person to what it was before - if the user wants to select
        // the person again, it'll automatically remove them from additional users
        if (!additionalPersonIds.value.includes(primaryPersonIdCached))
          primaryPersonId.value = primaryPersonIdCached;
        primaryPersonIdCached = undefined;
      }
    });
  }
});
</script>

<style></style>
