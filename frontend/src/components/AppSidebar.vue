<template>
  <div class="im-sidebar">
    <ul class="im-sidebar-nav">
      <template v-for="item in navItems">
        <SidebarNavDropdown
          v-if="item.children"
          :key="`${item.name}-${item.url}-dd`"
          :item="item"
        />
        <SidebarNavItem v-else :key="`${item.name}-${item.url}`" :item="item" />
      </template>
    </ul>
    <hr class="ml-3">
    <div class="im-sidebar-bottom">
      <div class="im-sidebar-notifications">
        <ImmyNavItem
          v-if="showCompleteAzureSetupButton"
          class="sidebar-nav-item-notification"
          :to="azureSetupRoute"
          title="Complete Azure Setup"
        >
          <span>Complete Azure Setup</span>
          <div class="sidebar-nav-item-notification-link d-flex align-items-center">
            <span>Setup</span>
            <i class="fa-solid fa-arrow-right-long ml-2" />
          </div>
        </ImmyNavItem>
        <ImmyNavItem
          v-else-if="permissionStore.can('azure_operations:update_permission_levels') && noPartnerTenants && mspTenant != null"
          class="sidebar-nav-item-notification"
          :to="tenantAzureTabRoute(mspTenant)"
          title="Click to add a partner tenant"
        >
          <span>The MSP Tenant is not linked to an Azure Partner</span>
          <div class="sidebar-nav-item-notification-link d-flex align-items-center">
            <span>Add Partner</span>
            <i class="fa-solid fa-arrow-right-long ml-2" />
          </div>
        </ImmyNavItem>
        <ImmyNavItem
          v-if="showUpdateAvailable"
          class="sidebar-nav-item-notification"
          :to="{ name: 'System Update' }"
          title="System Update Available"
        >
          <span>A New System Update Is Available!</span>
          <div class="sidebar-nav-item-notification-link d-flex align-items-center">
            <span>Update</span>
            <i class="fa-solid fa-arrow-right-long ml-2" />
          </div>
        </ImmyNavItem>
        <div v-if="authStore.authorized && authStore.hasManagementAccess && permissionStore.can('computers:manage')" class="im-installer-section">
          <DownloadAgentInstallerNavItem
            v-for="item in providerInstallerItems"
            :key="item.providerLink.id"
            :provider-link="item.providerLink"
            :provider-type="item.providerType"
          />
        </div>
      </div>
      <div class="im-footer">
        <p class="m-0 im-copyright text-muted">
          &copy; immy.bot {{ new Date().getFullYear() }}.
        </p>
        <p class="im-version text-muted mb-1">
          Version {{ appVersion }} {{ appVersionChannelText }}
        </p>
        <span class="im-company">ImmyBot, LLC</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed } from "vue";
import {
  IGetProviderLinkResponse,
  IGetTenantResponse,
  IProviderTypeDto,
} from "@/api/backend/generated/contracts";
import DownloadAgentInstallerNavItem from "@/components/DownloadAgentInstallerNavItem.vue";
import EnumTextHelpers from "@/helpers/enums/EnumTextHelpers";
import { INavItem } from "@/nav";
import { useAuthStore } from "@/store/pinia/auth-store";
import { usePermissionStore } from "@/store/pinia/permission-store";
import { useProviderLinksStore } from "@/store/pinia/provider-links-store";
import { useProviderTypesStore } from "@/store/pinia/provider-types-store";
import { useTenantsStore } from "@/store/pinia/tenants-store";
import { GetConstants } from "@/utils/constants";
import { providerSupportsAgentInstallation } from "@/utils/providers";
import SidebarNavDropdown from "./SidebarNavDropdown.vue";
import SidebarNavItem from "./SidebarNavItem.vue";

interface IProviderInstallerItem {
  providerLink: IGetProviderLinkResponse;
  providerType: IProviderTypeDto;
}

defineProps<{
  navItems: INavItem[];
}>();
const { version } = GetConstants();
const providerTypesStore = useProviderTypesStore();
const { allProviderLinks } = storeToRefs(useProviderLinksStore());
const authStore = useAuthStore();
const permissionStore = usePermissionStore();
const tenantsStore = useTenantsStore();
const { mspTenants, partnerTenants } = storeToRefs(tenantsStore);

const showUpdateAvailable = computed(() => {
  return permissionStore.can("system_operations:pull_updates") && authStore.updateAvailable && !authStore.isInstanceUpdating;
});
const showCompleteAzureSetupButton = computed(() => {
  return permissionStore.can("azure_operations:view") && authStore.userLevelAuthSelected;
});
const noPartnerTenants = computed(() => {
  return partnerTenants.value.length === 0;
});
const mspTenant = computed(() => {
  return mspTenants.value.find(t => t.id === authStore.tenantId);
});
const appVersion = computed<string>(() => authStore.currentReleaseVersion || version);

const appVersionChannelText = computed(() => {
  if (authStore.currentReleaseReleaseChannel) {
    return ` (${EnumTextHelpers.ReleaseChannel.GetTextByValue(
      authStore.currentReleaseReleaseChannel,
    )})`;
  }

  return "";
});

const providerTypes = computed(() => providerTypesStore.providerTypes);
const providerInstallerItems = computed(() => {
  return allProviderLinks.value.reduce((agg, providerLink) => {
    const capabilities = providerTypesStore.providerTypesCapabilityCheckers.get(
      providerLink.providerTypeId,
    );
    if (capabilities != null && providerSupportsAgentInstallation(capabilities)) {
      const providerType = providerTypes.value.find(
        a => a.providerTypeId === providerLink.providerTypeId,
      );
      if (providerType)
        agg.push({ providerLink, providerType });
    }
    return agg;
  }, [] as IProviderInstallerItem[]);
});

const azureSetupRoute = computed(() => {
  let targetTenant = mspTenants.value.find(t => t.id === authStore.tenantId);

  if (!targetTenant)
    targetTenant = mspTenants.value.find(t => !t.azureTenantLink);

  if (!targetTenant)
    targetTenant = mspTenants.value[0];

  return targetTenant ? tenantAzureTabRoute(targetTenant) : { name: "Azure Settings" };
});

function tenantAzureTabRoute(tenant: IGetTenantResponse) {
  return { name: "Tenant Details", params: { tenantId: tenant.id.toString(), tabName: "azure" } };
}
</script>

<style lang="scss" scoped></style>
