<template>
  <ImmyModal
    id="bulk-merge-modal"
    v-model="showModal"
    size="lg"
    ok-title="Merge"
    title="Merge Tenants"
    :ok-disabled="!targetTenantId"
    :auto-hide-on-ok="false"
    @ok="onOk"
    @cancel="onCancel"
    @hide="onHide"
  >
    <ImmyFormGroup label="Select a target tenant">
      <v-select
        v-model="targetTenantId"
        :class="{ invalid: !targetTenantId }"
        :options="tenants"
        label="name"
        :reduce="tenantReduceFn"
        placeholder="Select a target tenant"
      />
    </ImmyFormGroup>
    <div v-if="targetTenantId != null">
      <p>
        The following <strong>{{ tenants.length - 1 }}</strong> tenant(s) and all of their
        associated data will be <strong>permanently</strong> merged into
        <strong>{{ targetTenantName }}:</strong>
      </p>
      <ol>
        <li v-for="t in tenantsToMerge" :key="t.id">
          {{ t.name }}
        </li>
      </ol>
    </div>
  </ImmyModal>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import { IGetTenantResponse } from "@/api/backend/generated/contracts";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useTenantsStore } from "@/store/pinia/tenants-store";
import { toast } from "@/utils/toast";

interface IProps {
  tenants: Array<IGetTenantResponse>;
}

const props = defineProps<IProps>();
const emit = defineEmits(["done"]);
const appAlertsStore = useAppAlertsStore();
const tenantsStore = useTenantsStore();

const showModal = ref<boolean>(false);
const targetTenantId = ref<number | null>(null);

const tenantsToMerge = computed(() => {
  return props.tenants.filter(a => a.id != targetTenantId.value);
});
const targetTenantName = computed(() => {
  return props.tenants.find(a => a.id === targetTenantId.value)?.name;
});

onMounted(() => {
  showModal.value = true;
});

async function onOk() {
  if (!targetTenantId.value) {
    return;
  }
  try {
    const res = await tenantsStore.bulkMergeTenants({
      targetTenantId: targetTenantId.value,
      tenantsToMerge: tenantsToMerge.value.map(a => a.id),
    });
    if (res?.success) { toast.success("Success"); }
    else {
      appAlertsStore.addAlert({
        text: res?.message ?? "An error occurred while merging tenants",
      });
    }
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while merging the tenants.",
      details: err,
    });
  }
  finally {
    emit("done");
  }
}
function onCancel() {
  emit("done");
}
function onHide() {
  emit("done");
}

const tenantReduceFn = (tenant: IGetTenantResponse) => tenant.id;
</script>

<style lang="scss" scoped></style>
