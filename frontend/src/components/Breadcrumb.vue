<template>
  <ol class="breadcrumb" :class="{ 'pb-1': breadcrumbStore.breadcrumbs.length }">
    <template v-if="loading">
      <i class="fa fa-spinner fa-spin" />
    </template>
    <template v-else>
      <li
        v-for="(breadcrumb, index) in breadcrumbStore.breadcrumbs"
        :key="index"
        class="breadcrumb-item"
      >
        <span v-if="isLast(index)" class="active text-muted">{{ breadcrumb.text }}</span>
        <router-link v-else-if="breadcrumb.to" :to="breadcrumb.to">
          {{
            breadcrumb.text
          }}
        </router-link>
        <span v-else>
          {{ breadcrumb.text }}
        </span>
      </li>
    </template>
  </ol>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { useAuthStore } from "@/store/pinia/auth-store";
import { useBreadcrumbsStore } from "@/store/pinia/breadcrumbs-store";

const breadcrumbStore = useBreadcrumbsStore();
const authStore = useAuthStore();
const loading = computed(() => {
  if (breadcrumbStore.breadcrumbs.length === 0)
    return false;
  return authStore.loading;
});

function isLast(index: number) {
  return index === breadcrumbStore.breadcrumbs.length - 1;
}
</script>
