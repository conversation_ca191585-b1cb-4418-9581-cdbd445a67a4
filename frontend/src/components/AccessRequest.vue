<template>
  <ImmyAlert
    class="access-request"
    :variant="alertVariant"
    :show="granted === null"
    :dismissible="model.success != null"
  >
    <div class="d-flex align-items-center gap-3">
      <div>
        <strong>{{ model.fullName }} ({{ model.emailAddress }})</strong> requested access
        <em :title="requestedDateFormatted">{{ requestedDateText }}</em>.
      </div>
      <LoadButton size="sm" :handler="denyAccess" variant="danger">
        Deny Access
      </LoadButton>
    </div>
    <hr>
    <template v-if="featuresStore.isEnabled(FeatureEnum.RBACFeature)">
      <UpdateUserForm :person="request" @access-granted="onAccessGranted" />
    </template>
    <template v-else>
      <div v-if="model.success == null" class="d-flex align-items-center flex-wrap gap-3">
        <ImmyFormGroup label="Administrator Role" label-class="font-weight-bold">
          <ImmyCheckBox v-model="model.isAdmin" name="admin-checkbox" />
        </ImmyFormGroup>
        <ImmyFormGroup label="Access expires after" label-class="font-weight-bold">
          <ImmyRadioGroup
            id="expiration-time"
            v-model="model.expirationTime"
            :options="[
              { value: ExpirationTime.OneHour, text: 'One Hour' },
              { value: ExpirationTime.OneDay, text: 'One Day' },
              { value: ExpirationTime.ThreeDays, text: 'Three Days' },
              { value: ExpirationTime.Indefinite, text: 'Never' },
            ]"
          />
        </ImmyFormGroup>
        <div class="d-flex align-items-center gap-2 flex-wrap">
          <LoadButton :handler="grantAccess" size="sm" variant="primary">
            Grant Access
          </LoadButton>
          <LoadButton :handler="denyAccess" size="sm" variant="danger">
            Deny Access
          </LoadButton>
        </div>
      </div>
    </template>
  </ImmyAlert>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { ExpirationTime, FeatureEnum } from "@/api/backend/generated/contracts";
import { personsApi } from "@/api/backend/v1";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useAuthStore } from "@/store/pinia/auth-store";
import { useFeaturesStore } from "@/store/pinia/features-store";
import { formatDate, getHumanReadableDate } from "@/utils/misc";
import { IAccessRequestResponseViewModel } from "./AccessRequests.vue";

interface IProps {
  request: IAccessRequestResponseViewModel;
}

const props = defineProps<IProps>();

const appAlertsStore = useAppAlertsStore();
const authStore = useAuthStore();
const featuresStore = useFeaturesStore();

const model = ref<IAccessRequestResponseViewModel>(props.request);
const granted = ref<boolean | null>(null);

const alertVariant = computed(() => {
  if (model.value.success)
    return granted.value ? "success" : "danger";

  return "primary";
});

const requestedDateText = computed(() => getHumanReadableDate(model.value.dateRequestedUTC));

const requestedDateFormatted = computed(() => formatDate(model.value.dateRequestedUTC));

async function grantAccess() {
  try {
    const res = await personsApi.grantAccess(model.value.personId, {
      isAdmin: model.value.isAdmin,
      expirationTime: model.value.expirationTime,
      hasManagementAccess: true,
    });
    model.value.success = res.success;
    model.value.message = res.message;
    granted.value = true;
    authStore.subtractOpenAccessRequestCount();
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while granting access.",
      details: err,
    });
  }
}

async function denyAccess() {
  try {
    const res = await personsApi.denyAccess(model.value.personId);
    model.value.success = res.success;
    model.value.message = res.message;
    granted.value = false;
    authStore.subtractOpenAccessRequestCount();
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while denying access.",
      details: err,
    });
  }
}

function onAccessGranted() {
  granted.value = true;
  authStore.subtractOpenAccessRequestCount();
}
</script>

<style lang="scss" scoped></style>
