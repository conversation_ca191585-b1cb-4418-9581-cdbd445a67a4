<template>
  <ImmyList
    ref="immyAgentTable"
    class="agent-list-table"
    :store-state="false"
    :items="agents"
    :total-count="totalCount"
    :default-per-page="10"
    is-server-side
    :default-filter-value="defaultFilterValue"
    :sortable-properties="sortableProperties"
    :default-sort-property="sortableProperties[1]"
    default-sort-direction="desc"
    input-placeholder="Search by computer name, tenant name, serial number, or operating system..."
    :selection="selection"
    @selection-changed="onSelectionChanged"
    @options-updated="onOptionsUpdated"
  >
    <template #item="{ data }">
      <AgentTableRow :agent="data" @resolution-action-taken="onResolutionActionTaken" />
    </template>
  </ImmyList>
</template>

<script lang="ts" setup>
import throttle from "lodash.throttle";

import { computed, ref, watch } from "vue";
import { IGetPendingAgentResponse, ProviderAgentFilter } from "@/api/backend/generated/contracts";
import { providerAgentsApi } from "@/api/backend/v1";
import { useMountedPoller } from "@/composables/useMountedPoller";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { usePageVisibilityStore } from "@/store/pinia/page-visibility-store";
import { IFilterOptions, IListItem, ISortableProperty } from "@/utils/ImmyListItem";

type TAgent = IGetPendingAgentResponse & IListItem;

const props = withDefaults(
  defineProps<{
    includeOffline?: boolean;
    defaultFilterValue?: string | undefined;
    providerLinkId?: number | undefined;
    selection?: boolean;
    agentFilter?: ProviderAgentFilter | undefined;
  }>(),
  {
    includeOffline: false,
    defaultFilterValue: undefined,
    providerLinkId: undefined,
    selection: true,
    agentFilter: undefined,
  },
);

const emit = defineEmits<{
  (e: "selection-changed", selection: IListItem[]): void;
  (e: "loading", loading: boolean): void;
  (e: "update:count", count: number): void;
}>();

const REFRESH_INTERVAL_MS = 10000;

const appAlertsStore = useAppAlertsStore();

const agents = ref<TAgent[]>([]);
const totalCount = ref(0);
const options = ref<IFilterOptions | null>(null);
const loading = ref(false);

const sortableProperties = computed<ISortableProperty[]>(() => [
  { text: "Computer Name", value: "computerName", type: "string" },
  { text: "Date Added", value: "dateAdded", type: "date" },
  { text: "Operating System", value: "operatingSystemName", type: "string" },
  { text: "Serial Number", value: "serial", type: "string" },
  { text: "Tenant", value: "externalClientName", type: "string" },
]);

watch(
  () => props.includeOffline,
  () => {
    fetchAgents(options.value);
  },
);

watch(
  () => props.agentFilter,
  () => {
    fetchAgents(options.value);
  },
);

const pageVisibilityStore = usePageVisibilityStore();
useMountedPoller(async () => {
  await pageVisibilityStore.waitForActiveUser();
  await doFetch(options.value);
}, { pollIntervalMilliseconds: REFRESH_INTERVAL_MS });

function onResolutionActionTaken() {
  fetchAgents(options.value);
}

function onOptionsUpdated(updatedOptions: IFilterOptions) {
  options.value = updatedOptions;
  fetchAgents(options.value);
  // evt contains all of our params necessary for server-side operations
}

function onSelectionChanged(selection: IListItem[]) {
  emit("selection-changed", selection);
}

function fetchAgents(opts: IFilterOptions | null) {
  const optionsToUse = opts ?? options.value;
  throttle(() => doFetch(optionsToUse), 350)();
}

function clearAndRefresh() {
  clearSelection();
  fetchAgents(options.value);
}

const immyAgentTable = ref();
function clearSelection() {
  const table = immyAgentTable.value as { clearSelection: () => void } | undefined;
  if (table) {
    table.clearSelection();
  }
}

async function doFetch(options: IFilterOptions | null) {
  try {
    loading.value = true;
    emit("loading", loading.value);
    // get all agents paged
    const { count, results } = await providerAgentsApi.getPending({
      filter: options?.filter ?? undefined,
      sort: options?.sort ?? undefined,
      take: options?.take ?? undefined,
      skip: options?.skip ?? undefined,
      sortDesc: options?.sortDesc ?? undefined,
      includeOffline: props.includeOffline,
      providerLinkId: props.providerLinkId,
      agentFilter: props.agentFilter,
    });
    agents.value = results;
    totalCount.value = count;
    emit("update:count", totalCount.value);
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to retrieve agents",
      details: err,
    });
  }
  finally {
    loading.value = false;
    emit("loading", loading.value);
  }
}

defineExpose({
  clearAndRefresh,
  clearSelection,
});
</script>

<style lang="scss" scoped>
.agent-list-table {
  padding: 1rem;

  :deep(.immy-list-ul) {
    padding-left: 0rem;
  }
}
</style>
