<template>
  <ImmyModal
    id="auth-token-acquisition-modal"
    size="xl"
    :model-value="authTokenAcquisitionStore.isModalShown"
    @update:model-value="authTokenAcquisitionStore.setVisibility"
  >
    <template #modal-title>
      <div class="d-flex align-items-center gap-2">
        <span>New Permissions Required</span>
      </div>
    </template>
    <template v-if="authTokenAcquisitionStore.acquisitionOptions != null">
      <p class="text-primary font-weight-bold">
        {{ authTokenAcquisitionStore.acquisitionOptions.mainText }}
      </p>
      <p
        v-if="authTokenAcquisitionStore.acquisitionOptions.secondaryText != null"
        class="text-secondary"
      >
        {{ authTokenAcquisitionStore.acquisitionOptions.secondaryText }}
      </p>
    </template>

    <div v-if="authTokenAcquisitionStore.tenantNameBeingAccessed != null" class="mb-2">
      <strong>Tenant being accessed:</strong>
      <span class="ml-1">{{ authTokenAcquisitionStore.tenantNameBeingAccessed }}</span>
    </div>

    <div v-if="authTokenAcquisitionStore.consentData?.scopes" class="mb-2">
      <strong>Scopes being requested:</strong>
      <ul>
        <li v-for="scope in authTokenAcquisitionStore.consentData.scopes.split(' ')" :key="scope">
          <code style="word-wrap: anywhere">{{ scope }}</code>
        </li>
      </ul>
    </div>

    <ImmyTabs>
      <template #tabs>
        <ImmyTabItem v-model="currentTab" :tab-value="0" title="Provide Consent" />
        <ImmyTabItem
          v-if="authTokenAcquisitionStore.acquisitionOptions?.allowUserToSelectExistingToken"
          v-model="currentTab"
          :tab-value="1"
          title="Select Existing Token"
        />
      </template>
      <template #content>
        <ImmyTabBody v-show="currentTab == 0">
          <ImmyCard :no-body="true">
            <ImmyCardHeader>
              <div class="d-flex flex-column align-items-start">
                <span>Click the 'Provide consent' button below, follow the prompts in the new window,
                  then come back here</span>
              </div>
            </ImmyCardHeader>
            <ImmyCardBody>
              <template
                v-if="authTokenAcquisitionStore.acquisitionOptions?.allowIndefiniteAccessType"
              >
                <FormGroupWithHelp label="Access Type">
                  <template #help-alert-content>
                    <p>
                      <strong>One-Time</strong> access tokens are valid for 1 hour. After that, you
                      will need to re-authorize immy.bot to continue using the feature.
                    </p>
                    <p>
                      <strong>Indefinite</strong> access tokens are valid until you revoke them. You
                      can revoke them at any time from the OAuth Tokens page.
                    </p>
                  </template>
                  <ImmyRadioGroup
                    v-model="authTokenAcquisitionStore.selectedAccessType"
                    :options="accessTypeOptions"
                    name="accessType"
                    :stacked="true"
                  />
                </FormGroupWithHelp>
              </template>
              <template v-else>
                <p>
                  This access tokens will be valid for 1 hour. After that, you will need to
                  re-authorize immy.bot to continue using the feature.
                </p>
              </template>
            </ImmyCardBody>
            <ImmyCardFooter>
              <div class="d-flex flex-column align-items-start gap-1">
                <template v-if="authTokenAcquisitionStore.triggeredAcquisition" />
                <LoadButton
                  variant="primary"
                  :handler="authTokenAcquisitionStore.triggerAcquisition"
                >
                  Provide consent
                </LoadButton>
                <template v-if="authTokenAcquisitionStore.triggeredAcquisition">
                  <strong class="text-warning">This modal will automatically dismiss when the consent payload is received, but
                    it may take a few seconds for immy.bot to process the payload after you
                    consent</strong>
                </template>
              </div>
            </ImmyCardFooter>
          </ImmyCard>
        </ImmyTabBody>
        <ImmyTabBody v-show="currentTab == 1">
          <ImmyCard :no-body="true">
            <ImmyCardHeader>Select one of the tokens below</ImmyCardHeader>
            <ImmyCardBody>
              <OauthAccessTokenSelector
                :consent-data="authTokenAcquisitionStore.consentData ?? undefined"
                @update:model-value="selectedToken = $event ?? null"
              />
            </ImmyCardBody>
            <ImmyCardFooter>
              <ImmyButton
                variant="primary"
                :disabled="selectedToken == null"
                @click="
                  selectedToken != null && authTokenAcquisitionStore.setSelectedToken(selectedToken)
                "
              >
                Confirm selection
              </ImmyButton>
            </ImmyCardFooter>
          </ImmyCard>
        </ImmyTabBody>
      </template>
    </ImmyTabs>

    <template #modal-footer="{ cancel }">
      <ImmyButton size="sm" variant="outline-secondary" @click="cancel()">
        Cancel
      </ImmyButton>
    </template>
  </ImmyModal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { IOauth2AccessToken } from "@/api/backend/generated/interfaces";
import { useAuthTokenAcquisitionStore } from "@/store/pinia/auth-token-acquisition-store";

const authTokenAcquisitionStore = useAuthTokenAcquisitionStore();

const accessTypeOptions = [
  { text: "One-Time", value: "oneTime" },
  { text: "Indefinite", value: "indefinite" },
];

const currentTab = ref(0);
const selectedToken = ref<IOauth2AccessToken | null>(null);
</script>
