<template>
  <div class="branding-preview">
    <div class="branding-background" :style="customBackgroundStyle">
      <div class="branding-main" :style="customMainStyle">
        <div v-if="logoUrl" class="branding-logo">
          <img :src="logoUrl" width="auto">
        </div>
        <div class="branding-foreground mx-3" :style="customForegrounStyle">
          <div v-if="mascotUrl" class="branding-mascot">
            <img :src="mascotUrl">
          </div>
          <h3 class="title px-5" :style="customTitleStyle">
            <strong>
              {{ mascotName }} will perform maintenance on your computer at {{ props.displayTime }}
            </strong>
          </h3>
          <div class="message px-3" :style="customMessageStyle">
            You are receiving this email because you are currently logged into TEST-PC. This is your
            computer maintenance notification email.
          </div>
          <div class="action-table">
            <h3 class="mb-3">
              <strong>Managed Software</strong>
            </h3>
            <table border="1" :style="customTableStyle">
              <tr :style="tableHeaderStyle">
                <th>Name</th>
                <th>Description</th>
                <th>Action</th>
                <th>Result</th>
                <th>Reason</th>
              </tr>
              <tr>
                <td>7-Zip</td>
                <td>21.7.0</td>
                <td>No Action</td>
                <td>Up To Date</td>
                <td>Up to Date</td>
              </tr>
              <tr>
                <td>Zoom</td>
                <td>5.10.1.4420</td>
                <td>Install</td>
                <td>Pending</td>
                <td>Missing</td>
              </tr>
              <tr>
                <td>Everything</td>
                <td>1.4.1.1009</td>
                <td>No Action</td>
                <td>Up To Date</td>
                <td>Up To Date</td>
              </tr>
              <tr>
                <td>Audacity</td>
                <td>3.1.0 → 3.1.3</td>
                <td>Update</td>
                <td>Pending</td>
                <td>Update Available</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";

interface IProps {
  backgroundColor: string;
  foregroundColor: string;
  logoUrl?: string;
  mascotUrl?: string;
  textColor: string;
  tableHeaderBackgroundColor: string;
  tableHeaderTextColor: string;
  mascotName?: string;
  displayTime?: string;
}

const props = defineProps<IProps>();

const customBackgroundStyle = computed(() => props.backgroundColor);
const customForegrounStyle = computed(() => ({
  backgroundColor: props.foregroundColor,
  color: props.textColor,
}));
const customTitleStyle = computed(() => props.textColor);
const customMessageStyle = computed(() => props.textColor);
const tableHeaderStyle = computed(() => ({
  backgroundColor: props.tableHeaderBackgroundColor,
  color: props.tableHeaderTextColor,
}));
const customTableStyle = computed(() => props.textColor);
const customMainStyle = computed(() => ({
  paddingTop: props.logoUrl ? "0px" : "60px",
}));
</script>

<style lang="scss" scoped>
.branding-preview {
  min-width: 775px;
  max-width: 775px;
}

.branding-main {
  height: 100%;
  padding: 60px;
  padding-top: 0px;
}

.branding-logo {
  text-align: center;
}

.branding-foreground {
  padding-bottom: 2em;
}

.branding-mascot {
  text-align: center;
  padding-top: 2em;
}

.title {
  padding-top: 2em;
  text-align: center;
}

.message {
  padding-top: 2em;
  text-align: center;
}

.action-table {
  text-align: center;
  margin-top: 2em;

  table {
    margin: 0 auto;
    margin-bottom: 15px;
    border: 1px solid #dddddd;

    th,
    td {
      padding-left: 15px;
      padding-right: 15px;
    }

    td {
      padding-top: 5px;
      padding-bottom: 5px;
    }
  }
}
</style>
