<template>
  <ImmyCard
    header-tag="strong"
    header-class="p-1"
    body-class="p-1"
    class="mb-1"
    :header="modelValue.name"
  >
    <ImmyCardText class="mb-1">
      {{ Math.round(((modelValue.value ?? 0) + Number.EPSILON) * 100) / 100 }} {{ modelValue.unit }}
    </ImmyCardText>
  </ImmyCard>
</template>

<script setup lang="ts">
import { IGaugeMetric } from "@/api/backend/generated/interfaces";

defineProps<{
  modelValue: IGaugeMetric;
}>();
</script>

<style></style>
