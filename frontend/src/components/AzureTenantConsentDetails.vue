<template>
  <div class="mb-0">
    <ImmyAlert v-if="azurePermissions?.azurePermissionLevel == null" show variant="warning">
      <p class="mb-0">
        Consent can only be provided to tenants that are customers of an Azure Partner or that have
        an <em>Azure Permission Level</em> (which can be configured on the tenant's
        <router-link
          :to="{
            name: 'Tenant Details',
            params: {
              tenantId: immyTenantId?.toString() ?? '',
              tabName: 'azure',
            },
          }"
        >
          Azure tab
        </router-link>).
      </p>
    </ImmyAlert>
    <template v-else>
      <p class="mb-2">
        <template v-if="consentDetails.consentedWith != null && immyTenantId != null">
          <a
            v-if="azurePermissions?.customAppRegSelected"
            class="btn btn-sm btn-secondary"
            :href="
              azurePermissions.getConsentLink(
                immyTenantId,
                principalId,
                consentFlow,
                AppRegistrationType.Custom,
              )
            "
          >Reconsent (custom)<i class="fal fa-external-link-alt" /></a>
          <a
            v-else-if="
              azurePermissions?.azurePermissionLevel != null
                || consentFlow === 'manual-tenant-admin-consent'
            "
            class="btn btn-sm btn-secondary"
            :href="azurePermissions.getConsentLink(immyTenantId, principalId, consentFlow)"
          >Reconsent <i class="fal fa-external-link-alt" /></a>
        </template>
        <template v-else-if="immyTenantId != null">
          <a
            v-if="azurePermissions?.customAppRegSelected"
            class="btn btn-sm btn-secondary"
            :href="
              azurePermissions.getConsentLink(
                immyTenantId,
                principalId,
                consentFlow,
                AppRegistrationType.Custom,
              )
            "
          >Consent (custom) <i class="fal fa-external-link-alt" /></a>
          <a
            v-else-if="
              azurePermissions?.azurePermissionLevel != null
                || consentFlow === 'manual-tenant-admin-consent'
            "
            class="btn btn-sm btn-secondary"
            :href="azurePermissions.getConsentLink(immyTenantId, principalId, consentFlow)"
          >Consent <i class="fal fa-external-link-alt" /></a>
        </template>
      </p>
      <p class="mb-1">
        <template v-if="consentDetails.consentDateUtc">
          <span class="text-muted">Consented at:</span>&nbsp;{{
            formatDate(consentDetails.consentDateUtc)
          }}
          <span
            v-if="
              isLessThan30MinutesAgo(consentDetails.consentDateUtc)
                && (lastGetUsersAttemptResult?.attemptDateUtc == null
                  || lastGetUsersAttemptResult.attemptFailedErrorId)
            "
            class="ml-2 text-warning d-block"
          >(less than 30 minutes ago - it could still be propagating{{
            checkPropagatedText ? `; ${checkPropagatedText}` : ""
          }})</span>
        </template>
        <template v-else-if="consentDetails.consentedWith != null">
          Consent was provided at an unknown date
        </template>
        <template v-else>
          <i class="fa fa-exclamation-triangle text-warning" /> Consent has not yet been provided
        </template>
      </p>
      <p v-if="consentDetails.consentedWith != null" class="mb-1">
        <span class="text-muted">Consented to:</span>&nbsp;
        <span>
          <template v-if="consentDetails.consentedWith === AppRegistrationType.Custom">
            Custom app registration
          </template>
          <template v-else-if="consentDetails.consentedWith === AppRegistrationType.Backend">
            Default app registration
          </template>
        </span>
      </p>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computedAsync } from "@vueuse/core";
import { AppRegistrationType } from "@/api/backend/generated/enums";
import { IAzureSyncResult, IAzureTenantConsentDetails } from "@/api/backend/generated/interfaces";
import { useAzureTenantAuthStore } from "@/store/pinia/azure-tenant-auth-store";
import { formatDate, isLessThanXMinutesAgo } from "@/utils/misc";
import { TenantConsentCallbackFlow } from "@/utils/oauth-consent";

const props = defineProps<{
  immyTenantId: number;
  principalId: string;
  partnerPrincipalId: string | null | undefined;
  consentDetails: IAzureTenantConsentDetails;
  lastGetUsersAttemptResult: IAzureSyncResult | null | undefined;
  consentFlow: TenantConsentCallbackFlow;
  checkPropagatedText?: string;
}>();

const azurePermissions = computedAsync(
  () => useAzureTenantAuthStore(props.partnerPrincipalId ?? props.principalId),
  null,
);

function isLessThan30MinutesAgo(dateUtc: string) {
  return isLessThanXMinutesAgo(dateUtc, 30);
}
</script>
