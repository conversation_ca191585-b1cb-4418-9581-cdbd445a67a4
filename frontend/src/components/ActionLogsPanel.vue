<template>
  <logs-panel
    :logs="logs" :computer-name="computerName" :maintenance-session-id="sessionId" :streaming="false"
    :collapsible="false" :show-scroll-button="false"
  />
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { IGetMaintenanceSessionLogResponse } from "@/api/backend/generated/responses";
import { maintenanceActionsApi } from "@/api/backend/v1";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";

const props = defineProps<{
  actionId: number;
  sessionId?: number | null;
  computerName?: string;
}>();

const appAlertsStore = useAppAlertsStore();

const logs = ref<IGetMaintenanceSessionLogResponse[]>([]);

watch(() => props.actionId, async (newVal, oldVal) => {
  if (newVal === oldVal)
    return;

  // fetch logs
  await loadActionLogs();
}, { immediate: true });

async function loadActionLogs() {
  try {
    logs.value = [];
    logs.value = await maintenanceActionsApi.getLogsForAction(props.actionId);
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while fetching logs for the action.",
      details: err,
    });
  }
}
</script>

<style lang="scss" scoped></style>
