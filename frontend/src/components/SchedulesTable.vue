<template>
  <div>
    <ImmyDxDataGrid
      id="schedules-list"
      :data-source="populatedSchedules"
      :columns="columns"
      :store-state="false"
      column-auto-width
      show-header-filter
    >
      <template #targetType="{ data }">
        <TargetTypeColumn :data="data" />
      </template>
      <template #maintenanceName="{ data }">
        <MaintenanceItemColumn v-if="!initializing" :data="data" />
      </template>
      <template #audit="{ data }">
        <AuditableColumnData
          :updated-by="data.updatedByName"
          :updated-date-utc="data.updatedDateUTC"
          :created-date-utc="data.createdDateUTC"
        />
      </template>
      <template #nextRun="{ data }">
        <span v-if="data.disabled" class="d-block">Disabled</span>
        <template v-else>
          <span class="d-block">{{ data.nextRunLocal }}</span>
          <em class="text-muted">{{ data.nextRunText }}</em>
        </template>
      </template>
      <template #actions="{ data }">
        <div class="table-actions">
          <ImmyButton
            :to="{ name: 'Edit Schedule', params: { scheduleId: data.id } }"
            size="sm"
            variant="secondary"
          >
            <i class="fal fa-edit" />
          </ImmyButton>
          <ImmyButton
            v-if="isScheduleRunning(data)"
            size="sm"
            variant="warning"
            @click="cancelSchedule(data)"
          >
            <i class="fa fa-stop" />
            Cancel
          </ImmyButton>
          <ImmyButton v-else size="sm" variant="primary" class="deploy-action" @click="runScheduleNow(data)">
            Run
          </ImmyButton>
          <ImmyButton
            size="sm"
            variant="danger"
            :data-testid="`delete-schedule-button-${data.id}`"
            @click="remove(data)"
          >
            <i class="fal fa-trash-can" />
          </ImmyButton>
        </div>
      </template>
    </ImmyDxDataGrid>
  </div>
</template>

<script lang="ts" setup>
import type { Column } from "devextreme/ui/data_grid";
import type { IGetScheduleResponse } from "@/api/backend/generated/responses";
import Swal from "sweetalert2";
import { computed, onMounted, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { schedulesApi } from "@/api/backend/v1";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useAuthStore } from "@/store/pinia/auth-store";
import { useMaintenanceTasksStore } from "@/store/pinia/maintenance-tasks-store";
import { useSoftwareStore } from "@/store/pinia/software-store";
import { useTenantsStore } from "@/store/pinia/tenants-store";
import { populateMaintenanceItemFields } from "@/utils/misc";
import prettyCron from "@/utils/prettycron";
import { toast } from "@/utils/toast";

// Extend the base schedule type with runtime properties
interface Schedule extends IGetScheduleResponse {
  nextRunLocal?: string;
  nextRunText?: string;
  targetCount?: number;
}

interface Props {
  tenantId?: number;
}

const props = withDefaults(defineProps<Props>(), {
  tenantId: undefined,
});

const emit = defineEmits<{
  (e: "initialized"): void;
}>();

const router = useRouter();
const initializing = ref(false);
const schedules = ref<IGetScheduleResponse[]>([]);
const runningScheduleIds = ref<number[]>([]);

const appAlertsStore = useAppAlertsStore();
const maintenanceTasksStore = useMaintenanceTasksStore();
const softwareStore = useSoftwareStore();
const tenantsStore = useTenantsStore();
const authStore = useAuthStore();

const tenantsMap = computed(() => tenantsStore.tenantsMap);
const globalSoftware = computed(() => softwareStore.global);
const localSoftware = computed(() => softwareStore.local);
const localTasks = computed(() => maintenanceTasksStore.local);
const globalTasks = computed(() => maintenanceTasksStore.global);

const columns: Column[] = [
  {
    cellTemplate: "actions",
    caption: "Actions",
    alignment: "center",
    width: "auto",
    minWidth: 120,
  },
  {
    caption: "Target Details",
    alignment: "center",
    columns: [
      {
        dataField: "targetScopeName",
        caption: "Target Scope",
        groupIndex: undefined,
        alignment: "center",
        width: 150,
      },
      {
        groupIndex: undefined,
        caption: "Type",
        alignment: "center",
        dataField: "targetType",
        cellTemplate: "targetType",
        width: 200,
        calculateCellValue(rowInfo: Schedule) {
          return `${rowInfo.targetTypeName}${
            rowInfo.targetGroupFilterName && rowInfo.targetGroupFilterName !== "All"
              ? ` (${rowInfo.targetGroupFilterName})`
              : ""
          }`;
        },
      },
      {
        groupIndex: undefined,
        caption: "Tenant",
        dataField: "tenantId",
        alignment: "center",
        width: 150,
        calculateCellValue: (rowInfo: Schedule) => {
          const tenantId = rowInfo.tenantId;
          if (tenantId) {
            const tenant = getTenant(tenantId);
            if (tenant)
              return tenant.name;
          }
          return "";
        },
      },
      {
        caption: "Target",
        alignment: "center",
        dataField: "targetText",
        width: 150,
      },
    ],
  },
  {
    cellTemplate: "maintenanceName",
    dataField: "maintenanceName",
    caption: "Maintenance Item",
    alignment: "center",
    width: "auto",
  },
  {
    dataField: "nextOccurenceDate",
    cellTemplate: "nextRun",
    sortOrder: "asc",
    caption: "Next Run",
    alignment: "center",
    dataType: "datetime",
  },
  {
    dataField: "updatedDateUTC",
    dataType: "datetime",
    caption: "Last Updated",
    cellTemplate: "audit",
    alignment: "center",
    width: 150,
  },
];

const populatedSchedules = computed(() => {
  return schedules.value.map((s: Schedule) => {
    const schedule = { ...s };
    populateMaintenanceItemFields(
      schedule,
      localTasks.value,
      localSoftware.value,
      globalTasks.value,
      globalSoftware.value,
    );
    let cron = schedule.customCronExpression;
    if (!cron && schedule.time && schedule.time.length > 0 && schedule.day !== undefined) {
      const timeSplit = schedule.time.split(":");
      const hour = timeSplit[0];
      const minute = timeSplit[1];

      cron = `${minute} ${hour} * * ${schedule.day}`;
    }

    schedule.nextRunText = `${prettyCron.getNext(cron ?? "")} - ${schedule.timeZoneInfoId}`;

    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      month: "long",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      second: "numeric",
      timeZoneName: "short",
    };

    if (schedule.nextOccurenceDate) {
      schedule.nextRunLocal = new Intl.DateTimeFormat("en-US", options).format(
        new Date(schedule.nextOccurenceDate),
      );
    }

    return schedule;
  });
});

function getTenant(tenantId: number | undefined) {
  return tenantId ? tenantsMap.value.get(Number(tenantId)) : undefined;
}

function isScheduleRunning(schedule: Schedule) {
  return runningScheduleIds.value.includes(schedule.id);
}

async function cancelSchedule(schedule: Schedule) {
  try {
    const { value } = await Swal.fire({
      title: "Cancel Schedule",
      text: "Are you sure you want to cancel this schedule's active sessions?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
      reverseButtons: true,
    });

    if (value) {
      await schedulesApi.cancelSchedule(schedule.id);
      toast.success("Cancelling the schedule's active sessions.");
      router.push({ name: "Session List" });
    }
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to cancel schedule",
      details: err,
    });
  }
}

async function runScheduleNow(schedule: Schedule) {
  try {
    const { value } = await Swal.fire({
      title: "Run Schedule Now",
      text: "Are you sure you want to run this schedule now?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
      reverseButtons: true,
    });
    if (value) {
      await schedulesApi.runScheduleNow(schedule.id);
      toast.success("Scheduled sessions are now starting.");
      router.push({ name: "Session List" });
    }
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to run schedule",
      details: err,
    });
  }
}

async function remove(model: Schedule) {
  try {
    const { value } = await Swal.fire({
      title: "Are you sure?",
      text: "The schedule will be permanently deleted",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
    });
    if (value) {
      await schedulesApi.delete(model.id);
      schedules.value = schedules.value.filter((a: Schedule) => a.id != model.id);
      toast.success("Successfully deleted schedule.");
    }
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while deleting the schedule.",
      details: err,
    });
  }
}

async function fetchData() {
  authStore.setLoading();
  try {
    const [fetchedSchedules] = await Promise.all([
      schedulesApi.get({ tenantId: props.tenantId }),
      maintenanceTasksStore.loadAll(),
      softwareStore.loadAll(),
    ]);
    schedules.value = fetchedSchedules;
    runningScheduleIds.value = await schedulesApi.getRunningScheduleIds();
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while fetching the schedule.",
      details: err,
    });
  }
  finally {
    authStore.loadingComplete();
  }
}

async function initialize() {
  initializing.value = true;
  await fetchData();
  initializing.value = false;
}

watch(() => props.tenantId, async (newVal, oldVal) => {
  if (newVal !== oldVal) {
    await initialize();
  }
});

onMounted(async () => {
  await initialize();
  emit("initialized");
});
</script>

<style lang="scss" scoped>
:deep(.dx-template-wrapper) {
  white-space: break-spaces;
}

:deep(.dx-datagrid-rowsview) .dx-row > td {
  white-space: break-spaces;
}
</style>
