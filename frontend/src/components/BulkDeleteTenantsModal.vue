<template>
  <ImmyModal
    id="migrate-modal"
    v-model="showModal"
    size="lg"
    ok-title="Delete"
    title="Delete Tenants"
    :auto-hide-on-ok="autoHideOnOk"
    @ok="onOk"
    @cancel="onCancel"
    @hide="onHide"
  >
    <div v-if="mspTenants.length">
      <p class="text-muted">
        <em class="text-warning">The following MSP tenants cannot be deleted.</em>
      </p>

      <ol>
        <li v-for="t in mspTenants" :key="t.id">
          {{ t.name }}
        </li>
      </ol>
      <hr>
    </div>

    <p v-if="nonMspTenants.length === 0">
      No valid tenants have been selected.
    </p>
    <template v-else>
      <p>
        The following <strong>{{ nonMspTenants.length }}</strong> tenants and all of their associated data
        will be <strong>permanently</strong> deleted:
      </p>
      <ol>
        <li v-for="t in nonMspTenants" :key="t.id">
          {{ t.name }}
        </li>
      </ol>

      <p class="text-muted">
        The deletion will happen in the background, and a notification will be sent when completed.
      </p>
    </template>
  </ImmyModal>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import { IGetTenantResponse } from "@/api/backend/generated/contracts";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useTenantsStore } from "@/store/pinia/tenants-store";
import { toast } from "@/utils/toast";

interface IProps {
  tenants: Array<IGetTenantResponse>;
  autoHideOnOk?: boolean;
}

const props = defineProps<IProps>();
const emit = defineEmits(["done"]);
const appAlertsStore = useAppAlertsStore();
const tenantsStore = useTenantsStore();

const showModal = ref<boolean>(false);

const nonMspTenants = computed(() => props.tenants.filter(a => !a.isMsp));
const mspTenants = computed(() => props.tenants.filter(a => a.isMsp));
const tenantIds = computed(() => nonMspTenants.value.map(a => a.id));

onMounted(() => {
  showModal.value = true;
});

async function onOk() {
  try {
    if (tenantIds.value.length === 0)
      return;

    const res = await tenantsStore.bulkDeleteTenants({
      ids: tenantIds.value,
      permanent: true,
    });
    if (res?.success) { toast.success("Success"); }
    else {
      const errors = res?.tenantResults.reduce((agg, cur) => {
        if (cur.success)
          return agg;
        const tenantName = props.tenants.find(a => a.id === cur.tenantId)?.name;
        const msg = `${tenantName}: ${cur.failedReason}`;
        agg.push(msg);
        return agg;
      }, [] as string[]) ?? [];

      const text = `Some tenants were not marked for deletion:\n\n${errors.join("\n")}`;
      appAlertsStore.addAlert({
        text,
      });
    }
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while deleting the tenants.",
      details: err,
    });
  }
  finally {
    emit("done");
  }
}
function onCancel() {
  emit("done");
}
function onHide() {
  emit("done");
}
</script>

<style lang="scss" scoped></style>
