<template>
  <header class="app-header d-flex flex-column">
    <DevBanner
      v-if="layout.devInstanceDetails.value"
      :dev-instance-details="layout.devInstanceDetails.value"
    />
    <TrialBanner v-if="showTrialBanner" />
    <HeaderNavbar />
  </header>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import { useLayout } from "@/composables/Layout";
import { useAuthStore } from "@/store/pinia/auth-store";

const props = withDefaults(
  defineProps<{
    fixed?: boolean;
  }>(),
  {
    fixed: false,
  },
);
const authStore = useAuthStore();
const layout = useLayout();
onMounted(() => {
  if (props.fixed)
    document.body.classList.add("header-fixed");
  else
    document.body.classList.remove("header-fixed");

  if (layout.isDevInstance.value)
    document.body.classList.add("dev-instance");
});

const showTrialBanner = computed(() => {
  return authStore.daysLeftInTrial != null && authStore.authorized;
});
</script>

<style lang="scss" scoped>
.app-header {
  position: sticky !important;
  top: 0;
}
</style>
