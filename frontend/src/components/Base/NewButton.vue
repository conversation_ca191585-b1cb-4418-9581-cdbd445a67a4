<template>
  <ImmyButton
    class="new-btn"
    :data-testid="name"
    :to="to"
    variant="primary"
    title="New"
    :disabled="disabled"
  >
    {{ buttonText }}
  </ImmyButton>
</template>

<script lang="ts" setup>
import { RouteLocationRaw } from "vue-router";

withDefaults(defineProps<{
  name: string;
  to: Partial<RouteLocationRaw> | string;
  buttonText: string;
  disabled?: boolean;
}>(), {
  disabled: false,
});
</script>

<style lang="scss" scoped>
.new-btn {
  @extend .roboto-btn;
  font-size: 0.75rem;
}
</style>
