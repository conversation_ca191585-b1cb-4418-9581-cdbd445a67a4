<template>
  <ImmyButton
    :data-testid="name"
    :to="to"
    size="sm"
    variant="secondary"
    :title="title"
  >
    <i :class="icon" />
  </ImmyButton>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { RouteLocationRaw } from "vue-router";

const props = withDefaults(defineProps<{
  name: string;
  icon?: "fal fa-eye" | "fal fa-edit";
  to: Partial<RouteLocationRaw> | string;
}>(), {
  icon: "fal fa-edit",
});

const title = computed(() => {
  if (props.icon === "fal fa-edit")
    return "Edit";
  return "View";
});
</script>
