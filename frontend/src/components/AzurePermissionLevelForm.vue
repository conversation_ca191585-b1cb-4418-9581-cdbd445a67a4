<template>
  <div>
    <div class="current-permission mb-3">
      <ImmyButtonGroup>
        <LoadButton
          :disabled="updatingAzurePermissions"
          :variant="userLevelSelected ? 'primary' : 'secondary'"
          :handler="() => (userLevelSelected = true)"
        >
          {{ userLevelText }}
        </LoadButton>
        <LoadButton
          :disabled="updatingAzurePermissions"
          :variant="defaultAppRegSelected ? 'primary' : 'secondary'"
          :handler="() => (defaultAppRegSelected = true)"
        >
          {{ defaultAdminLevelText }}
        </LoadButton>
        <LoadButton
          :disabled="updatingAzurePermissions"
          :variant="customAppRegSelected ? 'primary' : 'secondary'"
          :handler="() => (customAppRegSelected = true)"
        >
          {{ customAdminLevelText }}
        </LoadButton>
      </ImmyButtonGroup>
      <div class="text-muted">
        <ImmyButton
          class="text-muted text-underline p-0 m-0"
          variant="link"
          size="sm"
          @click="onPermissionLevelClick"
        >
          <span style="text-decoration: underline">Show permission level info</span>
        </ImmyButton>
      </div>
    </div>
    <div v-if="customAppRegSelected">
      <div class="d-flex align-items-start">
        <div class="flex-shrink-0 d-flex align-items-center">
          <span class="mr-1">Custom App Registration:</span>
          <span>(</span>
          <a :href="customAppRegDocsHref" target="_BLANK" rel="noopener">See docs <i class="fal fa-external-link-alt" /></a>
          <span>)</span>
          <span class="mx-1">:</span>
          <ImmyButton
            v-if="changingCustomAppReg"
            class="mx-1"
            variant="secondary"
            @click="changingCustomAppReg = false"
          >
            Discard Changes
          </ImmyButton>
          <ImmyButton
            v-else-if="!showCustomAppRegForm"
            class="mx-1"
            variant="secondary"
            @click="changingCustomAppReg = true"
          >
            Change
          </ImmyButton>
          <template v-if="!showCustomAppRegForm && azureTenantAuthStore?.customAppRegAppId">
            <span title="Current custom app id">{{ azureTenantAuthStore?.customAppRegAppId }}</span>
          </template>
        </div>
      </div>
      <div
        v-if="showCustomAppRegForm"
        class="d-flex align-items-start flex-grow-1 ml-3 mt-1 flex-column"
      >
        <ImmyInputGroup prepend="Application (client) ID" size="sm">
          <ImmyInput
            v-model="customAppRegFormData.clientId"
            :placeholder="
              azureTenantAuthStore?.customAppRegAppId || 'Enter application (client) ID'
            "
            required
            :state="
              (!!customAppRegFormData.clientId && !!isClientIdAGuid)
                || (!customAppRegFormData.secret && !!azureTenantAuthStore?.customAppRegAppId)
            "
          />
          <ImmyInputGroupAppend v-if="customAppRegFormData.clientId && !isClientIdAGuid" is-text>
            <small class="text-danger"> The client id should be a guid. </small>
          </ImmyInputGroupAppend>
        </ImmyInputGroup>
        <small v-if="azureTenantAuthStore?.customAppRegAppId" class="mt-0 mb-2 form-text text-muted">Current application id:&nbsp;{{ azureTenantAuthStore?.customAppRegAppId }}</small>
        <ImmyInputGroup prepend="Client Secret Value" size="sm">
          <ImmyInput
            v-model="customAppRegFormData.secret"
            type="password"
            :placeholder="
              !customAppRegFormData.clientId && !!azureTenantAuthStore?.customAppRegAppId
                ? '•••••'
                : 'Enter the client secret value'
            "
            required
            :state="
              (!!customAppRegFormData.secret && !isClientSecretAGuid)
                || (!customAppRegFormData.clientId && !!azureTenantAuthStore?.customAppRegAppId)
            "
          />
          <ImmyInputGroupAppend v-if="isClientSecretAGuid" is-text>
            <small class="text-danger">
              The client secret should not be a guid. You probably copied the client secret id
              instead!
            </small>
          </ImmyInputGroupAppend>
        </ImmyInputGroup>
      </div>
    </div>
    <ImmyAlert v-if="hasChangesToSave" show variant="primary" class="mt-2">
      <i class="fa fa-info-circle mr-2" />
      Click the save button below to make the following changes to {{ partnerImmyTenant.name }}:
      <ul>
        <li v-for="change in changesToPerform" :key="change.text">
          <span>{{ change.text }}</span>
        </li>
      </ul>
    </ImmyAlert>
    <template v-if="showPermissionLevelInfo">
      <hr>
      <ImmyAlert show class="my-3">
        <i class="fa fa-info-circle mr-2" />
        immy.bot cannot automatically revoke permissions you have already granted. You can revoke
        permissions by deleting the
        <em>Immense Networks MSP ImmyBotBackend</em> application from the
        <a
          target="_blank"
          href="https://portal.azure.com/#blade/Microsoft_AAD_IAM/StartboardApplicationsMenuBlade/AllApps/menuId/"
        >
          Enterprise Applications <i class="fal fa-external-link-alt" />
        </a>
        section of your Azure Portal. Delete an enterprise application through the
        <em>Properties</em> pane and click the delete button at the top.
      </ImmyAlert>
      <div id="permission-levels" class="d-flex">
        <div
          class="permission-card me-level p-4 text-light"
          :class="{ selected: azureTenantAuthStore?.userLevelSelected }"
        >
          <div class="permission-body d-flex flex-column">
            <p class="permission-title text-center">
              {{ userLevelText }}
            </p>
            <div class="permission-details d-flex flex-column">
              <p class="m-0">
                immy.bot has no Graph API access
              </p>
              <p>
                Each user will be required to consent the first time they login to immy.bot&ast;
              </p>
            </div>
            <div class="permission-bottom mt-auto mt-auto" />
          </div>
        </div>
        <div
          class="permission-card org-level p-4 text-light"
          :class="{ selected: azureTenantAuthStore?.defaultAppRegSelected }"
        >
          <div class="permission-body d-flex flex-column">
            <p class="permission-title text-center">
              {{ defaultAdminLevelText }}<sup>&dagger;</sup>
            </p>
            <div class="permission-details d-flex flex-column">
              <p>
                Allows immy.bot to read information about users and group memberships in your tenant
              </p>
              <p class="m-0">
                <i class="fa fa-check text-success" />
                Install 365 applications based on user licenses
              </p>
              <p class="m-0">
                <i class="fa fa-check text-success" />
                Deploy software to Teams, Azure AD groups, and on-premises synced security groups
              </p>
              <p>
                <i class="fa fa-check text-success" />
                Sync people from your GDAP customers
                <small>
                  <a :href="gdapDocsHref" target="_BLANK" rel="noopener"><span style="text-decoration: underline">see GDAP docs</span>
                    <i class="fal fa-external-link-alt" /></a></small>
              </p>
            </div>
            <div class="permission-bottom mt-auto d-flex flex-wrap" />
          </div>
        </div>
        <div
          class="permission-card customer-level p-4 text-light"
          :class="{ selected: azureTenantAuthStore?.customAppRegSelected }"
        >
          <div class="permission-body d-flex flex-column">
            <p class="permission-title text-center">
              {{ customAdminLevelText }}<sup>&dagger;</sup>
            </p>
            <div class="permission-detail d-flex flex-column">
              <p>
                You create an app registration for immy.bot to use for this tenant, allowing you to
                add any permissions you want to use within immy.bot&nbsp;
                <a :href="customAppRegDocsHref" target="_BLANK" rel="noopener"><span style="text-decoration: underline">see docs</span>
                  <i class="fal fa-external-link-alt" /></a>
              </p>
            </div>
            <div class="permission-bottom mt-auto mt-auto d-flex flex-wrap" />
          </div>
        </div>
      </div>
      <p class="m-0 mt-2">
        &ast;&nbsp;<em>Restrictions in your tenant may prevent end users from consenting to applications</em>
      </p>
      <p class="m-0 mt-2">
        &dagger;&nbsp;<em>A Global Admin in your tenant will be required to consent</em>
      </p>
    </template>
    <LoadButton
      v-if="hasChangesToSave"
      :handler="saveChanges"
      :disabled="saveButtonDisabled"
      class="mt-2"
      variant="primary"
    >
      Update permission level
    </LoadButton>
    <p v-if="showCountdown">
      Permission changes usually take a few seconds to take effect. You will be forced to login
      again in
      <strong>{{ seconds }}</strong> seconds. Please wait.
    </p>
  </div>
</template>

<script setup lang="ts">
import { computedAsync } from "@vueuse/core";
import Swal from "sweetalert2";
import { computed, ref, watch } from "vue";
import { AppRegistrationType, AzurePermissionLevel2 } from "@/api/backend/generated/enums";
import { IAzureTenantResponse, IGetTenantResponse } from "@/api/backend/generated/responses";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useAuthStore } from "@/store/pinia/auth-store";
import { useAzureTenantAuthStore } from "@/store/pinia/azure-tenant-auth-store";
import { GetConstants } from "@/utils/constants";
import { isGuid } from "@/utils/misc";
import { toast } from "@/utils/toast";

const props = defineProps<{
  partnerImmyTenant: IGetTenantResponse;
  partnerAzureTenant: IAzureTenantResponse;
}>();
const emit = defineEmits<{
  (e: "azure-tenant-auth-updated"): void;
}>();
const { docsRoot } = GetConstants();
const customAppRegDocsHref = `${docsRoot}/azure-graph-permissions-setup.html#custom`;
const gdapDocsHref = `${docsRoot}/azure-graph-permissions-setup.html#gdap-customer-syncing`;
const userLevelText = "None";
const defaultAdminLevelText = "Default";
const customAdminLevelText = "Custom";

const authStore = useAuthStore();
const appAlertsStore = useAppAlertsStore();

const showPermissionLevelInfo = ref(authStore.userLevelAuthSelected);
const updatingAzurePermissions = ref(false);
const seconds = ref(30);
const showCountdown = ref(false);
const customAppRegFormData = ref<{ clientId: string | null; secret: string | null }>({
  clientId: null,
  secret: null,
});
const selectedPermissionLevel = ref<AzurePermissionLevel2 | null | undefined>(undefined);
const changingCustomAppReg = ref(false);

// Keeps track of what user had entered into client id/secret fields when they hide the custom app
// reg form, so that if they show it again they can start from their previous values
let customClientIdWas: string | null = null;
let customClientSecretWas: string | null = null;

const azureTenantAuthStore = computedAsync(
  () => useAzureTenantAuthStore(props.partnerAzureTenant.principalId),
  null,
);

watch(
  () => props.partnerAzureTenant.principalId,
  () => {
    selectedPermissionLevel.value = undefined;
    customAppRegFormData.value = { clientId: null, secret: null };
    changingCustomAppReg.value = false;
  },
);

const userLevelSelected = computed({
  get: () => {
    if (typeof selectedPermissionLevel.value === "undefined")
      return azureTenantAuthStore.value?.userLevelSelected ?? true;
    return selectedPermissionLevel.value === null;
  },
  set: () => {
    selectedPermissionLevel.value = null;
  },
});
const defaultAppRegSelected = computed({
  get: () => {
    if (typeof selectedPermissionLevel.value === "undefined")
      return azureTenantAuthStore.value?.defaultAppRegSelected ?? false;
    return selectedPermissionLevel.value === AzurePermissionLevel2.DefaultAppRegistration;
  },
  set: (val) => {
    if (val === false)
      selectedPermissionLevel.value = null;
    else
      selectedPermissionLevel.value = AzurePermissionLevel2.DefaultAppRegistration;
  },
});
const customAppRegSelected = computed({
  get: () => {
    if (typeof selectedPermissionLevel.value === "undefined")
      return azureTenantAuthStore.value?.customAppRegSelected ?? false;
    return selectedPermissionLevel.value === AzurePermissionLevel2.CustomAppRegistration;
  },
  set: (val) => {
    if (val === false)
      selectedPermissionLevel.value = null;
    else
      selectedPermissionLevel.value = AzurePermissionLevel2.CustomAppRegistration;
  },
});
const hasChangesToSave = computed(() => {
  const azPerms = azureTenantAuthStore.value;
  if (azPerms == null)
    return false;
  return (
    userLevelSelected.value !== azPerms.userLevelSelected
    || defaultAppRegSelected.value !== azPerms.defaultAppRegSelected
    || customAppRegSelected.value !== azPerms.customAppRegSelected
    || (customAppRegSelected.value
      && !!(customAppRegFormData.value.clientId || customAppRegFormData.value.secret))
  );
});

const showCustomAppRegForm = computed(() => {
  // show form is user has just moved to custom app reg or
  // if user wants to change the existing app reg
  return (
    customAppRegSelected.value
    && (!(azureTenantAuthStore.value != null && azureTenantAuthStore.value.customAppRegSelected)
      || changingCustomAppReg.value)
  );
});

watch(showCustomAppRegForm, (val) => {
  if (val) {
    customAppRegFormData.value = { clientId: customClientIdWas, secret: customClientSecretWas };
    customClientSecretWas = customClientIdWas = null;
  }
  else {
    ({ clientId: customClientIdWas, secret: customClientSecretWas } = customAppRegFormData.value);
    customAppRegFormData.value = { clientId: null, secret: null };
  }
});
const isClientSecretAGuid = computed(() => {
  return customAppRegFormData.value.secret ? isGuid(customAppRegFormData.value.secret) : undefined;
});
const isClientIdAGuid = computed(() =>
  customAppRegFormData.value.clientId ? isGuid(customAppRegFormData.value.clientId) : undefined,
);

const isCustomAppRegFormDataValid = computed(
  () => !!customAppRegFormData.value?.clientId && !!customAppRegFormData.value?.secret,
);

const saveButtonDisabled = computed(() => {
  const azPerms = azureTenantAuthStore.value;
  if (azPerms == null)
    return false;
  if (customAppRegSelected.value) {
    const data = customAppRegFormData.value;
    if (!data.clientId && !data.secret && azPerms.customAppRegAppId) {
      // don't disable the save button if there's already a custom app reg app id in the backend
      // and the user hasn't entered a new one. In this case, a confirmation modal is triggered
      // by saveChanges to make sure the user wants to keep the existing app id/secret
      return false;
    }
    // if there's not already an app id in the backend, or if the user has entered a client id or
    // secret, disable the save button if the client id or secret is missing or invalid
    return !isCustomAppRegFormDataValid.value;
  }
  return false;
});

const changesToPerform = computed(() => {
  const azPerms = azureTenantAuthStore.value;
  if (azPerms == null)
    return [];
  if (!hasChangesToSave.value)
    return [];
  const ret: { text: string }[] = [];
  if (userLevelSelected.value && !azPerms.userLevelSelected)
    ret.push({ text: `Update permission level to ${userLevelText}` });
  if (defaultAppRegSelected.value && !azPerms.defaultAppRegSelected)
    ret.push({ text: `Update permission level to ${defaultAdminLevelText}` });
  if (customAppRegSelected.value) {
    if (!azPerms.customAppRegSelected)
      ret.push({ text: `Update permission level to ${customAdminLevelText}` });
    if (customAppRegFormData.value.clientId) {
      ret.push({
        text: `Set the custom app registration client id to "${customAppRegFormData.value.clientId}"`,
      });
    }
    if (customAppRegFormData.value.secret)
      ret.push({ text: "Set the custom app registration secret" });
  }
  return ret;
});

function startCountdownToForceLogin() {
  // only need to re-login if level is basic or advanced
  showCountdown.value = true;
  setInterval(() => {
    if (seconds.value === 0)
      forceLogin();
    else
      seconds.value = seconds.value - 1;
  }, 1000);
}

async function forceLogin() {
  const azPerms = azureTenantAuthStore.value;
  if (azPerms == null)
    return;
  window.location.href = azPerms.getConsentLink(
    props.partnerImmyTenant.id,
    props.partnerAzureTenant.principalId,
    "msp-consent",
    customAppRegSelected.value ? AppRegistrationType.Custom : AppRegistrationType.Backend,
  );
}
function onPermissionLevelClick() {
  showPermissionLevelInfo.value = !showPermissionLevelInfo.value;
}

async function saveChanges() {
  const azPerms = azureTenantAuthStore.value;
  if (azPerms == null || !hasChangesToSave.value)
    return;
  if (customAppRegSelected.value) {
    const data = customAppRegFormData.value;
    if (!data.clientId && !data.secret && azPerms.customAppRegAppId != null) {
      const { value } = await Swal.fire({
        titleText: "Reuse old app registration credentials?",
        text: `You previously provided us with app registration credentials (app id: ${azPerms.customAppRegAppId}). Do you want to reuse them?`,
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });
      if (!value)
        return;
      await updateAzureTenantAuthDetails({
        selectedPermissionLevel: AzurePermissionLevel2.CustomAppRegistration,
      });
    }
    else if (isCustomAppRegFormDataValid.value && data.clientId && data.secret) {
      await updateAzureTenantAuthDetails({
        selectedPermissionLevel: AzurePermissionLevel2.CustomAppRegistration,
        customAppRegAppId: data.clientId,
        customAppRegSecret: data.secret,
      });
    }
  }
  else if (userLevelSelected.value) {
    await deleteAzureTenantAuthDetails();
  }
  else if (selectedPermissionLevel.value != null) {
    await updateAzureTenantAuthDetails({ selectedPermissionLevel: selectedPermissionLevel.value });
  }
}

async function updateAzureTenantAuthDetails(
  body: Parameters<
    Exclude<(typeof azureTenantAuthStore)["value"], null>["updateAzureTenantAuthDetails"]
  >[0],
): Promise<boolean> {
  try {
    const azPerms = azureTenantAuthStore.value;
    if (azPerms == null)
      return false;
    if (updatingAzurePermissions.value)
      return false;
    updatingAzurePermissions.value = true;

    const result = await azPerms.updateAzureTenantAuthDetails(body);
    if (result?.authDetails) {
      toast.success("Success");
      emit("azure-tenant-auth-updated");
      if (
        result.defaultAppRegistrationUpdated
        || props.partnerAzureTenant.consentDetails.consentDateUtc == null
      ) {
        showPermissionLevelInfo.value = false;
        startCountdownToForceLogin();
      }
      // clear out custom app reg form if we successfully updated permission level
      customAppRegFormData.value = { clientId: null, secret: null };
      changingCustomAppReg.value = false;
      return true;
    }
    else {
      appAlertsStore.addAlert({
        text: "An unexpected error occurred while updating the azure permission level.",
      });
      return false;
    }
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An unexpected error occurred while updating the azure permission level.",
      details: err,
    });
    return false;
  }
  finally {
    updatingAzurePermissions.value = false;
  }
}
async function deleteAzureTenantAuthDetails(): Promise<boolean> {
  try {
    const azPerms = azureTenantAuthStore.value;
    if (azPerms == null)
      return false;
    if (updatingAzurePermissions.value)
      return false;
    updatingAzurePermissions.value = true;

    const result = await azPerms.deleteAzureTenantAuthDetails();
    toast.success("Success");
    if (result.defaultAppRegistrationUpdated) {
      showPermissionLevelInfo.value = false;
      startCountdownToForceLogin();
    }
    // clear out custom app reg form if we successfully updated permission level
    customAppRegFormData.value = { clientId: null, secret: null };
    return true;
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An unexpected error occurred while updating the azure permission level.",
      details: err,
    });
    return false;
  }
  finally {
    updatingAzurePermissions.value = false;
  }
}
</script>

<style lang="scss" scoped>
$orgColor: #6f42c1;
$meColor: #20a8d8;
$customerColor: #aa0b37;

.permission-card {
  z-index: 5;
  flex: 1;
  width: 200px;
  transition: background-color 0.5s;

  .permission-title {
    font-size: 2em;
  }

  .permission-body {
    height: 100%;
  }
}

.selected {
  z-index: 6;
}

.customer-level {
  background-color: $customerColor;
}

.me-level {
  background-color: $meColor;
}

.org-level {
  background-color: $orgColor;
}
</style>
