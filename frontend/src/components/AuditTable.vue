<template>
  <ImmyDxDataGrid
    id="audit-list"
    show-header-filter
    :store-state="false"
    :columns="columns"
    remote-operations
    hide-column-chooser
    enable-master-detail
    :data-source="selectedDataSource"
  >
    <template #date="{ data }">
      <span :title="getHumanReadableDate(data.dateTimeUtc)">{{
        formatDate(data.dateTimeUtc)
      }}</span>
    </template>
    <template #detail="{ cellInfo: { data } }">
      <ApplicationAuditProperties :audit="data" />
    </template>
  </ImmyDxDataGrid>
</template>

<script setup lang="ts">
import { DxDataGridTypes } from "devextreme-vue/data-grid";
import { computed } from "vue";
import { DatabaseType } from "@/api/backend/generated/enums";
import { IAudit } from "@/api/backend/generated/interfaces";
import { auditsApi } from "@/api/backend/v1";
import { formatDate, getHumanReadableDate } from "@/utils/misc";

const props = withDefaults(defineProps<{
  databaseType?: DatabaseType;
  userId?: number;
}>(), {
  databaseType: DatabaseType.Local,
});

const columns = computed<DxDataGridTypes.Column<IAudit, number>[]>(() => {
  let ret: DxDataGridTypes.Column<IAudit, number>[] = [
    {
      dataField: "dateTimeUtc",
      cellTemplate: "date",
      caption: "Date",
      alignment: "center",
      sortOrder: "desc",
      dataType: "date",
    },
    {
      dataField: "type",
      caption: "Action",
      alignment: "center",
    },
    {
      dataField: "objectType",
      caption: "Object Type",
      alignment: "center",
    },
    {
      dataField: "objectName",
      caption: "Object Name",
      alignment: "center",
    },
    {
      dataField: "primaryKey",
      caption: "Object Id",
      alignment: "center",
    },
    {
      dataField: "userDisplayName",
      caption: "User",
      alignment: "center",
    },
    {
      dataField: "message",
      caption: "Message",
      alignment: "center",
    },
  ];

  if (props.userId) {
    ret = ret.filter(a => a.caption !== "User");
  }
  return ret;
});

const localDataSource = computed(() => {
  const store = auditsApi.getLocalDx();

  let filter: (string | number)[] | undefined;

  if (props.userId) {
    filter = ["userId", "=", props.userId];
  }
  return {
    store,
    filter,
  };
});

const globalDataSource = computed(() => {
  const store = auditsApi.getGlobalDx();

  let filter: (string | number)[] | undefined;

  if (props.userId) {
    filter = ["userId", "=", props.userId];
  }
  return {
    store,
    filter,
  };
});

const selectedDataSource = computed(() => {
  if (props.databaseType === DatabaseType.Local)
    return localDataSource.value;
  return globalDataSource.value;
});
</script>

<style scoped lang="scss">

</style>
