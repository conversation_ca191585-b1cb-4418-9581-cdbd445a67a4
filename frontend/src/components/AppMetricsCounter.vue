<template>
  <ImmyCard :no-body="true" class="mb-1">
    <ImmyCardHeader class="p-1 d-flex justify-content-between">
      <div class="left">
        <strong>{{ modelValue.name }}</strong>
      </div>
      <div class="right">
        <ImmyButton v-if="modelValue.items.length" size="sm" @click="showItems = !showItems">
          Show / Hide Set Items
        </ImmyButton>
      </div>
    </ImmyCardHeader>
    <ImmyCardBody class="p-1">
      <ImmyCardText class="mb-1">
        <span v-if="modelValue.items.length">Total:&nbsp;</span>
        <em>{{ Math.round((modelValue.count + Number.EPSILON) * 100) / 100 }}
          {{ modelValue.unit }}</em>
      </ImmyCardText>
      <template v-if="showItems">
        <ImmyCardText v-for="setItem in modelValue.items" :key="setItem.item" class="mb-1">
          <strong>{{ setItem.item }}:&nbsp;</strong>
          <em>{{ Math.round((setItem.count + Number.EPSILON) * 100) / 100 }}
            {{ modelValue.unit }}&nbsp;</em>
          <span v-if="setItem.percent" class="text-muted">({{ setItem.percent }}%)</span>
        </ImmyCardText>
      </template>
    </ImmyCardBody>
  </ImmyCard>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ICounterMetric } from "@/api/backend/generated/contracts";

defineProps<{
  modelValue: ICounterMetric;
}>();

const showItems = ref(true);
</script>

<style></style>
