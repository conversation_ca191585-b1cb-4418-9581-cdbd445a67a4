<template>
  <load-button
    v-if="customCanCancelSession"
    v-tooltip="'Cancel session'"
    :handler="cancel"
    size="sm"
    variant="danger"
  >
    Cancel
  </load-button>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import {
  IGetMaintenanceSessionResponse,
  IUpdateMaintenanceSessionResource,
} from "@/api/backend/generated/contracts";
import { canCancelSession, cancelSession } from "@/composables/SessionActions";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { toast } from "@/utils/toast";

interface IProps {
  session: IGetMaintenanceSessionResponse | IUpdateMaintenanceSessionResource;
}

const props = defineProps<IProps>();
const emit = defineEmits(["cancel"]);
const appAlertsStore = useAppAlertsStore();

const customCanCancelSession = computed(() => {
  return canCancelSession(props.session);
});

async function cancel() {
  try {
    const cancelled = await cancelSession(props.session.id);
    if (cancelled) {
      emit("cancel");
      toast.success("Session cancelled");
    }
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to cancel session",
      details: err,
    });
  }
}
</script>

<style lang="scss" scoped></style>
