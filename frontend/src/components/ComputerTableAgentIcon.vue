<template>
  <a
    v-if="isScreenShareSupported(providerLinkId, true)"
    type="button"
    title="Open Remote Session"
    @click="openScreenShareSession(computer.id, providerLinkId, computer.computerName ?? '')"
  >
    <img :src="getScreenShareLogoSrc(providerLinkId)" height="18">
  </a>
  <ComputerProviderTypeButton
    v-else-if="isAgentInstallationSupported && providerLink && providerType"
    class="m-0 p-0"
    size="sm"
    variant="link"
    :computer="computer"
    :provider-link="providerLink"
    :provider-type="providerType"
  />
  <a v-else-if="isExternalProviderAgentUrlSupported" type="button">
    <img
      :src="providerTypeLogoSrc"
      height="18"
      :title="providerLink?.name"
      @click="openExternalProviderAgentUrl"
    >
  </a>
  <dynamic-integration-logo
    v-else-if="isDynamicLogoSrc && providerType?.logoSrc"
    img-class="image-18"
    :media-id="parseInt(providerType.logoSrc)"
    :source="providerType.source"
  />
  <img
    v-else-if="providerTypeLogoSrc"
    :src="providerTypeLogoSrc"
    height="18"
    :title="providerLink?.name"
  >
</template>

<script lang="ts">
import { defineComponent, PropType } from "vue";
import {
  IComputerListViewModel,
  IGetProviderLinkResponse,
  IProviderTypeDto,
} from "@/api/backend/generated/contracts";
import { providerLinksApi } from "@/api/backend/v1";
import {
  getScreenShareLogoSrc,
  isScreenShareSupported,
  openScreenShareSession,
} from "@/composables/RemoteSessionSupport";

import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useProviderLinksStore } from "@/store/pinia/provider-links-store";
import { useProviderTypesStore } from "@/store/pinia/provider-types-store";
import { providerSupportsAgentInstallation } from "@/utils/providers";

export default defineComponent({
  props: {
    computer: {
      type: Object as PropType<IComputerListViewModel>,
      required: true,
    },
    providerLinkId: {
      type: Number,
      required: true,
    },
  },
  setup() {
    return {
      appAlertsStore: useAppAlertsStore(),
      providerTypesStore: useProviderTypesStore(),
      openScreenShareSession,
      getScreenShareLogoSrc,
      isScreenShareSupported,
      providerLinksStore: useProviderLinksStore(),
    };
  },
  computed: {
    isDynamicLogoSrc() {
      return !!this.providerType?.isDynamic;
    },
    providerTypeLogoSrc() {
      return this.providerType?.logoSrc;
    },
    providerLinks(): IGetProviderLinkResponse[] | undefined {
      return this.providerLinksStore.allProviderLinks;
    },
    providerTypes(): IProviderTypeDto[] | undefined {
      return this.providerTypesStore.providerTypes;
    },
    providerLink() {
      return this.providerLinks?.find(a => a.id === this.providerLinkId);
    },
    providerType() {
      return this.providerTypes?.find(a => a.providerTypeId === this.providerLink?.providerTypeId);
    },
    capabilities() {
      return this.providerLink != null
        ? this.providerTypesStore.providerTypesCapabilityCheckers.get(this.providerLink.providerTypeId)
        : null;
    },
    isExternalProviderAgentUrlSupported() {
      if (!this.capabilities)
        return false;
      return this.capabilities.supportsExternalProviderAgentUrl;
    },
    isAgentInstallationSupported() {
      if (!this.capabilities)
        return false;
      return providerSupportsAgentInstallation(this.capabilities);
    },
  },
  methods: {
    async openExternalProviderAgentUrl() {
      // fetch url from backend
      try {
        const url = await providerLinksApi.getExternalProviderAgentUrl(
          this.providerLinkId,
          this.computer.id,
        );
        if (url)
          window.open(url, "_blank", "noopener");
      }
      catch (err) {
        this.appAlertsStore.addAlert({
          text: "Failed to retrieve the agent url",
          details: err,
        });
      }
    },
  },
});
</script>

<style lang="scss" scoped>
a,
img,
:deep(.btn) {
  position: relative;
  top: -2.3px;
  margin-left: -3px;
}
</style>
