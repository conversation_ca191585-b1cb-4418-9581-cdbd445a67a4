<template>
  <UnlinkAzureTenantConfirmationModal
    v-if="unlinkingImmyTenantId != null"
    :immy-tenant-id="unlinkingImmyTenantId"
    @update:model-value="$event == false && (unlinkingImmyTenantId = null)"
    @unlinked="emit('tenant-unlinked', $event)"
  />
  <ProviderClientListLinkedTenantColumn
    v-if="mainLink == null"
    :model-value="null"
    hide-partner-tenants
    allow-edit
    tenant-selector-full-width
    :existing-links="existingLinks"
    @update:model-value="$event != null && setLinkedTenant($event)"
  />
  <ImmyCard v-else no-body class="mt-2" style="min-width: 550px">
    <ImmyCardHeader>
      <div class="d-flex justify-content-between align-items-start">
        <ProviderClientListLinkedTenantColumn
          class="align-self-stretch"
          :model-value="mainLink.id"
          hide-partner-tenants
          allow-edit
          tenant-selector-full-width
          :existing-links="existingLinks"
          @update:model-value="
            $event != null ? setLinkedTenant($event) : (unlinkingImmyTenantId = mainLink.id)
          "
        />
        <div
          v-if="unlinkedDomains != null"
          class="text-right"
          :title="`These are the domains from the Azure tenant that have not been split to a child tenant. Users in these domains will sync to the Immy tenant named ${mainLink.name}`"
        >
          <ImmyButton
            class="p-0"
            size="sm"
            variant="link"
            :disabled="unlinkedDomains.length === 0"
            @click="
              linkedDomainsVisibleMap.set(mainLink.id, !linkedDomainsVisibleMap.get(mainLink.id))
            "
          >
            {{ unlinkedDomains.length }} Azure domain{{ unlinkedDomains.length !== 1 ? "s" : "" }}
          </ImmyButton>
          <ul
            v-if="unlinkedDomains.length && linkedDomainsVisibleMap.get(mainLink.id)"
            class="text-left"
          >
            <li v-for="d in unlinkedDomains" :key="d">
              <span>{{ d }}</span>
            </li>
          </ul>
        </div>
      </div>
    </ImmyCardHeader>
    <ImmyListGroup flush>
      <ImmyListGroupItem v-for="t in childLinks" :key="t.id">
        <div class="d-flex justify-content-between align-items-start">
          <ProviderClientListLinkedTenantColumn
            hide-partner-tenants
            hide-linked-tenants
            :tenant-selector-full-width="false"
            :model-value="t.id"
            :existing-links="existingLinks"
            :allow-edit="allowCreateAndEditLinks"
            @update:model-value="$event == null && (unlinkingImmyTenantId = t.id)"
          />
          <div
            class="text-right"
            :title="`Users in these domains will sync to the Immy tenant named ${t.name}`"
          >
            <ImmyButton
              class="p-0"
              size="sm"
              variant="link"
              @click="linkedDomainsVisibleMap.set(t.id, !linkedDomainsVisibleMap.get(t.id))"
            >
              {{ t.azureTenantLink.limitToDomains.length }} Azure domain{{
                t.azureTenantLink.limitToDomains.length !== 1 ? "s" : ""
              }}
            </ImmyButton>
            <ul
              v-if="linkedDomainsVisibleMap.get(t.id) && t.azureTenantLink.shouldLimitDomains"
              class="text-left"
            >
              <li v-for="d in t.azureTenantLink.limitToDomains" :key="d.domainName">
                <span>{{ d.domainName }}</span>
              </li>
            </ul>
          </div>
        </div>
      </ImmyListGroupItem>
    </ImmyListGroup>
    <ImmyCardFooter
      v-if="
        domainOptions.length != 0 || allLinkedDomains.length == 0 || selectedDomains.length != 0
      "
    >
      <span v-if="!allowCreateAndEditLinks">Cannot edit</span>
      <span v-else-if="domainOptions.length == 0 && selectedDomains.length == 0">
        There are no Azure domains to link
      </span>
      <ImmyButton
        v-else-if="!showCreateLinkForm"
        class="text-center"
        @click="showCreateLinkForm = true"
      >
        Split one or more domains to another Immy tenant
      </ImmyButton>
      <div v-else class="text-left">
        <p class="mt-2 mb-0">
          Select one or more domains:
        </p>
        <v-select
          v-model="selectedDomain"
          :options="domainOptions"
          placeholder="Select one or more domains"
          append-to-body
        />
        <template v-if="selectedDomains.length > 0">
          <span>Selected domains:</span>
          <ul style="list-style: none">
            <li v-for="d in selectedDomains" :key="d" class="text-wrap">
              <i
                class="fa fa-times text-danger mr-1"
                title="Remove domain from selection"
                style="cursor: pointer"
                @click="selectedDomains = selectedDomains.filter(s => s !== d)"
              />
              <span>{{ d }}</span>
            </li>
          </ul>
        </template>
        <p class="mt-2 mb-0">
          Select an Immy tenant to link to:
        </p>
        <ProviderClientListLinkedTenantSelector
          v-model="selectedTenantId"
          hide-partner-tenants
          hide-linked-tenants
          :existing-links="existingLinks"
          :tenant-selector-full-width="false"
        />
        <hr>
        <div class="d-flex justify-content-between mt-2">
          <ImmyButton variant="primary" :disabled="!canCreateLink" @click="createLink">
            {{
              selectedTenantId === NEW_TENANT_ID
                ? "Create tenant"
                : selectedTenantId
                  ? "Link to the selected tenant"
                  : "Link to tenant"
            }}
          </ImmyButton>
          <ImmyButton variant="danger" @click="cancelCreateLink">
            Cancel
          </ImmyButton>
        </div>
      </div>
    </ImmyCardFooter>
  </ImmyCard>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { IGetTenantResponse } from "@/api/backend/generated/responses";
import { tenantsApi } from "@/api/backend/v1";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useAuthStore } from "@/store/pinia/auth-store";
import { useTenantsStore } from "@/store/pinia/tenants-store";
import { NEW_TENANT_ID } from "@/utils/constants";
import { toast } from "@/utils/toast";
import { WithRequiredProperty } from "@/utils/typescript-utils";
import ImmyCardHeader from "./ImmyCardHeader.vue";

const props = defineProps<{
  azureTenantId: string;
  azureTenantName: string;
  partnerPrincipalId: string | null;
  allowCreateAndEditLinks: boolean;
}>();
const emit = defineEmits<{
  (e: "tenant-linked", v: WithRequiredProperty<IGetTenantResponse, "azureTenantLink">): void;
  (e: "tenant-unlinked", v: IGetTenantResponse): void;
}>();
const tenantsStore = useTenantsStore();
const appAlertsStore = useAppAlertsStore();
const authStore = useAuthStore();

const updatingLinks = defineModel<boolean>("updatingLinks", { default: false });

const unlinkingImmyTenantId = ref<number | null>(null);
const showCreateLinkForm = ref(false);
const selectedDomain = ref<string | null>(null);
const selectedDomains = ref<string[]>([]);
const selectedTenantId = ref<number | null>(null);
const linkedDomainsVisibleMap = ref(new Map<number, boolean>());

const existingLinks = computed(() =>
  tenantsStore.allTenants
    .filter(t => t.azureTenantLink != null)
    .map(t => ({ linkedToTenantId: t.id })),
);
const partnerTenant = computed(() =>
  props.partnerPrincipalId != null
    ? tenantsStore.partnerTenants.find(
        t => t.azureTenantLink.azTenantId === props.partnerPrincipalId,
      )
    : null,
);
const azureTenant = computed(() => tenantsStore.azureTenantsMap.get(props.azureTenantId));
const azureTenantLinks = computed(() =>
  tenantsStore.azureTenantsHierarchy.get(props.azureTenantId),
);
const mainLink = computed(() =>
  azureTenantLinks.value?.find(l => !l.azureTenantLink.shouldLimitDomains),
);
const childLinks = computed(() =>
  azureTenantLinks.value?.filter(l => l.azureTenantLink.shouldLimitDomains),
);
const allLinkedDomains = computed(
  () =>
    azureTenantLinks.value
      ?.flatMap(l => l.azureTenantLink.limitToDomains)
      ?.map(d => d.domainName) ?? [],
);
const unlinkedDomains = computed(() =>
  azureTenant.value?.infoSyncedFromAzure?.domainNames?.filter(
    d => !allLinkedDomains.value.includes(d),
  ),
);
const domainOptions = computed(() =>
  (unlinkedDomains.value ?? []).filter(d => !selectedDomains.value.includes(d)),
);
const canCreateLink = computed(
  () => selectedDomains.value.length > 0 && selectedTenantId.value != null,
);
watch(selectedDomain, () => {
  if (selectedDomain.value) {
    selectedDomains.value.push(selectedDomain.value);
    selectedDomain.value = null;
  }
});

async function createLink() {
  if (selectedDomains.value.length > 0 && selectedTenantId.value != null) {
    await setLinkedTenant(selectedTenantId.value, selectedDomains.value);
    showCreateLinkForm.value = false;
    selectedTenantId.value = null;
    selectedDomains.value = [];
  }
}

function cancelCreateLink() {
  selectedTenantId.value = null;
  selectedDomains.value = [];
  showCreateLinkForm.value = false;
}

async function setLinkedTenant(tenantId: number, limitToDomains: string[] | null = null) {
  if (tenantId === NEW_TENANT_ID) {
    await createTenantFromCustomer(limitToDomains);
    return;
  }
  try {
    updatingLinks.value = true;
    const updateAzureTenantLinkRequestBody = {
      tenantId,
      principalId: props.azureTenantId,
      partnerPrincipalId: props.partnerPrincipalId ?? undefined,
      limitToDomains: limitToDomains ?? undefined,
      removeSyncedUsers: false,
      removeCustomersSyncedUsers: false,
      unlinkCustomers: false,
    };
    const updatedTenant = (await tenantsApi.updateAzureTenantLink(
      updateAzureTenantLinkRequestBody,
    )) as WithRequiredProperty<IGetTenantResponse, "azureTenantLink">;
    tenantsStore.updateCachedTenantById(tenantId, updatedTenant);
    toast.success(
      `Successfully linked ${
        limitToDomains?.length
          ? `${limitToDomains.length} domain${limitToDomains.length == 1 ? "" : "s"} of `
          : ""
      }Azure tenant ${props.azureTenantName} to Immy tenant ${updatedTenant.name}.`,
    );
    emit("tenant-linked", updatedTenant);
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Error occurred while linking the tenant.",
      details: err,
    });
  }
  finally {
    updatingLinks.value = false;
  }
}

async function createTenantFromCustomer(limitToDomains: string[] | null = null) {
  try {
    updatingLinks.value = true;
    const created = await tenantsStore.createTenant({
      isMsp: false,
      name: limitToDomains?.length ? limitToDomains[0] : props.azureTenantName,
      ownerTenantId: partnerTenant.value?.isMsp ? partnerTenant.value.id : authStore.tenantId!,
      principalId: props.azureTenantId,
      partnerPrincipalId: props.partnerPrincipalId ?? undefined,
      limitToDomains: limitToDomains ?? undefined,
    });

    toast.success(
      `Successfully created immy tenant ${created.name} linked to ${
        limitToDomains?.length
          ? `${limitToDomains.length} domain${limitToDomains.length == 1 ? "" : "s"} of `
          : ""
      }Azure tenant ${props.azureTenantName}.`,
    );
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Error occurred while creating the tenant.",
      details: err,
    });
  }
  finally {
    updatingLinks.value = false;
  }
}
</script>
