<!-- eslint-disable vue/no-v-html -->
<template>
  <div>
    <slot name="prefix" />
    <p v-if="prefix" class="mb-1 text-warning">
      {{ prefix }}
    </p>
    <div v-if="sourceMessage || date" class="d-flex flex-wrap justify-content-between">
      <div class="left">
        <span v-if="sourceMessage" class="text-warning">{{ sourceMessage }}</span>
      </div>
      <div v-if="showDate" class="right">
        <span v-if="date">{{ formatDate(date) }}</span>
      </div>
    </div>
    <div v-if="missingAccessTokenError != null" class="mb-1 d-flex align-items-end">
      <div class="left">
        <strong>The following scopes are needed to complete the operation</strong>
        <ul>
          <li v-for="s in missingAccessTokenError.requiredScopes.split(' ')" :key="s">
            <code>{{ s }}</code>
          </li>
        </ul>
        <ImmyButton
          size="sm"
          class="p-0"
          variant="link"
          @click="store.handleMissingAccessToken(missingAccessTokenError)"
        >
          Click here to provide consent for the missing scopes
        </ImmyButton>
      </div>
      <div class="right ml-1" />
    </div>
    <template v-else>
      <div v-if="focusedErrorDescription != null" class="my-1 d-flex align-items-end flex-wrap">
        <div class="left">
          <strong v-html="focusedErrorDescription.title" />
          &nbsp;<template v-if="focusedErrorDescription.subtitle">
            <br>
            <em v-html="focusedErrorDescription.subtitle" />&nbsp;
</template>
        </div>
        <div class="right">
          <ImmyButton
            size="sm"
            class="p-0"
            variant="link"
            @click="showFormattedError = !showFormattedError"
          >
            {{ showFormattedError ? "Hide" : "Show" }} error details
          </ImmyButton>
        </div>
      </div>
      <div
        v-if="credentialDetails != null && (!focusedErrorDescription || showFormattedError)"
        class="my-1"
      >
        <strong>Access Token Details:&nbsp;</strong>
        <p v-if="credentialDetails.tenantPrincipalId != null" class="my-0 ml-1">
          <em>Tenant:&nbsp;</em>
          <code>{{ credentialDetails.tenantPrincipalId }}</code>
        </p>
        <p v-if="credentialDetails.partnerPrincipalId != null" class="my-0 ml-1">
          <em>Partner Tenant:&nbsp;</em>
          <code>{{ credentialDetails.partnerPrincipalId }}</code>
        </p>
        <p v-if="credentialDetails.gotAccessTokenFrom != null" class="my-0 ml-1">
          <em>Token source:&nbsp;</em>
          <code>{{
            AccessTokenSourceExtended.GetTextByValue(credentialDetails.gotAccessTokenFrom)
          }}</code>
        </p>
        <p v-if="credentialDetails.resolvedClientId != null" class="my-0 ml-1">
          <em>Client ID:&nbsp;</em>
          <code>{{ credentialDetails.resolvedClientId }}</code>
        </p>
      </div>
      <div v-if="!focusedErrorDescription || showFormattedError" class="my-1">
        <strong>Error:&nbsp;</strong>
        <pre
          :style="{ 'color': 'unset', 'border-left': '2px solid red', 'white-space': 'pre-wrap' }"
          class="text-left pl-2"
        >{{ azErr?.formattedErrorMessage }}</pre>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, shallowRef, watch } from "vue";
import { AccessTokenSource } from "@/api/backend/generated/enums";
import { IAzureError, IAzureErrorLogItem } from "@/api/backend/generated/interfaces";
import AccessTokenSourceExtended from "@/helpers/enums/AccessTokenSourceExtended";
import { useAuthTokenAcquisitionStore } from "@/store/pinia/auth-token-acquisition-store";
import { useAzureErrorStore } from "@/store/pinia/azure-error-store";
import { formatDate } from "@/utils/misc";

const props = withDefaults(
  defineProps<{
    prefix?: string;
    azureError?: IAzureError;
    azureErrorLogId?: string;
    azureErrorLogItem?: IAzureErrorLogItem;
    showDate?: boolean;
  }>(),
  {
    prefix: undefined,
    azureError: undefined,
    azureErrorLogId: undefined,
    azureErrorLogItem: undefined,
    showDate: true,
  },
);
const store = useAuthTokenAcquisitionStore();
const azureErrorStore = useAzureErrorStore();

const azErrLogItem = shallowRef<IAzureErrorLogItem | null>(null);

watch(
  () => [props.azureErrorLogItem, props.azureErrorLogId] as const,
  async ([logItem, logItemId]) => {
    if (logItem != null)
      azErrLogItem.value = logItem;
    else if (logItemId != null)
      azErrLogItem.value = await azureErrorStore.getDxStoreForAllAzureErrors().byKey(logItemId);
    else
      azErrLogItem.value = null;
  },
  { immediate: true },
);

const azErr = computed(() => {
  if (props.azureError != null)
    return props.azureError;
  else
    return azErrLogItem.value?.azureError;
});

const sourceMessage = computed(() => azErrLogItem.value?.sourceMessage);
const date = computed(() => azErrLogItem.value?.createdDateUtc);

const showFormattedError = ref(false);

const oDataError = computed(() => azErr.value?.oDataError);
const msalError = computed(() => azErr.value?.msalError);
const missingAccessTokenError = computed(() => azErr.value?.missingAccessToken);
const partnerCenterApiError = computed(() => azErr.value?.partnerCenterApiResponseDetails);
const credentialDetails = computed(() => azErr.value?.credentialDetails);

const isInvalidClient = computed(() => {
  return msalError.value?.error === "invalid_client";
});
const msalErrorParts = computed<[string, string] | null>(() => {
  if (msalError.value == null)
    return null;

  const parts = msalError.value?.errorDescription.split(":");
  return [parts[0], parts.slice(1).join(":")];
});
const isInvalidClientSecret = computed(() => {
  return isInvalidClient.value && msalErrorParts.value?.[0] === "AADSTS7000215";
});
const isMissingReplyUrl = computed(() => {
  return msalErrorParts.value?.[0] === "AADSTS7000215";
});
const partnerCenterApiErrorContent = computed(() => {
  const content = partnerCenterApiError.value?.content;
  if (content == null)
    return null;
  try {
    return JSON.parse(content);
  }
  catch {
    return content;
  }
});
const isCustomAppRegInsufficientPrivilegeError = computed(() => {
  return (
    oDataError.value?.code === "Authorization_RequestDenied"
    && credentialDetails.value?.gotAccessTokenFrom === AccessTokenSource.CustomAppRegistration
  );
});
const isPartnerCenterInsufficientPrivilegeError = computed(() => {
  const parsedContent = partnerCenterApiErrorContent.value;
  if (parsedContent?.description == null)
    return false;
  try {
    const parsedDescription = JSON.parse(parsedContent.description);

    // So many formats this error can be returned in from azure...
    const odataError = parsedDescription?.["odata.error"] ?? parsedDescription?.error;
    return (
      odataError?.code === "Authorization_RequestDenied"
      && (odataError.message?.value ?? odataError.message)
      === "Insufficient privileges to complete the operation."
    );
  }
  catch {
    return false;
  }
});
const isDefaultOrCustomAppRegIdentityNotFoundError = computed(() => {
  return (
    (credentialDetails.value?.gotAccessTokenFrom === AccessTokenSource.CustomAppRegistration
      || credentialDetails.value?.gotAccessTokenFrom === AccessTokenSource.DefaultAppRegistration)
    && oDataError.value?.code === "Authorization_IdentityNotFound"
  );
});
const appRegName = computed(() => {
  if (credentialDetails.value?.gotAccessTokenFrom === AccessTokenSource.CustomAppRegistration)
    return "custom";
  else if (
    credentialDetails.value?.gotAccessTokenFrom === AccessTokenSource.DefaultAppRegistration
  )
    return "default";
  else
    return "";
});
const focusedErrorDescription = computed<{ title: string; subtitle?: string } | null>(() => {
  if (missingAccessTokenError.value) {
    return {
      title: "blah",
    };
  }
  if (isInvalidClientSecret.value) {
    return {
      title: "The client secret is invalid",
      subtitle: "Ensure the secret is the client secret value, not the client secret ID.",
    };
  }
  else if (isMissingReplyUrl.value) {
    return {
      title: "The reply URL is missing",
      subtitle:
        "Ensure a reply URL of type 'Web' is set for the app registration in the Azure portal. See <a href='https://docs.immy.bot/azure-graph-permissions-setup.html#custom' target='_blank'>the immy.bot documentation</a> for more information.",
    };
  }
  else if (isCustomAppRegInsufficientPrivilegeError.value) {
    return {
      title: "The custom app registration is missing a required permission.",
      subtitle:
        "Ensure the app registration has the required permissions and reconsent. See <a href='https://docs.immy.bot/azure-graph-permissions-setup.html#grant-permissions' target='_blank'>the immy.bot documentation</a> for more information.",
    };
  }
  else if (isPartnerCenterInsufficientPrivilegeError.value) {
    return {
      title: "The GDAP relationship might be missing a required role",
      subtitle:
        "We often see this 'Authorization_RequestDenied' error when the GDAP relationship does not have the Cloud Application Administrator role.",
    };
  }
  else if (isDefaultOrCustomAppRegIdentityNotFoundError.value) {
    return {
      title: `Consent hasn't been provided to the ${appRegName.value} app registration`,
      subtitle: "Ensure the app registration has been consented to by an administrator",
    };
  }
  else {
    return null;
  }
});
</script>
