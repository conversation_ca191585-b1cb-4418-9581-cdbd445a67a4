<template>
  <div class="azure-customer-mapper">
    <div id="az-customer-mapper-alerts">
      <ImmyAlert v-if="azureError != null" show variant="danger">
        <AzureError
          :azure-error="azureError"
          prefix="Error occurred when retrieving customers from Azure"
        />
      </ImmyAlert>
      <ImmyAlert
        v-else-if="sortedCustomers?.length === 0 && !fetchingCustomers"
        show
        variant="warning"
      >
        <p class="mb-0">
          No customers were found for this Azure partner. If you have customers, ensure you have
          delegated admin relationships with them.
        </p>
        <p v-if="azureTenantAuthDetails?.customAppRegSelected" class="mt-2 mb-0">
          <strong class="text-underline">Important:</strong>
          You should also check the permissions applied to your custom app registration to ensure
          you have the <code>DelegatedAdminRelationship.Read.All</code> permission added. Use the
          reconsent button above after making changes to the app registration.
        </p>
      </ImmyAlert>
      <ImmyAlert v-if="partnerCenterOrgProfileAzureError != null" show variant="danger">
        <AzureError
          :azure-error="partnerCenterOrgProfileAzureError"
          prefix="Error occurred when attempting to interact with the partner center API"
        />
      </ImmyAlert>
    </div>
    <div id="az-customer-mapper-toolbar" class="d-flex justify-content-between">
      <div class="left">
        <LoadButton
          class="m-1"
          :disabled="selectedLinkedCustomers.length === 0"
          :handler="syncSelectedTenants"
          variant="primary"
        >
          Sync users from {{ selectedLinkedCustomers.length }} selected customer{{
            selectedLinkedCustomers.length === 1 ? "" : "s"
          }}
        </LoadButton>
        <LoadButton
          variant="primary"
          class="mx-2"
          :title="`Bulk create tenant${selectedUnlinkedCustomers.length === 1 ? '' : 's'} for the ${
            selectedUnlinkedCustomers.length
          } selected unassigned customers`"
          :disabled="savingCustomers || selectedUnlinkedCustomers.length === 0"
          :handler="() => createTenants('selected')"
        >
          Import the {{ selectedUnlinkedCustomers.length }} selected unassigned customer{{
            selectedUnlinkedCustomers.length === 1 ? "" : "s"
          }}
        </LoadButton>
        <LoadButton
          variant="primary"
          :title="`Bulk create tenants for all ${unassignedCustomersCount} unassigned customers`"
          :disabled="unassignedCustomersCount === 0"
          :handler="() => createTenants('all')"
        >
          Import all customers
        </LoadButton>
        <LoadButton
          v-if="
            azureTenantAuthDetails?.defaultAppRegSelected
              || azureTenantAuthDetails?.customAppRegSelected
          "
          id="button-preconsent-selected-tenants"
          variant="primary"
          class="mx-2"
          :disabled="
            selectedLinkedCustomers.length === 0
              || preconsentingTenantIds.size > 0
              || gettingPreconsentAccessToken
          "
          :handler="preconsentSelectedTenants"
          :show-spinner="preconsentingTenantIds.size > 0 || gettingPreconsentAccessToken"
        >
          Pre-consent the {{ selectedLinkedCustomers.length }} selected customer{{
            selectedLinkedCustomers.length === 1 ? "" : "s"
          }}
          to the {{ azureTenantAuthDetails.defaultAppRegSelected ? "default" : "custom" }} app
          registration
        </LoadButton>
      </div>
      <div class="right">
        <LoadButton variant="primary" :handler="() => fetchCustomers()" title="Reload all customers">
          Refresh All Customers
        </LoadButton>
      </div>
    </div>
    <p v-if="sortedCustomers?.length">
      You can enable/disable the Azure User Sync job that runs every hour in
      <ImmyLink :to="{ name: 'Preferences' }">
        <span style="text-decoration: underline">preferences</span>
      </ImmyLink>.
    </p>
    <ImmyAlert
      v-if="
        inProgressPreconsents.size > 0
          || preconsentSuccesses.length > 0
          || preconsentFailures.length > 0
      "
      show
      :variant="preconsentFailures.length ? 'danger' : 'primary'"
    >
      <div class="d-flex align-items-end">
        <template v-if="inProgressPreconsents.size">
          <span v-if="inProgressPreconsents.size">
            {{
              inProgressPreconsents.size == 1
                ? "One pre-consent"
                : `${inProgressPreconsents.size} pre-consents`
            }}&nbsp;in-progress
          </span>
          <span v-if="preconsentSuccesses.length">
            {{
              preconsentSuccesses.length == 1
                ? "One pre-consent"
                : `${preconsentSuccesses.length} pre-consents`
            }}&nbsp;succeeded
          </span>
          <span v-if="preconsentFailures.length">
            {{
              preconsentFailures.length == 1
                ? "One pre-consent"
                : `${preconsentFailures.length} pre-consents`
            }}&nbsp;failed
          </span>
        </template>
        <template v-else>
          <span v-if="preconsentFailures.length === 0">
            {{
              preconsentSuccesses.length == 1
                ? "The pre-consent"
                : `All ${preconsentSuccesses.length} pre-consents`
            }}&nbsp;succeeded
          </span>
          <span v-else-if="preconsentSuccesses.length === 0">{{
            preconsentFailures.length == 1
              ? "The pre-consent"
              : `All ${preconsentFailures.length} pre-consents`
          }}&nbsp;failed</span>
          <span v-else>
            {{ preconsentSuccesses.length }} /
            {{ preconsentSuccesses.length + preconsentFailures.length }} pre-consents
            succeeded</span>
        </template>
        <ImmyButton
          v-if="inProgressPreconsents.size"
          sm
          class="py-0 px-1 ml-1"
          @click="showAllPreconsenting = !showAllPreconsenting"
        >
          {{ showAllPreconsenting ? "Hide" : "Show" }} in-progress
        </ImmyButton>
        <ImmyButton
          v-if="preconsentSuccesses.length"
          sm
          class="py-0 px-1 ml-1"
          @click="showAllPreconsentSuccesses = !showAllPreconsentSuccesses"
        >
          {{ showAllPreconsentSuccesses ? "Hide" : "Show" }} all successes
        </ImmyButton>
        <ImmyButton
          v-if="preconsentFailures.length"
          sm
          class="py-0 px-1 ml-1"
          @click="showAllPreconsentFailures = !showAllPreconsentFailures"
        >
          {{ showAllPreconsentFailures ? "Hide" : "Show" }} all failures
        </ImmyButton>
      </div>
    </ImmyAlert>
    <ProviderClientList
      ref="providerClientList"
      :loading="fetchingCustomers"
      table-id="azure-clients-list"
      :clients="sortedCustomers"
      :extra-columns="extraColumns"
      client-col-name="Azure Customer"
      hide-id-col
      hide-partner-tenants
      :enable-master-detail="false"
      client-col-width="400px"
      tenant-col-width="600px"
      :external-client-name-calculate-cell-value="client => JSON.stringify(client)"
      @selection-changed="setSelectedCustomers"
      @sync-tenant-button-clicked="
        emit('sync-tenants', [
          { azPrincipalId: $event.tenantId, azTenantName: $event.customerName },
        ])
      "
    >
      <template #customerConsent="{ customer }">
        <div v-if="customer.linkedToTenantId != null" class="d-flex flex-column">
          <template v-if="customer.lastGetUsersSyncResult?.attemptFailedErrorId == null">
            <span
              v-if="
                customer.lastGetUsersSyncResult?.attemptDateUtc != null
                  && (customer.azureTenant?.consentDetails.consentDateUtc != null
                    || customer.azureTenant?.consentDetails.consentedWith != null)
              "
              title="The last attempt to sync users from this customer succeeded. Click to expand details"
              style="cursor: pointer; text-decoration: underline"
              class="text-success"
              @click="toggleConsentDetailVisibilityForTenantId(customer.externalClientId)"
            >
              <i class="fa fa-check" /> Consent provided (click to expand details)
            </span>
            <template v-else>
              <span
                title="No user sync has yet been attempted for this customer. Click to expand details"
                style="cursor: pointer; text-decoration: underline"
                class="text-warning"
                @click="toggleConsentDetailVisibilityForTenantId(customer.externalClientId)"
              >
                <i class="fa fa-exclamation-triangle" /> Consent or user sync not yet attempted
                (click to begin)
              </span>
            </template>
          </template>
          <span
            v-else
            class="text-danger"
            style="cursor: pointer; text-decoration: underline"
            title="The last attempt to sync users from this customer failed. Click to expand"
            @click="toggleConsentDetailVisibilityForTenantId(customer.externalClientId)"
          >
            <i class="fa fa-exclamation-triangle" /> Last sync attempt failed (click to expand
            details)
          </span>
          <template
            v-if="
              customer.preconsenting
                || customer.preconsentSuccess != null
                || customer.preconsentFailure != null
            "
          >
            <small
              class="text-muted"
              style="cursor: pointer"
              @click="toggleMasterDetails(customer.externalClientId)"
            ><template v-if="customer.preconsenting"><i class="fa fa-spinner fa-spin" /> Preconsenting.</template>&nbsp;Click here to show/hide preconsent details</small>
          </template>
          <div
            v-if="showingConsentDetailsForAzurePrincipalIds.has(customer.externalClientId)"
            style="text-align: left"
            class="d-flex flex-column text-wrap"
          >
            <AzureError
              v-if="customer.lastGetUsersSyncResult?.attemptFailedErrorId != null"
              class="mb-3"
              :azure-error-log-id="customer.lastGetUsersSyncResult.attemptFailedErrorId"
            />
            <AzureTenantConsentDetails
              v-if="customer.azureTenant != null"
              :immy-tenant-id="customer.linkedToTenantId"
              :consent-details="customer.azureTenant.consentDetails"
              :last-get-users-attempt-result="customer.lastGetUsersSyncResult"
              :principal-id="customer.externalClientId"
              :partner-principal-id="partnerPrincipalId"
              consent-flow="customer-sync"
              check-propagated-text="attempt sync to recheck"
            />
            <p class="mb-1">
              <template v-if="customer.lastGetUsersSyncResult?.attemptDateUtc">
                <span class="text-muted">Last user sync:</span>
                {{ formatDate(customer.lastGetUsersSyncResult.attemptDateUtc) }}
                <span
                  v-if="customer.lastGetUsersSyncResult.attemptFailedErrorId != null"
                  class="text-danger"
                >(failed)</span>
                <span v-else class="text-success">(succeeded)</span>
              </template>
              <template v-else>
                <i class="fa fa-exclamation-triangle text-warning" />
                User sync has not yet been attempted
              </template>
            </p>
            <p class="mb-2 ml-2">
              <ImmyButton
                :disabled="syncingAzurePrincipalIds.has(customer.externalClientId)"
                size="sm"
                @click="
                  emit('sync-tenants', [
                    {
                      azPrincipalId: customer.externalClientId,
                      azTenantName: customer.externalClientName,
                    },
                  ])
                "
              >
                <i
                  v-if="syncingAzurePrincipalIds.has(customer.externalClientId)"
                  class="fa fa-spinner fa-spin mr-1"
                />Sync users from this Azure tenant
              </ImmyButton>
            </p>
          </div>
        </div>
        <span v-else>Link customer to an immy.bot tenant to get started</span>
      </template>
      <template #external-client-column="{ customer }">
        <TenantAzureDetails
          class="text-left"
          :azure-tenant="{
            principalId: customer.externalClientId,
            azureTenantType: AzTenantType.Customer,
            ...(customer.azureTenant ?? {}),
            infoSyncedFromAzure: {
              tenantName: customer.externalClientName,
              ...(customer.azureTenant?.infoSyncedFromAzure ?? {}),
            },
            consentDetails: customer.azureTenant?.consentDetails ?? {},
          }"
          hide-extra-domains-by-default
        />
      </template>
      <template #linked-tenant="{ customer }">
        <AzureTenantLinkedTenants
          v-model:updating-links="savingCustomers"
          :azure-tenant-id="customer.externalClientId"
          :azure-tenant-name="customer.externalClientName"
          :partner-principal-id="partnerPrincipalId"
          allow-create-and-edit-links
          @tenant-linked="
            emit('sync-tenants', [
              {
                azPrincipalId: customer.externalClientId,
                azTenantName: customer.externalClientName,
              },
            ])
          "
        />
      </template>
      <template #detail="{ data: customer }">
        <div class="p-2">
          <div class="d-flex justify-content-between mb-2 align-items-center">
            <div class="left">
              <template v-if="customer.preconsenting">
                <i class="fa fa-spinner fa-spin" /> Preconsent in progress
              </template>
            </div>
            <div class="right">
              <ImmyButton
                variant="secondary"
                @click="toggleMasterDetails(customer.externalClientId)"
              >
                <i class="fa fa-times" />
              </ImmyButton>
            </div>
          </div>
          <ImmyAlert
            v-if="
              customer.inProgressPreconsent?.value != null
                || customer.preconsentSuccess != null
                || customer.preconsentFailure != null
            "
            show
            :variant="
              customer.inProgressPreconsent
                ? 'warning'
                : customer.preconsentSuccess
                  ? 'primary'
                  : 'danger'
            "
          >
            <div class="d-flex flex-column">
              <strong v-if="customer.inProgressPreconsent == null">Preconsent {{ customer.preconsentSuccess ? "succeeded" : "failed" }} for
                {{ getTenantNameFromPrincipalId(customer.externalClientId) }}</strong>
              <AzureCustomerPreconsentResult
                :preconsent-result="
                  (customer.inProgressPreconsent?.value
                    ?? customer.preconsentSuccess
                    ?? customer.preconsentFailure)!
                "
              />
            </div>
          </ImmyAlert>
        </div>
      </template>
    </ProviderClientList>
  </div>
</template>

<script setup lang="ts">
import { computedAsync } from "@vueuse/core";
import { DxDataGridTypes } from "devextreme-vue/data-grid";
import { computed, Ref, ref, watch } from "vue";
import { ComponentExposed } from "vue-component-type-helpers";
import {
  AzTenantType,
  IAzureCustomerPreconsentResult,
  IAzureError,
  IAzureSyncResult,
  IAzureTenantCustomer,
  IAzureTenantLinkResponse,
  IAzureTenantResponse,
  IBulkCreateTenantRequestBody,
  IGetTenantResponse,
  IMissingAccessTokenDetails,
  ITenantInfoResult,
} from "@/api/backend/generated/contracts";
import UserHubEventInstance from "@/api/backend/signalr-hubs/UserHubEventInstance";
import { azureApi } from "@/api/backend/v1";
import { IAuthTokenAcquisitionFailure } from "@/composables/OauthTokenAcquisition";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useAuthStore } from "@/store/pinia/auth-store";
import { useAuthTokenAcquisitionStore } from "@/store/pinia/auth-token-acquisition-store";
import { useAzureTenantAuthStore } from "@/store/pinia/azure-tenant-auth-store";
import { useTenantsStore } from "@/store/pinia/tenants-store";
import { WrappedAzureError } from "@/utils/exceptions";
import { formatDate } from "@/utils/misc";
import { toast } from "@/utils/toast";
import { WithRequiredProperty } from "@/utils/typescript-utils";
import AzureCustomerPreconsentResult from "./AzureCustomerPreconsentResult.vue";
import AzureTenantConsentDetails from "./AzureTenantConsentDetails.vue";
import ImmyButton from "./ImmyButton.vue";
import ProviderClientList, { Client } from "./ProviderClientList.vue";

export interface IAzureCustomer extends Client {
  linkedToTenantId?: number;
  linkedToTenant?: IGetTenantResponse;
  azureTenantLinks: IAzureTenantLinkResponse[];
  linkedTenantIsMsp: boolean;
  externalClientId: string;
  externalClientName: string;
  spotlightCustomer: boolean;
  preconsenting: boolean;
  azureTenant: IAzureTenantResponse | undefined;
  consented: boolean;
  syncStatus: "succeeded" | "failed" | "none";
  lastGetUsersSyncResult: IAzureSyncResult | undefined;
  preconsentSuccess?: IAzureCustomerPreconsentResult;
  preconsentFailure?: IAzureCustomerPreconsentResult;
  inProgressPreconsent?: Ref<IAzureCustomerPreconsentResult>;
}

const props = defineProps<{
  partnerTenant: WithRequiredProperty<IGetTenantResponse, "azureTenantLink">;
  spotlightCustomerTenantId?: string;
  partnerTenantInfo: ITenantInfoResult | null;
  syncingAzurePrincipalIds: Set<string>;
}>();

const emit = defineEmits<{
  (e: "sync-tenants", v: { azPrincipalId: string; azTenantName: string }[]): void;
}>();

const auth = useAuthStore();
const appAlertsStore = useAppAlertsStore();
const tenantsStore = useTenantsStore();
const authTokenAcquisitionStore = useAuthTokenAcquisitionStore();

// name must match the "ref" used in the template
const providerClientList = ref<ComponentExposed<typeof ProviderClientList<IAzureCustomer>> | null>(
  null,
);
const selectedCustomers = ref<IAzureCustomer[]>([]);
const delegatedCustomers = ref<IAzureTenantCustomer[]>([]);
const azureError = ref<IAzureError | null>(null);
const partnerCenterOrgProfileAzureError = ref<IAzureError | null>(null);
const fetchingCustomers = ref(false);
const savingCustomers = ref(false);
const showingConsentDetailsForAzurePrincipalIds = ref(new Set<string>());
const preconsentingTenantIds = ref(new Set<string>());
const inProgressPreconsents = ref<Map<string, Ref<IAzureCustomerPreconsentResult>>>(new Map());
const preconsentSuccesses = ref<IAzureCustomerPreconsentResult[]>([]);
const preconsentFailures = ref<IAzureCustomerPreconsentResult[]>([]);
const gettingPreconsentAccessToken = ref(false);
const showAllPreconsentFailures = ref(false);
const showAllPreconsentSuccesses = ref(false);
const showAllPreconsenting = ref(false);

const partnerTenantId = computed(() => props.partnerTenant.id);
const partnerPrincipalId = computed(() => props.partnerTenant.azureTenantLink.azTenantId);
const azureTenantAuthDetails = computedAsync(
  () => useAzureTenantAuthStore(partnerPrincipalId.value),
  null,
);

const extraColumns = computed(() => {
  const r: (DxDataGridTypes.Column & { slotName: string })[] = [
    {
      slotName: "customerConsent",
      caption: "Consent",
      dataField: "consented",
      dataType: "boolean",
      minWidth: 375,
      alignment: "left",
      allowHeaderFiltering: true,
      headerFilter: {
        allowSelectAll: false,
        dataSource: [
          {
            text: "Consented & Syncing",
            value: [
              ["consented", "=", true],
              ["syncStatus", "=", "succeeded"],
            ],
          },
          {
            text: "Consented & Not Syncing",
            value: [
              ["consented", "=", true],
              ["syncStatus", "<>", "succeeded"],
            ],
          },
          { text: "Unconsented", value: [["consented", "=", false]] },
          { text: "Not Syncing", value: [["syncStatus", "<>", "succeeded"]] },
        ],
      },
      allowSearch: false,
      allowFiltering: false,
    },
  ];
  return r;
});

const customers = computed<IAzureCustomer[]>(() => {
  const allTenants = existingTenants.value;
  const agg = new Map<string, IAzureCustomer>();
  delegatedCustomers.value.forEach((c) => {
    const tenant = allTenants.find(
      t =>
        t.azureTenantLink?.azTenantId === c.tenantId
        && t.azureTenantLink.shouldLimitDomains === false,
    );
    const preconsentSuccess = preconsentSuccesses.value.find(
      p => p.customerPrincipalId === c.tenantId,
    );
    const preconsentFailure = preconsentFailures.value.find(
      p => p.customerPrincipalId === c.tenantId,
    );
    const consentDetails = tenant?.azureTenantLink?.azureTenant.consentDetails;
    const lastGetUsersSyncResult = tenant?.azureTenantLink?.azureTenant.lastGetUsersSyncResult;
    agg.set(c.tenantId, {
      linkedToTenantId: tenant ? tenant.id : undefined,
      linkedToTenant: tenant,
      azureTenantLinks: (azureTenantsHierarchy.value.get(c.tenantId) ?? []).map(
        a => a.azureTenantLink,
      ),
      externalClientId: c.tenantId,
      externalClientName: c.displayName,
      lastGetUsersSyncResult: tenant?.azureTenantLink?.azureTenant.lastGetUsersSyncResult,
      azureTenant: tenant?.azureTenantLink?.azureTenant,
      linkedTenantIsMsp: tenant?.isMsp ?? false,
      spotlightCustomer: c.tenantId === props.spotlightCustomerTenantId,
      preconsenting: tenant?.id != null && preconsentingTenantIds.value.has(c.tenantId),
      preconsentSuccess,
      preconsentFailure,
      inProgressPreconsent: inProgressPreconsents.value.get(c.tenantId),
      consented: consentDetails?.consentDateUtc != null || consentDetails?.consentedWith != null,
      syncStatus: lastGetUsersSyncResult?.attemptFailedErrorId
        ? "failed"
        : lastGetUsersSyncResult?.attemptDateUtc
          ? "succeeded"
          : "none",
    });
  });
  return [...agg.values()];
});

const sortedCustomers = computed<IAzureCustomer[]>(() => {
  const showFailures = showAllPreconsentFailures.value;
  const showSuccesses = showAllPreconsentSuccesses.value;
  const showPreconsenting = showAllPreconsenting.value;
  return [...customers.value].sort((a, b) => {
    if (a.spotlightCustomer && !b.spotlightCustomer)
      return -1;
    if (b.spotlightCustomer && !a.spotlightCustomer)
      return 1;

    if (showPreconsenting) {
      if (a.preconsenting && !b.preconsenting)
        return -1;
      if (b.preconsenting && !a.preconsenting)
        return 1;
    }
    if (showFailures) {
      if (a.preconsentFailure != null && b.preconsentFailure == null)
        return -1;
      if (b.preconsentFailure != null && a.preconsentFailure == null)
        return 1;
    }
    if (showSuccesses) {
      if (a.preconsentSuccess != null && b.preconsentSuccess == null)
        return -1;
      if (b.preconsentSuccess != null && a.preconsentSuccess == null)
        return 1;
    }
    return a.externalClientName.localeCompare(b.externalClientName);
  });
});

const existingTenants = computed<IGetTenantResponse[]>(() => tenantsStore.allTenants);
const azureTenantsHierarchy = computed(() => tenantsStore.azureTenantsHierarchy);

const unlinkedCustomers = computed(() => sortedCustomers.value.filter(a => !a.linkedToTenantId));
const selectedUnlinkedCustomers = computed<IAzureCustomer[]>(
  () => selectedCustomers.value.filter(a => !a.linkedToTenantId) as IAzureCustomer[],
);
const unassignedCustomersCount = computed(() => unlinkedCustomers.value.length);
const selectedLinkedCustomers = computed(
  () =>
    selectedCustomers.value.filter(a => a.linkedToTenantId) as WithRequiredProperty<
      IAzureCustomer,
      "linkedToTenantId"
    >[],
);

async function fetchCustomers() {
  fetchingCustomers.value = true;
  try {
    const results = await azureApi.getDelegatedAdminCustomers(partnerPrincipalId.value);
    const partnerResult = results[0];
    if ("message" in partnerResult.result) {
      delegatedCustomers.value = [];
      azureError.value = partnerResult.result;
    }
    else {
      delegatedCustomers.value = partnerResult.result;
      azureError.value = null;
    }
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An unexpected error occurred while loading delegated admin relationships from Azure",
      details: err,
    });
  }
  finally {
    fetchingCustomers.value = false;
  }
}

async function createTenants(whichOnes: "all" | "selected") {
  try {
    savingCustomers.value = true;
    const syncableCustomers: IAzureCustomer[]
      = whichOnes === "selected" ? selectedUnlinkedCustomers.value : unlinkedCustomers.value;
    const req: IBulkCreateTenantRequestBody = {
      ownerTenantId: props.partnerTenant.isMsp ? partnerTenantId.value : auth.tenantId!,
      tenants: syncableCustomers.map(a => ({
        name: a.externalClientName,
        principalId: a.externalClientId,
        partnerPrincipalId: partnerPrincipalId.value,
      })),
    };
    await tenantsStore.bulkCreateTenant(req);
    toast.success(`Successfully batch created tenants.`);
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Error occurred while batch creating tenants.",
      details: err,
    });
  }
  finally {
    savingCustomers.value = false;
  }
}

async function syncSelectedTenants() {
  emit(
    "sync-tenants",
    selectedLinkedCustomers.value.map(c => ({
      azPrincipalId: c.externalClientId,
      azTenantName: c.externalClientName,
    })),
  );
}

async function setupPreconsentSignalr(tenants: { principalId: string; customerName: string }[]) {
  const userHub = new UserHubEventInstance();
  const customerConsentsMap = new Map<
    string,
    { result: Ref<IAzureCustomerPreconsentResult>; finished: boolean }
  >();

  userHub.onEvent("AzureCustomerPreconsentStarted", (ev) => {
    const principalId = ev.customerTenantPrincipalId;
    if (principalId == null)
      return;
    if (!customerConsentsMap.has(principalId)) {
      const result = ref<IAzureCustomerPreconsentResult>({
        customerPrincipalId: principalId,
        messages: [],
      });
      customerConsentsMap.set(principalId, {
        result,
        finished: false,
      });
      inProgressPreconsents.value = new Map(
        [...customerConsentsMap.entries()]
          .filter(([, r]) => !r.finished)
          .map(([i, r]) => [i, r.result]),
      );
    }
  });
  userHub.onEvent("AzureCustomerPreconsentFinished", (ev) => {
    const principalId = ev.customerTenantPrincipalId;
    if (principalId == null)
      return;
    const preconsent = customerConsentsMap.get(principalId);
    if (preconsent == null)
      return;
    preconsent.finished = true;
    const finalResult = ev.result ?? preconsent.result.value;
    if (finalResult.messages.some(m => m.error != null && !m.isErrorNonFatal)) {
      preconsentFailures.value = [...preconsentFailures.value, finalResult];
    }
    else {
      preconsentSuccesses.value = [...preconsentSuccesses.value, finalResult];
      preconsentFailures.value = preconsentFailures.value.filter(
        f => f.customerPrincipalId !== ev.customerTenantPrincipalId,
      );
    }
    inProgressPreconsents.value = new Map(
      [...customerConsentsMap.entries()]
        .filter(([, r]) => !r.finished)
        .map(([i, r]) => [i, r.result]),
    );
    preconsentingTenantIds.value = new Set(
      [...preconsentingTenantIds.value].filter(t => t !== principalId),
    );
  });
  userHub.onEvent("AzureCustomerPreconsentProgressMessageAdded", (ev) => {
    const principalId = ev.customerTenantPrincipalId;
    if (principalId == null)
      return;
    const preconsent = customerConsentsMap.get(principalId);
    if (preconsent == null)
      return;
    preconsent.result.value.messages[ev.messageIndex] = ev.message;
    preconsent.result.value = { ...preconsent.result.value };
  });
  await userHub.joinAzureCustomerPreconsentGroups(partnerPrincipalId.value);

  try {
    return await new Promise<{ result: IAzureCustomerPreconsentResult[] } | { error: IAzureError }>(
      (resolve) => {
        userHub.onEvent("AzureMultiCustomerPreconsentFailed", (ev) => {
          if (ev.partnerTenantPrincipalId === partnerPrincipalId.value) {
            if (typeof ev.error === "string") {
              const e: IAzureError = { message: ev.error, formattedErrorMessage: ev.error };
              resolve({ error: e });
            }
            else {
              resolve({ error: ev.error.azureError });
            }
          }
        });
        userHub.onEvent("AzureMultiCustomerPreconsentFinished", (ev) => {
          if (ev.partnerTenantPrincipalId === partnerPrincipalId.value) {
            setTimeout(
              () => resolve({ result: [...customerConsentsMap.values()].map(r => r.result.value) }),
              3000,
            );
          }
        });
        azureApi.preconsentCustomerTenants({
          partnerPrincipalId: partnerPrincipalId.value,
          customerPrincipalIds: tenants.map(t => t.principalId),
        });
      },
    );
  }
  finally {
    await userHub.leaveAzureCustomerPreconsentGroups(partnerPrincipalId.value);
    userHub.clearCallbacks();
  }
}

async function preconsentTenants(
  tenants: { principalId: string; customerName: string }[],
  afterTokenAcquisition = false,
) {
  const tenantIds = new Map(tenants.map(t => [t.principalId, t]));
  if (afterTokenAcquisition) {
    // filter out the ones that succeeded on the last pass
    preconsentSuccesses.value.forEach(
      s => s.customerPrincipalId != null && tenantIds.delete(s.customerPrincipalId),
    );
  }
  else {
    preconsentSuccesses.value = [];
    preconsentFailures.value = [];
    showAllPreconsenting.value = true;
    showAllPreconsentFailures.value = true;
    showAllPreconsentSuccesses.value = true;
  }
  preconsentingTenantIds.value.forEach((t) => {
    const customerName = tenantIds.get(t)?.customerName;
    // Don't preconsent tenants that are already preconsenting
    tenantIds.delete(t)
    && toast.showError(`Preconsent already in progress for tenant ${customerName ?? t}`);
  });
  if (tenantIds.size === 0)
    return [];
  preconsentingTenantIds.value = new Set([...preconsentingTenantIds.value, ...tenantIds.keys()]);

  try {
    const resultOrError = await setupPreconsentSignalr([...tenantIds.values()]);
    if ("error" in resultOrError) {
      const err = resultOrError.error;
      if (
        err.missingAccessToken != null
        && err.missingAccessToken.requiredScopes
          ?.toLowerCase()
          ?.includes("https://api.partnercenter.microsoft.com/user_impersonation")
      ) {
        getPartnerCenterPreconsentAccessToken(tenants, err.missingAccessToken);
        return;
      }
      throw new WrappedAzureError(err);
    }
    const missingTokenFailures = preconsentFailures.value.filter(f =>
      f.messages.some(m => m.error?.azureError?.missingAccessToken != null),
    );
    if (missingTokenFailures.length) {
      const missingPartnerCenterToken = missingTokenFailures[0].messages.find(
        m =>
          m.error?.azureError?.missingAccessToken?.requiredScopes?.toLowerCase()
          === "https://api.partnercenter.microsoft.com/user_impersonation",
      )?.error?.azureError?.missingAccessToken;

      // appease typescript by doing a null check even though we know it's not null
      if (missingPartnerCenterToken != null)
        getPartnerCenterPreconsentAccessToken(tenants, missingPartnerCenterToken);
    }
  }
  finally {
    try {
      await tenantsStore.getAllTenants({ force: true });
    }
    catch {
      // ignore
    }
    preconsentingTenantIds.value = new Set(
      [...preconsentingTenantIds.value].filter(t => !tenantIds.has(t)),
    );
  }
}

async function preconsentSelectedTenants() {
  try {
    await preconsentTenants(
      selectedLinkedCustomers.value.map(s => ({
        tenantId: s.linkedToTenantId,
        principalId: s.externalClientId,
        customerName: s.externalClientName,
      })),
    );
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while pre-consenting the selected tenants.",
      details: err,
    });
  }
}

function toggleConsentDetailVisibilityForTenantId(tenantPrincipalId: string) {
  if (showingConsentDetailsForAzurePrincipalIds.value.has(tenantPrincipalId))
    showingConsentDetailsForAzurePrincipalIds.value.delete(tenantPrincipalId);
  else
    showingConsentDetailsForAzurePrincipalIds.value.add(tenantPrincipalId);

  showingConsentDetailsForAzurePrincipalIds.value = new Set(
    showingConsentDetailsForAzurePrincipalIds.value,
  );
}

function getTenantNameFromPrincipalId(principalId: string) {
  return (
    tenantsStore.allTenants.find(t => t.azureTenantLink?.azTenantId === principalId)?.name
    ?? `tenant with principal id=${principalId}`
  );
}

async function getPartnerCenterPreconsentAccessToken(
  tenantsBeingPreconsented: { principalId: string; customerName: string }[],
  missingAccessToken: IMissingAccessTokenDetails,
) {
  if (gettingPreconsentAccessToken.value)
    return;
  gettingPreconsentAccessToken.value = true;

  try {
    // trigger consent flow for the missing scopes
    await authTokenAcquisitionStore.handleMissingAccessToken(missingAccessToken, {
      mainText: "You must provide consent to allow immy.bot to access the Partner Center",
      secondaryText:
        "Please click the 'Provide access' button and authenticate with an account that is a "
        + "member of the 'AdminAgents' group in your partner tenant",
      allowIndefiniteAccessType: true,
    });
    preconsentTenants(tenantsBeingPreconsented, true);
  }
  catch (_err) {
    const err = _err as IAuthTokenAcquisitionFailure;
    if (err.code === "user-cancelled") {
      // do nothing
    }
    else if (err.code == "no-access-token-returned") {
      appAlertsStore.addAlert({
        text: "Preconsent failed - an error occurred performing the token request",
        details: new Error("The OAuth hook succeeded but no access token was returned"),
      });
    }
    else if (err.code === "oauth-error") {
      appAlertsStore.addAlert({
        text: "Preconsent failed - an error occurred performing the token request",
        details: new Error(JSON.stringify(err.failure)),
      });
    }
    else if (err.code === "unknown-error") {
      appAlertsStore.addAlert({
        text: "Preconsent failed - an unknown error occurred",
        details:
          err.failure instanceof Error
            ? err.failure
            : new Error(
              typeof err.failure === "string" ? err.failure : JSON.stringify(err.failure),
            ),
      });
    }
  }
  finally {
    gettingPreconsentAccessToken.value = false;
  }
}

function toggleMasterDetails(principalId: string) {
  providerClientList.value?.clientsDataGrid?.toggleMasterDetailsForRow(principalId);
}

function expandMasterDetails(principalId: string) {
  providerClientList.value?.clientsDataGrid?.expandRow(principalId);
}

function collapseMasterDetails(principalId: string) {
  providerClientList.value?.clientsDataGrid?.collapseRow(principalId);
}

function setSelectedCustomers(customers: IAzureCustomer[]) {
  selectedCustomers.value = customers.map(customer => ({
    ...customer,
    inProgressPreconsent: undefined,
    customerPrincipalId: "",
    customerTenantId: "",
    messages: [],
  }));
}

function resetState() {
  partnerCenterOrgProfileAzureError.value = null;
  azureError.value = null;
  delegatedCustomers.value = [];
  selectedCustomers.value = [];
  showingConsentDetailsForAzurePrincipalIds.value = new Set();
  preconsentingTenantIds.value = new Set();
  inProgressPreconsents.value = new Map();
  preconsentSuccesses.value = [];
  preconsentFailures.value = [];
  gettingPreconsentAccessToken.value = false;
  showAllPreconsentFailures.value = false;
  showAllPreconsentSuccesses.value = false;
  showAllPreconsenting.value = false;
}

watch(showAllPreconsenting, (v) => {
  if (v)
    preconsentingTenantIds.value.forEach(id => expandMasterDetails(id));
  else
    preconsentingTenantIds.value.forEach(id => collapseMasterDetails(id));
});

watch(showAllPreconsentFailures, (v) => {
  if (v)
    preconsentFailures.value.forEach(f => expandMasterDetails(f.customerPrincipalId));
  else
    preconsentFailures.value.forEach(f => collapseMasterDetails(f.customerPrincipalId));
});

watch(showAllPreconsentSuccesses, (v) => {
  if (v)
    preconsentSuccesses.value.forEach(f => expandMasterDetails(f.customerPrincipalId));
  else
    preconsentSuccesses.value.forEach(f => collapseMasterDetails(f.customerPrincipalId));
});

watch(
  () => azureTenantAuthDetails.value?.tenantPrincipalId,
  async (v) => {
    if (v != null) {
      resetState();
      await fetchCustomers();
    }
  },
  { immediate: true },
);
</script>

<style lang="scss">
.azure-customer-mapper {
  .dx-datagrid-rowsview .dx-row > .dx-master-detail-cell {
    padding: 0px;
  }

  .dx-datagrid-rowsview .dx-master-detail-r .dx-master-detail-row:not(.dx-datagrid-edit-form) ell {
    background-color: unset;
    border-bottom: unset;
  }

  .azure-customer-list-wrapper {
    padding: 1rem;
  }
}
</style>
