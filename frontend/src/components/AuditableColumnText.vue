<template>
  <div class="d-flex flex-column">
    <em v-if="userName" class="pii">{{ userName }}</em>
    <span v-if="formattedDate">{{ formattedDate }}</span>
  </div>
</template>

<script lang="ts" setup>
import dayjs from "dayjs";
import { computed } from "vue";
import { useUsersStore } from "@/store/pinia/users-store";

const props = defineProps<{
  userId?: number;
  date: string;
}>();

const usersStore = useUsersStore();

const userName = computed(() => {
  if (!props.userId)
    return null;
  return usersStore.namesByIds[props.userId];
});

const formattedDate = computed(() => {
  return props.date != null ? dayjs(props.date).format("MM/DD/YYYY HH:mm") : null;
});
</script>
