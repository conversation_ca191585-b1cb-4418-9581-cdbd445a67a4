<template>
  <div>
    <DxDataGrid
      v-if="loadErrors"
      :data-source="dxStore"
      :show-column-headers="false"
      data-row-template="rowTemplate"
    >
      <DxPaging :page-size="10" />
      <DxSorting mode="single" />
      <DxColumn data-field="createdDateUtc" sort-order="desc" :visible="false" />
      <DxColumn data-field="azureError" />
      <template #rowTemplate="{ data: rowInfo }">
        <tr>
          <td style="border-bottom: 1px solid white">
            <AzureError :azure-error-log-item="rowInfo.data" />
          </td>
        </tr>
      </template>
    </DxDataGrid>
  </div>
</template>

<script setup lang="ts">
import { DxColumn, DxDataGrid, DxPaging, DxSorting } from "devextreme-vue/data-grid";
import { ref, watch } from "vue";
import { useAzureErrorStore } from "@/store/pinia/azure-error-store";

const props = withDefaults(
  defineProps<{
    tenantPrincipalId: string;
    loadErrors?: boolean;
  }>(),
  {
    loadErrors: true,
  },
);

const emit = defineEmits<{
  (e: "num-errors", v: number): void;
}>();

const azureErrorStore = useAzureErrorStore();

const dxStore = azureErrorStore.getDxStoreForTenantAzureErrors(props.tenantPrincipalId);

const count = ref(0);

dxStore.totalCount({}).then((x) => {
  count.value = x;
});

watch(
  count,
  (newVal) => {
    emit("num-errors", newVal);
  },
  { immediate: true },
);
</script>
