<template>
  <span v-if="contextListIsEmpty" class="no-data">There are no metrics exposed</span>
  <ImmyListGroup v-else>
    <ImmyListGroupItem v-for="context in nonEmptyContexts" :key="context.context">
      <h3>
        <span class="small text-muted">Context:</span>
        <span>&nbsp;{{ context.context }}</span>
      </h3>
      <section>
        <ImmyRow>
          <ImmyCol
            v-for="meter in context.meters"
            :key="meter.name"
            :lg="getMetricWidth(context.context, meter.name)"
          >
            <AppMetricsMeter :model-value="meter" />
          </ImmyCol>
          <ImmyCol
            v-for="gauge in context.gauges"
            :key="gauge.name"
            :lg="getMetricWidth(context.context, gauge.name)"
          >
            <AppMetricsGauge :model-value="gauge" />
          </ImmyCol>
          <ImmyCol
            v-for="histogram in context.histograms"
            :key="histogram.name"
            :lg="getMetricWidth(context.context, histogram.name)"
          >
            <AppMetricsHistogram :model-value="histogram" />
          </ImmyCol>
          <ImmyCol
            v-for="counter in context.counters"
            :key="counter.name"
            :lg="getMetricWidth(context.context, counter.name)"
          >
            <AppMetricsCounter :model-value="counter" />
          </ImmyCol>
        </ImmyRow>
      </section>
    </ImmyListGroupItem>
  </ImmyListGroup>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { IMetricData, IMetricsContext } from "@/api/backend/generated/interfaces";

const props = defineProps<{
  modelValue: IMetricData;
}>();

const contextListIsEmpty = computed(() => {
  return (
    props.modelValue == null
    || props.modelValue.contexts == null
    || props.modelValue.contexts.length == 0
    || props.modelValue.contexts.every(c => contextHasNoMetrics(c))
  );
});

const nonEmptyContexts = computed(() => {
  return props.modelValue.contexts.filter(c => !contextHasNoMetrics(c));
});

function contextHasNoMetrics(context: IMetricsContext) {
  return (
    (context.apdexScores == null || context.apdexScores.length == 0)
    && (context.counters == null || context.counters.length == 0)
    && (context.gauges == null || context.gauges.length == 0)
    && (context.histograms == null || context.histograms.length == 0)
    && (context.meters == null || context.meters.length == 0)
    && (context.timers == null || context.timers.length == 0)
  );
}
function getMetricWidth(contextName: string, counterName: string) {
  // Hacky, but some metrics just need more width.
  if (
    contextName === "Ephemeral Agent Session Handler"
    && counterName === "Agent->Backend Script Bytes Received"
  )
    return 6;
  return 3;
}
</script>

<style></style>
