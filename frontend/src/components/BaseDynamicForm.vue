<!-- eslint-disable vue/no-v-html -->
<template>
  <div :id="props.id" class="dynamic-form">
    <div v-show="dynamicFormStore.showSimpleView">
      <DynamicFormSimpleView :form-id="props.formId" />
    </div>
    <div v-show="!dynamicFormStore.showSimpleView" v-if="dynamicFormStore.setNames.length === 1">
      <template v-for="p in dynamicFormStore.getParametersForSet()">
        <DynamicFormField
          v-if="!p.parameter.hidden && p.showField"
          :key="p.parameter.name"
          class="pb-3"
          :dynamic-form-id="props.id"
          :form-id="props.formId"
          set-name=""
          :parameter="p.parameter"
          :allow-input="!dynamicFormStore.readOnly"
          :error-text="p.errorText"
          :model-value="p.value"
          :tenant-id="dynamicFormStore.tenantId ?? undefined"
          :show-input="p.showInput"
          @parameter-value-changed="onParameterValueChanged($event, p.parameter)"
          @stripped-value-changed="onChangingStrippedValue($event, p)"
        >
          <template #parameterNameLine="{ parameter }">
            <slot name="parameterNameLine" :parameter="parameter" />
          </template>
          <template #afterParameterNameLine="{ parameter }">
            <slot name="afterParameterNameLine" :parameter="parameter" />
          </template>
          <template #overrideLink="{ parameter, fieldRef }">
            <slot name="overrideLink" :parameter="parameter" :field-ref="fieldRef" />
          </template>
          <template #defaultValue="{ parameter }">
            <slot name="defaultValue" :parameter="parameter" />
          </template>
        </DynamicFormField>
        <div v-else-if="p.errorText != null" :key="`${p.parameter.name}-error`">
          <label>{{ p.parameter.displayName ?? p.parameter.name }}</label>
          <p class="text-danger" v-html="markdownToHtml(p.errorText)" />
        </div>
      </template>
    </div>
    <ImmyTabs v-else v-show="!dynamicFormStore.showSimpleView">
      <template #tabs>
        <ImmyTabItem
          v-for="(set, index) in dynamicFormStore.setNames"
          :key="set"
          v-model="dynamicFormStore.selectedParameterSet"
          :tab-value="index"
        >
          {{ set }}
          <em v-if="index === dynamicFormStore.resolvedParameterSetIndex" class="ml-2 text-success">(Resolved)</em>
        </ImmyTabItem>
      </template>
      <template #content>
        <ImmyTabBody
          v-for="(set, index) in dynamicFormStore.setNames"
          v-show="index == dynamicFormStore.selectedParameterSet"
          :key="set"
        >
          <template v-for="p in dynamicFormStore.getParametersForSet(set)">
            <DynamicFormField
              v-if="!p.parameter.hidden && p.showField"
              :key="p.parameter.name"
              class="pb-3"
              :dynamic-form-id="props.id"
              :form-id="props.formId"
              :set-name="set"
              :parameter="p.parameter"
              :allow-input="!dynamicFormStore.readOnly"
              :error-text="p.errorText"
              :model-value="p.value"
              :tenant-id="dynamicFormStore.tenantId ?? undefined"
              :show-input="p.showInput"
              @parameter-value-changed="onParameterValueChanged($event, p.parameter)"
            >
              <template #parameterNameLine="{ parameter }">
                <slot name="parameterNameLine" :parameter="parameter" />
              </template>
              <template #afterParameterNameLine="{ parameter }">
                <slot name="afterParameterNameLine" :parameter="parameter" />
              </template>

              <template #overrideLink="{ parameter, fieldRef }">
                <slot name="overrideLink" :field-ref="fieldRef" :parameter="parameter" />
              </template>
              <template #defaultValue="{ parameter }">
                <slot name="defaultValue" :parameter="parameter" />
              </template>
            </DynamicFormField>
          </template>
        </ImmyTabBody>
      </template>
    </ImmyTabs>
    <p v-if="dynamicFormStore.nonFieldError" class="whitespace-prewrap text-danger mt-2">
      {{ dynamicFormStore.nonFieldError }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { watch } from "vue";
import { IParameter, IParameterValue } from "@/api/backend/generated/interfaces";

import { IParameterWithValue, useDynamicFormStore } from "@/store/pinia/dynamic-form-store";
import { markdownToHtml } from "@/utils/htmlSanitization";

interface IProps {
  /** unique identifier for the form  */
  id: string;
  /** unique identifier for the dynamic form store */
  formId: string;
  /** title of the form */
  title?: string;
  /** subtitle of the form */
  subtitle?: string;
}

const props = defineProps<IProps>();

const emits = defineEmits<{
  (e: "form:loaded"): void;
  (e: "parameters:changed"): void;
}>();

let dynamicFormStore = useDynamicFormStore(props.formId);
emits("form:loaded");

watch(() => props.formId, (val) => {
  dynamicFormStore = useDynamicFormStore(val);
  emits("form:loaded");
});

function onParameterValueChanged(val: IParameterValue, parameter: IParameter) {
  dynamicFormStore.setParameterValue(parameter, val);
  emits("parameters:changed");
}

function onChangingStrippedValue(val: boolean, parameter: IParameterWithValue) {
  dynamicFormStore.setChangingStrippedValue(parameter.parameter.name, val);
  emits("parameters:changed");
}
</script>

<style lang="scss" scoped>
:deep(.tabs) {
  .tab-content {
    display: inherit !important;
    background: inherit !important;
    margin-top: 0.5rem;
  }

  .i-tab {
    border-bottom: 1px solid var(--divider);
  }

  .i-tab-content {
    padding: 1rem;

    .dynamic-form-field:last-child {
      padding-bottom: 0 !important;
    }
  }
}
.dynamic-form {
  background: var(--assignment-with-task-section-bg-color);
  .dynamic-form-field:last-child {
    padding-bottom: 0 !important;
  }
}
</style>
