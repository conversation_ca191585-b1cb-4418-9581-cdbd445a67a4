<template>
  <div class="agent-identification-logs-stream">
    <div class="filter-container">
      <ImmyInput
        v-model="state.filter"
        class="w-100"
        placeholder="Filter..."
        @update:model-value="onFilterChanged"
      />
    </div>
    <XtermVue
      ref="xtermRef"
      class="xterm-wrapper"
      :font-size="12"
      background-color="#0e1010"
      font-family="Roboto Mono"
    />
  </div>
</template>

<script setup lang="ts">
import styles from "ansi-styles";
import { debounce } from "lodash";
import { onBeforeUnmount, onMounted, reactive, Ref, ref, watch } from "vue";
import xterm from "xterm";
import { AgentIdentificationLogType } from "@/api/backend/generated/contracts";
import UserHub from "@/api/backend/signalr-hubs/user-hub";
import UserHubEventInstance from "@/api/backend/signalr-hubs/UserHubEventInstance";

import XtermVue from "@/components/XtermVue.vue";
import { formatDate } from "@/utils/misc";

const props = withDefaults(defineProps<IProps>(), {
  streaming: false,
});

const userHubEventInstance = new UserHubEventInstance();

// - setup signlar listener for identification logs
// - add logs as they come in
// - allow for pulling initial logs from a given time

interface IProps {
  streaming?: boolean;
}

interface IState {
  logs: string[];
  tail: boolean;
  filter: string | null;
}

const state: IState = reactive({
  logs: [],
  tail: true,
  filter: null,
});

const xtermRef: Ref<XtermVue | null> = ref(null);

let terminal: xterm.Terminal | null = null;

const onFilterChanged = debounce(() => {
  if (xtermRef.value) {
    // clear the terminal, and re-add all logs matching filter
    const x = xtermRef.value;
    x.clear();
    const filter = state.filter;
    if (!filter?.length) {
      state.logs.forEach(l => terminal?.writeln(l));
    }
    else {
      state.logs.forEach((l) => {
        if (matchesFilter(filter, l))
          terminal?.writeln(l);
      });
    }
  }
}, 350);

function matchesFilter(filter: string, line: string) {
  const lineUpper = line.toUpperCase();
  const filterUpper = filter.toUpperCase();
  return lineUpper.includes(filterUpper);
}

onMounted(async () => {
  if (xtermRef.value) {
    const x = xtermRef.value as XtermVue;
    terminal = x.getXterm();
  }
  userHubEventInstance.onEvent(UserHub.Client.AddAgentIdentificationLog, async (log) => {
    const time = `${styles.color.ansi16m(...styles.hexToRgb("#8d8d8d"))}${formatDate(
      log.timeUtc,
      "mm/dd/yy h:MM:sstt Z",
    )}${styles.color.close}`;
    const deviceName = log.deviceName;
    let message = log.message;

    if (log.logType === AgentIdentificationLogType.Error) {
      message = `${styles.color.ansi16m(...styles.hexToRgb("#fa759f"))}${message}${
        styles.color.close
      }`;
    }

    const line = `${time} - ${deviceName} - ${message}`;
    if ((state.filter?.length && matchesFilter(state.filter, line)) || !state.filter?.length)
      terminal?.writeln(line);

    state.logs.push(line);
  });

  userHubEventInstance.onHubReconnected(async () => {
    if (props.streaming)
      await userHubEventInstance.joinAgentIdentificationLogsGroup();
  });

  window.onresize = () => {
    if (xtermRef.value) {
      const x = xtermRef.value as XtermVue;
      x.fit();
    }
  };
});

onBeforeUnmount(async () => {
  userHubEventInstance.clearCallbacks();
  await userHubEventInstance.leaveAgentIdentificationLogsgroup();
});

watch(
  () => props.streaming,
  async (val) => {
    if (val)
      await userHubEventInstance.joinAgentIdentificationLogsGroup();
    else
      await userHubEventInstance.leaveAgentIdentificationLogsgroup();
  },
  {
    immediate: true,
  },
);
</script>

<style lang="scss" scoped>
.xterm-wrapper {
  padding: 0.5rem;
  height: 100%;
  width: 100%;
}

.filter-container {
  padding: 0.5rem;
  border-bottom: 1px solid var(--divider);
  input {
    max-width: 100%;
  }
}
</style>
