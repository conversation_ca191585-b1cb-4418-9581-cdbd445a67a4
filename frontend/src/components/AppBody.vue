<template>
  <div class="app-body">
    <AuthTokenAcquisitionModal />
    <RemoteControlModal />
    <AppSidebar v-if="pageLayoutStore.showAppSidebar" :nav-items="navItems" />
    <SideBarContainer v-if="isAuthorized" />
    <MachineOnboarding v-if="authStore.showGettingStartedWizard" @dismissed="handleGettingStartedWizardDismissed" />
    <NewFrontendVersionAlert
      v-if="newFrontendVersionNotice && !newFrontendVersionNoticeDismissed"
      :frontend-version="newFrontendVersionNotice.frontendVersion"
      @dismissed="newFrontendVersionNoticeDismissed = true"
    />
    <main class="main">
      <app-alert v-if="messages.length" class="global-app-alert" is-global :messages="messages" />
      <div class="container-fluid my-4 px-0">
        <ImmyBotRemoteControlFeatureNotSupportedAlert
          v-if="showImmyBotRemoteControlFeatureNotSupportedAlert"
        />
        <div class="animated fadeIn">
          <ImmyAlert v-if="isInstanceUpdating" variant="warning" show class="text-center">
            <strong v-if="instanceUpdateSource === 'administrative'">
              immy.bot is being updated.
            </strong>
            <strong v-else-if="instanceUpdateSource === 'customer'">
              An administrator is updating immy.bot.
            </strong>
            <br>
            <strong>immy.bot will be restarted after the update is applied.</strong>
          </ImmyAlert>
          <ImmyAlert v-if="isInstanceRestarting" variant="warning" show class="text-center">
            <strong>immy.bot is restarting.</strong>
            <br>
          </ImmyAlert>
          <ImmyAlert v-if="authStore.impersonating" show variant="warning">
            <div class="d-flex align-items-center justify-content-between">
              <span>You are currently impersonating {{ `${userDisplayName}` }} {{ `(${userEmail})` }}.</span>
              <LoadButton
                variant="warning"
                :handler="stopImpersonating"
                data-testid="stop-impersonating-button"
              >
                Stop Impersonating
              </LoadButton>
            </div>
          </ImmyAlert>
          <Breadcrumb v-if="isAuthenticated" class="page-breadcrumb" />
          <router-view v-slot="{ Component, route }">
            <component :is="Component" :key="route.meta.force ? route.fullPath : null" />
          </router-view>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { usersApi } from "@/api/backend/v1";
import { useLayout } from "@/composables/Layout";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { useAuthStore } from "@/store/pinia/auth-store";
import { useNavigationStore } from "@/store/pinia/navigation-store";
import { useNotificationsStore } from "@/store/pinia/notifications-store";
import { usePageLayoutStore } from "@/store/pinia/page-layout-store";
import { usePreferencesStore } from "@/store/pinia/preferences-store";
import AuthTokenAcquisitionModal from "./AuthTokenAcquisitionModal.vue";

const {
  isAuthorized,
  isInstanceUpdating,
  instanceUpdateSource,
  isInstanceRestarting,
  isAuthenticated,
} = useLayout();

const appAlertsStore = useAppAlertsStore();
const navigationStore = useNavigationStore();
const notificationsStore = useNotificationsStore();
const preferencesStore = usePreferencesStore();
const authStore = useAuthStore();
const pageLayoutStore = usePageLayoutStore();
const newFrontendVersionNoticeDismissed = ref(false);

const userDisplayName = computed(() => authStore.displayName);
const userEmail = computed(() => authStore.emailAddress);

const navItems = computed(() => {
  return navigationStore.navItems;
});
const newFrontendVersionNotice = computed<{ frontendVersion: string } | null>(() => {
  return notificationsStore.newFrontendVersionNotice;
});
const messages = computed(() => {
  return appAlertsStore.alerts;
});

const showImmyBotRemoteControlFeatureNotSupportedAlert = computed(
  () => authStore.showImmyBotRemoteControlFeatureNotSupportedAlert,
);

async function handleGettingStartedWizardDismissed() {
  if (!authStore.showGettingStartedWizard) {
    // if it was opened automatically by the app preference and then dismissed
    // then we need to update that preference as well
    await preferencesStore.patchAppPreferences("showGettingStartedWizard", false);
  }
  authStore.showGettingStartedWizard = false;
}

async function stopImpersonating() {
  await usersApi.stopImpersonatingUser();
  location.reload();
}
</script>

<style lang="scss" scoped>
$breakpoint-tablet: 768px;
@media (max-width: $breakpoint-tablet) {
  .main {
    .container-fluid {
      margin-top: 1rem !important;
      padding: 0 10px !important;
    }
  }
}

@media (max-width: 930px) {
  .main {
    padding-left: 0;
    padding-right: 0;
  }
}

.overlay {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.global-app-alert {
  position: sticky;
  top: 1rem;
}
</style>
