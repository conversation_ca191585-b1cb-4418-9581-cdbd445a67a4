<template>
  <div class="app-alert-message">
    <span>{{ message.text }}</span>
    <template v-if="message.details">
      <br>
      <ImmyButton class="p-0" variant="link" @click="showingDetails = !showingDetails">
        Show Details
      </ImmyButton>
      <ImmyCard v-if="showingDetails">
        <AzureError v-if="azureError" :azure-error="azureError" />
        <pre v-else>{{ message.details }}</pre>
      </ImmyCard>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { IAlert } from "@/store/pinia/app-alert-store";
import { WrappedAzureError } from "@/utils/exceptions";

const props = defineProps<{
  message: IAlert;
}>();

const showingDetails = ref(false);
const azureError = computed(() => {
  return props.message.details instanceof WrappedAzureError
    ? props.message.details.azureError
    : null;
});
</script>

<style lang="scss" scoped>
.app-alert-message {
  &:not(:last-child) {
    padding-bottom: 1em;
  }

  white-space: break-spaces;

  animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

pre {
  white-space: pre-wrap;
  word-break: break-word;
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
}
:deep(.card) {
  background-color: var(--bg-danger) !important;
  color: var(--danger) !important;
  border: none !important;
  pre {
    color: var(--danger);
  }

  .card-body {
    padding: 0;
  }
}
</style>
