<template>
  <VueSelect
    key="azure-group-selector"
    v-model="model"
    data-testid="AzureGroupSelector-select"
    :options="options"
    label="displayName"
    placeholder="Search for groups..."
    :select-on-tab="true"
    :filterable="false"
    append-to-body
    :reduce="selectorReduceFn"
    :loading="loading"
    @search="onSearch"
    @update:model-value="onSelected"
  />
</template>

<script setup lang="ts">
import debounce from "lodash.debounce";
import { onMounted, ref } from "vue";
import VueSelect from "vue-select";
import { IGetAzureGroupResponse } from "@/api/backend/generated/responses";
import { tenantsApi } from "@/api/backend/v1";
import { useAppAlertsStore } from "@/store/pinia/app-alert-store";

const props = defineProps<{
  tenantId: number;
}>();

const emit = defineEmits<{
  (e: "update:azure-group", group: IGetAzureGroupResponse | null | undefined): void;
}>();

const model = defineModel<string | null>();

const options = ref<IGetAzureGroupResponse[]>([]);

const loading = ref(false);

function onSearch(value: string): void {
  if (!value)
    return;
  search(value);
}

const appAlertsStore = useAppAlertsStore();
const search = debounce(async (filter: string) => {
  loading.value = true;
  try {
    options.value = await tenantsApi.getAzureGroupsAtTenant(props.tenantId, { search: filter });
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred searching for azure groups",
      details: err,
    });
  }
  finally {
    loading.value = false;
  }
}, 200);

function onSelected(value: string): void {
  const group = options.value.find(v => v.id === value);
  emit("update:azure-group", group);
}

function selectorReduceFn(group: IGetAzureGroupResponse) {
  return group.id;
}

/**
 * Fetch the azure group if one was initially provided to us
 */
onMounted(async () => {
  if (model.value == null)
    return;

  try {
    const group = await tenantsApi.getAzureGroupAtTenant(model.value, props.tenantId);

    if (group == null)
      return;

    options.value = [group];
    emit("update:azure-group", group);
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred loading the selected azure group",
      details: err,
    });
  }
});
</script>

<style scoped lang="scss">

</style>
