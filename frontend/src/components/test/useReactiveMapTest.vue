<template>
  <div>
    <button class="add-button" @click="addOrUpdateItem" />
    <button class="delete-button" @click="deleteItem" />
    <ul>
      <li v-for="[key, value] in map" :key="key">
        {{ value }}
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useReactiveMap } from "@/composables/useReactiveMap";

const { addOrUpdateItemInMap, removeItemInMap } = useReactiveMap();

const map = ref(new Map<number, { id: number; value: string }>());

let id = 0;
function addOrUpdateItem() {
  id++;
  addOrUpdateItemInMap(map, { id, value: id.toString() }, "id", false);
}

function deleteItem() {
  removeItemInMap(map, id);
}
</script>

<style scoped></style>
