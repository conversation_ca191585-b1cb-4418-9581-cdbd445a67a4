<template>
  <ImmyCard
    header-tag="strong"
    header-class="p-1"
    body-class="p-1"
    class="mb-1"
    :header="modelValue.name"
  >
    <ImmyCardText class="mb-1">
      Last 1 minute:
      <em>
        {{ Math.round((modelValue.oneMinuteRate + Number.EPSILON) * 100) / 100 }}
        {{ modelValue.unit }}/{{ modelValue.rateUnit }}
      </em>
    </ImmyCardText>
    <ImmyCardText class="mb-1">
      Last 5 minutes:
      <em>
        {{ Math.round((modelValue.fiveMinuteRate + Number.EPSILON) * 100) / 100 }}
        {{ modelValue.unit }}/{{ modelValue.rateUnit }}
      </em>
    </ImmyCardText>
    <ImmyCardText class="mb-1">
      Last 15 minutes:
      <em>
        {{ Math.round((modelValue.fifteenMinuteRate + Number.EPSILON) * 100) / 100 }}
        {{ modelValue.unit }}/{{ modelValue.rateUnit }}
      </em>
    </ImmyCardText>
  </ImmyCard>
</template>

<script setup lang="ts">
import { IMeterMetric } from "@/api/backend/generated/interfaces";

defineProps<{
  modelValue: IMeterMetric;
}>();
</script>

<style></style>
