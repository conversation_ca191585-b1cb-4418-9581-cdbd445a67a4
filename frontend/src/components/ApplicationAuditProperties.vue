<template>
  <div>
    <div class="affected-properties">
      <h4>Modified Properties</h4>
      <div class="row audit-property-row">
        <div class="col-4">
          <strong>Property</strong>
        </div>
        <div class="col-4">
          <strong>Old Value</strong>
        </div>
        <div class="col-4">
          <strong>New Value</strong>
        </div>
      </div>
      <div v-for="property in properties" :key="property.name" class="row audit-property-row">
        <div class="col-4">
          {{ property.name }}
        </div>
        <div class="col-4">
          <span>{{ property.oldValue }}</span>
        </div>
        <div class="col-4">
          <span>{{ property.newValue }}</span>
        </div>
      </div>

      <monaco-diff-editor
        v-if="showDiffEditor"
        :id="`diff-editor-${props.audit.id}`"
        :old-value="oldScriptActionValue"
        :new-value="newScriptActionValue"
        readonly
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { IAudit } from "@/api/backend/generated/interfaces";

interface IPropertyRow {
  name: string;
  oldValue?: unknown;
  newValue?: unknown;
}

const props = defineProps<{
  audit: IAudit;
}>();

const showDiffEditor = computed(() => {
  return props.audit.objectType === "Script";
});

const affectedProperties = computed(() => {
  return props.audit.affectedProperties
    ? (JSON.parse(props.audit.affectedProperties) as string[])
    : null;
});

const oldValues = computed(() => {
  const vals: Record<string, unknown> = props.audit.oldValues
    ? JSON.parse(props.audit.oldValues)
    : {};
  return vals;
});

const oldScriptActionValue = computed(() => {
  return (oldValues.value.Action ?? "") as string;
});

const newScriptActionValue = computed(() => {
  return (newValues.value.Action ?? "") as string;
});

const newValues = computed(() => {
  const vals: Record<string, unknown> = props.audit.newValues
    ? JSON.parse(props.audit.newValues)
    : {};
  return vals;
});

function isScriptActionProperty(property: string) {
  return props.audit.objectType === "Script" && property === "Action";
}

function tryGetJson(value: unknown) {
  try {
    if (typeof value != "string")
      return value;
    return JSON.parse(value);
  }
  catch {
    return value;
  }
}

const properties = computed(() => {
  const ret: IPropertyRow[] = [];

  // only show affected property changes
  if (affectedProperties.value?.length) {
    for (const property of affectedProperties.value) {
      if (isScriptActionProperty(property))
        continue;
      const oldValue = tryGetJson(oldValues.value[property]);
      const newValue = tryGetJson(newValues.value[property]);
      const propertyName = property;

      ret.push({
        name: propertyName,
        oldValue,
        newValue,
      });
    }

    return ret;
  }

  if (props.audit.type === "Create") {
    // just show new values
    Object.keys(newValues.value).forEach((key) => {
      if (isScriptActionProperty(key))
        return;
      ret.push({
        name: key,
        newValue: tryGetJson(newValues.value[key]),
      });
    });
  }

  if (props.audit.type === "Delete") {
    // show old values
    Object.keys(oldValues.value).forEach((key) => {
      if (isScriptActionProperty(key))
        return;
      ret.push({
        name: key,
        newValue: tryGetJson(oldValues.value[key]),
      });
    });
  }

  return ret;
});
</script>

<style lang="scss" scoped>
.affected-properties > ul > li {
  margin: 0;
  padding: 0;
  list-style: none;
}
:deep(.table) {
  background: var(--bg-default2) !important;

  td {
    overflow: auto;
    @extend .immy-scrollbar;
  }
}

.affected-properties {
  pre {
    color: var(--text-primary);
  }
}

.audit-property-row:not(:last-child) {
  border-bottom: 1px solid var(--divider);
}
.audit-property-row {
  padding: 0.5rem 0;
  white-space: pre-wrap;
}
</style>
