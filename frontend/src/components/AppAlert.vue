<template>
  <ImmyAlert
    v-model="show"
    class="page-alert-message"
    variant="danger"
    :class="{ sticky }"
    dismissible
    fade
    @dismissed="onDismissed"
  >
    <div class="d-flex align-items-center">
      <div class="message-body">
        <app-alert-message
          v-for="message in resolveMessages"
          :key="message.id"
          :message="message"
        />
      </div>
      <div v-if="showIfProblemPersistsText">
        If this issue persists, please contact
        <a href="#">support</a>
        for assistance.
      </div>
    </div>
  </ImmyAlert>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { getAppInsights, getIsAppInsightsEnabled } from "@/config/app-insights";
import { IAlert, useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { NotCurrentAuthenticatedMessage } from "@/utils/misc";

const props = withDefaults(defineProps<{
  showIfProblemPersistsText?: boolean;
  messages: IAlert[];
  sticky?: boolean;
  isGlobal?: boolean;
}>(), {
  showIfProblemPersistsText: false,
  sticky: true,
  isGlobal: false,
});

const emit = defineEmits(["dismissed"]);

// simply mark the err as tracked if app insights is disabled
// todo: remove any type
let trackException = function (err: any) {
  err.tracked = true;
};

// track to app insights if enabled
if (getIsAppInsightsEnabled()) {
  trackException = function (err) {
    try {
      const data = Object.assign(
        {},
        err.response && {
          response: err.response,
        },
        err.extras,
      );

      // don't track 401 errors
      if (err.message === NotCurrentAuthenticatedMessage)
        return;

      getAppInsights()?.trackException(err, data);
    }
    finally {
      err.tracked = true;
    }
  };
}

// resolve the message and do not add duplicates
function resolveArray(arr: IAlert[], isGlobal: boolean) {
  return arr.reduce((agg, cur) => {
    const msg = resolveMessage(cur, isGlobal);
    const ind = agg.findIndex(a => a.text === msg.text);
    if (ind === -1)
      agg.push(msg);
    else agg.splice(ind, 1, msg);
    return agg;
  }, [] as IAlert[]);
}

// track exception if present and handle message
function resolveMessage(msg: IAlert, isGlobal: boolean) {
  // decide what text to immediately show the user
  // then put other data inside of details that can be expanded

  let text = msg.text;
  let details = msg.details;

  // track error
  if (msg.details instanceof Error) {
    // todo: remove any type
    if ((!msg.details as any).tracked)
      trackException(msg.details);

    console.error(msg.details);
  }

  // todo: remove any type
  const res = (msg.details as any)?.response;

  if (res) {
    // handle expected 4xx messages
    if (res.status === 400 || res.status === 409 || res.status === 403 || res.status === 422) {
      text = res.data?.detail ?? `${text} - ${res.data?.title || res.data}`;
      details = res.data?.errors ?? null;
    }
    else if (res.status === 404 && isGlobal) {
      // for 404 messages and the global alert, show the following default 404 text if we have no data
      text = res.data ?? "404: The requested resource could not be found.  It may no longer exist.";
      details = null;
    }
    else {
      details = res.data;
    }
  }
  return {
    id: msg.id,
    text,
    details,
  };
}

const appAlertsStore = useAppAlertsStore();

const show = ref(true);

const resolveMessages = computed(() => resolveArray(props.messages, props.isGlobal));

watch(() => props.messages, () => {
  show.value = true;
}, { deep: true });

function onDismissed() {
  // if this is the global app alert, then use pinia to clear the messages
  // otherwise emit an event to the parent to handle the dismissed event
  if (props.isGlobal)
    appAlertsStore.clear();
  else
    emit("dismissed");
}
</script>

<style lang="scss" scoped>
.page-alert-message {
  z-index: 99999;
  border-radius: 0px;
  border: none;
  background: #ffd9d9;
  border-left: 5px solid #e65e5e;
  font-size: 1em;
  color: black;
  box-shadow: none;
}

.sticky {
  position: sticky;
}

ul {
  margin: 0;
}
</style>
