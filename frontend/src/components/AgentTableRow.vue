<template>
  <div class="agent-row w-100">
    <div class="d-flex align-items-start flex-wrap flex-column">
      <div class="d-flex align-items-center justify-content-between gap-3 w-100 flex-wrap">
        <div class="d-flex align-items-center flex-wrap">
          <ComputerIcon
            class="mb-lg-0 mb-3 mr-3"
            :online="props.agent.isOnline"
            :supports-running-scripts="supportsRunningScripts"
          />
          <div class="agent-data">
            <ImmyLink
              v-if="props.agent.computerId && !props.agent.isComputerDeleted"
              class="m-0 mb-1"
              :to="{
                name: 'Computer Details',
                params: { computerId: props.agent.computerId?.toString() },
              }"
            >
              <span>{{ props.agent.computerName }}</span>
            </ImmyLink>
            <p v-else class="m-0 mb-1">
              {{ props.agent.computerName }}
              <span v-if="props.agent.requireManualIdentification" class="text-warning">(Unknown Tenant)</span>
            </p>
            <p class="agent-computer-details d-flex align-items-center flex-wrap m-0">
              <span title="Integration">{{ props.agent.providerLinkName }}</span>
              <span class="pii" title="Serial">{{ props.agent.serial }}</span>
              <span title="OS">{{ props.agent.operatingSystemName }}</span>
              <span title="Agent ID">{{ props.agent.externalAgentId }}</span>
              <span v-if="props.agent.isComputerDeleted" title="Associated to deleted computer" class="text-danger">Associated to a deleted computer</span>
            </p>
            <p class="agent-tenant d-flex align-items-center m-0">
              <span class="pii" title="Tenant">{{ props.agent.externalClientName }}</span>
            </p>
            <span class="agent-date">Added {{ formatDate(props.agent.dateAdded) }}</span>
          </div>
        </div>
        <div class="agent-row-right mt-2 mb-lg-0 d-flex flex-wrap align-items-center gap-1">
          <LoadButton
            v-if="props.agent.failed && permissionStore.can('computers:identify_agents')"
            variant="secondary"
            :disabled="state.resolvingFailures"
            :handler="() => resolveFailuresForAgent(props.agent.id)"
          >
            Attempt Identification Again
          </LoadButton>
          <template v-if="props.agent.requireManualIdentification && !identifiedAgent && permissionStore.can('computers:identify_agents')">
            <TenantSelectBox
              v-model="selectedTenant"
              class="identify-agent-tenant-dropdown"
              placeholder="Select a tenant to assign to this agent"
            />
            <LoadButton
              :disabled="selectedTenant == null"
              variant="warning"
              :handler="identifyAgent"
            >
              Identify
            </LoadButton>
          </template>
          <span
            v-else-if="props.agent.requireManualIdentification && identifiedAgent"
            class="text-success"
          >Agent has been enqueued for identification.</span>
          <ImmyButton v-else @click="state.showIdentificationLogs = !state.showIdentificationLogs">
            {{ state.showIdentificationLogs ? "Hide" : "Show" }} Identification Logs
          </ImmyButton>
        </div>
      </div>
      <span v-if="props.agent.requireManualIdentification" class="text-warning m-2">This agent is not associated to any known tenant and requires manual identification. Click
        the "Identify" button.</span>
      <AgentIdentificationLogsList
        v-if="state.showIdentificationLogs"
        class="mt-3"
        :provider-agent-id="agent.id"
      />
      <div v-if="props.agent.failed && props.agent.identificationFailures.length">
        <ul class="failure-list">
          <li>
            <i class="fal fa-triangle-exclamation mr-2" />Failed to identify the agent. Last
            attempted on
            {{ getLastAttemptedDate(props.agent.identificationFailures) }}
          </li>
          <li class="mt-1">
            <span>{{ getLastAttemptedResponse(props.agent.identificationFailures) }}</span>
          </li>
        </ul>
      </div>
      <div v-if="!props.agent.isOnline" class="mt-3 offline-message">
        <em>This agent is offline and identification can only run when it is online.</em>
      </div>
      <div v-if="props.agent.requiresManualResolution" class="mt-3">
        <PendingComputerConflictResolver
          :pending-conflict="agent"
          layout="horizontal"
          @finished="emit('resolutionActionTaken')"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import {
  IGetAgentIdentificationFailureResponse,
  IGetPendingAgentResponse,
} from "@/api/backend/generated/contracts";
import { providerAgentsApi } from "@/api/backend/v1";

import { useAppAlertsStore } from "@/store/pinia/app-alert-store";
import { usePermissionStore } from "@/store/pinia/permission-store";
import { useProviderLinksStore } from "@/store/pinia/provider-links-store";
import { formatDate } from "@/utils/misc";
import { doesProviderHaveCapability } from "@/utils/providers";

interface IProps {
  agent: IGetPendingAgentResponse;
}

interface IState {
  showIdentificationLogs: boolean;
  resolvingFailures: boolean;
}

const props = defineProps<IProps>();

const emit = defineEmits<{
  (e: "resolutionActionTaken"): void;
}>();

const state: IState = reactive({
  showIdentificationLogs: false,
  resolvingFailures: false,
});

const providerLinksStore = useProviderLinksStore();
const permissionStore = usePermissionStore();
const supportsRunningScripts = computed(() => {
  const link = providerLinksStore.cachedProviderLinks.get(props.agent.providerLinkId);
  return link != null && doesProviderHaveCapability(link, "IRunScriptProvider");
});

const appAlertsStore = useAppAlertsStore();

function getLastAttemptedDate(failures: IGetAgentIdentificationFailureResponse[]) {
  if (!failures?.length)
    return;
  return formatDate(failures[failures.length - 1].createdDateUTC);
}

async function resolveFailuresForAgent(agentId: number) {
  if (state.resolvingFailures)
    return;
  state.resolvingFailures = true;
  try {
    await providerAgentsApi.resolveFailuresForAgents({ agentIds: [agentId], allAgents: false });
    emit("resolutionActionTaken");
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "An error occurred while resolving the failures.",
      details: err,
    });
  }
  finally {
    state.resolvingFailures = false;
  }
}

const selectedTenant = ref<number>();
const identifiedAgent = ref(false);
async function identifyAgent() {
  if (props.agent.id == null || selectedTenant.value == null)
    return;
  const res = await providerAgentsApi.identifyAgents({
    agentIds: [props.agent.id],
    tenantId: selectedTenant.value,
  });
  if (res.isSuccess) { identifiedAgent.value = true; }
  else {
    appAlertsStore.addAlert({
      text: res.reason,
    });
  }
}

function getLastAttemptedResponse(failures: IGetAgentIdentificationFailureResponse[]) {
  if (!failures.length)
    return;
  return failures[failures.length - 1].message;
}
</script>

<style lang="scss" scoped>
.agent-computer-name {
}

.agent-computer-details {
  font-size: 0.75rem;
  font-weight: 400;
  color: var(--text-secondary);

  > * {
    &:not(:last-child) {
      &:after {
        content: "\2022";
        padding: 0 8px;
      }
    }
  }
}

.agent-tenant {
  font-size: 0.75rem;
  font-weight: 400;
  color: var(--text-primary);
}

.agent-date {
  font-size: 0.75rem;
  font-weight: 400;
  color: var(--text-primary);
}

.failure-list {
  color: var(--danger);
  list-style: none;
  margin: 0;
  padding: 0;
  margin-top: 0.5rem;
  max-height: 500px;
  overflow: auto;
  overflow-wrap: anywhere;
  @extend .immy-scrollbar;
}

.offline-message {
  color: var(--text-secondary);
}

.identify-agent-tenant-dropdown {
  min-width: 300px;
}
</style>
