<template>
  <div class="access-requests">
    <access-request v-for="r in requests" :key="r.id" :request="r" />
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from "vue";
import { ExpirationTime, IAccessRequestResponse } from "@/api/backend/generated/contracts";
import UserHub from "@/api/backend/signalr-hubs/user-hub";
import UserHubEventInstance from "@/api/backend/signalr-hubs/UserHubEventInstance";
import { personsApi } from "@/api/backend/v1";

export interface IAccessRequestResponseViewModel extends IAccessRequestResponse {
  isAdmin: boolean;
  expirationTime: ExpirationTime;
  success: boolean | null;
  message: string | null | undefined;
  hasManagementAccess: boolean;
  roleIds: number[];
}

function makeViewModel(model: IAccessRequestResponse) {
  return Object.assign(model, {
    id: model.personId,
    isAdmin: true,
    expirationTime: ExpirationTime.Indefinite,
    success: null,
    message: null,
    hasManagementAccess: true,
    roleIds: [],
  });
}

const requests = ref<IAccessRequestResponseViewModel[]>([]);
const userHubEventInstance = ref<UserHubEventInstance>(new UserHubEventInstance());

onMounted(async () => {
  const persons = await personsApi.getPersonsRequestingAccess();
  requests.value = persons.map(makeViewModel);
  userHubEventInstance.value.onEvent(UserHub.Client.AccessRequested, async (request) => {
    if (requests.value.some(r => r.id === request.id))
      return;
    requests.value.push(makeViewModel(request));
  });
});

onBeforeUnmount(() => {
  userHubEventInstance.value.clearCallbacks();
});
</script>

<style lang="scss" scoped></style>
