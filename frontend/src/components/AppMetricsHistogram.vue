<template>
  <ImmyCard
    header-tag="strong"
    header-class="p-1"
    body-class="p-1"
    class="mb-1"
    :header="modelValue.name"
  >
    <ImmyCardText class="mb-1">
      (statistics derived from <b>{{ modelValue.sampleSize }}</b> samples [<b>{{
        modelValue.count
      }}</b>
      samples total])
    </ImmyCardText>
    <ImmyCardText class="mb-1">
      Maximum:
      <em>
        {{ Math.round((modelValue.max + Number.EPSILON) * 100) / 100 }} {{ modelValue.unit }}
      </em>
    </ImmyCardText>
    <ImmyCardText class="mb-1">
      Minimum:
      <em>
        {{ Math.round((modelValue.min + Number.EPSILON) * 100) / 100 }} {{ modelValue.unit }}
      </em>
    </ImmyCardText>
    <ImmyCardText class="mb-1">
      Median:
      <em>
        {{ Math.round((modelValue.median + Number.EPSILON) * 100) / 100 }} {{ modelValue.unit }}
      </em>
    </ImmyCardText>
    <ImmyCardText class="mb-1">
      75th Percentile:
      <em>
        {{ Math.round((modelValue.percentile75 + Number.EPSILON) * 100) / 100 }}
        {{ modelValue.unit }}
      </em>
    </ImmyCardText>
    <ImmyCardText class="mb-1">
      95th Percentile:
      <em>
        {{ Math.round((modelValue.percentile95 + Number.EPSILON) * 100) / 100 }}
        {{ modelValue.unit }}
      </em>
    </ImmyCardText>
    <ImmyCardText class="mb-1">
      98th Percentile:
      <em>
        {{ Math.round((modelValue.percentile98 + Number.EPSILON) * 100) / 100 }}
        {{ modelValue.unit }}
      </em>
    </ImmyCardText>
    <ImmyCardText class="mb-1">
      99th Percentile:
      <em>
        {{ Math.round((modelValue.percentile99 + Number.EPSILON) * 100) / 100 }}
        {{ modelValue.unit }}
      </em>
    </ImmyCardText>
    <ImmyCardText class="mb-1">
      Standard Deviation:
      <em>
        {{ Math.round((modelValue.stdDev + Number.EPSILON) * 100) / 100 }} {{ modelValue.unit }}
      </em>
    </ImmyCardText>
  </ImmyCard>
</template>

<script setup lang="ts">
import { IHistogramMetric } from "@/api/backend/generated/interfaces";

defineProps<{
  modelValue: IHistogramMetric;
}>();
</script>

<style></style>
