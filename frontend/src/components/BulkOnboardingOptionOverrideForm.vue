<template>
  <div class="d-flex flex-column align-items-end">
    <div class="d-flex align-items-center">
      <span class="mr-2">Select a User Override for all parameters</span>
      <ImmyRadioGroup
        v-model="onboardingOptionsSelectedValue"
        :options="onboardingOptions"
      />
    </div>
    <ImmyButton
      v-if="onboardingOptionsSelectedValue != null"
      class="mt-2"
      @click="handleUpdateClick(onboardingOptionsSelectedValue)"
    >
      Update
    </ImmyButton>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ParameterOverridePolicy } from "@/composables/DynamicFormFieldOverridableHelpText";

const emit = defineEmits<{
  (e: "update:modelValue", val: ParameterOverridePolicy): void;
}>();

const onboardingOptions = [
  {
    text: "None",
    value: ParameterOverridePolicy.NotAllowed,
  },
  {
    text: "Allow",
    value: ParameterOverridePolicy.Allow,
  },
  {
    text: "Require",
    value: ParameterOverridePolicy.Require,
  },
];

const onboardingOptionsSelectedValue = ref<ParameterOverridePolicy>();

function handleUpdateClick(value: ParameterOverridePolicy) {
  emit("update:modelValue", value);
  onboardingOptionsSelectedValue.value = undefined;
}
</script>
