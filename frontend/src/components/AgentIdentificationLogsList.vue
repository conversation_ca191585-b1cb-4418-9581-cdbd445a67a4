<template>
  <div v-if="state.loading">
    Loading
  </div>
  <div v-else-if="!state.logs.length">
    No identification logs
  </div>
  <ImmyList
    v-else
    class="agent-identification-logs"
    :store-state="false"
    :paged="false"
    :show-footer="false"
    :show-hr="false"
    :show-search="false"
    :items="state.logs"
    :total-count="state.logs.length"
    input-placeholder="Search..."
  >
    <template #item="{ data }">
      <AgentIdentificationLogItem :log="data" />
    </template>
  </ImmyList>
</template>

<script lang="ts" setup>
import { onMounted, reactive } from "vue";
import { IGetProviderAgentIdentificationLogResponse } from "@/api/backend/generated/contracts";
import { providerAgentsApi } from "@/api/backend/v1";

import { useAppAlertsStore } from "@/store/pinia/app-alert-store";

interface IProps {
  providerAgentId: number;
}

interface IState {
  logs: IGetProviderAgentIdentificationLogResponse[];
  loading: boolean;
}

const props = defineProps<IProps>();

const appAlertsStore = useAppAlertsStore();

const state: IState = reactive({
  logs: [],
  loading: false,
});

onMounted(async () => {
  try {
    state.loading = true;
    state.logs = await providerAgentsApi.getIdentificationLogs(props.providerAgentId);
  }
  catch (err) {
    appAlertsStore.addAlert({
      text: "Failed to retrieve agent identification logs",
      details: err,
    });
  }
  finally {
    state.loading = false;
  }
});
</script>

<style lang="scss" scoped></style>
