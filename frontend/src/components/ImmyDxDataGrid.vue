<!--
  todo: This component should be deprecated.
  See https://gitlab.immense.net/immense-networks/immybot/immybot/-/issues/2477
 -->
<template>
  <Overlay :show="showOverlay">
    <DxDataGrid
      :id="id"
      ref="dataGrid"
      v-model:selected-row-keys="selectedRowKeys"
      class="immy-dx-data-grid"
      :data-source="dataSource"
      :column-auto-width="columnAutoWidth"
      :column-min-width="100"
      :columns="columns"
      :summary="summary"
      :sort-by-group-summary-info="sortByGroupSummaryInfo"
      :show-row-lines="showRowLines"
      :show-borders="true"
      :row-alternation-enabled="false"
      :allow-column-reordering="true"
      :show-column-headers="showColumnHeaders"
      :allow-column-resizing="true"
      :show-column-lines="false"
      column-resizing-mode="widget"
      :remote-operations="remoteOperations"
      :hover-state-enabled="hoverStateEnabled"
      :key-expr="keyExpr"
      :width="width"
      :repaint-changes-only="repaintChangesOnly"
      :word-wrap-enabled="wordWrapEnabled"
      @selection-changed="onSelectionChanged"
      @toolbar-preparing="onToolbarPreparing"
      @row-prepared="onRowPrepared"
      @row-updated="onRowUpdated"
      @exporting="onExporting"
      @content-ready="onContentReady"
      @cell-dbl-click="onCellDblClick"
      @row-expanding="onRowExpanding"
      @option-changed="onOptionChanged"
      @cell-prepared="onCellPrepared"
    >
      <DxExport :enabled="exportEnabled" />

      <template #title-template>
        <h4 v-if="title" class="m-0">
          {{ title }}
        </h4>
        <slot v-else name="toolbar-before" />
      </template>
      <template #time-filter>
        <DxSelectBox
          v-model="state.timeFilter"
          class="time-filter"
          :items="timeFilterOptions"
          display-expr="text"
          value-expr="value"
        />
      </template>
      <template #refresh>
        <ImmyButton :disabled="state.autoRefresh" size="sm" variant="link" @click="refresh">
          Refresh
        </ImmyButton>
      </template>
      <template #auto-refresh>
        <i
          :title="state.autoRefresh ? 'Turn off auto refresh' : 'Turn on auto refresh'"
          role="button"
          class="text-primary" :class="{
            'fad fa-play-circle': !state.autoRefresh,
            'fad fa-stop-circle': state.autoRefresh,
          }"
          @click="state.autoRefresh = !state.autoRefresh"
        />
      </template>
      <template #clearFilter>
        <ImmyButton size="sm" variant="link" class="mr-2" @click="clearFilter">
          Reset
        </ImmyButton>
      </template>
      <DxEditing :allow-updating="allowUpdating" mode="cell" />

      <DxLoadPanel :enabled="showLoadPanel" />

      <DxGroupPanel :visible="allowGrouping" />

      <DxGrouping :auto-expand-all="state.autoExpandAll" :visible="allowGrouping" />

      <DxSelection
        v-if="selection && selectionMode === 'multiple'"
        :select-all-mode="selectAllMode"
        show-check-boxes-mode="always"
        mode="multiple"
      />
      <DxSelection v-else-if="selection && selectionMode === 'single'" mode="single" />

      <DxColumnChooser :enabled="!hideColumnChooser" mode="select" />

      <DxStateStoring
        v-if="storeState"
        :enabled="true"
        type="custom"
        :custom-load="loadState"
        :custom-save="saveState"
        :storage-key="id"
      />

      <DxScrolling :scroll-by-content="false" :scroll-by-thumb="true" show-scrollbar="always" :mode="virtualScrollEnabled ? 'infinite' : undefined" />

      <DxFilterRow
        v-if="showFilterRow"
        :visible="showFilterRow"
        :apply-filter="state.currentFilter"
      />

      <DxHeaderFilter :visible="showHeaderFilter" />

      <DxSearchPanel :visible="showSearchPanel" :width="240" placeholder="Search..." />

      <DxPaging :page-size="pageSize" />

      <DxPager
        v-if="state.pageSizes.length > 1"
        :show-page-size-selector="true"
        :allowed-page-sizes="state.pageSizes"
        :show-info="true"
      />

      <DxMasterDetail :enabled="enableMasterDetail" template="mdt" />

      <template #mdt="{ data: masterDetailCellInfo }">
        <div>
          <slot
            name="detail"
            :cell-info="masterDetailCellInfo"
            :hide-details="() => hideDetails(masterDetailCellInfo)"
          />
        </div>
      </template>

      <template v-for="gct in groupCellTemplates" #[gct]="{ data: groupCellInfo }" :key="gct">
        <div>
          <slot :name="`groupCell(${gct})`" :cell-info="groupCellInfo" />
        </div>
      </template>

      <template v-for="ct in cellTemplates" #[ct]="{ data: columnCellInfo }" :key="ct">
        <div>
          <slot
            :name="ct"
            :data="columnCellInfo.data"
            :cell-info="columnCellInfo"
            :show-details="() => showDetails(columnCellInfo)"
            :hide-details="() => hideDetails(columnCellInfo)"
            :showing-details="state.expandedRows.has(columnCellInfo.row.key)"
          />
        </div>
      </template>

      <template v-for="(x, ind) in headerCellTemplates" #[x]="{ data: headerCellInfo }" :key="ind">
        <div>
          <slot :name="`headerCell(${x})`" :cell-info="headerCellInfo" />
        </div>
      </template>
    </DxDataGrid>
  </Overlay>
</template>

<script setup lang="ts" generic="T, K = string">
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import {
  DxColumnChooser,
  DxDataGrid,
  DxDataGridTypes,
  DxEditing,
  DxExport,
  DxFilterRow,
  DxGrouping,
  DxGroupPanel,
  DxHeaderFilter,
  DxLoadPanel,
  DxMasterDetail,
  DxPager,
  DxPaging,
  DxScrolling,
  DxSearchPanel,
  DxSelection,
  DxStateStoring,
} from "devextreme-vue/data-grid";
import { DxSelectBox } from "devextreme-vue/select-box";
import CustomStore from "devextreme/data/custom_store";
import { Options } from "devextreme/data/data_source";
import DataGrid, { ColumnCellTemplateData, ColumnGroupCellTemplateData, ColumnHeaderCellTemplateData, ExplicitTypes, MasterDetailTemplateData, OptionChangedEvent } from "devextreme/ui/data_grid";
import { computed, reactive, ref, watch } from "vue";
import { IEnumExtensionOptions } from "@/assets/js/enums/EnumExtensions";
import { useMountedPoller } from "@/composables/useMountedPoller";
import EnumTextHelpers from "@/helpers/enums/EnumTextHelpers";
import { TimeFilter } from "@/helpers/enums/TimeFilterExtended";
import { usePageVisibilityStore } from "@/store/pinia/page-visibility-store";

const props = withDefaults(
  defineProps<{
    dataSource: T[] | CustomStore<T, K> | Options<any, any, T, K>;
    columns: ImmyDataGridTypes["Column"][];
    id: string;
    summary?: ImmyDataGridTypes["Summary"];
    sortByGroupSummaryInfo?: DxDataGridTypes.SortByGroupSummaryInfoItem[];
    selection?: boolean;
    storeState?: boolean;
    showFilterRow?: boolean;
    remoteOperations?: boolean | object;
    allowUpdating?: boolean;
    allowGrouping?: boolean;
    enableMasterDetail?: boolean;
    noDetails?: boolean;
    showLoadPanel?: boolean;
    selectionMode?: "none" | "single" | "multiple";
    selectAllMode?: "page" | "allPages";
    hoverStateEnabled?: boolean;
    keyExpr?: string | string[] | null;
    showSearchPanel?: boolean;
    showRefresh?: boolean;
    showAutoRefresh?: boolean;
    initialRefreshValue?: boolean;
    showTimeFilter?: boolean;
    hideColumnChooser?: boolean;
    columnAutoWidth?: boolean;
    width?: string | null;
    onRowPrepared?: (e: ImmyDataGridTypes["RowPreparedEvent"]) => void;
    exportEnabled?: boolean;
    wordWrapEnabled?: boolean;
    loading?: boolean;
    pageSize?: number;
    title?: string | null;
    showHeaderFilter?: boolean;
    hideToolbar?: boolean;
    repaintChangesOnly?: boolean;
    refreshOnHeaderCellTemplateChange?: boolean;
    pageSizes?: number[];
    onCellPrepared?: (e: ImmyDataGridTypes["CellPreparedEvent"]) => void;
    virtualScrollEnabled?: boolean;
    showRowLines?: boolean;
    showColumnHeaders?: boolean;
  }>(),
  {
    sortByGroupSummaryInfo: () => [],
    selection: false,
    storeState: true,
    showFilterRow: true,
    remoteOperations: false,
    allowUpdating: false,
    allowGrouping: false,
    enableMasterDetail: false,
    noDetails: false,
    showLoadPanel: false,
    selectionMode: "multiple",
    selectAllMode: "page",
    hoverStateEnabled: false,
    keyExpr: null,
    showSearchPanel: true,
    showRefresh: false,
    showAutoRefresh: true,
    initialRefreshValue: false,
    showTimeFilter: false,
    hideColumnChooser: false,
    columnAutoWidth: true,
    width: null,
    onRowPrepared: () => {},
    exportEnabled: false,
    wordWrapEnabled: false,
    loading: false,
    pageSize: 50,
    title: null,
    showHeaderFilter: false,
    hideToolbar: false,
    repaintChangesOnly: false,
    summary: undefined,
    refreshOnHeaderCellTemplateChange: false,
    pageSizes: () => [10, 25, 50],
    onCellPrepared: () => {},
    virtualScrollEnabled: false,
    showRowLines: true,
    showColumnHeaders: true,
  },
);

const emit = defineEmits<{
  "row-updated": [e: ImmyDataGridTypes["RowUpdatedEvent"]];
  "row-expanding": [key: K];
  "cellDblClick": [e: ImmyDataGridTypes["CellDblClickEvent"]];
  "exporting": [e: ImmyDataGridTypes["ExportingEvent"]];
  "initialized": [e: ImmyDataGridTypes["ContentReadyEvent"]];
  "onSelectionChanged": [selectedItems: T[]];
  "filterCleared": [];
  "search-text-changed": [e: string];
}>();

defineSlots<
  {
    [x: string]: (props: {
      data: T;
      cellInfo: ColumnCellTemplateData<T, K>;
      showDetails?: () => void;
      hideDetails?: () => void;
      showingDetails?: boolean;
    }) => any;
  } & {
    [x: `groupCell${string}`]: (props: { cellInfo: ColumnGroupCellTemplateData<T, K> }) => any;
  } & {
    [x: `headerCell${string}`]: (props: { cellInfo: ColumnHeaderCellTemplateData<T, K> }) => any;
  } & {
    "detail"?: (props: {
      cellInfo: MasterDetailTemplateData<T, K>;
      hideDetails: (cellInfo: MasterDetailTemplateData<T, K>) => void;
    }) => any;
    "toolbar-before"?: () => any;
  }
>();

const selectedRowKeys = defineModel("selectedRowKeys");

dayjs.extend(utc);

interface DxDataGridTyped extends DxDataGrid {
  readonly instance?: DataGrid<T, K>;
}

// @ts-expect-error todo: fix this
export type ImmyDataGridTypes = ExplicitTypes<T, K>;

// may be a better place to put this
function getInitialTimeFilter(id: string): TimeFilter {
  const val = localStorage.getItem(`${id}-time-filter`);
  if (val) {
    const option = EnumTextHelpers.TimeFilter.GetOptionsByValue(Number.parseInt(val));
    if (option)
      return option.value;
  }
  return TimeFilter.LastWeek;
}

const state = reactive({
  pageSizes: props.pageSizes,
  currentFilter: null,
  autoExpandAll: false,
  timeFilter: getInitialTimeFilter(props.id),
  expandedRows: new Set(),
  initialized: false,
  autoRefresh: props.initialRefreshValue,
});

// name must match the ref from the template
const dataGrid = ref<DxDataGridTyped | null>(null);

function toggleMasterDetailsForRow(rowKey: K) {
  const instance = dataGrid.value?.instance;
  if (instance == null)
    return;
  if (instance.isRowExpanded(rowKey))
    instance.collapseRow(rowKey);
  else instance.expandRow(rowKey);
}

function expandRow(rowKey: K) {
  const instance = dataGrid.value?.instance;
  if (instance == null)
    return;
  instance.expandRow(rowKey);
}

function clearFilters() {
  const instance = dataGrid.value?.instance;
  if (instance == null)
    return;
  instance.clearFilter();
}

function collapseRow(rowKey: K) {
  const instance = dataGrid.value?.instance;
  if (instance == null)
    return;
  instance.collapseRow(rowKey);
}

async function selectAllRows() {
  const instance = dataGrid.value?.instance;
  if (instance == null)
    return;
  await instance.selectAll();
}
// defineExpose makes stuff visible to parent component
defineExpose({
  dataGrid,
  refresh,
  toggleMasterDetailsForRow,
  expandRow,
  collapseRow,
  selectAllRows,
  clearFilters,
});

const showOverlay = computed<boolean>(() => !state.initialized || props.loading);
const timeFilterOptions = computed<IEnumExtensionOptions<TimeFilter>[]>(() => {
  return EnumTextHelpers.TimeFilter.options;
});
const cellTemplates = computed(() => {
  const reducer = (agg: string[], curr: ImmyDataGridTypes["Column"] | string) => {
    if (typeof curr === "string")
      return agg;
    if (typeof curr.cellTemplate === "string")
      agg.push(curr.cellTemplate);

    if (curr.columns)
      agg.push(...curr.columns.reduce(reducer, []));

    return agg;
  };
  const templates = props.columns.reduce(reducer, []);
  return templates;
});

const groupCellTemplates = computed(() => {
  const reducer = (agg: string[], curr: ImmyDataGridTypes["Column"] | string) => {
    if (typeof curr === "string")
      return agg;

    if (typeof curr.groupCellTemplate === "string" && !agg.includes(curr.groupCellTemplate))
      agg.push(curr.groupCellTemplate);

    if (curr.columns)
      agg.push(...curr.columns.reduce(reducer, []));

    return agg;
  };
  const templates = props.columns.reduce(reducer, []);
  return templates;
});

const headerCellTemplates = computed(() => {
  const reducer = (agg: string[], curr: ImmyDataGridTypes["Column"] | string) => {
    if (typeof curr === "string")
      return agg;
    if (typeof curr.headerCellTemplate === "string") {
      if (!agg.includes(curr.headerCellTemplate))
        agg.push(curr.headerCellTemplate);
    }

    return agg;
  };
  const templates = props.columns.reduce(reducer, []);
  // hack to get the header cell template to render correctly.
  // there's some reactivity issues we need to fix with loading the header cell templates.
  if (props.refreshOnHeaderCellTemplateChange)
    refresh();
  return templates;
});

function onOptionChanged(e: OptionChangedEvent) {
  if (e.fullName.includes("filterValue")) {
    emit("search-text-changed", e.value);
  }
}

function onRowUpdated(e: ImmyDataGridTypes["RowUpdatedEvent"]) {
  emit("row-updated", e);
}
function onRowExpanding({ key }: ImmyDataGridTypes["RowExpandingEvent"]) {
  emit("row-expanding", key);
}
function onCellDblClick(e: ImmyDataGridTypes["CellDblClickEvent"]) {
  emit("cellDblClick", e);
}
function onExporting(e: ImmyDataGridTypes["ExportingEvent"]) {
  emit("exporting", e);
}
function onContentReady(e: ImmyDataGridTypes["ContentReadyEvent"]) {
  state.initialized = true;
  emit("initialized", e);
}

function showDetails(data: any) {
  if (props.noDetails)
    return;
  data.component.expandRow(data.key);
  state.expandedRows = new Set([...state.expandedRows, data.key]);
}

function hideDetails(data: MasterDetailTemplateData) {
  dataGrid.value?.instance?.collapseRow(data.key);
  state.expandedRows.delete(data.key);
  state.expandedRows = new Set(state.expandedRows);
}

function loadState() {
  const storedItem = window.localStorage.getItem(props.id);
  return storedItem != null ? JSON.parse(storedItem) : null;
}
function saveState(state: unknown) {
  window.localStorage.setItem(props.id, JSON.stringify(state));
}
function onTimeFilterSelected(val: TimeFilter) {
  localStorage.setItem(`${props.id}-time-filter`, val.toString());
  const date = val === TimeFilter.All ? new Date(0) : new Date();
  date.setHours(date.getHours() - val);
  dataGrid.value?.instance?.filter([
    "createdDate",
    ">",
    dayjs.utc(date).format("MM/DD/YYYY HH:mm:ss"),
  ]);
}

function onToolbarPreparing(e: any) {
  // Not sure of type
  if (props.hideToolbar) {
    e.toolbarOptions.visible = false;
    return;
  }

  e.toolbarOptions.items.push({
    location: "before",
    template: "title-template",
  });
  if (props.showTimeFilter) {
    e.toolbarOptions.items.push({
      location: "after",
      template: "time-filter",
    });
  }
  if (props.showSearchPanel) {
    e.toolbarOptions.items.push({
      location: "after",
      template: "clearFilter",
    });
  }
  if (props.showRefresh) {
    e.toolbarOptions.items.push({
      location: "after",
      template: "refresh",
    });
  }
  if (props.showRefresh && props.showAutoRefresh) {
    e.toolbarOptions.items.push({
      location: "after",
      template: "auto-refresh",
    });
  }
}

function onSelectionChanged(e: DxDataGridTypes.SelectionChangedEvent) {
  emit("onSelectionChanged", e.selectedRowsData);
  if (props.noDetails)
    return;
  if (props.selection && props.selectionMode === "single") {
    e.component.collapseAll(-1);
    e.component.expandRow(e.currentSelectedRowKeys[0]);
  }
}
function clearFilter() {
  const instance = dataGrid.value?.instance;
  instance?.clearFilter();
  // imemediately apply time filter back when clearing filter
  if (props.showTimeFilter)
    onTimeFilterSelected(state.timeFilter);
  instance?.clearSelection();

  if (props.id && props.storeState)
    window.localStorage.removeItem(props.id);

  emit("filterCleared");
}
function clearSelection() {
  const dg = dataGrid.value?.instance;
  dg?.clearSelection();
}

function refresh(): void {
  if (!state.autoRefresh)
    clearSelection();
  dataGrid.value?.instance?.getDataSource()?.reload();
}

const pageVisibilityStore = usePageVisibilityStore();
const { startPolling: startAutoRefresh, stopPolling: stopAutoRefresh } = useMountedPoller(async () => {
  await pageVisibilityStore.waitForActiveUser();
  refresh();
}, { pollIntervalMilliseconds: 5000, immediate: false });

watch(
  () => state.autoRefresh,
  (val) => {
    if (val) {
      startAutoRefresh();
    }
    else
      stopAutoRefresh();
  },
  { immediate: true },
);

watch(
  () => state.timeFilter,
  val => onTimeFilterSelected(val),
);
</script>

<style lang="scss" scoped>
.immy-dx-data-grid {
  overflow-x: auto;
  overflow-y: hidden;
}

.dx-datagrid-content .dx-datagrid-table .dx-row > td {
  vertical-align: middle;
}

.dx-datagrid-content .dx-datagrid-table .dx-row > td.dx-datagrid-group-space {
  vertical-align: middle !important;
}

.dx-datagrid-search-panel {
  margin: 0 !important;
}

.fa-sync {
  &:hover {
    cursor: pointer;
  }
}
.dx-datagrid .dx-row-alt.bg-warning > td,
.dx-datagrid .dx-row-alt.bg-warning > tr > td {
  background-color: unset;
}

.dx-datagrid-group-closed,
.dx-datagrid-group-opened {
  height: 21px !important;
}
.dx-datagrid-content .dx-datagrid-table .dx-row > td.dx-datagrid-group-space {
  padding: 0;
}

.time-filter {
  min-width: 150px;
}

.dx-item .dx-toolbar-item {
  white-space: nowrap;
}

.dx-show-clear-button .dx-icon-clear {
  color: var(--text-primary) !important;
  background: var(--bg-default2);
  background-color: var(--bg-default2) !important;
}

.dx-dropdowneditor-icon {
  color: var(--text-primary) !important;
  background: var(--bg-default2);
  background-color: var(--bg-default2) !important;
}

.dx-popup-wrapper > .dx-overlay-content {
  border: 1px solid var(--divider) !important;
  background: var(--bg-default2) !important;
  border-radius: 0 !important;
}

.dx-list:not(.dx-list-select-decorator-enabled) .dx-list-item.dx-list-item-selected {
  color: var(--text-primary) !important;
  background-color: var(--bg-default3) !important;
}

.dx-list:not(.dx-list-select-decorator-enabled) .dx-list-item.dx-state-hover {
  color: var(--text-primary) !important;
  background-color: var(--bg-default3) !important;
}

.dx-list:not(.dx-list-select-decorator-enabled) .dx-list-item.dx-state-active {
  color: var(--text-primary) !important;
  background-color: var(--bg-default3) !important;
}
</style>
