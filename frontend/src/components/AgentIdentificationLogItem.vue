<template>
  <div class="identification-log">
    <span class="m-0 text-muted">{{ timeFormatted }} - </span>
    <span :class="{ 'text-danger': isErrorLogType }" class="whitespace-prewrap"><span v-if="deviceName"><strong>{{ deviceName }}</strong> - </span>{{ props.log.message }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import {
  AgentIdentificationLogType,
  IAgentIdentificationLogResource,
  IGetProviderAgentIdentificationLogResponse,
} from "@/api/backend/generated/contracts";
import { formatDate } from "@/utils/misc";

interface IProps {
  log: IGetProviderAgentIdentificationLogResponse | IAgentIdentificationLogResource;
}

const props = defineProps<IProps>();

const timeFormatted = computed(() => {
  return formatDate(props.log.timeUtc, "mm/dd/yy h:MM:sstt Z");
});

const isErrorLogType = computed(() => {
  return props.log.logType === AgentIdentificationLogType.Error;
});

const deviceName = computed(() => {
  return "deviceName" in props.log ? props.log.deviceName : undefined;
});
</script>

<style lang="scss" scoped>
.identification-log {
  font-family: "Roboto Mono", monospace;
  font-size: 0.75rem;
}
</style>
