<template>
  <div>
    <div v-if="showSessionTypeSelector" class="d-flex align-items-center flex-wrap mb-3">
      <strong class="mr-3 line-height-1">Session Type:</strong>
      <div class="form-check form-check-inline">
        <input
          id="computerOption"
          v-model="selectedSessionType"
          class="form-check-input"
          type="radio"
          name="sessionTypeOption"
          :value="sessionTypeEnum.Computer"
        >
        <label class="form-check-label" for="computerOption">Computer</label>
      </div>
      <div class="form-check form-check-inline">
        <input
          id="cloudOption"
          v-model="selectedSessionType"
          class="form-check-input"
          type="radio"
          name="sessionTypeOption"
          :value="sessionTypeEnum.Cloud"
        >
        <label class="form-check-label" for="cloudOption">Cloud</label>
      </div>
      <div v-if="featuresStore.isEnabled(featureEnum.PersonTasksFeature)" class="form-check form-check-inline">
        <input
          id="personOption"
          v-model="selectedSessionType"
          class="form-check-input"
          type="radio"
          name="sessionTypeOption"
          :value="sessionTypeEnum.Person"
        >
        <label class="form-check-label" for="personOption">Person</label>
      </div>
    </div>
    <ImmyDxDataGrid
      :id="id"
      ref="dx-session-table"
      :data-source="dataSource"
      :columns="columns"
      :show-refresh="true"
      :show-time-filter="showTimeFilter"
      selection
      :remote-operations="remoteOperations"
      show-header-filter
      :store-state="false"
      :page-size="pageSize"
      @filter-cleared="onFilterCleared"
      @on-selection-changed="onSelectionChanged"
    >
      <template #triggeredBy="{ data }">
        <span v-if="data.createdBy != null && data.createdBy != ' '" class="pii">
          {{ data.createdBy }}
        </span>
        <span v-else-if="data.scheduleId != null">
          <template v-if="permissionStore.can('schedules:view')">
            <ImmyLink :to="{ name: 'Edit Schedule', params: { scheduleId: data.scheduleId } }">
              Schedule
            </ImmyLink>
          </template>
          <template v-else>Schedule</template>
        </span>
        <template v-else-if="data.onboardingStageStatus != null">
          <p class="m-0">
            System
          </p>
          <span class="text-muted fs-85">Automatic Onboarding</span>
        </template>
        <span v-else>Unknown</span>
      </template>
      <template #tenantName="{ data }">
        <ImmyLink
          class="pii"
          :to="{
            name: 'Tenant Details',
            params: { tenantId: data.tenantId, tabName: 'sessions', childId: data.id?.toString() },
          }"
        >
          {{ data.tenantName }}
        </ImmyLink>
      </template>
      <template #personName="{ data }">
        <ImmyLink
          class="pii"
          :to="{
            name: 'Edit Person',
            params: { personId: data.personId, tabName: 'sessions', childId: data.id?.toString() },
          }"
        >
          {{ data.personName }}
        </ImmyLink>
      </template>
      <template #status="{ data }">
        <div v-for="status in sessionStatusOptions" :key="status.value">
          <template v-if="data.sessionStatus === status.value">
            <strong :class="`badge badge-${status.alert}`">
              <ImmyLink
                v-if="status.value === 10 || status.value === 13"
                :to="{
                  name: 'Billing',
                  params: { tabName: 'features' },
                }"
              ><i :class="status.icon" class="mr-2" />{{ status.text }}</ImmyLink>
              <template v-else>
                <i :class="status.icon" class="mr-2" />{{ status.text }}
              </template>
            </strong>
          </template>
        </div>
        <div v-if="data.onboarding">
          <strong class="mt-1 text-primary">
            <i class="fa fa-laptop" />
            Onboarding
          </strong>
        </div>
      </template>
      <template #session="{ data }">
        <div>#{{ data.id }}</div>
        <div class="d-flex align-items-center flex-wrap justify-content-center">
          <ImmyBadge
            v-if="isInventoryOnly(data)"
            class="p-1 m-1"
            variant="secondary"
            title="This session only ran inventory. It passes if updates complete successfully"
          >
            inventory only
          </ImmyBadge>
          <ImmyBadge
            v-if="isAgentUpdatesOnly(data)"
            class="p-1 m-1"
            variant="secondary"
            title="This session only ran agent updates. It passes if updates complete successfully"
          >
            agent updates only
          </ImmyBadge>
          <ImmyBadge
            v-if="isDetectionOnly(data)"
            class="p-1 m-1"
            variant="secondary"
            title="This session only ran detection. It passes if detection completes successfully"
          >
            detection only
          </ImmyBadge>
          <ImmyBadge
            v-if="isOnboarding(data)"
            class="p-1 m-1"
            variant="primary"
            title="This session is for onboarding a computer"
          >
            onboarding
          </ImmyBadge>
          <ImmyBadge
            v-else-if="data.fullMaintenance"
            class="p-1 m-1"
            variant="primary"
            :title="fullMaintenanceDescription"
          >
            full maintenance
          </ImmyBadge>
        </div>
      </template>
      <template #computer="{ data }">
        <ImmyLink
          v-if="data.computerId != null"
          :to="{ name: 'Computer Details', params: { computerId: data.computerId } }"
        >
          {{ data.computerName }}
        </ImmyLink>
        <div v-if="data.primaryPersonEmail" class="computer-primary-person">
          <span class="d-block pii">
            {{ data.primaryPersonName }}
          </span>
          <span class="d-block pii">
            {{ data.primaryPersonEmail }}
          </span>
        </div>
      </template>
      <template #stages="{ data }">
        <MaintenanceSessionStageIndicator
          class="justify-content-center"
          :session="data"
          :show-stage-name="false"
        />
      </template>
      <template #time="{ data }">
        <div class="time-running">
          <span class="text-muted text-smaller">Time running:</span>
          <span class="ml-1">{{ getTimeRunningForSession(data) }}</span>
        </div>
        <div v-tooltip="getFormattedDate(data.createdDate)" class="time-created">
          <span class="text-muted text-smaller">Created:</span>
          <span class="ml-1" :title="getFormattedDate(data.createdDate)">{{
            getHumanReadableDate(data.createdDate)
          }}</span>
        </div>
        <div
          v-if="data.scheduledExecutionDate"
          v-tooltip="getFormattedDate(data.scheduledExecutionDate)"
          class="time-scheduled"
        >
          <span class="text-muted text-smaller">Scheduled Execution:</span>
          <span class="ml-1">{{ getHumanReadableDate(data.scheduledExecutionDate) }}</span>
        </div>
      </template>
      <template #actions="{ data }">
        <div class="table-actions">
          <CancelSessionButton :session="data" @cancel="onSessionCancelled(data)" />
          <RerunSessionButton
            :session="data"
            :custom-rerun="pageFilter === 'technician-pod'"
            @rerun="onRerun"
          />
          <ResumeSessionButton :session="data" />
          <ImmyButton
            v-if="pageFilter === 'technician-pod'"
            variant="secondary"
            size="sm"
            @click="$emit('update:viewSessionId', data.id)"
          >
            View
          </ImmyButton>
          <ImmyButton
            v-else
            variant="secondary"
            size="sm"
            :to="goToSessionDetailsPage(data.id, data.computerId, data.tenantId, data.personId)"
          >
            View
          </ImmyButton>
        </div>
      </template>
    </ImmyDxDataGrid>
  </div>
</template>

<script>
import { mapStores } from "pinia";
import { ref } from "vue";
import { useRouter } from "vue-router";

import { FeatureEnum, SessionStatus, SessionType } from "@/api/backend/generated/contracts";
import { getSessionDataSource } from "@/composables/DxServerDataSource";
import SessionStatusExtended from "@/helpers/enums/SessionStatusExtended";
import { useAuthStore } from "@/store/pinia/auth-store";
import { useFeaturesStore } from "@/store/pinia/features-store";
import { usePermissionStore } from "@/store/pinia/permission-store";
import { useSidebarSessionActionsStore } from "@/store/pinia/sidebar-session-actions-store";
import {
  formatDate,
  fullMaintenanceDescription,
  getHumanReadableDate,
  getTimeRunningForSession,
} from "@/utils/misc";

export default {
  name: "DxSessionTable",
  props: {
    id: { type: String, required: true },
    remote: {
      type: Boolean,
      required: false,
      default: false,
    },
    showTimeFilter: {
      type: Boolean,
      default: false,
      required: false,
    },
    statusFilter: {
      type: Array,
      required: false,
      default: () => [],
    },
    computerId: {
      type: Number,
      required: false,
      default: null,
    },
    tenantId: {
      type: Number,
      required: false,
      default: null,
    },
    personId: {
      type: Number,
      required: false,
      default: null,
    },
    pageSize: {
      type: Number,
      required: false,
      default: 10,
    },
    showSessionTypeSelector: {
      type: Boolean,
      required: false,
      default: false,
    },
    sessionType: {
      type: Number,
      required: false,
      default: SessionType.Computer,
    },
    pageFilter: {
      type: String,
      required: false,
      default: "computer-details",
    },
    viewSessionId: {
      type: Number,
      required: false,
      default: null,
    },
    // making this false will show all sessions regardless of session type and allow for sorting by session type after the data is loaded
    limitToSelectedSessionType: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  emits: ["session-cancelled", "filterCleared", "update:viewSessionId", "rerun"],
  setup(props) {
    const selectedSessionType = ref(props.sessionType);

    return {
      selectedSessionType,
      router: useRouter(),
      authStore: useAuthStore(),
      permissionStore: usePermissionStore(),
      featuresStore: useFeaturesStore(),
    };
  },
  data() {
    return {
      dataSource: [],
    };
  },
  computed: {
    ...mapStores(useSidebarSessionActionsStore),
    featureEnum() {
      return FeatureEnum;
    },
    sidebarActionsTaken() {
      return this.sidebarSessionActionsStore.actionsTaken;
    },
    sessionTypeEnum() {
      return SessionType;
    },
    fullMaintenanceDescription() {
      return fullMaintenanceDescription;
    },
    columns() {
      const columns = [
        {
          dataField: "createdBy",
          cellTemplate: "triggeredBy",
          caption: "Triggered By",
          alignment: "center",
          width: 100,
          allowHeaderFiltering: true,
          headerFilter: {
            dataSource: (options) => {
              const dataSource = options.dataSource;
              dataSource.postProcess = (results) => {
                results.push({
                  text: "Schedule",
                  value: ["scheduleId", "<>", null],
                });
                results.push({
                  text: "System Automatic Onboarding",
                  value: ["onboardingStageStatus", "<>", null],
                });
                return results;
              };
            },
          },
        },
        {
          cellTemplate: "status",
          dataField: "sessionStatus",
          caption: "Status",
          alignment: "center",
          headerFilter: {
            dataSource: SessionStatusExtended.options,
          },
        },
        {
          cellTemplate: "stages",
          caption: "Stages",
          alignment: "center",
          allowFiltering: false,
          allowHeaderFiltering: true,
          headerFilter: {
            dataSource: [
              {
                text: "Resolution",
                value: ["resolutionStageStatus", "<>", null],
              },
              {
                text: "Detection",
                value: ["detectionStageStatus", "<>", null],
              },
              {
                text: "Execution",
                value: ["executionStageStatus", "<>", null],
              },
              {
                text: "Onboarding",
                value: ["onboardingStageStatus", "<>", null],
              },
              {
                text: "Agent Updates",
                value: ["agentUpdatesStageStatus", "<>", null],
              },
            ],
          },
        },
        {
          cellTemplate: "time",
          caption: "Time",
          alignment: "center",
        },
      ];

      columns.unshift(0, 0, {
        dataField: "personName",
        cellTemplate: "personName",
        caption: "Person",
        alignment: "center",
        width: 100,
        visible: this.selectedSessionType === SessionType.Person,
        calculateFilterExpression(filterValue, selectedFilterOperation) {
          if (!filterValue || filterValue.length === 1)
            return;
          // search computer name, primary person email, and primary person name
          const filterOperation = selectedFilterOperation || "contains";
          const logicalOperator = filterOperation === "notcontains" ? "and" : "or";
          return [
            ["personName", filterOperation, filterValue],
            logicalOperator,
            ["personEmail", filterOperation, filterValue],
          ];
        },
      });

      if (this.computerId == null && this.selectedSessionType === SessionType.Computer) {
        columns.unshift({
          dataField: "serialNumber",
          alignment: "center",
          caption: "Serial Number",
          visible: false,
        });
        columns.unshift({
          dataField: "operatingSystem",
          alignment: "center",
          caption: "OS",
          visible: false,
        });
        columns.unshift({
          dataField: "manufacturer",
          alignment: "center",
          caption: "Manufacturer",
          visible: false,
        });
        columns.unshift({
          dataField: "model",
          alignment: "center",
          caption: "Model",
          visible: false,
        });
        columns.unshift({
          dataField: "domain",
          alignment: "center",
          caption: "Domain",
          visible: false,
        });
        columns.unshift({
          dataField: "computerName",
          cellTemplate: "computer",
          alignment: "center",
          caption: "Computer",
          calculateFilterExpression(filterValue, selectedFilterOperation) {
            if (!filterValue || filterValue.length === 1)
              return;
            // search computer name, primary person email, and primary person name
            const filterOperation = selectedFilterOperation || "contains";
            const logicalOperator = filterOperation === "notcontains" ? "and" : "or";
            return [
              ["computerName", filterOperation, filterValue],
              logicalOperator,
              ["primaryPersonName", filterOperation, filterValue],
              logicalOperator,
              ["primaryPersonEmail", filterOperation, filterValue],
            ];
          },
        });
      }

      if (this.permissionStore.canSeeMultipleTenants("maintenance_sessions:view")) {
        columns.unshift(0, 0, {
          dataField: "tenantName",
          cellTemplate: "tenantName",
          caption: "Company",
          alignment: "center",
          width: 100,
        });
      }

      columns.unshift({
        dataField: "id",
        cellTemplate: "session",
        caption: "Session",
        sortOrder: "desc",
        alignment: "center",
        width: "auto",
      });

      columns.unshift({
        cellTemplate: "actions",
        caption: "Actions",
        alignment: "center",
        minWidth: 120,
        width: "auto",
      });

      return columns;
    },
    remoteOperations() {
      if (this.remote) {
        return {
          filtering: true,
          sorting: true,
          grouping: false,
          paging: true,
        };
      }
      else { return false; }
    },
    sessionStatusOptions() {
      return SessionStatusExtended.options;
    },
  },
  watch: {
    async sidebarActionsTaken() {
      // when an action is taken then reload the computer table
      this.refreshData();
    },
    async selectedSessionType(newVal, oldVal) {
      if (newVal != oldVal && this.remote) {
        await this.getSessionsBySessionType(newVal);
      }
    },
    statusFilter: {
      handler(newVal) {
        // if (newVal === oldVal) return;
        // apply status column filterc
        this.$nextTick(() => {
          let val = newVal;
          if (!newVal || newVal.length === 0)
            val = undefined;
          const grid = this.$refs["dx-session-table"];
          if (grid) {
            grid.dataGrid.instance.columnOption("sessionStatus", "filterValues", val);
            grid.dataGrid.instance.refresh();
          }
        });
      },
      immediate: true,
    },
  },

  async mounted() {
    // if user is msp user then add a company column to the table.
    if (this.remote) {
      await this.getSessionsBySessionType(this.sessionType);
    }
  },
  methods: {
    goToSessionDetailsPage(sessionId, computerId, tenantId, personId) {
      if (!this.authStore.hasManagementAccess) {
        return {
          name: "Session Details",
          params: {
            sessionId: sessionId.toString(),
          },
        };
      }

      if (personId) {
        return {
          name: "Edit Person",
          params: {
            personId,
            tabName: "sessions",
            childId: sessionId.toString(),
          },
        };
      }

      if (computerId) {
        return {
          name: "Computer Details",
          params: {
            computerId,
            tabName: "sessions",
            childId: sessionId.toString(),
          },
        };
      }

      return {
        name: "Tenant Details",
        params: {
          tenantId,
          tabName: "sessions",
          childId: sessionId.toString(),
        },
      };
    },
    async getSessionsBySessionType(sessionType) {
      if (!this.limitToSelectedSessionType) {
        this.dataSource = await getSessionDataSource({
          id: this.id,
          useTimeFilter: this.showTimeFilter,
          computerId: this.computerId,
          tenantId: this.tenantId,
          personId: this.personId,
          sessionType: this.selectedSessionType,
        });
        return;
      }

      // otherwise pull in only relevant sessions to the selected session type
      switch (sessionType) {
        case SessionType.Computer:
          this.dataSource = await getSessionDataSource({
            id: this.id,
            useTimeFilter: this.showTimeFilter,
            computerId: this.computerId,
            sessionType: SessionType.Computer,
          });
          break;
        case SessionType.Cloud:
          this.dataSource = await getSessionDataSource({
            id: this.id,
            tenantId: this.tenantId,
            useTimeFilter: this.showTimeFilter,
            sessionType: SessionType.Cloud,
          });
          break;
        case SessionType.Person:
          this.dataSource = await getSessionDataSource({
            id: this.id,
            useTimeFilter: this.showTimeFilter,
            personId: this.personId,
            sessionType: SessionType.Person,
          });
          break;
      }
    },

    onSelectionChanged(selection) {
      this.sidebarSessionActionsStore.setSelectedSessions(selection);
    },
    onSessionCancelled(session) {
      session.sessionStatus = SessionStatus.Cancelled;
      this.dataSource?.store?.push([{ type: "update", data: session, key: session.id }]);
      this.$emit("session-cancelled", session);
    },
    /** @public */
    refreshData() {
      const grid = this.$refs["dx-session-table"];
      grid.refresh();
    },
    isAgentUpdatesOnly(session) {
      return (
        session.detectionStageStatus == null
        && session.onboardingStageStatus == null
        && session.executionStageStatus == null
        && session.agentUpdatesStageStatus != null
      );
    },
    isInventoryOnly(session) {
      return session.inventoryStageStatus != null;
    },
    isDetectionOnly(session) {
      return (
        session.inventoryStageStatus == null
        && session.executionStageStatus == null
        && session.agentUpdatesStageStatus == null
      );
    },
    isOnboarding(session) {
      return session.onboardingStageStatus != null;
    },
    onFilterCleared() {
      this.$emit("filterCleared");
    },
    onRerun(session) {
      this.$emit("rerun", session);
    },
    getTimeRunningForSession(session) {
      return getTimeRunningForSession(
        session.createdDate,
        session.updatedDate,
        session.sessionStatus,
        session.duration,
      );
    },
    getHumanReadableDate(date) {
      return getHumanReadableDate(date);
    },
    getFormattedDate(date) {
      return formatDate(date);
    },
  },
};
</script>

<style lang="scss" scoped>
.table > tbody > tr > td {
  vertical-align: middle;
}

.list-group-horizontal {
  flex-direction: row;
}

.list-group-item {
  border: none;
}

:deep(.dx-template-wrapper) {
  white-space: break-spaces;
}

:deep(.dx-datagrid-rowsview .dx-row > td) {
  white-space: break-spaces;
}
</style>
