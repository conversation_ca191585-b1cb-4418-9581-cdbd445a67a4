namespace Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
#nullable enable

public class TelemetryOptions
{
  /// <summary> The key used for this option in appsettings.json. </summary>
  public const string SectionKey = "TelemetryOptions";

  public string? OtelCollectorLogEndpoint { get; set; }
  public string? OtelCollectorLogHeaders { get; set; }
  public string? OtelCollectorTraceEndpoint { get; set; }
  public string? OtelCollectorTraceHeaders { get; set; }
  public string? OtelCollectorMetricEndpoint { get; set; }
  public string? OtelCollectorMetricHeaders { get; set; }
  public bool OtelCollectorUseHttp { get; set; }
  public int OtelCollectorTimeoutMilliseconds { get; set; }

  /// <summary>
  /// Rules applied by the Sampler during Activity creation (Head Sampling).
  /// Rules are evaluated in order; the first match determines the probability that the trace is exported.
  /// These are preferred if the initial Activity has sufficient information to make a decision, because they will prevent the Activity from incurring any subsequent overhead.
  /// If no rules match, the trace is sampled.
  /// </summary>
  public SamplingRule[] HeadSamplingRules { get; set; } = [];

  /// <summary>
  /// Rules applied by the Exporter just before exporting (Tail Sampling).
  /// Allows filtering based on the final state of the Activity (e.g., status code).
  /// Rules are evaluated in order; the first match determines the probability that the trace is exported.
  /// If no rules match, the trace is sampled.
  /// </summary>
  public SamplingRule[] TailSamplingRules { get; set; } = [];

  public bool EnableAzureMonitorExporters { get; set; } = true;
}

/// <summary>
/// Represents a rule for sampling telemetry data based on matching criteria.
/// The rules are evaluated in order, and the first rules that matches all criteria determines the sampling ratio.
/// </summary>
public class SamplingRule
{
  /// <summary> A descriptive name shown within rule evaluation logs/traces. </summary>
  public string? RuleName { get; set; }

  /// <summary>
  /// If true, this rule will only be evaluated against root traces, and all descendant spans will be sampled in the same way as the root trace.
  /// If false, the rule will be evaluated against all spans whose parents haven't been filtered out.
  /// </summary>
  public bool DelegateSamplingDecisionToRootTrace { get; set; } = true;

  /// <summary> Matched against the DisplayName of an Activity. </summary>
  public string? MatchingName { get; set; }

  /// <summary> Matched against the tags of an Activity. All attributes must match for the rule to apply. </summary>
  public Dictionary<string, string>? MatchingAttributes { get; set; }

  /// <summary> The sampling ratio (0.0 to 1.0) to apply if the attributes match. </summary>
  public double SamplingRatio { get; set; } = 1.0;
}
