using System.Diagnostics;
using System.Globalization;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using OpenTelemetry.Trace;

#nullable enable

// ReSharper disable ForeachCanBePartlyConvertedToQueryUsingAnotherGetEnumerator
#pragma warning disable S3267 // Loops should be simplified using the "Where" LINQ method // avoid closure and enumerator allocations

namespace Immybot.Backend.Infrastructure.Telemetry;

public static class TelemetrySampling
{
  public static SamplingResult ShouldSample(in SamplingParameters samplingParameters, SamplingRule[] rules)
  {
    static SamplingResult? TryGetSamplingResultFromParent(ActivityContext parentContext, bool delegateDecisionToParent)
    {
      if (parentContext.TraceId == default)
        return null; // no parent

      if (!parentContext.TraceFlags.HasFlag(ActivityTraceFlags.Recorded))
        return new SamplingResult(SamplingDecision.Drop); // always drop if parent dropped

      if (!delegateDecisionToParent)
        return null; // let the sampling rules run against the span

      return new SamplingResult(SamplingDecision.RecordAndSample,
      [
        new("telemetry.sampling.delegated", "true")
      ]);
    }

    var delegateDecisionToParent = rules.All(r => r.DelegateSamplingDecisionToRootTrace);
    var resultFromParent = TryGetSamplingResultFromParent(samplingParameters.ParentContext, delegateDecisionToParent);
    if (resultFromParent != null)
      return resultFromParent.Value;

    const double defaultSamplingRatio = 1.0; // sample everything not matched by any rules
    var ratio = GetSamplingRatio(samplingParameters, rules, defaultSamplingRatio, out var matchedRule);
    var shouldSample = ratio switch
    {
      <= 0.0 => false,
      >= 1.0 => true,
      _ => ShouldTraceBeSampled(samplingParameters.TraceId, ratio),
    };
    var decision = shouldSample ? SamplingDecision.RecordAndSample : SamplingDecision.Drop;

    return decision is SamplingDecision.Drop
      ? new SamplingResult(SamplingDecision.Drop)
      : new SamplingResult(decision,
      [
        new("telemetry.sampling.matched_rule", matchedRule?.RuleName ?? "<NONE>"),
        new("telemetry.sampling.ratio", ratio),
      ]);
  }

  public static double GetSamplingRatio(
    in SamplingParameters samplingParameters,
    SamplingRule[] rules,
    double defaultRatio,
    out SamplingRule? matchedRule)
  {
    matchedRule = null;
    if (rules.Length == 0)
      return defaultRatio;

    var tagsCache = default(List<KeyValuePair<string, object?>>?); // only enumerate once
    var stringifiedTagsCache = default(Dictionary<string, string?>?); // only call ToString strictly when needed

    foreach (var rule in rules)
      if (DoesRuleMatch(in samplingParameters, rule, ref tagsCache, ref stringifiedTagsCache))
      {
        matchedRule = rule;
        return rule.SamplingRatio;
      }

    return defaultRatio;

    static bool DoesRuleMatch(
      in SamplingParameters samplingParameters,
      SamplingRule rule,
      ref List<KeyValuePair<string, object?>>? tagsCache,
      ref Dictionary<string, string?>? stringifiedTagsCache)
    {
      if (!string.IsNullOrEmpty(rule.MatchingName) &&
          !string.Equals(rule.MatchingName, samplingParameters.Name, StringComparison.Ordinal))
      {
        return false;
      }

      if (rule.MatchingAttributes is { Count: > 0 })
      {
        if (tagsCache == null)
        {
          if (samplingParameters.Tags == null)
            return false;

          tagsCache = samplingParameters.Tags.ToList();

          // reverse so we're only matching against the latest value of a tag that appears multiple times
          // matches the OpenTelemetry spec attribute guidelines here: https://github.com/open-telemetry/opentelemetry-specification/blob/2b9ef9613f3bc4b135f0c1393be641f3a6245643/specification/common/README.md#attribute-collections
          tagsCache.Reverse();
        }

        if (tagsCache.Count == 0)
          return false;

        stringifiedTagsCache ??= new Dictionary<string, string?>(rule.MatchingAttributes.Count);

        foreach (var attribute in rule.MatchingAttributes)
        {
          var tagValue = GetStringifiedTagValue(attribute.Key, tagsCache, ref stringifiedTagsCache);
          if (!string.Equals(attribute.Value, tagValue, StringComparison.Ordinal))
            return false;
        }
      }

      // NOTE: rules with nothing to match are considered to always match (useful for a catch-all rule).
      return true;
    }

    static string? GetStringifiedTagValue(
      string attributeName,
      List<KeyValuePair<string, object?>> tagsCache,
      ref Dictionary<string, string?> stringifiedTagsCache)
    {
      if (stringifiedTagsCache.TryGetValue(attributeName, out var value))
        return value;

      foreach (var tag in tagsCache)
      {
        if (tag.Key.Equals(attributeName, StringComparison.Ordinal))
        {
          try
          {
            // TODO switch on tag type instead (kind of like ProtobufOtlpTagWriter)?
            value = Convert.ToString(tag.Value, CultureInfo.InvariantCulture);
          }
          catch (Exception)
          {
            // ignore failed ToString (same as ProtobufOtlpTagWriter)
          }

          break;
        }
      }

      stringifiedTagsCache[attributeName] = value;
      return value;
    }
  }

  public static bool ShouldTraceBeSampled(in ActivityTraceId traceId, double ratio)
  {
    // implementation copied from TraceIdRatioBasedSampler

    var idUpperBound = (long)(ratio * long.MaxValue);

    Span<byte> traceIdBytes = stackalloc byte[16];
    traceId.CopyTo(traceIdBytes);
    return Math.Abs(GetLowerLong(traceIdBytes)) < idUpperBound;

    static long GetLowerLong(ReadOnlySpan<byte> bytes)
    {
      long result = 0;
      for (var i = 0; i < 8; i++)
      {
        result <<= 8;
#pragma warning disable CS0675 // Bitwise-or operator used on a sign-extended operand
        result |= bytes[i] & 0xff;
#pragma warning restore CS0675 // Bitwise-or operator used on a sign-extended operand
      }

      return result;
    }
  }
}
