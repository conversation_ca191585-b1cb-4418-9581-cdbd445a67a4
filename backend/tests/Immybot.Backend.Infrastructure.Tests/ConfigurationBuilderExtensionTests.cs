using Microsoft.Extensions.Hosting;

namespace Immybot.Backend.Infrastructure.Tests;

public class ConfigurationBuilderExtensionTests
{
  [Fact]
  public void AddJsonFilesConfiguration_ShouldPickUpAppSettingsFile()
  {
    // Arrange
    var builder = new HostApplicationBuilder();

    // act
    builder.AddJsonFilesConfiguration();

    var config = builder.Build();

    // Assert
    Assert.NotNull(config);
  }
}
