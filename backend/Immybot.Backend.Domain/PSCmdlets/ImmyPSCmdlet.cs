using System;
using System.Diagnostics.CodeAnalysis;
using System.Management.Automation;
using System.Management.Automation.Language;
using System.Reflection;
using OneOf;

namespace Immybot.Backend.Domain.PSCmdlets;

[SuppressMessage("Major Code Smell",
  "S3011:Reflection should not be used to increase accessibility of classes, methods, or fields")]
public abstract class ImmyPSCmdlet : PSCmdlet
{
  private static Type EmptyScriptExtentType = typeof(IScriptExtent).Assembly.GetType("System.Management.Automation.Language.EmptyScriptExtent")!;
  private static MethodInfo SetInvocationInfo = typeof(ErrorRecord).GetMethod("SetInvocationInfo", BindingFlags.Instance | BindingFlags.NonPublic)!;
  private static FieldInfo ScriptPosition = typeof(InvocationInfo).GetField("_scriptPosition", BindingFlags.Instance | BindingFlags.NonPublic)!;

  /// <summary>
  /// Throws a terminating error with the given error record. This is the only way to throw a terminating error in a cmdlet regardless of the error-action preference.
  /// </summary>
  /// <param name="error">Either an ErrorRecord or Exception. If supplied with an ErrorRecord,
  /// the resulting error will contain line & col information as to where it threw. Supplied with an exception,
  /// the error presented will simply contain the error message & type, with an OperationStopped ErrorCategory</param>
  /// <param name="fixInvocationInfo">If true, will attempt to fix the invocation info of the error record to point to the correct location of the error.
  /// This is most of the time good, however if the ErrorRecord supplied already has the correct InvocationInfo, set this to false.
  /// This only has an effect if supplied with an ErrorRecord, and not an Exception.</param>
  public void ThrowImmyTerminatingError(OneOf<ErrorRecord, Exception> error, bool fixInvocationInfo = true)
  {
    if (error.IsT0 && fixInvocationInfo)
    {
      var stackFrames = InvokeCommand.InvokeScript("Get-PSCallStack");
      // retrieve the second to last frame, which should be the correct location of the error, and set it as the invocation info of the error record.
      // This will fix the "At line:1 char:1" error message & related positional data to show the correct line & col number for the origin script rather
      // than the below script executing inside the cmdlet.
      if (stackFrames.Count > 1 && stackFrames[^2].BaseObject is CallStackFrame frame)
      {
        SetInvocationInfo.Invoke(error.Value, [frame.InvocationInfo]);
      }
    }
    try
    {
      InvokeCommand.InvokeScript($"throw $args[0]; # If you can see this, report it to the ImmyBot Dev Team.", error.Value);
    }
    // If this is an Exception rather than an ErrorRecord, we want to modify the exception's invocation info to point to an empty script extent
    // so that the error message doesn't show the line & col number & related script snippet.
    catch (RuntimeException ex) when (error.IsT1)
    {
      var emptyScriptExtent = Activator.CreateInstance(EmptyScriptExtentType);
      ScriptPosition.SetValue(ex.ErrorRecord.InvocationInfo, emptyScriptExtent);
      throw;
    }
  }
}
