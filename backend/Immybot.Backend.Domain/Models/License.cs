using System.Collections.Generic;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Shared.Primitives.Attributes;
using NuGet.Versioning;

namespace Immybot.Backend.Domain.Models;

/// <summary>
/// Domain model for software licenses.
/// A software / version has many licenses.
/// A license belongs to one software / version.
/// A tenant has many licenses.
/// A license belongs to zero or one tenant.
/// </summary>
public class License : AuditableUserEntity, IAuditableLoggableEntity
{
  public License()
  {
    TargetAssignments = new HashSet<TargetAssignment>();
  }

  public int Id { get; set; }

  // "Name" for easier distinction between similar licenses
  [AuditObjectName]
  public required string Name { get; set; }

  // blob reference to the resource if LicenseType.File or just text otherwise
  public string LicenseValue { get; set; } = string.Empty;

  // Allow for licenses to global/local/ninite/choco/etc software providers
  public SoftwareType SoftwareType { get; set; }
  public required string SoftwareIdentifier { get; set; }

  // keep the name of the software on the license for easy lookup
  public required string SoftwareName { get; set; }

  // optionally allow a license to a specific version?
  public SemanticVersion? SemanticVersion { get; set; }

  // optionally allow a license for a specific major version
  public bool RestrictToMajorVersion { get; set; }

  // optionally specify a tenant it applies to
  // if null then it applies to all tenants of the software's scope
  public int? TenantId { get; set; }

  public Tenant? Tenant { get; set; }

  public ICollection<TargetAssignment> TargetAssignments { get; set; }

  public string? GetFileName(LicenseType licenseType)
  {
    if (licenseType != LicenseType.LicenseFile) return null;
    return System.IO.Path.GetFileName(LicenseValue);
  }

  public User? CreatedByUser { get; set; }
  public User? UpdatedByUser { get; set; }
}
