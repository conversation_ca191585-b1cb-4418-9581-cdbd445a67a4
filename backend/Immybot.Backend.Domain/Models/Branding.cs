using System;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Shared.Primitives.Attributes;

namespace Immybot.Backend.Domain.Models;

public class Branding : AuditableUserEntity, IAuditableLoggableEntity
{
  public int Id { get; set; }
  public int? TenantId { get; set; }
  public string FromAddress { get; set; } = null!;
  public DateTime? StartDate { get; set; }
  public DateTime? EndDate { get; set; }
  public bool? IgnoreYear { get; set; }
  public string? TimeFormat { get; set; }
  public string? MascotImgUri { get; set; }
  public string? MascotName { get; set; }
  public string? LogoUri { get; set; }
  public string? LogoAltText { get; set; }
  public string BackgroundColor { get; set; } = null!;
  public string ForegroundColor { get; set; } = null!;
  public string TableHeaderColor { get; set; } = null!;
  public string TableHeaderTextColor { get; set; } = null!;
  public string TextColor { get; set; } = null!;

  [AuditObjectName]
  public string Description { get; set; } = null!;

  public Tenant? Tenant { get; set; }
  public User? CreatedByUser { get; set; }
  public User? UpdatedByUser { get; set; }
}
