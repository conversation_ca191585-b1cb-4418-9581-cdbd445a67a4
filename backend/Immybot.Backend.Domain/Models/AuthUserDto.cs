using System.Linq.Expressions;

namespace Immybot.Backend.Domain.Models;

public class AuthUserDto
{
  public required int Id { get; set; }
  public required int TenantId { get; set; }
  public required bool IsMsp { get; set; }
  public required bool IsAdmin { get; set; }
  public required int? PersonId { get; set; }
  public required bool IsSupportTechnician { get; set; }
  public required bool HasManagementAccess { get; set; }
  public required bool CanManageCrossTenantDeployments { get; set; }
  public required string? ServicePrincipalId { get; set; }
  public required string? FirstName { get; set; }
  public required string? LastName { get; set; }
  public required string? TenantName { get; set; }
  public required string? DisplayName { get; set; }
  public required string? PrincipalId { get; set; }
  public required string? Email { get; set; }
  public required DateTime? ExpirationDateUtc { get; set; }
  public AuthUserDto? ImpersonatingUser { get; set; }
  public AuthUserDto? ImpersonatorUser { get; set; }

  public required List<AuthUserRoleDto> Roles { get; set; }

  public bool IsExpired()
  {
    if (ExpirationDateUtc == null) return false;
    return DateTime.UtcNow > ExpirationDateUtc;
  }

  public class AuthUserRoleDto
  {
    public required string Name { get; set; }
    public required List<AuthUserClaimDto> Claims { get; set; }
  }

  public class AuthUserClaimDto
  {
    public required string Type { get; set; }
    public required string Value { get; set; }
  }

  public static Expression<Func<User, AuthUserDto>> Projection => a => new AuthUserDto
  {
    Id = a.Id,
    TenantId = a.Person != null ? a.Person.TenantId : a.TenantId,
    IsMsp = a.Tenant != null && a.Tenant.IsMsp,
    // todo: remove when frontend finishes swapping to role claims
    IsAdmin = a.IsAdmin,
    IsSupportTechnician = a.IsSupportTechnician,
    ServicePrincipalId = a.ServicePrincipalId,
    FirstName = a.Person != null
      ? a.Person.FirstName
      : null,
    LastName = a.Person != null
      ? a.Person.LastName
      : null,
    DisplayName =
      a.ServicePrincipalId ??
      (a.Person != null
        ? string.Concat(a.Person.FirstName,
          " ",
          a.Person.LastName)
        : string.Empty),
    PrincipalId = a.Person != null
      ? a.Person.AzurePrincipalId
      : null,
    Email = a.Person != null
      ? a.Person.EmailAddress
      : null,
    ExpirationDateUtc = a.ExpirationDateUTC,
    Roles = a.UserRoles.Where(role => role.Role!.Name != null)
      .Select(role => new AuthUserRoleDto
      {
        Name = role.Role!.Name!,
        Claims = role.Role.RoleClaims.Where(claim => claim.ClaimType != null)
          .Select(claim =>
            new AuthUserClaimDto { Type = claim.ClaimType!, Value = claim.ClaimValue ?? string.Empty })
          .ToList()
      })
      .ToList(),
    PersonId = a.PersonId,
    HasManagementAccess = a.HasManagementAccess,
    CanManageCrossTenantDeployments = a.CanManageCrossTenantDeployments,
    TenantName = a.Tenant != null ? a.Tenant.Name : string.Empty,
  };
}
