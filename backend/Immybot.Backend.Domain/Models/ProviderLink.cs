using System;
using System.Collections.Generic;
using System.Text.Json;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Shared.Primitives.Attributes;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Immybot.Backend.Domain.Models;

public class ProviderLink : AuditableUserEntity, IProviderLinkDetails
{
  public ProviderLink()
  {
    ProviderClients = new HashSet<ProviderClient>();
    Agents = new HashSet<ProviderAgent>();
    Schedules = new HashSet<Schedule>();
    TargetAssignments = new HashSet<TargetAssignment>();
    ProvidersLinkedFromThisProvider = new HashSet<ProviderLinkCrossReference>();
    LinkedFromProviders = new HashSet<ProviderLinkCrossReference>();
    ExcludedCapabilities = [];
  }
  public int Id { get; set; }
  public bool Disabled { get; set; }

  public bool DisabledOrUnhealthy => Disabled || HealthStatus is HealthStatus.Unhealthy;

  private HealthStatus? _healthStatus;

  public HealthStatus HealthStatus
  {
    get => _healthStatus ?? HealthStatus.Healthy;
    set => _healthStatus = value;
  }

  public string? HealthStatusMessage { get; set; }
  public int UnhealthyCount { get; set; }

  public int OwnerTenantId { get; set; }
  public required string Name { get; set; }

  /// <summary>
  /// Priority increases ascending
  /// e.g. 0 is a lower priority than 1
  /// </summary>
  public int RunScriptPriority { get; set; }

  [MaskAuditLog]
  public JsonElement ProviderTypeFormData { get; set; }
  public Guid ProviderTypeId { get; set; }
  public Tenant? OwnerTenant { get; set; }
  public ICollection<ProviderClient> ProviderClients { get; set; }
  public ICollection<Schedule> Schedules { get; set; }
  public ICollection<TargetAssignment> TargetAssignments { get; set; }
  public ICollection<ProviderAgent> Agents { get; set; }
  public ICollection<ProviderLinkCrossReference> ProvidersLinkedFromThisProvider { get; set; }
  public ICollection<ProviderLinkCrossReference> LinkedFromProviders { get; set; }
  public User? CreatedByUser { get; set; }
  public User? UpdatedByUser { get; set; }

  public List<string>? ExcludedCapabilities { get; set; }

  public ProviderLinkInternalData? ProviderInternalData { get; set; }
}
