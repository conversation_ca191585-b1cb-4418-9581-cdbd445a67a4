using System;
using System.Collections.Generic;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Shared.Primitives.Attributes;

namespace Immybot.Backend.Domain.Models;

public class MaintenanceTask : AuditableUserEntity, IMaintenanceTaskBase, IAuditableLoggableEntity,
  ITenantRelationships<TenantMaintenanceTask>
{
  public MaintenanceTask()
  {
    Parameters = new HashSet<MaintenanceTaskParameter>();
    TenantRelationships = new HashSet<TenantMaintenanceTask>();
  }

  public int Id { get; set; }

  [AuditObjectName]
  public required string Name { get; set; }
  public bool OnboardingOnly { get; set; }

  /// <summary>
  /// Indicates that this task will be skipped when running an automatic onboarding session
  /// </summary>
  public bool IgnoreDuringAutomaticOnboarding { get; set; }

  public MaintenanceTaskCategory MaintenanceTaskCategory { get; set; }

  private DatabaseType? _databaseType;

  public DatabaseType DatabaseType
  {
    get => _databaseType ?? DatabaseType.Global;
    set => _databaseType = value;
  }

  /// <summary>
  /// Indicates that this maintenance task can only execute one at a time
  /// </summary>
  public bool ExecuteSerially { get; set; }

  // test script
  public bool TestEnabled { get; set; } = false;
  public int? TestScriptId { get; set; }
  public DatabaseType? TestScriptType { get; set; }

  // get script
  public bool GetEnabled { get; set; } = false;
  public int? GetScriptId { get; set; }
  public DatabaseType? GetScriptType { get; set; }

  // set script
  public bool SetEnabled { get; set; } = false;
  public int? SetScriptId { get; set; }
  public DatabaseType? SetScriptType { get; set; }

  public bool Recommended { get; set; }

  public bool IsConfigurationTask { get; set; }

  public int? IconMediaId { get; set; }

  public string? Notes { get; set; }

  /// <summary>
  /// Indicates that this task pulls the parameter value definition from the script.
  /// This is only applicable for tasks that use a single script.
  /// </summary>
  public bool UseScriptParamBlock { get; set; }

  /// <summary>
  /// The task id that supersedes this task.
  /// </summary>
  public int? SupersededByTaskId { get; set; }

  /// <summary>
  /// The task type that supersedes this task.
  /// </summary>
  public DatabaseType? SupersededByTaskType { get; set; }

  /// <summary>
  /// The script id that will migrate the parameters of this task to the format of the superseding task.
  /// Must be a cloud script.
  /// </summary>
  public int? SupersededByTaskMigrationScriptId { get; set; }

  /// <summary>
  /// The type of script that will migrate parameters.
  /// </summary>
  public DatabaseType? SupersededByTaskMigrationScriptType { get; set; }

  /// <summary>
  /// Indicates whether this is an integration task.
  /// The value should be a known integration type id so we can link it to a specific integration.
  /// </summary>
  public Guid? IntegrationTypeId { get; set; }

  // associations
  public ICollection<MaintenanceTaskParameter> Parameters { get; set; }

  // local maintenance task only
  public ICollection<TenantMaintenanceTask> TenantRelationships { get; set; }

  // assignable fetched associations
  public Script? SetScript { get; set; }
  public Script? GetScript { get; set; }
  public Script? TestScript { get; set; }

  public Media? Icon { get; set; }

  // getter to return maintenance type used by maintenance logic
  public MaintenanceType MaintenanceType
  {
    get
    {
      switch (DatabaseType)
      {
        case DatabaseType.Global:
          return MaintenanceType.GlobalMaintenanceTask;
        case DatabaseType.Local:
          return MaintenanceType.LocalMaintenanceTask;
        default:
          throw new NotSupportedException();
      }
    }
  }

  public User? CreatedByUser { get; set; }
  public User? UpdatedByUser { get; set; }
}
