using System;
using System.Collections.Generic;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Shared.Primitives.Attributes;
using Microsoft.AspNetCore.Identity;

namespace Immybot.Backend.Domain.Models;

public class User : IdentityUser<int>, IAuditableUserEntity, IAuditableLoggableEntity, ICustomAuditProperties
{
  public User()
  {
    CreatedDeployments = new HashSet<TargetAssignment>();
    UpdatedDeployments = new HashSet<TargetAssignment>();
    CreatedScripts = new HashSet<Script>();
    UpdatedScripts = new HashSet<Script>();
    CreatedPersons = new HashSet<Person>();
    UpdatedPersons = new HashSet<Person>();
    CreatedSoftware = new HashSet<LocalSoftware>();
    UpdatedSoftware = new HashSet<LocalSoftware>();
    CreatedSoftwareVersions = new HashSet<LocalSoftwareVersion>();
    UpdatedSoftwareVersions = new HashSet<LocalSoftwareVersion>();
    CreatedLicenses = new HashSet<License>();
    UpdatedLicenses = new HashSet<License>();
    CreatedMaintenanceTasks = new HashSet<MaintenanceTask>();
    UpdatedMaintenanceTasks = new HashSet<MaintenanceTask>();
    CreatedSchedules = new HashSet<Schedule>();
    UpdatedSchedules = new HashSet<Schedule>();
    CreatedProviderLinks = new HashSet<ProviderLink>();
    UpdatedProviderLinks = new HashSet<ProviderLink>();
    CreatedBrandings = new HashSet<Branding>();
    UpdatedBrandings = new HashSet<Branding>();
    CreatedMedia = new HashSet<Media>();
    UpdatedMedia = new HashSet<Media>();
    CreatedRecommendedTargetAssignmentApprovals = new HashSet<RecommendedTargetAssignmentApproval>();
    UpdatedRecommendedTargetAssignmentApprovals = new HashSet<RecommendedTargetAssignmentApproval>();
    CreatedMaintenanceSessions = new HashSet<MaintenanceSession>();
    AccessRequestAcknowledgements = new HashSet<AccessRequest>();
    UpdatedTags = new HashSet<Tag>();
    CreatedTags = new HashSet<Tag>();
    UserRoles = new HashSet<UserRole>();
    SilencedNotifications = new HashSet<UserSilencedNotification>();
    CreatedChangeRequests = new HashSet<ChangeRequest>();
    UpdatedChangeRequests = new HashSet<ChangeRequest>();
    CreatedChangeRequestComments = new HashSet<ChangeRequestComment>();
    UpdatedChangeRequestComments = new HashSet<ChangeRequestComment>();
    UserImpersonations = new HashSet<UserImpersonation>();
    ImpersonatedByUsers = new HashSet<UserImpersonation>();
  }

  // RBAC
  // Note that the Id field is missing here since it is defined in the base class IdentityUser<int>.

  public DateTime? ExpirationDateUTC { get; set; }

  public bool IsAdmin { get; set; }

  /// <summary>
  /// Only used for MSP non-admin users. If true, the user can manage cross-tenant deployments.
  /// If the preference for non-admin users must submit change requests for cross-tenant deployments
  /// is true, then the user will be able to submit change requests for cross-tenant deployments.
  /// </summary>
  public bool CanManageCrossTenantDeployments { get; set; }

  public string? ServicePrincipalId { get; set; }
  public int? PersonId { get; set; }
  public int TenantId { get; set; }
  public Person? Person { get; set; }
  public Tenant? Tenant { get; set; }
  public bool IsSupportTechnician { get; set; }

  public string? DisplayName => ServicePrincipalId ?? Person?.DisplayName;

  //RBAC implements Email. Overriding it here for custom logic.
  [AuditObjectName]
  public override string? Email => Person?.EmailAddress;
  public string? AzurePrincipalId => ServicePrincipalId ?? Person?.AzurePrincipalId;

  public bool IsExpired()
  {
    if (ExpirationDateUTC == null) return false;
    return DateTime.UtcNow > ExpirationDateUTC;
  }

  /// <summary>
  /// When true, the user can use immybot to perform management operations such as deploying software for other computers.
  /// When false, the user is only allowed to use immybot for self-service of their own computers and to run cloud tasks.
  /// </summary>
  public bool HasManagementAccess { get; set; }

  // auditable relationships
  public ICollection<TargetAssignment> CreatedDeployments { get; set; }
  public ICollection<TargetAssignment> UpdatedDeployments { get; set; }
  public ICollection<Script> CreatedScripts { get; set; }
  public ICollection<Script> UpdatedScripts { get; set; }
  public ICollection<Person> CreatedPersons { get; set; }
  public ICollection<Person> UpdatedPersons { get; set; }
  public ICollection<LocalSoftware> CreatedSoftware { get; set; }
  public ICollection<LocalSoftware> UpdatedSoftware { get; set; }
  public ICollection<LocalSoftwareVersion> CreatedSoftwareVersions { get; set; }
  public ICollection<LocalSoftwareVersion> UpdatedSoftwareVersions { get; set; }
  public ICollection<License> CreatedLicenses { get; set; }
  public ICollection<License> UpdatedLicenses { get; set; }
  public ICollection<MaintenanceTask> CreatedMaintenanceTasks { get; set; }
  public ICollection<MaintenanceTask> UpdatedMaintenanceTasks { get; set; }
  public ICollection<Schedule> CreatedSchedules { get; set; }
  public ICollection<Schedule> UpdatedSchedules { get; set; }
  public ICollection<ProviderLink> CreatedProviderLinks { get; set; }
  public ICollection<ProviderLink> UpdatedProviderLinks { get; set; }
  public ICollection<Branding> CreatedBrandings { get; set; }
  public ICollection<Branding> UpdatedBrandings { get; set; }
  public ICollection<Media> CreatedMedia { get; set; }
  public ICollection<Media> UpdatedMedia { get; set; }
  public ICollection<Tag> CreatedTags { get; set; }
  public ICollection<Tag> UpdatedTags { get; set; }
  public ICollection<RecommendedTargetAssignmentApproval> CreatedRecommendedTargetAssignmentApprovals { get; set; }
  public ICollection<RecommendedTargetAssignmentApproval> UpdatedRecommendedTargetAssignmentApprovals { get; set; }
  public ICollection<MaintenanceSession> CreatedMaintenanceSessions { get; set; }
  public ICollection<AccessRequest> AccessRequestAcknowledgements { get; set; }
  public ICollection<UserRole> UserRoles { get; set; }
  public ICollection<UserSilencedNotification> SilencedNotifications { get; set; }
  public ICollection<ChangeRequest> CreatedChangeRequests { get; set; }
  public ICollection<ChangeRequest> UpdatedChangeRequests { get; set; }

  public ICollection<ChangeRequestComment> CreatedChangeRequestComments { get; set; }
  public ICollection<ChangeRequestComment> UpdatedChangeRequestComments { get; set; }

  public ICollection<UserImpersonation> UserImpersonations { get; set; }
  public ICollection<UserImpersonation> ImpersonatedByUsers { get; set; }

  /// <summary>
  /// This property should only be set by auth middleware to provide the impersonator user
  /// </summary>
  public User? ImpersonatorUser { get; set; }

  // RBAC
  // Have to manually implement these rather than inheriting AuditableUserEntity since Identity framework requires inheritance on the User class.
  public int? CreatedBy { get; set; }
  public int? UpdatedBy { get; set; }
  private DateTime _createdDate;
  private DateTime _updatedDate;
  public DateTime CreatedDate { get => new DateTime(_createdDate.Ticks, DateTimeKind.Utc); set => _createdDate = value; }
  public DateTime UpdatedDate { get => new DateTime(_updatedDate.Ticks, DateTimeKind.Utc); set => _updatedDate = value; }

  /// <summary>
  /// Used to audit changes to roles assigned to this user.
  /// </summary>
  public List<AuditPropertyChange> CustomAuditProperties { get; } = [];
}
