using System.Collections.Generic;
using System.Text.Json;
using Immybot.Backend.Domain.Providers;

namespace Immybot.Backend.Domain.Models;

public class ProviderClient : AuditableUserEntity, IProviderClientDetails
{
  public ProviderClient()
  {
    ProviderAgents = new HashSet<ProviderAgent>();
    Types = [];
  }
  public int ProviderLinkId { get; set; }
  public required string ExternalClientId { get; set; }
  public required string ExternalClientName { get; set; }
  public string? Status { get; set; }
  public JsonElement? InternalData { get; set; }

  public int? LinkedToTenantId { get; set; }
  public Tenant? LinkedToTenant { get; set; }
  public ProviderLink? ProviderLink { get; set; }

  public bool HasCompletedInitialAgentSync { get; set; }
  public List<string>? Types { get; set; }
  public ICollection<ProviderAgent> ProviderAgents { get; set; }
}
