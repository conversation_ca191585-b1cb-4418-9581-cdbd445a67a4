using System;
using System.Collections.Generic;

namespace Immybot.Backend.Domain.Models;

public class MaintenanceSession : AuditableUserEntity
{
  public MaintenanceSession()
  {
    Stages = new HashSet<MaintenanceSessionStage>();
    Logs = new HashSet<SessionLog>();
    MaintenanceActions = new HashSet<MaintenanceAction>();
    Phases = new HashSet<SessionPhase>();
    Activities = new HashSet<MaintenanceActionActivity>();

  }
  public int Id { get; set; }
  public string? JobId { get; set; }
  public int? ComputerId { get; set; }
  public int? TenantId { get; set; }
  public int? PersonId { get; set; }
  public int? ScheduledId { get; set; }
  public SessionStatus SessionStatus { get; set; } = SessionStatus.Created;
  public bool Onboarding { get; set; }
  public DateTime? ScheduledExecutionDate { get; set; }
  public TimeSpan? Duration { get; set; }
  public bool UsingActiveHours { get; set; }
  public bool FullMaintenance { get; set; }

  public SessionJobArgs JobArgs { get; set; } = new();

  public virtual ActiveSession? ActiveSession { get; set; }
  public virtual ICollection<MaintenanceAction> MaintenanceActions { get; set; }
  public virtual ICollection<MaintenanceSessionStage> Stages { get; set; }
  public virtual ICollection<SessionLog> Logs { get; set; }
  public virtual ICollection<SessionPhase> Phases { get; set; }
  public virtual ICollection<MaintenanceActionActivity> Activities { get; }

  public Computer? Computer { get; set; }

  public Person? Person { get; set; }
  public Tenant? Tenant { get; set; }
  public User? CreatedByUser { get; set; }
}

