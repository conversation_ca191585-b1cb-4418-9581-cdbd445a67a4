using System;
using System.Collections.Generic;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Shared.Primitives.Attributes;

namespace Immybot.Backend.Domain.Models;

public class Tenant
  : AuditableUserEntity
    , IAuditableLoggableEntity
{
  public int Id { get; set; }

  [AuditObjectName]
  public required string Name { get; set; }
  public string? Slug { get; set; }

  /// <summary>
  /// Marks this tenant as a child of another tenant for the purposes of propagating deployments to child organizations
  /// </summary>
  public int? ParentTenantId { get; set; }

  public int? OwnerTenantId { get; set; }
  public bool Active { get; set; }
  public bool IsMsp { get; set; }

  public AzureTenantLink? AzureTenantLink { get; set; }
  public Tenant? OwnerTenant { get; set; }
  public SmtpConfig? SmtpConfig { get; set; }
  public TenantPreferences? TenantPreferences { get; set; }

  public List<Tenant> OwnedTenants { get; } = [];
  public List<User> Users { get; } = [];
  public List<ProviderClient> ProviderClients { get; } = [];
  public List<ProviderLink> OwnedProviderLinks { get; } = [];
  public List<TenantSoftware> TenantSoftware { get; } = [];
  public List<DetectedComputerSoftware> DetectedComputerSoftware { get; } = [];
  public List<TenantScript> Scripts { get; } = [];
  public List<TenantMedia> Media { get; } = [];
  public List<TenantMaintenanceTask> MaintenanceTasks { get; } = [];
  public List<Schedule> Schedules { get; } = [];
  public List<Computer> Computers { get; } = [];
  public List<Person> Persons { get; } = [];
  public List<Branding> Brandings { get; } = [];
  public List<License> Licenses { get; } = [];
  public List<MaintenanceSession> MaintenanceSessions { get; } = [];
  public List<Tag> Tags { get; } = [];
  public List<TenantTagAuthorization> TenantTagAuthorizations { get; } = [];
  public List<TenantTag> TenantTags { get; } = [];

  public DateTime? MarkedForDeletionAtUtc { get; set; }

  public override string ToString()
  {
    return this.Name;
  }
}
