using Immybot.Backend.Domain.Interfaces;


namespace Immybot.Backend.Domain.Models.Preferences;

public class TenantPreferences : IAuditableLoggableEntity
{
  public int Id { get; set; }
  public int TenantId { get; set; }
  public bool EnableOnboarding { get; set; }
  public bool EnableSessionEmails { get; set; } = true;
  public bool EnableOnboardingPatching { get; set; }
  public Tenant? Tenant { get; set; }
  public DefaultEmailBccList? DefaultEmailBccList { get; set; }
  public bool OverwriteExistingDeviceIfOSIsNew { get; set; } = true;
  public bool? RequireConsentForExternalSessionProviders { get; set; }
  public string? BusinessHoursStart { get; set; }
  public string? BusinessHoursEnd { get; set; }
  public string? TimeZoneInfoId { get; set; }
  public bool ExcludeFromCrossTenantDeploymentsAndSchedules { get; set; }
  public bool EnableUserAffinitySync { get; set; } = true;

  /// <summary>
  /// Indicates whether we should allow ImmyBot Remote Control for computers in this tenant
  /// </summary>
  public bool? EnableImmyBotRemoteControl { get; set; }

  /// <summary>
  /// Whether ImmyBot remote control sessions should be recorded.
  /// </summary>
  public bool? EnableImmyBotRemoteControlRecording { get; set; }
}
