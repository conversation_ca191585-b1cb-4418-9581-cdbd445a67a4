using System;
using Immybot.Backend.Domain.Interfaces;

namespace Immybot.Backend.Domain.Models;

public class Schedule : AuditableUserEntity, ITargetGroup, IOptionalMaintenanceSpecifier, IMaintenanceEmailConfiguration, IMaintenanceSchedulingConfiguration, IAuditableLoggableEntity
{
  public Schedule()
  {
  }

  public int Id { get; set; }

  // Target Group
  public string? Target { get; set; }
  public string? TargetName { get; set; }
  public TargetType TargetType { get; set; }
  public TargetGroupFilter TargetGroupFilter { get; set; }
  public Guid? ProviderDeviceGroupType { get; set; }
  public Guid? ProviderClientGroupType { get; set; }
  public int? ProviderLinkId { get; set; }
  public int? TenantId { get; set; }
  public bool PropagateToChildTenants { get; set; }
  public bool AllowAccessToParentTenant { get; set; }
  public TargetCategory TargetCategory { get; set; }

  // Optional Maintenance Specifier
  //  fields that allow a schedule to target a single maintenance item
  // instead of running full maintenance
  public MaintenanceType? MaintenanceType { get; set; }
  public string? MaintenanceIdentifier { get; set; }

  // Schedule fields
  public bool Disabled { get; set; }
  public int? Day { get; set; }
  public bool SendDetectionEmail { get; set; }
  public bool SendDetectionEmailWhenAllActionsAreCompliant { get; set; }
  public bool SendFollowUpEmail { get; set; }
  public bool SendFollowUpOnlyIfActionNeeded { get; set; }
  public bool ShowRunNowButton { get; set; }
  public bool ShowPostponeButton { get; set; }
  public RebootPreference RebootPreference { get; set; }
  public PromptTimeoutAction PromptTimeoutAction { get; set; }
  public bool AutoConsentToReboots { get; set; }
  public int PromptTimeoutMinutes { get; set; }
  public bool ApplyWindowsUpdates { get; set; }
  public bool ShowMaintenanceActions { get; set; }
  public bool AllowAccessToMSPResources { get; set; }

  // scheduling fields
  public string? TimeZoneInfoId { get; set; }
  public string? Time { get; set; }
  public string? MaintenanceTime { get; set; }
  public string? CustomCronExpression { get; set; }

  private ComputerOfflineMaintenanceSessionBehavior? _offlineBehavior;

  public ComputerOfflineMaintenanceSessionBehavior OfflineBehavior
  {
    get => _offlineBehavior ?? ComputerOfflineMaintenanceSessionBehavior.Skip;
    set => _offlineBehavior = value;
  }

  public bool SuppressRebootsDuringBusinessHours { get; set; }

  /// <inheritdoc/>
  public bool UseComputersTimezoneForExecution { get; set; }

  /// <inheritdoc/>
  public bool ScheduleExecutionAfterActiveHours { get; set; }

  public User? CreatedByUser { get; set; }
  public User? UpdatedByUser { get; set; }
  public Tenant? Tenant { get; set; }
  public ProviderLink? ProviderLink { get; set; }
}
