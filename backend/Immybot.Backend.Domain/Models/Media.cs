using System.Collections.Generic;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Shared.Primitives.Attributes;

namespace Immybot.Backend.Domain.Models;

public class Media : AuditableUserEntity, IAuditableLoggableEntity, ITenantRelationships<TenantMedia>
{

// Ignore nullability warnings for EF entities.
#nullable disable
  public Media()
  {
    TenantRelationships = new HashSet<TenantMedia>();
    LocalSoftware = new HashSet<LocalSoftware>();
    GlobalSoftware = new HashSet<GlobalSoftware>();
    IconForMaintenanceTasks = new HashSet<MaintenanceTask>();
  }

  /// <summary>
  /// Internal constructor used only for testing.
  /// </summary>
  /// <param name="databaseType"></param>
  internal Media(DatabaseType databaseType)
    : this()
  {
    DatabaseType = databaseType;
    DynamicIntegrationTypes = new HashSet<DynamicIntegrationType>();
  }

#nullable enable

  public int Id { get; set; }

  private DatabaseType? _databaseType;

  /// <summary>
  /// Don't allow setting of this field.  ef will populate it automatically
  /// </summary>
  public DatabaseType DatabaseType
  {
    get => _databaseType ?? DatabaseType.Global;
    set => _databaseType = value;
  }

  /// <summary>
  /// Custom / unique identifiable name
  /// </summary>
  [AuditObjectName]
  public string Name { get; set; }

  /// <summary>
  /// Actualy file name of the media
  /// </summary>
  public string FileName { get; set; }

  /// <summary>
  /// Type of the file
  /// </summary>
  public string? MimeType { get; set; }

  /// <summary>
  /// Optional package hash used to verify the integrity of the file
  /// </summary>
  public string? PackageHash { get; set; }

  /// <summary>
  /// The reference to the file stored in azure blob storage
  /// </summary>
  public string BlobReference { get; set; }

  /// <summary>
  /// Maybe get rid of this?
  /// </summary>
  public string? RelativeCacheSourcePath { get; set; }

  public MediaCategory Category { get; set; }

  public ICollection<TenantMedia> TenantRelationships { get; set; }
  public ICollection<LocalSoftware> LocalSoftware { get; }
  public ICollection<GlobalSoftware> GlobalSoftware { get; }

  public ICollection<MaintenanceTask> IconForMaintenanceTasks { get; }

  public User? CreatedByUser { get; set; }
  public User? UpdatedByUser { get; set; }

  public ICollection<DynamicIntegrationType> DynamicIntegrationTypes { get; }

}
