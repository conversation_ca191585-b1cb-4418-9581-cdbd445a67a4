using System;
using System.Collections.Generic;
using System.Management.Automation;
using System.Text.Json;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Shared.Primitives.Attributes;
using Immybot.Shared.Scripts;

namespace Immybot.Backend.Domain.Models;

public class Script : AuditableUserEntity, IAuditableLoggableEntity, ITenantRelationships<TenantScript>
{
  public Script()
  {
    TenantRelationships = new HashSet<TenantScript>();
    Variables = [];
    Parameters = [];
    ChangeRequests = new HashSet<ChangeRequest>();
    ParameterOverrides = [];
  }

  [AuditObjectName]
  public required string Name { get; set; }
  public int Id { get; set; }
  public string Action { get; set; } = string.Empty;
  public ScriptLanguage ScriptLanguage { get; set; } = ScriptLanguage.PowerShell;
  public int? Timeout { get; set; }
  public ScriptExecutionContext ScriptExecutionContext { get; set; }
  public DatabaseType ScriptType { get; set; }
  public bool ReadOnly { get; set; }
  public string Identifier => Id.ToString();
  public ScriptCategory ScriptCategory { get; set; }
  public ScriptOutputType OutputType { get; set; }
  /// <summary>
  /// The url at which this script's Action is downloadable from public storage. For Global
  /// Scripts, this is automatically populated by IScriptActions#GetGlobalScriptById. For
  /// Local Scripts, this is set automatically in the scripts controller when the
  /// script is created/updated (only for Inventory Scripts that have ScriptCacheName set)
  /// </summary>
  public string? PublicStorageDownloadUrl { get; set; }
  /// <summary>
  /// The Sha256 hash of this script's Action. For Global Scripts, this is set automatically
  /// in the database whenever the script is created/updated. For Local Scripts, this is set
  /// automatically in the scripts controller when the script is created/updated (only for
  /// Inventory Scripts that have ScriptCacheName set)
  /// </summary>
  public string? ScriptHash { get; set; }
  /// <summary>
  /// The name of the blob to reference in public storage for devices to download this script
  /// from. If this is present on Global Inventory Scripts, the script action will be
  /// automatically uploaded to the instances' public storage containers when the script is
  /// retrieved by IScriptActions#GetGlobalScriptId. If this is present on Local Inventory
  /// Scripts, the script action will be automatically uploaded to the instance's public storage
  /// container by the scripts controller when the script is updated/created
  /// </summary>
  public string? ScriptCacheName { get; set; }

  /// <summary>
  /// Values provided here will be added as Variables to the Runspace
  /// This is generally used to hold data from the RunContext
  /// e.g. $InstallerFile, $InstallerFolder, $LicenseFilePath, $LicenseValue, $DetectionString, $DisplayName, $InstallerLogFile, $method (for tasks), $PrimaryPersonEmail
  /// </summary>
  public Dictionary<string, object?> Variables { get; set; }
  /// <summary>
  /// Values provided here will be bound to the script's param block
  /// Under the hood they are used in the PowerShell.AddParameters() method
  /// Conceptually similar to splatting
  /// </summary>
  public Dictionary<string, object?> Parameters { get; set; }
  /// <summary>
  /// Used to override parameter values provided by the Deployment/TargetAssignment.
  /// This property is used by the script editor in the frontend to manually change parameter values before
  /// running a script.
  /// </summary>
  public Dictionary<string, JsonElement?> ParameterOverrides { get; set; }

  /// <summary>
  /// field to store the store id for a dynamic provider store
  /// </summary>
  public string? DynamicProviderStoreId { get; set; }

  /// <summary>
  /// field to store dynamic integration type properties that will be available
  /// to the New-DynamicIntegration cmdlet
  /// </summary>
  public DynamicIntegrationTypeProperties? DynamicIntegrationTypeProperties { get; set; }

  /// <summary>
  /// The provider link id for the maintenance item tied to this script.
  /// </summary>
  public int? ProviderLinkIdForMaintenanceItem { get; set; }

  // a script can be optionally assigned to multiple tenants or owned by a tenant
  public ICollection<TenantScript> TenantRelationships { get; set; }

  public ICollection<ChangeRequest> ChangeRequests { get; set; }

  public override string ToString()
  {
    return $"{Name} [{ScriptLanguage}]{Environment.NewLine}\t{Action?.Replace(Environment.NewLine, Environment.NewLine + "\t")}";
  }

  public User? CreatedByUser { get; set; }
  public User? UpdatedByUser { get; set; }

#pragma warning disable S1133
  [Obsolete("No longer used. Kept for backwards compatibility.")]
#pragma warning restore S1133
  public bool Hidden { get; set; }

  public ActionPreference ErrorActionPreference { get; set; }
  public bool SkipPreflight { get; set; }
  public bool SkipBusinessHoursCheck { get; set; }
  public static Script Copy(Script s)
  {
    var script = new Script
    {
      Name = s.Name,
      TenantRelationships = s.TenantRelationships,
      Id = s.Id,
      Action = s.Action,
      ScriptLanguage = s.ScriptLanguage,
      Timeout = s.Timeout,
      ScriptExecutionContext = s.ScriptExecutionContext,
      ScriptType = s.ScriptType,
      ReadOnly = s.ReadOnly,
      ScriptCategory = s.ScriptCategory,
      ScriptHash = s.ScriptHash,
      PublicStorageDownloadUrl = s.PublicStorageDownloadUrl,
      ScriptCacheName = s.ScriptCacheName,
      OutputType = s.OutputType,
      ErrorActionPreference = s.ErrorActionPreference,
      SkipPreflight = s.SkipPreflight,
      SkipBusinessHoursCheck = s.SkipBusinessHoursCheck,
      ProviderLinkIdForMaintenanceItem = s.ProviderLinkIdForMaintenanceItem
    };

    if (s.Variables.Count > 0)
    {
      script.Variables = new Dictionary<string, object?>(s.Variables);
    }

    if (s.Parameters.Count > 0)
    {
      script.Parameters = new Dictionary<string, object?>(s.Parameters);
    }

    return script;
  }

  // relationships
  public DynamicIntegrationType? DynamicIntegrationType { get; set; }
  public InventoryTaskScript? InventoryTaskScript { get; set; }
}
