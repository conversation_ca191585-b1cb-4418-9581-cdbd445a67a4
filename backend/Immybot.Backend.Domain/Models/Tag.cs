using Immybot.Backend.Domain.Interfaces;
using System.Collections.Generic;
using Immybot.Shared.Primitives.Attributes;

namespace Immybot.Backend.Domain.Models;

public class Tag : AuditableUserEntity, ITag, IBaseEntity, IAuditableLoggableEntity,
  ITenantRelationships<TenantTagAuthorization>
{
  public Tag()
  {
    Computers = new HashSet<Computer>();
    ComputerTags = [];
    TenantTags = [];
    Tenants = new HashSet<Tenant>();
    TenantRelationships = new HashSet<TenantTagAuthorization>();
    Persons = new HashSet<Person>();
    PersonTags = new HashSet<PersonTag>();
  }

  public int Id { get; set; }

  [AuditObjectName]
  public string Name { get; set; } = null!;
  public string? Description { get; set; }
  public string? Color { get; set; }

  public User? CreatedByUser { get; set; }
  public User? UpdatedByUser { get; set; }

  public ICollection<Computer> Computers { get; set; }
  public List<ComputerTag> ComputerTags { get; set; }
  public List<TenantTag> TenantTags { get; set; }
  public ICollection<Tenant> Tenants { get; set; }

  public ICollection<Person> Persons { get; set; }
  public ICollection<PersonTag> PersonTags { get; set; }
  public ICollection<TenantTagAuthorization> TenantRelationships { get; set; }

}
