using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using Immybot.Backend.Domain.Interfaces;
using NuGet.Versioning;

namespace Immybot.Backend.Domain.Models;

public class TargetAssignment : AuditableUserEntity, ITargetGroup, IMaintenanceSpecifier, ISoftwareAssignmentDetails, IMaintenanceTaskAssignmentDetails, IAuditableLoggableEntity
{
  /* A target assignment assigns a software version to a particular target
   * A target assignment can be of multiple types
   * A tenant can only assign global software and software they create to a target assignment
   * */
  public TargetAssignment()
  {
    #pragma warning disable CS0618 // Type or member is obsolete
    MaintenanceTaskParameterValues = new HashSet<MaintenanceTaskParameterValue>();
    #pragma warning restore CS0618 // Type or member is obsolete
    ChangeRequests = new HashSet<ChangeRequest>();
  }

  public int Id { get; set; }

  private DatabaseType? _databaseType;

  public DatabaseType DatabaseType
  {
    get => _databaseType ?? DatabaseType.Global;
    set => _databaseType = value;
  }

  /// <summary>
  /// Indicates that this assignment cannot be unapproved. It is a core assignment that is mandated by ImmyBot
  /// </summary>
  public bool IsCore { get; set; }

  /// <summary>
  /// Indicates that this assignment should be auto-approved for new instances
  /// </summary>
  public bool AutoApprove { get; set; }

  /// <summary>
  /// Indicates that this assignment is tied to an integration and is only visible if the specified integration exists
  /// </summary>
  public string? IntegrationTypeId { get; set; }
  public string? IntegrationPrompt { get; set; }

  /// <summary>
  /// indicates whether this assignment is eligible to be considered during assignment resolution
  /// </summary>
  public bool Excluded { get; set; }

  public int SortOrder { get; set; }
  // maintenance fields

  public required string MaintenanceIdentifier { get; set; }

  public MaintenanceType MaintenanceType { get; set; }

  // target fields
  private string? targetName { get; set; }
  #pragma warning disable S1133 // Do not forget to remove this deprecated code someday
  [Obsolete("No longer used as this is now handled by Target Enforcement")]
  #pragma warning restore S1133 // Do not forget to remove this deprecated code someday
  public bool OnboardingOnly { get; set; }
  public TargetEnforcement TargetEnforcement { get; set; }
  public TargetType TargetType { get; set; }
  public TargetCategory TargetCategory { get; set; }
  public TargetGroupFilter TargetGroupFilter { get; set; }
  public string? Target { get; set; }
  public int? TenantId { get; set; }
  public bool PropagateToChildTenants { get; set; }
  public bool AllowAccessToParentTenant { get; set; }
  public int? ProviderLinkId { get; set; }
  public Guid? ProviderDeviceGroupType { get; set; }
  public Guid? ProviderClientGroupType { get; set; }

  /// <summary>
  /// When present, this assignment's maintenance item is tied to this provider link id.
  /// The value is used to perform actions against a specific provider during execution of the maintenance item.
  /// e.g. Sentinel One software installs will reach out to the Sentinel One provider to get an install token that is passed
  /// to the install script.
  /// </summary>
  public int? ProviderLinkIdForMaintenanceItem { get; set; }

  public string? TargetName
  {
    get
    {
      if (targetName != null)
        return targetName;
      else
        return Target;
    }
    set
    {
      targetName = value;
    }
  }
  public ProviderLink? ProviderLink { get; set; }

  public TargetAssignmentNotes? Notes { get; set; }

  /// <summary>
  /// Unavailable for global target assignments
  /// </summary>
  public TargetAssignmentVisibility? Visibility { get; set; }

  // software fields
  public SemanticVersion? SoftwareSemanticVersion { get; set; }
  public string? SoftwareSemanticVersionString => SoftwareSemanticVersion?.ToNormalizedString();
  public DesiredSoftwareState? DesiredSoftwareState { get; set; }
  public SoftwareProviderType? SoftwareProviderType { get; set; }

  /// <summary>
  /// When an assignment targets a task that uses a param block, the values are stored here as json.
  /// </summary>
  public ImmutableDictionary<string, DeploymentParameterValue>? TaskParameterValues { get; set; }

  public int? LicenseId { get; set; }
  public License? License { get; set; }


  // maintenance task fields
  public MaintenanceTaskMode? MaintenanceTaskMode { get; set; }

  #pragma warning disable S1133 // Do not forget to remove this deprecated code someday
  [Obsolete("TaskParameterValues is now used instead")]
  #pragma warning restore S1133 // Do not forget to remove this deprecated code someday
  public ICollection<MaintenanceTaskParameterValue> MaintenanceTaskParameterValues { get; set; }

  public ICollection<ChangeRequest> ChangeRequests { get; set; }

  public string? ParameterValueMigrationErrors { get; set; }

  // provider

  // software specifier helper
  public SoftwareSpecifier? SoftwareSpecifier
  {
    get
    {
      SoftwareType type;
      switch (MaintenanceType)
      {
        case MaintenanceType.GlobalSoftware:
          type = SoftwareType.GlobalSoftware;
          break;
        case MaintenanceType.LocalSoftware:
          type = SoftwareType.LocalSoftware;
          break;
        case MaintenanceType.NiniteSoftware:
          type = SoftwareType.Ninite;
          break;
        case MaintenanceType.ChocolateySoftware:
          type = SoftwareType.Chocolatey;
          break;
        default:
          return null;
      }
      return new SoftwareSpecifier
      {
        SoftwareType = type,
        SoftwareIdentifier = MaintenanceIdentifier,
      };
    }
  }

  // maintenance specifier helper
  public MaintenanceSpecifier MaintenanceSpecifier
  {
    get
    {
      return new MaintenanceSpecifier
      {
        MaintenanceIdentifier = MaintenanceIdentifier,
        MaintenanceType = MaintenanceType,
      };
    }
  }

  public User? CreatedByUser { get; set; }
  public User? UpdatedByUser { get; set; }

  #pragma warning disable S1133 // Do not forget to remove this deprecated code someday
  [Obsolete("No longer used")]
  #pragma warning restore S1133 // Do not forget to remove this deprecated code someday
  public int? SoftwareId { get; set; }

  #pragma warning disable S1133 // Do not forget to remove this deprecated code someday
  [Obsolete("No longer used")]
  #pragma warning restore S1133 // Do not forget to remove this deprecated code someday
  public int SoftwareVersionId { get; set; }

  public override string ToString()
  {
    return $"{(DatabaseType == DatabaseType.Global ? "Recommended " : "")}Deployment #{Id} specifies {MaintenanceSpecifier} should have {DesiredSoftwareState} on {TargetType} {targetName}";
  }
}
