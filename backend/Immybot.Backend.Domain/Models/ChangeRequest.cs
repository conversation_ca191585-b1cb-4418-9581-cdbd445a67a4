using System.Collections.Generic;
using System.Text.Json;
using Immybot.Backend.Domain.Interfaces;

namespace Immybot.Backend.Domain.Models;

public interface IChangeRequest
{
  int Id { get; }
  ChangeRequestObjectType ObjectType { get; }
  ChangeRequestState State { get; set; }
  int? TargetAssignmentId { get; }
  int? ScriptId { get; }
  JsonElement NewValuesJson { get; }
  int? AcknowledgedByUserId { get; }
  string? AcknowledgedByUserName { get; }
}

public class ChangeRequest : AuditableUserEntity, IAuditableLoggableEntity, IChangeRequest
{
  public ChangeRequest()
  {
    Comments = new HashSet<ChangeRequestComment>();
  }

  /// <summary>
  /// pk
  /// </summary>
  public int Id { get; set; }

  /// <summary>
  /// The type of object associated with this change request
  /// </summary>
  public ChangeRequestObjectType ObjectType { get; set; }

  /// <summary>
  /// FK to target assignment
  /// </summary>
  public int? TargetAssignmentId { get; set; }

  /// <summary>
  /// FK to script
  /// </summary>
  public int? ScriptId { get; set; }

  /// <summary>
  /// The new values of the object in json format
  /// </summary>
  public JsonElement NewValuesJson { get; set; }

  private ChangeRequestState? _state;

  /// <summary>
  /// The current state of the change request
  /// </summary>
  public ChangeRequestState State
  {
    get => _state ?? ChangeRequestState.WaitingForApproval;
    set => _state = value;
  }


  /// <summary>
  /// The user id of the user who approved the change request (if it was not a global approval)
  /// </summary>
  public int? AcknowledgedByUserId { get; set; }

  /// <summary>
  /// The name of the user who approved the change request
  /// </summary>
  public string? AcknowledgedByUserName { get; set; }

  /// <summary>
  /// Comments tied to the change request
  /// </summary>
  public ICollection<ChangeRequestComment> Comments { get; set; }

  /// <summary>
  /// User who created the merge request
  /// </summary>
  public User? CreatedByUser { get; set; }

  /// <summary>
  /// User who updated the merge request
  /// </summary>
  public User? UpdatedByUser { get; set; }

  /// <summary>
  /// The target assignment associated with this change request if it was for a target assignment
  /// </summary>
  public TargetAssignment? TargetAssignment { get; set; }

  /// <summary>
  /// The script associated with this change request if it was for a script
  /// </summary>
  public Script? Script { get; set; }
}
