namespace Immybot.Backend.Domain.Models;

public class SmtpConfig
{
  public int TenantId { get; set; }
  public int Port { get; set; }
  public string Host { get; set; } = string.Empty;
  public bool EnableSSL { get; set; }
  public int Timeout { get; set; }
  public string? Username { get; set; }
  public string? PasswordHash { get; set; }
  public bool Enabled { get; set; }
  public bool UseAuthentication { get; set; }
  public Tenant? Tenant { get; set; }
}
