using System;
using System.Collections.Generic;
using System.Linq;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Immybot.Backend.Domain.Models;

public class ComputerLatestProviderEvent
{
  public int ComputerId { get; set; }

  // last provider agent connected/disconnected event for this computer
  public DateTime LastProviderAgentEventDateUtc { get; set; }

  public Computer? Computer { get; set; }
}
public class Computer : AuditableDateEntity, ISoftDelete
{
  public Computer()
  {
    Sessions = new HashSet<MaintenanceSession>();
    AdditionalPersons = new HashSet<ComputerPerson>();
    Schedules = new HashSet<Schedule>();
    Agents = new HashSet<ProviderAgent>();
    LatestInventoryScriptResults = new HashSet<ComputerInventoryTaskScriptResult>();
    AgentIdentificationFailures = new HashSet<AgentIdentificationFailure>();
    DetectedSoftware = new HashSet<DetectedComputerSoftware>();
    Tags = new HashSet<Tag>();
    ComputerTags = [];
    RemoteControlRecordings = new HashSet<RemoteControlRecording>();
    PredecessorComputers = new HashSet<Computer>();
    UserAffinities = new HashSet<UserAffinity>();
  }
  public int Id { get; set; }
  public Guid DeviceId { get; set; }
  public int TenantId { get; set; }
  public int? PrimaryPersonId { get; set; }
  public ComputerOnboardingStatus OnboardingStatus { get; set; } = ComputerOnboardingStatus.Onboarded;
  public DateTime? OnboardedDateUtc { get; set; }
  public DateTime? InventoryStartedDate { get; set; }
  public bool DetectionOutdated { get; set; }

  // Inventory-driven cached items
  public string? ComputerName { get; set; }
  public List<int>? ChassisTypes { get; set; }
  public string? OperatingSystem { get; set; }
  public string? Manufacturer { get; set; }
  public string? Model { get; set; }
  public string? SerialNumber { get; set; }
  public int? DomainRole { get; set; }
  public bool? HasPendingReboot { get; set; }
  public string? Domain { get; set; }
  public string? LastBootTimeUtc { get; set; }
  public string? InternalIpAddress { get; set; }
  public string? ExternalIpAddress { get; set; }
  public string? LastLoggedOnUser { get; set; }
  public DateTime? OSInstallDate { get; set; }
  public bool IsSandbox { get; set; }

  /// <summary>
  /// Indicates that this computer should not be considered when running user affinity
  /// </summary>
  public bool ExcludedFromUserAffinity { get; set; }

  // set by user action - indicates we want to start utilizing the vm and set an expiration date
  public DateTime? DevLabVmClaimExpirationDateUtc { get; set; }

  // set by user action - indicates the vm is unclaimed (marked for deletion)
  public bool DevLabVmUnclaimed { get; set; }

  // name of the vm resource we have claimed
  public string? DevLabVmName { get; set; }

  public bool ExcludeFromMaintenance { get; set; }

  public TrustedManufacturer? GetTrustedManufacturer() => ManufacturerHelpers.GetTrustedManufacturer(Manufacturer);

  // Relationships
  public Tenant? Tenant { get; set; }
  public Person? PrimaryPerson { get; set; }
  public ComputerLatestProviderEvent? LatestProviderEvent { get; set; }
  public ComputerNote? ComputerNote { get; set; }
  public ICollection<MaintenanceSession> Sessions { get; set; }
  public ICollection<ComputerPerson> AdditionalPersons { get; set; }
  public ICollection<Schedule> Schedules { get; set; }
  public ICollection<ProviderAgent> Agents { get; set; }
  public ICollection<ComputerInventoryTaskScriptResult> LatestInventoryScriptResults { get; set; }
  public ICollection<AgentIdentificationFailure> AgentIdentificationFailures { get; set; }
  public ICollection<DetectedComputerSoftware> DetectedSoftware { get; set; }
  public ICollection<Tag> Tags { get; set; }
  public ICollection<RemoteControlRecording> RemoteControlRecordings { get; set; }
  public ICollection<UserAffinity> UserAffinities { get; set; }
  public List<ComputerTag> ComputerTags { get; set; }

  // Computed getters
  public IReadOnlyCollection<ProviderAgent> GetRunScriptAgents()
    => Agents.Where(a => a.SupportsRunningScripts && a.DeletedAt == null).ToArray();

  public IReadOnlyCollection<ProviderAgent> GetOnlineRunScriptAgents()
    => GetRunScriptAgents().Where(
      a => a.IsOnline &&
           a.DeletedAt is null &&
           !a.ProviderLink!.Disabled &&
           a.ProviderLink.HealthStatus == HealthStatus.Healthy).ToArray();

  public IReadOnlyCollection<ProviderAgent> GetRunScriptAgentsForHealthyProviders()
    => GetRunScriptAgents().Where(a => !a.ProviderLink!.Disabled && a.ProviderLink.HealthStatus == HealthStatus.Healthy)
      .ToArray();

  /// <summary>
  /// A device is considered "trackable" when it is over seven days old
  /// </summary>
  public bool GetIsTrackable(DateTime now) =>
    GetIsOverSevenDaysSinceOnboarded(now);

  /// <summary>
  /// Determines if more than 7 days have passed since the computer was onboarded.
  /// Returns true if:
  /// 1. OnboardedDateUtc + 7 days is earlier than current UTC time, or
  /// 2. Computer is onboarded, OnboardedDateUtc is null, and CreatedDate + 7 days is earlier than current UTC time.
  /// </summary>
  public bool GetIsOverSevenDaysSinceOnboarded(DateTime now) =>
    (OnboardedDateUtc?.AddDays(7) is { } d && d < now)
    || (OnboardingStatus is ComputerOnboardingStatus.Onboarded &&
        OnboardedDateUtc is null &&
        CreatedDate.AddDays(7) < now);
  public DateTime? DeletedAt { get; set; }
  public string? DeletedReason { get; set; }

  /// <summary>
  /// The id of the computer that this computer was replaced by
  /// </summary>
  public int? SuccessorComputerId { get; set; }

  public Computer? SuccessorComputer { get; set; }
  public ICollection<Computer> PredecessorComputers { get; set; }
}
