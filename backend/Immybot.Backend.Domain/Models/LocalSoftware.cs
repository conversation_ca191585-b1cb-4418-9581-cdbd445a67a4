using Immybot.Backend.Domain.Comparers;
using Immybot.Backend.Domain.Interfaces;

namespace Immybot.Backend.Domain.Models;

public class LocalSoftware : Software, ICustomAuditProperties
{
  public LocalSoftware()
  {
    TenantSoftware = new HashSet<TenantSoftware>();
    SoftwareVersions = new HashSet<LocalSoftwareVersion>();
    SoftwarePrerequisites = new SortedSet<SoftwarePrerequisite>(new ByActionToPerform());
  }

  public int Id { get; set; }
  public int? OwnerTenantId { get; set; }

  public override SoftwareType SoftwareType => SoftwareType.LocalSoftware;
  public override string Identifier => Id.ToString();

  // Relationships
  public ICollection<TenantSoftware> TenantSoftware { get; set; }
  public ICollection<LocalSoftwareVersion> SoftwareVersions { get; set; }
  public override ICollection<SoftwarePrerequisite> SoftwarePrerequisites { get; }

  public override ICollection<SoftwareVersion> GetSoftwareVersions() =>
    SoftwareVersions.OfType<SoftwareVersion>().ToList();

  public Media? SoftwareIcon { get; }
  public User? CreatedByUser { get; set; }
  public User? UpdatedByUser { get; set; }

  /// <summary>
  /// Used to store custom audit changes that are not part of the standard auditing process.
  /// We'll use this to store prerequisite changes, for example.
  /// </summary>
  public List<AuditPropertyChange> CustomAuditProperties { get; } = [];
}
