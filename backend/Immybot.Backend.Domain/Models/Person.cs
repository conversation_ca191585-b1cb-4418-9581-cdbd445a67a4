using System.Collections.Generic;
using Immybot.Backend.Domain.Models.RBAC;

namespace Immybot.Backend.Domain.Models;

public class Person : AuditableUserEntity
{
  public Person()
  {
    PrimaryComputers = new HashSet<Computer>();
    AdditionalComputers = new HashSet<ComputerPerson>();
    UserAffinities = new HashSet<UserAffinity>();
    AccessRequests = new HashSet<AccessRequest>();
    Tags = new HashSet<Tag>();
    PersonTags = new HashSet<PersonTag>();
    DetectedComputerSoftware = new HashSet<DetectedComputerSoftware>();
    PersonSessions = new HashSet<MaintenanceSession>();
  }

  public int Id { get; set; }
  public int TenantId { get; set; }
  public string? AzurePrincipalId { get; set; }
  public string? FirstName { get; set; }
  public string? LastName { get; set; }
  public required string EmailAddress { get; set; }
#pragma warning disable S3220 // Method calls should not resolve ambiguously to overloads with "params"
  public string DisplayName => string.Concat(FirstName, " ", LastName);
#pragma warning restore S3220 // Method calls should not resolve ambiguously to overloads with "params"
  public ICollection<Computer> PrimaryComputers { get; set; }
  public ICollection<ComputerPerson> AdditionalComputers { get; set; }
  public ICollection<DetectedComputerSoftware> DetectedComputerSoftware { get; set; }
  public ICollection<MaintenanceSession> PersonSessions { get; set; }
  public User? User { get; set; }
  public Tenant? Tenant { get; set; }
  public ICollection<UserAffinity> UserAffinities { get; set; }

  public ICollection<Tag> Tags { get; set; }
  public ICollection<PersonTag> PersonTags { get; set; }
  public string? OnPremisesSecurityIdentifier { get; set; }

  public override string ToString()
  {
    return this.DisplayName;
  }

  public User? CreatedByUser { get; set; }
  public User? UpdatedByUser { get; set; }
  public ICollection<AccessRequest> AccessRequests { get; set; }
}
