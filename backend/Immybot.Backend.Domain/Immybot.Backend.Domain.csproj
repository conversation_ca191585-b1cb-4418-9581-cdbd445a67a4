<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ProduceReferenceAssembly>true</ProduceReferenceAssembly>
    <ProduceReferenceAssemblyInOutDir>true</ProduceReferenceAssemblyInOutDir>
  </PropertyGroup>
  <ItemGroup Condition="'$(PSESDebug)' != 'true'">
    <PackageReference Include="Microsoft.PowerShell.Commands.Diagnostics" GeneratePathProperty="true" />
    <PackageReference Include="Microsoft.PowerShell.Commands.Management" GeneratePathProperty="true" />
    <PackageReference Include="Microsoft.PowerShell.Commands.Utility" GeneratePathProperty="true" />
    <PackageReference Include="Microsoft.PowerShell.SDK" GeneratePathProperty="true" />
    <PackageReference Include="System.Management.Automation" GeneratePathProperty="true" />
    <PackageReference Include="Microsoft.WSMan.Management" GeneratePathProperty="true" />
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>$(AssemblyName)</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>
  <ItemGroup>
    <None Include="$(PkgMicrosoft_powershell_commands_utility)/runtimes/unix/lib/net8.0/Microsoft.PowerShell.Commands.Utility.dll" CopyToOutputDirectory="PreserveNewest" />
    <None Include="$(PkgSystem_management_automation)/runtimes/unix/lib/net8.0/System.Management.Automation.dll" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>
  <ItemGroup Condition="$(PSESDebug) == 'true'">
    <None Include="$(MSBuildProjectDirectory)/../../../PowerShell/src/Microsoft.PowerShell.Commands.Diagnostics/**/*.cs" Link="%(RecursiveDir)%(Filename)%(Extension)" />
    <None Include="$(MSBuildProjectDirectory)/../../../PowerShell/src/Microsoft.PowerShell.Commands.Management/**/*.cs" Link="%(RecursiveDir)%(Filename)%(Extension)" />
    <None Include="$(MSBuildProjectDirectory)/../../../PowerShell/src/Microsoft.PowerShell.Commands.Utility/**/*.cs" Link="%(RecursiveDir)%(Filename)%(Extension)" />
    <None Include="$(MSBuildProjectDirectory)/../../../PowerShell/src/Microsoft.PowerShell.SDK/**/*.cs" Link="%(RecursiveDir)%(Filename)%(Extension)" />
    <None Include="$(MSBuildProjectDirectory)/../../../PowerShell/src/System.Management.Automation/**/*.cs" Link="%(RecursiveDir)%(Filename)%(Extension)" />
    <None Include="$(MSBuildProjectDirectory)/../../../PowerShell/src/Microsoft.WSMan.Management/**/*.cs" Link="%(RecursiveDir)%(Filename)%(Extension)" />
  </ItemGroup>
  <ItemGroup>
    <InternalsAssemblyName Include="System.Management.Automation" />
    <PackageReference Include="App.Metrics.Abstractions" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.CodeAnalysis.Common" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    <PackageReference Include="Immybot.Manager.Shared" />
    <PackageReference Include="Microsoft.Extensions.Primitives" />
    <PackageReference Include="Microsoft.VisualStudio.Threading" />
    <PackageReference Include="MonoMod.RuntimeDetour" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="NuGet.Versioning" />
    <PackageReference Include="OneOf" />
    <PackageReference Include="Polly" />
    <PackageReference Include="SonarAnalyzer.CSharp" />
    <PackageReference Include="System.Text.Json" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\shared\Immybot.Shared.DataContracts\Immybot.Shared.DataContracts.csproj" />
    <ProjectReference Include="..\..\shared\Immybot.Shared.JsonDeepEqual\Immybot.Shared.JsonDeepEqual.csproj" />
    <ProjectReference Include="..\..\shared\Immybot.Shared.Primitives\Immybot.Shared.Primitives.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Exceptions\"/>
  </ItemGroup>
</Project>
