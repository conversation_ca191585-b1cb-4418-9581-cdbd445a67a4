using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Shared.Primitives;
using Microsoft;
using OneOf;

namespace Immybot.Backend.Domain.Helpers;

public static class TaskExtensions
{
  public static void Forget(this Task task)
  {
    // note: this code is inspired by a tweet from <PERSON>: https://twitter.com/ben_a_adams/status/1045060828700037125
    // Only care about tasks that may fault (not completed) or are faulted,
    // so fast-path for SuccessfullyCompleted and Canceled tasks.
    if (!task.IsCompleted || task.IsFaulted)
    {
      // use "_" (Discard operation) to remove the warning IDE0058: Because this call is not awaited, execution of the current method continues before the call is completed
      // https://docs.microsoft.com/en-us/dotnet/csharp/discards#a-standalone-discard
      _ = ForgetAwaited(task);
    }

    // Allocate the async/await state machine only when needed for performance reason.
    // More info about the state machine: https://blogs.msdn.microsoft.com/seteplia/2017/11/30/dissecting-the-async-methods-in-c/?WT.mc_id=DT-MVP-5003978
    static async Task ForgetAwaited(Task task)
    {
      try
      {
        // No need to resume on the original SynchronizationContext, so use ConfigureAwait(false)
#pragma warning disable VSTHRD003
        await task.ConfigureAwait(false);
#pragma warning restore VSTHRD003
      }
      catch
      {
        // Nothing to do here
      }
    }
  }

  /// <summary>
  /// Allows you to await a task with a timeout without it throwing a TimeoutException exception, instead returning an OpResult of <typeparamref name="T"/>.
  /// </summary>
  /// <typeparam name="T"></typeparam>
  /// <param name="task"></param>
  /// <param name="timeoutSource"></param>
  /// <param name="catchAllExceptions">When true, no exceptions are allowed to 'bubble up' without being wrapped in an OpResult. If left as false (default), the only exceptions caught will be TimeoutExceptions.</param>
  /// <returns></returns>
  public static async Task<OpResult<T>> WaitAsyncOpResult<T>(this Task<T> task, OneOf<TimeSpan, CancellationToken> timeoutSource, bool catchAllExceptions = false)
  {
    try
    {
      var result = await timeoutSource.Match(task.WaitAsync, task.WaitAsync).ConfigureAwait(false);
      return new(result);
    }
    catch (TimeoutException ex)
    {
      return new(ex);
    }
    catch (Exception ex) when (catchAllExceptions)
    {
      return new(ex);
    }
  }

#pragma warning disable VSTHRD003 // Avoid awaiting foreign Tasks
  // (JG: This code was copied from the same repo where the analyzer exists, so...)
  /// <summary>
  /// This code was extracted from Microsoft.VisualStudio.Threading.  I just cleaned it up a little.
  /// </summary>
  /// <typeparam name="T"></typeparam>
  /// <param name="task"></param>
  /// <param name="cancellationToken"></param>
  /// <returns></returns>
  public static Task<T> WithCancellation<T>(this Task<T> task, CancellationToken cancellationToken)
  {
    Requires.NotNull(task);
    if (!cancellationToken.CanBeCanceled || task.IsCompleted)
    {
      return task;
    }

    if (cancellationToken.IsCancellationRequested)
    {
      return Task.FromCanceled<T>(cancellationToken);
    }

    return WithCancellationSlow(task, cancellationToken);
  }

  public static Task<bool> IgnoreCancellation(this Task task) => task.ContinueWith(t => t.IsCanceled, TaskScheduler.Default);

  [DebuggerStepThrough]
  private static async Task<T> WithCancellationSlow<T>(Task<T> task, CancellationToken cancellationToken)
  {
    Assumes.NotNull(task);
    Assumes.True(cancellationToken.CanBeCanceled);

    var taskCompletionSource = new TaskCompletionSource<bool>();

    await using var registration = cancellationToken.Register(s =>
    {
      if (s is TaskCompletionSource<bool> tcs)
      {
        tcs.TrySetResult(true);
      }
    }, taskCompletionSource);

    if (task != await Task.WhenAny(task, taskCompletionSource.Task).ConfigureAwait(false))
    {
      cancellationToken.ThrowIfCancellationRequested();
    }

    return await task.ConfigureAwait(false);
  }
#pragma warning restore VSTHRD003 // Avoid awaiting foreign Tasks

  public static async Task<T?> GetFirstResultOrDefault<T>(
      this ICollection<Task<T>> tasks,
      Predicate<T> predicate,
      T? defaultValue = null) where T : class
  {
    var remainingTasks = tasks.ToList();
    while (remainingTasks.Any())
    {
      var completedTask = await Task.WhenAny(remainingTasks);
      if (await completedTask is { } result && predicate(result))
      {
        return result;
      }
      remainingTasks.Remove(completedTask);
    }
    return defaultValue;
  }
}

