$ExistingBITStransfer = Get-BitsTransfer | ? { $_.FileList.RemoteName -contains $src }
if ($ExistingBITStransfer -eq $null) {
  New-Item -ItemType directory -Path (split-path -Parent $dest) -force -erroraction silentlycontinue | Out-Null
  $Job = Start-BitsTransfer -Source $src -Destination $dest -Asynchronous -RetryTimeout 600 -RetryInterval 60
  $Job = Get-BitsTransfer -JobId $job.JobId
}
else {
  $Job = $ExistingBITStransfer
}
$guid = $job.JobId.Guid
$guid
