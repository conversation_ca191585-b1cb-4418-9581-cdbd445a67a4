New-Item -ItemType Directory -Force -Path $targetFolder | Out-Null

try {
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
}
catch {
    "Unable to set SecurityProtocol to TLS v1.2, will fall-back to TLS v1.0 for download.";
    [System.Net.ServicePointManager]::SecurityProtocol = 'Tls';
}

$WebClient = New-Object System.Net.WebClient
$WebClient.UseDefaultCredentials = $true
$WebClient.Proxy = [System.Net.WebRequest]::GetSystemWebProxy()
$WebClient.DownloadFile($uri, $localPath)
