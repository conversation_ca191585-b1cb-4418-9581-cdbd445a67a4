using System;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NuGet.Versioning;

namespace Immybot.Backend.Persistence.Configurations;

public class MaintenanceActionConfiguration : IEntityTypeConfiguration<MaintenanceAction>
{
  public void Configure(EntityTypeBuilder<MaintenanceAction> builder)
  {
    builder.HasKey(s => s.Id);
    builder.Property(s => s.MaintenanceSessionId).IsRequired(true);
    builder.Property(s => s.DetectedVersion).HasConversion(v => v.ToNormalizedString(), s => NuGetVersion.Parse(s));
    builder.Property(s => s.DesiredVersion).HasConversion(v => v.ToNormalizedString(), s => NuGetVersion.Parse(s));
    builder.HasMany(s => s.DependsOn)
      .WithOne(d => d.Dependent)
      .HasForeignKey(d => d.DependentId)
      .HasPrincipalKey(a => a.Id)
      .OnDelete(DeleteBehavior.Cascade);
    builder.HasMany(s => s.Dependents)
      .WithOne(d => d.DependsOn)
      .HasForeignKey(d => d.DependsOnId)
      .HasPrincipalKey(s => s.Id)
      .OnDelete(DeleteBehavior.Cascade);

    builder.HasIndex(a => a.ComputerId).IsUnique(false);

    builder.Property(a => a.SoftwareProviderType).HasDefaultValue(SoftwareProviderType.Inherent);
    builder.Property(p => p.MaintenanceDisplayName).HasMaxLength(MaxLengthConstants.MediumText);
    builder.Property(p => p.MaintenanceIdentifier).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(p => p.DetectedVersion).HasMaxLength(MaxLengthConstants.SemanticVersion);
    builder.Property(p => p.DesiredVersion).HasMaxLength(MaxLengthConstants.SemanticVersion);
    #pragma warning disable CS0618 // Type or member is obsolete
    builder.Property(p => p.LastActionRmmComputerId).HasMaxLength(MaxLengthConstants.SmallText);
    #pragma warning restore CS0618 // Type or member is obsolete
    builder.Property(a => a.CreatedDate)
     .HasConversion(
       a => a,
       a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.StartTime)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.EndTime)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Ignore(p => p.DependsOnActions);

    builder.HasIndex(a => a.TenantId);

    builder.HasIndex(a => new { a.AssignmentId, a.AssignmentType });
    builder.HasIndex(a => new { a.MaintenanceType, a.MaintenanceIdentifier });
    builder.HasIndex(a => a.CreatedDate).IsDescending();
    builder.Property(a => a.HasDeterminedDesiredVersion).HasDefaultValue(false);
    builder.Property(a => a.HasDeterminedDetectedVersion).HasDefaultValue(false);
    builder.Property(a => a.HasDeterminedTaskGetResult).HasDefaultValue(false);
    builder.Property(a => a.HasDeterminedTaskTestResult).HasDefaultValue(false);

    builder.Property(a => a.Parameters);

    builder.Property(a => a.ParentId);

    builder.Property(a => a.UsesManualProgressControl).HasDefaultValue(false);
  }
}
public class MaintenanceActionDependencyConfiguration : IEntityTypeConfiguration<MaintenanceActionDependency>
{
  public void Configure(EntityTypeBuilder<MaintenanceActionDependency> builder)
  {
    builder.ToTable("maintenance_action_dependencies");
    builder.HasKey(s => new { s.DependentId, s.DependsOnId });
    builder.HasIndex(a => a.DependentId);
    builder.HasIndex(a => a.DependsOnId);
  }
}
