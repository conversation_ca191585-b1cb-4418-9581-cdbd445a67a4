using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class ComputerInventorySoftwareConfiguration : IEntityTypeConfiguration<ComputerInventorySoftware>
{
  public void Configure(EntityTypeBuilder<ComputerInventorySoftware> builder)
  {
    builder.HasNoKey();
    builder.ToSqlQuery(@"select dcs.display_name, dcs.display_version , dcs.upgrade_code, count(*) as computers
from detected_computer_software dcs
inner join computers c on c.id = dcs.computer_id
where c.deleted_at is null
group by display_name , dcs.display_version, dcs.upgrade_code");
  }
}
