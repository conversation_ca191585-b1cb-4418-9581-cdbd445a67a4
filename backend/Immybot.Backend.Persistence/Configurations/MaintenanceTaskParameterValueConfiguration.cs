using System;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class MaintenanceTaskParameterValueConfiguration : IEntityTypeConfiguration<MaintenanceTaskParameterValue>
{
  public void Configure(EntityTypeBuilder<MaintenanceTaskParameterValue> builder)
  {
    builder.ToTable("maintenance_task_parameter_values");
    builder.Property(a => a.Value);
    builder.Property(a => a.MaintenanceTaskId).IsRequired();
    builder.Property(a => a.MaintenanceTaskParameterId).IsRequired();

    #pragma warning disable CS0618 // Type or member is obsolete
    builder.HasOne(a => a.Deployment).WithMany(a => a.MaintenanceTaskParameterValues);
    #pragma warning restore CS0618 // Type or member is obsolete

    builder.Ignore(a => a.MaintenanceTask);
    builder.Ignore(a => a.MaintenanceTaskParameter);
    builder.Ignore(a => a.Media);

    builder.Property(a => a.Value).HasMaxLength(MaxLengthConstants.MediumText);

    builder.HasBaseType((Type)null);
    builder.Property(a => a.AllowOverrideFromComputerOnboarding).HasDefaultValue(false);
  }
}
