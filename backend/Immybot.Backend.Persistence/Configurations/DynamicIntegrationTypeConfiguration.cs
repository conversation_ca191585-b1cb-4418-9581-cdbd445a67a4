using System;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class DynamicIntegrationTypeConfiguration: IEntityTypeConfiguration<DynamicIntegrationType>
{
  public void Configure(EntityTypeBuilder<DynamicIntegrationType> builder)
  {
    builder.HasKey(a => a.Id);

    builder.Property(a => a.DatabaseType)
      .HasDefaultValue(DatabaseType.Local);

    builder.Property(a => a.Tag)
      .HasDefaultValue(IntegrationTag.Developer);

    builder.HasIndex(a => a.IntegrationTypeId)
      .IsUnique();
    builder.Property(a => a.Name)
      .IsRequired()
      .HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.ScriptId)
      .IsRequired();

    builder
      .HasOne(a => a.Logo)
      .WithMany(a => a.DynamicIntegrationTypes)
      .HasForeignKey(a => a.LogoId)
      .OnDelete(DeleteBehavior.SetNull);

    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Ignore(a => a.Secrets);
  }
}
