using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class ProviderClientConfiguration : IEntityTypeConfiguration<ProviderClient>
{
  public void Configure(EntityTypeBuilder<ProviderClient> builder)
  {
    builder.HasKey(c => new { c.ProviderLinkId, c.ExternalClientId });

    builder.Property(c => c.ExternalClientId)
      .HasMaxLength(100)
      .IsRequired();

    builder
      .HasMany(c => c.ProviderAgents)
      .WithOne(r => r.ProviderClient)
      .HasForeignKey(r => new { r.ProviderLinkId, r.ExternalClientId })
      .HasPrincipalKey(c => new { c.ProviderLinkId, c.ExternalClientId })
      .OnDelete(DeleteBehavior.Cascade);

    builder.Property(a => a.ExternalClientName).HasMaxLength(MaxLengthConstants.SmallText);
  }
}
