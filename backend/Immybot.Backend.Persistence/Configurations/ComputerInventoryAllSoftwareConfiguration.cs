using Immybot.Backend.Domain.Models;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class ComputerInventoryAllSoftwareConfiguration : IEntityTypeConfiguration<ComputerInventoryAllSoftware>
{
  public void Configure(EntityTypeBuilder<ComputerInventoryAllSoftware> builder)
  {
    builder
      .HasNoKey()
      .ToView(null)
      .ToSqlQuery(@"SELECT
  c.id AS computer_id,
  t.id AS tenant_id,
  t.name AS tenant,
	c.computer_name AS computer,
	v.value ->> 'DisplayName' AS software_name,
	v.value ->> 'DisplayVersion' AS software_version
FROM computers c
INNER JOIN tenants t ON c.tenant_id = t.id
LEFT JOIN LATERAL (
  SELECT * FROM computer_inventory_task_script_results softwares
  WHERE softwares.computer_id = c.id AND softwares.inventory_key = 'Software'
  ORDER BY softwares.timestamp DESC LIMIT 1
) AS softwares ON true
CROSS JOIN LATERAL jsonb_array_elements(softwares.latest_success_result -> 'Output') v(value)
WHERE c.deleted_at IS NULL");
  }
}
