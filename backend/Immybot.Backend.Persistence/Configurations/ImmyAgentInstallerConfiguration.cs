using System;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class ImmyAgentInstallerConfiguration : IEntityTypeConfiguration<ImmyAgentInstaller>
{
  public void Configure(EntityTypeBuilder<ImmyAgentInstaller> builder)
  {
    builder.HasKey(a => a.Id);
    builder.HasIndex(a => a.InstallerId);
    builder.HasIndex(a => a.ProviderLinkId);

    builder.Property(a => a.AdditionalPersonIds).IsRequired(false);
    builder.Property(a => a.Tags).IsRequired(false);
    builder.Property(a => a.<PERSON>ey).IsRequired();

    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
  }
}
