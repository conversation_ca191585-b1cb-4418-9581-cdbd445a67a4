using System.Text.Json;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class AzureErrorsConfiguration : IEntityTypeConfiguration<AzureErrorLogItem>
{
  private static readonly JsonSerializerOptions _jsonOpts = new()
  {
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
  };
  public void Configure(EntityTypeBuilder<AzureErrorLogItem> builder)
  {
    builder.ToTable("azure_errors");

    builder.HasNoKey();
    builder.Property(a => a.Id).IsRequired();
    builder.Property(a => a.AzureError).IsRequired();
    builder.Property(a => a.SourceMessage).IsRequired();
    builder.Property(a => a.CreatedDateUtc).IsRequired();
    builder.Property(a => a.TenantPrincipalId).HasMaxLength(MaxLengthConstants.Guid);
    builder.Property(a => a.Oauth2AccessTokenId).HasMaxLength(MaxLengthConstants.Guid);

    builder.HasIndex(a => a.Id).IsUnique();
    builder.HasIndex(a => a.TenantPrincipalId);

    builder.Property(x => x.AzureError)
      .HasColumnType("jsonb")
      .HasConversion(
        a => a == null ? null : JsonSerializer.Serialize(a, _jsonOpts),
        a => a == null ? null : JsonSerializer.Deserialize<AzureError>(a, _jsonOpts));
  }
}
