using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class ComputerNoteConfiguration : IEntityTypeConfiguration<ComputerNote>
{
  public void Configure(EntityTypeBuilder<ComputerNote> builder)
  {
    builder.HasKey(e => e.Id);
    builder.Property(e => e.ComputerId).IsRequired();

    builder.HasOne(e => e.Computer)
      .WithOne(e => e.ComputerNote)
      .HasForeignKey<ComputerNote>(e => e.ComputerId)
      .OnDelete(DeleteBehavior.Cascade);

    builder.HasOne(e => e.CreatedByUser)
      .WithMany()
      .HasForeignKey(e => e.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);

    builder.HasOne(e => e.UpdatedByUser)
      .WithMany()
      .HasForeign<PERSON>ey(e => e.UpdatedBy)
      .OnDelete(DeleteBehavior.SetNull);
  }
}
