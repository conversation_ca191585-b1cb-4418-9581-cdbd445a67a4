using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Immybot.Backend.Domain.Models;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Immybot.Backend.Persistence.Configurations;

public class ProviderLinkInternalDataConfiguration : IEntityTypeConfiguration<ProviderLinkInternalData>
{
  public void Configure(EntityTypeBuilder<ProviderLinkInternalData> builder)
  {
    builder.HasKey(p => p.ProviderLinkId);
    builder.HasQueryFilter(a => !a.ProviderLink.Disabled && a.ProviderLink.HealthStatus == HealthStatus.Healthy);
  }
}
