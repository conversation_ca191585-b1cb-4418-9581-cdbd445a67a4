using System;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

internal class ImmyMigrationsConfiguration : IEntityTypeConfiguration<ImmyMigration>
{
  public void Configure(EntityTypeBuilder<ImmyMigration> builder)
  {
    builder.Property(a => a.DateRanUtc).HasConversion(
      a => a,
      a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
  }
}
