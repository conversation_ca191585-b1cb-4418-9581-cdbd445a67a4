using System;
using Immybot.Backend.Domain.Models;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class InventoryTaskScriptConfiguration : IEntityTypeConfiguration<InventoryTaskScript>
{
  public void Configure(EntityTypeBuilder<InventoryTaskScript> builder)
  {
    builder.HasKey(a => new { a.Inventory<PERSON><PERSON>, a.InventoryTaskId });
    builder.Property(a => a.InventoryKey).IsRequired();
    builder.Property(s => s.SaveInformationStream).HasDefaultValue(false);
    builder.Property(s => s.SaveWarningStream).HasDefaultValue(false);
    builder.Property(s => s.SaveDebugStream).HasDefaultValue(false);
    builder.Property(s => s.SaveVerboseStream).HasDefaultValue(false);
    builder.Property(a => a.InventoryKey).IsRequired();

    builder
      .HasOne(a => a.Script)
      .WithOne(b => b.InventoryTaskScript)
      .HasForeignKey<InventoryTaskScript>(a => a.ScriptId);

    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Ignore(a => a.FromProvider);
  }
}
