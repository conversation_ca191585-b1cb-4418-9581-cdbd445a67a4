using System;
using Immybot.Backend.Domain.Models;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

internal class NotificationConfiguration : IEntityTypeConfiguration<Notification>
{
  public void Configure(EntityTypeBuilder<Notification> builder)
  {
    // guid for primary key
    builder.HasKey(x => x.Id);

    builder.Property(x => x.Acknowledgement)
      .HasDefaultValue(NotificationAcknowledgement.Unacknowledged);

    builder.Property(x => x.Severity)
      .HasDefaultValue(NotificationSeverity.Info);

    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    // use-case: some notification types can only exist once
    builder.HasIndex(a => a.Type);
    // use-case: some notification types can only exist once per object
    builder.HasIndex(a => new { a.Type, a.ObjectId });
    // use-case: show me all unacknowledged notifications for a particular tenant
    builder.HasIndex(a => new { a.TenantId, a.Acknowledgement });
    // use-case: show me all notifications for a particular tenant
    builder.HasIndex(a => a.TenantId);
    // use-case: show all notifications at a specific tenant including only for that user and triggered by that user
    builder.HasIndex(a => new { a.OnlyForUserId, a.TenantId, a.TriggeredByUserId});
    // use-case: all queries will at minimum include this filter
    builder.HasIndex(a => a.OnlyForUserId);
  }
}
