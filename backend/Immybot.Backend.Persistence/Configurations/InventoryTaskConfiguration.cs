using System;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class InventoryTaskConfiguration : IEntityTypeConfiguration<InventoryTask>
{
  public void Configure(EntityTypeBuilder<InventoryTask> builder)
  {
    builder.<PERSON><PERSON><PERSON>(a => a.Id);

    builder
      .HasMany(a => a.Scripts)
      .WithOne(b => b.InventoryTask)
      .OnDelete(DeleteBehavior.Cascade);

    builder.Property(a => a.Name).HasMaxLength(MaxLengthConstants.SmallText).IsRequired();

    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Ignore(a => a.FromProvider);
    builder.Property(a => a.InventoryTaskType).HasDefaultValue(DatabaseType.Local);
  }
}
