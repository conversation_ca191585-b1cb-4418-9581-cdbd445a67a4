using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class MaintenanceSessionConfiguration : IEntityTypeConfiguration<MaintenanceSession>
{
  public void Configure(EntityTypeBuilder<MaintenanceSession> builder)
  {
    builder
     .Property(a => a.JobArgs)
     .HasColumnType("jsonb")
     .HasConversion(a => SessionJobArgs.Serialize(a), a => SessionJobArgs.Deserialize(a))
     .IsRequired();

    builder.HasMany(a => a.Stages).WithOne(b => b.MaintenanceSession).OnDelete(DeleteBehavior.Cascade);

    builder
     .HasOne(a => a.CreatedByUser)
     .WithMany(a => a.CreatedMaintenanceSessions)
     .HasForeignKey(a => a.CreatedBy)
     .OnDelete(DeleteBehavior.SetNull);

    builder
      .HasOne(a => a.ActiveSession)
      .WithOne(b => b.MaintenanceSession);

    builder.HasMany(a => a.MaintenanceActions).WithOne(b => b.MaintenanceSession).OnDelete(DeleteBehavior.Cascade);

    builder.Property(a => a.JobId).HasMaxLength(MaxLengthConstants.Guid);

    builder.HasIndex(a => a.JobId);
    builder.HasIndex(a => a.SessionStatus);
    builder.HasIndex(a => a.CreatedDate);
    builder.Property(a => a.FullMaintenance).HasDefaultValue(false);
  }
}
