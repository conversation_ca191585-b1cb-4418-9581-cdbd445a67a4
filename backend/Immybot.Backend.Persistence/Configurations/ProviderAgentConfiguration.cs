using System;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;

using Immybot.Shared.DataContracts.Converters.SemanticVersioning;
using Immybot.Shared.JsonConverters;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class ProviderAgentConfiguration : IEntityTypeConfiguration<ProviderAgent>
{
  public void Configure(EntityTypeBuilder<ProviderAgent> builder)
  {
    builder.HasKey(r => r.Id);

    builder.Property(a => a.ProviderLinkId)
      .IsRequired();

    builder.Property(a => a.ExternalClientId)
      .HasMaxLength(100)
      .IsRequired();

    builder.Property(a => a.ExternalAgentId)
      .HasMaxLength(100)
      .IsRequired();

    builder.Property(a => a.LastUpdatedUTC)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder
      .Property(v => v.AgentVersion)
      .HasConversion(
        v => v.ToNormalizedString(),
        v => SemanticVersionParser.ParseVersionString(v));

    builder
      .Property(p => p.AgentVersion)
      .HasMaxLength(MaxLengthConstants.SemanticVersion);

    builder.HasIndex(a => new
    {
      a.ProviderLinkId,
      a.ExternalClientId,
      a.ExternalAgentId,
    }).IsUnique();

    builder.HasIndex(a => a.ProviderLinkId).IncludeProperties(a => new
    {
      a.IsOnline,
      a.LastUpdatedUTC,
      a.ExternalClientId,
      a.ExternalAgentId
    });

    builder.Property(b => b.DateAddedUTC)
      .HasConversion(
        b => b,
        b => DateTime.SpecifyKind(b, DateTimeKind.Utc));

    builder.OwnsOne(p => p.DeviceDetails, dBuilder =>
    {
      dBuilder.Property(d => d.OSInstallDateUTC)
        .HasConversion(
          b => b,
          b => b == null ? null : DateTime.SpecifyKind(b.Value, DateTimeKind.Utc));
    });

    builder.OwnsOne(p => p.OnboardingOptions);

    builder.HasQueryFilter(a => a.DeletedAt == null);
    builder.Property(a => a.DeletedAt).HasColumnName("deleted_at");
    // only one non-deleted agent per computer from a given provider
    builder.HasIndex(a => new
    {
      a.ProviderLinkId,
      a.ComputerId,
      a.DeletedAt // Include deleted at in the index to allow multiple deleted agents per computer
    })
      .HasFilter("deleted_at is null")
      .IsUnique();
  }
}
