using System;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Persistence.Configurations;

public class GettingStartedStepConfiguration : IEntityTypeConfiguration<GettingStartedStep>
{
  public void Configure(EntityTypeBuilder<GettingStartedStep> builder)
  {
    builder.HasKey(a => a.Id);
    builder.Property(a => a.Name).IsRequired();
    builder.Property(a => a.CompletedAtUtc).IsRequired();
    builder.Property(a => a.CompletedAtUtc)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
  }
}
