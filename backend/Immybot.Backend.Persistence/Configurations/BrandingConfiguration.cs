using Immybot.Backend.Infrastructure.Configuration.Persistence;
using System;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class BrandingConfiguration : IEntityTypeConfiguration<Branding>
{
  public void Configure(EntityTypeBuilder<Branding> builder)
  {
    builder.Property(a => a.BackgroundColor).HasMaxLength(MaxLengthConstants.HexColor);
    builder.Property(a => a.ForegroundColor).HasMaxLength(MaxLengthConstants.HexColor);
    builder.Property(a => a.TableHeaderColor).HasMaxLength(MaxLengthConstants.HexColor);
    builder.Property(a => a.TableHeaderTextColor).HasMaxLength(MaxLengthConstants.HexColor);
    builder.Property(a => a.TextColor).HasMaxLength(MaxLengthConstants.HexColor);
    builder.Property(a => a.FromAddress).HasMaxLength(MaxLengthConstants.EmailAddress);
    builder.Property(a => a.MascotImgUri).HasMaxLength(MaxLengthConstants.Url);
    builder.Property(a => a.MascotName).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.LogoUri).HasMaxLength(MaxLengthConstants.Url);
    builder.Property(a => a.LogoAltText).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.Description).HasMaxLength(MaxLengthConstants.MediumText);
    builder.Property(a => a.Description).IsRequired();
    builder.Property(a => a.FromAddress).HasDefaultValue("<EMAIL>");

    builder
     .HasOne(a => a.UpdatedByUser)
     .WithMany(a => a.UpdatedBrandings)
     .HasForeignKey(a => a.UpdatedBy)
     .OnDelete(DeleteBehavior.SetNull);
    builder
      .HasOne(a => a.CreatedByUser)
      .WithMany(a => a.CreatedBrandings)
      .HasForeignKey(a => a.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);
    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Property(a => a.TextColor).HasDefaultValue("#000000");
    builder.Property(a => a.TableHeaderTextColor).HasDefaultValue("#ffffff");
    builder.Property(a => a.TableHeaderColor).HasDefaultValue("#3d238a");
    builder.Property(a => a.BackgroundColor).HasDefaultValue("#222222");
    builder.Property(a => a.ForegroundColor).HasDefaultValue("#f7f5f2");
  }
}
