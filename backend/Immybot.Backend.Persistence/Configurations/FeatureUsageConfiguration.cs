using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class FeatureUsageConfiguration : IEntityTypeConfiguration<FeatureUsage>
{
  public void Configure(EntityTypeBuilder<FeatureUsage> builder)
  {
    builder.HasKey(c => new { c.FeatureId, c.FeatureTrackStartDateUtc });
    builder.OwnsMany(c => c.Items, iBuilder =>
    {
      iBuilder.ToTable("feature_usage_items");
      iBuilder.WithOwner()
        .HasForeign<PERSON>ey("feature_id", "feature_track_start_date_utc");
      iBuilder.Property(i => i.Value).IsRequired();
      iBuilder.Property(i => i.Count);
      iBuilder.HasKey("feature_id", "feature_track_start_date_utc", "Value");
    });
  }
}
