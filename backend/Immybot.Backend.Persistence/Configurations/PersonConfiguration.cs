using Immybot.Backend.Infrastructure.Configuration.Persistence;
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Persistence.Configurations;

public class PersonConfiguration : IEntityTypeConfiguration<Person>
{
  public void Configure(EntityTypeBuilder<Person> builder)
  {
    builder.HasKey(a => a.Id);
    builder.Property(a => a.EmailAddress).IsRequired();
    builder.HasMany(a => a.PrimaryComputers).WithOne(a => a.PrimaryPerson).OnDelete(DeleteBehavior.SetNull);
    builder.HasOne(p => p.User).WithOne(u => u.Person).OnDelete(DeleteBehavior.ClientSetNull);
    builder.HasMany(a => a.UserAffinities).WithOne(a => a.Person).HasForeignKey(a => a.PersonId).HasPrincipalKey(a => a.Id).OnDelete(DeleteBehavior.Cascade);
    builder
      .HasMany(c => c.PersonSessions)
      .WithOne(s => s.Person)
      .HasForeignKey(s => s.PersonId)
      .HasPrincipalKey(c => c.Id)
      .OnDelete(DeleteBehavior.Cascade);

    builder.Property(a => a.FirstName).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.OnPremisesSecurityIdentifier).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.LastName).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.EmailAddress).HasMaxLength(MaxLengthConstants.EmailAddress);
    builder.Property(a => a.AzurePrincipalId).HasMaxLength(MaxLengthConstants.Guid);
    builder
      .HasOne(a => a.UpdatedByUser)
      .WithMany(a => a.UpdatedPersons)
      .HasForeignKey(a => a.UpdatedBy)
      .OnDelete(DeleteBehavior.SetNull);
    builder
      .HasOne(a => a.CreatedByUser)
      .WithMany(a => a.CreatedPersons)
      .HasForeignKey(a => a.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);
    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.HasMany(c => c.Tags)
      .WithMany(t => t.Persons)
      .UsingEntity<PersonTag>(
        j => j.HasOne(ct => ct.Tag)
              .WithMany(c => c.PersonTags)
              .HasForeignKey(ct => ct.TagId),
        j => j.HasOne(ct => ct.Person)
              .WithMany(c => c.PersonTags)
              .HasForeignKey(ct => ct.EntityId),
        j =>
        {
          j.HasKey(t => new { t.EntityId, t.TagId });
        });

    builder.HasIndex(a => a.AzurePrincipalId);
  }
}
