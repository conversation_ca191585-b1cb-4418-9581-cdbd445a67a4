using System;
using Immybot.Backend.Domain.Models;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;
public class ComputerConfiguration : IEntityTypeConfiguration<Computer>
{
  public void Configure(EntityTypeBuilder<Computer> builder)
  {
    builder.HasKey(c => c.Id);
    builder.Property(c => c.TenantId).IsRequired();
    builder.Property(c => c.DeviceId).IsRequired();
    builder.HasIndex(c => c.DeviceId).IsUnique();
    builder.HasIndex(c => c.CreatedDate);
    builder.HasIndex(c => c.OnboardingStatus);
    builder.HasIndex(c => c.ComputerName);

    builder.HasOne(a => a.SuccessorComputer)
      .WithMany(a => a.PredecessorComputers)
      .HasForeignKey(a => a.SuccessorComputerId)
      .OnDelete(DeleteBehavior.SetNull);

    builder
      .HasMany(c => c.Sessions)
      .WithOne(s => s.Computer)
      .HasForeignKey(s => s.ComputerId)
      .HasPrincipalKey(c => c.Id)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasMany(c => c.Agents)
      .WithOne(r => r.Computer)
      .HasForeignKey(r => r.ComputerId)
      .HasPrincipalKey(c => c.Id)
      .OnDelete(DeleteBehavior.Cascade);

    builder.HasMany(c => c.Tags)
      .WithMany(t => t.Computers)
      .UsingEntity<ComputerTag>(
        j => j.HasOne(ct => ct.Tag)
              .WithMany(c => c.ComputerTags)
              .HasForeignKey(ct => ct.TagId),
        j => j.HasOne(ct => ct.Computer)
              .WithMany(c => c.ComputerTags)
              .HasForeignKey(ct => ct.EntityId),
        j =>
        {
          j.HasKey(t => new { t.EntityId, t.TagId });
        });

    builder
      .HasMany(c => c.DetectedSoftware)
      .WithOne(s => s.Computer)
      .HasForeignKey(s => s.ComputerId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasMany(c => c.RemoteControlRecordings)
      .WithOne(r => r.Computer)
      .HasForeignKey(r => r.ComputerId)
      .IsRequired()
      .OnDelete(DeleteBehavior.Cascade);

    builder.Property(a => a.InventoryStartedDate)
      .HasConversion(
        a => a,
        a => a == null ? null : DateTime.SpecifyKind(a.Value, DateTimeKind.Utc));

    builder.Property(a => a.OnboardedDateUtc)
      .HasConversion(
        a => a,
        a => a == null ? null : DateTime.SpecifyKind(a.Value, DateTimeKind.Utc));

    builder.Property(d => d.DevLabVmClaimExpirationDateUtc)
      .HasConversion(
        b => b,
        b => b == null ? null : DateTime.SpecifyKind(b.Value, DateTimeKind.Utc));

    builder.Property(d => d.IsSandbox).HasDefaultValue(false);
    builder.Property(d => d.ExcludeFromMaintenance).HasDefaultValue(false);
    builder.Property(d => d.DetectionOutdated).HasDefaultValue(false);

    builder.HasOne(c => c.ComputerNote)
      .WithOne(n => n.Computer)
      .HasForeignKey<ComputerNote>(n => n.ComputerId)
      .OnDelete(DeleteBehavior.Cascade);

    builder.HasQueryFilter(a => a.DeletedAt == null);

  }
}
