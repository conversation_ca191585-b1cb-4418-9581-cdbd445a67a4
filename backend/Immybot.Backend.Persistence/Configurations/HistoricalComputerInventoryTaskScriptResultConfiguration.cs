using System;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class HistoricalComputerInventoryTaskScriptResultConfiguration
  : IEntityTypeConfiguration<HistoricalComputerInventoryTaskScriptResult>
{
  public void Configure(EntityTypeBuilder<HistoricalComputerInventoryTaskScriptResult> builder)
  {
    builder.HasNoKey();
    builder.HasIndex(a => a.ComputerId);
    builder.Property(a => a.Timestamp)
      .HasConversion(a => a, a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.HasIndex(i => i.InventoryKey);
  }
}
