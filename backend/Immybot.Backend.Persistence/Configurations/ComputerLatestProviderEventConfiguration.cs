using System;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class ComputerLatestProviderEventConfiguration : IEntityTypeConfiguration<ComputerLatestProviderEvent>
{
  public void Configure(EntityTypeBuilder<ComputerLatestProviderEvent> builder)
  {
    builder.<PERSON><PERSON><PERSON>(e => e.ComputerId);

    builder.HasOne(c => c.Computer)
      .WithOne(e => e.LatestProviderEvent)
      .HasForeignKey((ComputerLatestProviderEvent c) => c.ComputerId)
      .OnDelete(DeleteBehavior.Cascade);

    builder.Property(d => d.LastProviderAgentEventDateUtc)
      .IsRequired()
      .HasConversion(
        b => b,
        b => DateTime.SpecifyKind(b, DateTimeKind.Utc));

    builder.HasQueryFilter(a => a.Computer.DeletedAt == null);
  }
}
