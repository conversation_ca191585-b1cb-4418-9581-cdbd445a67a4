using System;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class MediaConfiguration : IEntityTypeConfiguration<Media>
{
  public void Configure(EntityTypeBuilder<Media> builder)
  {
    builder.HasKey(s => s.Id);
    builder.Property(a => a.Name).IsRequired();
    builder.Property(a => a.FileName).IsRequired();
    builder.Property(a => a.BlobReference).IsRequired();
    builder.HasIndex(a => a.Name).IsUnique(false);
    builder.Property(a => a.Name).HasMaxLength(MaxLengthConstants.MediumText);
    builder.Property(a => a.FileName).HasMaxLength(MaxLengthConstants.MediumText);
    builder.Property(a => a.DatabaseType).HasDefaultValue(DatabaseType.Local);
    builder.Property(a => a.Category).HasDefaultValue(MediaCategory.None);

    builder.Ignore(a => a.GlobalSoftware);

    builder
     .HasOne(a => a.UpdatedByUser)
     .WithMany(a => a.UpdatedMedia)
     .HasForeignKey(a => a.UpdatedBy)
     .OnDelete(DeleteBehavior.SetNull);

    builder
      .HasOne(a => a.CreatedByUser)
      .WithMany(a => a.CreatedMedia)
      .HasForeignKey(a => a.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);

    builder.Property(a => a.UpdatedDate)
     .HasConversion(
       a => a,
       a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.Name).HasDefaultValue("");
  }
}
