using System;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class RecommendedTargetAssignmentApprovalConfiguration : IEntityTypeConfiguration<RecommendedTargetAssignmentApproval>
{
  public void Configure(EntityTypeBuilder<RecommendedTargetAssignmentApproval> builder)
  {
    builder.<PERSON><PERSON><PERSON>(a => a.GlobalTargetAssignmentId);

    builder
     .HasOne(a => a.UpdatedByUser)
     .WithMany(a => a.UpdatedRecommendedTargetAssignmentApprovals)
     .HasForeignKey(a => a.UpdatedBy)
     .OnDelete(DeleteBehavior.SetNull);
    builder
      .HasOne(a => a.CreatedByUser)
      .WithMany(a => a.CreatedRecommendedTargetAssignmentApprovals)
      .HasForeignKey(a => a.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);
    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
  }
}
