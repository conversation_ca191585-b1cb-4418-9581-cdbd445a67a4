using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;
internal class DetectedComputerSoftwareConfiguration : IEntityTypeConfiguration<DetectedComputerSoftware>
{
  public void Configure(EntityTypeBuilder<DetectedComputerSoftware> builder)
  {
    builder.HasKey(x => x.Id);

    builder
      .Property(x => x.DisplayName)
      .HasMaxLength(MaxLengthConstants.MediumText)
      .IsRequired();

    builder
      .HasOne(s => s.Computer)
      .WithMany(c => c.DetectedSoftware)
      .HasPrincipalKey(c => c.Id)
      .HasForeignKey(s => s.ComputerId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasOne(s => s.Tenant)
      .WithMany(t => t.DetectedComputerSoftware)
      .HasPrincipalKey(t => t.Id)
      .HasForeignKey(s => s.TenantId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasOne(s => s.PrimaryPerson)
      .WithMany(p => p.DetectedComputerSoftware)
      .HasPrincipalKey(p => p.Id)
      .HasForeignKey(s => s.PrimaryPersonId)
      .IsRequired(false)
      .OnDelete(DeleteBehavior.SetNull);

    builder
      .Property(x => x.DisplayIcon)
      .HasMaxLength(MaxLengthConstants.MediumText);

    builder
      .Property(x => x.DisplayVersion)
      .HasMaxLength(MaxLengthConstants.SmallText);

    builder
      .Property(x => x.GlobalSoftwareName)
      .HasMaxLength(MaxLengthConstants.MediumText);

    builder
      .Property(x => x.Platform)
      .HasMaxLength(MaxLengthConstants.SmallText);

    builder
      .Property(x => x.QuietUninstallString)
      .HasMaxLength(MaxLengthConstants.LargeText);

    builder
      .Property(x => x.UninstallString)
      .HasMaxLength(MaxLengthConstants.LargeText);

    builder
      .Property(x => x.UserName)
      .HasMaxLength(MaxLengthConstants.SmallText);

    builder
      .Property(x => x.UserSid)
      .HasMaxLength(MaxLengthConstants.SmallText);

    builder
      .Property(x => x.RegistryPath)
      .HasMaxLength(MaxLengthConstants.MediumText);

    builder
      .Property(x => x.InstallLocation)
      .HasMaxLength(MaxLengthConstants.MediumText);

    builder.HasIndex(x => x.ComputerId);
    builder.HasIndex(x => x.TenantId);
    builder.HasIndex(x => x.PrimaryPersonId);
    builder.HasIndex(x => x.DisplayName);
    builder.HasIndex(x => x.GlobalSoftwareName);
    builder.HasIndex(x => x.UpgradeCode);

    builder.HasQueryFilter(a => a.Computer.DeletedAt == null);
  }
}
