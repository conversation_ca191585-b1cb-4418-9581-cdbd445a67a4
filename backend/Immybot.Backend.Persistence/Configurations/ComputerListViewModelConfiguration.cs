using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class ComputerListViewModelConfiguration : IEntityTypeConfiguration<ComputerListViewModel>
{
  public void Configure(EntityTypeBuilder<ComputerListViewModel> builder)
  {
    builder.HasNoKey().ToTable("ComputerListViewModel", t => t.ExcludeFromMigrations());
  }
}

public class ComputerListViewModelCountConfiguration : IEntityTypeConfiguration<ComputerListViewModelCount>
{
  public void Configure(EntityTypeBuilder<ComputerListViewModelCount> builder)
  {
    builder.HasNoKey().ToTable("ComputerListViewModelCount", t => t.ExcludeFromMigrations());
  }
}
