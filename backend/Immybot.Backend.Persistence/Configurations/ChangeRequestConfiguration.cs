using System;
using Immybot.Backend.Domain.Models;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

internal class ChangeRequestConfiguration : IEntityTypeConfiguration<ChangeRequest>
{
  public void Configure(EntityTypeBuilder<ChangeRequest> builder)
  {
    builder.Has<PERSON>ey(a => a.Id);

    builder.HasIndex(a => a.ObjectType);

    builder.Property(a => a.AcknowledgedByUserName).IsRequired(false);

    builder.Property(a => a.State)
      .HasDefaultValue(ChangeRequestState.WaitingForApproval);

    builder
      .HasOne(a => a.UpdatedByUser)
      .WithMany(a => a.UpdatedChangeRequests)
      .HasForeignKey(a => a.UpdatedBy)
      .OnDelete(DeleteBehavior.SetNull);

    builder
      .HasOne(a => a.CreatedByUser)
      .WithMany(a => a.CreatedChangeRequests)
      .HasForeignKey(a => a.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);

    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder
      .HasOne(a => a.TargetAssignment)
      .WithMany(a => a.ChangeRequests)
      .HasForeignKey(a => a.TargetAssignmentId)
      .OnDelete(DeleteBehavior.Cascade);

    builder.HasOne(a => a.Script)
      .WithMany(a => a.ChangeRequests)
      .HasForeignKey(a => a.ScriptId)
      .OnDelete(DeleteBehavior.Cascade);

    builder.HasMany(a => a.Comments)
      .WithOne(a => a.ChangeRequest)
      .OnDelete(DeleteBehavior.Cascade);
  }
}
