using Immybot.Backend.Domain.Models;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

internal class MaintenanceItemOrderConfiguration : IEntityTypeConfiguration<MaintenanceItemOrder>
{
  public void Configure(EntityTypeBuilder<MaintenanceItemOrder> builder)
  {
    builder
      .HasIndex(a => new { a.MaintenanceIdentifier, a.MaintenanceType })
      .IsUnique();
    builder
      .HasIndex(a => new { a.Location, a.SortOrder })
      .IsUnique()
      .HasFilter("location != 2");
    builder.Property(a => a.Location).HasDefaultValue(MaintenanceItemOrderLocation.DontCare);

  }
}
