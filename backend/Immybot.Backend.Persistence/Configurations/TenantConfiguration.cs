using System;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class TenantConfiguration : IEntityTypeConfiguration<Tenant>
{
  public void Configure(EntityTypeBuilder<Tenant> builder)
  {
    builder.HasKey(a => a.Id);
    builder.Property(a => a.Id).IsRequired();
    builder.Property(a => a.Name).IsRequired();
    builder
      .HasMany(t => t.OwnedTenants)
      .WithOne(t => t.OwnerTenant)
      .HasPrincipalKey(t => t.Id)
      .HasForeignKey(t => t.OwnerTenantId);

    builder
      .HasMany(t => t.OwnedProviderLinks)
      .WithOne(c => c.OwnerTenant)
      .HasPrincipalKey(t => t.Id)
      .HasForeignKey(l => l.OwnerTenantId)
      .OnDelete(DeleteBehavior.Restrict);

    builder
      .HasMany(t => t.TenantSoftware)
      .WithOne(s => s.Tenant)
      .HasPrincipalKey(t => t.Id)
      .HasForeignKey(s => s.TenantId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasOne(t => t.TenantPreferences)
      .WithOne(t => t.Tenant)
      .HasPrincipalKey<Tenant>(t => t.Id)
      .HasForeignKey<TenantPreferences>(p => p.TenantId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasMany(t => t.Users)
      .WithOne(u => u.Tenant)
      .HasPrincipalKey(t => t.Id)
      .HasForeignKey(u => u.TenantId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasMany(t => t.Persons)
      .WithOne(p => p.Tenant)
      .HasPrincipalKey(t => t.Id)
      .HasForeignKey(p => p.TenantId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasMany(t => t.Brandings)
      .WithOne(b => b.Tenant)
      .HasPrincipalKey(t => t.Id)
      .HasForeignKey(b => b.TenantId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasMany(t => t.Computers)
      .WithOne(c => c.Tenant)
      .HasPrincipalKey(t => t.Id)
      .HasForeignKey(c => c.TenantId)
      .OnDelete(DeleteBehavior.Cascade);

    builder.HasOne(a => a.SmtpConfig).WithOne(a => a.Tenant);

    builder
      .HasMany(t => t.Scripts)
      .WithOne(s => s.Tenant)
      .HasPrincipalKey(t => t.Id)
      .HasForeignKey(s => s.TenantId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasMany(t => t.Media)
      .WithOne(s => s.Tenant)
      .HasPrincipalKey(t => t.Id)
      .HasForeignKey(s => s.TenantId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasMany(t => t.MaintenanceTasks)
      .WithOne(m => m.Tenant)
      .HasPrincipalKey(t => t.Id)
      .HasForeignKey(m => m.TenantId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasMany(t => t.ProviderClients)
      .WithOne(c => c.LinkedToTenant)
      .HasForeignKey(c => c.LinkedToTenantId)
      .HasPrincipalKey(t => t.Id)
      .OnDelete(DeleteBehavior.SetNull);

    builder
      .HasMany(t => t.OwnedProviderLinks)
      .WithOne(l => l.OwnerTenant)
      .HasForeignKey(l => l.OwnerTenantId)
      .HasPrincipalKey(t => t.Id)
      .OnDelete(DeleteBehavior.Restrict);

    builder
      .HasMany(t => t.Licenses)
      .WithOne(l => l.Tenant)
      .HasForeignKey(l => l.TenantId)
      .OnDelete(DeleteBehavior.SetNull);

    builder
      .HasMany(t => t.MaintenanceSessions)
      .WithOne(m => m.Tenant)
      .HasForeignKey(m => m.TenantId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
      .HasMany(t => t.Schedules)
      .WithOne(m => m.Tenant)
      .HasForeignKey(m => m.TenantId)
      .OnDelete(DeleteBehavior.Cascade);

    builder.HasMany(t => t.Tags)
      .WithMany(t => t.Tenants)
      .UsingEntity(e => e.ToTable("tenant_tags"));

    builder
     .HasMany(t => t.TenantTagAuthorizations)
     .WithOne(s => s.Tenant)
     .HasPrincipalKey(t => t.Id)
     .HasForeignKey(s => s.TenantId)
     .OnDelete(DeleteBehavior.Cascade);

    builder.HasMany(c => c.Tags)
      .WithMany(t => t.Tenants)
      .UsingEntity<TenantTag>(
        j => j.HasOne(ct => ct.Tag)
              .WithMany(c => c.TenantTags)
              .HasForeignKey(ct => ct.TagId),
        j => j.HasOne(ct => ct.Tenant)
              .WithMany(c => c.TenantTags)
              .HasForeignKey(ct => ct.EntityId),
        j =>
        {
          j.HasKey(t => new { t.EntityId, t.TagId });
        });

    builder.Property(a => a.Name).HasMaxLength(MaxLengthConstants.SmallText);

    builder.HasOne(a => a.AzureTenantLink)
      .WithOne(b => b.Tenant)
      .HasForeignKey<AzureTenantLink>(a => a.ImmyTenantId)
      .OnDelete(DeleteBehavior.Cascade);



    builder.Property(a => a.MarkedForDeletionAtUtc)
      .HasConversion(
        a => a,
        a => a.HasValue ? DateTime.SpecifyKind(a.Value, DateTimeKind.Utc) : null);
  }
}
