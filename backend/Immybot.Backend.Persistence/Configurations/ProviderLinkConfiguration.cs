using System;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Immybot.Backend.Persistence.Configurations;

public class ProviderLinkConfiguration : IEntityTypeConfiguration<ProviderLink>
{
  public void Configure(EntityTypeBuilder<ProviderLink> builder)
  {
    builder.Property(a => a.OwnerTenantId)
      .IsRequired();

    builder.Property(a => a.ProviderTypeId)
      .IsRequired();

    builder.Property(a => a.Name)
      .IsRequired();
    builder.HasIndex(a => a.Name)
      .IsUnique();

    builder.HasMany(l => l.Schedules)
      .WithOne(s => s.ProviderLink)
      .HasForeignKey(s => s.ProviderLinkId)
      .HasPrincipalKey(l => l.Id)
      .OnDelete(DeleteBehavior.Cascade);

    builder.HasMany(l => l.TargetAssignments)
      .WithOne(t => t.ProviderLink)
      .HasForeignKey(t => t.ProviderLinkId)
      .HasPrincipalKey(l => l.Id)
      .OnDelete(DeleteBehavior.Cascade);

    builder.HasMany(a => a.Agents)
      .WithOne(a => a.ProviderLink)
      .HasForeignKey(a => a.ProviderLinkId)
      .HasPrincipalKey(l => l.Id)
      .OnDelete(DeleteBehavior.Restrict);

    builder
      .HasMany(l => l.ProviderClients)
      .WithOne(c => c.ProviderLink)
      .HasForeignKey(c => c.ProviderLinkId)
      .HasPrincipalKey(l => l.Id)
      .OnDelete(DeleteBehavior.Restrict);

    builder
      .HasMany(l => l.ProvidersLinkedFromThisProvider)
      .WithOne(s => s.ProviderLink1)
      .HasForeignKey(s => s.ProviderLink1Id)
      .HasPrincipalKey(l => l.Id)
      .OnDelete(DeleteBehavior.Cascade);

    builder
     .HasOne(a => a.UpdatedByUser)
     .WithMany(a => a.UpdatedProviderLinks)
     .HasForeignKey(a => a.UpdatedBy)
     .OnDelete(DeleteBehavior.SetNull);
    builder
      .HasOne(a => a.CreatedByUser)
      .WithMany(a => a.CreatedProviderLinks)
      .HasForeignKey(a => a.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);

    builder
      .HasOne(a => a.ProviderInternalData)
      .WithOne(a => a.ProviderLink)
      .HasForeignKey<ProviderLinkInternalData>(a => a.ProviderLinkId)
      .HasPrincipalKey<ProviderLink>(a => a.Id);

    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Property(a => a.HealthStatus).HasDefaultValue(HealthStatus.Healthy);

    // default to only return enabled providers
    builder.HasQueryFilter(a => !a.Disabled && a.HealthStatus == HealthStatus.Healthy);
  }
}
