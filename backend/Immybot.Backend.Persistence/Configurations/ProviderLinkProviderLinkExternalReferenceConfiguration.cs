using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Immybot.Backend.Persistence.Configurations;

public class ProviderLinkProviderLinkExternalReferenceConfiguration
  : IEntityTypeConfiguration<ProviderLinkCrossReference>
{
  public void Configure(EntityTypeBuilder<ProviderLinkCrossReference> builder)
  {
    builder.HasKey(t => new { t.ProviderLink1Id, t.ProviderLink2Id });

    builder.HasQueryFilter(a => !a.ProviderLink1.Disabled && a.ProviderLink1.HealthStatus == HealthStatus.Healthy);
  }
}
