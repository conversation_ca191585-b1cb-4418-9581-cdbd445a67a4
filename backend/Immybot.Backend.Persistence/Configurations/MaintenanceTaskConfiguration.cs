using Immybot.Backend.Infrastructure.Configuration.Persistence;
using System;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class MaintenanceTaskConfiguration : IEntityTypeConfiguration<MaintenanceTask>
{
  public void Configure(EntityTypeBuilder<MaintenanceTask> builder)
  {
    builder.HasKey(a => a.Id);
    builder.Property(a => a.DatabaseType).HasDefaultValue(DatabaseType.Local);
    builder.Property(a => a.Name).IsRequired().HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.Notes).HasMaxLength(MaxLengthConstants.LargeText);
    builder.Ignore(a => a.SetScript);
    builder.Ignore(a => a.GetScript);
    builder.Ignore(a => a.TestScript);

    builder
      .HasMany(a => a.Parameters)
      .WithOne(a => a.MaintenanceTask);

    builder
      .HasMany(m => m.TenantRelationships)
      .WithOne(t => t.MaintenanceTask)
      .HasPrincipalKey(m => m.Id)
      .HasForeignKey(m => m.MaintenanceTaskId)
      .OnDelete(DeleteBehavior.Cascade);

    builder
     .HasOne(a => a.UpdatedByUser)
     .WithMany(a => a.UpdatedMaintenanceTasks)
     .HasForeignKey(a => a.UpdatedBy)
     .OnDelete(DeleteBehavior.SetNull);
    builder
      .HasOne(a => a.CreatedByUser)
      .WithMany(a => a.CreatedMaintenanceTasks)
      .HasForeignKey(a => a.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);
    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder
      .HasOne(a => a.Icon)
      .WithMany(a => a.IconForMaintenanceTasks)
      .HasForeignKey(a => a.IconMediaId)
      .OnDelete(DeleteBehavior.SetNull);
    builder.Property(a => a.MaintenanceTaskCategory).HasDefaultValue(MaintenanceTaskCategory.Computer);
    builder.Property(a => a.Recommended).HasDefaultValue(false);
    builder.Property(a => a.IsConfigurationTask).HasDefaultValue(false);
    builder.Property(a => a.ExecuteSerially).HasDefaultValue(false);
    builder.Property(a => a.OnboardingOnly).HasDefaultValue(false);
  }
}
