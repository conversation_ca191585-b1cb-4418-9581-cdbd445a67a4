using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class MaintenanceTaskParameterConfiguration : IEntityTypeConfiguration<MaintenanceTaskParameter>
{
  public void Configure(EntityTypeBuilder<MaintenanceTaskParameter> builder)
  {
    builder.Property(a => a.Name).IsRequired().HasMaxLength(255);
    builder.Property(a => a.DataType).IsRequired();
    builder.Property(a => a.Required).HasDefaultValue(false);

    builder.HasOne(a => a.MaintenanceTask).WithMany(a => a.Parameters);
    builder.Property(a => a.Notes).HasMaxLength(MaxLengthConstants.MediumText);

    builder.Ignore(a => a.DefaultMedia);
    builder.Property(a => a.Hidden).HasDefaultValue(false);
    builder.Property(a => a.Order).HasDefaultValue(0);
  }
}
