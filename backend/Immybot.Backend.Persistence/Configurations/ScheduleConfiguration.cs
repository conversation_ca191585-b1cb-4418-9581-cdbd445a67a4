using Immybot.Backend.Infrastructure.Configuration.Persistence;
using System;
using Immybot.Backend.Domain.Models;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

class ScheduleConfiguration : IEntityTypeConfiguration<Schedule>
{
  public void Configure(EntityTypeBuilder<Schedule> builder)
  {
    builder.HasKey(a => a.Id);
    builder.Property(a => a.Time).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.CustomCronExpression).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.MaintenanceTime).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.Target).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.TargetName).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.SendDetectionEmailWhenAllActionsAreCompliant).HasDefaultValue(false);

    builder
     .HasOne(a => a.UpdatedByUser)
     .WithMany(a => a.UpdatedSchedules)
     .HasForeignKey(a => a.UpdatedBy)
     .OnDelete(DeleteBehavior.SetNull);
    builder
      .HasOne(a => a.CreatedByUser)
      .WithMany(a => a.CreatedSchedules)
      .HasForeignKey(a => a.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);

    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Property(a => a.ApplyWindowsUpdates).HasDefaultValue(false);
    builder.Property(a => a.TargetCategory).HasDefaultValue(TargetCategory.Computer);
    builder.Property(a => a.OfflineBehavior).HasDefaultValue(ComputerOfflineMaintenanceSessionBehavior.Skip);
    builder.Property(a => a.ShowPostponeButton).HasDefaultValue(false);
    builder.Property(a => a.ShowRunNowButton).HasDefaultValue(false);
    builder.Property(a => a.ShowMaintenanceActions).HasDefaultValue(false);
    builder.Property(a => a.AllowAccessToMSPResources).HasDefaultValue(false);
    builder.Property(a => a.AllowAccessToParentTenant).HasDefaultValue(false);
    builder.Property(a => a.SuppressRebootsDuringBusinessHours).HasDefaultValue(false);
  }
}
