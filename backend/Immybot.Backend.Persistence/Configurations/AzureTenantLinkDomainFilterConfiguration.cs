using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class AzureTenantLinkDomainFilterConfiguration : IEntityTypeConfiguration<AzureTenantLinkDomainFilter>
{
  public void Configure(EntityTypeBuilder<AzureTenantLinkDomainFilter> builder)
  {
    builder.ToTable("azure_tenant_link_domain_filters");
    builder.HasKey(a => new { a.ImmyTenantId, a.DomainName });
    // Unique constraint on PrincipalId+LimitToDomains
    builder.HasIndex(a => new { a.AzTenantId, a.DomainName }).IsUnique();
  }
}
