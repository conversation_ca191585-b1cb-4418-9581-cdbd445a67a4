using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class ComputerInventoryKeyConfiguration : IEntityTypeConfiguration<ComputerInventoryKeyViewModel>
{
  public void Configure(EntityTypeBuilder<ComputerInventoryKeyViewModel> builder)
  {
    builder.HasNoKey().ToTable("ComputerInventoryKeyViewModel", t => t.ExcludeFromMigrations());
  }
}
