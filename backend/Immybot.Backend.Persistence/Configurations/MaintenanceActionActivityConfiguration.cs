using System;
using Immybot.Backend.Domain;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class MaintenanceActionActivityConfiguration : IEntityTypeConfiguration<MaintenanceActionActivity>
{
  public void Configure(EntityTypeBuilder<MaintenanceActionActivity> builder)
  {
    builder.<PERSON><PERSON><PERSON>(a => a.Id);
    builder.HasIndex(a => a.MaintenanceActionId);
    builder.HasIndex(a => new { a.MaintenanceActionId, a.Activity }).IsUnique();
    builder.Property(a => a.Id).IsRequired();
    builder.Property(a => a.ScriptName).IsRequired().HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.CurrentOperation).IsRequired().HasMaxLength(MaxLengthConstants.MediumText);
    builder.Property(a => a.Activity).IsRequired().HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.ActivityId).IsRequired().HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.ParentId).IsRequired().HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.SourceId).IsRequired().HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.Status).IsRequired().HasMaxLength(MaxLengthConstants.MediumText);
    builder.Property(a => a.Activity).IsRequired().HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(a => a.DateUtc)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder
      .HasOne(a => a.MaintenanceAction)
      .WithMany(a => a.Activities);

    builder.HasOne(a => a.MaintenanceSession)
      .WithMany(a => a.Activities);

    builder.Property(a => a.ScriptName).IsRequired(false);
    builder.Property(a => a.CurrentOperation).IsRequired(false);
    builder.Property(a => a.ActivityId).IsRequired(false);
    builder.Property(a => a.ParentId).IsRequired(false);
    builder.Property(a => a.SourceId).IsRequired(false);
    builder.Property(a => a.Status).IsRequired(false);
  }
}
