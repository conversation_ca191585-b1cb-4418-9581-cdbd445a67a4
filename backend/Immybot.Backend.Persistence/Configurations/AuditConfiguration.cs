using System;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;
internal class AuditConfiguration : IEntityTypeConfiguration<Audit>
{
  public void Configure(EntityTypeBuilder<Audit> builder)
  {
    builder.Property(a => a.DateTimeUtc)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.HasIndex(a => a.UserId);
    builder.HasIndex(a => new { a.ObjectType, a.PrimaryKey });
    builder.HasIndex(a => a.DateTimeUtc);
    builder.Property(a => a.ObjectName).HasMaxLength(MaxLengthConstants.SmallText);
  }
}
