using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class DisabledPreflightScriptConfiguration: IEntityTypeConfiguration<DisabledPreflightScript>
{
  public void Configure(EntityTypeBuilder<DisabledPreflightScript> builder)
  {
    builder.HasKey(a => a.Id);
    builder.Property(a => a.ScriptId).IsRequired();
    builder.Property(a => a.DatabaseType).IsRequired();
  }
}
