using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class MaintenanceSessionStageConfiguration : IEntityTypeConfiguration<MaintenanceSessionStage>
{
  public void Configure(EntityTypeBuilder<MaintenanceSessionStage> builder)
  {
    builder.HasKey(s => s.Id);
    builder.Property(s => s.MaintenanceSessionId).IsRequired(true);
    builder.Property(a => a.JobId).HasMaxLength(MaxLengthConstants.Guid);
  }
}
