using System;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

internal class ChangeRequestCommentConfiguration : IEntityTypeConfiguration<ChangeRequestComment>
{
  public void Configure(EntityTypeBuilder<ChangeRequestComment> builder)
  {
    builder.<PERSON><PERSON><PERSON>(a => a.Id);

    builder
      .HasOne(a => a.UpdatedByUser)
      .WithMany(a => a.UpdatedChangeRequestComments)
      .HasForeignKey(a => a.UpdatedBy)
      .OnDelete(DeleteBehavior.SetNull);

    builder
      .HasOne(a => a.CreatedByUser)
      .WithMany(a => a.CreatedChangeRequestComments)
      .HasForeignKey(a => a.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);

    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
  }
}
