using System;
using Immybot.Backend.Domain.Models;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class ComputerInventoryTaskScriptResultConfiguration
  : IEntityTypeConfiguration<ComputerInventoryTaskScriptResult>
{
  public void Configure(EntityTypeBuilder<ComputerInventoryTaskScriptResult> builder)
  {
    builder.Has<PERSON><PERSON>(r => new { r.ComputerId, r.InventoryKey });
    builder.HasOne(a => a.Computer)
      .WithMany(c => c.LatestInventoryScriptResults)
      .HasForeignKey(i => i.ComputerId)
      .HasPrincipalKey(c => c.Id)
      .OnDelete(DeleteBehavior.Cascade);
    builder.Property(a => a.Timestamp)
      .HasConversion(a => a, a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.HasIndex(i => i.InventoryKey);
    builder.HasIndex(i => i.ComputerId);
    builder.HasQueryFilter(a => a.Computer.DeletedAt == null);
  }
}
