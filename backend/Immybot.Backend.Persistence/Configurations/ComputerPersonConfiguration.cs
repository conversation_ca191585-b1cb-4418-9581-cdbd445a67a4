using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class ComputerPersonConfiguration : IEntityTypeConfiguration<ComputerPerson>
{
  public void Configure(EntityTypeBuilder<ComputerPerson> builder)
  {
    builder
      .ToTable("computer_persons");
    builder
      .<PERSON><PERSON><PERSON>(a => new { a.ComputerId, a.PersonId });
    builder
      .HasOne(a => a.Person)
      .WithMany(a => a.AdditionalComputers)
      .HasForeignKey(a => a.PersonId)
      .OnDelete(DeleteBehavior.Restrict);
    builder
      .HasOne(a => a.Computer)
      .WithMany(a => a.AdditionalPersons)
      .HasForeignKey(a => a.ComputerId)
      .OnDelete(DeleteBehavior.Cascade);

    builder.HasQueryFilter(a => a.Computer.DeletedAt == null);
  }
}
