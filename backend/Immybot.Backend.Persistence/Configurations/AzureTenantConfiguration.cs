using System;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class AzureTenantConfiguration : IEntityTypeConfiguration<AzureTenant>
{
  public void Configure(EntityTypeBuilder<AzureTenant> builder)
  {
    builder.ToTable("azure_tenants");
    builder.<PERSON><PERSON>ey(a => a.PrincipalId);
    builder.HasMany(a => a.AzureTenantLinks)
      .WithOne(b => b.AzureTenant)
      .HasForeignKey(b => b.AzTenantId)
      .HasPrincipalKey(a => a.PrincipalId)
      .OnDelete(DeleteBehavior.Cascade); // when az tenant is deleted, delete links

    builder.HasOne(a => a.ParentPartner)
      .WithMany()
      .HasPrincipalKey(a => a.PrincipalId)
      .HasForeignKey(a => a.PartnerPrincipalId)
      .OnDelete(DeleteBehavior.Restrict); // Don't allow deleting a partner if it has customers

    builder.Property(a => a.PrincipalId).HasMaxLength(MaxLengthConstants.Guid);
    builder.Property(a => a.PartnerPrincipalId).HasMaxLength(MaxLengthConstants.Guid);
    builder.ComplexProperty(d => d.ConsentDetails, consentBuilder =>
    {
      consentBuilder.Property(a => a.ConsentDateUtc)
        .HasConversion(
          a => a,
          a => a == null ? null : DateTime.SpecifyKind(a.Value, DateTimeKind.Utc));
    });
    builder.OwnsOne(a => a.InfoSyncedFromAzure, infoSyncBuilder =>
    {
      infoSyncBuilder.PrimitiveCollection(s => s.DomainNames);
    });
    builder.OwnsOne(a => a.LastGetUsersSyncResult, syncResultBuilder =>
    {
      syncResultBuilder.Property(a => a.AttemptDateUtc)
        .HasConversion(
          a => a,
          a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    });
    builder.OwnsOne(a => a.LastGetTenantInfoSyncResult, syncResultBuilder =>
    {
      syncResultBuilder.Property(a => a.AttemptDateUtc)
        .HasConversion(
          a => a,
          a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    });
  }
}
