using Immybot.Backend.Infrastructure.Configuration.Persistence;
using System;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NuGet.Versioning;

namespace Immybot.Backend.Persistence.Configurations;

public class LicenseConfiguration : IEntityTypeConfiguration<License>
{
  public void Configure(EntityTypeBuilder<License> builder)
  {
    builder.HasKey(p => p.Id);
    builder.Property(p => p.Name).IsRequired();
    builder.Property(p => p.SoftwareIdentifier).IsRequired();
    builder.Property(p => p.SoftwareType).IsRequired();
    builder.Property(p => p.LicenseValue).IsRequired();
    builder.Property(p => p.SoftwareName).IsRequired();
    builder.Property(v => v.SemanticVersion).HasConversion(v => v.ToNormalizedString(), v => NuGetVersion.Parse(v));
    builder.Property(p => p.Name).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(p => p.SoftwareIdentifier).HasMaxLength(MaxLengthConstants.SmallText);
    builder.Property(p => p.SemanticVersion).HasMaxLength(MaxLengthConstants.SemanticVersion);
    builder.Property(p => p.SoftwareName).HasMaxLength(MaxLengthConstants.SmallText);

    builder
      .HasMany(a => a.TargetAssignments)
      .WithOne(l => l.License)
      .HasForeignKey(l => l.LicenseId)
      .OnDelete(DeleteBehavior.SetNull);

    builder
     .HasOne(a => a.UpdatedByUser)
     .WithMany(a => a.UpdatedLicenses)
     .HasForeignKey(a => a.UpdatedBy)
     .OnDelete(DeleteBehavior.SetNull);
    builder
      .HasOne(a => a.CreatedByUser)
      .WithMany(a => a.CreatedLicenses)
      .HasForeignKey(a => a.CreatedBy)
      .OnDelete(DeleteBehavior.SetNull);
    builder.Property(a => a.UpdatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.CreatedDate)
      .HasConversion(
        a => a,
        a => DateTime.SpecifyKind(a, DateTimeKind.Utc));
    builder.Property(a => a.RestrictToMajorVersion).HasDefaultValue(false);
  }
}
