using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

internal class GlobalSoftwareMatchesConfiguration : IEntityTypeConfiguration<GlobalSoftwareMatch>
{
  public void Configure(EntityTypeBuilder<GlobalSoftwareMatch> builder)
  {
    builder.HasK<PERSON>(x => x.Id);

    builder.HasIndex(x => x.DisplayName);

    builder
      .Property(x => x.GlobalSoftwareId)
      .IsRequired();

    builder
      .Property(x => x.DisplayName)
      .HasMaxLength(MaxLengthConstants.MediumText)
      .IsRequired();
  }
}
