using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;
internal class RemoteControlRecordingConfiguration : IEntityTypeConfiguration<RemoteControlRecording>
{
  public void Configure(EntityTypeBuilder<RemoteControlRecording> builder)
  {
    builder.HasKey(x => x.Id);
    builder
      .Property(x => x.Id)
      .ValueGeneratedOnAdd();

    builder
      .HasIndex(x => x.BlobName)
      .IsUnique();

    builder
      .Property(x => x.BlobName)
      .IsRequired();

    builder
      .Property(x => x.MimeContentType)
      .IsRequired()
      .HasDefaultValue("application/octet-stream");

    builder.HasQueryFilter(a => a.Computer.DeletedAt == null);
  }
}
