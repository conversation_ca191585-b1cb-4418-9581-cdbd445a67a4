using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

public class AzureTenantLinkConfiguration : IEntityTypeConfiguration<AzureTenantLink>
{
  public void Configure(EntityTypeBuilder<AzureTenantLink> builder)
  {
    builder.ToTable("azure_tenant_links");
    // One-to-one with ImmyBot Tenants
    builder.HasKey(a => a.ImmyTenantId);

    // There's a filter defined on this column, so we should specify the name
    // instead of letting EF infer it
    builder.Property(a => a.ShouldLimitDomains)
      .HasColumnName("should_limit_domains");

    // Only one non-domain-limited link per Azure tenant
    builder.HasIndex(a => new { a.AzTenantId, a.ShouldLimitDomains })
      .HasFilter("should_limit_domains = false")
      .IsUnique();

    builder.HasMany(a => a.LimitToDomains)
      .WithOne()
      .HasForeignKey(a => a.ImmyTenantId)
      .HasPrincipalKey(b => b.ImmyTenantId)
      .OnDelete(DeleteBehavior.Cascade); // when link is deleted, delete domains
  }
}
