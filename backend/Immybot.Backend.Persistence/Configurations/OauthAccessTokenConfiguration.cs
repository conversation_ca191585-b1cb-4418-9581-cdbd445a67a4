using System;
using System.Collections.Immutable;
using System.Linq;
using System.Text.Json;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Immybot.Backend.Persistence.Configurations;

internal class OauthAccessTokenConfiguration : IEntityTypeConfiguration<Oauth2AccessToken>
{
  public void Configure(EntityTypeBuilder<Oauth2AccessToken> builder)
  {
    builder.ToTable("oauth_access_tokens");
    builder.HasKey(a => a.Id);
    builder.Property(a => a.AccessTokenExpiresAtUtc).HasConversion(
      a => a,
      a => DateTime.SpecifyKind(a, DateTimeKind.Utc));

    builder.Property(a => a.RefreshTokenExpiresAtUtc).HasConversion(
      a => a,
      a => a == null ? null : DateTime.SpecifyKind(a.Value, DateTimeKind.Utc));

    builder.OwnsOne(a => a.ConsentData, b =>
    {
      b.Property(bb => bb.ExtraQueryParameters)
        .HasColumnType("jsonb")
        .HasConversion(
          bb => JsonSerializer.Serialize(bb, (JsonSerializerOptions)null),
          bb => JsonSerializer.Deserialize<ImmutableDictionary<string, string>>(bb, (JsonSerializerOptions)null),
          new ValueComparer<ImmutableDictionary<string, string>>(
            (c1, c2) => c1.SequenceEqual(c2),
            c => c.GetHashCode(),

            // ExtraQueryParameters is immutable, so we can just return the original value
            // for the snapshot used for change-tracking
            c => c));
    });
  }
}
