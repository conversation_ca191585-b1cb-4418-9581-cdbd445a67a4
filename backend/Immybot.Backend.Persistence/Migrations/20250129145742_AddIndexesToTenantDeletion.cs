using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Immybot.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddIndexesToTenantDeletion : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "ix_tenant_deletions_created_by",
                table: "tenant_deletions",
                column: "created_by");

            migrationBuilder.CreateIndex(
                name: "ix_tenant_deletions_tenant_id_status",
                table: "tenant_deletions",
                columns: new[] { "tenant_id", "status" });

            migrationBuilder.CreateIndex(
                name: "ix_tenant_deletions_updated_by",
                table: "tenant_deletions",
                column: "updated_by");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_tenant_deletions_created_by",
                table: "tenant_deletions");

            migrationBuilder.DropIndex(
                name: "ix_tenant_deletions_tenant_id_status",
                table: "tenant_deletions");

            migrationBuilder.DropIndex(
                name: "ix_tenant_deletions_updated_by",
                table: "tenant_deletions");
        }
    }
}
