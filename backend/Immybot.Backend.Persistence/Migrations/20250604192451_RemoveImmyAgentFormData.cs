using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Immybot.Backend.Persistence.Migrations
{
  /// <inheritdoc />
  public partial class RemoveImmyAgentFormData : Migration
  {
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
      // wipe out the form data for the immy agent provider because all configuration is pulled from the backend agent options
      migrationBuilder.Sql(@"update provider_links set provider_type_form_data = '{}' where provider_type_id = 'fcde1b0b-38a2-41b3-86d6-e8bd0ad52171';");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
      // not backwards compatible
    }
  }
}
