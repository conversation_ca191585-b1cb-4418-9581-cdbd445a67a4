using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Immybot.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddingAuditInfoToRoles : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "ix_roles_created_by",
                table: "roles",
                column: "created_by");

            migrationBuilder.CreateIndex(
                name: "ix_roles_updated_by",
                table: "roles",
                column: "updated_by");

            migrationBuilder.AddForeignKey(
                name: "fk_roles_users_created_by",
                table: "roles",
                column: "created_by",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_roles_users_updated_by",
                table: "roles",
                column: "updated_by",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_roles_users_created_by",
                table: "roles");

            migrationBuilder.DropForeignKey(
                name: "fk_roles_users_updated_by",
                table: "roles");

            migrationBuilder.DropIndex(
                name: "ix_roles_created_by",
                table: "roles");

            migrationBuilder.DropIndex(
                name: "ix_roles_updated_by",
                table: "roles");
        }
    }
}
