using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Immybot.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddMaintenanceActionActivityIndex : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "ix_maintenance_action_activities_maintenance_action_id",
                table: "maintenance_action_activities",
                column: "maintenance_action_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_maintenance_action_activities_maintenance_action_id",
                table: "maintenance_action_activities");
        }
    }
}
