// <auto-generated />
using System;
using System.Collections.Generic;
using System.Text.Json;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Immybot.Backend.Persistence.Migrations
{
    [DbContext(typeof(ImmybotDbContext))]
    [Migration("20250123212313_AddTenantDeletions")]
    partial class AddTenantDeletions
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Immybot.Backend.Domain.MaintenanceActionActivity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Activity")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("activity");

                    b.Property<string>("ActivityId")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("activity_id");

                    b.Property<bool?>("Completed")
                        .HasColumnType("boolean")
                        .HasColumnName("completed");

                    b.Property<string>("CurrentOperation")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("current_operation");

                    b.Property<DateTime>("DateUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("date_utc");

                    b.Property<int>("MaintenanceActionId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_action_id");

                    b.Property<int>("MaintenanceSessionId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_session_id");

                    b.Property<string>("ParentId")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("parent_id");

                    b.Property<int?>("PercentComplete")
                        .HasColumnType("integer")
                        .HasColumnName("percent_complete");

                    b.Property<string>("ScriptName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("script_name");

                    b.Property<int?>("SecondsRemaining")
                        .HasColumnType("integer")
                        .HasColumnName("seconds_remaining");

                    b.Property<string>("SourceId")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("source_id");

                    b.Property<string>("Status")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("status");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_action_activities");

                    b.HasIndex("MaintenanceSessionId")
                        .HasDatabaseName("ix_maintenance_action_activities_maintenance_session_id");

                    b.HasIndex("MaintenanceActionId", "Activity")
                        .IsUnique()
                        .HasDatabaseName("ix_maintenance_action_activities_maintenance_action_id_activity");

                    b.ToTable("maintenance_action_activities", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AccessRequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("AcknowledgedByUserId")
                        .HasColumnType("integer")
                        .HasColumnName("acknowledged_by_user_id");

                    b.Property<DateTime?>("DateAcknowledgedUTC")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("date_acknowledged_utc");

                    b.Property<DateTime>("DateRequestedUTC")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("date_requested_utc");

                    b.Property<int?>("ExpirationTime")
                        .HasColumnType("integer")
                        .HasColumnName("expiration_time");

                    b.Property<bool>("Granted")
                        .HasColumnType("boolean")
                        .HasColumnName("granted");

                    b.Property<bool?>("IsAdmin")
                        .HasColumnType("boolean")
                        .HasColumnName("is_admin");

                    b.Property<string>("Message")
                        .HasColumnType("text")
                        .HasColumnName("message");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer")
                        .HasColumnName("person_id");

                    b.HasKey("Id")
                        .HasName("pk_access_requests");

                    b.HasIndex("AcknowledgedByUserId")
                        .HasDatabaseName("ix_access_requests_acknowledged_by_user_id");

                    b.HasIndex("PersonId")
                        .HasDatabaseName("ix_access_requests_person_id");

                    b.ToTable("access_requests", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ActiveSession", b =>
                {
                    b.Property<int>("MaintenanceSessionId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_session_id");

                    b.Property<int>("SessionStatus")
                        .HasColumnType("integer")
                        .HasColumnName("session_status");

                    b.HasKey("MaintenanceSessionId")
                        .HasName("pk_active_sessions");

                    b.HasIndex("SessionStatus")
                        .HasDatabaseName("ix_active_sessions_session_status");

                    b.ToTable("active_sessions", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AgentIdentificationFailure", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<DateTime>("CreatedDateUTC")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date_utc");

                    b.Property<int?>("ExistingAgentId")
                        .HasColumnType("integer")
                        .HasColumnName("existing_agent_id");

                    b.Property<bool>("FeatureUsageExceeded")
                        .HasColumnType("boolean")
                        .HasColumnName("feature_usage_exceeded");

                    b.Property<int?>("ManualResolutionDecision")
                        .HasColumnType("integer")
                        .HasColumnName("manual_resolution_decision");

                    b.Property<string>("Message")
                        .HasColumnType("text")
                        .HasColumnName("message");

                    b.Property<int>("PendingAgentId")
                        .HasColumnType("integer")
                        .HasColumnName("pending_agent_id");

                    b.Property<bool>("RequiresManualResolution")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("requires_manual_resolution");

                    b.Property<bool>("Resolved")
                        .HasColumnType("boolean")
                        .HasColumnName("resolved");

                    b.Property<bool>("UnsupportedDeviceType")
                        .HasColumnType("boolean")
                        .HasColumnName("unsupported_device_type");

                    b.HasKey("Id")
                        .HasName("pk_agent_identification_failures");

                    b.HasIndex("ComputerId")
                        .HasDatabaseName("ix_agent_identification_failures_computer_id");

                    b.HasIndex("PendingAgentId")
                        .HasDatabaseName("ix_agent_identification_failures_pending_agent_id");

                    b.ToTable("agent_identification_failures", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AgentIdentificationLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("LogType")
                        .HasColumnType("integer")
                        .HasColumnName("log_type");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("message");

                    b.Property<int>("ProviderAgentId")
                        .HasColumnType("integer")
                        .HasColumnName("provider_agent_id");

                    b.Property<DateTime>("TimeUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("time_utc");

                    b.HasKey("Id")
                        .HasName("pk_agent_identification_logs");

                    b.HasIndex("ProviderAgentId")
                        .HasDatabaseName("ix_agent_identification_logs_provider_agent_id");

                    b.ToTable("agent_identification_logs", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Audit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AffectedProperties")
                        .HasColumnType("text")
                        .HasColumnName("affected_properties");

                    b.Property<DateTime>("DateTimeUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("date_time_utc");

                    b.Property<string>("NewValues")
                        .HasColumnType("text")
                        .HasColumnName("new_values");

                    b.Property<string>("ObjectType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("object_type");

                    b.Property<string>("OldValues")
                        .HasColumnType("text")
                        .HasColumnName("old_values");

                    b.Property<string>("PrimaryKey")
                        .HasColumnType("text")
                        .HasColumnName("primary_key");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("type");

                    b.Property<string>("UserAzureId")
                        .HasColumnType("text")
                        .HasColumnName("user_azure_id");

                    b.Property<string>("UserDisplayName")
                        .HasColumnType("text")
                        .HasColumnName("user_display_name");

                    b.Property<int?>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_audits");

                    b.HasIndex("DateTimeUtc")
                        .HasDatabaseName("ix_audits_date_time_utc");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_audits_user_id");

                    b.HasIndex("ObjectType", "PrimaryKey")
                        .HasDatabaseName("ix_audits_object_type_primary_key");

                    b.ToTable("audits", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AzureErrorLogItem", b =>
                {
                    b.Property<string>("AzureError")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("azure_error");

                    b.Property<DateTime>("CreatedDateUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date_utc");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int?>("Oauth2AccessTokenId")
                        .HasMaxLength(36)
                        .HasColumnType("integer")
                        .HasColumnName("oauth2access_token_id");

                    b.Property<string>("SourceMessage")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("source_message");

                    b.Property<string>("TenantPrincipalId")
                        .HasMaxLength(36)
                        .HasColumnType("character varying(36)")
                        .HasColumnName("tenant_principal_id");

                    b.HasIndex("Id")
                        .IsUnique()
                        .HasDatabaseName("ix_azure_errors_id");

                    b.HasIndex("TenantPrincipalId")
                        .HasDatabaseName("ix_azure_errors_tenant_principal_id");

                    b.ToTable("azure_errors", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AzureTenant", b =>
                {
                    b.Property<string>("PrincipalId")
                        .HasMaxLength(36)
                        .HasColumnType("character varying(36)")
                        .HasColumnName("principal_id");

                    b.Property<int>("AzureTenantType")
                        .HasColumnType("integer")
                        .HasColumnName("azure_tenant_type");

                    b.Property<string>("PartnerPrincipalId")
                        .HasMaxLength(36)
                        .HasColumnType("character varying(36)")
                        .HasColumnName("partner_principal_id");

                    b.ComplexProperty<Dictionary<string, object>>("ConsentDetails", "Immybot.Backend.Domain.Models.AzureTenant.ConsentDetails#AzureTenantConsentDetails", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<DateTime?>("ConsentDateUtc")
                                .HasColumnType("timestamp without time zone")
                                .HasColumnName("consent_details_consent_date_utc");

                            b1.Property<int?>("ConsentedWith")
                                .HasColumnType("integer")
                                .HasColumnName("consent_details_consented_with");
                        });

                    b.HasKey("PrincipalId")
                        .HasName("pk_azure_tenants");

                    b.HasIndex("PartnerPrincipalId")
                        .HasDatabaseName("ix_azure_tenants_partner_principal_id");

                    b.ToTable("azure_tenants", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AzureTenantLink", b =>
                {
                    b.Property<int>("ImmyTenantId")
                        .HasColumnType("integer")
                        .HasColumnName("immy_tenant_id");

                    b.Property<string>("AzTenantId")
                        .IsRequired()
                        .HasColumnType("character varying(36)")
                        .HasColumnName("az_tenant_id");

                    b.Property<bool>("ShouldLimitDomains")
                        .HasColumnType("boolean")
                        .HasColumnName("should_limit_domains");

                    b.HasKey("ImmyTenantId")
                        .HasName("pk_azure_tenant_links");

                    b.HasIndex("AzTenantId", "ShouldLimitDomains")
                        .IsUnique()
                        .HasDatabaseName("ix_azure_tenant_links_az_tenant_id_should_limit_domains")
                        .HasFilter("should_limit_domains = false");

                    b.ToTable("azure_tenant_links", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AzureTenantLinkDomainFilter", b =>
                {
                    b.Property<int>("ImmyTenantId")
                        .HasColumnType("integer")
                        .HasColumnName("immy_tenant_id");

                    b.Property<string>("DomainName")
                        .HasColumnType("text")
                        .HasColumnName("domain_name");

                    b.Property<string>("AzTenantId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("az_tenant_id");

                    b.HasKey("ImmyTenantId", "DomainName")
                        .HasName("pk_azure_tenant_link_domain_filters");

                    b.HasIndex("AzTenantId", "DomainName")
                        .IsUnique()
                        .HasDatabaseName("ix_azure_tenant_link_domain_filters_az_tenant_id_domain_name");

                    b.ToTable("azure_tenant_link_domain_filters", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Branding", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColor")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(7)
                        .HasColumnType("character varying(7)")
                        .HasDefaultValue("#222222")
                        .HasColumnName("background_color");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("description");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("end_date");

                    b.Property<string>("ForegroundColor")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(7)
                        .HasColumnType("character varying(7)")
                        .HasDefaultValue("#f7f5f2")
                        .HasColumnName("foreground_color");

                    b.Property<string>("FromAddress")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasDefaultValue("<EMAIL>")
                        .HasColumnName("from_address");

                    b.Property<bool?>("IgnoreYear")
                        .HasColumnType("boolean")
                        .HasColumnName("ignore_year");

                    b.Property<string>("LogoAltText")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("logo_alt_text");

                    b.Property<string>("LogoUri")
                        .HasMaxLength(2083)
                        .HasColumnType("character varying(2083)")
                        .HasColumnName("logo_uri");

                    b.Property<string>("MascotImgUri")
                        .HasMaxLength(2083)
                        .HasColumnType("character varying(2083)")
                        .HasColumnName("mascot_img_uri");

                    b.Property<string>("MascotName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("mascot_name");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("start_date");

                    b.Property<string>("TableHeaderColor")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(7)
                        .HasColumnType("character varying(7)")
                        .HasDefaultValue("#3d238a")
                        .HasColumnName("table_header_color");

                    b.Property<string>("TableHeaderTextColor")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(7)
                        .HasColumnType("character varying(7)")
                        .HasDefaultValue("#ffffff")
                        .HasColumnName("table_header_text_color");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("TextColor")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(7)
                        .HasColumnType("character varying(7)")
                        .HasDefaultValue("#000000")
                        .HasColumnName("text_color");

                    b.Property<string>("TimeFormat")
                        .HasColumnType("text")
                        .HasColumnName("time_format");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_brandings");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_brandings_created_by");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_brandings_tenant_id");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_brandings_updated_by");

                    b.ToTable("brandings", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ChangeRequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("AcknowledgedByUserId")
                        .HasColumnType("integer")
                        .HasColumnName("acknowledged_by_user_id");

                    b.Property<string>("AcknowledgedByUserName")
                        .HasColumnType("text")
                        .HasColumnName("acknowledged_by_user_name");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<JsonElement>("NewValuesJson")
                        .HasColumnType("jsonb")
                        .HasColumnName("new_values_json");

                    b.Property<int>("ObjectType")
                        .HasColumnType("integer")
                        .HasColumnName("object_type");

                    b.Property<int?>("ScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("script_id");

                    b.Property<int>("State")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("state");

                    b.Property<int?>("TargetAssignmentId")
                        .HasColumnType("integer")
                        .HasColumnName("target_assignment_id");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_change_requests");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_change_requests_created_by");

                    b.HasIndex("ObjectType")
                        .HasDatabaseName("ix_change_requests_object_type");

                    b.HasIndex("ScriptId")
                        .HasDatabaseName("ix_change_requests_script_id");

                    b.HasIndex("TargetAssignmentId")
                        .HasDatabaseName("ix_change_requests_target_assignment_id");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_change_requests_updated_by");

                    b.ToTable("change_requests", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ChangeRequestComment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ChangeRequestId")
                        .HasColumnType("integer")
                        .HasColumnName("change_request_id");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("comment");

                    b.Property<string>("CommentedByUsername")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("commented_by_username");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_change_request_comments");

                    b.HasIndex("ChangeRequestId")
                        .HasDatabaseName("ix_change_request_comments_change_request_id");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_change_request_comments_created_by");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_change_request_comments_updated_by");

                    b.ToTable("change_request_comments", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Computer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<List<int>>("ChassisTypes")
                        .HasColumnType("integer[]")
                        .HasColumnName("chassis_types");

                    b.Property<string>("ComputerName")
                        .HasColumnType("text")
                        .HasColumnName("computer_name");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("DeletedReason")
                        .HasColumnType("text")
                        .HasColumnName("deleted_reason");

                    b.Property<bool>("DetectionOutdated")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("detection_outdated");

                    b.Property<DateTime?>("DevLabVmClaimExpirationDateUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("dev_lab_vm_claim_expiration_date_utc");

                    b.Property<string>("DevLabVmName")
                        .HasColumnType("text")
                        .HasColumnName("dev_lab_vm_name");

                    b.Property<bool>("DevLabVmUnclaimed")
                        .HasColumnType("boolean")
                        .HasColumnName("dev_lab_vm_unclaimed");

                    b.Property<Guid>("DeviceId")
                        .HasColumnType("uuid")
                        .HasColumnName("device_id");

                    b.Property<string>("Domain")
                        .HasColumnType("text")
                        .HasColumnName("domain");

                    b.Property<int?>("DomainRole")
                        .HasColumnType("integer")
                        .HasColumnName("domain_role");

                    b.Property<bool>("ExcludeFromMaintenance")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("exclude_from_maintenance");

                    b.Property<bool>("ExcludedFromUserAffinity")
                        .HasColumnType("boolean")
                        .HasColumnName("excluded_from_user_affinity");

                    b.Property<string>("ExternalIpAddress")
                        .HasColumnType("text")
                        .HasColumnName("external_ip_address");

                    b.Property<bool?>("HasPendingReboot")
                        .HasColumnType("boolean")
                        .HasColumnName("has_pending_reboot");

                    b.Property<string>("InternalIpAddress")
                        .HasColumnType("text")
                        .HasColumnName("internal_ip_address");

                    b.Property<DateTime?>("InventoryStartedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("inventory_started_date");

                    b.Property<bool>("IsSandbox")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_sandbox");

                    b.Property<string>("LastBootTimeUtc")
                        .HasColumnType("text")
                        .HasColumnName("last_boot_time_utc");

                    b.Property<string>("LastLoggedOnUser")
                        .HasColumnType("text")
                        .HasColumnName("last_logged_on_user");

                    b.Property<string>("Manufacturer")
                        .HasColumnType("text")
                        .HasColumnName("manufacturer");

                    b.Property<string>("Model")
                        .HasColumnType("text")
                        .HasColumnName("model");

                    b.Property<DateTime?>("OSInstallDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("os_install_date");

                    b.Property<DateTime?>("OnboardedDateUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("onboarded_date_utc");

                    b.Property<int>("OnboardingStatus")
                        .HasColumnType("integer")
                        .HasColumnName("onboarding_status");

                    b.Property<string>("OperatingSystem")
                        .HasColumnType("text")
                        .HasColumnName("operating_system");

                    b.Property<int?>("PrimaryPersonId")
                        .HasColumnType("integer")
                        .HasColumnName("primary_person_id");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("text")
                        .HasColumnName("serial_number");

                    b.Property<int?>("SuccessorComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("successor_computer_id");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_computers");

                    b.HasIndex("ComputerName")
                        .HasDatabaseName("ix_computers_computer_name");

                    b.HasIndex("CreatedDate")
                        .HasDatabaseName("ix_computers_created_date");

                    b.HasIndex("DeviceId")
                        .IsUnique()
                        .HasDatabaseName("ix_computers_device_id");

                    b.HasIndex("OnboardingStatus")
                        .HasDatabaseName("ix_computers_onboarding_status");

                    b.HasIndex("PrimaryPersonId")
                        .HasDatabaseName("ix_computers_primary_person_id");

                    b.HasIndex("SuccessorComputerId")
                        .HasDatabaseName("ix_computers_successor_computer_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_computers_tenant_id");

                    b.ToTable("computers", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ComputerInventoryAllSoftware", b =>
                {
                    b.Property<string>("Computer")
                        .HasColumnType("text")
                        .HasColumnName("computer");

                    b.Property<int>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<string>("SoftwareName")
                        .HasColumnType("text")
                        .HasColumnName("software_name");

                    b.Property<string>("SoftwareVersion")
                        .HasColumnType("text")
                        .HasColumnName("software_version");

                    b.Property<string>("Tenant")
                        .HasColumnType("text")
                        .HasColumnName("tenant");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.ToTable((string)null);

                    b.ToView(null, (string)null);

                    b.ToSqlQuery("SELECT\n  c.id AS computer_id,\n  t.id AS tenant_id,\n  t.name AS tenant,\n	c.computer_name AS computer,\n	v.value ->> 'DisplayName' AS software_name,\n	v.value ->> 'DisplayVersion' AS software_version\nFROM computers c\nINNER JOIN tenants t ON c.tenant_id = t.id\nLEFT JOIN LATERAL (\n  SELECT * FROM computer_inventory_task_script_results softwares\n  WHERE softwares.computer_id = c.id AND softwares.inventory_key = 'Software'\n  ORDER BY softwares.timestamp DESC LIMIT 1\n) AS softwares ON true\nCROSS JOIN LATERAL jsonb_array_elements(softwares.latest_success_result -> 'Output') v(value)\nWHERE c.deleted_at IS NULL");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ComputerInventoryKeyViewModel", b =>
                {
                    b.Property<int>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<Guid>("DeviceId")
                        .HasColumnType("uuid")
                        .HasColumnName("device_id");

                    b.Property<string>("InventoryKey")
                        .HasColumnType("text")
                        .HasColumnName("inventory_key");

                    b.Property<int>("ScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("script_type");

                    b.ToTable("ComputerInventoryKeyViewModel", null, t =>
                        {
                            t.ExcludeFromMigrations();
                        });
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ComputerInventorySoftware", b =>
                {
                    b.Property<int>("Computers")
                        .HasColumnType("integer")
                        .HasColumnName("computers");

                    b.Property<string>("DisplayName")
                        .HasColumnType("text")
                        .HasColumnName("display_name");

                    b.Property<string>("DisplayVersion")
                        .HasColumnType("text")
                        .HasColumnName("display_version");

                    b.Property<Guid?>("UpgradeCode")
                        .HasColumnType("uuid")
                        .HasColumnName("upgrade_code");

                    b.ToTable((string)null);

                    b.ToSqlQuery("select dcs.display_name, dcs.display_version , dcs.upgrade_code, count(*) as computers\nfrom detected_computer_software dcs\ninner join computers c on c.id = dcs.computer_id\nwhere c.deleted_at is null\ngroup by display_name , dcs.display_version, dcs.upgrade_code");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ComputerInventoryTaskScriptResult", b =>
                {
                    b.Property<int>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<string>("InventoryKey")
                        .HasColumnType("text")
                        .HasColumnName("inventory_key");

                    b.Property<JsonDocument>("LatestErrorResult")
                        .HasColumnType("jsonb")
                        .HasColumnName("latest_error_result");

                    b.Property<bool>("LatestResultIsError")
                        .HasColumnType("boolean")
                        .HasColumnName("latest_result_is_error");

                    b.Property<JsonDocument>("LatestSuccessResult")
                        .HasColumnType("jsonb")
                        .HasColumnName("latest_success_result");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("timestamp");

                    b.HasKey("ComputerId", "InventoryKey")
                        .HasName("pk_computer_inventory_task_script_results");

                    b.HasIndex("ComputerId")
                        .HasDatabaseName("ix_computer_inventory_task_script_results_computer_id");

                    b.HasIndex("InventoryKey")
                        .HasDatabaseName("ix_computer_inventory_task_script_results_inventory_key");

                    b.ToTable("computer_inventory_task_script_results", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ComputerLatestProviderEvent", b =>
                {
                    b.Property<int>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<DateTime>("LastProviderAgentEventDateUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("last_provider_agent_event_date_utc");

                    b.HasKey("ComputerId")
                        .HasName("pk_computer_latest_provider_events");

                    b.ToTable("computer_latest_provider_events", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ComputerListViewModel", b =>
                {
                    b.Property<int?>("ActiveSessionId")
                        .HasColumnType("integer")
                        .HasColumnName("active_session_id");

                    b.Property<int?>("ActiveSessionStatus")
                        .HasColumnType("integer")
                        .HasColumnName("active_session_status");

                    b.Property<List<int>>("ChassisTypes")
                        .HasColumnType("integer[]")
                        .HasColumnName("chassis_types");

                    b.Property<string>("ComputerName")
                        .HasColumnType("text")
                        .HasColumnName("computer_name");

                    b.Property<List<int>>("ComputerTagIds")
                        .HasColumnType("integer[]")
                        .HasColumnName("computer_tag_ids");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("deleted_at");

                    b.Property<DateTime?>("DevLabVmClaimExpirationDateUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("dev_lab_vm_claim_expiration_date_utc");

                    b.Property<string>("DevLabVmName")
                        .HasColumnType("text")
                        .HasColumnName("dev_lab_vm_name");

                    b.Property<bool>("DevLabVmUnclaimed")
                        .HasColumnType("boolean")
                        .HasColumnName("dev_lab_vm_unclaimed");

                    b.Property<Guid>("DeviceId")
                        .HasColumnType("uuid")
                        .HasColumnName("device_id");

                    b.Property<string>("Domain")
                        .HasColumnType("text")
                        .HasColumnName("domain");

                    b.Property<int?>("DomainRole")
                        .HasColumnType("integer")
                        .HasColumnName("domain_role");

                    b.Property<bool>("ExcludeFromMaintenance")
                        .HasColumnType("boolean")
                        .HasColumnName("exclude_from_maintenance");

                    b.Property<string>("ExternalIpAddress")
                        .HasColumnType("text")
                        .HasColumnName("external_ip_address");

                    b.Property<bool?>("HasPendingReboot")
                        .HasColumnType("boolean")
                        .HasColumnName("has_pending_reboot");

                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("InternalIpAddress")
                        .HasColumnType("text")
                        .HasColumnName("internal_ip_address");

                    b.Property<bool?>("IsOnline")
                        .HasColumnType("boolean")
                        .HasColumnName("is_online");

                    b.Property<bool>("IsSandbox")
                        .HasColumnType("boolean")
                        .HasColumnName("is_sandbox");

                    b.Property<string>("LastBootTimeUtc")
                        .HasColumnType("text")
                        .HasColumnName("last_boot_time_utc");

                    b.Property<string>("LastLoggedOnUser")
                        .HasColumnType("text")
                        .HasColumnName("last_logged_on_user");

                    b.Property<DateTime?>("LastProviderAgentEventDateUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("last_provider_agent_event_date_utc");

                    b.Property<bool?>("Licensed")
                        .HasColumnType("boolean")
                        .HasColumnName("licensed");

                    b.Property<string>("Manufacturer")
                        .HasColumnType("text")
                        .HasColumnName("manufacturer");

                    b.Property<string>("Model")
                        .HasColumnType("text")
                        .HasColumnName("model");

                    b.Property<int>("OnboardingStatus")
                        .HasColumnType("integer")
                        .HasColumnName("onboarding_status");

                    b.Property<string>("OperatingSystem")
                        .HasColumnType("text")
                        .HasColumnName("operating_system");

                    b.Property<int?>("PrimaryPersonId")
                        .HasColumnType("integer")
                        .HasColumnName("primary_person_id");

                    b.Property<string>("PrimaryUserEmail")
                        .HasColumnType("text")
                        .HasColumnName("primary_user_email");

                    b.Property<string>("PrimaryUserFirstName")
                        .HasColumnType("text")
                        .HasColumnName("primary_user_first_name");

                    b.Property<string>("PrimaryUserLastName")
                        .HasColumnType("text")
                        .HasColumnName("primary_user_last_name");

                    b.Property<List<int>>("ProviderLinkIds")
                        .HasColumnType("integer[]")
                        .HasColumnName("provider_link_ids");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("text")
                        .HasColumnName("serial_number");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("TenantName")
                        .HasColumnType("text")
                        .HasColumnName("tenant_name");

                    b.ToTable("ComputerListViewModel", null, t =>
                        {
                            t.ExcludeFromMigrations();
                        });
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ComputerPerson", b =>
                {
                    b.Property<int>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer")
                        .HasColumnName("person_id");

                    b.HasKey("ComputerId", "PersonId")
                        .HasName("pk_computer_persons");

                    b.HasIndex("PersonId")
                        .HasDatabaseName("ix_computer_persons_person_id");

                    b.ToTable("computer_persons", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ComputerTag", b =>
                {
                    b.Property<int>("EntityId")
                        .HasColumnType("integer")
                        .HasColumnName("entity_id");

                    b.Property<int>("TagId")
                        .HasColumnType("integer")
                        .HasColumnName("tag_id");

                    b.HasKey("EntityId", "TagId")
                        .HasName("pk_computer_tags");

                    b.HasIndex("TagId")
                        .HasDatabaseName("ix_computer_tags_tag_id");

                    b.ToTable("computer_tags", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.DetectedComputerSoftware", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<int>("Context")
                        .HasColumnType("integer")
                        .HasColumnName("context");

                    b.Property<DateTimeOffset>("DetectedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("detected_at");

                    b.Property<string>("DisplayIcon")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("display_icon");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("display_name");

                    b.Property<string>("DisplayVersion")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("display_version");

                    b.Property<int?>("GlobalSoftwareId")
                        .HasColumnType("integer")
                        .HasColumnName("global_software_id");

                    b.Property<string>("GlobalSoftwareName")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("global_software_name");

                    b.Property<DateTime?>("InstallDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("install_date");

                    b.Property<string>("InstallLocation")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("install_location");

                    b.Property<string>("Platform")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("platform");

                    b.Property<int?>("PrimaryPersonId")
                        .HasColumnType("integer")
                        .HasColumnName("primary_person_id");

                    b.Property<Guid?>("ProductCode")
                        .HasColumnType("uuid")
                        .HasColumnName("product_code");

                    b.Property<string>("QuietUninstallString")
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)")
                        .HasColumnName("quiet_uninstall_string");

                    b.Property<string>("RegistryPath")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("registry_path");

                    b.Property<int?>("SystemComponent")
                        .HasColumnType("integer")
                        .HasColumnName("system_component");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("UninstallString")
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)")
                        .HasColumnName("uninstall_string");

                    b.Property<Guid?>("UpgradeCode")
                        .HasColumnType("uuid")
                        .HasColumnName("upgrade_code");

                    b.Property<string>("UserName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("user_name");

                    b.Property<string>("UserSid")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("user_sid");

                    b.HasKey("Id")
                        .HasName("pk_detected_computer_software");

                    b.HasIndex("ComputerId")
                        .HasDatabaseName("ix_detected_computer_software_computer_id");

                    b.HasIndex("DisplayName")
                        .HasDatabaseName("ix_detected_computer_software_display_name");

                    b.HasIndex("GlobalSoftwareName")
                        .HasDatabaseName("ix_detected_computer_software_global_software_name");

                    b.HasIndex("PrimaryPersonId")
                        .HasDatabaseName("ix_detected_computer_software_primary_person_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_detected_computer_software_tenant_id");

                    b.HasIndex("UpgradeCode")
                        .HasDatabaseName("ix_detected_computer_software_upgrade_code");

                    b.ToTable("detected_computer_software", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.DisabledPreflightScript", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("DatabaseType")
                        .HasColumnType("integer")
                        .HasColumnName("database_type");

                    b.Property<int>("ScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("script_id");

                    b.HasKey("Id")
                        .HasName("pk_disabled_preflight_scripts");

                    b.ToTable("disabled_preflight_scripts", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.DynamicIntegrationType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("CreationErrorMessage")
                        .HasColumnType("text")
                        .HasColumnName("creation_error_message");

                    b.Property<int>("DatabaseType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("database_type");

                    b.Property<string>("DocsUrl")
                        .HasColumnType("text")
                        .HasColumnName("docs_url");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean")
                        .HasColumnName("enabled");

                    b.Property<Guid>("IntegrationTypeId")
                        .HasColumnType("uuid")
                        .HasColumnName("integration_type_id");

                    b.Property<int>("LogoId")
                        .HasColumnType("integer")
                        .HasColumnName("logo_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<int>("ScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("script_id");

                    b.Property<int>("Tag")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("tag");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_dynamic_integration_types");

                    b.HasIndex("IntegrationTypeId")
                        .IsUnique()
                        .HasDatabaseName("ix_dynamic_integration_types_integration_type_id");

                    b.HasIndex("LogoId")
                        .HasDatabaseName("ix_dynamic_integration_types_logo_id");

                    b.HasIndex("ScriptId")
                        .HasDatabaseName("ix_dynamic_integration_types_script_id");

                    b.ToTable("dynamic_integration_types", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.FeatureUsage", b =>
                {
                    b.Property<string>("FeatureId")
                        .HasColumnType("text")
                        .HasColumnName("feature_id");

                    b.Property<DateTime>("FeatureTrackStartDateUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("feature_track_start_date_utc");

                    b.Property<int>("Count")
                        .HasColumnType("integer")
                        .HasColumnName("count");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean")
                        .HasColumnName("enabled");

                    b.Property<int?>("MaxCount")
                        .HasColumnType("integer")
                        .HasColumnName("max_count");

                    b.Property<int?>("MaxCountPerItem")
                        .HasColumnType("integer")
                        .HasColumnName("max_count_per_item");

                    b.Property<DateTime?>("UsageExceededEventNotifiedDateUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("usage_exceeded_event_notified_date_utc");

                    b.HasKey("FeatureId", "FeatureTrackStartDateUtc")
                        .HasName("pk_feature_usages");

                    b.ToTable("feature_usages", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.GettingStartedStep", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CompletedAtUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("completed_at_utc");

                    b.Property<int?>("CompletedByUserId")
                        .HasColumnType("integer")
                        .HasColumnName("completed_by_user_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_getting_started_steps");

                    b.ToTable("getting_started_steps", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.GlobalSoftwareMatch", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("display_name");

                    b.Property<int>("GlobalSoftwareId")
                        .HasColumnType("integer")
                        .HasColumnName("global_software_id");

                    b.HasKey("Id")
                        .HasName("pk_global_software_matches");

                    b.HasIndex("DisplayName")
                        .HasDatabaseName("ix_global_software_matches_display_name");

                    b.ToTable("global_software_matches", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.HistoricalComputerInventoryTaskScriptResult", b =>
                {
                    b.Property<int>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<string>("InventoryKey")
                        .HasColumnType("text")
                        .HasColumnName("inventory_key");

                    b.Property<JsonDocument>("InventoryTaskResult")
                        .HasColumnType("jsonb")
                        .HasColumnName("inventory_task_result");

                    b.Property<bool>("IsError")
                        .HasColumnType("boolean")
                        .HasColumnName("is_error");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("timestamp");

                    b.HasIndex("ComputerId")
                        .HasDatabaseName("ix_historical_computer_inventory_task_script_results_computer_");

                    b.HasIndex("InventoryKey")
                        .HasDatabaseName("ix_historical_computer_inventory_task_script_results_inventory");

                    b.ToTable("historical_computer_inventory_task_script_results", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ImmyAgentInstaller", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<List<int>>("AdditionalPersonIds")
                        .HasColumnType("integer[]")
                        .HasColumnName("additional_person_ids");

                    b.Property<bool>("Authorized")
                        .HasColumnType("boolean")
                        .HasColumnName("authorized");

                    b.Property<bool>("AutomaticallyOnboard")
                        .HasColumnType("boolean")
                        .HasColumnName("automatically_onboard");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("InstallerId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("installer_id");

                    b.Property<int>("InstancedDeviceCount")
                        .HasColumnType("integer")
                        .HasColumnName("instanced_device_count");

                    b.Property<bool>("IsDevLab")
                        .HasColumnType("boolean")
                        .HasColumnName("is_dev_lab");

                    b.Property<bool>("IsSingleUse")
                        .HasColumnType("boolean")
                        .HasColumnName("is_single_use");

                    b.Property<string>("OnboardingCorrelationId")
                        .HasColumnType("text")
                        .HasColumnName("onboarding_correlation_id");

                    b.Property<bool>("OnboardingSessionAutoConsentToReboots")
                        .HasColumnType("boolean")
                        .HasColumnName("onboarding_session_auto_consent_to_reboots");

                    b.Property<int?>("OnboardingSessionPromptTimeoutAction")
                        .HasColumnType("integer")
                        .HasColumnName("onboarding_session_prompt_timeout_action");

                    b.Property<int>("OnboardingSessionPromptTimeoutMinutes")
                        .HasColumnType("integer")
                        .HasColumnName("onboarding_session_prompt_timeout_minutes");

                    b.Property<int?>("OnboardingSessionRebootPreference")
                        .HasColumnType("integer")
                        .HasColumnName("onboarding_session_reboot_preference");

                    b.Property<bool>("OnboardingSessionSendFollowUpEmail")
                        .HasColumnType("boolean")
                        .HasColumnName("onboarding_session_send_follow_up_email");

                    b.Property<int?>("PrimaryPersonId")
                        .HasColumnType("integer")
                        .HasColumnName("primary_person_id");

                    b.Property<int>("ProviderLinkId")
                        .HasColumnType("integer")
                        .HasColumnName("provider_link_id");

                    b.Property<byte[]>("PublicKey")
                        .IsRequired()
                        .HasColumnType("bytea")
                        .HasColumnName("public_key");

                    b.Property<List<int>>("Tags")
                        .HasColumnType("integer[]")
                        .HasColumnName("tags");

                    b.Property<string>("TargetExternalClientId")
                        .HasColumnType("text")
                        .HasColumnName("target_external_client_id");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_immy_agent_installers");

                    b.HasIndex("InstallerId")
                        .HasDatabaseName("ix_immy_agent_installers_installer_id");

                    b.HasIndex("ProviderLinkId")
                        .HasDatabaseName("ix_immy_agent_installers_provider_link_id");

                    b.ToTable("immy_agent_installers", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ImmyMigration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("DateRanUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("date_ran_utc");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_immy_migrations");

                    b.ToTable("immy_migrations", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.InventoryTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int>("Frequency")
                        .HasColumnType("integer")
                        .HasColumnName("frequency");

                    b.Property<int>("InventoryTaskType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("inventory_task_type");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<int?>("SpecifiedNumMinutes")
                        .HasColumnType("integer")
                        .HasColumnName("specified_num_minutes");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_inventory_tasks");

                    b.ToTable("inventory_tasks", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.InventoryTaskScript", b =>
                {
                    b.Property<string>("InventoryKey")
                        .HasColumnType("text")
                        .HasColumnName("inventory_key");

                    b.Property<int>("InventoryTaskId")
                        .HasColumnType("integer")
                        .HasColumnName("inventory_task_id");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<bool>("SaveDebugStream")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("save_debug_stream");

                    b.Property<bool>("SaveInformationStream")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("save_information_stream");

                    b.Property<bool>("SaveVerboseStream")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("save_verbose_stream");

                    b.Property<bool>("SaveWarningStream")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("save_warning_stream");

                    b.Property<int>("ScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("script_id");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("InventoryKey", "InventoryTaskId")
                        .HasName("pk_inventory_task_scripts");

                    b.HasIndex("InventoryTaskId")
                        .HasDatabaseName("ix_inventory_task_scripts_inventory_task_id");

                    b.HasIndex("ScriptId")
                        .IsUnique()
                        .HasDatabaseName("ix_inventory_task_scripts_script_id");

                    b.ToTable("inventory_task_scripts", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.License", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("LicenseValue")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("license_value");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<bool>("RestrictToMajorVersion")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("restrict_to_major_version");

                    b.Property<string>("SemanticVersion")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("semantic_version");

                    b.Property<string>("SoftwareIdentifier")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("software_identifier");

                    b.Property<string>("SoftwareName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("software_name");

                    b.Property<int>("SoftwareType")
                        .HasColumnType("integer")
                        .HasColumnName("software_type");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_licenses");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_licenses_created_by");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_licenses_tenant_id");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_licenses_updated_by");

                    b.ToTable("licenses", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.LocalSoftware", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid?>("AgentIntegrationTypeId")
                        .HasColumnType("uuid")
                        .HasColumnName("agent_integration_type_id");

                    b.Property<int?>("AutoUpdateScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("auto_update_script_id");

                    b.Property<int?>("AutoUpdateScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("auto_update_script_type");

                    b.Property<string>("ChocoProviderSoftwareId")
                        .HasColumnType("text")
                        .HasColumnName("choco_provider_software_id");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int>("DetectionMethod")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("detection_method");

                    b.Property<int?>("DetectionScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("detection_script_id");

                    b.Property<int?>("DetectionScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("detection_script_type");

                    b.Property<int?>("DownloadInstallerScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("download_installer_script_id");

                    b.Property<int?>("DownloadInstallerScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("download_installer_script_type");

                    b.Property<int?>("DynamicVersionsScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("dynamic_versions_script_id");

                    b.Property<int?>("DynamicVersionsScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("dynamic_versions_script_type");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<int>("InstallOrder")
                        .HasColumnType("integer")
                        .HasColumnName("install_order");

                    b.Property<int?>("InstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("install_script_id");

                    b.Property<int?>("InstallScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("install_script_type");

                    b.Property<string>("LicenseDescription")
                        .HasColumnType("text")
                        .HasColumnName("license_description");

                    b.Property<int>("LicenseRequirement")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("license_requirement");

                    b.Property<int>("LicenseType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("license_type");

                    b.Property<int?>("MaintenanceTaskId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_id");

                    b.Property<int?>("MaintenanceTaskType")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_type");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<string>("NiniteProviderSoftwareId")
                        .HasColumnType("text")
                        .HasColumnName("ninite_provider_software_id");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<int?>("OwnerTenantId")
                        .HasColumnType("integer")
                        .HasColumnName("owner_tenant_id");

                    b.Property<int?>("PostInstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("post_install_script_id");

                    b.Property<int?>("PostInstallScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("post_install_script_type");

                    b.Property<int?>("PostUninstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("post_uninstall_script_id");

                    b.Property<int?>("PostUninstallScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("post_uninstall_script_type");

                    b.Property<bool>("RebootNeeded")
                        .HasColumnType("boolean")
                        .HasColumnName("reboot_needed");

                    b.Property<bool>("Recommended")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("recommended");

                    b.Property<Guid>("RelativeCacheSourcePath")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("relative_cache_source_path")
                        .HasDefaultValueSql("uuid_generate_v4()");

                    b.Property<int?>("RepairScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("repair_script_id");

                    b.Property<int?>("RepairScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("repair_script_type");

                    b.Property<int>("RepairType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("repair_type");

                    b.Property<int?>("SoftwareIconMediaId")
                        .HasColumnType("integer")
                        .HasColumnName("software_icon_media_id");

                    b.Property<string>("SoftwareTableName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("software_table_name");

                    b.Property<int?>("SoftwareTableNameSearchMode")
                        .HasColumnType("integer")
                        .HasColumnName("software_table_name_search_mode");

                    b.Property<string>("TestFailedError")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("test_failed_error");

                    b.Property<bool>("TestRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("test_required");

                    b.Property<int?>("TestScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("test_script_id");

                    b.Property<int?>("TestScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("test_script_type");

                    b.Property<int?>("UninstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("uninstall_script_id");

                    b.Property<int?>("UninstallScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("uninstall_script_type");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.Property<string>("UpgradeCode")
                        .HasColumnType("text")
                        .HasColumnName("upgrade_code");

                    b.Property<int?>("UpgradeScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("upgrade_script_id");

                    b.Property<int?>("UpgradeScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("upgrade_script_type");

                    b.Property<int>("UpgradeStrategy")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("upgrade_strategy");

                    b.Property<bool>("UseDynamicVersions")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("use_dynamic_versions");

                    b.Property<bool>("UseSoftwareTableDetection")
                        .HasColumnType("boolean")
                        .HasColumnName("use_software_table_detection");

                    b.HasKey("Id")
                        .HasName("pk_software");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_software_created_by");

                    b.HasIndex("SoftwareIconMediaId")
                        .HasDatabaseName("ix_software_software_icon_media_id");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_software_updated_by");

                    b.ToTable("software", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.LocalSoftwareVersion", b =>
                {
                    b.Property<int>("SoftwareId")
                        .HasColumnType("integer")
                        .HasColumnName("software_id");

                    b.Property<string>("SemanticVersion")
                        .HasColumnType("text")
                        .HasColumnName("semantic_version");

                    b.Property<int?>("Architecture")
                        .HasColumnType("integer")
                        .HasColumnName("architecture");

                    b.Property<string>("BlobName")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)")
                        .HasColumnName("blob_name");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("DependsOnSemanticVersion")
                        .HasColumnType("text")
                        .HasColumnName("depends_on_semantic_version");

                    b.Property<int?>("DeprecatedIdField")
                        .HasColumnType("integer")
                        .HasColumnName("deprecated_id_field");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("display_name");

                    b.Property<string>("DisplayVersion")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("display_version");

                    b.Property<int?>("InstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("install_script_id");

                    b.Property<int?>("InstallScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("install_script_type");

                    b.Property<string>("InstallerFile")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("installer_file");

                    b.Property<int>("InstallerType")
                        .HasColumnType("integer")
                        .HasColumnName("installer_type");

                    b.Property<int?>("LastResult")
                        .HasColumnType("integer")
                        .HasColumnName("last_result");

                    b.Property<string>("Notes")
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)")
                        .HasColumnName("notes");

                    b.Property<int>("NumActionFailures")
                        .HasColumnType("integer")
                        .HasColumnName("num_action_failures");

                    b.Property<int>("NumActionSuccesses")
                        .HasColumnType("integer")
                        .HasColumnName("num_action_successes");

                    b.Property<string>("PackageHash")
                        .HasColumnType("text")
                        .HasColumnName("package_hash");

                    b.Property<int>("PackageType")
                        .HasColumnType("integer")
                        .HasColumnName("package_type");

                    b.Property<int?>("PostInstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("post_install_script_id");

                    b.Property<int?>("PostInstallScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("post_install_script_type");

                    b.Property<int?>("PostUninstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("post_uninstall_script_id");

                    b.Property<int?>("PostUninstallScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("post_uninstall_script_type");

                    b.Property<string>("ProductCode")
                        .HasColumnType("text")
                        .HasColumnName("product_code");

                    b.Property<string>("RelativeCacheSourcePath")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("relative_cache_source_path");

                    b.Property<string>("TestFailedError")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("test_failed_error");

                    b.Property<bool>("TestRequired")
                        .HasColumnType("boolean")
                        .HasColumnName("test_required");

                    b.Property<int?>("TestScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("test_script_id");

                    b.Property<int?>("TestScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("test_script_type");

                    b.Property<string>("URL")
                        .HasMaxLength(2083)
                        .HasColumnType("character varying(2083)")
                        .HasColumnName("url");

                    b.Property<int?>("UninstallScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("uninstall_script_id");

                    b.Property<int?>("UninstallScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("uninstall_script_type");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.Property<int?>("UpgradeScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("upgrade_script_id");

                    b.Property<int?>("UpgradeScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("upgrade_script_type");

                    b.Property<int>("UpgradeStrategy")
                        .HasColumnType("integer")
                        .HasColumnName("upgrade_strategy");

                    b.HasKey("SoftwareId", "SemanticVersion")
                        .HasName("pk_software_versions");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_software_versions_created_by");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_software_versions_updated_by");

                    b.ToTable("software_versions", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceAction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ActionReason")
                        .HasColumnType("integer")
                        .HasColumnName("action_reason");

                    b.Property<int>("ActionResult")
                        .HasColumnType("integer")
                        .HasColumnName("action_result");

                    b.Property<int?>("ActionResultReason")
                        .HasColumnType("integer")
                        .HasColumnName("action_result_reason");

                    b.Property<string>("ActionResultReasonMessage")
                        .HasColumnType("text")
                        .HasColumnName("action_result_reason_message");

                    b.Property<int>("ActionStatus")
                        .HasColumnType("integer")
                        .HasColumnName("action_status");

                    b.Property<int>("ActionType")
                        .HasColumnType("integer")
                        .HasColumnName("action_type");

                    b.Property<int?>("AssignmentId")
                        .HasColumnType("integer")
                        .HasColumnName("assignment_id");

                    b.Property<int?>("AssignmentType")
                        .HasColumnType("integer")
                        .HasColumnName("assignment_type");

                    b.Property<int?>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int?>("DesiredSoftwareState")
                        .HasColumnType("integer")
                        .HasColumnName("desired_software_state");

                    b.Property<string>("DesiredVersion")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("desired_version");

                    b.Property<string>("DetectedVersion")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("detected_version");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("end_time");

                    b.Property<bool>("HasDeterminedDesiredVersion")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("has_determined_desired_version");

                    b.Property<bool>("HasDeterminedDetectedVersion")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("has_determined_detected_version");

                    b.Property<bool>("HasDeterminedTaskGetResult")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("has_determined_task_get_result");

                    b.Property<bool>("HasDeterminedTaskTestResult")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("has_determined_task_test_result");

                    b.Property<string>("LastAction")
                        .HasColumnType("text")
                        .HasColumnName("last_action");

                    b.Property<string>("LastActionResult")
                        .HasColumnType("text")
                        .HasColumnName("last_action_result");

                    b.Property<string>("LastActionRmmComputerId")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("last_action_rmm_computer_id");

                    b.Property<int>("LastActionType")
                        .HasColumnType("integer")
                        .HasColumnName("last_action_type");

                    b.Property<string>("MaintenanceDisplayName")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("maintenance_display_name");

                    b.Property<string>("MaintenanceIdentifier")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("maintenance_identifier");

                    b.Property<int>("MaintenanceSessionId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_session_id");

                    b.Property<string>("MaintenanceTaskGetResult")
                        .HasColumnType("text")
                        .HasColumnName("maintenance_task_get_result");

                    b.Property<int?>("MaintenanceTaskMode")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_mode");

                    b.Property<int>("MaintenanceType")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_type");

                    b.Property<string>("Parameters")
                        .HasColumnType("text")
                        .HasColumnName("parameters");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_id");

                    b.Property<int?>("PersonId")
                        .HasColumnType("integer")
                        .HasColumnName("person_id");

                    b.Property<string>("PolicyDescription")
                        .HasColumnType("text")
                        .HasColumnName("policy_description");

                    b.Property<string>("PostMaintenanceTest")
                        .HasColumnType("text")
                        .HasColumnName("post_maintenance_test");

                    b.Property<bool?>("PostMaintenanceTestResult")
                        .HasColumnType("boolean")
                        .HasColumnName("post_maintenance_test_result");

                    b.Property<string>("PostMaintenanceTestResultMessage")
                        .HasColumnType("text")
                        .HasColumnName("post_maintenance_test_result_message");

                    b.Property<int?>("PostMaintenanceTestType")
                        .HasColumnType("integer")
                        .HasColumnName("post_maintenance_test_type");

                    b.Property<int>("ScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("script_type");

                    b.Property<int?>("SoftwareActionIdForConfigurationTask")
                        .HasColumnType("integer")
                        .HasColumnName("software_action_id_for_configuration_task");

                    b.Property<int>("SoftwareProviderType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(3)
                        .HasColumnName("software_provider_type");

                    b.Property<string>("SoftwareTableRegexString")
                        .HasColumnType("text")
                        .HasColumnName("software_table_regex_string");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("start_time");

                    b.Property<bool?>("TaskTestResult")
                        .HasColumnType("boolean")
                        .HasColumnName("task_test_result");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.Property<bool>("UsesManualProgressControl")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("uses_manual_progress_control");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_actions");

                    b.HasIndex("ComputerId")
                        .HasDatabaseName("ix_maintenance_actions_computer_id");

                    b.HasIndex("CreatedDate")
                        .IsDescending()
                        .HasDatabaseName("ix_maintenance_actions_created_date");

                    b.HasIndex("MaintenanceSessionId")
                        .HasDatabaseName("ix_maintenance_actions_maintenance_session_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_maintenance_actions_tenant_id");

                    b.HasIndex("AssignmentId", "AssignmentType")
                        .HasDatabaseName("ix_maintenance_actions_assignment_id_assignment_type");

                    b.HasIndex("MaintenanceType", "MaintenanceIdentifier")
                        .HasDatabaseName("ix_maintenance_actions_maintenance_type_maintenance_identifier");

                    b.ToTable("maintenance_actions", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceActionDependency", b =>
                {
                    b.Property<int>("DependentId")
                        .HasColumnType("integer")
                        .HasColumnName("dependent_id");

                    b.Property<int>("DependsOnId")
                        .HasColumnType("integer")
                        .HasColumnName("depends_on_id");

                    b.HasKey("DependentId", "DependsOnId")
                        .HasName("pk_maintenance_action_dependencies");

                    b.HasIndex("DependsOnId")
                        .HasDatabaseName("ix_maintenance_action_dependencies_depends_on_id");

                    b.ToTable("maintenance_action_dependencies", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceItemOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Location")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(2)
                        .HasColumnName("location");

                    b.Property<string>("MaintenanceIdentifier")
                        .HasColumnType("text")
                        .HasColumnName("maintenance_identifier");

                    b.Property<int>("MaintenanceType")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_type");

                    b.Property<double>("SortOrder")
                        .HasColumnType("double precision")
                        .HasColumnName("sort_order");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_item_orders");

                    b.HasIndex("Location", "SortOrder")
                        .IsUnique()
                        .HasDatabaseName("ix_maintenance_item_orders_location_sort_order")
                        .HasFilter("location != 2");

                    b.HasIndex("MaintenanceIdentifier", "MaintenanceType")
                        .IsUnique()
                        .HasDatabaseName("ix_maintenance_item_orders_maintenance_identifier_maintenance_");

                    b.ToTable("maintenance_item_orders", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceSession", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<bool>("FullMaintenance")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("full_maintenance");

                    b.Property<string>("JobArgs")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("job_args");

                    b.Property<string>("JobId")
                        .HasMaxLength(36)
                        .HasColumnType("character varying(36)")
                        .HasColumnName("job_id");

                    b.Property<bool>("Onboarding")
                        .HasColumnType("boolean")
                        .HasColumnName("onboarding");

                    b.Property<int?>("PersonId")
                        .HasColumnType("integer")
                        .HasColumnName("person_id");

                    b.Property<DateTime?>("ScheduledExecutionDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("scheduled_execution_date");

                    b.Property<int?>("ScheduledId")
                        .HasColumnType("integer")
                        .HasColumnName("scheduled_id");

                    b.Property<int>("SessionStatus")
                        .HasColumnType("integer")
                        .HasColumnName("session_status");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.Property<bool>("UsingActiveHours")
                        .HasColumnType("boolean")
                        .HasColumnName("using_active_hours");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_sessions");

                    b.HasIndex("ComputerId")
                        .HasDatabaseName("ix_maintenance_sessions_computer_id");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_maintenance_sessions_created_by");

                    b.HasIndex("CreatedDate")
                        .HasDatabaseName("ix_maintenance_sessions_created_date");

                    b.HasIndex("JobId")
                        .HasDatabaseName("ix_maintenance_sessions_job_id");

                    b.HasIndex("PersonId")
                        .HasDatabaseName("ix_maintenance_sessions_person_id");

                    b.HasIndex("SessionStatus")
                        .HasDatabaseName("ix_maintenance_sessions_session_status");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_maintenance_sessions_tenant_id");

                    b.ToTable("maintenance_sessions", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceSessionStage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("JobId")
                        .HasMaxLength(36)
                        .HasColumnType("character varying(36)")
                        .HasColumnName("job_id");

                    b.Property<int>("MaintenanceSessionId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_session_id");

                    b.Property<int>("StageStatus")
                        .HasColumnType("integer")
                        .HasColumnName("stage_status");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_session_stages");

                    b.HasIndex("MaintenanceSessionId")
                        .HasDatabaseName("ix_maintenance_session_stages_maintenance_session_id");

                    b.ToTable("maintenance_session_stages", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int>("DatabaseType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("database_type");

                    b.Property<bool>("ExecuteSerially")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("execute_serially");

                    b.Property<bool>("GetEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("get_enabled");

                    b.Property<int?>("GetScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("get_script_id");

                    b.Property<int?>("GetScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("get_script_type");

                    b.Property<int?>("IconMediaId")
                        .HasColumnType("integer")
                        .HasColumnName("icon_media_id");

                    b.Property<bool>("IgnoreDuringAutomaticOnboarding")
                        .HasColumnType("boolean")
                        .HasColumnName("ignore_during_automatic_onboarding");

                    b.Property<Guid?>("IntegrationTypeId")
                        .HasColumnType("uuid")
                        .HasColumnName("integration_type_id");

                    b.Property<bool>("IsConfigurationTask")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_configuration_task");

                    b.Property<int>("MaintenanceTaskCategory")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("maintenance_task_category");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<string>("Notes")
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)")
                        .HasColumnName("notes");

                    b.Property<bool>("OnboardingOnly")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("onboarding_only");

                    b.Property<bool>("Recommended")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("recommended");

                    b.Property<bool>("SetEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("set_enabled");

                    b.Property<int?>("SetScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("set_script_id");

                    b.Property<int?>("SetScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("set_script_type");

                    b.Property<int?>("SupersededByTaskId")
                        .HasColumnType("integer")
                        .HasColumnName("superseded_by_task_id");

                    b.Property<int?>("SupersededByTaskMigrationScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("superseded_by_task_migration_script_id");

                    b.Property<int?>("SupersededByTaskMigrationScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("superseded_by_task_migration_script_type");

                    b.Property<int?>("SupersededByTaskType")
                        .HasColumnType("integer")
                        .HasColumnName("superseded_by_task_type");

                    b.Property<bool>("TestEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("test_enabled");

                    b.Property<int?>("TestScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("test_script_id");

                    b.Property<int?>("TestScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("test_script_type");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.Property<bool>("UseScriptParamBlock")
                        .HasColumnType("boolean")
                        .HasColumnName("use_script_param_block");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_tasks");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_maintenance_tasks_created_by");

                    b.HasIndex("IconMediaId")
                        .HasDatabaseName("ix_maintenance_tasks_icon_media_id");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_maintenance_tasks_updated_by");

                    b.ToTable("maintenance_tasks", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceTaskParameter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("DataType")
                        .HasColumnType("integer")
                        .HasColumnName("data_type");

                    b.Property<int?>("DefaultMediaDatabaseType")
                        .HasColumnType("integer")
                        .HasColumnName("default_media_database_type");

                    b.Property<int?>("DefaultMediaId")
                        .HasColumnType("integer")
                        .HasColumnName("default_media_id");

                    b.Property<string>("DefaultValue")
                        .HasColumnType("text")
                        .HasColumnName("default_value");

                    b.Property<bool>("Hidden")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("hidden");

                    b.Property<int>("MaintenanceTaskId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<int>("Order")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("order");

                    b.Property<bool>("Required")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("required");

                    b.Property<List<string>>("SelectableValues")
                        .HasColumnType("text[]")
                        .HasColumnName("selectable_values");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_task_parameters");

                    b.HasIndex("MaintenanceTaskId")
                        .HasDatabaseName("ix_maintenance_task_parameters_maintenance_task_id");

                    b.ToTable("maintenance_task_parameters", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceTaskParameterValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("AllowOverrideFromComputerOnboarding")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("allow_override_from_computer_onboarding");

                    b.Property<int>("DeploymentId")
                        .HasColumnType("integer")
                        .HasColumnName("deployment_id");

                    b.Property<int>("MaintenanceTaskId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_id");

                    b.Property<int>("MaintenanceTaskParameterId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_parameter_id");

                    b.Property<int>("MaintenanceTaskType")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_type");

                    b.Property<int?>("MediaDatabaseType")
                        .HasColumnType("integer")
                        .HasColumnName("media_database_type");

                    b.Property<int?>("MediaId")
                        .HasColumnType("integer")
                        .HasColumnName("media_id");

                    b.Property<string>("ParameterName")
                        .HasColumnType("text")
                        .HasColumnName("parameter_name");

                    b.Property<int?>("ParameterType")
                        .HasColumnType("integer")
                        .HasColumnName("parameter_type");

                    b.Property<string>("Value")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("value");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_task_parameter_values");

                    b.HasIndex("DeploymentId")
                        .HasDatabaseName("ix_maintenance_task_parameter_values_deployment_id");

                    b.ToTable("maintenance_task_parameter_values", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Media", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BlobReference")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("blob_reference");

                    b.Property<int>("Category")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("category");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int>("DatabaseType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("database_type");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("file_name");

                    b.Property<string>("MimeType")
                        .HasColumnType("text")
                        .HasColumnName("mime_type");

                    b.Property<string>("Name")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasDefaultValue("")
                        .HasColumnName("name");

                    b.Property<string>("PackageHash")
                        .HasColumnType("text")
                        .HasColumnName("package_hash");

                    b.Property<string>("RelativeCacheSourcePath")
                        .HasColumnType("text")
                        .HasColumnName("relative_cache_source_path");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_media");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_media_created_by");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_media_name");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_media_updated_by");

                    b.ToTable("media", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("AcknowledgedByUserAzureId")
                        .HasColumnType("text")
                        .HasColumnName("acknowledged_by_user_azure_id");

                    b.Property<string>("AcknowledgedByUserDisplayName")
                        .HasColumnType("text")
                        .HasColumnName("acknowledged_by_user_display_name");

                    b.Property<int?>("AcknowledgedByUserId")
                        .HasColumnType("integer")
                        .HasColumnName("acknowledged_by_user_id");

                    b.Property<int>("Acknowledgement")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("acknowledgement");

                    b.Property<bool>("AdminOnly")
                        .HasColumnType("boolean")
                        .HasColumnName("admin_only");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<JsonElement?>("InputData")
                        .HasColumnType("jsonb")
                        .HasColumnName("input_data");

                    b.Property<string>("ObjectId")
                        .HasColumnType("text")
                        .HasColumnName("object_id");

                    b.Property<int?>("OnlyForUserId")
                        .HasColumnType("integer")
                        .HasColumnName("only_for_user_id");

                    b.Property<JsonElement?>("OutputData")
                        .HasColumnType("jsonb")
                        .HasColumnName("output_data");

                    b.Property<bool>("Resolved")
                        .HasColumnType("boolean")
                        .HasColumnName("resolved");

                    b.Property<int>("Severity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(4)
                        .HasColumnName("severity");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("title");

                    b.Property<int?>("TriggeredByUserId")
                        .HasColumnType("integer")
                        .HasColumnName("triggered_by_user_id");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_notifications");

                    b.HasIndex("OnlyForUserId")
                        .HasDatabaseName("ix_notifications_only_for_user_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_notifications_tenant_id");

                    b.HasIndex("Type")
                        .HasDatabaseName("ix_notifications_type");

                    b.HasIndex("TenantId", "Acknowledgement")
                        .HasDatabaseName("ix_notifications_tenant_id_acknowledgement");

                    b.HasIndex("Type", "ObjectId")
                        .HasDatabaseName("ix_notifications_type_object_id");

                    b.HasIndex("OnlyForUserId", "TenantId", "TriggeredByUserId")
                        .HasDatabaseName("ix_notifications_only_for_user_id_tenant_id_triggered_by_user_");

                    b.ToTable("notifications", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Oauth2AccessToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("AccessTokenExpiresAtUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("access_token_expires_at_utc");

                    b.Property<string>("AccessTokenId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("access_token_id");

                    b.Property<bool>("AllowSilentRefresh")
                        .HasColumnType("boolean")
                        .HasColumnName("allow_silent_refresh");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("IdentityTokenId")
                        .HasColumnType("text")
                        .HasColumnName("identity_token_id");

                    b.Property<DateTime?>("RefreshTokenExpiresAtUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("refresh_token_expires_at_utc");

                    b.Property<string>("RefreshTokenId")
                        .HasColumnType("text")
                        .HasColumnName("refresh_token_id");

                    b.Property<string>("TenantPrincipalId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("tenant_principal_id");

                    b.Property<string>("TokenType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("token_type");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_oauth_access_tokens");

                    b.ToTable("oauth_access_tokens", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.OptionalTargetAssignmentApproval", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Approved")
                        .HasColumnType("integer")
                        .HasColumnName("approved");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int>("MaintenanceType")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_type");

                    b.Property<string>("Target")
                        .HasColumnType("text")
                        .HasColumnName("target");

                    b.Property<int>("TargetAssignmentId")
                        .HasColumnType("integer")
                        .HasColumnName("target_assignment_id");

                    b.Property<int>("TargetType")
                        .HasColumnType("integer")
                        .HasColumnName("target_type");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_optional_target_assignment_approvals");

                    b.ToTable("optional_target_assignment_approvals", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.PermissionClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("PermissionAction")
                        .HasColumnType("uuid")
                        .HasColumnName("permission_action");

                    b.Property<Guid>("PermissionSubject")
                        .HasColumnType("uuid")
                        .HasColumnName("permission_subject");

                    b.Property<int>("PermissionValue")
                        .HasColumnType("integer")
                        .HasColumnName("permission_value");

                    b.Property<int>("UserRoleId")
                        .HasColumnType("integer")
                        .HasColumnName("user_role_id");

                    b.HasKey("Id")
                        .HasName("pk_permission_claims");

                    b.HasIndex("UserRoleId")
                        .HasDatabaseName("ix_permission_claims_user_role_id");

                    b.ToTable("permission_claims", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Person", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AzurePrincipalId")
                        .HasMaxLength(36)
                        .HasColumnType("character varying(36)")
                        .HasColumnName("azure_principal_id");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("EmailAddress")
                        .IsRequired()
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("email_address");

                    b.Property<string>("FirstName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("first_name");

                    b.Property<string>("LastName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("last_name");

                    b.Property<string>("OnPremisesSecurityIdentifier")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("on_premises_security_identifier");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_persons");

                    b.HasIndex("AzurePrincipalId")
                        .HasDatabaseName("ix_persons_azure_principal_id");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_persons_created_by");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_persons_tenant_id");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_persons_updated_by");

                    b.ToTable("persons", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.PersonTag", b =>
                {
                    b.Property<int>("EntityId")
                        .HasColumnType("integer")
                        .HasColumnName("entity_id");

                    b.Property<int>("TagId")
                        .HasColumnType("integer")
                        .HasColumnName("tag_id");

                    b.HasKey("EntityId", "TagId")
                        .HasName("pk_person_tags");

                    b.HasIndex("TagId")
                        .HasDatabaseName("ix_person_tags_tag_id");

                    b.ToTable("person_tags", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Preferences.ApplicationPreferences", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("AllowNonAdminsAndNonMspUsersToUseTerminalsAndEditScripts")
                        .HasColumnType("boolean")
                        .HasColumnName("allow_non_admins_and_non_msp_users_to_use_terminals_and_edit_scripts");

                    b.Property<bool>("AllowNonAdminsToManageAssignments")
                        .HasColumnType("boolean")
                        .HasColumnName("allow_non_admins_to_manage_assignments");

                    b.Property<int>("AutomaticImmyBotReleaseUpdateHour")
                        .HasColumnType("integer")
                        .HasColumnName("automatic_immy_bot_release_update_hour");

                    b.Property<int>("DaysToWaitBeforeAutomaticImmyBotUpdate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(7)
                        .HasColumnName("days_to_wait_before_automatic_immy_bot_update");

                    b.Property<int?>("DefaultBrandingId")
                        .HasColumnType("integer")
                        .HasColumnName("default_branding_id");

                    b.Property<string>("DefaultEmailBccList")
                        .HasColumnType("text")
                        .HasColumnName("default_email_bcc_list");

                    b.Property<string>("DefaultTimeZone")
                        .HasColumnType("text")
                        .HasColumnName("default_time_zone");

                    b.Property<bool>("DisconnectLeastActiveEditorServiceWhenLimitReached")
                        .HasColumnType("boolean")
                        .HasColumnName("disconnect_least_active_editor_service_when_limit_reached");

                    b.Property<bool>("EnableAutomaticImmyBotReleaseUpdates")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_automatic_immy_bot_release_updates");

                    b.Property<bool>("EnableAzureUserSync")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_azure_user_sync");

                    b.Property<bool>("EnableBetaDynamicIntegrationMigrations")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("enable_beta_dynamic_integration_migrations");

                    b.Property<bool>("EnableEphemeralAgentDebugMode")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_ephemeral_agent_debug_mode");

                    b.Property<bool>("EnableHistoricalInventory")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_historical_inventory");

                    b.Property<bool>("EnableImmyBotRemoteControl")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_immy_bot_remote_control");

                    b.Property<bool>("EnableImmyBotRemoteControlRecording")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_immy_bot_remote_control_recording");

                    b.Property<bool>("EnableMaintenanceActionActivities")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("enable_maintenance_action_activities");

                    b.Property<bool>("EnableNiniteIntegration")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_ninite_integration");

                    b.Property<bool>("EnableNonEssentialDeviceInventory")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_non_essential_device_inventory");

                    b.Property<bool>("EnableOnboarding")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_onboarding");

                    b.Property<bool>("EnablePreflightScripts")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_preflight_scripts");

                    b.Property<bool>("EnableProviderAuditLogging")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("enable_provider_audit_logging");

                    b.Property<bool>("EnableRequestAccess")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_request_access");

                    b.Property<bool>("EnableSessionEmails")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_session_emails");

                    b.Property<bool>("EnableUserAffinitySync")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_user_affinity_sync");

                    b.Property<bool>("EnableUserImpersonation")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_user_impersonation");

                    b.Property<bool>("HideChocolateyPackages")
                        .HasColumnType("boolean")
                        .HasColumnName("hide_chocolatey_packages");

                    b.Property<string>("ImmyScriptPath")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text")
                        .HasColumnName("immy_script_path")
                        .HasDefaultValueSql("REPLACE(uuid_generate_v4()::text,'-','')");

                    b.Property<bool>("MspNonAdminsRequireChangeRequestsForCrossTenantDeployments")
                        .HasColumnType("boolean")
                        .HasColumnName("msp_non_admins_require_change_requests_for_cross_tenant_deployments");

                    b.Property<bool>("OverwriteExistingDeviceIfOSIsNew")
                        .HasColumnType("boolean")
                        .HasColumnName("overwrite_existing_device_if_os_is_new");

                    b.Property<bool>("RequireConsentForExternalSessionProviders")
                        .HasColumnType("boolean")
                        .HasColumnName("require_consent_for_external_session_providers");

                    b.Property<bool>("RunScheduledInventoryAsMaintenanceSessions")
                        .HasColumnType("boolean")
                        .HasColumnName("run_scheduled_inventory_as_maintenance_sessions");

                    b.Property<bool>("ShowGettingStartedWizard")
                        .HasColumnType("boolean")
                        .HasColumnName("show_getting_started_wizard");

                    b.Property<int>("StaleComputersLastAgentConnectionAgeDays")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(30)
                        .HasColumnName("stale_computers_last_agent_connection_age_days");

                    b.Property<bool>("UseImmyBotChocolateyFeed")
                        .HasColumnType("boolean")
                        .HasColumnName("use_immy_bot_chocolatey_feed");

                    b.HasKey("Id")
                        .HasName("pk_application_preferences");

                    b.HasIndex("DefaultBrandingId")
                        .HasDatabaseName("ix_application_preferences_default_branding_id");

                    b.ToTable("application_preferences", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Preferences.TenantPreferences", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BusinessHoursEnd")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text")
                        .HasDefaultValue("17:00")
                        .HasColumnName("business_hours_end");

                    b.Property<string>("BusinessHoursStart")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text")
                        .HasDefaultValue("09:00")
                        .HasColumnName("business_hours_start");

                    b.Property<string>("DefaultEmailBccList")
                        .HasColumnType("text")
                        .HasColumnName("default_email_bcc_list");

                    b.Property<bool?>("EnableImmyBotRemoteControl")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_immy_bot_remote_control");

                    b.Property<bool?>("EnableImmyBotRemoteControlRecording")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_immy_bot_remote_control_recording");

                    b.Property<bool>("EnableOnboarding")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("enable_onboarding");

                    b.Property<bool>("EnableOnboardingPatching")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_onboarding_patching");

                    b.Property<bool>("EnableSessionEmails")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_session_emails");

                    b.Property<bool>("EnableUserAffinitySync")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_user_affinity_sync");

                    b.Property<bool>("ExcludeFromCrossTenantDeploymentsAndSchedules")
                        .HasColumnType("boolean")
                        .HasColumnName("exclude_from_cross_tenant_deployments_and_schedules");

                    b.Property<bool>("OverwriteExistingDeviceIfOSIsNew")
                        .HasColumnType("boolean")
                        .HasColumnName("overwrite_existing_device_if_os_is_new");

                    b.Property<bool?>("RequireConsentForExternalSessionProviders")
                        .HasColumnType("boolean")
                        .HasColumnName("require_consent_for_external_session_providers");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("TimeZoneInfoId")
                        .HasColumnType("text")
                        .HasColumnName("time_zone_info_id");

                    b.HasKey("Id")
                        .HasName("pk_tenant_preferences");

                    b.HasIndex("TenantId")
                        .IsUnique()
                        .HasDatabaseName("ix_tenant_preferences_tenant_id");

                    b.ToTable("tenant_preferences", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Preferences.UserPreferences", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_user_preferences");

                    b.HasIndex("UserId")
                        .IsUnique()
                        .HasDatabaseName("ix_user_preferences_user_id");

                    b.ToTable("user_preferences", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderAgent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AgentVersion")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("agent_version");

                    b.Property<int?>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<DateTime>("DateAddedUTC")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("date_added_utc");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("DeletedReason")
                        .HasColumnType("text")
                        .HasColumnName("deleted_reason");

                    b.Property<string>("ExternalAgentId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("external_agent_id");

                    b.Property<string>("ExternalClientId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("external_client_id");

                    b.Property<JsonElement?>("InternalData")
                        .HasColumnType("jsonb")
                        .HasColumnName("internal_data");

                    b.Property<bool>("IsMemberOfInitialDeviceSync")
                        .HasColumnType("boolean")
                        .HasColumnName("is_member_of_initial_device_sync");

                    b.Property<bool>("IsOnline")
                        .HasColumnType("boolean")
                        .HasColumnName("is_online");

                    b.Property<DateTime>("LastUpdatedUTC")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("last_updated_utc");

                    b.Property<int>("ProviderLinkId")
                        .HasColumnType("integer")
                        .HasColumnName("provider_link_id");

                    b.Property<bool>("RequireManualIdentification")
                        .HasColumnType("boolean")
                        .HasColumnName("require_manual_identification");

                    b.Property<bool>("SupportsOnlineStatus")
                        .HasColumnType("boolean")
                        .HasColumnName("supports_online_status");

                    b.Property<bool>("SupportsRunningScripts")
                        .HasColumnType("boolean")
                        .HasColumnName("supports_running_scripts");

                    b.HasKey("Id")
                        .HasName("pk_provider_agents");

                    b.HasIndex("ComputerId")
                        .HasDatabaseName("ix_provider_agents_computer_id");

                    b.HasIndex("ProviderLinkId")
                        .HasDatabaseName("ix_provider_agents_provider_link_id");

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("ProviderLinkId"), new[] { "IsOnline", "LastUpdatedUTC", "ExternalClientId", "ExternalAgentId" });

                    b.HasIndex("ProviderLinkId", "ComputerId", "DeletedAt")
                        .IsUnique()
                        .HasDatabaseName("ix_provider_agents_provider_link_id_computer_id_deleted_at")
                        .HasFilter("deleted_at is null");

                    b.HasIndex("ProviderLinkId", "ExternalClientId", "ExternalAgentId")
                        .IsUnique()
                        .HasDatabaseName("ix_provider_agents_provider_link_id_external_client_id_externa");

                    b.ToTable("provider_agents", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderClient", b =>
                {
                    b.Property<int>("ProviderLinkId")
                        .HasColumnType("integer")
                        .HasColumnName("provider_link_id");

                    b.Property<string>("ExternalClientId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("external_client_id");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("ExternalClientName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("external_client_name");

                    b.Property<bool>("HasCompletedInitialAgentSync")
                        .HasColumnType("boolean")
                        .HasColumnName("has_completed_initial_agent_sync");

                    b.Property<JsonElement?>("InternalData")
                        .HasColumnType("jsonb")
                        .HasColumnName("internal_data");

                    b.Property<int?>("LinkedToTenantId")
                        .HasColumnType("integer")
                        .HasColumnName("linked_to_tenant_id");

                    b.Property<string>("Status")
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.Property<List<string>>("Types")
                        .HasColumnType("text[]")
                        .HasColumnName("types");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("ProviderLinkId", "ExternalClientId")
                        .HasName("pk_provider_clients");

                    b.HasIndex("LinkedToTenantId")
                        .HasDatabaseName("ix_provider_clients_linked_to_tenant_id");

                    b.ToTable("provider_clients", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderLink", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean")
                        .HasColumnName("disabled");

                    b.Property<List<string>>("ExcludedCapabilities")
                        .HasColumnType("text[]")
                        .HasColumnName("excluded_capabilities");

                    b.Property<int>("HealthStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(2)
                        .HasColumnName("health_status");

                    b.Property<string>("HealthStatusMessage")
                        .HasColumnType("text")
                        .HasColumnName("health_status_message");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<int>("OwnerTenantId")
                        .HasColumnType("integer")
                        .HasColumnName("owner_tenant_id");

                    b.Property<JsonElement>("ProviderTypeFormData")
                        .HasColumnType("jsonb")
                        .HasColumnName("provider_type_form_data");

                    b.Property<Guid>("ProviderTypeId")
                        .HasColumnType("uuid")
                        .HasColumnName("provider_type_id");

                    b.Property<int>("RunScriptPriority")
                        .HasColumnType("integer")
                        .HasColumnName("run_script_priority");

                    b.Property<int>("UnhealthyCount")
                        .HasColumnType("integer")
                        .HasColumnName("unhealthy_count");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_provider_links");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_provider_links_created_by");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("ix_provider_links_name");

                    b.HasIndex("OwnerTenantId")
                        .HasDatabaseName("ix_provider_links_owner_tenant_id");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_provider_links_updated_by");

                    b.ToTable("provider_links", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderLinkCrossReference", b =>
                {
                    b.Property<int>("ProviderLink1Id")
                        .HasColumnType("integer")
                        .HasColumnName("provider_link1id");

                    b.Property<int>("ProviderLink2Id")
                        .HasColumnType("integer")
                        .HasColumnName("provider_link2id");

                    b.Property<bool>("IsExternalClientLinkingEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_external_client_linking_enabled");

                    b.Property<bool>("IsProviderLink2InitializedFromProviderLink1")
                        .HasColumnType("boolean")
                        .HasColumnName("is_provider_link2initialized_from_provider_link1");

                    b.HasKey("ProviderLink1Id", "ProviderLink2Id")
                        .HasName("pk_provider_link_cross_reference");

                    b.HasIndex("ProviderLink2Id")
                        .HasDatabaseName("ix_provider_link_cross_reference_provider_link2id");

                    b.ToTable("provider_link_cross_reference", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderLinkInternalData", b =>
                {
                    b.Property<int>("ProviderLinkId")
                        .HasColumnType("integer")
                        .HasColumnName("provider_link_id");

                    b.Property<JsonElement?>("InternalData")
                        .HasColumnType("jsonb")
                        .HasColumnName("internal_data");

                    b.HasKey("ProviderLinkId")
                        .HasName("pk_provider_link_internal_data");

                    b.ToTable("provider_link_internal_data", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.RecommendedTargetAssignmentApproval", b =>
                {
                    b.Property<int>("GlobalTargetAssignmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("global_target_assignment_id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("GlobalTargetAssignmentId"));

                    b.Property<bool>("Approved")
                        .HasColumnType("boolean")
                        .HasColumnName("approved");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("GlobalTargetAssignmentId")
                        .HasName("pk_recommended_target_assignment_approvals");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_recommended_target_assignment_approvals_created_by");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_recommended_target_assignment_approvals_updated_by");

                    b.ToTable("recommended_target_assignment_approvals", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.RemoteControlRecording", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BlobName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("blob_name");

                    b.Property<int>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("MimeContentType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text")
                        .HasDefaultValue("application/octet-stream")
                        .HasColumnName("mime_content_type");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_remote_control_recordings");

                    b.HasIndex("BlobName")
                        .IsUnique()
                        .HasDatabaseName("ix_remote_control_recordings_blob_name");

                    b.HasIndex("ComputerId")
                        .HasDatabaseName("ix_remote_control_recordings_computer_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_remote_control_recordings_tenant_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_remote_control_recordings_user_id");

                    b.ToTable("remote_control_recordings", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Schedule", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int?>("Id"));

                    b.Property<bool>("AllowAccessToMSPResources")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("allow_access_to_msp_resources");

                    b.Property<bool>("AllowAccessToParentTenant")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("allow_access_to_parent_tenant");

                    b.Property<bool>("ApplyWindowsUpdates")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("apply_windows_updates");

                    b.Property<bool>("AutoConsentToReboots")
                        .HasColumnType("boolean")
                        .HasColumnName("auto_consent_to_reboots");

                    b.Property<int?>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("CustomCronExpression")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("custom_cron_expression");

                    b.Property<int?>("Day")
                        .HasColumnType("integer")
                        .HasColumnName("day");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean")
                        .HasColumnName("disabled");

                    b.Property<string>("MaintenanceIdentifier")
                        .HasColumnType("text")
                        .HasColumnName("maintenance_identifier");

                    b.Property<string>("MaintenanceTime")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("maintenance_time");

                    b.Property<int?>("MaintenanceType")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_type");

                    b.Property<int>("OfflineBehavior")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("offline_behavior");

                    b.Property<int>("PromptTimeoutAction")
                        .HasColumnType("integer")
                        .HasColumnName("prompt_timeout_action");

                    b.Property<int>("PromptTimeoutMinutes")
                        .HasColumnType("integer")
                        .HasColumnName("prompt_timeout_minutes");

                    b.Property<bool>("PropagateToChildTenants")
                        .HasColumnType("boolean")
                        .HasColumnName("propagate_to_child_tenants");

                    b.Property<Guid?>("ProviderClientGroupType")
                        .HasColumnType("uuid")
                        .HasColumnName("provider_client_group_type");

                    b.Property<Guid?>("ProviderDeviceGroupType")
                        .HasColumnType("uuid")
                        .HasColumnName("provider_device_group_type");

                    b.Property<int?>("ProviderLinkId")
                        .HasColumnType("integer")
                        .HasColumnName("provider_link_id");

                    b.Property<int>("RebootPreference")
                        .HasColumnType("integer")
                        .HasColumnName("reboot_preference");

                    b.Property<bool>("ScheduleExecutionAfterActiveHours")
                        .HasColumnType("boolean")
                        .HasColumnName("schedule_execution_after_active_hours");

                    b.Property<bool>("SendDetectionEmail")
                        .HasColumnType("boolean")
                        .HasColumnName("send_detection_email");

                    b.Property<bool>("SendDetectionEmailWhenAllActionsAreCompliant")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("send_detection_email_when_all_actions_are_compliant");

                    b.Property<bool>("SendFollowUpEmail")
                        .HasColumnType("boolean")
                        .HasColumnName("send_follow_up_email");

                    b.Property<bool>("SendFollowUpOnlyIfActionNeeded")
                        .HasColumnType("boolean")
                        .HasColumnName("send_follow_up_only_if_action_needed");

                    b.Property<bool>("ShowMaintenanceActions")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("show_maintenance_actions");

                    b.Property<bool>("ShowPostponeButton")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("show_postpone_button");

                    b.Property<bool>("ShowRunNowButton")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("show_run_now_button");

                    b.Property<bool>("SuppressRebootsDuringBusinessHours")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("suppress_reboots_during_business_hours");

                    b.Property<string>("Target")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("target");

                    b.Property<int>("TargetCategory")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("target_category");

                    b.Property<short>("TargetGroupFilter")
                        .HasColumnType("smallint")
                        .HasColumnName("target_group_filter");

                    b.Property<string>("TargetName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("target_name");

                    b.Property<int>("TargetType")
                        .HasColumnType("integer")
                        .HasColumnName("target_type");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Time")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("time");

                    b.Property<string>("TimeZoneInfoId")
                        .HasColumnType("text")
                        .HasColumnName("time_zone_info_id");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.Property<bool>("UseComputersTimezoneForExecution")
                        .HasColumnType("boolean")
                        .HasColumnName("use_computers_timezone_for_execution");

                    b.HasKey("Id")
                        .HasName("pk_schedules");

                    b.HasIndex("ComputerId")
                        .HasDatabaseName("ix_schedules_computer_id");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_schedules_created_by");

                    b.HasIndex("ProviderLinkId")
                        .HasDatabaseName("ix_schedules_provider_link_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_schedules_tenant_id");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_schedules_updated_by");

                    b.ToTable("schedules", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ScheduledEmail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Action")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("action");

                    b.Property<bool>("ActionTaken")
                        .HasColumnType("boolean")
                        .HasColumnName("action_taken");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("JobId")
                        .HasMaxLength(36)
                        .HasColumnType("character varying(36)")
                        .HasColumnName("job_id");

                    b.Property<int>("SessionId")
                        .HasColumnType("integer")
                        .HasColumnName("session_id");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_scheduled_emails");

                    b.ToTable("scheduled_emails", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Script", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("action");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<int>("OutputType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("output_type");

                    b.Property<string>("PublicStorageDownloadUrl")
                        .HasColumnType("text")
                        .HasColumnName("public_storage_download_url");

                    b.Property<string>("ScriptCacheName")
                        .HasColumnType("text")
                        .HasColumnName("script_cache_name");

                    b.Property<int>("ScriptCategory")
                        .HasColumnType("integer")
                        .HasColumnName("script_category");

                    b.Property<int>("ScriptExecutionContext")
                        .HasColumnType("integer")
                        .HasColumnName("script_execution_context");

                    b.Property<string>("ScriptHash")
                        .HasColumnType("text")
                        .HasColumnName("script_hash");

                    b.Property<int>("ScriptLanguage")
                        .HasColumnType("integer")
                        .HasColumnName("script_action_type");

                    b.Property<int>("ScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("script_type");

                    b.Property<int?>("Timeout")
                        .HasColumnType("integer")
                        .HasColumnName("timeout");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_scripts");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_scripts_created_by");

                    b.HasIndex("ScriptCacheName")
                        .IsUnique()
                        .HasDatabaseName("ix_scripts_script_cache_name");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_scripts_updated_by");

                    b.ToTable("scripts", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.SessionLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool?>("ImmediateUser")
                        .HasColumnType("boolean")
                        .HasColumnName("immediate_user");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("boolean")
                        .HasColumnName("is_primary");

                    b.Property<int?>("MaintenanceActionId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_action_id");

                    b.Property<int?>("MaintenanceActionStatus")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_action_status");

                    b.Property<int>("MaintenanceSessionId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_session_id");

                    b.Property<int?>("MaintenanceSessionStageId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_session_stage_id");

                    b.Property<string>("Message")
                        .HasColumnType("text")
                        .HasColumnName("message");

                    b.Property<string>("ParamBlockParameters")
                        .HasColumnType("text")
                        .HasColumnName("param_block_parameters");

                    b.Property<string>("ProgressActivity")
                        .HasColumnType("text")
                        .HasColumnName("progress_activity");

                    b.Property<bool>("ProgressCompleted")
                        .HasColumnType("boolean")
                        .HasColumnName("progress_completed");

                    b.Property<Guid?>("ProgressCorrelationId")
                        .HasColumnType("uuid")
                        .HasColumnName("progress_correlation_id");

                    b.Property<string>("ProgressCurrentOperation")
                        .HasColumnType("text")
                        .HasColumnName("progress_current_operation");

                    b.Property<decimal?>("ProgressPercentComplete")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)")
                        .HasColumnName("progress_percent_complete");

                    b.Property<double?>("ProgressSecondsRemaining")
                        .HasColumnType("double precision")
                        .HasColumnName("progress_seconds_remaining");

                    b.Property<string>("ProgressStatus")
                        .HasColumnType("text")
                        .HasColumnName("progress_status");

                    b.Property<string>("Script")
                        .HasColumnType("text")
                        .HasColumnName("script");

                    b.Property<int?>("ScriptExecutionContext")
                        .HasColumnType("integer")
                        .HasColumnName("script_execution_context");

                    b.Property<int?>("ScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("script_id");

                    b.Property<int?>("ScriptLanguage")
                        .HasColumnType("integer")
                        .HasColumnName("script_action_type");

                    b.Property<string>("ScriptOutput")
                        .HasColumnType("text")
                        .HasColumnName("script_output");

                    b.Property<string>("ScriptParameters")
                        .HasColumnType("text")
                        .HasColumnName("script_parameters");

                    b.Property<int?>("ScriptTimeout")
                        .HasColumnType("integer")
                        .HasColumnName("script_timeout");

                    b.Property<int?>("ScriptType")
                        .HasColumnType("integer")
                        .HasColumnName("script_type");

                    b.Property<int>("SessionLogType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("session_log_type");

                    b.Property<int?>("SessionPhaseId")
                        .HasColumnType("integer")
                        .HasColumnName("session_phase_id");

                    b.Property<DateTime>("Time")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("time");

                    b.Property<DateTime?>("UpdatedTime")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_time");

                    b.HasKey("Id")
                        .HasName("pk_session_logs");

                    b.HasIndex("MaintenanceActionId")
                        .HasDatabaseName("ix_session_logs_maintenance_action_id");

                    b.HasIndex("MaintenanceSessionId")
                        .HasDatabaseName("ix_session_logs_maintenance_session_id");

                    b.HasIndex("MaintenanceSessionStageId")
                        .HasDatabaseName("ix_session_logs_maintenance_session_stage_id");

                    b.HasIndex("SessionPhaseId")
                        .HasDatabaseName("ix_session_logs_session_phase_id");

                    b.ToTable("session_logs", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.SessionPhase", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("ActionProgressPhaseName")
                        .HasColumnType("integer")
                        .HasColumnName("action_progress_phase_name");

                    b.Property<DateTime?>("DateCompletedUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("date_completed_utc");

                    b.Property<DateTime?>("DateStartedUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("date_started_utc");

                    b.Property<int?>("MaintenanceActionId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_action_id");

                    b.Property<int>("MaintenanceSessionId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_session_id");

                    b.Property<int>("MaintenanceSessionStageId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_session_stage_id");

                    b.Property<string>("PhaseName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("phase_name");

                    b.Property<bool>("ProgressCompleted")
                        .HasColumnType("boolean")
                        .HasColumnName("progress_completed");

                    b.Property<decimal?>("ProgressPercentComplete")
                        .HasColumnType("numeric")
                        .HasColumnName("progress_percent_complete");

                    b.Property<string>("ProgressStatus")
                        .HasColumnType("text")
                        .HasColumnName("progress_status");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.HasKey("Id")
                        .HasName("pk_session_phases");

                    b.HasIndex("MaintenanceActionId")
                        .HasDatabaseName("ix_session_phases_maintenance_action_id");

                    b.HasIndex("MaintenanceSessionId")
                        .HasDatabaseName("ix_session_phases_maintenance_session_id");

                    b.HasIndex("MaintenanceSessionStageId")
                        .HasDatabaseName("ix_session_phases_maintenance_session_stage_id");

                    b.ToTable("session_phases", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.SmtpConfig", b =>
                {
                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<bool>("EnableSSL")
                        .HasColumnType("boolean")
                        .HasColumnName("enable_ssl");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean")
                        .HasColumnName("enabled");

                    b.Property<string>("Host")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("host");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("password_hash");

                    b.Property<int>("Port")
                        .HasColumnType("integer")
                        .HasColumnName("port");

                    b.Property<int>("Timeout")
                        .HasColumnType("integer")
                        .HasColumnName("timeout");

                    b.Property<bool>("UseAuthentication")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("use_authentication");

                    b.Property<string>("Username")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("username");

                    b.HasKey("TenantId")
                        .HasName("pk_smtp_configs");

                    b.ToTable("smtp_configs", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.SoftwarePrerequisite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ActionToPerform")
                        .HasColumnType("integer")
                        .HasColumnName("action_to_perform");

                    b.Property<int>("Condition")
                        .HasColumnType("integer")
                        .HasColumnName("condition");

                    b.Property<int>("LocalSoftwareId")
                        .HasColumnType("integer")
                        .HasColumnName("local_software_id");

                    b.Property<int>("SubjectQualifier")
                        .HasColumnType("integer")
                        .HasColumnName("subject_qualifier");

                    b.HasKey("Id")
                        .HasName("pk_software_prerequisite");

                    b.HasIndex("LocalSoftwareId")
                        .HasDatabaseName("ix_software_prerequisite_local_software_id");

                    b.ToTable("software_prerequisite", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Tag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Color")
                        .HasColumnType("text")
                        .HasColumnName("color");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("description");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_tags");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_tags_created_by");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("ix_tags_name");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_tags_updated_by");

                    b.ToTable("tags", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TargetAssignment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("AllowAccessToParentTenant")
                        .HasColumnType("boolean")
                        .HasColumnName("allow_access_to_parent_tenant");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int>("DatabaseType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("database_type");

                    b.Property<int?>("DesiredSoftwareState")
                        .HasColumnType("integer")
                        .HasColumnName("desired_software_state");

                    b.Property<bool>("Excluded")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("excluded");

                    b.Property<int?>("LicenseId")
                        .HasColumnType("integer")
                        .HasColumnName("license_id");

                    b.Property<string>("MaintenanceIdentifier")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("maintenance_identifier");

                    b.Property<int?>("MaintenanceTaskMode")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_mode");

                    b.Property<int>("MaintenanceType")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_type");

                    b.Property<bool>("OnboardingOnly")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("onboarding_only");

                    b.Property<string>("ParameterValueMigrationErrors")
                        .HasColumnType("text")
                        .HasColumnName("parameter_value_migration_errors");

                    b.Property<bool>("PropagateToChildTenants")
                        .HasColumnType("boolean")
                        .HasColumnName("propagate_to_child_tenants");

                    b.Property<Guid?>("ProviderClientGroupType")
                        .HasColumnType("uuid")
                        .HasColumnName("provider_client_group_type");

                    b.Property<Guid?>("ProviderDeviceGroupType")
                        .HasColumnType("uuid")
                        .HasColumnName("provider_device_group_type");

                    b.Property<int?>("ProviderLinkId")
                        .HasColumnType("integer")
                        .HasColumnName("provider_link_id");

                    b.Property<int?>("ProviderLinkIdForMaintenanceItem")
                        .HasColumnType("integer")
                        .HasColumnName("provider_link_id_for_maintenance_item");

                    b.Property<int?>("SoftwareId")
                        .HasColumnType("integer")
                        .HasColumnName("software_id");

                    b.Property<int?>("SoftwareProviderType")
                        .HasColumnType("integer")
                        .HasColumnName("software_provider_type");

                    b.Property<string>("SoftwareSemanticVersion")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("software_semantic_version");

                    b.Property<int>("SoftwareVersionId")
                        .HasColumnType("integer")
                        .HasColumnName("software_version_id");

                    b.Property<string>("Target")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("target");

                    b.Property<int>("TargetCategory")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("target_category");

                    b.Property<int>("TargetEnforcement")
                        .HasColumnType("integer")
                        .HasColumnName("target_enforcement");

                    b.Property<short>("TargetGroupFilter")
                        .HasColumnType("smallint")
                        .HasColumnName("target_group_filter");

                    b.Property<string>("TargetName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("target_name");

                    b.Property<int>("TargetType")
                        .HasColumnType("integer")
                        .HasColumnName("target_type");

                    b.Property<string>("TaskParameterValues")
                        .HasColumnType("jsonb")
                        .HasColumnName("task_parameter_values");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_target_assignments");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_target_assignments_created_by");

                    b.HasIndex("LicenseId")
                        .HasDatabaseName("ix_target_assignments_license_id");

                    b.HasIndex("ProviderLinkId")
                        .HasDatabaseName("ix_target_assignments_provider_link_id");

                    b.HasIndex("UpdatedBy")
                        .HasDatabaseName("ix_target_assignments_updated_by");

                    b.ToTable("target_assignments", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TargetAssignmentNotes", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)")
                        .HasColumnName("notes");

                    b.Property<int>("TargetAssignmentId")
                        .HasColumnType("integer")
                        .HasColumnName("target_assignment_id");

                    b.Property<string>("UpdatedByName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("updated_by_name");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_target_assignment_notes");

                    b.HasIndex("TargetAssignmentId")
                        .IsUnique()
                        .HasDatabaseName("ix_target_assignment_notes_target_assignment_id");

                    b.ToTable("target_assignment_notes", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TargetAssignmentVisibility", b =>
                {
                    b.Property<int>("TargetAssignmentId")
                        .HasColumnType("integer")
                        .HasColumnName("target_assignment_id");

                    b.Property<bool>("SelfService")
                        .HasColumnType("boolean")
                        .HasColumnName("self_service");

                    b.Property<bool>("TechnicianPod")
                        .HasColumnType("boolean")
                        .HasColumnName("technician_pod");

                    b.HasKey("TargetAssignmentId")
                        .HasName("pk_target_assignment_visibilities");

                    b.ToTable("target_assignment_visibilities", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Tenant", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("boolean")
                        .HasColumnName("active");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<bool>("IsMsp")
                        .HasColumnType("boolean")
                        .HasColumnName("is_msp");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<int?>("OwnerTenantId")
                        .HasColumnType("integer")
                        .HasColumnName("owner_tenant_id");

                    b.Property<int?>("ParentTenantId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_tenant_id");

                    b.Property<string>("Slug")
                        .HasColumnType("text")
                        .HasColumnName("slug");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_tenants");

                    b.HasIndex("OwnerTenantId")
                        .HasDatabaseName("ix_tenants_owner_tenant_id");

                    b.ToTable("tenants", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantDeletion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CompletionMessage")
                        .HasColumnType("text")
                        .HasColumnName("completion_message");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("status");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_tenant_deletions");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_tenant_deletions_tenant_id");

                    b.ToTable("tenant_deletions", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantMaintenanceTask", b =>
                {
                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int>("MaintenanceTaskId")
                        .HasColumnType("integer")
                        .HasColumnName("maintenance_task_id");

                    b.Property<int>("Relationship")
                        .HasColumnType("integer")
                        .HasColumnName("relationship");

                    b.HasKey("TenantId", "MaintenanceTaskId")
                        .HasName("pk_tenant_maintenance_tasks");

                    b.HasIndex("MaintenanceTaskId")
                        .HasDatabaseName("ix_tenant_maintenance_tasks_maintenance_task_id");

                    b.ToTable("tenant_maintenance_tasks", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantMedia", b =>
                {
                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int>("MediaId")
                        .HasColumnType("integer")
                        .HasColumnName("media_id");

                    b.Property<int>("Relationship")
                        .HasColumnType("integer")
                        .HasColumnName("relationship");

                    b.HasKey("TenantId", "MediaId")
                        .HasName("pk_tenant_media");

                    b.HasIndex("MediaId")
                        .HasDatabaseName("ix_tenant_media_media_id");

                    b.ToTable("tenant_media", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantPermissionAssignment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("PermissionAction")
                        .HasColumnType("uuid")
                        .HasColumnName("permission_action");

                    b.Property<Guid>("PermissionSubject")
                        .HasColumnType("uuid")
                        .HasColumnName("permission_subject");

                    b.Property<int>("PermissionValue")
                        .HasColumnType("integer")
                        .HasColumnName("permission_value");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int>("UserRoleId")
                        .HasColumnType("integer")
                        .HasColumnName("user_role_id");

                    b.HasKey("Id")
                        .HasName("pk_tenant_permission_assignments");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_tenant_permission_assignments_tenant_id");

                    b.HasIndex("UserRoleId")
                        .HasDatabaseName("ix_tenant_permission_assignments_user_role_id");

                    b.ToTable("tenant_permission_assignments", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantScript", b =>
                {
                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int>("ScriptId")
                        .HasColumnType("integer")
                        .HasColumnName("script_id");

                    b.Property<int>("Relationship")
                        .HasColumnType("integer")
                        .HasColumnName("relationship");

                    b.HasKey("TenantId", "ScriptId")
                        .HasName("pk_tenant_scripts");

                    b.HasIndex("ScriptId")
                        .HasDatabaseName("ix_tenant_scripts_script_id");

                    b.ToTable("tenant_scripts", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantSearch", b =>
                {
                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int>("SearchId")
                        .HasColumnType("integer")
                        .HasColumnName("search_id");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("TenantId", "SearchId")
                        .HasName("pk_tenant_searches");

                    b.ToTable("tenant_searches", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantSoftware", b =>
                {
                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int>("SoftwareId")
                        .HasColumnType("integer")
                        .HasColumnName("software_id");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("TenantId", "SoftwareId")
                        .HasName("pk_tenant_software");

                    b.HasIndex("SoftwareId")
                        .HasDatabaseName("ix_tenant_software_software_id");

                    b.ToTable("tenant_software", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantTag", b =>
                {
                    b.Property<int>("EntityId")
                        .HasColumnType("integer")
                        .HasColumnName("entity_id");

                    b.Property<int>("TagId")
                        .HasColumnType("integer")
                        .HasColumnName("tag_id");

                    b.HasKey("EntityId", "TagId")
                        .HasName("pk_tenant_tags");

                    b.HasIndex("TagId")
                        .HasDatabaseName("ix_tenant_tags_tag_id");

                    b.ToTable("tenant_tags", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantTagAuthorization", b =>
                {
                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TagId")
                        .HasColumnType("integer")
                        .HasColumnName("tag_id");

                    b.Property<int>("Relationship")
                        .HasColumnType("integer")
                        .HasColumnName("relationship");

                    b.HasKey("TenantId", "TagId")
                        .HasName("pk_tenant_tag_authorizations");

                    b.HasIndex("TagId")
                        .HasDatabaseName("ix_tenant_tag_authorizations_tag_id");

                    b.ToTable("tenant_tag_authorizations", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TimelineEvent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Data")
                        .HasColumnType("text")
                        .HasColumnName("data");

                    b.Property<DateTime>("DateUTC")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("date_utc");

                    b.Property<int>("EventType")
                        .HasColumnType("integer")
                        .HasColumnName("event_type");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("message");

                    b.Property<string>("ObjectId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("object_id");

                    b.Property<int>("ObjectType")
                        .HasColumnType("integer")
                        .HasColumnName("object_type");

                    b.HasKey("Id")
                        .HasName("pk_timeline_events");

                    b.HasIndex("DateUTC")
                        .HasDatabaseName("ix_timeline_events_date_utc");

                    b.ToTable("timeline_events", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("CanManageCrossTenantDeployments")
                        .HasColumnType("boolean")
                        .HasColumnName("can_manage_cross_tenant_deployments");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<DateTime?>("ExpirationDateUTC")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("expiration_date_utc");

                    b.Property<bool>("HasManagementAccess")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("has_management_access");

                    b.Property<bool>("IsAdmin")
                        .HasColumnType("boolean")
                        .HasColumnName("is_admin");

                    b.Property<int?>("PersonId")
                        .HasColumnType("integer")
                        .HasColumnName("person_id");

                    b.Property<string>("ServicePrincipalId")
                        .HasMaxLength(36)
                        .HasColumnType("character varying(36)")
                        .HasColumnName("service_principal_id");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id")
                        .HasName("pk_users");

                    b.HasIndex("PersonId")
                        .IsUnique()
                        .HasDatabaseName("ix_users_person_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_users_tenant_id");

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.UserAffinity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ComputerId")
                        .HasColumnType("integer")
                        .HasColumnName("computer_id");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("date");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer")
                        .HasColumnName("person_id");

                    b.HasKey("Id")
                        .HasName("pk_user_affinities");

                    b.HasIndex("ComputerId")
                        .HasDatabaseName("ix_user_affinities_computer_id");

                    b.HasIndex("PersonId")
                        .HasDatabaseName("ix_user_affinities_person_id");

                    b.ToTable("user_affinities", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.UserImpersonation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("ExpiresAtUtc")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("expires_at_utc");

                    b.Property<int>("ImpersonatingUserId")
                        .HasColumnType("integer")
                        .HasColumnName("impersonating_user_id");

                    b.Property<int>("ImpersonatorUserId")
                        .HasColumnType("integer")
                        .HasColumnName("impersonator_user_id");

                    b.HasKey("Id")
                        .HasName("pk_user_impersonations");

                    b.HasIndex("ImpersonatingUserId")
                        .HasDatabaseName("ix_user_impersonations_impersonating_user_id");

                    b.HasIndex("ImpersonatorUserId")
                        .HasDatabaseName("ix_user_impersonations_impersonator_user_id");

                    b.ToTable("user_impersonations", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.UserRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_user_roles");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_user_roles_tenant_id");

                    b.ToTable("user_roles", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.UserSilencedNotification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("NotificationObjectId")
                        .HasColumnType("text")
                        .HasColumnName("notification_object_id");

                    b.Property<int>("NotificationType")
                        .HasColumnType("integer")
                        .HasColumnName("notification_type");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_user_silenced_notifications");

                    b.HasIndex("UserId", "NotificationType", "NotificationObjectId")
                        .IsUnique()
                        .HasDatabaseName("ix_user_silenced_notifications_user_id_notification_type_notif");

                    b.ToTable("user_silenced_notifications", (string)null);
                });

            modelBuilder.Entity("UserUserRole", b =>
                {
                    b.Property<int>("UserRolesId")
                        .HasColumnType("integer")
                        .HasColumnName("user_roles_id");

                    b.Property<int>("UsersId")
                        .HasColumnType("integer")
                        .HasColumnName("users_id");

                    b.HasKey("UserRolesId", "UsersId")
                        .HasName("pk_user_user_role");

                    b.HasIndex("UsersId")
                        .HasDatabaseName("ix_user_user_role_users_id");

                    b.ToTable("user_user_role", (string)null);
                });

            modelBuilder.Entity("Immybot.Backend.Domain.MaintenanceActionActivity", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceAction", "MaintenanceAction")
                        .WithMany("Activities")
                        .HasForeignKey("MaintenanceActionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_maintenance_action_activities_maintenance_actions_maintenan");

                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceSession", "MaintenanceSession")
                        .WithMany("Activities")
                        .HasForeignKey("MaintenanceSessionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_maintenance_action_activities_maintenance_sessions_maintena");

                    b.Navigation("MaintenanceAction");

                    b.Navigation("MaintenanceSession");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AccessRequest", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "AcknowledgedByUser")
                        .WithMany("AccessRequestAcknowledgements")
                        .HasForeignKey("AcknowledgedByUserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_access_requests_users_acknowledged_by_user_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Person", "Person")
                        .WithMany("AccessRequests")
                        .HasForeignKey("PersonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_access_requests_persons_person_id");

                    b.Navigation("AcknowledgedByUser");

                    b.Navigation("Person");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ActiveSession", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceSession", "MaintenanceSession")
                        .WithOne("ActiveSession")
                        .HasForeignKey("Immybot.Backend.Domain.Models.ActiveSession", "MaintenanceSessionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_active_sessions_maintenance_sessions_maintenance_session_id");

                    b.Navigation("MaintenanceSession");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AgentIdentificationFailure", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Computer", "Computer")
                        .WithMany("AgentIdentificationFailures")
                        .HasForeignKey("ComputerId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_agent_identification_failures_computers_computer_id");

                    b.HasOne("Immybot.Backend.Domain.Models.ProviderAgent", "PendingAgent")
                        .WithMany("IdentificationFailures")
                        .HasForeignKey("PendingAgentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_agent_identification_failures_provider_agents_pending_agent");

                    b.Navigation("Computer");

                    b.Navigation("PendingAgent");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AgentIdentificationLog", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.ProviderAgent", "ProviderAgent")
                        .WithMany("IdentificationLogs")
                        .HasForeignKey("ProviderAgentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_agent_identification_logs_provider_agents_provider_agent_id");

                    b.Navigation("ProviderAgent");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AzureTenant", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.AzureTenant", "ParentPartner")
                        .WithMany()
                        .HasForeignKey("PartnerPrincipalId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_azure_tenants_azure_tenants_partner_principal_id");

                    b.OwnsOne("Immybot.Backend.Domain.Models.AzureSyncResult", "LastGetTenantInfoSyncResult", b1 =>
                        {
                            b1.Property<string>("AzureTenantPrincipalId")
                                .HasColumnType("character varying(36)")
                                .HasColumnName("principal_id");

                            b1.Property<DateTime>("AttemptDateUtc")
                                .HasColumnType("timestamp without time zone")
                                .HasColumnName("last_get_tenant_info_sync_result_attempt_date_utc");

                            b1.Property<Guid?>("AttemptFailedErrorId")
                                .HasColumnType("uuid")
                                .HasColumnName("last_get_tenant_info_sync_result_attempt_failed_error_id");

                            b1.HasKey("AzureTenantPrincipalId");

                            b1.ToTable("azure_tenants");

                            b1.WithOwner()
                                .HasForeignKey("AzureTenantPrincipalId")
                                .HasConstraintName("fk_azure_tenants_azure_tenants_principal_id");
                        });

                    b.OwnsOne("Immybot.Backend.Domain.Models.AzureSyncResult", "LastGetUsersSyncResult", b1 =>
                        {
                            b1.Property<string>("AzureTenantPrincipalId")
                                .HasColumnType("character varying(36)")
                                .HasColumnName("principal_id");

                            b1.Property<DateTime>("AttemptDateUtc")
                                .HasColumnType("timestamp without time zone")
                                .HasColumnName("last_get_users_sync_result_attempt_date_utc");

                            b1.Property<Guid?>("AttemptFailedErrorId")
                                .HasColumnType("uuid")
                                .HasColumnName("last_get_users_sync_result_attempt_failed_error_id");

                            b1.HasKey("AzureTenantPrincipalId");

                            b1.ToTable("azure_tenants");

                            b1.WithOwner()
                                .HasForeignKey("AzureTenantPrincipalId")
                                .HasConstraintName("fk_azure_tenants_azure_tenants_principal_id");
                        });

                    b.OwnsOne("Immybot.Backend.Domain.Models.AzureTenantInfo", "InfoSyncedFromAzure", b1 =>
                        {
                            b1.Property<string>("AzureTenantPrincipalId")
                                .HasColumnType("character varying(36)")
                                .HasColumnName("principal_id");

                            b1.Property<string>("DefaultDomainName")
                                .HasColumnType("text")
                                .HasColumnName("info_synced_from_azure_default_domain_name");

                            b1.Property<List<string>>("DomainNames")
                                .HasColumnType("text[]")
                                .HasColumnName("info_synced_from_azure_domain_names");

                            b1.Property<string>("TenantName")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("info_synced_from_azure_tenant_name");

                            b1.HasKey("AzureTenantPrincipalId");

                            b1.ToTable("azure_tenants");

                            b1.WithOwner()
                                .HasForeignKey("AzureTenantPrincipalId")
                                .HasConstraintName("fk_azure_tenants_azure_tenants_principal_id");
                        });

                    b.Navigation("InfoSyncedFromAzure");

                    b.Navigation("LastGetTenantInfoSyncResult");

                    b.Navigation("LastGetUsersSyncResult");

                    b.Navigation("ParentPartner");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AzureTenantLink", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.AzureTenant", "AzureTenant")
                        .WithMany("AzureTenantLinks")
                        .HasForeignKey("AzTenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_azure_tenant_links_azure_tenants_az_tenant_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithOne("AzureTenantLink")
                        .HasForeignKey("Immybot.Backend.Domain.Models.AzureTenantLink", "ImmyTenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_azure_tenant_links_tenants_immy_tenant_id");

                    b.Navigation("AzureTenant");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AzureTenantLinkDomainFilter", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.AzureTenantLink", null)
                        .WithMany("LimitToDomains")
                        .HasForeignKey("ImmyTenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_azure_tenant_link_domain_filters_azure_tenant_links_immy_te");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Branding", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedBrandings")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_brandings_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("Brandings")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_brandings_tenants_tenant_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedBrandings")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_brandings_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Tenant");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ChangeRequest", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedChangeRequests")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_change_requests_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.Script", "Script")
                        .WithMany("ChangeRequests")
                        .HasForeignKey("ScriptId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_change_requests_scripts_script_id");

                    b.HasOne("Immybot.Backend.Domain.Models.TargetAssignment", "TargetAssignment")
                        .WithMany("ChangeRequests")
                        .HasForeignKey("TargetAssignmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_change_requests_target_assignments_target_assignment_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedChangeRequests")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_change_requests_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Script");

                    b.Navigation("TargetAssignment");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ChangeRequestComment", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.ChangeRequest", "ChangeRequest")
                        .WithMany("Comments")
                        .HasForeignKey("ChangeRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_change_request_comments_change_requests_change_request_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedChangeRequestComments")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_change_request_comments_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedChangeRequestComments")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_change_request_comments_users_updated_by");

                    b.Navigation("ChangeRequest");

                    b.Navigation("CreatedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Computer", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Person", "PrimaryPerson")
                        .WithMany("PrimaryComputers")
                        .HasForeignKey("PrimaryPersonId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_computers_persons_primary_person_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Computer", "SuccessorComputer")
                        .WithMany("PredecessorComputers")
                        .HasForeignKey("SuccessorComputerId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_computers_computers_successor_computer_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("Computers")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_computers_tenants_tenant_id");

                    b.Navigation("PrimaryPerson");

                    b.Navigation("SuccessorComputer");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ComputerInventoryTaskScriptResult", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Computer", "Computer")
                        .WithMany("LatestInventoryScriptResults")
                        .HasForeignKey("ComputerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_computer_inventory_task_script_results_computers_computer_id");

                    b.Navigation("Computer");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ComputerLatestProviderEvent", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Computer", "Computer")
                        .WithOne("LatestProviderEvent")
                        .HasForeignKey("Immybot.Backend.Domain.Models.ComputerLatestProviderEvent", "ComputerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_computer_latest_provider_events_computers_computer_id");

                    b.Navigation("Computer");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ComputerPerson", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Computer", "Computer")
                        .WithMany("AdditionalPersons")
                        .HasForeignKey("ComputerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_computer_persons_computers_computer_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Person", "Person")
                        .WithMany("AdditionalComputers")
                        .HasForeignKey("PersonId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_computer_persons_persons_person_id");

                    b.Navigation("Computer");

                    b.Navigation("Person");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ComputerTag", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Computer", "Computer")
                        .WithMany("ComputerTags")
                        .HasForeignKey("EntityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_computer_tags_computers_entity_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tag", "Tag")
                        .WithMany("ComputerTags")
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_computer_tags_tags_tag_id");

                    b.Navigation("Computer");

                    b.Navigation("Tag");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.DetectedComputerSoftware", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Computer", "Computer")
                        .WithMany("DetectedSoftware")
                        .HasForeignKey("ComputerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_detected_computer_software_computers_computer_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Person", "PrimaryPerson")
                        .WithMany("DetectedComputerSoftware")
                        .HasForeignKey("PrimaryPersonId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_detected_computer_software_persons_primary_person_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("DetectedComputerSoftware")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_detected_computer_software_tenants_tenant_id");

                    b.Navigation("Computer");

                    b.Navigation("PrimaryPerson");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.DynamicIntegrationType", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Media", "Logo")
                        .WithMany("DynamicIntegrationTypes")
                        .HasForeignKey("LogoId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .IsRequired()
                        .HasConstraintName("fk_dynamic_integration_types_media_logo_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Script", "Script")
                        .WithMany()
                        .HasForeignKey("ScriptId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_dynamic_integration_types_scripts_script_id");

                    b.Navigation("Logo");

                    b.Navigation("Script");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.FeatureUsage", b =>
                {
                    b.OwnsMany("Immybot.Backend.Domain.Models.FeatureUsageItem", "Items", b1 =>
                        {
                            b1.Property<string>("feature_id")
                                .HasColumnType("text")
                                .HasColumnName("feature_id");

                            b1.Property<DateTime>("feature_track_start_date_utc")
                                .HasColumnType("timestamp without time zone")
                                .HasColumnName("feature_track_start_date_utc");

                            b1.Property<string>("Value")
                                .HasColumnType("text")
                                .HasColumnName("value");

                            b1.Property<int>("Count")
                                .HasColumnType("integer")
                                .HasColumnName("count");

                            b1.HasKey("feature_id", "feature_track_start_date_utc", "Value")
                                .HasName("pk_feature_usage_items");

                            b1.ToTable("feature_usage_items", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("feature_id", "feature_track_start_date_utc")
                                .HasConstraintName("fk_feature_usage_items_feature_usages_feature_id_feature_track");
                        });

                    b.Navigation("Items");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.InventoryTaskScript", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.InventoryTask", "InventoryTask")
                        .WithMany("Scripts")
                        .HasForeignKey("InventoryTaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_inventory_task_scripts_inventory_tasks_inventory_task_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Script", "Script")
                        .WithOne("InventoryTaskScript")
                        .HasForeignKey("Immybot.Backend.Domain.Models.InventoryTaskScript", "ScriptId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_inventory_task_scripts_scripts_script_id");

                    b.Navigation("InventoryTask");

                    b.Navigation("Script");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.License", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedLicenses")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_licenses_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("Licenses")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_licenses_tenants_tenant_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedLicenses")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_licenses_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Tenant");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.LocalSoftware", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedSoftware")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_software_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.Media", "SoftwareIcon")
                        .WithMany("LocalSoftware")
                        .HasForeignKey("SoftwareIconMediaId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_software_media_software_icon_media_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedSoftware")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_software_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("SoftwareIcon");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.LocalSoftwareVersion", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedSoftwareVersions")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_software_versions_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.LocalSoftware", "Software")
                        .WithMany("SoftwareVersions")
                        .HasForeignKey("SoftwareId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_software_versions_software_software_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedSoftwareVersions")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_software_versions_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Software");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceAction", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Computer", "Computer")
                        .WithMany("Actions")
                        .HasForeignKey("ComputerId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_maintenance_actions_computers_computer_id");

                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceSession", "MaintenanceSession")
                        .WithMany("MaintenanceActions")
                        .HasForeignKey("MaintenanceSessionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_maintenance_actions_maintenance_sessions_maintenance_sessio");

                    b.Navigation("Computer");

                    b.Navigation("MaintenanceSession");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceActionDependency", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceAction", "Dependent")
                        .WithMany("DependsOn")
                        .HasForeignKey("DependentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_maintenance_action_dependencies_maintenance_actions_depende");

                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceAction", "DependsOn")
                        .WithMany("Dependents")
                        .HasForeignKey("DependsOnId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_maintenance_action_dependencies_maintenance_actions_depends");

                    b.Navigation("Dependent");

                    b.Navigation("DependsOn");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceSession", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Computer", "Computer")
                        .WithMany("Sessions")
                        .HasForeignKey("ComputerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_maintenance_sessions_computers_computer_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedMaintenanceSessions")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_maintenance_sessions_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.Person", "Person")
                        .WithMany("PersonSessions")
                        .HasForeignKey("PersonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_maintenance_sessions_persons_person_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("MaintenanceSessions")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_maintenance_sessions_tenants_tenant_id");

                    b.Navigation("Computer");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Person");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceSessionStage", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceSession", "MaintenanceSession")
                        .WithMany("Stages")
                        .HasForeignKey("MaintenanceSessionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_maintenance_session_stages_maintenance_sessions_maintenance");

                    b.Navigation("MaintenanceSession");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceTask", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedMaintenanceTasks")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_maintenance_tasks_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.Media", "Icon")
                        .WithMany("IconForMaintenanceTasks")
                        .HasForeignKey("IconMediaId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_maintenance_tasks_media_icon_media_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedMaintenanceTasks")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_maintenance_tasks_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Icon");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceTaskParameter", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceTask", "MaintenanceTask")
                        .WithMany("Parameters")
                        .HasForeignKey("MaintenanceTaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_maintenance_task_parameters_maintenance_tasks_maintenance_t");

                    b.Navigation("MaintenanceTask");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceTaskParameterValue", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.TargetAssignment", "Deployment")
                        .WithMany("MaintenanceTaskParameterValues")
                        .HasForeignKey("DeploymentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_maintenance_task_parameter_values_target_assignments_deploy");

                    b.Navigation("Deployment");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Media", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedMedia")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_media_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedMedia")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_media_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Oauth2AccessToken", b =>
                {
                    b.OwnsOne("Immybot.Backend.Domain.Models.OauthConsentData", "ConsentData", b1 =>
                        {
                            b1.Property<int>("Oauth2AccessTokenId")
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            b1.Property<string>("AuthorizationEndpoint")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("consent_data_authorization_endpoint");

                            b1.Property<string>("ClientId")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("consent_data_client_id");

                            b1.Property<string>("ExtraQueryParameters")
                                .IsRequired()
                                .HasColumnType("jsonb")
                                .HasColumnName("consent_data_extra_query_parameters");

                            b1.Property<string>("Resource")
                                .HasColumnType("text")
                                .HasColumnName("consent_data_resource");

                            b1.Property<string>("Scopes")
                                .HasColumnType("text")
                                .HasColumnName("consent_data_scopes");

                            b1.Property<string>("TokenEndpoint")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("consent_data_token_endpoint");

                            b1.HasKey("Oauth2AccessTokenId");

                            b1.ToTable("oauth_access_tokens");

                            b1.WithOwner()
                                .HasForeignKey("Oauth2AccessTokenId")
                                .HasConstraintName("fk_oauth_access_tokens_oauth_access_tokens_id");
                        });

                    b.Navigation("ConsentData")
                        .IsRequired();
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.PermissionClaim", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.UserRole", "UserRole")
                        .WithMany("PermissionClaims")
                        .HasForeignKey("UserRoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_permission_claims_user_roles_user_role_id");

                    b.Navigation("UserRole");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Person", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedPersons")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_persons_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("Persons")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_persons_tenants_tenant_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedPersons")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_persons_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Tenant");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.PersonTag", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Person", "Person")
                        .WithMany("PersonTags")
                        .HasForeignKey("EntityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_person_tags_persons_entity_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tag", "Tag")
                        .WithMany("PersonTags")
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_person_tags_tags_tag_id");

                    b.Navigation("Person");

                    b.Navigation("Tag");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Preferences.ApplicationPreferences", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Branding", "DefaultBranding")
                        .WithMany()
                        .HasForeignKey("DefaultBrandingId")
                        .HasConstraintName("fk_application_preferences_brandings_default_branding_id");

                    b.OwnsOne("Immybot.Backend.Domain.Models.Preferences.DefaultScriptTimeouts", "DefaultScriptTimeouts", b1 =>
                        {
                            b1.Property<int>("ApplicationPreferencesId")
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            b1.Property<int>("Action")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(1800)
                                .HasColumnName("default_script_timeouts_action");

                            b1.Property<int>("AutoUpdateJob")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(21600)
                                .HasColumnName("default_script_timeouts_auto_update_job");

                            b1.Property<int>("Detection")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(120)
                                .HasColumnName("default_script_timeouts_detection");

                            b1.Property<int>("DynamicVersions")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(120)
                                .HasColumnName("default_script_timeouts_dynamic_versions");

                            b1.Property<int>("Install")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(1800)
                                .HasColumnName("default_script_timeouts_install");

                            b1.Property<int>("Uninstall")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(1800)
                                .HasColumnName("default_script_timeouts_uninstall");

                            b1.Property<int>("Upgrade")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasDefaultValue(1800)
                                .HasColumnName("default_script_timeouts_upgrade");

                            b1.HasKey("ApplicationPreferencesId");

                            b1.ToTable("application_preferences");

                            b1.WithOwner()
                                .HasForeignKey("ApplicationPreferencesId")
                                .HasConstraintName("fk_application_preferences_application_preferences_id");
                        });

                    b.Navigation("DefaultBranding");

                    b.Navigation("DefaultScriptTimeouts")
                        .IsRequired();
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Preferences.TenantPreferences", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithOne("TenantPreferences")
                        .HasForeignKey("Immybot.Backend.Domain.Models.Preferences.TenantPreferences", "TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_preferences_tenants_tenant_id");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderAgent", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Computer", "Computer")
                        .WithMany("Agents")
                        .HasForeignKey("ComputerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_provider_agents_computers_computer_id");

                    b.HasOne("Immybot.Backend.Domain.Models.ProviderLink", "ProviderLink")
                        .WithMany("Agents")
                        .HasForeignKey("ProviderLinkId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_provider_agents_provider_links_provider_link_id");

                    b.HasOne("Immybot.Backend.Domain.Models.ProviderClient", "ProviderClient")
                        .WithMany("ProviderAgents")
                        .HasForeignKey("ProviderLinkId", "ExternalClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_provider_agents_provider_clients_provider_link_id_external_");

                    b.OwnsOne("Immybot.Backend.Domain.Models.DeviceDetails", "DeviceDetails", b1 =>
                        {
                            b1.Property<int>("ProviderAgentId")
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            b1.Property<string>("AzureTenantId")
                                .HasColumnType("text")
                                .HasColumnName("device_details_azure_tenant_id");

                            b1.Property<int[]>("ChassisTypes")
                                .HasColumnType("integer[]")
                                .HasColumnName("device_details_chassis_types");

                            b1.Property<Guid?>("DeviceId")
                                .HasColumnType("uuid")
                                .HasColumnName("device_details_device_id");

                            b1.Property<string>("DeviceName")
                                .HasColumnType("text")
                                .HasColumnName("device_details_device_name");

                            b1.Property<string>("Domain")
                                .HasColumnType("text")
                                .HasColumnName("device_details_domain");

                            b1.Property<bool>("IsSandbox")
                                .HasColumnType("boolean")
                                .HasColumnName("device_details_is_sandbox");

                            b1.Property<string>("MachineId")
                                .HasColumnType("text")
                                .HasColumnName("device_details_machine_id");

                            b1.Property<string>("Manufacturer")
                                .HasColumnType("text")
                                .HasColumnName("device_details_manufacturer");

                            b1.Property<DateTime?>("OSInstallDateUTC")
                                .HasColumnType("timestamp without time zone")
                                .HasColumnName("device_details_os_install_date_utc");

                            b1.Property<string>("OperatingSystemName")
                                .HasColumnType("text")
                                .HasColumnName("device_details_operating_system_name");

                            b1.Property<string>("SerialNumber")
                                .HasColumnType("text")
                                .HasColumnName("device_details_serial_number");

                            b1.HasKey("ProviderAgentId");

                            b1.ToTable("provider_agents");

                            b1.WithOwner()
                                .HasForeignKey("ProviderAgentId")
                                .HasConstraintName("fk_provider_agents_provider_agents_id");
                        });

                    b.OwnsOne("Immybot.Backend.Domain.Providers.AgentOnboardingOptions", "OnboardingOptions", b1 =>
                        {
                            b1.Property<int>("ProviderAgentId")
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            b1.Property<List<int>>("AdditionalPersonIds")
                                .HasColumnType("integer[]")
                                .HasColumnName("onboarding_options_additional_person_ids");

                            b1.Property<bool>("AutomaticallyOnboard")
                                .HasColumnType("boolean")
                                .HasColumnName("onboarding_options_automatically_onboard");

                            b1.Property<bool>("IsDevLab")
                                .HasColumnType("boolean")
                                .HasColumnName("onboarding_options_is_dev_lab");

                            b1.Property<string>("OnboardingCorrelationId")
                                .HasColumnType("text")
                                .HasColumnName("onboarding_options_onboarding_correlation_id");

                            b1.Property<bool>("OnboardingSessionAutoConsentToReboots")
                                .HasColumnType("boolean")
                                .HasColumnName("onboarding_options_onboarding_session_auto_consent_to_reboots");

                            b1.Property<int?>("OnboardingSessionPromptTimeoutAction")
                                .HasColumnType("integer")
                                .HasColumnName("onboarding_options_onboarding_session_prompt_timeout_action");

                            b1.Property<int>("OnboardingSessionPromptTimeoutMinutes")
                                .HasColumnType("integer")
                                .HasColumnName("onboarding_options_onboarding_session_prompt_timeout_minutes");

                            b1.Property<int?>("OnboardingSessionRebootPreference")
                                .HasColumnType("integer")
                                .HasColumnName("onboarding_options_onboarding_session_reboot_preference");

                            b1.Property<bool>("OnboardingSessionSendFollowUpEmail")
                                .HasColumnType("boolean")
                                .HasColumnName("onboarding_options_onboarding_session_send_follow_up_email");

                            b1.Property<int?>("PrimaryPersonId")
                                .HasColumnType("integer")
                                .HasColumnName("onboarding_options_primary_person_id");

                            b1.Property<List<int>>("Tags")
                                .HasColumnType("integer[]")
                                .HasColumnName("onboarding_options_tags");

                            b1.HasKey("ProviderAgentId");

                            b1.ToTable("provider_agents");

                            b1.WithOwner()
                                .HasForeignKey("ProviderAgentId")
                                .HasConstraintName("fk_provider_agents_provider_agents_id");
                        });

                    b.Navigation("Computer");

                    b.Navigation("DeviceDetails")
                        .IsRequired();

                    b.Navigation("OnboardingOptions")
                        .IsRequired();

                    b.Navigation("ProviderClient");

                    b.Navigation("ProviderLink");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderClient", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "LinkedToTenant")
                        .WithMany("ProviderClients")
                        .HasForeignKey("LinkedToTenantId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_provider_clients_tenants_linked_to_tenant_id");

                    b.HasOne("Immybot.Backend.Domain.Models.ProviderLink", "ProviderLink")
                        .WithMany("ProviderClients")
                        .HasForeignKey("ProviderLinkId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_provider_clients_provider_links_provider_link_id");

                    b.Navigation("LinkedToTenant");

                    b.Navigation("ProviderLink");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderLink", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedProviderLinks")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_provider_links_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "OwnerTenant")
                        .WithMany("OwnedProviderLinks")
                        .HasForeignKey("OwnerTenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_provider_links_tenants_owner_tenant_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedProviderLinks")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_provider_links_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("OwnerTenant");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderLinkCrossReference", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.ProviderLink", "ProviderLink1")
                        .WithMany("ProvidersLinkedFromThisProvider")
                        .HasForeignKey("ProviderLink1Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_provider_link_cross_reference_provider_links_provider_link1");

                    b.HasOne("Immybot.Backend.Domain.Models.ProviderLink", "ProviderLink2")
                        .WithMany("LinkedFromProviders")
                        .HasForeignKey("ProviderLink2Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_provider_link_cross_reference_provider_links_provider_link2");

                    b.Navigation("ProviderLink1");

                    b.Navigation("ProviderLink2");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderLinkInternalData", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.ProviderLink", "ProviderLink")
                        .WithOne("ProviderInternalData")
                        .HasForeignKey("Immybot.Backend.Domain.Models.ProviderLinkInternalData", "ProviderLinkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_provider_link_internal_data_provider_links_provider_link_id");

                    b.Navigation("ProviderLink");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.RecommendedTargetAssignmentApproval", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedRecommendedTargetAssignmentApprovals")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_recommended_target_assignment_approvals_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedRecommendedTargetAssignmentApprovals")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_recommended_target_assignment_approvals_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.RemoteControlRecording", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Computer", "Computer")
                        .WithMany("RemoteControlRecordings")
                        .HasForeignKey("ComputerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_remote_control_recordings_computers_computer_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_remote_control_recordings_tenants_tenant_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_remote_control_recordings_users_user_id");

                    b.Navigation("Computer");

                    b.Navigation("Tenant");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Schedule", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Computer", null)
                        .WithMany("Schedules")
                        .HasForeignKey("ComputerId")
                        .HasConstraintName("fk_schedules_computers_computer_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedSchedules")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_schedules_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.ProviderLink", "ProviderLink")
                        .WithMany("Schedules")
                        .HasForeignKey("ProviderLinkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_schedules_provider_links_provider_link_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("Schedules")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_schedules_tenants_tenant_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedSchedules")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_schedules_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("ProviderLink");

                    b.Navigation("Tenant");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Script", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedScripts")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_scripts_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedScripts")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_scripts_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.SessionLog", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceAction", "MaintenanceAction")
                        .WithMany("Logs")
                        .HasForeignKey("MaintenanceActionId")
                        .HasConstraintName("fk_session_logs_maintenance_actions_maintenance_action_id");

                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceSession", "MaintenanceSession")
                        .WithMany("Logs")
                        .HasForeignKey("MaintenanceSessionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_session_logs_maintenance_sessions_maintenance_session_id");

                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceSessionStage", "MaintenanceSessionStage")
                        .WithMany("Logs")
                        .HasForeignKey("MaintenanceSessionStageId")
                        .HasConstraintName("fk_session_logs_maintenance_session_stages_maintenance_session");

                    b.HasOne("Immybot.Backend.Domain.Models.SessionPhase", "SessionPhase")
                        .WithMany("Logs")
                        .HasForeignKey("SessionPhaseId")
                        .HasConstraintName("fk_session_logs_session_phases_session_phase_id");

                    b.Navigation("MaintenanceAction");

                    b.Navigation("MaintenanceSession");

                    b.Navigation("MaintenanceSessionStage");

                    b.Navigation("SessionPhase");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.SessionPhase", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceAction", "MaintenanceAction")
                        .WithMany("Phases")
                        .HasForeignKey("MaintenanceActionId")
                        .HasConstraintName("fk_session_phases_maintenance_actions_maintenance_action_id");

                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceSession", "MaintenanceSession")
                        .WithMany("Phases")
                        .HasForeignKey("MaintenanceSessionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_session_phases_maintenance_sessions_maintenance_session_id");

                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceSessionStage", "MaintenanceSessionStage")
                        .WithMany("Phases")
                        .HasForeignKey("MaintenanceSessionStageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_session_phases_maintenance_session_stages_maintenance_sessi");

                    b.Navigation("MaintenanceAction");

                    b.Navigation("MaintenanceSession");

                    b.Navigation("MaintenanceSessionStage");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.SmtpConfig", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithOne("SmtpConfig")
                        .HasForeignKey("Immybot.Backend.Domain.Models.SmtpConfig", "TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_smtp_configs_tenants_tenant_id");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.SoftwarePrerequisite", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.LocalSoftware", null)
                        .WithMany("SoftwarePrerequisites")
                        .HasForeignKey("LocalSoftwareId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_software_prerequisite_software_local_software_id");

                    b.OwnsMany("Immybot.Backend.Domain.Models.SoftwareSpecifier", "SoftwaresForCondition", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<string>("SoftwareIdentifier")
                                .IsRequired()
                                .HasMaxLength(150)
                                .HasColumnType("character varying(150)")
                                .HasColumnName("software_identifier");

                            b1.Property<int>("SoftwarePrerequisiteId")
                                .HasColumnType("integer")
                                .HasColumnName("software_prerequisite_id");

                            b1.Property<int>("SoftwareType")
                                .HasColumnType("integer")
                                .HasColumnName("software_type");

                            b1.HasKey("Id");

                            b1.HasIndex("SoftwarePrerequisiteId")
                                .HasDatabaseName("ix_software_prerequisite_softwares_for_condition_software_prereq");

                            b1.ToTable("software_prerequisite_SoftwaresForCondition", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("SoftwarePrerequisiteId")
                                .HasConstraintName("fk_software_prerequisite_softwares_for_condition_software_prereq");
                        });

                    b.OwnsMany("Immybot.Backend.Domain.Models.SoftwareSpecifier", "SoftwaresToPerformActionOn", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<string>("SoftwareIdentifier")
                                .IsRequired()
                                .HasMaxLength(150)
                                .HasColumnType("character varying(150)")
                                .HasColumnName("software_identifier");

                            b1.Property<int>("SoftwarePrerequisiteId")
                                .HasColumnType("integer")
                                .HasColumnName("software_prerequisite_id");

                            b1.Property<int>("SoftwareType")
                                .HasColumnType("integer")
                                .HasColumnName("software_type");

                            b1.HasKey("Id")
                                .HasName("pk_software_prerequisite_softwares_to_perform_action_on");

                            b1.HasIndex("SoftwarePrerequisiteId")
                                .HasDatabaseName("ix_software_prerequisite_softwares_to_perform_action_on_softwa");

                            b1.ToTable("software_prerequisite_softwares_to_perform_action_on", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("SoftwarePrerequisiteId")
                                .HasConstraintName("fk_software_prerequisite_softwares_to_perform_action_on_softwa");
                        });

                    b.Navigation("SoftwaresForCondition");

                    b.Navigation("SoftwaresToPerformActionOn");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Tag", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedTags")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_tags_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedTags")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_tags_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TargetAssignment", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "CreatedByUser")
                        .WithMany("CreatedDeployments")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_target_assignments_users_created_by");

                    b.HasOne("Immybot.Backend.Domain.Models.License", "License")
                        .WithMany("TargetAssignments")
                        .HasForeignKey("LicenseId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_target_assignments_licenses_license_id");

                    b.HasOne("Immybot.Backend.Domain.Models.ProviderLink", "ProviderLink")
                        .WithMany("TargetAssignments")
                        .HasForeignKey("ProviderLinkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_target_assignments_provider_links_provider_link_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "UpdatedByUser")
                        .WithMany("UpdatedDeployments")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_target_assignments_users_updated_by");

                    b.Navigation("CreatedByUser");

                    b.Navigation("License");

                    b.Navigation("ProviderLink");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TargetAssignmentNotes", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.TargetAssignment", "TargetAssignment")
                        .WithOne("Notes")
                        .HasForeignKey("Immybot.Backend.Domain.Models.TargetAssignmentNotes", "TargetAssignmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_target_assignment_notes_target_assignments_target_assignmen");

                    b.Navigation("TargetAssignment");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TargetAssignmentVisibility", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.TargetAssignment", "TargetAssignment")
                        .WithOne("Visibility")
                        .HasForeignKey("Immybot.Backend.Domain.Models.TargetAssignmentVisibility", "TargetAssignmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_target_assignment_visibilities_target_assignments_target_as");

                    b.Navigation("TargetAssignment");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Tenant", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "OwnerTenant")
                        .WithMany("OwnedTenants")
                        .HasForeignKey("OwnerTenantId")
                        .HasConstraintName("fk_tenants_tenants_owner_tenant_id");

                    b.Navigation("OwnerTenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantMaintenanceTask", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.MaintenanceTask", "MaintenanceTask")
                        .WithMany("Tenants")
                        .HasForeignKey("MaintenanceTaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_maintenance_tasks_maintenance_tasks_maintenance_task");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("MaintenanceTasks")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_maintenance_tasks_tenants_tenant_id");

                    b.Navigation("MaintenanceTask");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantMedia", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Media", "Media")
                        .WithMany("Tenants")
                        .HasForeignKey("MediaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_media_media_media_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("Media")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_media_tenants_tenant_id");

                    b.Navigation("Media");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantPermissionAssignment", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("PermissionAssignments")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_permission_assignments_tenants_tenant_id");

                    b.HasOne("Immybot.Backend.Domain.Models.UserRole", "UserRole")
                        .WithMany("TenantPermissionAssignments")
                        .HasForeignKey("UserRoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_permission_assignments_user_roles_user_role_id");

                    b.Navigation("Tenant");

                    b.Navigation("UserRole");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantScript", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Script", "Script")
                        .WithMany("Tenants")
                        .HasForeignKey("ScriptId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_scripts_scripts_script_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("Scripts")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_scripts_tenants_tenant_id");

                    b.Navigation("Script");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantSoftware", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.LocalSoftware", "Software")
                        .WithMany("TenantSoftware")
                        .HasForeignKey("SoftwareId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_software_software_software_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("TenantSoftware")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_software_tenants_tenant_id");

                    b.Navigation("Software");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantTag", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("TenantTags")
                        .HasForeignKey("EntityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_tags_tenants_entity_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tag", "Tag")
                        .WithMany("TenantTags")
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_tags_tags_tag_id");

                    b.Navigation("Tag");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TenantTagAuthorization", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Tag", "Tag")
                        .WithMany("TenantTagAuthorizations")
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_tag_authorizations_tags_tag_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("TenantTagAuthorizations")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tenant_tag_authorizations_tenants_tenant_id");

                    b.Navigation("Tag");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.User", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Person", "Person")
                        .WithOne("User")
                        .HasForeignKey("Immybot.Backend.Domain.Models.User", "PersonId")
                        .HasConstraintName("fk_users_persons_person_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("Users")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_users_tenants_tenant_id");

                    b.Navigation("Person");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.UserAffinity", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Computer", "Computer")
                        .WithMany("UserAffinities")
                        .HasForeignKey("ComputerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_affinities_computers_computer_id");

                    b.HasOne("Immybot.Backend.Domain.Models.Person", "Person")
                        .WithMany("UserAffinities")
                        .HasForeignKey("PersonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_affinities_persons_person_id");

                    b.Navigation("Computer");

                    b.Navigation("Person");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.UserImpersonation", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "ImpersonatingUser")
                        .WithMany("ImpersonatedByUsers")
                        .HasForeignKey("ImpersonatingUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_impersonations_users_impersonating_user_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", "ImpersonatorUser")
                        .WithMany("UserImpersonations")
                        .HasForeignKey("ImpersonatorUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_impersonations_users_impersonator_user_id");

                    b.Navigation("ImpersonatingUser");

                    b.Navigation("ImpersonatorUser");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.UserRole", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.Tenant", "Tenant")
                        .WithMany("UserRoles")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_roles_tenants_tenant_id");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.UserSilencedNotification", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.User", "User")
                        .WithMany("SilencedNotifications")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_silenced_notifications_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("UserUserRole", b =>
                {
                    b.HasOne("Immybot.Backend.Domain.Models.UserRole", null)
                        .WithMany()
                        .HasForeignKey("UserRolesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_user_role_user_roles_user_roles_id");

                    b.HasOne("Immybot.Backend.Domain.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UsersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_user_role_users_users_id");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AzureTenant", b =>
                {
                    b.Navigation("AzureTenantLinks");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.AzureTenantLink", b =>
                {
                    b.Navigation("LimitToDomains");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ChangeRequest", b =>
                {
                    b.Navigation("Comments");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Computer", b =>
                {
                    b.Navigation("Actions");

                    b.Navigation("AdditionalPersons");

                    b.Navigation("AgentIdentificationFailures");

                    b.Navigation("Agents");

                    b.Navigation("ComputerTags");

                    b.Navigation("DetectedSoftware");

                    b.Navigation("LatestInventoryScriptResults");

                    b.Navigation("LatestProviderEvent");

                    b.Navigation("PredecessorComputers");

                    b.Navigation("RemoteControlRecordings");

                    b.Navigation("Schedules");

                    b.Navigation("Sessions");

                    b.Navigation("UserAffinities");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.InventoryTask", b =>
                {
                    b.Navigation("Scripts");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.License", b =>
                {
                    b.Navigation("TargetAssignments");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.LocalSoftware", b =>
                {
                    b.Navigation("SoftwarePrerequisites");

                    b.Navigation("SoftwareVersions");

                    b.Navigation("TenantSoftware");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceAction", b =>
                {
                    b.Navigation("Activities");

                    b.Navigation("Dependents");

                    b.Navigation("DependsOn");

                    b.Navigation("Logs");

                    b.Navigation("Phases");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceSession", b =>
                {
                    b.Navigation("ActiveSession");

                    b.Navigation("Activities");

                    b.Navigation("Logs");

                    b.Navigation("MaintenanceActions");

                    b.Navigation("Phases");

                    b.Navigation("Stages");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceSessionStage", b =>
                {
                    b.Navigation("Logs");

                    b.Navigation("Phases");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.MaintenanceTask", b =>
                {
                    b.Navigation("Parameters");

                    b.Navigation("Tenants");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Media", b =>
                {
                    b.Navigation("DynamicIntegrationTypes");

                    b.Navigation("IconForMaintenanceTasks");

                    b.Navigation("LocalSoftware");

                    b.Navigation("Tenants");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Person", b =>
                {
                    b.Navigation("AccessRequests");

                    b.Navigation("AdditionalComputers");

                    b.Navigation("DetectedComputerSoftware");

                    b.Navigation("PersonSessions");

                    b.Navigation("PersonTags");

                    b.Navigation("PrimaryComputers");

                    b.Navigation("User");

                    b.Navigation("UserAffinities");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderAgent", b =>
                {
                    b.Navigation("IdentificationFailures");

                    b.Navigation("IdentificationLogs");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderClient", b =>
                {
                    b.Navigation("ProviderAgents");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.ProviderLink", b =>
                {
                    b.Navigation("Agents");

                    b.Navigation("LinkedFromProviders");

                    b.Navigation("ProviderClients");

                    b.Navigation("ProviderInternalData");

                    b.Navigation("ProvidersLinkedFromThisProvider");

                    b.Navigation("Schedules");

                    b.Navigation("TargetAssignments");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Script", b =>
                {
                    b.Navigation("ChangeRequests");

                    b.Navigation("InventoryTaskScript");

                    b.Navigation("Tenants");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.SessionPhase", b =>
                {
                    b.Navigation("Logs");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Tag", b =>
                {
                    b.Navigation("ComputerTags");

                    b.Navigation("PersonTags");

                    b.Navigation("TenantTagAuthorizations");

                    b.Navigation("TenantTags");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.TargetAssignment", b =>
                {
                    b.Navigation("ChangeRequests");

                    b.Navigation("MaintenanceTaskParameterValues");

                    b.Navigation("Notes");

                    b.Navigation("Visibility");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.Tenant", b =>
                {
                    b.Navigation("AzureTenantLink");

                    b.Navigation("Brandings");

                    b.Navigation("Computers");

                    b.Navigation("DetectedComputerSoftware");

                    b.Navigation("Licenses");

                    b.Navigation("MaintenanceSessions");

                    b.Navigation("MaintenanceTasks");

                    b.Navigation("Media");

                    b.Navigation("OwnedProviderLinks");

                    b.Navigation("OwnedTenants");

                    b.Navigation("PermissionAssignments");

                    b.Navigation("Persons");

                    b.Navigation("ProviderClients");

                    b.Navigation("Schedules");

                    b.Navigation("Scripts");

                    b.Navigation("SmtpConfig");

                    b.Navigation("TenantPreferences");

                    b.Navigation("TenantSoftware");

                    b.Navigation("TenantTagAuthorizations");

                    b.Navigation("TenantTags");

                    b.Navigation("UserRoles");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.User", b =>
                {
                    b.Navigation("AccessRequestAcknowledgements");

                    b.Navigation("CreatedBrandings");

                    b.Navigation("CreatedChangeRequestComments");

                    b.Navigation("CreatedChangeRequests");

                    b.Navigation("CreatedDeployments");

                    b.Navigation("CreatedLicenses");

                    b.Navigation("CreatedMaintenanceSessions");

                    b.Navigation("CreatedMaintenanceTasks");

                    b.Navigation("CreatedMedia");

                    b.Navigation("CreatedPersons");

                    b.Navigation("CreatedProviderLinks");

                    b.Navigation("CreatedRecommendedTargetAssignmentApprovals");

                    b.Navigation("CreatedSchedules");

                    b.Navigation("CreatedScripts");

                    b.Navigation("CreatedSoftware");

                    b.Navigation("CreatedSoftwareVersions");

                    b.Navigation("CreatedTags");

                    b.Navigation("ImpersonatedByUsers");

                    b.Navigation("SilencedNotifications");

                    b.Navigation("UpdatedBrandings");

                    b.Navigation("UpdatedChangeRequestComments");

                    b.Navigation("UpdatedChangeRequests");

                    b.Navigation("UpdatedDeployments");

                    b.Navigation("UpdatedLicenses");

                    b.Navigation("UpdatedMaintenanceTasks");

                    b.Navigation("UpdatedMedia");

                    b.Navigation("UpdatedPersons");

                    b.Navigation("UpdatedProviderLinks");

                    b.Navigation("UpdatedRecommendedTargetAssignmentApprovals");

                    b.Navigation("UpdatedSchedules");

                    b.Navigation("UpdatedScripts");

                    b.Navigation("UpdatedSoftware");

                    b.Navigation("UpdatedSoftwareVersions");

                    b.Navigation("UpdatedTags");

                    b.Navigation("UserImpersonations");
                });

            modelBuilder.Entity("Immybot.Backend.Domain.Models.UserRole", b =>
                {
                    b.Navigation("PermissionClaims");

                    b.Navigation("TenantPermissionAssignments");
                });
#pragma warning restore 612, 618
        }
    }
}
