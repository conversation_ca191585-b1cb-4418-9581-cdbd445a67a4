using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Immybot.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddDomainRoleToDeviceDetails : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "device_details_domain_role",
                table: "provider_agents",
                type: "integer",
                nullable: true);

            // Update the stored procedure
            migrationBuilder.Sql(@"
                DROP PROCEDURE IF EXISTS public.sp_create_computer_from_agent(int4, uuid, bool, text, text, text, int4, bool, text);

                CREATE OR REPLACE PROCEDURE public.sp_create_computer_from_agent (
                  agent_id INTEGER,
                  device_id uuid,
                  is_onboarding_disabled BOOLEAN DEFAULT TRUE,
                  operating_system_name TEXT DEFAULT ''::TEXT,
                  device_name TEXT DEFAULT ''::TEXT,
                  serial_number TEXT DEFAULT ''::TEXT,
                  domain_role INTEGER DEFAULT NULL::INTEGER,
                  primary_person_id INTEGER DEFAULT NULL::INTEGER,
                  is_sandbox BOOLEAN DEFAULT FALSE,
                  dev_lab_vm_name TEXT DEFAULT ''::TEXT
                ) LANGUAGE plpgsql AS $procedure$
                                DECLARE
                                  _msp_tenant_id integer;
                                  _new_computer_id integer;
                                  _provider_link_id integer;
                                  _provider_client_external_id character varying(100);
                                  _now timestamp without time zone;
                                  _onboarding_status integer;
                                  _agent_id integer;
                             	  _onboarded_date_utc timestamp without time zone;
                                BEGIN
                                  _now = now() at time zone 'UTC';
                                  SELECT id
                                  INTO STRICT _msp_tenant_id
                                  FROM tenants
                                  WHERE owner_tenant_id IS NULL
                                  LIMIT 1;
                                  SELECT id,
                                    provider_link_id,
                                    external_client_id,
                                    -- If onboarding is not enabled or if the pending rmm computer was in the initial device sync,
                                    -- then it should be processed as already onboarded
                                    CASE
                                    WHEN is_onboarding_disabled THEN 2 -- 2 <=> Onboarded
                                    WHEN is_member_of_initial_device_sync THEN 2
                                    ELSE 0 END, -- 0 <=> needs_onboarding
                                    -- If onboarding is not enabled or if the pending rmm computer was in the initial device sync,
                                    -- then set the onboarded_date_utc field to now
                                    CASE
                                    WHEN is_onboarding_disabled THEN _now
                                    WHEN is_member_of_initial_device_sync THEN _now
                                    ELSE NULL END
                                  INTO STRICT
                                    _agent_id,
                                    _provider_link_id,
                                    _provider_client_external_id,
                                    _onboarding_status,
                                    _onboarded_date_utc
                                  FROM provider_agents
                                  WHERE id = agent_id;

                                  INSERT INTO computers (
                                    device_id,
                                    tenant_id,
                                    onboarding_status,
                                    onboarded_date_utc,
                                    created_date,
                                    updated_date,
                                    primary_person_id,
                                    computer_name,
                                    operating_system,
                                    serial_number,
                                    domain_role,
                                    is_sandbox,
                                    dev_lab_vm_name
                                  )
                                  SELECT device_id,
                                    COALESCE(c.linked_to_tenant_id, r.owner_tenant_id, _msp_tenant_id),
                                    _onboarding_status,
                                    _onboarded_date_utc,
                                    _now,
                                    _now,
                                    primary_person_id,
                                    device_name,
                                    operating_system_name,
                                    serial_number,
                                    domain_role,
                                    is_sandbox,
                                    dev_lab_vm_name
                                  FROM provider_clients c
                                  INNER JOIN provider_links r
                                    ON r.id = c.provider_link_id
                                  WHERE c.provider_link_id = _provider_link_id
                                    AND c.external_client_id = _provider_client_external_id
                                  RETURNING id
                                  INTO STRICT _new_computer_id;

                                  UPDATE provider_agents
                                  SET computer_id = _new_computer_id
                                  WHERE id = _agent_id;
                                END;
                                $procedure$;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "device_details_domain_role",
                table: "provider_agents");

            // Restore the previous version of the stored procedure (without domain_role parameter)
            migrationBuilder.Sql(@"
                DO $$
                DECLARE p record;
                BEGIN
                  FOR p IN
                    SELECT oid, pg_get_function_identity_arguments(oid) AS arglist
                    FROM pg_proc
                    WHERE proname = 'sp_create_computer_from_agent'
                      AND pronamespace = 'public'::regnamespace
                  LOOP
                    EXECUTE format('DROP PROCEDURE IF EXISTS public.sp_create_computer_from_agent(%s);', p.arglist);
                  END LOOP;
                END
                $$;

                CREATE OR REPLACE PROCEDURE public.sp_create_computer_from_agent(
                  agent_id integer,
                  device_id uuid,
                  is_onboarding_disabled boolean DEFAULT true,
                  operating_system_name text DEFAULT ''::text,
                  device_name text DEFAULT ''::text,
                  serial_number text DEFAULT ''::text,
                  primary_person_id integer DEFAULT NULL::integer,
                  is_sandbox boolean DEFAULT false,
                  dev_lab_vm_name text DEFAULT ''::text
                ) LANGUAGE plpgsql AS $procedure$
                                DECLARE
                                  _msp_tenant_id integer;
                                  _new_computer_id integer;
                                  _provider_link_id integer;
                                  _provider_client_external_id character varying(100);
                                  _now timestamp without time zone;
                                  _onboarding_status integer;
                                  _agent_id integer;
                             	  _onboarded_date_utc timestamp without time zone;
                                BEGIN
                                  _now = now() at time zone 'UTC';
                                  SELECT id
                                  INTO STRICT _msp_tenant_id
                                  FROM tenants
                                  WHERE owner_tenant_id IS NULL
                                  LIMIT 1;
                                  SELECT id,
                                    provider_link_id,
                                    external_client_id,
                                    -- If onboarding is not enabled or if the pending rmm computer was in the initial device sync,
                                    -- then it should be processed as already onboarded
                                    CASE
                                    WHEN is_onboarding_disabled THEN 2 -- 2 <=> Onboarded
                                    WHEN is_member_of_initial_device_sync THEN 2
                                    ELSE 0 end, -- 0 <=> needs_onboarding
                                    -- If onboarding is not enabled or if the pending rmm computer was in the initial device sync,
                                    -- then set the onboarded_date_utc field to now
                                    CASE
                                    WHEN is_onboarding_disabled THEN _now
                                    WHEN is_member_of_initial_device_sync THEN _now
                                    ELSE null END
                                  INTO STRICT
                                    _agent_id,
                                    _provider_link_id,
                                    _provider_client_external_id,
                                    _onboarding_status,
                                    _onboarded_date_utc
                                  FROM provider_agents
                                  WHERE id = agent_id;

                                  INSERT INTO computers (
                                    device_id
                                    , tenant_id
                                    , onboarding_status
                                    , onboarded_date_utc
                                    , created_date
                                    , updated_date
                                    , primary_person_id
                                    , computer_name
                                    , operating_system
                                    , serial_number
                                    , is_sandbox
                                    , dev_lab_vm_name
                                  )
                                  SELECT device_id
                                    , COALESCE(c.linked_to_tenant_id, r.owner_tenant_id, _msp_tenant_id)
                                    , _onboarding_status
                                    , _onboarded_date_utc
                                    , _now
                                    , _now
                                    , primary_person_id
                                    , device_name
                                    , operating_system_name
                                    , serial_number
                                    , is_sandbox
                                    , dev_lab_vm_name
                                  FROM provider_clients c
                                  INNER JOIN provider_links r
                                    ON r.id = c.provider_link_id
                                  WHERE c.provider_link_id = _provider_link_id
                                    AND c.external_client_id = _provider_client_external_id
                                  RETURNING id
                                  INTO STRICT _new_computer_id;

                                  UPDATE provider_agents
                                  SET computer_id = _new_computer_id
                                  WHERE id = _agent_id;
                                END;
                                $procedure$;
            ");
        }
    }
}
