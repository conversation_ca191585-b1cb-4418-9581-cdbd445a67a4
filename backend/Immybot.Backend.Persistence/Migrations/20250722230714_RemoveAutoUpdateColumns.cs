using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Immybot.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class RemoveAutoUpdateColumns : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "auto_update_script_id",
                table: "software");

            migrationBuilder.DropColumn(
                name: "auto_update_script_type",
                table: "software");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "auto_update_script_id",
                table: "software",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "auto_update_script_type",
                table: "software",
                type: "integer",
                nullable: true);
        }
    }
}
