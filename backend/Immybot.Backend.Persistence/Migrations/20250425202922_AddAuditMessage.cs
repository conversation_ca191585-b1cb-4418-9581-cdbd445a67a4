using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Immybot.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddAuditMessage : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "message",
                table: "audits",
                type: "text",
                nullable: true);
            migrationBuilder.Sql(
              "create index if not exists ix_audits_message_trgm on audits using gin ((message::text) gin_trgm_ops);");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "message",
                table: "audits");
        }
    }
}
