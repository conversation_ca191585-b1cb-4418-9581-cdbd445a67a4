using System;
using System.Collections;
using System.Collections.Specialized;
using System.Linq;
using System.Management.Automation;
using System.Threading;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Providers.CwAutomateProvider;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Shared.ProxyHelpers;
using Microsoft.PowerShell.Commands;
using Microsoft.VisualStudio.Threading;
using Newtonsoft.Json;

namespace CwAutomateProvider;

[Cmdlet("Invoke", "CWARestMethod")]
public class InvokeCwaRestMethodCommand : PSCmdlet
{
  [Parameter(Mandatory = true, Position = 0)]
  public string? RelativeUri { get; set; }

  [Parameter(Mandatory = false, Position = 1)]
  public string Method { get; set; } = "get";

  [Parameter(Mandatory = false, Position = 2)]
  public string? Body { get; set; }

  [Parameter(Mandatory = false, Position = 3)]
  public Hashtable? QueryParams { get; set; }

  [Parameter(Mandatory = true, Position = 4)]
  public IProvider? Provider { get; set; }

  private static NameValueCollection? HashtableToNameValueCollection(Hashtable? table)
  {
    return (NameValueCollection?)(table?.Cast<NameValueCollection>());
  }
  protected override void ProcessRecord()
  {
    if (Provider == null)
    {
      throw new CwAutomateException("Provider is missing");
    }
    var providerUnwrapped = ProxyHelpers.UnwrapProxy(Provider) ?? throw new CwAutomateException("Provider proxy cannot be unwrapped.");
    if (providerUnwrapped is not CwAutomateProvider cwAutomateProvider) throw new CwAutomateException("Provider is not an CWAutomateProvider.");

    if (RelativeUri == null)
    {
      throw new CwAutomateException("RelativeUri is missing");
    }
    var token = this.DemandVariableValue<CancellationToken>("CancellationToken");
    try
    {
      var result = new JoinableTaskContext().Factory.Run(async () => await cwAutomateProvider.InvokeRestMethod(RelativeUri, Method, Body,
        HashtableToNameValueCollection(QueryParams),
        token));
      if (result != null)
      {
        var objResult = JsonObject.ConvertFromJson(result.ToString(Formatting.Indented), out _);
        WriteObject(objResult, true);
      }
    }
    catch (CwAutomateHttpException ex)
    {
      WriteError(new ErrorRecord(
        new Exception(ex.HttpProblem?.ToLogString() ?? "Error getting data from CWAutomate REST API"),
          ex.HttpProblem?.Status?.ToString() ?? "No StatusCode",
          ErrorCategory.NotSpecified,
          RelativeUri));
    }
  }
}
