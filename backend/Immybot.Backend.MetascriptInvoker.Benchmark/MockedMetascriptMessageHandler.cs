using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Lib.MetaScripts;

namespace Immybot.Backend.MetascriptInvoker.Benchmark;

public class MockedMetascriptMessageHandler : IMetascriptMessageHandler
{
  public IMetascriptMessageHandler SetTerminalId(Guid id)
  {
    return this;
  }

  public void HandlePsHostEvent(PSHostEvent psHostEvent, Guid? correlationId)
  {
  }

  public void HandleStringMessage(string? message)
  {
  }
}
