using System.Management.Automation;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.MetascriptInvoker.Benchmark;

public class MockedPowerShellErrorEventHubService : IPowerShellErrorEventHubService
{
  public Task StartAsync(CancellationToken cancellationToken)
  {
    return Task.CompletedTask;
  }

  public Task StopAsync(CancellationToken cancellationToken)
  {
    return Task.CompletedTask;
  }

  public void AddEvents(IRunContext? runContext, Script script, ICollection<ErrorRecord> errorRecords)
  {
  }
}
