using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Engines;
using Immybot.Backend.Application.DbContextExtensions.Preferences;
using Immybot.Backend.Application.DynamicProviders;
using Immybot.Backend.Application.Infrastructure;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Application.Lib.Policies;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Infrastructure.Configuration.Application;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Infrastructure.Telemetry.Configuration;
using Immybot.Backend.Manager;
using Immybot.Backend.Manager.Domain;
using Immybot.Backend.Persistence;
using Immybot.Backend.UnitTests;
using Immybot.Backend.UnitTests.Shared.Lib;
using Immybot.Backend.UnitTests.Shared.Mocks;
using Immybot.Shared.Scripts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.MetascriptInvoker.Benchmark;

[MemoryDiagnoser]
[ThreadingDiagnoser]
// [DisassemblyDiagnoser]
// [MinIterationCount(10)]
// [MaxIterationCount(15)]
[SimpleJob(RunStrategy.Monitoring, warmupCount: 0, iterationCount: 5)]
[MinColumn, MaxColumn]
public class MetascriptInvokerBenchmark : BaseUnitTests
{
  private IMetascriptInvoker _metascriptInvoker = default!;
  private Script _script = default!;
  private readonly IRunContext _runContext = default!;

  [GlobalSetup]
  public void Setup()
  {
    new JoinableTaskContext().Factory.Run(async () =>
    {
      var immybotDbContext = this.GetNewSqliteDbContext();
      var softwareDbContext = this.GetNewSqliteSoftwareDbContext();

      var dbCtxFactory = () => immybotDbContext;
      var globalDbCtxFactory = () => softwareDbContext;

      await ImportGlobalFactoryData(globalDbCtxFactory);

      // TODO how much of this setup can/should we replace with this?
      // var mocks = new MockedServices();
      // var (metascriptInvoker, _) =
      //   Helpers.BuildMetascriptInvoker(mocks, ctxFactory: dbCtxFactory, globalCtxFactory: globalDbCtxFactory);
      //
      // var (ephemeralHandler, _) = Helpers.BuildEphemeralAgentSessionHandler(dbCtxFactory, mocks);
      //
      // var (ephemeralAgentAcquisition, _) =
      //   Helpers.BuildEphemeralAgentAcquisition(dbCtxFactory, ephemeralHandler, mocks, loggerFactory: _loggerFactory);

      // mocks.IEphemeralAgentAcquisition = new Mock<IEphemeralAgentAcquisition>(() => ephemeralAgentAcquisition);

      // _runContext = mocks.BuildRunContext();

      var builder = Host.CreateApplicationBuilder();

      var dir = AppDomain.CurrentDomain.BaseDirectory;
      builder.Configuration
        .AddJsonFile(Path.Join(dir, "appsettings.json"), optional: false, reloadOnChange: true)
        .AddJsonFile(Path.Join(dir, "appsettings.Development.json"), optional: true, reloadOnChange: true);
      var dbOptions = new DatabaseOptions();
      builder.Configuration.Bind(DatabaseOptions.SectionKey, dbOptions);
      builder.Services.Configure<EphemeralAgentSettings>(
        builder.Configuration.GetSection(EphemeralAgentSettings.SectionKey));
      builder.AddTelemetryServices();
      builder.Services
        .AddSingleton<IPowerShellErrorEventHubService, MockedPowerShellErrorEventHubService>()
        .AddSingleton<IImmyCancellationManager, ImmyCancellationManager>()
        .AddSingleton<IPowershellLoader, MockedPowershellLoader>()
        .AddSingleton(new EphemeralAgentSettings())
        .AddSingleton<MetascriptInvokerDefaults>()
        .AddSingleton<ICachedSingleton<ApplicationPreferences>>(new CachedSingleton<ApplicationPreferences>())
        .AddSingleton<IManagerProvidedSettings>(new MockedManagerProvidedSettings())
        .AddSingleton<IFunctionScriptManager, FunctionScriptManager>()
        .AddSingleton<IInitialSessionStateFactory, InitialSessionStateFactory>()
        .AddSingleton<IDynamicProviderStore, DynamicProviderStore>()
        .AddTransient<IMetascriptMessageHandler, MockedMetascriptMessageHandler>();
      var policyRegistry = builder.Services.AddPolicyRegistry();
      policyRegistry.AddShowCommandInfoCachePolicy();
      builder.Services.AddSingleton<IPolicyCacheStore>(_ => PolicyCacheStore.Instance);
      builder.Services.AddSingleton<Func<ImmybotDbContext>>(_ => dbCtxFactory);
      builder.Services.AddSingleton<Func<SoftwareDbContext>>(_ => globalDbCtxFactory);

      builder.Services.AddHostedService<IMetascriptRunspaceServer, MetascriptRunspaceServer>();
      builder.Services.AddTransient<IMetascriptInvoker, Application.Lib.MetaScripts.MetascriptInvoker>();
      builder.Logging.AddFilter("Immybot.Backend.Application.Lib.MetaScripts.MetascriptRunspaceServer", LogLevel.Debug);

      var host = builder.Build();

      var cachedAppPrefs = host.Services.GetRequiredService<ICachedSingleton<ApplicationPreferences>>();
      cachedAppPrefs.Value = immybotDbContext.GetApplicationPreferences();
      _metascriptInvoker = host.Services.GetRequiredService<IMetascriptInvoker>();

      // not sure if necessary
      // this.ImportDetectedSoftware(softwareDbContext);

      _script = new Script
      {
        Name = "",
        Action = this.ShouldScriptSleep
          ? "Start-Sleep -Milliseconds 100; " + this.ScriptContent
          : this.ScriptContent,
        ScriptType = DatabaseType.Local,
        ScriptCategory = ScriptCategory.MaintenanceTaskSetter,
        ScriptLanguage = ScriptLanguage.PowerShell,
        ScriptExecutionContext = ScriptExecutionContext.Metascript
      };

      host.RunAsync().Forget(); // i guess could save off this task, or something
    });
  }

  [Params([
    "Out-Rainbow -WriteHostString \"Hello world v$ImmyBotVersion\"",
    // "Invoke-ImmyCommand { Get-LastBootTime }",
  ])]
  public required string ScriptContent { get; set; }

  [ParamsAllValues] public bool ShouldScriptSleep { get; set; }

  [Benchmark]
  [Arguments(10)]
  [Arguments(100)]
  [Arguments(500)]
  public Task RunSimpleScript_InParallel(int count) =>
    Task.WhenAll(Enumerable.Range(0, count).Select(_ => ExecuteScript()).ToList());

  [Benchmark]
  [Arguments(1)]
  [Arguments(10)]
  [Arguments(100)]
  [Arguments(500)]
  public async Task RunSimpleScript_Sequentially(int count)
  {
    for (int i = 0; i < count; i++)
      await ExecuteScript();
  }

  public Task<MetaScriptResult<object>> ExecuteScript() =>
    _metascriptInvoker.RunMetascript<object>(
      canAccessMspResources: false,
      canAccessParentTenant: false,
      script: _script,
      CancellationToken.None,
      TimeSpan.FromSeconds(60),
      runContext: _runContext
    );
}
