using Immybot.Backend.Application.Interface;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.MetascriptInvoker.Benchmark;

public class MockedPowershellLoader : IPowershellLoader
{
  public string GetPowerShellModulePath(string scriptName)
  {
    throw new NotImplementedException();
  }

  public Script GetPowerShellScript(string scriptName,
    int? timeoutSeconds = null,
    Dictionary<string, object?>? parameters = null)
  {
    throw new NotImplementedException();
  }

  public string GetPowerShellScriptPath(string scriptName)
  {
    throw new NotImplementedException();
  }

  public string GetPowerShellScriptTemplate(string scriptName)
  {
    throw new NotImplementedException();
  }

  public string GetPowerShellScriptText(string scriptName)
  {
    throw new NotImplementedException();
  }
}
