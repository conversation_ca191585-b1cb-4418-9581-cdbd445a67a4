using System.Diagnostics;
using BenchmarkDotNet.Columns;
using BenchmarkDotNet.Configs;
using BenchmarkDotNet.Loggers;
using BenchmarkDotNet.Running;
using BenchmarkDotNet.Validators;
using Immybot.Backend.MetascriptInvoker.Benchmark;
using JetBrains.Profiler.SelfApi;

// ReSharper disable HeuristicUnreachableCode
#pragma warning disable CS0162 // Unreachable code detected
#pragma warning disable S1199 // Nested code blocks should not be used

const bool benchmark = true;
//const bool benchmark = false;

// const bool profileWithDotMemory = true;
const bool profileWithDotMemory = false;

const bool runInParallel = true;
// const bool runInParallel = false;

const int numIterations = 100;

static void InitializeDotMemory()
{
  if (profileWithDotMemory)
  {
    DotMemory.Init();
    var config = new DotMemory.Config(); //.OpenDotMemory();
    // config.SaveToDir("C:\\Temp\\Workspace");
    DotMemory.Attach(config);
    DotMemory.GetSnapshot("initial");
  }
}

static void FinalizeDotMemory()
{
  if (profileWithDotMemory)
  {
    DotMemory.GetSnapshot("final");
    Console.WriteLine(DotMemory.Detach());
  }
}

static void TakeDotMemorySnapshot(string name)
{
  if (profileWithDotMemory)
  {
    DotMemory.GetSnapshot(name);
  }
}

if (benchmark)
{
  BenchmarkRunner.Run<MetascriptInvokerBenchmark>(
    // new DebugInProcessConfig() // for running in debugger
    new ManualConfig()
      .WithOptions(ConfigOptions.DisableOptimizationsValidator)
      .AddValidator(JitOptimizationsValidator.FailOnError)
      .AddLogger(ConsoleLogger.Default)
      .AddColumnProvider(DefaultColumnProviders.Instance),
    args
  );
}
else
{
  try
  {
    InitializeDotMemory();
    {
      MetascriptInvokerBenchmark b = new()
      {
        ScriptContent = "Start-Sleep -Milliseconds 100; Out-Rainbow -WriteHostString \"Hello world v$ImmyBotVersion\"",
        ShouldScriptSleep = true,
      };
      b.Setup();
      TakeDotMemorySnapshot("post-setup");

      async Task ExecuteScript(int i)
      {
        var timer = Stopwatch.StartNew();
        var result = await b.ExecuteScript();
        timer.Stop();
        Console.WriteLine($@"{i} Time: {timer.ElapsedMilliseconds}ms ConsoleText: {result.ConsoleText}");
        if (result.GetErrorString() is { } error)
          Console.WriteLine($@"Error: {error}");
      }

      if (runInParallel)
      {
        await Task.WhenAll(Enumerable.Range(0, numIterations).Select(async i =>
        {
          await Task.Delay(10);
          await ExecuteScript(i);
        }));
      }
      else
      {
        for (var i = 0; i < numIterations; i++)
        {
          await ExecuteScript(i);
          if (i % 10 == 0)
            TakeDotMemorySnapshot($"post-execute-{i}");
        }
      }
    }

    // GC.Collect();
    // GC.Collect();
    // GC.Collect();
  }
  finally
  {
    FinalizeDotMemory();
  }
}
