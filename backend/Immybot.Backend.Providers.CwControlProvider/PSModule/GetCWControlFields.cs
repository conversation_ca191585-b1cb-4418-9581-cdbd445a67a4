using System.Collections;
using System.Linq;
using System.Management.Automation;
using System.Threading;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Shared.ProxyHelpers;
using Microsoft.VisualStudio.Threading;
using ControlProvider = CwControlProvider.CwControlProvider;

namespace Immybot.Backend.Providers.CwControlProvider.PSModule;

[Cmdlet("Get", "CWControlFields")]
[OutputType(typeof(IDictionary))]
public class GetCwControlFields : PSCmdlet
{
  [Parameter(Mandatory = true)]
  public IProvider? Provider { get; set; }

  [Parameter(Mandatory = true)]
  public string? DeviceId { get; set; }

  protected override void ProcessRecord()
  {
    if (Provider is null) throw new CwControlException("Provider is missing.");
    var providerUnwrapped = ProxyHelpers.UnwrapProxy(Provider) ?? throw new CwControlException("Provider proxy cannot be unwrapped.");
    if (providerUnwrapped is not ControlProvider cwControlProvider) throw new CwControlException("Provider is not an CWControlProvider.");
    if (string.IsNullOrWhiteSpace(DeviceId)) throw new CwControlException("DeviceId is missing.");

    var token = this.DemandVariableValue<CancellationToken>("CancellationToken");

    var agentInfo = new JoinableTaskContext().Factory.Run(async () =>
      await cwControlProvider.GetAgent(DeviceId, token)
    )
    ?? throw new CwControlException("'GetAgent' returned a null CWControlAgent object.");

    // Offset index so that it matches the index of the fields in the CWControl UI
    var dict = agentInfo.CwCustomProperyValues?.Select((value, index) => (value, index)).ToDictionary(x => x.index + 1, x => x.value);
    WriteObject(dict);
  }
}
