using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Services;
using Moq;

namespace Immybot.Backend.UnitTests.ContextTests;

public class TenantActionsTests : ActionsTestBase
{
  [Fact]
  public async Task CreateTenantForNonMsp_ShouldFailWhenOwnerTenantIdIsMissing()
  {
    // arrange
    var mockedServices = new MockedServices();
    var cachedTenantPreferences = Helpers.MockTenantPrefsCache(mockedServices);
    var azLinkCmd = Helpers.MockUpdateAzureTenantLinkCmd();
    var cmd = new CreateTenantCmd(() => GetNewSqliteDbContext(), cachedTenantPreferences, azLinkCmd, Mock.Of<IPendoEventManagementService>());
    var payload = MockCreateTenantPayload("name", "prin");

    try
    {
      // act
      await cmd.CreateTenant(payload.Object, null, CancellationToken.None);
    }
    catch (ValidationException ex)
    {
      // assert
      Assert.NotNull(ex);
    }
  }

  [Fact]
  public async Task CreateTenantForMsp_ShouldHaveNullOwnerTenantId()
  {
    // arrange
    var mockedServices = new MockedServices();
    var cachedTenantPreferences = Helpers.MockTenantPrefsCache(mockedServices);
    var cmd = new CreateTenantCmd(() => GetNewSqliteDbContext(), cachedTenantPreferences,
      Helpers.MockUpdateAzureTenantLinkCmd(), Mock.Of<IPendoEventManagementService>());
    var payload = MockCreateTenantPayload("name", "prin", isMsp: true);

    // act
    var created = await cmd.CreateTenant(payload.Object, null, CancellationToken.None);

    // assert
    Assert.Null(created.OwnerTenantId);
  }

  [Fact]
  public async Task CreateTenantForMsp_ShouldHaveTenantPreferences()
  {
    // arrange
    var mockedServices = new MockedServices();
    var cachedTenantPreferences = Helpers.MockTenantPrefsCache(mockedServices);
    var cmd = new CreateTenantCmd(() => GetNewSqliteDbContext(), cachedTenantPreferences,
      Helpers.MockUpdateAzureTenantLinkCmd(), Mock.Of<IPendoEventManagementService>());
    var payload = MockCreateTenantPayload("name", "prin", isMsp: true);

    // act
    var created = await cmd.CreateTenant(payload.Object, null, CancellationToken.None);

    // assert
    Assert.NotNull(created.TenantPreferences);
  }

  [Fact]
  public async Task CreateTenant_WithDuplicateTenantName_ShouldThrowValidationException()
  {
    // arrange
    var mockedServices = new MockedServices();
    var cachedTenantPreferences = Helpers.MockTenantPrefsCache(mockedServices);
    var cmd = new CreateTenantCmd(() => GetNewSqliteDbContext(), cachedTenantPreferences,
      Helpers.MockUpdateAzureTenantLinkCmd(), Mock.Of<IPendoEventManagementService>());
    var tenant1 = MockCreateTenantPayload("name", "prin", isMsp: true);
    var tenant2 = MockCreateTenantPayload("name", "prin", isMsp: true);
    _ = await cmd.CreateTenant(tenant1.Object, null, CancellationToken.None);

    // act and assert
    await Assert.ThrowsAsync<ValidationException>(
      async () => await cmd
        .CreateTenant(tenant2.Object, null, CancellationToken.None, throwOnDuplicateTenantName: true));
  }
}
