using System;
using System.Collections.Generic;
using System.Linq;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Commands.Payloads.Licenses;
using Immybot.Backend.Application.Interface.Commands.Payloads.MaintenanceTasks;
using Immybot.Backend.Application.Interface.Commands.Payloads.Scripts;
using Immybot.Backend.Application.Interface.Commands.Payloads.TargetAssignments;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Application.SoftwareManagement.CustomAudits.Implementations;
using Immybot.Backend.Application.SoftwareManagement.CustomAudits.Interfaces;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Infrastructure.Telemetry.Configuration;
using Immybot.Backend.Persistence;
using Immybot.Backend.UnitTests.Shared.Lib;
using Immybot.Backend.Web.Common.Startup;
using Immybot.Shared.Scripts;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Moq;
using NuGet.Versioning;
using Polly.Registry;

namespace Immybot.Backend.UnitTests.ContextTests;

public class ActionsTestBase : BaseUnitTests
{
  private IDomainEventBroker? _domainBroker;
  private GlobalSoftwareActions? _globalSoftwareActions;
  private LocalSoftwareActions? _localSoftwareActions;

  public ActionsTestBase()
  {
  }

  public ActionsTestBase(ITestOutputHelper helper, bool showLogsInTestOutput = true) : base(helper, showLogsInTestOutput)
  {
  }

  private protected IDomainEventBroker GetEventBroker()
  {
    var logger = Mock.Of<ILogger<DomainEventBroker>>();
    return _domainBroker ??= new DomainEventBroker(logger);
  }

  private protected GlobalSoftwareActions GetGlobalSoftwareActions()
  {
    return GetGlobalSoftwareActions(Mock.Of<IReadOnlyPolicyRegistry<string>>());
  }

  private protected GlobalSoftwareActions GetGlobalSoftwareActions(IReadOnlyPolicyRegistry<string> policyRegistry)
  {
    return GetGlobalSoftwareActions(policyRegistry, Mock.Of<ILogger<GlobalSoftwareActions>>());
  }

  private protected GlobalSoftwareActions GetGlobalSoftwareActions(
   IReadOnlyPolicyRegistry<string> policyRegistry,
   ILogger<GlobalSoftwareActions> logger)
  {
    return GetGlobalSoftwareActions(
      GetSqliteSoftwareDbContextFactory(),
      GetSqliteDbContextFactory(),
      policyRegistry,
      logger);
  }

  private protected GlobalSoftwareActions GetGlobalSoftwareActions(
    Func<SoftwareDbContext> globalDbFactory,
    Func<ImmybotDbContext> localDbFactory,
    IReadOnlyPolicyRegistry<string> policyRegistry,
    ILogger<GlobalSoftwareActions> logger)
  {
    return _globalSoftwareActions ??= new GlobalSoftwareActions(
      localDbFactory,
      globalDbFactory,
      GetSoftwarePrerequisiteCustomAudit(),
      GetEventBroker(),
      policyRegistry,
      logger);
  }

  private protected LocalSoftwareActions GetLocalSoftwareActions()
  {
    return _localSoftwareActions ??= new LocalSoftwareActions(
      GetSqliteDbContextFactory(),
      GetSoftwarePrerequisiteCustomAudit(),
      GetEventBroker(),
      Mock.Of<IPendoEventManagementService>());
  }

  private protected ISoftwarePrerequisiteCustomAudit GetSoftwarePrerequisiteCustomAudit()
  {
    return new SoftwarePrerequisiteCustomAudit(GetSqliteDbContextFactory(), GetSqliteSoftwareDbContextFactory());
  }

  private protected AzureActions GetAzureActions(
    IAzureTenantTokenCredentialFactory? credentialFactory = null,
    IGraphApi? graphApi = null,
    ISyncAzureUsersAndImmyPersonsCmd? syncPersonsCmd = null)
  {
    return new AzureActions(
      syncPersonsCmd ?? Helpers.MockSyncAzureUsersAndImmyPersonsCmd().Object,
      GetSqliteDbContextFactory(),
      graphApi ?? Helpers.MockGraphApi().Object,
      Mock.Of<IPartnerCenterApi>(),
      Mock.Of<ILogger<AzureActions>>(),
      Mock.Of<IOptions<AzureActiveDirectoryAuthOptions>>(),
      Mock.Of<IOptions<AppSettingsOptions>>(),
      Mock.Of<IHostApplicationLifetime>(),
      credentialFactory ?? Mock.Of<IAzureTenantTokenCredentialFactory>(),
      Mock.Of<IOauthAccessTokenStore>(),
      Mock.Of<IDomainEventEmitter>(),
      Mock.Of<IAzureExceptionHandler>(),
      Mock.Of<IPendoEventManagementService>(),
      Mock.Of<IReadOnlyPolicyRegistry<string>>()
      );
  }

  protected static Mock<ICreateLocalScriptPayload> MockCreateLocalScriptPayload(
    int createdById,
    string name = "script",
    string action = "choco -v",
    ScriptLanguage scriptLanguage = ScriptLanguage.PowerShell,
    List<TenantScript>? tenants = null
  )
  {
    var payload = new Mock<ICreateLocalScriptPayload>();
    payload.SetupGet(p => p.Name).Returns(name);
    payload.SetupGet(p => p.Action).Returns(action);
    payload.SetupGet(p => p.ScriptLanguage).Returns(scriptLanguage);
    payload.SetupGet(p => p.Tenants).Returns(tenants ?? []);
    return payload;
  }

  protected static Mock<IUpdateLocalScriptPayload> MockUpdateLocalScriptPayload(
    int updatedByUserId,
    Script existing,
    string name = "script",
    string action = "choco -v",
    ScriptLanguage scriptLanguage = ScriptLanguage.PowerShell,
    List<TenantScript>? tenants = null
  )
  {
    var payload = new Mock<IUpdateLocalScriptPayload>();
    payload.SetupGet(p => p.Name).Returns(name);
    payload.SetupGet(p => p.Action).Returns(action);
    payload.SetupGet(p => p.ScriptLanguage).Returns(scriptLanguage);
    payload.SetupGet(p => p.Tenants).Returns(tenants ?? []);
    return payload;
  }

  protected static Mock<ICreateGlobalScriptPayload> MockCreateGlobalScriptPayload(
    string name = "script",
    string action = "choco -v",
    ScriptLanguage scriptLanguage = ScriptLanguage.PowerShell
  )
  {
    var payload = new Mock<ICreateGlobalScriptPayload>();
    payload.SetupGet(p => p.Name).Returns(name);
    payload.SetupGet(p => p.Action).Returns(action);
    payload.SetupGet(p => p.ScriptLanguage).Returns(scriptLanguage);
    return payload;
  }

  protected static Mock<IUpdateGlobalScriptPayload> MockUpdateGlobalScriptPayload(
    Script existing,
    string name = "script",
    string action = "choco -v",
    ScriptLanguage scriptLanguage = ScriptLanguage.PowerShell
  )
  {
    var payload = new Mock<IUpdateGlobalScriptPayload>();
    payload.SetupGet(p => p.Name).Returns(name);
    payload.SetupGet(p => p.Action).Returns(action);
    payload.SetupGet(p => p.ScriptLanguage).Returns(scriptLanguage);
    return payload;
  }


  protected static Mock<ICreateBrandingPayload> MockCreateBrandingPayload(
    User createdBy,
    int? tenantId = null,
    DateTime? startDate = null,
    DateTime? endDate = null,
    bool? ignoreYear = null,
    string? timeFormat = null,
    string? fromAddress = null,
    string? mascotImgUri = null,
    string? mascotName = null,
    string? logoUri = null,
    string? logoAltText = null,
    string? backgroundColor = null,
    string? foregroundColor = null,
    string? tableHeaderColor = null,
    string? description = null)
  {
    var payload = new Mock<ICreateBrandingPayload>();
    payload.SetupGet(p => p.TenantId).Returns(tenantId);
    payload.SetupGet(p => p.StartDate).Returns(startDate);
    payload.SetupGet(p => p.EndDate).Returns(endDate);
    payload.SetupGet(p => p.IgnoreYear).Returns(ignoreYear);
    payload.SetupGet(p => p.TimeFormat).Returns(timeFormat);
    payload.SetupGet(p => p.FromAddress).Returns(fromAddress);
    payload.SetupGet(p => p.MascotImgUri).Returns(mascotImgUri);
    payload.SetupGet(p => p.MascotName).Returns(mascotName);
    payload.SetupGet(p => p.LogoUri).Returns(logoUri);
    payload.SetupGet(p => p.LogoAltText).Returns(logoAltText);
    payload.SetupGet(p => p.BackgroundColor).Returns(backgroundColor);
    payload.SetupGet(p => p.ForegroundColor).Returns(foregroundColor);
    payload.SetupGet(p => p.TableHeaderColor).Returns(tableHeaderColor);
    payload.SetupGet(p => p.Description).Returns(description ?? string.Empty);
    return payload;
  }

  protected static Mock<IUpdateBrandingPayload> MockUpdateBrandingPayload(
    Branding existing,
    User updatedBy,
    int? tenantId = null,
    DateTime? startDate = null,
    DateTime? endDate = null,
    bool? ignoreYear = null,
    string? timeFormat = null,
    string fromAddress = "<EMAIL>",
    string? mascotImgUri = null,
    string? mascotName = null,
    string? logoUri = null,
    string? logoAltText = null,
    string backgroundColor = "#000000",
    string foregroundColor = "#000000",
    string tableHeaderColor = "#000000",
    string textColor = "#000000",
    string tableHeaderTextColor = "#000000",
    string description = "some test description")
  {
    var payload = new Mock<IUpdateBrandingPayload>();
    payload.SetupGet(p => p.Id).Returns(existing.Id);
    payload.SetupGet(p => p.TenantId).Returns(tenantId);
    payload.SetupGet(p => p.StartDate).Returns(startDate);
    payload.SetupGet(p => p.EndDate).Returns(endDate);
    payload.SetupGet(p => p.IgnoreYear).Returns(ignoreYear);
    payload.SetupGet(p => p.TimeFormat).Returns(timeFormat);
    payload.SetupGet(p => p.FromAddress).Returns(fromAddress);
    payload.SetupGet(p => p.MascotImgUri).Returns(mascotImgUri);
    payload.SetupGet(p => p.MascotName).Returns(mascotName);
    payload.SetupGet(p => p.LogoUri).Returns(logoUri);
    payload.SetupGet(p => p.LogoAltText).Returns(logoAltText);
    payload.SetupGet(p => p.BackgroundColor).Returns(backgroundColor);
    payload.SetupGet(p => p.ForegroundColor).Returns(foregroundColor);
    payload.SetupGet(p => p.TableHeaderColor).Returns(tableHeaderColor);
    payload.SetupGet(p => p.Description).Returns(description);
    payload.SetupGet(p => p.TextColor).Returns(textColor);
    payload.SetupGet(p => p.TableHeaderTextColor).Returns(tableHeaderTextColor);
    return payload;
  }

  protected static CreateLocalTargetAssignmentPayload MockCreateTargetAssignmentPayload(
    User createdBy,
    TargetType targetType,
    MaintenanceType maintenanceType,
    string maintenanceIdentifier,
    string? target,
    DesiredSoftwareState desiredSoftwareState,
    SemanticVersion? softwareSemanticVersion = null,
    TargetAssignmentVisibilityPayload? visibility = null)
  {
    var payload = new CreateLocalTargetAssignmentPayload
    {
      Target = target,
      TargetType = targetType,
      MaintenanceType = maintenanceType,
      MaintenanceIdentifier = maintenanceIdentifier,
      DesiredSoftwareState = desiredSoftwareState,
      SoftwareSemanticVersion = softwareSemanticVersion,
      Visibility = visibility
    };

    return payload;
  }

  protected static UpdateLocalTargetAssignmentPayload MockUpdateTargetAssignmentPayload(
    User user,
    TargetAssignment assignmentToUpdate,
    TargetType? targetType = TargetType.All,
    string? target = null,
    MaintenanceType? maintenanceType = null,
    string? maintenanceIdentifier = null,
    DesiredSoftwareState? desiredSoftwareState = null,
    SemanticVersion? softwareSemanticVersion = null,
    TargetAssignmentVisibilityPayload? visibility = null)
  {
    var payload = new UpdateLocalTargetAssignmentPayload
    {
      Id = assignmentToUpdate.Id,
      Target = target ?? assignmentToUpdate.Target,
      TargetType = targetType ?? assignmentToUpdate.TargetType,
      MaintenanceType = maintenanceType ?? assignmentToUpdate.MaintenanceType,
      MaintenanceIdentifier = maintenanceIdentifier ?? assignmentToUpdate.MaintenanceIdentifier,
      DesiredSoftwareState = desiredSoftwareState ?? assignmentToUpdate.DesiredSoftwareState,
      SoftwareSemanticVersion = softwareSemanticVersion ?? assignmentToUpdate.SoftwareSemanticVersion,
      TargetGroupFilter = default,
      Visibility = visibility
    };
    return payload;
  }

  protected static Mock<ICreateLicensePayload> MockCreateLicensePayload(
    int createdBy,
    string? name,
    string? softwareIdentifier,
    SoftwareType softwareType,
    string? licenseValue,
    LicenseType licenseType,
    SemanticVersion? version,
    int? tenantId
  )
  {
    var pl = new Mock<ICreateLicensePayload>();
    // forcibly allow null
    pl.SetupGet(p => p.Name).Returns(name!);
    // forcibly allow null
    pl.SetupGet(p => p.SoftwareIdentifier).Returns(softwareIdentifier!);
    pl.SetupGet(p => p.SoftwareType).Returns(softwareType);
    // forcibly allow null
    pl.SetupGet(p => p.LicenseValue).Returns(licenseValue!);
    pl.SetupGet(p => p.SemanticVersion).Returns(version);
    pl.SetupGet(p => p.TenantId).Returns(tenantId);
    // forcibly allow null
    pl.SetupGet(p => p.SoftwareName).Returns(softwareIdentifier!);
    return pl;
  }

  protected static Mock<IUpdateLicensePayload> MockUpdateLicensePayload(
   License licenseToUpdate,
   int updatedBy,
   string? name = null,
   string? softwareIdentifier = null,
    SoftwareType? softwareType = null,
    string? licenseValue = null,
    LicenseType? licenseType = null,
    SemanticVersion? version = null,
    int? tenantId = null)
  {
    var pl = new Mock<IUpdateLicensePayload>();

    pl.SetupGet(p => p.Id).Returns(licenseToUpdate.Id);
    pl.SetupGet(p => p.Name).Returns(name ?? licenseToUpdate.Name);
    pl.SetupGet(p => p.SoftwareIdentifier).Returns(softwareIdentifier ?? licenseToUpdate.SoftwareIdentifier);
    pl.SetupGet(p => p.SoftwareType).Returns(softwareType ?? licenseToUpdate.SoftwareType);
    pl.SetupGet(p => p.LicenseValue).Returns(licenseValue ?? licenseToUpdate.LicenseValue);
    pl.SetupGet(p => p.SemanticVersion).Returns(version ?? licenseToUpdate.SemanticVersion);
    pl.SetupGet(p => p.TenantId).Returns(tenantId ?? licenseToUpdate.TenantId);
    pl.SetupGet(p => p.SoftwareName).Returns(softwareIdentifier ?? licenseToUpdate.SoftwareIdentifier);
    return pl;
  }

  protected static Mock<ICreateLocalSoftwarePayload> MockCreateLocalSoftwarePayload(
    string name,
    int createdBy,
    bool hidden = false,
    SoftwareLicenseRequirement licenseRequirement = SoftwareLicenseRequirement.None,
    ICollection<SoftwarePrerequisite>? prerequisites = null,
    ICollection<int>? tenantSoftware = null)
  {
    var createPayload = new Mock<ICreateLocalSoftwarePayload>();
    createPayload.SetupGet(p => p.Name).Returns(name);
    createPayload.SetupGet(p => p.Hidden).Returns(hidden);
    createPayload.SetupGet(p => p.LicenseRequirement).Returns(licenseRequirement);
    createPayload.SetupGet(p => p.SoftwarePrerequisites).Returns(prerequisites ?? []);
    createPayload.SetupGet(p => p.TenantSoftware).Returns(tenantSoftware ?? []);
    return createPayload;
  }

  protected static Mock<ICreateLocalSoftwareVersionPayload> MockCreateLocalSoftwareVersionPayload(
    LocalSoftware software,
    string semanticVersion,
    int createdBy,
    LicenseType licenseType = LicenseType.None)
  {
    var createPayload = new Mock<ICreateLocalSoftwareVersionPayload>();
    createPayload.SetupGet(p => p.SoftwareId).Returns(software.Id);
    createPayload.SetupGet(p => p.SemanticVersion).Returns(NuGetVersion.Parse(semanticVersion));

    return createPayload;
  }

  protected static Mock<IUpdateLocalSoftwarePayload> MockUpdateLocalSoftwarePayload(
    LocalSoftware softwareToUpdate,
    int updatedBy,
    string? name = null,
    bool? hidden = null,
    SoftwareLicenseRequirement? licenseRequirement = null,
    ICollection<SoftwarePrerequisite>? prerequisites = null,
    ICollection<int>? tenantSoftware = null)
  {
    var updatePayload = new Mock<IUpdateLocalSoftwarePayload>();
    updatePayload.SetupGet(p => p.SoftwareId).Returns(softwareToUpdate.Id);
    updatePayload.SetupGet(p => p.Hidden).Returns(hidden ?? softwareToUpdate.Hidden);
    updatePayload.SetupGet(p => p.LicenseRequirement).Returns(licenseRequirement ?? softwareToUpdate.LicenseRequirement);
    updatePayload.SetupGet(p => p.Name).Returns(name ?? softwareToUpdate.Name);
    updatePayload.SetupGet(p => p.SoftwarePrerequisites).Returns(prerequisites ?? softwareToUpdate.SoftwarePrerequisites);
    updatePayload.SetupGet(p => p.TenantSoftware).Returns(tenantSoftware ?? []);
    return updatePayload;
  }

  protected static Mock<IUpdateLocalSoftwareVersionPayload> MockUpdateLocalSoftwareVersionPayload(
    LocalSoftwareVersion softwareVersion,
    int updatedBy,
    string? newSemanticVersion = null,
    LicenseType licenseType = LicenseType.None)
  {
    var updatePayload = new Mock<IUpdateLocalSoftwareVersionPayload>();
    updatePayload.SetupGet(p => p.SoftwareId).Returns(softwareVersion.SoftwareId);
    updatePayload.SetupGet(p => p.CurrentSemanticVersion).Returns(softwareVersion.SemanticVersion);
    updatePayload.SetupGet(p => p.SemanticVersion).Returns(newSemanticVersion != null ? NuGetVersion.Parse(newSemanticVersion) : softwareVersion.SemanticVersion);

    return updatePayload;
  }

  protected static Mock<ICreateGlobalSoftwarePayload> MockCreateGlobalSoftwarePayload(
    string name,
    bool hidden = false,
    SoftwareLicenseRequirement licenseRequirement = SoftwareLicenseRequirement.None,
    ICollection<SoftwarePrerequisite>? prerequisites = null)
  {
    var createPayload = new Mock<ICreateGlobalSoftwarePayload>();
    createPayload.SetupGet(p => p.Name).Returns(name);
    createPayload.SetupGet(p => p.Hidden).Returns(hidden);
    createPayload.SetupGet(p => p.LicenseRequirement).Returns(licenseRequirement);
    createPayload.SetupGet(p => p.SoftwarePrerequisites).Returns(prerequisites ?? []);

    return createPayload;
  }

  protected static Mock<ICreateGlobalSoftwareVersionPayload> MockCreateGlobalSoftwareVersionPayload(
    GlobalSoftware software,
    string semanticVersion,
    LicenseType licenseType = LicenseType.None)
  {
    var createPayload = new Mock<ICreateGlobalSoftwareVersionPayload>();
    createPayload.SetupGet(p => p.SoftwareId).Returns(software.Id);
    createPayload.SetupGet(p => p.SemanticVersion).Returns(NuGetVersion.Parse(semanticVersion));
    return createPayload;
  }

  protected static Mock<ICreateTenantPayload> MockCreateTenantPayload(
    string name,
    string? principalId = null,
    bool? active = null,
    bool isMsp = false)
  {
    var payload = new Mock<ICreateTenantPayload>();
    payload.SetupGet(p => p.Name).Returns(name);
    payload.SetupGet(p => p.PrincipalId).Returns(principalId);
    if (active.HasValue)
    {
      payload.SetupGet(p => p.Active).Returns(active.Value);
    }
    payload.SetupGet(p => p.IsMsp).Returns(isMsp);
    return payload;
  }
  protected static Mock<IUpdateGlobalSoftwarePayload> MockUpdateGlobalSoftwarePayload(
    GlobalSoftware softwareToUpdate,
    string? name = null,
    bool? hidden = null,
    SoftwareLicenseRequirement? licenseRequirement = null,
    ICollection<SoftwarePrerequisite>? prerequisites = null)
  {
    var updatePayload = new Mock<IUpdateGlobalSoftwarePayload>();
    updatePayload.SetupGet(p => p.SoftwareId).Returns(softwareToUpdate.Id);
    updatePayload.SetupGet(p => p.Hidden).Returns(hidden ?? softwareToUpdate.Hidden);
    updatePayload.SetupGet(p => p.LicenseRequirement).Returns(licenseRequirement ?? softwareToUpdate.LicenseRequirement);
    updatePayload.SetupGet(p => p.Name).Returns(name ?? softwareToUpdate.Name);
    updatePayload.SetupGet(p => p.SoftwarePrerequisites).Returns(prerequisites ?? softwareToUpdate.SoftwarePrerequisites);
    return updatePayload;
  }

  protected static Mock<IUpdateGlobalSoftwareVersionPayload> MockUpdateGlobalSoftwareVersionPayload(
    GlobalSoftwareVersion softwareVersion,
    string? newSemanticVersion = null,
    LicenseType licenseType = LicenseType.None)
  {
    var updatePayload = new Mock<IUpdateGlobalSoftwareVersionPayload>();
    updatePayload.SetupGet(p => p.SoftwareId).Returns(softwareVersion.SoftwareId);
    updatePayload.SetupGet(p => p.CurrentSemanticVersion).Returns(softwareVersion.SemanticVersion);
    updatePayload.SetupGet(p => p.SemanticVersion).Returns(newSemanticVersion != null ? NuGetVersion.Parse(newSemanticVersion) : softwareVersion.SemanticVersion);
    return updatePayload;
  }

  protected static CreateLocalMaintenanceTaskPayload MockCreateLocalMaintenanceTaskPayload(
    string name,
    int createdBy,
    List<MaintenanceTaskParameterPayload>? parameters = null
  )
  {
    var payload = new CreateLocalMaintenanceTaskPayload { Name = name };

    if (parameters?.Any() == true)
    {
      foreach (var p in parameters)
      {
        payload.Parameters.Add(p);
      }
    }

    return payload;
  }

  protected static CreateGlobalMaintenanceTaskPayload MockCreateGlobalMaintenanceTaskPayload(
    string name,
    List<MaintenanceTaskParameterPayload>? parameters = null
  )
  {
    var payload = new CreateGlobalMaintenanceTaskPayload { Name = "" };
    payload.Name = name;

    if (parameters?.Any() == true)
    {
      foreach (var p in parameters)
      {
        payload.Parameters.Add(p);
      }
    }
    return payload;
  }

  protected static UpdateLocalMaintenanceTaskPayload MockUpdateLocalMaintenanceTaskPayload(
    MaintenanceTask task,
    int updatedBy,
    string? name = null,
    List<MaintenanceTaskParameterPayload>? parameters = null
  )
  {
    var payload = new UpdateLocalMaintenanceTaskPayload { Name = name ?? task.Name, Id = task.Id };

    if (parameters?.Any() == true)
    {
      foreach (var p in parameters)
      {
        payload.Parameters.Add(p);
      }
    }

    return payload;
  }

  protected static UpdateGlobalMaintenanceTaskPayload MockUpdateGlobalMaintenanceTaskPayload(
    MaintenanceTask task,
    string? name = null,
    List<MaintenanceTaskParameterPayload>? parameters = null
  )
  {
    var payload = new UpdateGlobalMaintenanceTaskPayload { Id = task.Id, Name = name ?? task.Name };

    if (parameters?.Any() == true)
    {
      foreach (var p in parameters)
      {
        payload.Parameters.Add(p);
      }
    }
    return payload;
  }
}
