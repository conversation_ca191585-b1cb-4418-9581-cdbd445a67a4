using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Extensions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.Shared.Lib;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.UnitTests.ContextTests;

public class ComputerExtensionTests(ITestOutputHelper helper) : BaseUnitTests(helper, false)
{
  [Fact]
  public void GetsAllComputersInSystem()
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid()
    );
    CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "2",
      tenantId: tenant.Id,
      computerName: "2",
      deviceId: Guid.NewGuid()
    );
    CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "3",
      tenantId: tenant.Id,
      computerName: "3",
      deviceId: Guid.NewGuid()
    );

    // act
    var c = ctx.GetAllComputers().ToList();

    // assert
    Assert.Equal(3, c.Count);
  }

  [Theory]
  [CombinatorialData]
  public void GetAllComputers_ShouldIncludeDescendentTenantComputersIfSpecified(bool includeChildTenants)
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var mspTenant = GetOrCreateMspTenant();
    var parentTenant = GetOrCreateCustomerTenant();
    var childTenant = CreateTenant(ownerTenantId: mspTenant.Id, parentTenantId: parentTenant.Id);
    var grandchildTenant = CreateTenant(ownerTenantId: mspTenant.Id, parentTenantId: childTenant.Id);

    var link = GetOrCreateDefaultProviderLink(mspTenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    var mspTenantComputer = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: mspTenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid()
    );
    var parentTenantComputer = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "2",
      tenantId: parentTenant.Id,
      computerName: "2",
      deviceId: Guid.NewGuid()
    );
    var childTenantComputer = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "3",
      tenantId: childTenant.Id,
      computerName: "3",
      deviceId: Guid.NewGuid()
    );
    var grandchildTenantComputer = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "4",
      tenantId: grandchildTenant.Id,
      computerName: "4",
      deviceId: Guid.NewGuid()
    );

    // act
    var c = ctx
      .GetAllComputers(tenantId: parentTenant.Id, includeChildTenants: includeChildTenants)
      .ToList();

    // assert
    Assert.Null(c.SingleOrDefault(a => a.Id == mspTenantComputer.Id));
    Assert.NotNull(c.SingleOrDefault(a => a.Id == parentTenantComputer.Id));
    if (includeChildTenants)
    {
      Assert.NotNull(c.SingleOrDefault(a => a.Id == childTenantComputer.Id));
      Assert.NotNull(c.SingleOrDefault(a => a.Id == grandchildTenantComputer.Id));
    }
    else
    {
      Assert.Null(c.SingleOrDefault(a => a.Id == childTenantComputer.Id));
      Assert.Null(c.SingleOrDefault(a => a.Id == grandchildTenantComputer.Id));
    }
  }

  [Fact]
  public void GetsComputerById()
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    var computer = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid()
    );

    // act
    var c = ctx.GetComputerById(computer.Id);

    // assert
    Assert.Equal(computer.Id, c?.Id);
  }

  [Fact]
  public void ComputerShouldBeOwnedByTenant()
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    var computer = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid()
    );

    // act
    var c = ctx.IsComputerOwnedByTenant(computer.Id, tenant.Id);

    // assert
    Assert.True(c);
  }

  [Fact]
  public void GetsComputersForProvider()
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    _ = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid()
    );
    _ = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "2",
      tenantId: tenant.Id,
      computerName: "2",
      deviceId: Guid.NewGuid()
    );


    // act
    var c = ctx.GetComputersForProviderLink(link.Id).ToList();

    // assert
    Assert.Equal(2, c.Count);
  }

  [Fact]
  public void GetsAllComputersForPrimaryPerson()
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    var person = CreatePerson(
      tenantId: tenant.Id,
      emailAddress: "<EMAIL>",
      firstName: "immy",
      lastName: "bot"
    );
    CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      primaryPersonId: person.Id,
      deviceId: Guid.NewGuid()
    );

    // act
    var c = ctx.GetAllComputersForPrimaryPerson(person.Id).ToList();

    // assert
    Assert.Single(c);
  }

  [Theory]
  [CombinatorialData]
  public void SetComputerOnboardingStatus_ShouldSetOnboardingDateAppropriately(
    [CombinatorialValues(ComputerOnboardingStatus.NeedsOnboarding, ComputerOnboardingStatus.Onboarded)]
    ComputerOnboardingStatus updateStatusFrom,
    bool hasOnboardingDate)
  {
    // arrange
    var updateStatusTo = updateStatusFrom == ComputerOnboardingStatus.NeedsOnboarding
      ? ComputerOnboardingStatus.Onboarded
      : ComputerOnboardingStatus.NeedsOnboarding;
    var ctx = GetSqliteDbContext();
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    DateTime? onboardedDate = hasOnboardingDate ? DateTime.UtcNow.AddDays(-1) : null;
    var computer = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid(),
      onboardingStatus: updateStatusFrom,
      onboardedDate: onboardedDate);

    // act
    ctx.SetComputerOnboardingStatus(computer.Id, updateStatusTo);

    // assert
    var c = ctx.GetComputerById(computer.Id, asNoTracking: true)!;
    if (updateStatusTo == ComputerOnboardingStatus.Onboarded && !hasOnboardingDate)
    {
      // Should set onboarding date if updating to onboarded and not already set
      Assert.True(c.OnboardedDateUtc > DateTime.UtcNow.AddMinutes(-1));
    }
    else if (updateStatusTo == ComputerOnboardingStatus.Onboarded)
    {
      // Should not change onboarding date if updating to onboarded and already set
      //
      // This is to prevent user from setting a previously-onboarded computer to needs-onboarding
      // then back to onboarded in order to reset the onboarded date
      //
      Assert.Equal(onboardedDate, c.OnboardedDateUtc);
    }
    else
    {
      // Should never change onboarding date if not updating to onboarded
      Assert.Equal(onboardedDate, c.OnboardedDateUtc);
    }

    Assert.Equal(updateStatusTo, c.OnboardingStatus);
  }

  [Fact]
  public void GetIsWorkstation_ShouldReturnFalse_WhenChassisTypesIsNull()
  {
    var computer = new Computer() { ChassisTypes = null };
    Assert.False(computer.GetIsWorkstation());
  }

  [Fact]
  public void AsExpandedComputers_ShouldReturnFalse_ForIsDesktop_WhenChassisTypesIsNull()
  {
    var computer = new Computer()
    {
      ChassisTypes = null,
      OperatingSystem = "test",
      PrimaryPerson = new Person() { EmailAddress = "" },
      Tenant = new Tenant() { Name = "test"},
      LatestInventoryScriptResults = new List<ComputerInventoryTaskScriptResult>()
    };
    var queryable = new List<Computer>() { computer }.AsQueryable();

    var res = queryable.AsExpandedComputers().ToList();

    Assert.False(res[0].IsDesktop);
  }

  private static readonly string[] _primaryPersonIdTextArray = ["PrimaryPersonId"];

  [Theory]
  [CombinatorialData]
  public async Task SetComputerPrimaryPersonId_ShouldUpdateAuditTable_WhenPrimaryPersonChanges(
    bool removePrimaryPerson)
  {
    // arrange
    var tenant = GetOrCreateMspTenant();
    var user = GetOrCreateDefaultUser(tenant.Id);

    var primaryPerson = CreatePerson(tenant.Id, "<EMAIL>");
    int? oldPrimaryPersonId = removePrimaryPerson ? primaryPerson.Id : null;
    var computer = CreateComputer(primaryPersonId: oldPrimaryPersonId);

    // act
    var ctx = GetSqliteDbContext();
    var newPrimaryPerson = CreatePerson(tenant.Id, "<EMAIL>");
    int? newPrimaryPersonId = removePrimaryPerson ? null : newPrimaryPerson.Id;
    await ctx.SetComputerPrimaryPersonId(computer.Id,
      newPrimaryPersonId,
      new AuditUserDetails(user.Id, user.AzurePrincipalId, user.DisplayName));
    var audit = await ctx.Audits.AsNoTracking()
      .FirstOrDefaultAsync(a =>
        a.PrimaryKey == computer.Id.ToString() && a.ObjectType == "Computer" && a.Type == "Update");

    // assert
    Assert.NotNull(audit);
    var affectedProperties =
      System.Text.Json.JsonSerializer.Serialize(_primaryPersonIdTextArray);
    var oldValues =
      System.Text.Json.JsonSerializer.Serialize(new { PrimaryPersonId = oldPrimaryPersonId });
    var newValues =
      System.Text.Json.JsonSerializer.Serialize(new { PrimaryPersonId = newPrimaryPersonId });
    Assert.Equal(affectedProperties, audit.AffectedProperties);
    Assert.Equal(oldValues, audit.OldValues);
    Assert.Equal(newValues, audit.NewValues);
  }

  [Theory]
  [CombinatorialData]
  public void GetClaimedExpiredDevLabComputers_ShouldReturnComputersWithExpiredClaimDate(
    bool isDevLabVm,
    bool isExpired,
    bool isUnclaimed)
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    _ = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid(),
      devLabExpirationDate: DateTime.UtcNow.AddDays(isExpired ? -1 : 1),
      isDevLabUnclaimed: isUnclaimed,
      isDevLabVm: isDevLabVm
    );

    // act
    var c = ctx.GetClaimedExpiredDevLabComputers(DateTime.UtcNow).ToList();

    // assert
    if (isDevLabVm && isExpired && !isUnclaimed)
    {
      Assert.Single(c);
    }
    else
    {
      Assert.Empty(c);
    }
  }

  [Theory]
  [CombinatorialData]
  public void GetClaimedDevLabComputersWithNoExpiration_ShouldReturnComputersWithNoExpirationDate(
    bool isDevLabVm,
    bool hasExpirationDate,
    bool isUnclaimed)
  {
    // arrange
    var ctx = GetSqliteDbContext();
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    _ = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid(),
      devLabExpirationDate: hasExpirationDate ? DateTime.UtcNow : null,
      isDevLabUnclaimed: isUnclaimed,
      isDevLabVm: isDevLabVm
    );

    // act
    var c = ctx.GetClaimedDevLabComputersWithNoExpiration().ToList();

    // assert
    if (isDevLabVm && !hasExpirationDate && !isUnclaimed)
    {
      Assert.Single(c);
    }
    else
    {
      Assert.Empty(c);
    }
  }

  [Theory]
  [CombinatorialData]
  public async Task Computer_ShortCircuitNoResults_ShouldWork(bool shortCircuit)
  {
    // arrange
    await using var ctx = GetSqliteDbContext();
    var computer = CreateComputer();

    // act

    IQueryable<Computer> query;
    if (shortCircuit)
    {
      query = ctx.ShortCircuitNoResults<Computer>();
    }
    else
    {
      query = ctx.Computers.AsNoTracking();
    }

    // assert

    var res = await query.ToListAsync();
    if (shortCircuit)
    {
      Assert.Empty(res);
    }
    else
    {
      Assert.NotEmpty(res);
    }
  }
}
