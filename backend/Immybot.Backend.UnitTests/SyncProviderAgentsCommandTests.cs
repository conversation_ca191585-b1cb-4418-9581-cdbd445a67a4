using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Backend.UnitTests.ContextTests;
using Immybot.Shared.Primitives;
using Moq;

namespace Immybot.Backend.UnitTests;

public class SyncProviderAgentsCommandTests : ActionsTestBase
{
  private readonly Mock<ILogger<SyncProviderAgentsCommand>> _logger;
  private readonly Mock<IProviderAgentEventHandler> _providerAgentEventHandler;
  private readonly Mock<IProviderStore> _providerStore;
  private readonly SyncProviderAgentsCommand _cmd;
  private readonly int _providerLinkId = 1;
  private readonly List<ProviderAgent> _knownAgents;
  private readonly List<ProviderClient> _knownClients;


  public SyncProviderAgentsCommandTests()
  {
    _logger = new Mock<ILogger<SyncProviderAgentsCommand>>();
    _providerAgentEventHandler = new Mock<IProviderAgentEventHandler>();
    _providerStore = new Mock<IProviderStore>();

    _knownAgents =
    [
      MakeProviderAgent(1, "Agent1", "Client1"),
      MakeProviderAgent(2, "Agent2", "Client2"),
      MakeProviderAgent(3, "Agent3", "Client3"),
    ];

    _knownClients =
    [
      MakeProviderClient(1, "Client1", "Client1"),
      MakeProviderClient(2, "Client2", "Client2"),
      MakeProviderClient(3, "Client3", "Client3")
    ];

    _providerStore
      .Setup(a => a.GetAgentsForProviderLink(_providerLinkId, It.IsAny<IList<string>>(), It.IsAny<bool>()))
      .Returns(DisposableValue.Create(_knownAgents.AsQueryable(), onDisposedCallback: () => { }));

    _providerStore
      .Setup(a => a.GetClientsForProviderLink(_providerLinkId))
      .Returns(DisposableValue.Create(_knownClients.AsQueryable(), onDisposedCallback: () => { }));

    _cmd = new SyncProviderAgentsCommand(
      _logger.Object,
      _providerAgentEventHandler.Object,
      _providerStore.Object
    );
  }

  private ProviderAgent MakeProviderAgent(int id, string agentId, string clientId)
  {
    return new ProviderAgent
    {
      Id = id,
      ExternalAgentId = agentId,
      ExternalClientId = clientId,
      ProviderLinkId = _providerLinkId,
      DeviceDetails = new DeviceDetails()
    };
  }

  private ProviderClient MakeProviderClient(int tenantId, string clientId, string clientName)
  {
    return new ProviderClient
    {
      ExternalClientId = clientId,
      ProviderLinkId = _providerLinkId,
      ExternalClientName = clientName,
      LinkedToTenantId = tenantId
    };
  }

  [Fact]
  public async Task Sync_ShouldDeleteAgents_ThatHaveBeenRemovedFromSource()
  {
    // arrange
    var agents = new List<IProviderAgentDetails> { _knownAgents[0] };
    var deletedAgents = _knownAgents.Except(agents);

    // act
    await _cmd.Sync(
      _providerLinkId,
      agents,
      new List<string>(),
      CancellationToken.None);

    // assert that AgentsDeletedAsync has been called with the expected deleted agents
    _providerAgentEventHandler.Verify(
      a => a.AgentsDeletedAsync(
        _providerLinkId,
        It.Is<ICollection<(string clientId, string agentId)>>(z =>
          z.All(b => deletedAgents.Any(c => c.ExternalAgentId == b.agentId && c.ExternalClientId == b.clientId))),
        null,
        It.IsAny<CancellationToken>()));
  }

  [Fact]
  public async Task Sync_ShouldDeleteAgents_WhenClientsAreNoLongerLinked()
  {
    // arrange

    // unlink all clients
    _knownClients.ForEach(a => a.LinkedToTenantId = null);

    // act
    await _cmd.Sync(
      _providerLinkId,
      _knownAgents.OfType<IProviderAgentDetails>().ToList(),
      new List<string>(),
      CancellationToken.None);

    // assert that AgentsDeletedAsync has been called with the expected deleted agents
    _providerAgentEventHandler.Verify(
      a => a.AgentsDeletedAsync(
        _providerLinkId,
        It.Is<ICollection<(string clientId, string agentId)>>(b => b.Count == _knownAgents.Count),
        null,
        It.IsAny<CancellationToken>()));
  }

  [Fact]
  public async Task Sync_ShouldDisconnectAgents_WhenAgentsAreDisconnected()
  {
    // arrange

    // set all existing agents to online
    _knownAgents.ForEach(a => a.IsOnline = true);

    // set one agent offline
    var existingAgent = _knownAgents[0];
    var offlineAgent = MakeProviderAgent(
      existingAgent.Id,
      existingAgent.ExternalAgentId,
      existingAgent.ExternalClientId);

    var newAgents = _knownAgents.Except(new List<ProviderAgent>() { existingAgent }).ToList();
    newAgents.Add(offlineAgent);

    // act
    await _cmd.Sync(
      _providerLinkId,
      newAgents.OfType<IProviderAgentDetails>().ToList(),
      token: CancellationToken.None);

    var onlyContainsOfflineAgent = (ICollection<(string clientId, string agentId)> disconnectedAgents) =>
    {
      var first = disconnectedAgents.First();
      return disconnectedAgents.Count == 1 && first.agentId == offlineAgent.ExternalAgentId && first.clientId == offlineAgent.ExternalClientId;
    };

    // assert
    _providerAgentEventHandler.Verify(
      a => a.AgentsDisconnectedAsync(
        _providerLinkId,
        It.Is<ICollection<(string clientId, string agentId)>>(b => onlyContainsOfflineAgent(b)),
        null,
        It.IsAny<CancellationToken>()));
  }

  [Fact]
  public async Task Sync_ShouldConnectAgents_WhenAgentsAreConnected()
  {
    // arrange

    // set one agent online
    var existingAgent = _knownAgents[0];
    var onlineAgent = MakeProviderAgent(
      existingAgent.Id,
      existingAgent.ExternalAgentId,
      existingAgent.ExternalClientId);
    onlineAgent.IsOnline = true;

    var newAgents = _knownAgents.Except(new List<ProviderAgent>() { existingAgent }).ToList();
    newAgents.Add(onlineAgent);

    // act
    await _cmd.Sync(
      _providerLinkId,
      newAgents.OfType<IProviderAgentDetails>().ToList(),
      token: CancellationToken.None);

    var onlineContainsOnlineAgent = (ICollection<(string clientId, string agentId)> connectedAgents) =>
    {
      var first = connectedAgents.First();
      return connectedAgents.Count == 1 && first.agentId == onlineAgent.ExternalAgentId && first.clientId == onlineAgent.ExternalClientId;
    };

    // assert
    _providerAgentEventHandler.Verify(
      a => a.AgentsConnectedAsync(
        _providerLinkId,
        It.Is<ICollection<(string clientId, string agentId)>>(b => onlineContainsOnlineAgent(b)),
        null,
        It.IsAny<CancellationToken>()));
  }

  [Fact]
  public async Task Sync_ShouldCreateAgents_WhenAgentsAreNotAlreadyKnown()
  {
    // arrange
    var newAgents = _knownAgents.ToList();
    _knownAgents.Clear();

    // act
    await _cmd.Sync(
      _providerLinkId,
      newAgents.OfType<IProviderAgentDetails>().ToList(),
      token: CancellationToken.None);

    // assert
    _providerAgentEventHandler.Verify(
      a => a.AgentsCreatedAsync(
        true,
        _providerLinkId,
        It.Is<ICollection<IProviderAgentDetails>>(b => b.Count == newAgents.Count),
        null,
        It.IsAny<CancellationToken>()));
  }

  [Fact]
  public async Task Sync_ShouldCreateAgents_WhenAgentsAreNotAlreadyKnown_OnlyForSpecifiedClientIds()
  {
    // arrange
    var newAgents = _knownAgents.ToList();
    _knownAgents.Clear();

    var clientToSync = _knownClients[0];

    // act
    await _cmd.Sync(
      _providerLinkId,
      newAgents.OfType<IProviderAgentDetails>().ToList(),
      new List<string>() { clientToSync.ExternalClientId },
      CancellationToken.None);

    // assert
    _providerAgentEventHandler.Verify(
      a => a.AgentsCreatedAsync(
        true,
        _providerLinkId,
        It.Is<ICollection<IProviderAgentDetails>>(b => b.Count == 1),
        null,
        It.IsAny<CancellationToken>()));
  }
}
