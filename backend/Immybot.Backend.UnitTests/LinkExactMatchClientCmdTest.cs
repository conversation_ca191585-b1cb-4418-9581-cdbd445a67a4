using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.UnitTests.Shared.Lib;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace Immybot.Backend.UnitTests;
public class LinkExactMatchClientCmdTest : BaseUnitTests
{
  [Fact]
  public async Task LinkExactMatchClientCmd_ShouldWork()
  {
    await using var ctx = GetSqliteDbContext();
    var providerActions = new Mock<IProviderActions>();

    const string shouldMatch1 = "shouldMatch1";
    const string shouldMatch2 = "shouldMatch2";
    const string shouldNotMatch = "shouldNotMatch";

    var mspTenant = GetOrCreateMspTenant();

    var shouldMatchTenant1 = CreateTenant(tenantName: shouldMatch1);
    var shouldMatchTenant2 = CreateTenant(tenantName: shouldMatch2);
    _ = CreateTenant(tenantName: shouldNotMatch);

    var link = GetOrCreateDefaultProviderLink(mspTenant.Id);

    _ = CreateProviderClient(
      link.Id,
      name: shouldMatch1);

    _ = CreateProviderClient(
      link.Id,
      name: shouldMatch2);

    _ = CreateProviderClient(
      link.Id,
      name: Guid.NewGuid().ToString());

    var command = new LinkExactMatchClientsCmd(
      GetSqliteDbContextFactory(),
      providerActions.Object);

    _ = await command.Run(new LinkExactMatchClientsPayload(link.Id, false), CancellationToken.None);

    // fetch again from database to ensure it's updated

    var updatedClients = await ctx.ProviderClients
      .AsNoTracking()
      .Where(a => a.ProviderLinkId == link.Id && a.LinkedToTenantId != null)
      .ToListAsync();

    Assert.Equal(2, updatedClients.Count);

    var linkedClient1 = updatedClients.First(a => a.ExternalClientName == shouldMatch1);
    Assert.Equal(shouldMatchTenant1.Id, linkedClient1.LinkedToTenantId);

    var linkedClient2 = updatedClients.First(a => a.ExternalClientName == shouldMatch2);
    Assert.Equal(shouldMatchTenant2.Id, linkedClient2.LinkedToTenantId);

    var notLinkedClient = updatedClients.Find(a => a.ExternalClientName == shouldNotMatch);
    Assert.Null(notLinkedClient);
  }
}
