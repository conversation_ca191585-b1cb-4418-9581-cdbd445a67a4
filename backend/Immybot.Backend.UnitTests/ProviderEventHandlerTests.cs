using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Providers;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence;
using Immybot.Backend.UnitTests.ContextTests;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace Immybot.Backend.UnitTests;

public class ProviderEventHandlerTests : ActionsTestBase
{
  private readonly ProviderEventHandler _handler;
  private readonly Func<ImmybotDbContext> _dbFactory;
  private readonly ProviderLink _link;
  private readonly Guid _key;
  private readonly ProviderClient _client;
  private readonly Tenant _tenant;
  private readonly Mock<IDomainEventEmitter> _eventEmitter;

  public ProviderEventHandlerTests()
  {
    // arrange
    _dbFactory = GetSqliteDbContextFactory();
    var loggerFactory = Helpers.MockLoggerFactory();
    var linkLocker = new ProviderLinkDataConsistencyLocker();
    _eventEmitter = new Mock<IDomainEventEmitter>();
    var namesCache = new CachedSingleton<ProviderLinkNames>();
    _handler = new ProviderEventHandler(
      _dbFactory,
      loggerFactory,
      linkLocker,
      _eventEmitter.Object,
      namesCache,
      Helpers.MockOptionsMonitor(new AppSettingsOptions()));

    _tenant = GetOrCreateMspTenant();
    _link = GetOrCreateDefaultProviderLink(_tenant.Id);
    _client = GetOrCreateDefaultProviderClient(_link.Id);
    _key = linkLocker.ManufactureLockKey(_link.Id);
  }

  [Theory]
  [InlineData("some Mac os", false)]
  [InlineData("Some macos", false)]
  [InlineData("Linux", false)]
  [InlineData("Windows 11", true)]
  [InlineData("Microsoft Server", true)]
  public async Task AgentsCreatedAsync_ShouldCreateAgent_WhenOperatingSystem_IsWindows(string osName, bool shouldCreate)
  {
    // arrange
    var agent = new ProviderAgent
    {
      ExternalAgentId = "1",
      ExternalClientId = _client.ExternalClientId,
      DeviceDetails = new DeviceDetails { OperatingSystemName = osName, }
    };

    // act
    await _handler.AgentsCreatedAsync(
      true,
      _link.Id,
      new List<IProviderAgentDetails> { agent },
      _key,
      CancellationToken.None);

    // assert
    await using var ctx = _dbFactory();
    var createdAgent = await ctx.ProviderAgents.FirstOrDefaultAsync();

    if (shouldCreate) Assert.NotNull(createdAgent);
    else Assert.Null(createdAgent);
  }

  [Theory]
  [InlineData("some Mac os", false)]
  [InlineData("Some macos", false)]
  [InlineData("Linux", false)]
  [InlineData("Windows 11", true)]
  [InlineData("Microsoft Server", true)]
  public async Task AgentUpsertedAsync_ShouldCreateAgent_WhenOperatingSystem_IsWindows(string osName, bool shouldCreate)
  {
    // arrange
    var agent = new ProviderAgent
    {
      ExternalAgentId = "1",
      ExternalClientId = _client.ExternalClientId,
      DeviceDetails = new DeviceDetails { OperatingSystemName = osName, }
    };

    // act
    await _handler.AgentsUpsertedAsync(
      _link.Id,
      new List<IProviderAgentDetails> { agent },
      _key,
      CancellationToken.None);

    // assert
    await using var ctx = _dbFactory();
    var createdAgent = await ctx.ProviderAgents.FirstOrDefaultAsync();

    if (shouldCreate) Assert.NotNull(createdAgent);
    else Assert.Null(createdAgent);
  }

  [Theory]
  [CombinatorialData]
  public async Task AgentsUpdatedAsync_ShouldUpdateAgent_For_EnabledClients(bool enabled)
  {
    // arrange
    var agent = new ProviderAgent
    {
      ExternalAgentId = "1",
      ExternalClientId = _client.ExternalClientId,
      DeviceDetails = new DeviceDetails { OperatingSystemName = "windows", DeviceName = "foo"}
    };

    await _handler.AgentsCreatedAsync(
      true,
      _link.Id,
      new List<IProviderAgentDetails> { agent },
      _key,
      CancellationToken.None);

    agent.DeviceDetails.DeviceName = "bar";

    await using var ctx = _dbFactory();
    if (!enabled)
    {
      await ctx.ProviderClients.UpdateFromQueryAsync(a =>
        new ProviderClient { ExternalClientId = agent.ExternalClientId, ExternalClientName = "", LinkedToTenantId = null });
    }

    // act
    await _handler.AgentsUpdatedAsync(
      _link.Id,

      new List<IProviderAgentDetails>()
      {
        agent
      },
      _key,
      CancellationToken.None);

    // assert
    var updatedAgent = await ctx.ProviderAgents.FirstOrDefaultAsync();
    if (enabled) Assert.Equal("bar", updatedAgent?.DeviceDetails.DeviceName);
    else Assert.Null(updatedAgent);
  }

  private async Task<ProviderAgent> CreateThenDeleteAgent(bool isOnline = false)
  {
    var agent = new ProviderAgent
    {
      ExternalAgentId = "1",
      ExternalClientId = _client.ExternalClientId,
      DeviceDetails = new DeviceDetails { OperatingSystemName = "windows", DeviceName = "foo"},
      IsOnline = isOnline,
    };

    await _handler.AgentsCreatedAsync(
      true,
      _link.Id,
      new List<IProviderAgentDetails> { agent },
      _key,
      CancellationToken.None);

    await _handler.AgentsDeletedAsync(
      _link.Id,
      new List<(string clientId, string agentId)>()
      {
        (agent.ExternalClientId, agent.ExternalAgentId)
      },
      _key,
      CancellationToken.None);

    return agent;
  }

  [Fact]
  public async Task AgentsConnectedAsync_ShouldUndeleteAgent()
  {
    var agent = await CreateThenDeleteAgent();

    // act
    await _handler.AgentsConnectedAsync(
      _link.Id,
      new List<(string clientId, string agentId)>()
      {
        (agent.ExternalClientId, agent.ExternalAgentId)
      },
      _key,
      CancellationToken.None);

    // assert
    await using var ctx = _dbFactory();
    var connectedAgent = await ctx.ProviderAgents.FirstOrDefaultAsync();
    Assert.NotNull(connectedAgent);
    Assert.Null(connectedAgent.DeletedAt);
    Assert.Null(connectedAgent.DeletedReason);
  }

  [Fact]
  public async Task AgentsDisconnectedAsync_ShouldUndeleteAgent()
  {
    var agent = await CreateThenDeleteAgent(isOnline: true);

    // act
    await _handler.AgentsDisconnectedAsync(
      _link.Id,
      new List<(string clientId, string agentId)>()
      {
        (agent.ExternalClientId, agent.ExternalAgentId)
      },
      _key,
      CancellationToken.None);

    // assert
    await using var ctx = _dbFactory();
    var connectedAgent = await ctx.ProviderAgents.FirstOrDefaultAsync();
    Assert.NotNull(connectedAgent);
    Assert.Null(connectedAgent.DeletedAt);
    Assert.Null(connectedAgent.DeletedReason);
  }

  [Fact]
  public async Task AgentsUpdatedAsync_ShouldUndeleteAgent()
  {
    var agent = await CreateThenDeleteAgent();

    // act
    await _handler.AgentsUpdatedAsync(
      _link.Id,
      new List<IProviderAgentDetails> { agent },
      _key,
      CancellationToken.None);

    // assert
    await using var ctx = _dbFactory();
    var connectedAgent = await ctx.ProviderAgents.FirstOrDefaultAsync();
    Assert.NotNull(connectedAgent);
    Assert.Null(connectedAgent.DeletedAt);
    Assert.Null(connectedAgent.DeletedReason);
  }
  [Fact]
  public async Task AgentsConnectedAsync_ShouldMarkAgentOnline()
  {
    // arrange
    var agent = new ProviderAgent
    {
      ExternalAgentId = "1",
      ExternalClientId = _client.ExternalClientId,
      DeviceDetails = new DeviceDetails { OperatingSystemName = "windows", DeviceName = "foo"},
      IsOnline = false
    };

    await _handler.AgentsCreatedAsync(
      true,
      _link.Id,
      new List<IProviderAgentDetails> { agent },
      _key,
      CancellationToken.None);

    // act
    await _handler.AgentsConnectedAsync(
      _link.Id,
      new List<(string clientId, string agentId)>()
      {
        (agent.ExternalClientId, agent.ExternalAgentId)
      },
      _key,
      CancellationToken.None);

    // assert
    await using var ctx = _dbFactory();
    var connectedAgent = await ctx.ProviderAgents.FirstOrDefaultAsync();
    Assert.True(connectedAgent?.IsOnline);
  }

  [Fact]
  public async Task AgentsDisconnectedAsync_ShouldMarkAgentOffline()
  {
    // arrange
    var agent = new ProviderAgent
    {
      ExternalAgentId = "1",
      ExternalClientId = _client.ExternalClientId,
      DeviceDetails = new DeviceDetails { OperatingSystemName = "windows", DeviceName = "foo"},
      IsOnline = true
    };

    await _handler.AgentsCreatedAsync(
      true,
      _link.Id,
      new List<IProviderAgentDetails> { agent },
      _key,
      CancellationToken.None);

    // act
    await _handler.AgentsDisconnectedAsync(
      _link.Id,
      new List<(string clientId, string agentId)>()
      {
        (agent.ExternalClientId, agent.ExternalAgentId)
      },
      _key,
      CancellationToken.None);

    // assert
    await using var ctx = _dbFactory();
    var connectedAgent = await ctx.ProviderAgents.FirstOrDefaultAsync();
    Assert.False(connectedAgent?.IsOnline);
  }

  [Fact]
  public async Task AgentsDeletedAsync_ShouldDeleteAgent()
  {
    // arrange
    var agent = new ProviderAgent
    {
      ExternalAgentId = "1",
      ExternalClientId = _client.ExternalClientId,
      DeviceDetails = new DeviceDetails { OperatingSystemName = "windows", DeviceName = "foo"},
      IsOnline = true
    };

    await _handler.AgentsCreatedAsync(
      true,
      _link.Id,
      new List<IProviderAgentDetails> { agent },
      _key,
      CancellationToken.None);

    // act
    await _handler.AgentsDeletedAsync(
      _link.Id,
      new List<(string clientId, string agentId)>()
      {
        (agent.ExternalClientId, agent.ExternalAgentId)
      },
      _key,
      CancellationToken.None);

    // assert
    await using var ctx = _dbFactory();
    var deletedAgent = await ctx.ProviderAgents.FirstOrDefaultAsync();
    Assert.Null(deletedAgent);
  }

  [Fact]
  public async Task AgentsDeletedAsync_ShouldNotDelete_IfAlreadyDeleted()
  {
    await using var ctx = _dbFactory();
    var agent = await CreateThenDeleteAgent();
    _ = await ctx.ProviderAgents
      .AsNoTracking()
      .IgnoreQueryFilters()
      .FirstOrDefaultAsync();

    // loop 10 times
    for (int i = 0; i < 10; i++)
    {
      // act
      await _handler.AgentsDeletedAsync(
        _link.Id,
        new List<(string clientId, string agentId)>() { (agent.ExternalClientId, agent.ExternalAgentId) },
        _key,
        CancellationToken.None);
    }

    // assert
    _eventEmitter.Verify(a => a.EmitEvent(It.IsAny<AgentDeletedEvent>()), Times.Once());
  }
}
