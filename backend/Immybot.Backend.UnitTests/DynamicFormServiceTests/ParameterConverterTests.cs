using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Lib.MetaScripts.Attributes;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.ContextTests;

namespace Immybot.Backend.UnitTests.DynamicFormServiceTests;
public class ParameterConverterTests : ActionsTestBase
{
  private (IDynamicFormService, IRunContext) GetDynamicFormService()
  {
    var (dynamicFormService, runContext, _) = Helpers.BuildDynamicFormService(ctx: GetSqliteDbContextFactory(), sftCtx: GetSqliteSoftwareDbContextFactory());
    return (dynamicFormService, runContext);
  }

  public record MaintenanceTaskParameterTestCase(List<MaintenanceTaskParameter> MaintenanceTaskParameters, List<MaintenanceTaskParameterValue> MaintenanceTaskParameterValues);

  public static MaintenanceTaskParameterTestCase ParameterTestCase1()
  {
    var data = new List<MaintenanceTaskParameter>();
    var values = new List<MaintenanceTaskParameterValue>();

    data.Add(new MaintenanceTaskParameter
    {
      Name = "NumberParam",
      DataType = MaintenanceTaskParameterType.Number,
      DefaultValue = "4",
      Hidden = true,
      Notes = "Some number notes",
      Order = 0,
      Required = false
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "TextParam",
      DataType = MaintenanceTaskParameterType.Text,
      DefaultValue = "text",
      Hidden = false,
      Notes = "Some text notes",
      Order = 1,
      Required = true
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "BooleanParam",
      DataType = MaintenanceTaskParameterType.Boolean,
      Order = 3,
      DefaultValue = "true",
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "SelectParam",
      DataType = MaintenanceTaskParameterType.Select,
      Order = 2,
      SelectableValues = ["Value1", "Value2", "Value3"],
      DefaultValue = "Value1",
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "PasswordParam",
      DataType = MaintenanceTaskParameterType.Password,
      Order = 6
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "MediaParam",
      DataType = MaintenanceTaskParameterType.File,
      Order = 5,
      DefaultMediaId = 4,
      DefaultMediaDatabaseType = DatabaseType.Local
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "UriParam",
      DataType = MaintenanceTaskParameterType.Uri,
      Order = 8,
      Required = true
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "KeyValueParam",
      DataType = MaintenanceTaskParameterType.KeyValuePair,
      Order = 24,
      Required = true,
      DefaultValue = "[[\"test\",\"test\"],[\"test2\",\"test2\"]]"
    });

    return new MaintenanceTaskParameterTestCase(data, values);
  }

  public static MaintenanceTaskParameterTestCase ParameterTestCase2()
  {
    var data = new List<MaintenanceTaskParameter>();
    var values = new List<MaintenanceTaskParameterValue>();

    data.Add(new MaintenanceTaskParameter
    {
      Name = "NumberParam",
      DataType = MaintenanceTaskParameterType.Number,
      DefaultValue = "4",
      Hidden = true,
      Notes = "Some number notes",
      Order = 0,
      Required = false
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "TextParam",
      DataType = MaintenanceTaskParameterType.Text,
      DefaultValue = "text",
      Hidden = false,
      Notes = "Some text notes",
      Order = 1,
      Required = true
    });

    values.Add(new MaintenanceTaskParameterValue
    {
      Value = "text $param ''^&**%# value",
      ParameterName = "TextParam",
      ParameterType = MaintenanceTaskParameterType.Text
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "BooleanParam",
      DataType = MaintenanceTaskParameterType.Boolean,
      Order = 3,
      DefaultValue = "true",
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "SelectParam",
      DataType = MaintenanceTaskParameterType.Select,
      Order = 2,
      SelectableValues = ["Value1", "Value2", "Value3"],
      DefaultValue = "Value1",
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "PasswordParam",
      DataType = MaintenanceTaskParameterType.Password,
      Required = true
    });

    values.Add(new MaintenanceTaskParameterValue
    {
      Value = "%67$326889%4'43#25",
      ParameterName = "PasswordParam",
      ParameterType = MaintenanceTaskParameterType.Password
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "UriParam",
      DataType = MaintenanceTaskParameterType.Uri,
      Order = 8,
      Required = true
    });

    values.Add(new MaintenanceTaskParameterValue
    {
      Value = "https://foo.bar",
      ParameterName = "UriParam",
      ParameterType = MaintenanceTaskParameterType.Uri
    });

    data.Add(new MaintenanceTaskParameter
    {
      Name = "KeyValueParam",
      DataType = MaintenanceTaskParameterType.KeyValuePair,
      Order = 24,
      Required = true,
      DefaultValue = "[[\"test\",\"test\"],[\"test2\",\"test2\"]]"
    });

    values.Add(new MaintenanceTaskParameterValue
    {
      Value = "[[\"asdf\",\"asdf\"],[\"asdf22\",\"2323\"],[\"23sfgg\",\"3222\"]]",
      ParameterName = "KeyValueParam",
      ParameterType = MaintenanceTaskParameterType.KeyValuePair
    });

    return new MaintenanceTaskParameterTestCase(data, values);
  }

  public static IEnumerable<object[]> MaintenanceTaskParameterTestCases()
  {
    yield return [ParameterTestCase1()];
  }

  public static IEnumerable<object[]> MaintenanceTaskParameterValueTestCases()
  {
    yield return [ParameterTestCase2()];
  }

  [Theory]
  [MemberData(nameof(MaintenanceTaskParameterTestCases), DisableDiscoveryEnumeration = true)]
  public async Task GetParamBlockFromMaintenanceTaskParameters_ShouldGenerateExpectedProperties(MaintenanceTaskParameterTestCase testData)
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var paramBlock = ParameterConverter.GetParamBlockFromMaintenanceTaskParameters(testData.MaintenanceTaskParameters);
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, paramBlock, DatabaseType.Local, CancellationToken.None);

    var parameters = bindResult.ShowCommandInfo.ParameterSets[0].Parameters;

    // assert
    var expectedParametersOrdered = testData.MaintenanceTaskParameters.OrderBy(a => a.Order).ToList();
    foreach (var parameter in parameters)
    {
      var expected = expectedParametersOrdered.Find(a => a.Name == parameter.Name);

      if (expected is null)
      {
        throw new Exception("Expected parameter was not found");
      }

      // assert default value
      switch (expected.DataType)
      {
        case MaintenanceTaskParameterType.File:
          var mediaIdentifier = parameter.DefaultValue as MediaIdentifier;
          if (expected.DefaultMediaId is { } mediaId)
          {
            Assert.Equal(mediaId, mediaIdentifier?.Id);
          }

          if (expected.DefaultMediaDatabaseType is { } type)
          {
            Assert.Equal(type, mediaIdentifier?.DatabaseType);
          }
          break;
        case MaintenanceTaskParameterType.Number:
          Assert.Equal(Convert.ToInt32(expected.DefaultValue), parameter.DefaultValue);
          break;
        case MaintenanceTaskParameterType.Boolean:
          Assert.Equal(Convert.ToBoolean(expected.DefaultValue), parameter.DefaultValue);
          break;
        case MaintenanceTaskParameterType.KeyValuePair:
          Assert.NotNull(expected.DefaultValue);
          var arr = System.Text.Json.JsonSerializer.Deserialize<string[][]?>(expected.DefaultValue);
          Assert.Equal(arr?.Length, (parameter.DefaultValue as Hashtable)?.Count);
          break;
        case MaintenanceTaskParameterType.Uri:
        case MaintenanceTaskParameterType.Text:
        case MaintenanceTaskParameterType.Select:
        case MaintenanceTaskParameterType.Password:
          Assert.Equal(expected.DefaultValue, parameter.DefaultValue);
          break;

      }

      Assert.Equal(expected.Name, parameter.Name);
      Assert.Equal(expected.Hidden, parameter.Hidden);
      Assert.Equal(expected.Required, parameter.IsMandatory);
      Assert.Equal(expectedParametersOrdered.IndexOf(expected), parameter.Position);
      Assert.Equal(expected.Notes, parameter.HelpText);
      Assert.Equal(expected.SelectableValues?.ToArray(), parameter.ValidParamSetValues);
      Assert.Equal(ParameterConverter.GetParameterTypeName(expected.DataType), parameter.ParameterType.Name);
    }
  }

  [Theory]
  [MemberData(nameof(MaintenanceTaskParameterValueTestCases), DisableDiscoveryEnumeration = true)]
  public async Task GetParameterValueDictionaryFromMaintenanceTaskParameterValues_ShouldNotHaveBindErrors(MaintenanceTaskParameterTestCase testData)
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var paramBlock = ParameterConverter.GetParamBlockFromMaintenanceTaskParameters(testData.MaintenanceTaskParameters);
    var results = ParameterConverter.GetParameterValueDictionaryFromMaintenanceTaskParameterValues(testData.MaintenanceTaskParameterValues.OfType<IMaintenanceTaskParameterValueDetails>().ToList());
    var bindableDictionary = results.ParameterValues.ToParameterValueDictionary();
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, paramBlock, DatabaseType.Local, CancellationToken.None, bindableDictionary);

    // assert
    Assert.False(bindResult.HasErrors);
    Assert.Empty(bindResult.BindErrors);
    Assert.Equal(7, bindResult.ConvertedParameters.Count);
  }

  [Fact]
  public async Task GetParamBlockFromMaintenanceTaskParameters_ShouldNotExpandVariablesInNotes()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    var parameters = new List<MaintenanceTaskParameter>
    {
      new MaintenanceTaskParameter
      {
        Name = "TestParam",
        DataType = MaintenanceTaskParameterType.Number,
        Order = 0,
        Notes = "$Variable = 1; $($Variable);"
      }
    };

    // act
    var paramBlock = ParameterConverter.GetParamBlockFromMaintenanceTaskParameters(parameters);
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, paramBlock, DatabaseType.Local, CancellationToken.None, []);

    // assert
    Assert.False(bindResult.HasErrors);
  }

  [Fact]
  public async Task GetParamBlockFromMaintenanceTaskParameters_ShouldEscapeSingleQuotesInNotes()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    var parameters = new List<MaintenanceTaskParameter>
    {
      new MaintenanceTaskParameter
      {
        Name = "TestParam",
        DataType = MaintenanceTaskParameterType.Number,
        Order = 0,
        Notes = $"'something in single quotes'"
      }
    };

    // act
    var paramBlock = ParameterConverter.GetParamBlockFromMaintenanceTaskParameters(parameters);
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, paramBlock, DatabaseType.Local, CancellationToken.None, []);

    // assert
    var generatedHelpText = bindResult.ShowCommandInfo.ParameterSets[0].Parameters[0].HelpText;
    Assert.False(bindResult.HasErrors);
    Assert.Equal(parameters[0].Notes, generatedHelpText);
  }

  [Fact]
  public async Task GetParamBlockFromMaintenanceTaskParameters_ShouldEscapeSingleQuotesInDefaultValue()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    var parameters = new List<MaintenanceTaskParameter>
    {
      new MaintenanceTaskParameter
      {
        Name = "TestParam",
        DataType = MaintenanceTaskParameterType.Text,
        Order = 0,
        DefaultValue = "'something in single quotes'"
      }
    };

    // act
    var paramBlock = ParameterConverter.GetParamBlockFromMaintenanceTaskParameters(parameters);
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, paramBlock, DatabaseType.Local, CancellationToken.None, []);

    // assert
    var defaultValue = bindResult.ShowCommandInfo.ParameterSets[0].Parameters[0].DefaultValue;
    Assert.False(bindResult.HasErrors);
    Assert.Equal(parameters[0].DefaultValue, defaultValue);
  }
}

