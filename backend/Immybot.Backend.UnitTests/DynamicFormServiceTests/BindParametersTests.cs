using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Lib.MetaScripts.Attributes;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.ContextTests;

namespace Immybot.Backend.UnitTests.DynamicFormServiceTests;
public class BindParametersTests : ActionsTestBase
{
  private (IDynamicFormService, IRunContext) GetDynamicFormService()
  {
    var (dynamicFormService, runContext, _) = Helpers.BuildDynamicFormService(ctx: GetSqliteDbContextFactory(), sftCtx: GetSqliteSoftwareDbContextFactory());
    return (dynamicFormService, runContext);
  }

  [Fact]
  public async Task BindParameters_ShouldHaveParameter()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(true, true, runContext, "param([int]$MyNumber)", DatabaseType.Local, CancellationToken.None);


    // assert
    Assert.NotNull(bindResult.ShowCommandInfo.Definition);
    Assert.NotEmpty(bindResult.ShowCommandInfo.ParameterSets);
    Assert.NotEmpty(bindResult.ShowCommandInfo.ParameterSets[0].Parameters);
  }

  [Fact]
  public async Task BindParameters_ShouldHaveRuntimeDefinedParameter()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(true, true, runContext, "[CmdletBinding()]param()\ndynamicparam{ New-Parameter -Name Param1 -Mandatory -SkipBind -PassThru } process {}", DatabaseType.Global, CancellationToken.None);


    // assert
    Assert.NotNull(bindResult.ShowCommandInfo.Definition);
    Assert.NotEmpty(bindResult.ShowCommandInfo.ParameterSets);
    Assert.NotEmpty(bindResult.ShowCommandInfo.ParameterSets[0].Parameters);
  }

  [Fact]
  public async Task BindParameters_ShouldAllowDropdownAttribute()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      @"
param(
  [Dropdown({
      $obj = new-object psobject -Property @{
        Id = 1
        Name = ""test""
      }
      @($obj)
    }, ""Id"", ""Name"")]
  $DropdownParam
 )",
      DatabaseType.Local,
      CancellationToken.None);


    // assert

    var dropdownParameter = bindResult.ShowCommandInfo.ParameterSets[0].Parameters[0];

    Assert.False(bindResult.HasErrors);
    Assert.Equal("DropdownParam", dropdownParameter.Name);
    Assert.True(dropdownParameter.HasDropdownParameterSet);
    Assert.Equal("1", dropdownParameter.ValidDropdownValues[0].Value);
    Assert.Equal("test", dropdownParameter.ValidDropdownValues[0].Text);
  }

  [Theory]
  [InlineData(true, "someval", false)]
  [InlineData(true, "test", true)]
  [InlineData(false, "", false)]
  public async Task BindParameters_ShouldAllowDynamicParam(bool specifyUserParam, string value, bool shouldShowRuntimeParam)
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    var parameterValues = new Dictionary<string, ParameterValue>();

    if (specifyUserParam)
    {
      parameterValues.Add("user", new ParameterValue(JsonSerializer.SerializeToElement(value)));
    }

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      @"
[cmdletbinding()]
param(
    [Parameter()]
    [string]$user
)
dynamicparam {
    if ($user -eq 'test') {
        New-RuntimeDefinedParameterCollection @(
            New-RuntimeDefinedParameter -Name ""Runtime"" -Type ""String""
        )
    }
}
process {

}
",
      DatabaseType.Local,
      CancellationToken.None, parameterValues);


    // assert
    Assert.False(bindResult.HasErrors);

    var runtimeParam = bindResult.ShowCommandInfo.ParameterSets[0].Parameters.ToList().Find(a => a.Name == "Runtime");
    if (shouldShowRuntimeParam)
    {
      Assert.NotNull(runtimeParam);
    }
    else
    {
      Assert.Null(runtimeParam);
    }
  }

  [Fact]
  public async Task BindParameters_ShouldAllowNestedDynamicParameters()
  {
    // arrange

    var ps = @"
[cmdletbinding()]
param(
    [Parameter(Mandatory = $true)]
    $Look,

    # required to allow nested dynamic parameters without throwing an error
    [Parameter(ValueFromRemainingArguments = $true, DontShow)]
    $Rest
)

dynamicparam
{
    if ($Look -eq ""Look"")
    {
        $attr = New-RuntimeDefinedParameter -Name Nested -ValidValues 'Nested','SomeOtherValue' -Mandatory -Type ""string""

        if ($Nested -eq ""Nested"")
        {
            $attr2 = New-RuntimeDefinedParameter -Name Dynamic -Mandatory -Type ""string""

            if ($Dynamic -eq ""Dynamic"")
            {
                $attr3 = New-RuntimeDefinedParameter -Name Parameters -Mandatory -Type ""string""
            }
        }

    }
    return New-RuntimeDefinedParameterCollection -Parameters @($attr, $attr2, $attr3)
}

process
{
    return $false
}
";

    var (dynamicFormService, runContext) = GetDynamicFormService();
    var parameterValues = new Dictionary<string, ParameterValue>
    {
      { "Look", new ParameterValue(JsonSerializer.SerializeToElement("Look")) },
      { "Nested", new ParameterValue(JsonSerializer.SerializeToElement("Nested")) },
      { "Dynamic", new ParameterValue(JsonSerializer.SerializeToElement("Dynamic")) },
      { "Parameters", new ParameterValue(JsonSerializer.SerializeToElement("Parameters")) }
    };

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false, runContext, ps, DatabaseType.Local, CancellationToken.None, parameterValues);

    // assert
    Assert.False(bindResult.HasErrors);

    var lookParameter = bindResult.ConvertedParameters.FirstOrDefault(a => a.Key == "Look");
    Assert.Equal("Look", lookParameter.Value);

    var nestedParameter = bindResult.ConvertedParameters.FirstOrDefault(a => a.Key == "Nested");
    Assert.Equal("Nested", nestedParameter.Value);

    var dynamicParameter = bindResult.ConvertedParameters.FirstOrDefault(a => a.Key == "Dynamic");
    Assert.Equal("Dynamic", dynamicParameter.Value);

    var parametersParameter = bindResult.ConvertedParameters.FirstOrDefault(a => a.Key == "Parameters");
    Assert.Equal("Parameters", parametersParameter.Value);

    Assert.Contains(bindResult.ShowCommandInfo.ParameterSets.SelectMany(a => a.Parameters), a => a.Name == "Look");
    Assert.Contains(bindResult.ShowCommandInfo.ParameterSets.SelectMany(a => a.Parameters), a => a.Name == "Nested");
    Assert.Contains(bindResult.ShowCommandInfo.ParameterSets.SelectMany(a => a.Parameters), a => a.Name == "Dynamic");
    Assert.Contains(bindResult.ShowCommandInfo.ParameterSets.SelectMany(a => a.Parameters),
      a => a.Name == "Parameters");
  }

  [Fact]
  public async Task BindParameters_ShouldAllowValidateSet()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, "param([ValidateSet('cat','dog')][string]$PetType)", DatabaseType.Local, CancellationToken.None);

    var parameter = bindResult.ShowCommandInfo.ParameterSets[0].Parameters[0];

    // assert
    Assert.NotNull(parameter);
    Assert.Contains("cat", parameter.ValidParamSetValues);
    Assert.Contains("dog", parameter.ValidParamSetValues);
  }

  [Fact]
  public async Task BindParameters_ShouldAllowMandatory()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, "param([Parameter(Mandatory)][bool]$MyBool)", DatabaseType.Local, CancellationToken.None);

    var parameter = bindResult.ShowCommandInfo.ParameterSets[0].Parameters[0];

    // assert
    Assert.True(parameter.IsMandatory);
  }
  [Fact]
  public async Task WriteErrorInDynamicParamBlock_ShouldNotCauseIndexOutOfRangeException()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: @"
[cmdletbinding()]
param()
dynamicparam {
    Write-Error 'Some Error'
}
process {

}
", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.Contains("Some Error", metaScriptResult.ConsoleText);
    Assert.False(metaScriptResult.HadTerminatingException);
    Assert.NotNull(metaScriptResult.ErrorCollection);
    Assert.True(metaScriptResult.ErrorCollection.Count == 1);
    var errorRecord = metaScriptResult.ErrorCollection[0];
    Assert.Equal("Some Error", errorRecord.Exception.Message);
  }

  [Fact]
  public async Task BindParameters_ShouldAllowDefaultValue()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var defaultValue = "My Default String Value";
    var bindResult = await dynamicFormService.BindParameters(false, false,runContext, $"param([string]$MyParam='{defaultValue}')", DatabaseType.Local, CancellationToken.None);

    var parameter = bindResult.ShowCommandInfo.ParameterSets[0].Parameters[0];

    // assert
    Assert.Equal(defaultValue, parameter.DefaultValue);
  }

  [Fact]
  public async Task BindParameters_ShouldAllowMediaAttribute()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();
    var media = CreateMedia(null, 1);
    var mediaIdentifier = new MediaIdentifier(media.Id, media.DatabaseType);
    // act
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, $"param([Media(DefaultMediaId = {media.Id}, DefaultMediaType = {Convert.ToInt32(media.DatabaseType)})]$MediaParam)", DatabaseType.Local, CancellationToken.None);

    var parameter = bindResult.ShowCommandInfo.ParameterSets[0].Parameters[0];

    // assert
    Assert.Equal("Media", parameter.ParameterType.Name);
    Assert.Equal(mediaIdentifier, parameter.DefaultValue);
  }

  [Fact]
  public async Task BindParameters_ShouldAllowStringType()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var parameterValues = new Dictionary<string, ParameterValue>() { { "MyParam", Helpers.CreateParameterValue("ParamValue") } };
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, "param([string]$MyParam)", DatabaseType.Local, CancellationToken.None, parameterValues);

    // assert
    Assert.False(bindResult.HasErrors);
  }

  [Fact]
  public async Task BindParameters_ShouldAllowBooleanType()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();


    // act
    var parameterValues = new Dictionary<string, ParameterValue>() { { "TrueParam", Helpers.CreateParameterValue(true) }, { "FalseParam", Helpers.CreateParameterValue(false) } };
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, "param([boolean]$TrueParam, [boolean]$FalseParam)", DatabaseType.Local, CancellationToken.None, parameterValues);
    // assert
    Assert.False(bindResult.HasErrors);
  }

  [Fact]
  public async Task BindParameters_ShouldAllowSwitchType()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();


    // act
    var parameterValues = new Dictionary<string, ParameterValue>() { { "TrueParam", Helpers.CreateParameterValue(true) }, { "FalseParam", Helpers.CreateParameterValue(false) } };
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, "param([switch]$TrueParam, [switch]$FalseParam)", DatabaseType.Local, CancellationToken.None, parameterValues);

    // assert
    Assert.False(bindResult.HasErrors);
  }

  [Fact]
  public async Task BindParameters_ShouldAllowUriType()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    var url = "https://foo.bar";

    // act
    var parameterValues = new Dictionary<string, ParameterValue>() { { "UriParam", Helpers.CreateParameterValue(url) } };
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, "param([uri]$UriParam)", DatabaseType.Local, CancellationToken.None, parameterValues);

    // assert
    Assert.False(bindResult.HasErrors);
    Assert.IsType<Uri>(bindResult.ConvertedParameters.Values.First());
  }

  [Fact]
  public async Task BindParameters_ShouldAllowInt32Type()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();


    // act
    var parameterValues = new Dictionary<string, ParameterValue>() { { "MyParam", Helpers.CreateParameterValue(12345) } };
    var bindResult = await dynamicFormService.BindParameters(false, false, runContext, "param([int]$MyParam)", DatabaseType.Local, CancellationToken.None, parameterValues);

    // assert
    Assert.False(bindResult.HasErrors);
  }

  [Fact]
  public async Task BindParameters_ShouldAllowNewMediaParameterCmdlet()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      @"
[cmdletbinding()]
param()
dynamicparam {
    New-RuntimeDefinedParameterCollection @(
        New-MediaParameter -Name ""Media""
    )
}
process {

}
",
      DatabaseType.Local,
      CancellationToken.None);


    // assert
    Assert.False(bindResult.HasErrors);

    var mediaParameter = bindResult.ShowCommandInfo.ParameterSets[0].Parameters.ToList().Find(a => a.Name == "Media");
    Assert.NotNull(mediaParameter);
  }

  [Fact]
  public async Task BindParameters_ShouldAllowNewPasswordParameterCmdlet()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      @"
[cmdletbinding()]
param()
dynamicparam {
    New-RuntimeDefinedParameterCollection @(
        New-PasswordParameter -Name ""Password""
    )
}
process {

}
",
      DatabaseType.Local,
      CancellationToken.None);


    // assert
    Assert.False(bindResult.HasErrors);

    var passwordParameter =
      bindResult.ShowCommandInfo.ParameterSets[0].Parameters.ToList().Find(a => a.Name == "Password");
    Assert.NotNull(passwordParameter);
  }

  [Theory]
  // script block returning array of primitive values
  [InlineData("-ScriptBlock { return @(1,2,3) } -DefaultValue 2", true)]
  // script block returning an array of hashtables specifying id and label property names
  [InlineData("-ScriptBlock { return @(@{ id = 1; label = \"B\" }, @{ id = 2; label = \"A\" }) } -DefaultValue @{ id = 2; label = \"A\" } -IdPropertyName \"Id\" -LabelPropertyName \"label\"", true)]
  //
  [InlineData("-ScriptBlock { return @((new-object PSObject -property @{ id = 1; label = \"B\" }), (new-object PSObject -property @{ id = 2; label = \"A\" })) }-DefaultValue (new-object PSObject -property @{ id = 2; label = \"A\" }) -IdPropertyName \"Id\" -LabelPropertyName \"label\"", true)]
  [InlineData("-ScriptBlock { return @(1,2,3) }", false)]
  [InlineData("-ValidValues @{ A = 1; B = 2 } -DefaultValue 2", true)]
  [InlineData("-ValidValues @{ A = @{ id = 1 }; B = @{ id = 2  } } -DefaultValue @{ id = 2  } -IdPropertyName \"Id\"", true)]
  [InlineData("-ValidValues @(@{ id = 1; label = \"B\" }, @{ id = 2; label = \"A\" }) -DefaultValue @{ id = 2; label = \"A\" } -IdPropertyName \"Id\" -LabelPropertyName \"label\"", true)]
  [InlineData("-ValidValues @((new-object PSObject -property @{ id = 1; label = \"B\" }), (new-object PSObject -property @{ id = 2; label = \"A\" })) -DefaultValue (new-object PSObject -property @{ id = 2; label = \"A\" }) -IdPropertyName \"Id\" -LabelPropertyName \"label\"", true)]
  public async Task BindParameters_ShouldAllowNewDropdownParameterCmdlet(string definition, bool hasDefaultValue)
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      $@"
[cmdletbinding()]
param()
dynamicparam {{
    New-RuntimeDefinedParameterCollection @(
        New-DropdownParameter -Name ""Dropdown"" {definition}
    )
}}
process {{

}}
",
      DatabaseType.Local,
      CancellationToken.None);


    // assert
    Assert.False(bindResult.HasErrors);

    var dropdownParameter =
      bindResult.ShowCommandInfo.ParameterSets[0].Parameters.ToList().Find(a => a.Name == "Dropdown");
    Assert.NotNull(dropdownParameter);
    Assert.NotEmpty(dropdownParameter.ValidDropdownValues);

    if (hasDefaultValue)
    {
      Assert.NotNull(dropdownParameter.DefaultValue);
    }
  }

  [Fact]
  public async Task BindParameters_ShouldAllowNewHelpTextParameterCmdlet()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();
    var helpText = "some help text";

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      @$"
[cmdletbinding()]
param()
dynamicparam {{
    New-ParameterCollection @(
        New-HelpText -Name SomeName ""{helpText}""
    )
}}
process {{

}}
",
      DatabaseType.Local,
      CancellationToken.None);


    // assert
    Assert.False(bindResult.HasErrors);

    var parameter = bindResult.ShowCommandInfo.ParameterSets[0].Parameters.ToList().Find(a => a.OnlyForHelpText);
    Assert.NotNull(parameter);
    Assert.Equal(helpText, parameter.HelpText);
  }

  [Theory]
  [InlineData("New-TextParameter", "String")]
  [InlineData("New-NumberParameter", "Int32")]
  [InlineData("New-CheckboxParameter", "SwitchParameter")]
  [InlineData("New-BooleanParameter", "Boolean")]
  [InlineData("New-KeyValueParameter", "Hashtable")]
  [InlineData("New-UriParameter", "Uri")]
  [InlineData("New-MediaParameter", "Media")]
  [InlineData("New-PasswordParameter", "Password")]
  public async Task BindParameters_ShouldAllowAllParameterTypesAsCmdlets(string cmdlet, string parameterTypeName)
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      @$"
[cmdletbinding()]
param()
dynamicparam {{
    New-ParameterCollection @(
        {cmdlet} -Name TestParam
    )
}}
process {{

}}
",
      DatabaseType.Local,
      CancellationToken.None);


    // assert
    Assert.False(bindResult.HasErrors);

    var parameter = bindResult.ShowCommandInfo.ParameterSets[0].Parameters[0];
    Assert.NotNull(parameter);
    Assert.Equal(parameterTypeName, parameter.ParameterType.Name);
  }

  [Fact]
  public async Task BindParameters_ShouldAllowNewMediaParameterDefaultValue()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();
    var media = CreateMedia();
    var mediaIdentifier = new MediaIdentifier(media.Id, media.DatabaseType);
    var parameterName = "MediaParam";
    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      @$"
[cmdletbinding()]
param()
dynamicparam {{
    New-ParameterCollection @(
        New-MediaParameter -Name {parameterName} -DefaultMediaId {mediaIdentifier.Id} -DefaultMediaType {Convert.ToInt32(mediaIdentifier.DatabaseType)}
    )
}}
process {{

}}
",
      DatabaseType.Local,
      CancellationToken.None);


    // assert
    var parameter = bindResult.ShowCommandInfo.ParameterSets[0].Parameters[0];
    Assert.NotNull(parameter);
    Assert.Equal("Media", parameter.ParameterType.Name);
    Assert.Equal(parameterName, parameter.Name);
    Assert.Equal(mediaIdentifier, parameter.DefaultValue);
  }
  [Theory]
  [InlineData("cat", "cat")]
  [InlineData("dog", "Your pet sucks")]
  public async Task ThrowStringInValidateScript_ShouldHaveParameterNameInErrorListForFrontend(string input, string expectedOutput)
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext, """
[CmdletBinding()]
param()
dynamicparam{
  New-Parameter -Name PetType -ValidateScript { if ($_ -ne 'cat') { throw 'Your pet sucks' } $true} -Mandatory
}
process{
  $PetType
}
""",
      DatabaseType.Local,
      CancellationToken.None,
      specifiedParameters: new()
      {
        { "PetType", Helpers.CreateParameterValue(input) }
      });

    // assert
    if (input != "cat")
    {
      Assert.True(bindResult.HasErrors);
      Assert.True(bindResult.BindErrors.Count == 1);
      var bindError = bindResult.BindErrors.First();
      Assert.True(bindError.Key == "PetType");
      Assert.Equal(expectedOutput, bindError.Value);
    }
    else
    {
      Assert.False(bindResult.HasErrors);
      var actual = bindResult.ConvertedParameters["PetType"];
      Assert.Equal(expectedOutput, actual);
    }
  }

  [Fact]
  public async Task BindParameters_ShouldExcludeNullValues()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var parameters = new Dictionary<string, ParameterValue>
    {
      { "VolumesToEncrypt", new ParameterValue(null) }
    };
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      script: @"
param(
[Parameter(Position=0,Mandatory=$False,HelpMessage=@'
Turning this on will cause all fixed disks on the system to become encrypted
'@)]
[ValidateSet('OS Only','All Non-Removable')]
[String]$VolumesToEncrypt='OS Only')",
      DatabaseType.Local,
      CancellationToken.None,
      specifiedParameters: parameters);


    Assert.False(bindResult.HasErrors);
  }

  [Fact]
  public async Task BindParameters_ShouldConvertDictionaryValuesToHashtable()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var val = new Dictionary<object, object>() { { "foo", "bar" } };
    var parameters = new Dictionary<string, ParameterValue>
    {
      { "KeyVal", new ParameterValue(JsonSerializer.SerializeToElement(val)) }
    };
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      script: @"
param([Hashtable]$KeyVal)",
      DatabaseType.Local,
      CancellationToken.None,
      specifiedParameters: parameters);


    Assert.False(bindResult.HasErrors);
    Assert.IsType<Hashtable>(bindResult.ConvertedParameters.FirstOrDefault().Value);
  }

  [Theory]
  [CombinatorialData]
  public async Task BindParameters_ShouldWorkWithGetCommandParameters(
    bool supplyLaptopFormFactorShortCode,
    bool supplyDesktopFormFactorShortCode)
  {
    // Arrange

    const string testDynamicParametersScriptName = "Test-DynamicParameters";
    const string desktopShortCode = "DT";
    const string laptopShortCode = "LT";
    const string computerNameTemplate = "ABC-$FormFactorShort";

    // add a function script for Get-CommandParameters
    var getCommandParametersScript =
      await File.ReadAllTextAsync("DynamicFormServiceTests/Get-CommandParameters.ps1");
    AddFunctionScript("Get-CommandParameters", getCommandParametersScript);

    // add a function script for Test-DynamicParameters, which is mimicking a true script that needs to run in immy for some software or task
    var testDynamicParametersScript =
      await File.ReadAllTextAsync("DynamicFormServiceTests/Test-DynamicParameters.ps1");
    AddFunctionScript(testDynamicParametersScriptName, testDynamicParametersScript);

    // this is the script we will run to grab the parameters from the above test script
    var callGetCommandParametersScript =
      await File.ReadAllTextAsync("DynamicFormServiceTests/Call-GetCommandParameters.ps1");

    var (dynamicFormService, runContext) = GetDynamicFormService();

    // Act

    var parameters = new Dictionary<string, ParameterValue>
    {
      {
        "ScriptName",
        new ParameterValue(JsonSerializer.SerializeToElement(testDynamicParametersScriptName))
      },
      {
        "ComputerNameTemplate",
        new ParameterValue(JsonSerializer.SerializeToElement(computerNameTemplate))
      },
    };

    if (supplyDesktopFormFactorShortCode)
    {
      parameters.Add("DesktopFormFactorShortCode",
        new ParameterValue(JsonSerializer.SerializeToElement(desktopShortCode)));
    }

    if (supplyLaptopFormFactorShortCode)
    {
      parameters.Add("LaptopFormFactorShortCode",
        new ParameterValue(JsonSerializer.SerializeToElement(laptopShortCode)));
    }

    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      script: callGetCommandParametersScript,
      DatabaseType.Local,
      CancellationToken.None,
      specifiedParameters: parameters);


    // Assert

    // both short codes are mandatory so if one is missing then we should have an error
    if (!supplyDesktopFormFactorShortCode || !supplyLaptopFormFactorShortCode)
    {
      Assert.True(bindResult.HasErrors);
    }
    else
    {
      Assert.False(bindResult.HasErrors);

      // assert the converted parameter values
      Assert.Equal(desktopShortCode, bindResult.ConvertedParameters["DesktopFormFactorShortCode"]);
      Assert.Equal(laptopShortCode, bindResult.ConvertedParameters["LaptopFormFactorShortCode"]);
    }

    Assert.Equal(computerNameTemplate, bindResult.ConvertedParameters["ComputerNameTemplate"]);
    Assert.Equal(testDynamicParametersScriptName, bindResult.ConvertedParameters["ScriptName"]);
  }

  /// <summary>
  /// This test ensures, if a runContext is passed to BindParameters, then a
  /// variable for $TenantId is available inside the dynamicparam block.
  /// </summary>
  /// <param name="includeRunContext"></param>
  [Theory]
  [CombinatorialData]
  public async Task BindParameters_ShouldIncludeRunContextVariablesInDynamicParam(bool includeRunContext)
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      includeRunContext ? runContext : null,
      @$"
[cmdletbinding()]
param()
dynamicparam {{
    if ($TenantId -ne $null) {{
      New-ParameterCollection @(
          New-TextParameter -Name TestParam
      )
    }}
}}
process {{

}}
",
      DatabaseType.Local,
      CancellationToken.None);


    // assert
    Assert.False(bindResult.HasErrors);

    var parameter = bindResult.ShowCommandInfo.ParameterSets.FirstOrDefault()?.Parameters.FirstOrDefault();
    if (includeRunContext)
    {
      Assert.NotNull(parameter);
    }
    else
    {
      Assert.Null(parameter);
    }
  }
}
