using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Management.Automation;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.ContextTests;

namespace Immybot.Backend.UnitTests.DynamicFormServiceTests;
public class DropdownParameterTests : ActionsTestBase
{
  private (IDynamicFormService, IRunContext) GetDynamicFormService()
  {
    var (dynamicFormService, runContext, _) = Helpers.BuildDynamicFormService(ctx: GetSqliteDbContextFactory(), sftCtx: GetSqliteSoftwareDbContextFactory());
    return (dynamicFormService, runContext);
  }
  [Fact]
  public async Task BindParameters_ShouldAllowDropdownAttributeReturningHashtable()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      @"
param(
  [Dropdown({@{test=1}})]
  $DropdownParam
 )",
      DatabaseType.Local,
      CancellationToken.None);


    // assert

    var dropdownParameter = bindResult.ShowCommandInfo.ParameterSets[0].Parameters[0];

    Assert.False(bindResult.HasErrors);
    Assert.Equal("DropdownParam", dropdownParameter.Name);
    Assert.True(dropdownParameter.HasDropdownParameterSet);
    Assert.Equal("1", dropdownParameter.ValidDropdownValues[0].Value);
    Assert.Equal("test", dropdownParameter.ValidDropdownValues[0].Text);
  }
  [Fact]
  public async Task BindParameters_ShouldAllowDropdownAttribute()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      @"
param(
  [Dropdown({
      $obj = new-object psobject -Property @{
        Id = 1
        Name = ""test""
      }
      @($obj)
    }, ""Id"", ""Name"")]
  $DropdownParam
 )",
      DatabaseType.Local,
      CancellationToken.None);


    // assert

    var dropdownParameter = bindResult.ShowCommandInfo.ParameterSets[0].Parameters[0];

    Assert.False(bindResult.HasErrors);
    Assert.Equal("DropdownParam", dropdownParameter.Name);
    Assert.True(dropdownParameter.HasDropdownParameterSet);
    Assert.Equal("1", dropdownParameter.ValidDropdownValues[0].Value);
    Assert.Equal("test", dropdownParameter.ValidDropdownValues[0].Text);
  }

  [Theory]
  // script block returning array of primitive values
  [InlineData("-ScriptBlock { return @(1,2,3) } -DefaultValue 2", true)]
  // script block returning an array of hashtables specifying id and label property names
  [InlineData("-ScriptBlock { return @(@{ id = 1; label = \"B\" }, @{ id = 2; label = \"A\" }) } -DefaultValue @{ id = 2; label = \"A\" } -IdPropertyName \"Id\" -LabelPropertyName \"label\"", true)]
  //
  [InlineData("-ScriptBlock { return @((new-object PSObject -property @{ id = 1; label = \"B\" }), (new-object PSObject -property @{ id = 2; label = \"A\" })) }-DefaultValue (new-object PSObject -property @{ id = 2; label = \"A\" }) -IdPropertyName \"Id\" -LabelPropertyName \"label\"", true)]
  [InlineData("-ScriptBlock { return @(1,2,3) }", false)]
  [InlineData("-ValidValues @{ A = 1; B = 2 } -DefaultValue 2", true)]
  [InlineData("-ValidValues @{ A = @{ id = 1 }; B = @{ id = 2  } } -DefaultValue @{ id = 2  } -IdPropertyName \"Id\"", true)]
  [InlineData("-ValidValues @(@{ id = 1; label = \"B\" }, @{ id = 2; label = \"A\" }) -DefaultValue @{ id = 2; label = \"A\" } -IdPropertyName \"Id\" -LabelPropertyName \"label\"", true)]
  [InlineData("-ValidValues @((new-object PSObject -property @{ id = 1; label = \"B\" }), (new-object PSObject -property @{ id = 2; label = \"A\" })) -DefaultValue (new-object PSObject -property @{ id = 2; label = \"A\" }) -IdPropertyName \"Id\" -LabelPropertyName \"label\"", true)]
  public async Task BindParameters_ShouldAllowNewDropdownParameterCmdlet(string definition, bool hasDefaultValue)
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      $@"
[cmdletbinding()]
param()
dynamicparam {{
    New-RuntimeDefinedParameterCollection @(
        New-DropdownParameter -Name ""Dropdown"" {definition}
    )
}}
process {{

}}
",
      DatabaseType.Local,
      CancellationToken.None);


    // assert
    Assert.False(bindResult.HasErrors);

    var dropdownParameter =
      bindResult.ShowCommandInfo.ParameterSets[0].Parameters.ToList().Find(a => a.Name == "Dropdown");
    Assert.NotNull(dropdownParameter);
    Assert.NotEmpty(dropdownParameter.ValidDropdownValues);

    if (hasDefaultValue)
    {
      Assert.NotNull(dropdownParameter.DefaultValue);
    }
  }

  [Fact]
  public async Task BindParameters_ShouldAllowMultiSelectDropdown()
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      $@"
[cmdletbinding()]
param()
dynamicparam {{
    New-RuntimeDefinedParameterCollection @(
        New-DropdownParameter -Name ""Dropdown"" -ValidValues @{{ A = 1; B = 2; }} -MultiSelect
    )
}}
process {{

}}
",
      DatabaseType.Local,
      CancellationToken.None);


    // assert
    Assert.False(bindResult.HasErrors);

    var dropdownParameter =
      bindResult.ShowCommandInfo.ParameterSets[0].Parameters.ToList().Find(a => a.Name == "Dropdown");
    Assert.NotNull(dropdownParameter);
    Assert.True(dropdownParameter.ParameterType.IsArray);
  }

  [Theory]
  [InlineData($"@(\"1\", \"2\")", 2)]
  [InlineData($"@(\"1\")", 1)]
  [InlineData($"\"1\"", 1)]
  [InlineData($"1", 1)]
  [InlineData($"@(1, 2)", 2)]
  [InlineData($"", 0)]
  public async Task BindParameters_MultiSelectDropdown_ShouldHaveArrayDefaultValue(string? defaultValue, int count)
  {
    // arrange
    var (dynamicFormService, runContext) = GetDynamicFormService();

    // act
    var defaultValueLine = string.IsNullOrEmpty(defaultValue) ? "" : $"-defaultValue {defaultValue}";
    var bindResult = await dynamicFormService.BindParameters(
      false,
      false,
      runContext,
      $@"
[cmdletbinding()]
param()
dynamicparam {{
    New-RuntimeDefinedParameterCollection @(
        New-DropdownParameter -Name ""Dropdown"" -ValidValues @{{ A = 1; B = 2; }} {defaultValueLine} -MultiSelect
    )
}}
process {{

}}
",
      DatabaseType.Local,
      CancellationToken.None);


    // assert
    Assert.False(bindResult.HasErrors);

    var dropdownParameter =
      bindResult.ShowCommandInfo.ParameterSets[0].Parameters.ToList().Find(a => a.Name == "Dropdown");
    Assert.NotNull(dropdownParameter);

    var df = dropdownParameter.DefaultValue as object[];
    if (count > 0)
    {
      Assert.Equal(count, df?.Count());
    }
    else
    {
      Assert.Null(df);
    }
  }

  [Fact]
  public async Task RunMetascript_ShouldHonorAlreadyTransformedDropdownParameters_ForPsObject()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var functionScript =
      await File.ReadAllTextAsync("DynamicFormServiceTests/Get-PSObjectDropdownParameter.ps1");
    AddFunctionScript("Get-PSObjectDropdownParameter", functionScript);

    var scriptActionToRun =
      await File.ReadAllTextAsync(
        "DynamicFormServiceTests/Binds-PsObjectDropdownParameter.ps1");

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: scriptActionToRun,
        executionContext: ScriptExecutionContext.Metascript,
        parameters: new Dictionary<string, object?>
        {
          { "DropdownParameter", PSObject.AsPSObject(new { Id = 1, Name = "One" }) }
        }),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.Equal(1, metaScriptResult.OutputAsObject);
  }

  [Fact]
  public async Task RunMetascript_ShouldHonorAlreadyTransformedDropdownParameters_ForHashtable()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var functionScript =
      await File.ReadAllTextAsync("DynamicFormServiceTests/Get-HashtableDropdownParameter.ps1");
    AddFunctionScript("Get-HashtableDropdownParameter", functionScript);

    var scriptActionToRun =
      await File.ReadAllTextAsync(
        "DynamicFormServiceTests/Binds-HashtableDropdownParameter.ps1");

    // act
    var ht = new Hashtable { { "Id", 1 }, { "Name", "One" } };
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false, Helpers.MakeScript(action: scriptActionToRun,
        executionContext: ScriptExecutionContext.Metascript,
        parameters: new Dictionary<string, object?>
        {
          { "DropdownParameter", ht }
        }),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.Equal(1, metaScriptResult.OutputAsObject);
  }
}
