using System;
using System.IO;
using System.IO.Pipes;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Infrastructure;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Language;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Infrastructure.Telemetry.Configuration;
using Immybot.Backend.Persistence;
using Immybot.Backend.UnitTests.ContextTests;
using Immybot.Backend.Web.Common.Startup;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using OmniSharp.Extensions.LanguageServer.Protocol;
using OmniSharp.Extensions.LanguageServer.Protocol.Models;
using StreamJsonRpc;

namespace Immybot.Backend.UnitTests.MetascriptTests;

public class EditorServiceTests : ActionsTestBase
{
  async Task RunEditorServices(Func<JsonRpc, Task> func)
  {
    var builder = Host.CreateApplicationBuilder();

    builder
      .ConfigureImmybotOptions()
      .AddTelemetryServices()
      ;

    builder.Services.AddEditorServicesRequiredServices();

    builder.Services.AddSingleton<Func<ImmybotDbContext>>(_ => GetSqliteDbContextFactory());
    builder.Services.AddTransient(sp => sp.GetRequiredService<Func<ImmybotDbContext>>()());

    builder.Services.AddSingleton<Func<SoftwareDbContext>>(_ => GetSqliteSoftwareDbContextFactory());
    builder.Services.AddTransient(sp => sp.GetRequiredService<Func<SoftwareDbContext>>()());

    // must dispose this to clean up things like ActivityListener that can affect other tests
    using var host = builder.Build();

    await host.StartAsync();
    await using var _ = Disposable.CreateAsync(async () => await host.StopAsync());

    var languageService = (LanguageService)host.Services.GetRequiredService<ILanguageService>();
    var cancellationManager = host.Services.GetRequiredService<IImmyCancellationManager>();

    var terminalId = Guid.NewGuid();
    var languageServerPipeName = $"PSES_test_{terminalId}";
    var sessionInfoFilePath = Path.GetTempFileName();
    using var cancellationTokenSource = new CancellationTokenSource();
    var cancellationToken = cancellationTokenSource.Token;
    var cancellationId = await languageService.StartEditorServices(
      languageServerPipeName,
      sessionInfoFilePath,
      terminalId,
      null,
      null,
      ScriptCategory.Function,
      ScriptExecutionContext.Metascript,
      cancellationToken
    );
    Assert.NotNull(cancellationId);
    using var __ = Disposable.Create(() =>
    {
      Assert.True(cancellationManager.CancelScript(cancellationId.Value));
      cancellationTokenSource.Cancel();
    });

    var psesPipeName = Path.GetFileName(languageServerPipeName);
    await using var namedPipeStream =
      new NamedPipeClientStream(".", psesPipeName, PipeDirection.InOut, PipeOptions.Asynchronous);
    await namedPipeStream.ConnectAsync(cancellationToken);

    using var editorServicesRpc = new JsonRpc(namedPipeStream);

    Action<LogMessageParams> logMessageHandler = message => Console.WriteLine($"{message.Type}: {message.Message}");
    editorServicesRpc.AddLocalRpcMethod(logMessageHandler.GetMethodInfo(),
      logMessageHandler.Target,
      new JsonRpcMethodAttribute("window/logMessage") { UseSingleObjectParameterDeserialization = true });

    editorServicesRpc.StartListening();

    var initializeResponse = await editorServicesRpc.InvokeWithParameterObjectAsync<object>(GeneralNames.Initialize,
      new
      {
        capabilities = new { },
        initializationOptions = new
        {
          enableProfileLoading = false, analyzeOpenDocumentsOnly = false, scriptAnalysis = new { enable = true }
        }
      },
      cancellationToken);
    Assert.NotNull(initializeResponse);

    await editorServicesRpc.NotifyWithParameterObjectAsync(GeneralNames.Initialized);

    await using var ___ = Disposable.CreateAsync(async () =>
    {
      await editorServicesRpc.InvokeWithParameterObjectAsync(GeneralNames.Shutdown, null, cancellationToken);
      await editorServicesRpc.NotifyWithParameterObjectAsync(GeneralNames.Exit);
    });

    await func(editorServicesRpc);
  }

  [Fact]
  public async Task BasicStartStopEditorServices()
  {
    await RunEditorServices(async editorService => { });
  }

  [Fact]
  public async Task HoverOverImportModule()
  {
    await RunEditorServices(async editorService =>
    {
      var fileName = Guid.NewGuid().ToString();
      await editorService.NotifyWithParameterObjectAsync(TextDocumentNames.DidOpen,
        new
        {
          textDocument = new
          {
            uri = $"inmemory://{fileName}.ps1", languageId = "powershell", version = 1, text = "Import-Module asdf"
          }
        });

      var hoverResponse = await editorService.InvokeWithParameterObjectAsync<object>(TextDocumentNames.Hover,
        new { textDocument = new { uri = $"inmemory://{fileName}.ps1" }, position = new { line = 0, character = 0 } });

      Assert.NotNull(hoverResponse);
    });
  }

  // TODO performance profiling on PSES process -- takes ~7s to start up
}
