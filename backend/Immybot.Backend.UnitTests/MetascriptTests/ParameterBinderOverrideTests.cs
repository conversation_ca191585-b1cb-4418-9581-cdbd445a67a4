using System.Threading.Tasks;
using System;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.ContextTests;

namespace Immybot.Backend.UnitTests.MetascriptTests;
public class ParameterBinderOverrideTests : ActionsTestBase
{
  // Tests for the ParameterBinderOverride class
  [Theory]
  [InlineData("Test-DynamicParameters2 -", "Product")]
  [InlineData("Test-DynamicParameters2 -Product ", "Beta")]
  [InlineData("Test-DynamicParameters2 -Product Beta -", "Platform")]
  [InlineData("Test-DynamicParameters2 -Product Beta -Platform ", "Windows")]
  public async Task IncrementalParameterBinding_ShouldWorkWithTabExpansion(string input, string output)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    string functionScript = @$"
$ErrorActionPreference = 'Stop'
Function Test-DynamicParameters2
{{
  [CmdletBinding()]
  param()
  dynamicparam{{
    New-Parameter -Name Product -ValidValues 'Beta','Stable','Dev','Canary' -Type ([string]) -Mandatory -Position 0
    if($Product){{
      New-Parameter -Name Platform -ValidValues 'Windows','Linux','MacOS','Android' -Type ([string]) -Mandatory -Position 1
      if($Platform){{
          New-Parameter -Name Architecture -ValidValues 'x86','x64','arm64','universal' -Type ([string]) -Mandatory
          if($Architecture){{
              New-Parameter -Name Version -ValidValues '1.0.0','1.0.1','1.0.2','1.0.3' -Type ([string]) -Mandatory
          }}
      }}
    }}
  }}
}}
$inputScript = ""{input}""
$Matches2 = TabExpansion2 -inputScript $inputScript
$Matches2.CompletionMatches.ListItemText | ?{{ $_ -is [string]}} | ?{{$_.Trim() -notin 'Verbose', 'Debug', 'ErrorAction', 'WarningAction', 'InformationAction', 'ErrorVariable', 'WarningVariable', 'InformationVariable', 'OutVariable', 'OutBuffer', 'PipelineVariable','ProgressAction'}}
";
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      canAccessMspResources: false,
      canAccessParentTenant: false,
      Helpers.MakeScript(action: functionScript, executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.Contains(output, metaScriptResult.OutputAsCollection);
  }
  [Theory]
  [InlineData("cat", "cat")]
  [InlineData("dog", ": Cannot validate argument on parameter 'PetType'. Your pet sucks")]
  public async Task ThrowStringInValidateScript_ShouldShowInConsole(string input, string expectedOutput)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    string functionScript = """
[CmdletBinding()]
param()
dynamicparam{
  New-Parameter -Name PetType -ValidateScript { if ($_ -ne 'cat') { throw 'Your pet sucks' } $true} -Mandatory
}
process{
  $PetType
}
""";
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(true, false,
      Helpers.MakeScript(action: functionScript, executionContext: ScriptExecutionContext.Metascript, parameters: new()
      {
        { "PetType", input }
      }),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer
      );

    // assert
    Assert.Contains(expectedOutput, metaScriptResult.ConsoleText);
  }
}
