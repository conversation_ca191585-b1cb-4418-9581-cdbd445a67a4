using System.Management.Automation;
using System.Management.Automation.Remoting.Internal;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security;
using System.Text.Json;
using System.Text.RegularExpressions;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Application.Lib.MetaScripts.Modules;
using Immybot.Backend.Application.Lib.Scripts;
using Immybot.Backend.Application.Lib.Scripts.EphemeralAgent;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Backend.UnitTests.ContextTests;
using Immybot.Shared.Abstractions.Device.Exceptions;
using Microsoft.Extensions.Options;
using Microsoft.PowerShell.Commands;
using Moq;
using OtpNet;
using Polly;
using StreamJsonRpc;
using StreamJsonRpc.Protocol;
using Immybot.Shared.Primitives;

namespace Immybot.Backend.UnitTests.MetascriptTests;

public class MetascriptInvokerTests : ActionsTestBase
{
  public record FilteredPsComputersTestPayload(
    string Action,
    bool SetLimitToTenantId,
    bool SetIncludeChildTenants,
    bool SetAllowAccessToParentTenant,
    bool SetComputerId,
    TargetType ExpectedTargetType,
    string? ExpectedError = null,
    string? ExpectedTerminatingException = null,
    DatabaseType ScriptType = DatabaseType.Local);
  private async Task<MetaScriptResult<T>> RunScript<T>(string script)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<T>(
      false,
      false,
      Helpers.MakeScript(action: script, executionContext: ScriptExecutionContext.Metascript, outputType: ScriptOutputType.Table),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);
    return metaScriptResult;
  }
  private async Task<string?> RunScript(string script)
  {
    return (await RunScript<object>(script)).ConsoleText?.Trim();
  }
  protected void CreateLocalModuleScript(string moduleName = "MyModule", string moduleFunctionName = "Get-ModuleFunc", string moduleFunctionDefinition = "param([string]$MyParam) \r\n$MyParam")
  {
    CreateLocalScript(moduleName, $"Function {moduleFunctionName}\r\n{{\r\n\t{moduleFunctionDefinition}\r\n}}\r\nExport-ModuleMember -Function '{moduleFunctionName}'", category: ScriptCategory.Module);
  }
  private static string StripColorCodes(string input)
  {
    // useful when dealing with console output from Get-ImmyComputer
    // this method will remove any color codes from the string
    return Regex.Replace(input, @"\u001b\[[0-9;]*m|\u001b", string.Empty);
  }

  [Fact]
  public void GetImmyComputerFilterScript_ShouldHaveRequiredAttributes()
  {
    // arrange
    var attr = typeof(GetImmyComputerFilterScriptCommand).GetCustomAttribute<CmdletAttribute>();

    // assert
    Assert.NotNull(attr);
    Assert.False(string.IsNullOrEmpty(attr.NounName));
    Assert.False(string.IsNullOrEmpty(attr.VerbName));
  }

  [Fact]
  public async Task RunMetascript_WriteError_ShouldReturnNonTerminatingError()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: "write-error \"error\"", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.False(metaScriptResult.HadTerminatingException);
    Assert.Null(metaScriptResult.TerminatingErrorRecord);
    Assert.IsType<WriteErrorException>(metaScriptResult.ErrorCollection[0].Exception);
    Assert.Equal("Write-Error: error\n", metaScriptResult.ConsoleText);
  }

  [Fact]
  public async Task RunMetascript_WriteError_WithCategory_OperationStopped_ShouldReturnNonTerminatingError()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: "write-error \"error\" -Category OperationStopped", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.False(metaScriptResult.HadTerminatingException);
    Assert.Null(metaScriptResult.TerminatingErrorRecord);
    Assert.Equal(ErrorCategory.OperationStopped, metaScriptResult.ErrorCollection[0].CategoryInfo.Category);
  }


  [Fact]
  public async Task RunMetascript_ThrowError_ShouldReturnTerminatingError()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: "throw \"error\"", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.True(metaScriptResult.HadTerminatingException);
    Assert.NotNull(metaScriptResult.TerminatingErrorRecord);
    Assert.Equal("Exception: error\n", metaScriptResult.ConsoleText);
  }

  [Fact]
  public async Task RunMetascript_UsingVariableShouldBeReplaced()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var agent = new Mock<IEphemeralAgentSession>();
    agent.SetupGet(a => a.IsConnected).Returns(true);
    Helpers.MockIEphemeralAgentAcquisition(mocks, agent.Object);
    var runContext = mocks.BuildRunContext();
    var args = mocks.MockRunContextArgs(computer: Helpers.MakeComputer());

    mocks.IScriptInvoker!
      .SetupRunScriptAsync()
      .ReturnsAsync(new Application.Lib.Scripts.SerializedPowerShellScriptResult());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<string>(
      false,
      false,
      Helpers.MakeScript(action: "$MyVar = 'poop';Invoke-ImmyCommand { write-output \"$using:Myvar\" }", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      againstComputer: args.Computer);

    // assert
    mocks.IScriptInvoker!.Verify(o => o.RunScriptAsync(
      It.IsAny<IEphemeralAgentSession>(),
      It.Is<Script>(a => a.Action.Contains("$__using_")),
      It.IsAny<int>(),
      It.IsAny<CancellationToken>(),
      It.IsAny<PSTaskDataStreamWriter>()));
    Assert.False(metaScriptResult.HadTerminatingException);
    Assert.Null(metaScriptResult.TerminatingErrorRecord);
  }

  [Theory]
  [CombinatorialData]
  public async Task RunMetascript_InvokeImmyCommand_WriteError_ShouldShowInConsoleText(bool systemScript)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var agent = new Mock<IEphemeralAgentSession>();
    agent.SetupGet(a => a.IsConnected).Returns(true);
    Helpers.MockIEphemeralAgentAcquisition(mocks, agent.Object);
    var runContext = mocks.BuildRunContext();
    var args = mocks.MockRunContextArgs(computer: Helpers.MakeComputer());

    var res = new Application.Lib.Scripts.SerializedPowerShellScriptResult();
    mocks.IScriptInvoker!
      .SetupRunScriptAsync()
      .Callback((
        IEphemeralAgentSession _,
        Script _,
        int _,
        CancellationToken _,
        PSTaskDataStreamWriter? writer) =>
      {
        var ex = new RemoteException("test");
        var errRecord = new ErrorRecord(ex, "Test", ErrorCategory.WriteError, null);
        writer?.Add(new PSStreamObject(PSStreamObjectType.Error, errRecord));
        res.HadErrors = true;
      })
      .ReturnsAsync(res);

    // act
    var action = systemScript ? "mocked to return writ-error" : "Invoke-ImmyCommand { 'mocked to return writ-error' }";
    var metaScriptResult = await metascriptInvoker.RunMetascript<string>(
      false,
      false,
      Helpers.MakeScript(
        action: action,
        executionContext: systemScript ? ScriptExecutionContext.System : ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      againstComputer: args.Computer);

    // assert
    Assert.Contains("WriteError: test", metaScriptResult.ConsoleText);
    Assert.True(res.HadErrors);
  }

  [Fact]
  public async Task RunMetascript_WithDynamicParam_ShouldHandleMultipleCallsToWriteHostAndWriteError()
  {
    if (OperatingSystem.IsMacOS())
    {
      return;
    }
    // arrange
    var dbFactory = GetSqliteDbContextFactory();
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: dbFactory,
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    const string script = """
                          [CmdletBinding()]
                          param()
                          dynamicparam{
                              Write-Host "1"
                              Write-Error "2"
                              Write-Host "3"
                              Write-Error "4"
                              Write-Host "5"
                              Write-Error "6"
                          }
                          process{}
                          """;

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: script, executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    string expectedConsoleText = """
                                 1
                                 Write-Error:
                                 Line |
                                    5 |      Write-Error "2"
                                      |      ~~~~~~~~~~~~~~~
                                      | 2
                                 3
                                 Write-Error:
                                 Line |
                                    7 |      Write-Error "4"
                                      |      ~~~~~~~~~~~~~~~
                                      | 4
                                 5
                                 Write-Error:
                                 Line |
                                    9 |      Write-Error "6"
                                      |      ~~~~~~~~~~~~~~~
                                      | 6
                                 """
    // remove spaces and newlines for easier comparison
    .Replace(" ", "").Replace("\n", "");

    var consoleTextCleaned = metaScriptResult.ConsoleText?.Replace(" ", "").Replace("\n", "");
    Assert.Equal(expectedConsoleText, consoleTextCleaned);
  }

  /// <summary>
  /// This test is to ensure that the metascript invoker can run a script that has a function definition with a cmdletbinding without having any errors.
  /// The original error that was thrown was "Cannot retrieve the dynamic parameters for the cmdlet. IncrementalBinding only supported for scripts with [CmdletBinding()]"
  /// </summary>
  [Fact]
  public async Task RunMetascript_RunningAFunctionScript_UsingCmdletBinding_ShouldNotError()
  {
    // arrange
    var dbFactory = GetSqliteDbContextFactory();
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: dbFactory,
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    await using var localDb = dbFactory();

    var scriptName = "Run-TestFunctionScript";

    var fnScript = new Script()
    {
      Name = scriptName,
      Action = """
               [CmdletBinding()]
               param()
               dynamicparam { }
               process { }
               """,
      ScriptExecutionContext = ScriptExecutionContext.Metascript,
      ScriptCategory = ScriptCategory.Function,
    };
    await localDb.Scripts.AddAsync(fnScript);
    await localDb.SaveChangesAsync();

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: scriptName, executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.False(metaScriptResult.HadTerminatingException);
    Assert.Empty(metaScriptResult.ErrorCollection);
    Assert.Null(metaScriptResult.TerminatingErrorRecord);
  }

  [Fact]
  public async Task RunMetascript_WriteError_ErrorActionStop_ShouldReturnTerminatingError()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: "write-error \"error\" -ErrorAction Stop", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.True(metaScriptResult.HadTerminatingException);
    Assert.NotNull(metaScriptResult.TerminatingErrorRecord);
    Assert.IsType<WriteErrorException>(metaScriptResult.TerminatingErrorRecord.Exception);
    Assert.Equal("Write-Error: error\n", metaScriptResult.ConsoleText);
  }

  [Fact]
  public async Task RunMetascript_ExceptionShouldPropagateWhenInvokeImmyCommandThrows()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();
    var args = mocks.MockRunContextArgs(computer: Helpers.MakeComputer());
    mocks.IScriptInvoker!.SetupRunScriptAsync().Throws(new Exception("Should not be called"));
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<string>(false, false,
      Helpers.MakeScript(action: " write-output \"This should only print\";Invoke-ImmyCommand { write-output \"test\" }; write-output \"This should never print\";", executionContext: ScriptExecutionContext.Metascript),
      default, TimeSpan.FromSeconds(180), runContext, againstComputer: args.Computer);
    // assert
    Assert.True(metaScriptResult.HadTerminatingException);
    Assert.NotNull(metaScriptResult.TerminatingErrorRecord);
    Assert.Equal(1, metaScriptResult.TerminatingErrorRecord.InvocationInfo.ScriptLineNumber);
    Assert.Equal(40, metaScriptResult.TerminatingErrorRecord.InvocationInfo.OffsetInLine);
    // Ensure line-numbers were corrected when throwing the terminating exception inside the cmdlet
    Assert.Contains("At line:1 char:40", metaScriptResult.TerminatingErrorRecord.InvocationInfo.PositionMessage);
    Assert.Equal("This should only print", metaScriptResult.OutputAsObject);
  }
  [Fact]
  public async Task RunMetascript_ExceptionShouldNotPropagateWhenInvokeImmyCommandThrowsButIsCaught()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();
    var args = mocks.MockRunContextArgs(computer: Helpers.MakeComputer());
    mocks.IScriptInvoker!.SetupRunScriptAsync().Throws(new Exception("Should not be called"));
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<string>(false, false,
      Helpers.MakeScript(action: " write-output \"This should print\"; try { Invoke-ImmyCommand { write-output \"test\" }} catch { write-output \"Caught exception\"}; write-output \"This should print too\";", executionContext: ScriptExecutionContext.Metascript),
      default, TimeSpan.FromSeconds(180), runContext, againstComputer: args.Computer);
    // assert
    Assert.False(metaScriptResult.HadTerminatingException);
    Assert.Null(metaScriptResult.TerminatingErrorRecord);
    Assert.Equal(new[] { "This should print", "Caught exception", "This should print too" }, metaScriptResult.OutputAsCollection);
  }
  [Fact]
  public async Task RunMetascript_ShouldThrowNonTerminatingErrorWhenInvokeImmyIsSuppliedParameterThatIsUnavailableInCurrentSet()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();
    var args = mocks.MockRunContextArgs(computer: Helpers.MakeComputer());
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<string>(false, false,
      Helpers.MakeScript(action: "Invoke-ImmyCommand -TerminateFromNoLoggedOnUser { write-output \"test\" };", executionContext: ScriptExecutionContext.Metascript),
      default, TimeSpan.FromSeconds(180), runContext, againstComputer: args.Computer);
    // assert
    Assert.False(metaScriptResult.HadTerminatingException);
    Assert.Contains("A parameter cannot be found that matches parameter name 'TerminateFromNoLoggedOnUser'", metaScriptResult.ConsoleText);
  }
  [Fact]
  public async Task RunMetascript_ShouldThrowNonTerminatingWarningWhenInvokeImmyEncountersNoLoggedOnUserException()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();

    var noLoggedUserEx = new RemoteInvocationException("No logged on user", errorCode: -1, errorData: new(), deserializedErrorData: new CommonErrorData(new NoLoggedOnUserException()));
    mocks.IEphemeralAgentAcquisition!
      .Setup(ephemeralAgentAcquisition =>
        ephemeralAgentAcquisition.AcquireEphemeralAgentWithRetryAsync(It.IsAny<Dictionary<int, IRunScriptProvider>>(),
          It.IsAny<EphemeralSessionLink>(),
          It.IsAny<CancellationToken>(),
          It.IsAny<Action<string>?>(),
          It.IsAny<PSTaskDataStreamWriter?>(),
          It.IsAny<ComputerCircuitBreakerPolicy>(),
          It.IsAny<int>(),
          It.IsAny<int?>()))
      .ThrowsAsync(noLoggedUserEx);

    var args = mocks.MockRunContextArgs(computer: Helpers.MakeComputer());
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<string>(false, false,
      Helpers.MakeScript(action: "write-output \"This should print\";Invoke-ImmyCommand -Context User { write-output \"will never print\" }; write-output \"This should print too\";", executionContext: ScriptExecutionContext.Metascript),
      default, TimeSpan.FromSeconds(180), runContext, againstComputer: args.Computer);
    // assert
    Assert.False(metaScriptResult.HadTerminatingException);
    Assert.Contains("WARNING: No logged on user", metaScriptResult.ConsoleText);
    Assert.Equal(new[] { "This should print", "This should print too" }, metaScriptResult.OutputAsCollection);
  }
  [Fact]
  public async Task RunMetascript_ShouldThrowTerminatingErrorWhenInvokeImmyEncountersNoLoggedOnUserExceptionWithTerminateFromNoLoggedOnUser()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();

    var noLoggedUserEx = new RemoteInvocationException("No logged on user", errorCode: -1, errorData: new(), deserializedErrorData: new CommonErrorData(new NoLoggedOnUserException()));
    mocks.IEphemeralAgentAcquisition!
      .Setup(ephemeralAgentAcquisition =>
        ephemeralAgentAcquisition.AcquireEphemeralAgentWithRetryAsync(It.IsAny<Dictionary<int, IRunScriptProvider>>(),
          It.IsAny<EphemeralSessionLink>(),
          It.IsAny<CancellationToken>(),
          It.IsAny<Action<string>?>(),
          It.IsAny<PSTaskDataStreamWriter?>(),
          It.IsAny<ComputerCircuitBreakerPolicy>(),
          It.IsAny<int>(),
          It.IsAny<int?>()))
      .ThrowsAsync(noLoggedUserEx);

    var args = mocks.MockRunContextArgs(computer: Helpers.MakeComputer());
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<string>(false, false,
      Helpers.MakeScript(action: "write-output \"This should print\";Invoke-ImmyCommand -Context User -TerminateFromNoLoggedOnUser { write-output \"will never print\" }; write-output \"This shouldn't too\";", executionContext: ScriptExecutionContext.Metascript),
      default, TimeSpan.FromSeconds(180), runContext, againstComputer: args.Computer);
    // assert
    Assert.True(metaScriptResult.HadTerminatingException);
    Assert.NotNull(metaScriptResult.TerminatingErrorRecord);
    Assert.Equal("No logged on user", metaScriptResult.TerminatingErrorRecord.Exception.Message);
    Assert.Equal(new[] { "This should print" }, metaScriptResult.OutputAsCollection);
  }
  [Fact]
  public async Task RunMetascript_UserExecutionContextShouldNotShowInternalPowershellTerminatingCodeFromPowershellError()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();

    var noLoggedUserEx = new RemoteInvocationException("No logged on user", errorCode: -1, errorData: new(), deserializedErrorData: new CommonErrorData(new NoLoggedOnUserException()));
    mocks.IEphemeralAgentAcquisition!
      .Setup(ephemeralAgentAcquisition =>
        ephemeralAgentAcquisition.AcquireEphemeralAgentWithRetryAsync(It.IsAny<Dictionary<int, IRunScriptProvider>>(),
          It.IsAny<EphemeralSessionLink>(),
          It.IsAny<CancellationToken>(),
          It.IsAny<Action<string>?>(),
          It.IsAny<PSTaskDataStreamWriter?>(),
          It.IsAny<ComputerCircuitBreakerPolicy>(),
          It.IsAny<int>(),
          It.IsAny<int?>()))
      .ThrowsAsync(noLoggedUserEx);

    var args = mocks.MockRunContextArgs(computer: Helpers.MakeComputer());
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<string>(false, false,
      Helpers.MakeScript(action: "write-output \"This should never even print\";", executionContext: ScriptExecutionContext.CurrentUser),
      default, TimeSpan.FromSeconds(180), runContext, againstComputer: args.Computer);
    // assert
    Assert.DoesNotContain("throw $args[0];", metaScriptResult.ConsoleText);
  }
  [Fact]
  public async Task RunMetascript_ShouldThrowTerminatingExceptionForUserScriptIfSuppliedWithTerminateFromNoLoggedOnUserSwitch()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();
    var noLoggedUserEx = new RemoteInvocationException("No logged on user", errorCode: -1, errorData: new(), deserializedErrorData: new CommonErrorData(new NoLoggedOnUserException()));
    mocks.IEphemeralAgentAcquisition!
      .Setup(ephemeralAgentAcquisition =>
        ephemeralAgentAcquisition.AcquireEphemeralAgentWithRetryAsync(It.IsAny<Dictionary<int, IRunScriptProvider>>(),
          It.IsAny<EphemeralSessionLink>(),
          It.IsAny<CancellationToken>(),
          It.IsAny<Action<string>?>(),
          It.IsAny<PSTaskDataStreamWriter?>(),
          It.IsAny<ComputerCircuitBreakerPolicy>(),
          It.IsAny<int>(),
          It.IsAny<int?>()))
      .ThrowsAsync(noLoggedUserEx);

    var args = mocks.MockRunContextArgs(computer: Helpers.MakeComputer());
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<string>(false, false,
      Helpers.MakeScript(action: "write-output \"This should print only\";Invoke-ImmyCommand -Context User -TerminateFromNoLoggedOnUser { write-output \"Should never see this\" }; write-output \"This should not print\";", executionContext: ScriptExecutionContext.Metascript),
      default, TimeSpan.FromSeconds(180), runContext, againstComputer: args.Computer);
    // assert
    Assert.True(metaScriptResult.HadTerminatingException);
    Assert.Equal(new[] { "This should print only" }, metaScriptResult.OutputAsCollection);
  }
  [Fact]
  public async Task RunMetascript_ShouldWork()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: "\"Hello World!\"", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);
    // assert
    Assert.Equal("Hello World!", metaScriptResult.OutputAsObject);
  }

  [Theory]
  [CombinatorialData]
  public async Task RunMetascript_ShouldEmitScriptOutputToRunContextWhenScriptOutputCorrelationIdIsProvided(
    bool isScriptOutputCorrelationIdProvided)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = (mocks.IRunContextMock = new()).Object;
    Guid? correlationId = isScriptOutputCorrelationIdProvided ? Guid.NewGuid() : null;

    // act
    await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: "Write-Host -NoNewLine \"Hello World!\"", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      scriptOutputCorrelationId: correlationId,
      againstComputer: null);

    // assert
    mocks.IRunContextMock.Verify(r => r.HandlePsHostEvent(
      It.Is<ScriptOutputPSHostEvent>(e => e.ScriptOutput == "Hello World!"),
        correlationId
      ), Times.Once());
  }

  [Theory]
  [CombinatorialData]
  public async Task RunMetascript_ShouldEmitProgressEventsToRunContext(
    bool isScriptOutputCorrelationIdProvided)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = (mocks.IRunContextMock = new()).Object;
    Guid? correlationId = isScriptOutputCorrelationIdProvided ? Guid.NewGuid() : null;

    // act
    await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: "Write-Progress -Activity 'A' -Status 'B' -PercentComplete 45 -SecondsRemaining 11 -CurrentOperation 'D' -Completed ", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      scriptOutputCorrelationId: correlationId,
      againstComputer: null);

    // assert
    // we get progress logs even if the correlation id is not provided
    mocks.IRunContextMock
      .Verify(r => r.HandlePsHostEvent(It.IsAny<ProgressPsHostEvent>(), correlationId), Times.Once());
    var evt = (ProgressPsHostEvent)mocks.IRunContextMock.Invocations.First(i => i.IsVerified).Arguments[0];
    Assert.Equal("A", evt.ProgressRecord.Activity);
    Assert.Equal(45, evt.ProgressRecord.PercentComplete);
    Assert.Equal("B", evt.ProgressRecord.StatusDescription);
    Assert.Equal(11, evt.ProgressRecord.SecondsRemaining);
    Assert.Equal(ProgressRecordType.Completed, evt.ProgressRecord.RecordType);
    Assert.Equal("D", evt.ProgressRecord.CurrentOperation);

    mocks.IRunContextMock
      .Verify(a => a.HandlePsHostEvent(
        It.Is<ScriptOutputPSHostEvent>(e => e.ScriptOutput == "PROGRESS: B - A - D (11 seconds remaining) - 45%\r\n"),
        correlationId
      ), Times.Once());
  }

  [Fact]
  public async Task RunMetascript_ShouldNotShowInvisibleObjects()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<PSObject>(
      false,
      false,
      Helpers.MakeScript(action: "\"New-Object PSObject -Property @{PSTypeName='ImmyBot.Invisible';N=1}", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);
    // assert
    Assert.True(string.IsNullOrEmpty(metaScriptResult.ConsoleText?.Trim()));
  }


  [CombinatorialData]
  [Theory]
  public async Task RunMetascript_GetProviderInfo_Returns_Configuration(bool isDynamicProviderType)
  {
    // arrange
    var (_, mocks) = Helpers.BuildDomainEventBroker();
    _ = Helpers.BuildProviderActionsMock(mocks: mocks,
      integrationFormDataTypeFunc: isDynamicProviderType ? () => null : null
      );

    mocks.IPolicyRegistry ??= new();
    mocks.IMetascriptInvoker ??= new();
    mocks.IInitialSessionStateFactory ??= new();
    mocks.IMetascriptMessageHandler ??= new();

    mocks.MockServiceScopeFactoryService<IDynamicFormService>(
      new DynamicFormService(
        Helpers.BuildMetascriptRunspaceServer(mocks, GetSqliteDbContextFactory(), GetSqliteSoftwareDbContextFactory()).Item1,
        new CommandInfoConverter(
          Mock.Of<IOptions<AzureActiveDirectoryAuthOptions>>()),
        mocks.IMetascriptMessageHandler.Object)
      );

    var (metascriptInvoker, _) = Helpers.BuildMetascriptInvoker(mocks: mocks, ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var runContext = mocks.BuildRunContext();

    // act
    var metaScriptResultTask = metascriptInvoker.RunMetascript<object>(
      canAccessMspResources: false,
      canAccessParentTenant: false,
      Helpers.MakeScript(action: $"(Get-ProviderInfo -ProviderType \"UnitTestProvider\").Configuration", executionContext: ScriptExecutionContext.Metascript),
      cancellationToken: default,
      TimeSpan.FromSeconds(180),
      runContext);

    var scriptResult = await metaScriptResultTask;
    // assert
    Assert.Empty(scriptResult.ErrorCollection);
    Assert.False(scriptResult.HadTerminatingException);
    if (!(scriptResult.OutputAsObject is PSObject configObj))
    {
      Assert.Fail("Object received isn't a PSObject!");
      return;
    }

    var config = configObj.Members
      .Where(x => x.MemberType == PSMemberTypes.NoteProperty)
      .Select(x => (x.Name, Value: ((JsonElement)x.Value)))
      .ToDictionary(k => k.Name, v => v.Value);

    var foo = Assert.Contains("Foo", config);
    Assert.True(foo.ValueEquals("Bar"));
  }

  [Fact]
  public async Task RunMetascript_SetCacheKeyExpiration_Should_WorkWithExpiresNowSwitch()
  {
    // arrange
    var (keyedLocker, cacheRepo, mocks) = Helpers.BuildKeyedSemaphoreLockerAndCacheRepo();
    mocks.MockServiceScopeFactoryService(keyedLocker);
    mocks.MockServiceScopeFactoryService<IImmyCacheRepository>(cacheRepo);
    var (metascriptInvoker, _) = Helpers.BuildMetascriptInvoker(mocks: mocks, ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();
    var computerMock = runContext.Args.Computer;

    var setCachedDate = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
       Helpers.MakeScript(action: """Invoke-CommandCached -CacheKey "ABC123" -TTL (New-Timespan -seconds 60) -ScriptBlock {Get-Date}""",
       executionContext: ScriptExecutionContext.Metascript),
       default,
       TimeSpan.FromSeconds(180),
       runContext,
       againstComputer: computerMock);

    var keyWasExpired = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: """Set-CacheKeyExpiration -CacheKey "ABC123" -ExpiresNow""",
      executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      againstComputer: computerMock);

    var cachedDate = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
       Helpers.MakeScript(action: """Invoke-CommandCached -CacheKey "ABC123" -TTL (New-Timespan -seconds 60) -ScriptBlock {Get-Date}""",
       executionContext: ScriptExecutionContext.Metascript),
       default,
       TimeSpan.FromSeconds(180),
       runContext,
       againstComputer: computerMock);
    // assert
    Assert.All(new[] { setCachedDate, keyWasExpired, cachedDate }, r => Assert.False(r.HadTerminatingException));
    // Ensure SetExpiry returned true, indicating it found the key and expired it
    Assert.True(keyWasExpired.OutputAsObject is bool b && b);
    // Ensure the cached date is different from the first cached date, indicating the cache was cleared
    Assert.NotEqual((DateTime)setCachedDate.OutputAsObject!, (DateTime)cachedDate.OutputAsObject!);
  }

  [Fact]
  public async Task RunMetascript_SetCacheKeyExpiration_Should_WorkWithTimeSpanZero()
  {
    // arrange
    var (keyedLocker, cacheRepo, mocks) = Helpers.BuildKeyedSemaphoreLockerAndCacheRepo();
    mocks.MockServiceScopeFactoryService(keyedLocker);
    mocks.MockServiceScopeFactoryService<IImmyCacheRepository>(cacheRepo);
    var (metascriptInvoker, _) = Helpers.BuildMetascriptInvoker(mocks: mocks, ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();
    var computerMock = runContext.Args.Computer;

    var setCachedDate = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
       Helpers.MakeScript(action: """Invoke-CommandCached -CacheKey "ABC123" -TTL (New-Timespan -seconds 60) -ScriptBlock {Get-Date}""",
       executionContext: ScriptExecutionContext.Metascript),
       default,
       TimeSpan.FromSeconds(180),
       runContext,
       againstComputer: computerMock);

    var keyWasExpired = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: """Set-CacheKeyExpiration -CacheKey "ABC123" -TTL (New-Timespan)""",
      executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      againstComputer: computerMock);

    var cachedDate = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
       Helpers.MakeScript(action: """Invoke-CommandCached -CacheKey "ABC123" -TTL (New-Timespan -seconds 60) -ScriptBlock {Get-Date}""",
       executionContext: ScriptExecutionContext.Metascript),
       default,
       TimeSpan.FromSeconds(180),
       runContext,
       againstComputer: computerMock);

    // assert
    Assert.All(new[] { setCachedDate, keyWasExpired, cachedDate }, r => Assert.False(r.HadTerminatingException));
    // Ensure SetExpiry returned true, indicating it found the key and expired it
    Assert.True(keyWasExpired.OutputAsObject is bool b && b);
    // Ensure the cached date is different from the first cached date, indicating the cache was cleared
    Assert.NotEqual((DateTime)setCachedDate.OutputAsObject!, (DateTime)cachedDate.OutputAsObject!);
  }

  [Fact]
  public async Task RunMetascript_SetCacheKeyExpiration_Should_WorkWithTimespan()
  {
    // arrange
    var (keyedLocker, cacheRepo, mocks) = Helpers.BuildKeyedSemaphoreLockerAndCacheRepo();
    mocks.MockServiceScopeFactoryService(keyedLocker);
    mocks.MockServiceScopeFactoryService<IImmyCacheRepository>(cacheRepo);
    var (metascriptInvoker, _) = Helpers.BuildMetascriptInvoker(mocks: mocks, ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();
    var computerMock = runContext.Args.Computer;

    var setCachedDate = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
       Helpers.MakeScript(action: """Invoke-CommandCached -CacheKey "ABC123" -TTL (New-Timespan -seconds 60) -ScriptBlock {Get-Date}""",
       executionContext: ScriptExecutionContext.Metascript),
       default,
       TimeSpan.FromSeconds(180),
       runContext,
       againstComputer: computerMock);

    var keyWasExpired = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: """Set-CacheKeyExpiration -CacheKey "ABC123" -TTL (New-Timespan -seconds 5)""",
      executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      againstComputer: computerMock);

    await Task.Delay(TimeSpan.FromSeconds(6));

    var cachedDate = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
       Helpers.MakeScript(action: """Invoke-CommandCached -CacheKey "ABC123" -TTL (New-Timespan -seconds 60) -ScriptBlock {Get-Date}""",
       executionContext: ScriptExecutionContext.Metascript),
       default,
       TimeSpan.FromSeconds(180),
       runContext,
       againstComputer: computerMock);

    // assert
    Assert.All(new[] { setCachedDate, keyWasExpired, cachedDate }, r => Assert.False(r.HadTerminatingException));
    // Ensure SetExpiry returned true, indicating it found the key and adjusted the expiry
    Assert.True(keyWasExpired.OutputAsObject is bool b && b);
    // Ensure the cached date is different from the first cached date, indicating the cache was cleared
    Assert.NotEqual((DateTime)setCachedDate.OutputAsObject!, (DateTime)cachedDate.OutputAsObject!);
  }

  [Fact]
  public async Task RunMetascript_SetCacheKeyExpiration_Should_WorkWithDateTime()
  {
    // arrange
    var (keyedLocker, cacheRepo, mocks) = Helpers.BuildKeyedSemaphoreLockerAndCacheRepo();
    mocks.MockServiceScopeFactoryService(keyedLocker);
    mocks.MockServiceScopeFactoryService<IImmyCacheRepository>(cacheRepo);
    var (metascriptInvoker, _) = Helpers.BuildMetascriptInvoker(mocks: mocks, ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();
    var computerMock = runContext.Args.Computer;

    var setCachedDate = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
       Helpers.MakeScript(action: """Invoke-CommandCached -CacheKey "ABC123" -TTL (New-Timespan -seconds 60) -ScriptBlock {Get-Date}""",
       executionContext: ScriptExecutionContext.Metascript),
       default,
       TimeSpan.FromSeconds(180),
       runContext,
       againstComputer: computerMock);

    var keyWasExpired = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(
      variables: new() { { "NewExpiry", DateTime.UtcNow + TimeSpan.FromSeconds(5) } },
      action: """Set-CacheKeyExpiration -CacheKey "ABC123" -TTL $NewExpiry""",
      executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      againstComputer: computerMock);

    await Task.Delay(TimeSpan.FromSeconds(5));

    var cachedDate = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
       Helpers.MakeScript(action: """Invoke-CommandCached -CacheKey "ABC123" -TTL (New-Timespan -seconds 60) -ScriptBlock {Get-Date}""",
       executionContext: ScriptExecutionContext.Metascript),
       default,
       TimeSpan.FromSeconds(180),
       runContext,
       againstComputer: computerMock);
    // assert
    Assert.All(new[] { setCachedDate, keyWasExpired, cachedDate }, r => Assert.False(r.HadTerminatingException));
    // Ensure SetExpiry returned true, indicating it found the key and adjusted the expiry
    Assert.True(keyWasExpired.OutputAsObject is bool b && b);
    // Ensure the cached date is different from the first cached date, indicating the cache was cleared
    Assert.NotEqual((DateTime)setCachedDate.OutputAsObject!, (DateTime)cachedDate.OutputAsObject!);
  }

  [Fact]
  public async Task RunMetascript_SetCacheKeyExpiration_Should_ReturnFalseWhenKeyNotFound()
  {
    // arrange
    var (keyedLocker, cacheRepo, mocks) = Helpers.BuildKeyedSemaphoreLockerAndCacheRepo();
    mocks.MockServiceScopeFactoryService(keyedLocker);
    mocks.MockServiceScopeFactoryService<IImmyCacheRepository>(cacheRepo);
    var (metascriptInvoker, _) = Helpers.BuildMetascriptInvoker(mocks: mocks, ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();
    var computerMock = runContext.Args.Computer;

    var keyWasExpired = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(
      variables: new() { { "NewExpiry", DateTime.UtcNow + TimeSpan.FromSeconds(5) } },
      action: """Set-CacheKeyExpiration -CacheKey "ABC123" -TTL $NewExpiry""",
      executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      againstComputer: computerMock);
    // assert
    // Ensure SetExpiry returned false, indicating it did not find the key
    Assert.True(keyWasExpired.OutputAsObject is bool b && !b);
  }

  [Fact]
  public async Task RunMetascript_InvokeCommandCached_ShouldCorrectlyExpireCachedItemOnSubsequentInvocationsIfTTLExceeded()
  {
    // arrange
    var (keyedLocker, cacheRepo, mocks) = Helpers.BuildKeyedSemaphoreLockerAndCacheRepo();
    mocks.MockServiceScopeFactoryService(keyedLocker);
    mocks.MockServiceScopeFactoryService<IImmyCacheRepository>(cacheRepo);
    var (metascriptInvoker, _) = Helpers.BuildMetascriptInvoker(mocks: mocks, ctxFactory: GetSqliteDbContextFactory(), globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var runContext = mocks.BuildRunContext();
    var computerMock = runContext.Args.Computer;
    var invokeCachedCommandTest =
      () => metascriptInvoker.RunMetascript<object>(
        false,
        false,
      Helpers.MakeScript(action: """Invoke-CommandCached -CacheKey "ABC123" -TTL (New-Timespan -seconds 5) -ScriptBlock {Get-Date}""",
      executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      againstComputer: computerMock);
    // act
    var firstResult = await invokeCachedCommandTest();
    await Task.Delay(TimeSpan.FromSeconds(3));
    var secondResult = await invokeCachedCommandTest();
    await Task.Delay(TimeSpan.FromSeconds(2));
    var thirdResult = await invokeCachedCommandTest();

    // assert
    Assert.False(firstResult.HadTerminatingException);
    Assert.False(secondResult.HadTerminatingException);
    Assert.False(thirdResult.HadTerminatingException);

    var firstDate = (DateTime)firstResult.OutputAsObject!;
    var secondDate = (DateTime)secondResult.OutputAsObject!;
    var thirdDate = (DateTime)thirdResult.OutputAsObject!;

    // ensure that the first and second tasks returned the same date, indicating the cache worked as the TTL had not expired
    Assert.Equal(firstDate, secondDate);
    // ensure that the third task returned a different date, indicating the cache expired and thus the command was re-run
    Assert.NotEqual(firstDate, thirdDate);
  }

  [Fact]
  public async Task RunMetascript_GivenInvalidFunctionScript_ShouldWork()
  {
    var dbFactory = GetSqliteDbContextFactory();
    await using var localDb = dbFactory();

    var badFunctionScript = new Script()
    {
      Name = "Get-MyBadObject",
      Action = "$MyBadObject = @{ 'test' }; function Get-MyBadObject {  return $MyBadObject; }",
      ScriptExecutionContext = ScriptExecutionContext.Metascript,
      ScriptCategory = ScriptCategory.Function,
    };
    await localDb.Scripts.AddAsync(badFunctionScript);
    await localDb.SaveChangesAsync();

    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: dbFactory,
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: "\"Hello World!\"", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);
    // assert
    Assert.Equal("Hello World!", metaScriptResult.OutputAsObject);
  }

  [Fact]
  public async Task RunMetascript_ShouldUseConstrainedLanguageModeInFunctionScripts()
  {
    var dbFactory = GetSqliteDbContextFactory();
    await using var localDb = dbFactory();


    var getLanguageModeScript = new Script()
    {
      Name = "Get-LanguageMode",
      Action = "$ExecutionContext.SessionState.LanguageMode",
      ScriptExecutionContext = ScriptExecutionContext.Metascript,
      ScriptCategory = ScriptCategory.Function,
    };
    var getEnvironmentVariablesScript = new Script()
    {
      Name = "Get-EnvironmentVariables",
      Action = "[System.Environment]::GetEnvironmentVariables()",
      ScriptExecutionContext = ScriptExecutionContext.Metascript,
      ScriptCategory = ScriptCategory.Function,
    };
    await localDb.Scripts.AddAsync(getLanguageModeScript);
    await localDb.Scripts.AddAsync(getEnvironmentVariablesScript);
    await localDb.SaveChangesAsync();

    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: dbFactory,
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var result = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: "Get-LanguageMode", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);
    Assert.Equal("ConstrainedLanguage", result.OutputAsObject!.ToString());

    var result2 = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: "Get-EnvironmentVariables", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // Don't use Assert.Null here, because if that does fail, it will show the
    // entire object, which is potentially sensitive in CI
    Assert.True(result2.OutputAsObject == null, "Get-EnvironmentVariables was expected to return null but didn't");
  }

  [Theory]
  [InlineData("\"Hello World!\"", true)]
  [InlineData("$false", false)]
  public async Task RunMetascript_ShouldAddWarningWhenExpectedOutputIsBoolButActualOutputIsNot(string script, bool shouldShowWarning)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<bool>(
      false,
      false,
      Helpers.MakeScript(action: script, executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);
    // assert

    var partialWarningMessage = "Prevent output pollution by leveraging";
    if (shouldShowWarning)
    {
      Assert.Contains(partialWarningMessage, metaScriptResult.ConsoleText);
    }
    else
    {
      Assert.DoesNotContain(partialWarningMessage, metaScriptResult.ConsoleText);
    }
  }

  [Fact]
  public async Task RunMetascript_ShouldReturnOuterError()
  {
    // arrange
    var outerErr = "Outer!!";
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: $"try {{ throw \"Inner!!\" }} catch {{ throw \"{outerErr}\" }}", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.True(metaScriptResult.HadTerminatingException);
    Assert.NotNull(metaScriptResult.TerminatingErrorRecord);
    Assert.Equal(outerErr, metaScriptResult.TerminatingErrorRecord!.Exception.Message);
  }

  [Fact]
  public async Task RunMetascript_GetDate_ShouldWork()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: "get-date", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.False(metaScriptResult.HadTerminatingException);
    Assert.Empty(metaScriptResult.ErrorCollection);
    Assert.True(metaScriptResult.OutputAsObject is DateTime);
  }

  [Fact]
  public async Task RunMetascript_GetOtp_Totp_ShouldWork()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var keyString = "JBSWY3DPEHPK3PXP";
    var totp = new Totp(Base32Encoding.ToBytes(keyString));
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: $"Get-Otp -Type TOTP -KeyType Base32 -KeyString \"{keyString}\"", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.False(metaScriptResult.HadTerminatingException);
    Assert.Empty(metaScriptResult.ErrorCollection);
    Assert.Equal(totp.ComputeTotp(), metaScriptResult.OutputAsObject);
  }

  [Fact]
  public async Task RunMetascript_GetOtp_Hotp_ShouldWork()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var keyString = "JBSWY3DPEHPK3PXP";
    var counter = 133742069;
    var hotp = new Hotp(Base32Encoding.ToBytes(keyString));
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: $"Get-Otp -Type HOTP -KeyType Base32 -KeyString \"{keyString}\" -Counter {counter}", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.False(metaScriptResult.HadTerminatingException);
    Assert.Empty(metaScriptResult.ErrorCollection);
    Assert.Equal(hotp.ComputeHOTP(counter), metaScriptResult.OutputAsObject);
  }

  public static IEnumerable<object?[]> ScriptInputAndOutputs()
  {
    return new object?[][]
      {
        ["5..1 | sort", "1\n2\n3\n4\n5\n", new[] { 1, 2, 3, 4, 5 }, ScriptOutputType.Table],
        ["$null", "", null, ScriptOutputType.Object]
      };
  }

  [Theory]
  [MemberData(nameof(ScriptInputAndOutputs))]
  public async Task RunMetascript_ShouldReturnExpectedOutput(
    string code,
    string expectedConsoleText,
    object? expectedOutput,
    ScriptOutputType outputType)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var script = Helpers.MakeScript(action: code, executionContext: ScriptExecutionContext.Metascript, outputType: outputType);

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      script,
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.False(metaScriptResult.HadTerminatingException);
    Assert.Null(metaScriptResult.TerminatingErrorRecord);
    Assert.Equal(expectedConsoleText, metaScriptResult.ConsoleText);
    Assert.Equal(expectedOutput, script.OutputType == ScriptOutputType.Object ? metaScriptResult.OutputAsObject : metaScriptResult.OutputAsCollection);
  }

  public static IEnumerable<object[]> GetComputersForFilterScriptPayloads()
  {
    return new[]
    {
      new FilteredPsComputersTestPayload("Get-ImmyComputer", false, false, false, false, TargetType.All),
      new FilteredPsComputersTestPayload("Get-ImmyComputer", true, false, false, false, TargetType.AllForTenant),
      new FilteredPsComputersTestPayload("Get-ImmyComputer", false, false, false, true, TargetType.Computer),
      new FilteredPsComputersTestPayload("Get-ImmyComputer", true, false, false, true, TargetType.Computer),
      // test-function is a local function script added to the db
      new FilteredPsComputersTestPayload("test-function", true, false, false, false, TargetType.AllForTenant),
      new FilteredPsComputersTestPayload("test-function", true, false, false, false, TargetType.AllForTenant, ScriptType: DatabaseType.Global, ExpectedError: "The term 'test-function' is not recognized"),
      new FilteredPsComputersTestPayload("Write-Output $false", true, false, false, false, TargetType.AllForTenant, ExpectedError: "Error converting value"),
    }.Select(o => new object[] { o });
  }

  [Fact]
  public async Task GetImmyComputer_InMetascript_ShouldReturnDevicesExcludedFromMaintenance()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());


    var mspTenant = GetOrCreateMspTenant();
    var runContext = mocks.BuildRunContext(tenant: mspTenant,
      manuallyTriggeredBy: GetAuthUser(GetOrCreateUser(UserType.MspAdmin)),
      ctxFactory: GetSqliteDbContextFactory());

    mocks.IComputerAssignmentActions ??= new Mock<IComputerAssignmentActions>();
    mocks.MockServiceScopeFactoryService(mocks.IComputerAssignmentActions!.Object);

    var script = new Script
    {
      Name = "",
      Action = "Get-ImmyComputer -TargetGroupFilter DomainControllers",
      ScriptCategory = ScriptCategory.MaintenanceTaskSetter,
      ScriptExecutionContext = ScriptExecutionContext.Metascript,
      OutputType = ScriptOutputType.Object,
      ScriptType = DatabaseType.Local,
    };


    // act
    await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      script,
      default, TimeSpan.FromSeconds(30), runContext: runContext);

    mocks.IComputerAssignmentActions!.Verify(a => a.GetComputersInTarget(
      It.IsAny<TargetType>(),
      It.IsAny<TargetGroupFilter>(),
      It.IsAny<string?>(),
      It.IsAny<int?>(),
      It.IsAny<bool>(),
      It.IsAny<bool>(),
      It.IsAny<int?>(), // providerLinkId
      null, // providerDeviceGroupType
      null, // providerClientGroupType
      It.IsAny<Guid?>(),
      It.IsAny<IAsyncPolicy>(),
      It.IsAny<Context>(),
      It.IsAny<bool>(),
      It.IsAny<bool>(),
      It.IsAny<bool>(),
      It.IsAny<bool>(),
      It.IsAny<int?>(),
      It.IsAny<bool>(),
      It.IsAny<CancellationToken>(),
      It.IsAny<bool>(),
      It.IsAny<bool>(),
      It.IsAny<bool>(),
      It.IsAny<ICollection<string>>(),
      It.IsAny<bool>(),
      // assert false is passed here
      false), Times.Once);

  }

  [Theory]
  [MemberData(nameof(GetComputersForFilterScriptPayloads), DisableDiscoveryEnumeration = true)]
  public async Task GetComputersForFilterScript_ShouldHaveExpectedOutcome(
    FilteredPsComputersTestPayload payload)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    CreateLocalScript("test-function", "Get-ImmyComputer", category: ScriptCategory.Function);

    // two computers, one tenant
    var tenant = GetOrCreateMspTenant();
    var computer1 = CreateBlankComputer(tenant.Id);
    CreateBlankComputer(tenant.Id);

    int? limitToTenantId = payload.SetLimitToTenantId ? tenant.Id : null;
    bool includeChildTenants = payload.SetIncludeChildTenants;
    bool allowAccessToParentTenant = payload.SetAllowAccessToParentTenant;
    int? computerId = payload.SetComputerId ? computer1.Id : null;

    var script = new Script
    {
      Name = "",
      Action = payload.Action,
      ScriptCategory = ScriptCategory.FilterScriptDeploymentTarget,
      ScriptExecutionContext = ScriptExecutionContext.Metascript,
      OutputType = ScriptOutputType.Object,
      ScriptType = payload.ScriptType,
    };
    (mocks.IComputerAssignmentActions ??= new())
      .Setup(a => a.GetComputersInTarget(
        payload.ExpectedTargetType,
        TargetGroupFilter.All,
        computerId != null ? computerId.ToString() : null,
        limitToTenantId,
        includeChildTenants,
        allowAccessToParentTenant,
        null, // providerLinkId
        null, // providerDeviceGroupType
        null, // providerClientGroupType
        It.IsAny<Guid?>(),
        It.IsAny<IAsyncPolicy>(),
        It.IsAny<Context>(),
        It.IsAny<bool>(),
        It.IsAny<bool>(),
        It.IsAny<bool>(),
        It.IsAny<bool>(),
        It.IsAny<int?>(),
        It.IsAny<bool>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<bool>(),
        It.IsAny<bool>(),
        It.IsAny<bool>(),
        It.IsAny<ICollection<string>>(),
        It.IsAny<bool>(),
        It.IsAny<bool>()))
      // return just one computer. We verify it gets sent to the metascript result below
      .ReturnsAsync(() => DisposableValue.Create(new[] { computer1 }.AsQueryable(), onDisposedCallback: () => { }))
      .Verifiable();
    mocks.MockServiceScopeFactoryService(mocks.IComputerAssignmentActions!.Object);

    // act
    var res = await metascriptInvoker.GetComputersForFilterScript(
      true,
      true,
      script,
      default,
      limitToTenantId: limitToTenantId,
      includeChildTenants: includeChildTenants,
      computerId: computerId);

    // assert

    if (payload.ExpectedError is null && payload.ExpectedTerminatingException is null)
    {
      // Make sure GetComputersInTarget was called with the
      // expected arguments
      Mock.Verify(mocks.IComputerAssignmentActions);
      // Make sure the result of GetComputersInTarget is correctly
      // converted to ps computers and written to the metascript result
      Assert.Single(res.OutputAsCollection);
      Assert.Equal(computer1.ComputerName, res.OutputAsCollection.First().Name);
      Assert.Empty(res.ErrorCollection);
    }
    else
    {
      if (payload.ExpectedTerminatingException is not null)
      {
        Assert.Contains(payload.ExpectedTerminatingException, res.TerminatingErrorRecord?.ToString());
      }
      if (payload.ExpectedError is not null)
      {
        Assert.NotEmpty(res.ErrorCollection);
        Assert.StartsWith(payload.ExpectedError, res.ErrorCollection[0].ToString());
      }
    }
  }
  [Fact]
  public async Task ExpandString_ShouldExpandVariables()
  {
    // arrange
    var (metascriptInvoker, _) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
        Helpers.MakeScript(action: @"$var = 'Hello World';Expand-String '$var'", executionContext: ScriptExecutionContext.Metascript),
        default,
        TimeSpan.FromSeconds(180));
    // assert
    Assert.Empty(metaScriptResult.ErrorCollection);
    Assert.Equal("Hello World", metaScriptResult.ConsoleText?.Trim());
  }
  [Fact]
  public async Task NewLiteralString_ShouldCreateLiteralStringType()
  {
    // arrange
    var (metascriptInvoker, _) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    var metaScriptResult = await metascriptInvoker.RunMetascript<LiteralString>(
      false,
      false,
        Helpers.MakeScript(action: @"New-LiteralString '$var'", executionContext: ScriptExecutionContext.Metascript),
        default,
        TimeSpan.FromSeconds(180));
    // assert
    Assert.Empty(metaScriptResult.ErrorCollection);
    Assert.Equal("$var", metaScriptResult.ConsoleText?.Trim());
  }
  public enum TriggeredBy
  {
    Schedule,
    ScheduleWithMspResources,
    MspAdmin,
    CustomerAdmin,
    CustomerNonAdmin,
    MspNonAdmin
  }

  [Theory]
  [InlineData(TriggeredBy.MspAdmin, false)]
  [InlineData(TriggeredBy.CustomerAdmin, true)]
  [InlineData(TriggeredBy.CustomerNonAdmin, true)]
  [InlineData(TriggeredBy.MspNonAdmin, true)]
  [InlineData(TriggeredBy.Schedule, true)]
  public async Task GetImmyComputer_UseParentTenant_RestrictsAccessToMspAdmins(TriggeredBy triggeredByType,
    bool shouldThrow)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var mspTenant = GetOrCreateMspTenant();
    var customerTenant = GetOrCreateCustomerTenant(parentTenantId: mspTenant.Id);

    var parentTenantComputer = CreateBlankComputer(mspTenant.Id);
    var childTenantComputer = CreateBlankComputer(customerTenant.Id);

    // mocks for GetComputersInTarget
    mocks.IComputerAssignmentActions ??= new Mock<IComputerAssignmentActions>();
    mocks.MockComputerAssignmentActions_GetComputersInTarget(
      TargetType.All,
      mspTenant.Id,
      includeChildTenantsToMock: false,
      allowAccessToParentTenantToMock: false,
      [parentTenantComputer]);

    mocks.MockComputerAssignmentActions_GetComputersInTarget(
      TargetType.All,
      customerTenant.Id,
      includeChildTenantsToMock: false,
      allowAccessToParentTenantToMock: false,
      [childTenantComputer]);

    mocks.MockServiceScopeFactoryService(mocks.IComputerAssignmentActions.Object);

    var featureTracker = new Mock<IFeatureTracker>();
    featureTracker.Setup(a => a.IsEnabled(FeatureEnum.GetImmyComputerUseParentTenantFeature, It.IsAny<bool>()))
      .Returns(true);

    mocks.MockServiceScopeFactoryService(featureTracker.Object);

    // run context
    var runContext = triggeredByType switch
    {
      TriggeredBy.MspAdmin => Helpers.BuildStageRunContext(
        mocks,
        tenant: mspTenant,
        computer: childTenantComputer),
      TriggeredBy.MspNonAdmin => Helpers.BuildStageRunContext(
        mocks,
        tenant: mspTenant,
        computer: childTenantComputer),
      TriggeredBy.CustomerAdmin => Helpers.BuildStageRunContext(
        mocks,
        tenant: customerTenant,
        computer: childTenantComputer),
      TriggeredBy.CustomerNonAdmin => Helpers.BuildStageRunContext(
        mocks,
        tenant: customerTenant,
        computer: childTenantComputer),
      TriggeredBy.Schedule => Helpers.BuildStageRunContext(
        mocks,
        tenant: customerTenant,
        computer: childTenantComputer),
      _ => throw new NotImplementedException(),
    };

    if (triggeredByType == TriggeredBy.MspAdmin)
      runContext.Args.Tenant.IsMsp = true;

    var triggeredByUser = triggeredByType switch
    {
      TriggeredBy.MspAdmin => GetOrCreateUser(UserType.MspAdmin),
      TriggeredBy.MspNonAdmin => GetOrCreateUser(UserType.MspNonAdmin),
      TriggeredBy.CustomerAdmin => GetOrCreateUser(UserType.CustomerAdmin),
      TriggeredBy.CustomerNonAdmin => GetOrCreateUser(UserType.CustomerNonAdmin),
      _ => null
    };

    if (triggeredByUser is not null)
      runContext.Args.ManuallyTriggeredBy = await GetAuthUserDtoFromUserId(triggeredByUser.Id);

    await runContext.Initialize();

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<string>(
      runContext.CanAccessMspResources(),
      runContext.CanAccessParentTenant(),
      Helpers.MakeScript(action: "Get-ImmyComputer -UseParentTenant", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    if (shouldThrow)
    {
      Assert.NotEmpty(metaScriptResult.ErrorCollection);
      var error = metaScriptResult.ErrorCollection[0];
      Assert.Equal(ErrorCategory.PermissionDenied, error.CategoryInfo.Category);
      Assert.Contains(
        "The 'UseParentTenant' switch can only be used by an MSP admin.",
        error.Exception.Message);
    }
    else
    {
      if (metaScriptResult.ConsoleText != null)
      {
        var lines = StripColorCodes(metaScriptResult.ConsoleText).Split('\n', StringSplitOptions.RemoveEmptyEntries);
        var nameLine = Array.Find(lines, l => l.StartsWith("Name"));

        if (nameLine != null)
        {
          var computerName = nameLine[(nameLine.IndexOf(':') + 1)..].Trim();
          Assert.Equal(parentTenantComputer.ComputerName, computerName);
        }
        else
        {
          Assert.Fail("Could not find computer name in the output");
        }
      }
    }
  }

  [Theory]
  [InlineData(TriggeredBy.Schedule, true)]
  [InlineData(TriggeredBy.ScheduleWithMspResources, false)]
  [InlineData(TriggeredBy.MspAdmin, false)]
  [InlineData(TriggeredBy.CustomerAdmin, true)]
  public async Task GetAzureADAuthHeader_UseMspTenant(TriggeredBy triggeredByType, bool shouldThrow)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var azureActionsMock = new Mock<IAzureActions>();
    azureActionsMock.Setup(a => a.GetAzureTenantAuthParameters(It.IsAny<Tenant>(), It.IsAny<AzureResourceAlias>()))
      .ReturnsAsync(new Application.Lib.Azure.AzureAuthParameters() { TenantId = "", Token = "", ClientId = "" });

    mocks.MockServiceScopeFactoryService(azureActionsMock.Object);

    var mspTenant = GetOrCreateMspTenant();
    var customerTenant = GetOrCreateCustomerTenant();

    var triggeredByUser = triggeredByType switch
    {
      TriggeredBy.MspAdmin => CreateUser(mspTenant.Id, isAdmin: true),
      TriggeredBy.CustomerAdmin => CreateUser(customerTenant.Id, isAdmin: true),
      _ => null
    };

    Schedule? schedule = triggeredByType switch
    {
      TriggeredBy.Schedule => CreateSchedule(new Schedule() { AllowAccessToMSPResources = false }),
      TriggeredBy.ScheduleWithMspResources => CreateSchedule(new Schedule() { AllowAccessToMSPResources = true }),
      _ => null
    };

    if (schedule is not null)
    {
      mocks.IScheduleActions ??= new Mock<IScheduleActions>();
      mocks.IScheduleActions
        .Setup(a => a.GetScheduleById(schedule.Id, It.IsAny<CancellationToken>()))
        .ReturnsAsync(schedule);
    }

    mocks.ITenantActions ??= new Mock<ITenantActions>();

    mocks.ITenantActions
      .Setup(a => a.GetTenantById(mspTenant.Id, It.IsAny<CancellationToken>()))
      .ReturnsAsync(mspTenant);

    mocks.ITenantActions
      .Setup(a => a.GetTenantById(customerTenant.Id, It.IsAny<CancellationToken>()))
      .ReturnsAsync(customerTenant);

    var runContext = triggeredByType switch
    {
      TriggeredBy.MspAdmin => mocks.BuildRunContext(tenant: mspTenant,
        manuallyTriggeredBy:  triggeredByUser is not null ? GetAuthUser(triggeredByUser) : null,
        ctxFactory: GetSqliteDbContextFactory()),
      TriggeredBy.CustomerAdmin => mocks.BuildRunContext(tenant: customerTenant,
        manuallyTriggeredBy:  triggeredByUser is not null ? GetAuthUser(triggeredByUser) : null,
        ctxFactory: GetSqliteDbContextFactory()),
      TriggeredBy.ScheduleWithMspResources => mocks.BuildRunContext(tenant: customerTenant,
        scheduleId: schedule?.Id,
        ctxFactory: GetSqliteDbContextFactory()),
      TriggeredBy.Schedule => mocks.BuildRunContext(tenant: customerTenant,
        scheduleId: schedule?.Id,
        ctxFactory: GetSqliteDbContextFactory()),
      _ => throw new NotImplementedException(),
    };
    await runContext.Initialize();
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      runContext.CanAccessMspResources(),
      runContext.CanAccessParentTenant(),
        Helpers.MakeScript(action: @"Get-ImmyAzureAuthHeader -UseMspTenant", executionContext: ScriptExecutionContext.Metascript),
        default,
        TimeSpan.FromSeconds(180),
        runContext,
        againstComputer: mocks.RunContext!.Args.Computer);
    // assert
    if (shouldThrow)
      Assert.Equal(ErrorCategory.PermissionDenied, metaScriptResult.ErrorCollection[0].CategoryInfo.Category);
    else
      Assert.Empty(metaScriptResult.ErrorCollection);
  }

  [Fact]
  public async Task RunMetascript_WriteError_ErrorActionStop_ShouldReturnTheLastError()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: "Write-Error \"little problem\"; write-error \"Big Problem\" -ErrorAction Stop", executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.True(metaScriptResult.HadTerminatingException);
    Assert.NotNull(metaScriptResult.TerminatingErrorRecord);
    Assert.IsType<WriteErrorException>(metaScriptResult.TerminatingErrorRecord.Exception);
    Assert.Equal("Big Problem", metaScriptResult.TerminatingErrorRecord.Exception.Message);
  }

  [Fact]
  public async Task InvokingModuleFunction_ShouldEmitCommandNotFoundWhenModuleNotImported()
  {
    var moduleName = "MyModule";
    var moduleFunctionName = "Get-ModuleFunc";
    CreateLocalModuleScript(moduleName, moduleFunctionName);

    var nonImportModuleFunctionResult = await RunScript<object>($"{moduleFunctionName} \"Hello World!\"");
    Assert.Single(nonImportModuleFunctionResult.ErrorCollection);
    Assert.IsType<CommandNotFoundException>(nonImportModuleFunctionResult.ErrorCollection.Single().Exception);
    Assert.Contains($"The term '{moduleFunctionName}' is not recognized", nonImportModuleFunctionResult.ConsoleText);
  }

  [Theory()]
  [InlineData("Add-UriQueryParameter 'https://www.google.com/?q=test' -OverwriteParameters @('b') -Parameter @{q='test2'}", "Add-UriQueryParameter: Overwrite-parameter 'b' does not exist in the input table\nhttps://www.google.com/?q=test&q=test2")]
  [InlineData("Add-UriQueryParameter -Uri 'https://www.google.com' -Parameter @{q='test'}", "https://www.google.com/?q=test")]
  [InlineData("Add-UriQueryParameter -Uri 'https://www.google.com' @{q='test'}", "https://www.google.com/?q=test")]
  [InlineData("Add-UriQueryParameter 'https://www.google.com' -Parameter @{q='test'}", "https://www.google.com/?q=test")]
  [InlineData("Add-UriQueryParameter 'https://www.google.com/?q=test' -Parameter @{q='test2'}", "https://www.google.com/?q=test&q=test2")]
  [InlineData("Add-UriQueryParameter 'https://www.google.com/?q=test' -OverwriteParameters @('q') -Parameter @{q='test2'}", "https://www.google.com/?q=test2")]
  [InlineData("'https://www.google.com' | Add-UriQueryParameter -Parameter @{q='test'}", "https://www.google.com/?q=test")]
  [InlineData("'https://www.google.com' | Add-UriQueryParameter @{q='test'}", "https://www.google.com/?q=test")]
  [InlineData("'https://www.google.com' | Add-UriQueryParameter", "https://www.google.com/")]
  public async Task BuiltInCmdletTest(string script, string expectedConsoleOut)
  {
    var consoleOut = await RunScript($"Trace-Command ParameterBinding {{ {script} }} ");
    // Should have the query parameter added
    Assert.Equal(expectedConsoleOut, consoleOut);
  }

  [Fact]
  public async Task InvokingModuleFunction_ShouldExecuteWhenAfterImportingModule()
  {
    var moduleName = "MyModule";
    var moduleFunctionName = "Get-ModuleFunc";
    CreateLocalModuleScript(moduleName, moduleFunctionName);


    var helloWorld = await RunScript($"Import-Module {moduleName.ToUpper()};{moduleFunctionName} \"Hello World!\"");
    Assert.Equal("Hello World!", helloWorld);
  }

  [Fact]
  public async Task ModuleFunction_ShouldExecuteAfterImport()
  {
    var moduleName = "MyModule";
    var moduleFunctionName = "Get-ModuleFunc";
    CreateLocalModuleScript(moduleName, moduleFunctionName);

    var helloWorld = await RunScript($"Import-Module {moduleName};{moduleFunctionName} \"Hello World!\"");
    Assert.Equal("Hello World!", helloWorld);
  }
  [Fact]
  public async Task RunMetascript_ShouldAddMaintenanceVariablesFromRunContext()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var action = new MaintenanceAction
    {
      Id = 10,
      MaintenanceIdentifier = "test",
      MaintenanceDisplayName = "ActionName",
      DetectedVersion = new NuGet.Versioning.SemanticVersion(1, 2, 3),
      DesiredVersion = new NuGet.Versioning.SemanticVersion(2, 3, 4),
      DesiredSoftwareState = DesiredSoftwareState.ThisVersion,
      ActionStatus = MaintenanceActionStatus.PendingExecution,
      ActionReason = MaintenanceActionReason.SoftwareMissing,
      ActionResult = MaintenanceActionResult.Pending,
      ActionResultReason = null,
      Parameters = PSSerializer.Serialize(new { foo = "bar" })
    };
    var actionRunContext = Helpers.BuildActionRunContext(mocks,
      session: new MaintenanceSession { Id = 5 },
      action: action,
      isRepair: true);

    var script = Helpers.MakeScript(action: @"@($ActionId
$ActionName
$ActionDetectedVersion.ToString()
$ActionDesiredVersion.ToString()
$ActionDesiredSoftwareState
$ActionStatus
$ActionReason
$ActionResult
$ActionResultReason
$ActionParameters
$SessionIsRepairing
$SessionId) | Out-String", executionContext: ScriptExecutionContext.Metascript);

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      script,
      default,
      TimeSpan.FromSeconds(180),
      actionRunContext,
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.Equal(@"10
ActionName
1.2.3
2.3.4
ThisVersion
PendingExecution
SoftwareMissing
Pending

foo
---
bar
True
5", metaScriptResult.ConsoleText?.Trim());
  }

  /// <summary>
  /// This unit test currently only passes on linux
  /// </summary>
  [Fact]

  public async Task FileSystemProvider_ShouldBeInaccessibleFromScripts()
  {
    if (OperatingSystem.IsMacOS())
    {
      return;
    }
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    string functionScript = @$"
dir / -ErrorAction Stop
";
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: functionScript, executionContext: ScriptExecutionContext.Metascript, name: "get-dir"),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert

    // -ErrorAction Stop does not work here because we are mucking with powershell hooks to cause this exception to occur.
    if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
    {
      Assert.IsType<SecurityException>(metaScriptResult.ErrorCollection[0].Exception.InnerException);
    }
    else
    {
      Assert.IsType<ItemNotFoundException>(metaScriptResult.TerminatingException);
    }
  }

  /// <summary>
  /// This unit test currently only passes on linux
  /// </summary>
  [Fact]
  public async Task FileSystemProvider_ShouldBeInaccessibleFromPSESScripts()
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    string functionScript = @$"
dir / -ErrorAction Stop
";
    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: functionScript,
        executionContext: ScriptExecutionContext.Metascript,
        useNullScriptName: true),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
    {
      Assert.IsType<ProviderNotFoundException>(metaScriptResult.TerminatingException);
    }
    else
    {
      Assert.IsType<ItemNotFoundException>(metaScriptResult.TerminatingException);
    }
  }

  [Theory]
  [CombinatorialData]
  public async Task RunMetascript_ShouldOnlyLoadAzureADWhenRequired(bool required)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());
    // act

    var script = required ? "Import-Module AzureAD\n" : "";
    script += "(get-command Connect-AzureAD) -eq $null";
    var metaScriptResult = await metascriptInvoker.RunMetascript<bool>(
      false,
      false,
      Helpers.MakeScript(action: script, executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    if (required)
    {
      Assert.False(metaScriptResult.OutputAsObject);
    }
    else
    {
      Assert.True(metaScriptResult.OutputAsObject);
    }
  }

  /// <summary>
  /// Calling Connect-ImmyAzureAD should work even if the user did not explicitly call Import-Module AzureAD
  /// </summary>
  /// <param name="importAzureAd"></param>
  [Theory]
  [CombinatorialData]
  public async Task RunMetascript_Connect_ImmyAzureAD_ShouldImportAZModuleWhenMissing(
    bool importAzureAd)
  {
    // arrange
    var mocks = new MockedServices();
    var azureActionsMock = new Mock<IAzureActions>();
    azureActionsMock.Setup(a =>
        a.GetAzureTenantAuthParameters(It.IsAny<Tenant>(), It.IsAny<AzureResourceAlias>()))
      .ReturnsAsync(new Application.Lib.Azure.AzureAuthParameters
      {
        ClientId = "test",
        Token = "test",
        TenantId = "",
      });

    mocks.MockServiceScopeFactoryService(azureActionsMock.Object);

    var (metascriptInvoker, _) = Helpers.BuildMetascriptInvoker(mocks: mocks,
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var mspTenant = GetOrCreateMspTenant();
    var runContext = mocks.BuildRunContext(tenant: mspTenant);
    var computerMock = runContext.Args.Computer;

    // act
    var script = importAzureAd
      ? "Import-Module AzureAD\nConnect-ImmyAzureAD"
      : "Connect-ImmyAzureAD";
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: script, executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      againstComputer: computerMock);

    // assert
    Assert.Empty(metaScriptResult.ErrorCollection);
    Assert.False(metaScriptResult.HadTerminatingException);
  }

  [Theory]
  [InlineData("docker ps", false)]
  [InlineData("& { docker ps }", false)]
  [InlineData(@"iex -Command ""docker ps""", false)]
  [InlineData(@"icm { docker ps }", false)]
  [InlineData(@". { docker ps }", false)]
  [InlineData("bash -c set", true)]
  [InlineData("& { /bin/bash -c set }", true)]
  [InlineData(@"iex -Command ""/bin/bash -c set""", true)]
  [InlineData(@"icm { /bin/bash -c set }", true)]
  [InlineData(@". { /bin/bash -c set }", true)]
  public async Task RunMetascript_ShouldNotAllowAccessToFileSystem(string script, bool linuxOnly)
  {
    if (OperatingSystem.IsMacOS())
    {
      return;
    }
    if (linuxOnly && !OperatingSystem.IsLinux())
    {
      return;
    }

    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: script, executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.Single(metaScriptResult.ErrorCollection);
    Assert.Contains("Cannot access filesystem",
      metaScriptResult.ErrorCollection[0].ToString());
  }

  [Fact]
  public async Task RunMetascript_ShouldAllowPullingDynamicParameters_FromNestedFunctionScripts()
  {
    if (OperatingSystem.IsMacOS())
    {
      return;
    }
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var testFunctionWithDynamicParameters =
      await File.ReadAllTextAsync(
        "MetascriptTests/Scripts_ShouldHavePSBoundParameters_NotUsingGetCommandParameters/Test-Function.ps1");
    AddFunctionScript("Test-Function",
      testFunctionWithDynamicParameters);

    var testFunctionCallsAnotherFunction =
      await File.ReadAllTextAsync(
        "MetascriptTests/Scripts_ShouldHavePSBoundParameters_NotUsingGetCommandParameters/Get-TestFunctionParameters.ps1");
    AddFunctionScript("Get-TestFunctionParameters",
      testFunctionCallsAnotherFunction);

    var scriptAction = await File.ReadAllTextAsync(
      "MetascriptTests/Scripts_ShouldHavePSBoundParameters_NotUsingGetCommandParameters/Test-CallTestFunction.ps1");
    var script = Helpers.MakeScript(
      action: scriptAction,
      executionContext: ScriptExecutionContext.Metascript,
      parameters: new Dictionary<string, object?> { { "TestParam", "foobar" }, });

    // act

    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      script,
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.Equal("foobar", metaScriptResult.OutputAsObject?.ToString());
  }

  [Fact]
  public async Task RunMetascript_ForEachObject_Parallel_ShouldRunInConstrainedLanguageMode()
  {
    if (OperatingSystem.IsMacOS())
    {
      return;
    }
    var script = "1 | ForEach-Object -Parallel { $ExecutionContext.SessionState.LanguageMode }";

    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<object>(
      false,
      false,
      Helpers.MakeScript(action: script, executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      mocks.BuildRunContext(),
      againstComputer: mocks.RunContext!.Args.Computer);

    // assert
    Assert.Equal(PSLanguageMode.ConstrainedLanguage, metaScriptResult.OutputAsObject);
  }

  [Theory]
  [CombinatorialData]
  public async Task RunMetascript_InvokeImmyCommand_UsingVariables_ShouldProcessCorrectly(bool hasParamBlock)
  {
    // arrange
    var (metascriptInvoker, mocks) = Helpers.BuildMetascriptInvoker(
      ctxFactory: GetSqliteDbContextFactory(),
      globalCtxFactory: GetSqliteSoftwareDbContextFactory());

    var agent = new Mock<IEphemeralAgentSession>();
    agent.SetupGet(a => a.IsConnected).Returns(true);
    Helpers.MockIEphemeralAgentAcquisition(mocks, agent.Object);

    var runContext = mocks.BuildRunContext();
    var args = mocks.MockRunContextArgs(computer: Helpers.MakeComputer());

    var scriptResult = new SerializedPowerShellScriptResult();
    mocks.IScriptInvoker!
      .SetupRunScriptAsync()
      .Callback((IEphemeralAgentSession _, Script script, int _, CancellationToken __, PSTaskDataStreamWriter? writer) =>
      {
        Assert.Contains("param()", script.Action);
        Assert.Contains("$__using_testVar", script.Action);
        writer?.Add(new PSStreamObject(PSStreamObjectType.Warning, "TestInvocationDone"));
      })
      .ReturnsAsync(scriptResult);

    var scriptAction = hasParamBlock
      ? "param($testVar) Invoke-ImmyCommand { Write-Output $using:testVar }"
      : "Invoke-ImmyCommand { Write-Output $using:testVar }";

    // act
    var metaScriptResult = await metascriptInvoker.RunMetascript<string>(
      false,
      false,
      Helpers.MakeScript(action: scriptAction, executionContext: ScriptExecutionContext.Metascript),
      default,
      TimeSpan.FromSeconds(180),
      runContext,
      againstComputer: args.Computer);

    // assert
    Assert.Contains("TestInvocationDone", metaScriptResult.ConsoleText);
    Assert.False(scriptResult.HadErrors);
  }
}
