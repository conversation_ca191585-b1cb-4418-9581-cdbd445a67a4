using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.KeyVaultRepositories;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.ContextTests;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace Immybot.Backend.UnitTests.CommandTests;

public class MergeTenantsCmdTest : ActionsTestBase
{
  [Fact]
  public async Task Run_ShouldMergeAllTenantData()
  {
    var ctx = GetSqliteDbContext();
    var msp = GetOrCreateMspTenant();
    var tenantToDelete = CreateTenant(msp.Id, principalId: "Foobar");
    CreatePerson(tenantToDelete.Id, "<EMAIL>");
    var userToDelete = CreateUser(tenantToDelete.Id);
    var providerLink = GetOrCreateDefaultProviderLink(msp.Id);
    var providerClientToDelete = CreateProviderClient(providerLink.Id, tenantToDelete.Id);

    var computerToDelete = CreateComputer(providerLink.Id,
      providerClientToDelete.ExternalClientId,
      "someid",
      tenantToDelete.Id,
      "some name",
      Guid.NewGuid());
    CreateTargetAssignment(new TargetAssignment
    {
      Target = "foo",
      MaintenanceIdentifier = "foo",
      MaintenanceType = MaintenanceType.ChocolateySoftware,
      TenantId = tenantToDelete.Id
    });
    CreateSession(computerToDelete.Id, tenantId: tenantToDelete.Id);
    CreateBranding(userToDelete, tenantId: tenantToDelete.Id);
    CreateLicense("foo",
      "foo",
      SoftwareType.Chocolatey,
      "foo",
      LicenseType.Key,
      NuGet.Versioning.SemanticVersion.Parse("1.0.0"),
      tenantToDelete.Id);
    CreateMedia(ownerTenantId: tenantToDelete.Id);
    CreatePendingProviderAgent(providerLink.Id, providerClientToDelete.ExternalClientId, "test2");
    CreateSchedule(new Schedule { TenantId = tenantToDelete.Id });
    CreateSmtpConfig(tenantToDelete.Id);

    var loggerMock = new Mock<ILogger<MergeTenantsCmd>>();
    var azTenantAuthDetailsRepoMock = new Mock<IAzureTenantAuthDetailsRepository>();
    var cmd = new MergeTenantsCmd(loggerMock.Object,
      () => GetNewSqliteDbContext(),
      azTenantAuthDetailsRepoMock.Object);

    // act
    var res = await cmd.Run(
      new MergeTenantsPayload(msp.Id, [tenantToDelete.Id]),
      CancellationToken.None);

    var numTenants = await ctx.Tenants.AsNoTracking().CountAsync();
    var computer = await ctx.Computers.AsNoTracking().FirstOrDefaultAsync();
    var branding = await ctx.Brandings.AsNoTracking().FirstOrDefaultAsync();
    var license = await ctx.Licenses.AsNoTracking().FirstOrDefaultAsync();
    var action = await ctx.MaintenanceActions.AsNoTracking().FirstOrDefaultAsync();
    var session = await ctx.MaintenanceSessions.AsNoTracking().FirstOrDefaultAsync();
    var person = await ctx.Persons.AsNoTracking().FirstOrDefaultAsync();
    var client = await ctx.GetProviderClients().FirstOrDefaultAsync();
    var schedule = await ctx.Schedules.AsNoTracking().FirstOrDefaultAsync();
    var smtp = await ctx.SmtpConfigs.AsNoTracking().FirstOrDefaultAsync();
    var assignment = await ctx.TargetAssignments.AsNoTracking().FirstOrDefaultAsync();
    var user = await ctx.Users.AsNoTracking().FirstOrDefaultAsync();

    // assert
    Assert.True(res.Success);
    Assert.Equal(1, numTenants);
    Assert.Equal(msp.Id, computer?.TenantId);
    Assert.Equal(msp.Id, branding?.TenantId);
    Assert.Equal(msp.Id, license?.TenantId);
    Assert.Equal(msp.Id, action?.TenantId);
    Assert.Equal(msp.Id, session?.TenantId);
    Assert.Equal(msp.Id, person?.TenantId);
    Assert.Equal(msp.Id, client?.LinkedToTenantId);
    Assert.Equal(msp.Id, schedule?.TenantId);
    Assert.Equal(msp.Id, smtp?.TenantId);
    Assert.Equal(msp.Id, assignment?.TenantId);
    Assert.Equal(msp.Id, user?.TenantId);
    azTenantAuthDetailsRepoMock
      .Verify(r => r.DeleteAzureTenantAuthDetails(
          tenantToDelete.AzureTenantLink!.AzureTenant!.PrincipalId,
          It.IsAny<CancellationToken>()),
        Times.Once());
  }
}
