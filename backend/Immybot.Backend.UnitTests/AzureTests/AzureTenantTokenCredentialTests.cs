using System;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Azure;
using Immybot.Backend.Application.KeyVaultRepositories;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.UnitTests.Shared.Lib;
using Microsoft.Extensions.Options;
using Moq;

namespace Immybot.Backend.UnitTests.AzureTests;

/// <summary>
/// Tests for both the <see cref="AzureTenantTokenCredential"/> and
/// <see cref="AzureTenantTokenCredentialFactory"/> classes
/// </summary>
public class AzureTenantTokenCredentialTests(ITestOutputHelper helper) : BaseUnitTests(helper, showLogsInTestOutput: false)
{
  [Theory, CombinatorialData]
  public async Task GetTokenAsync_ShouldWorkAsExpected(
    bool asMspTenant,
    AzurePermissionLevel2? tenantPermissionLevel,
    bool hasOboTokenInStore)
  {
    var azAdOpts = new AzureActiveDirectoryAuthOptions { ClientId = "ClientId", ClientSecret = "ClientSecret" };
    var appSettingsOpts = new AppSettingsOptions { PartnerCenterApiUrl = new Uri("https://partnercenterapiurl"), PartnerCenterUserImpersonationScope = "https://partnercenterapiurl/user_impersonation" };
    var tenant = asMspTenant ? GetOrCreateMspTenant() : GetOrCreateCustomerTenant();
    var partnerPrincipalId = asMspTenant
      ? tenant.AzureTenantLink?.AzureTenant?.PrincipalId
      : tenant.AzureTenantLink?.AzureTenant?.PartnerPrincipalId!;

    Assert.NotNull(partnerPrincipalId);
    var tenantAuthDetails = tenantPermissionLevel switch
    {
      AzurePermissionLevel2.CustomAppRegistration => new AzureTenantAuthDetails(partnerPrincipalId, tenantPermissionLevel.Value, "Foo", "Bar"),
      AzurePermissionLevel2.DefaultAppRegistration => new AzureTenantAuthDetails(partnerPrincipalId, tenantPermissionLevel.Value, null, null),
      _ => null // null => no azure permission level selected for this tenant
    };
    var azureTenantAuthDetailsRepository = new Mock<IAzureTenantAuthDetailsRepository>();
    azureTenantAuthDetailsRepository
      .Setup(r => r.GetAzureTenantAuthDetailsByAzurePrincipalId(
        tenant.AzureTenantLink!.AzureTenant!.PartnerPrincipalId ?? tenant.AzureTenantLink.AzureTenant.PrincipalId,
        It.IsAny<CancellationToken>()))
      .ReturnsAsync(tenantAuthDetails);
    var accessTokenStore = new Mock<IOauthAccessTokenStore>();
    accessTokenStore
      .Setup(s => s.GetAccessTokenForScopes(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<string?>(), It.IsAny<CancellationToken>(), It.IsAny<bool>()))
      .ThrowsAsync(new MissingAccessTokenException("No access token found"));
    if (hasOboTokenInStore)
    {
      accessTokenStore
        .Setup(s => s.GetAccessTokenForScopes(It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<string?>(), It.IsAny<CancellationToken>(), It.IsAny<bool>()))
        .ReturnsAsync(new AuthToken("Foobar", DateTimeOffset.UtcNow.Add(TimeSpan.FromDays(1)), azAdOpts.ClientId));
    }
    else
    {
      accessTokenStore
        .Setup(s => s.GenerateAccessTokenFromRefreshTokenForScopes(
          It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string[]>(), It.IsAny<string[]>(), It.IsAny<string?>(),
          It.IsAny<string?>(), It.IsAny<CancellationToken>()))
        .ThrowsAsync((new MissingAccessTokenException("No access token found")));
    }
    var credentialFactory = new AzureTenantTokenCredentialFactory(accessTokenStore.Object, azureTenantAuthDetailsRepository.Object, Options.Create(azAdOpts), Options.Create(appSettingsOpts));

    Assert.NotNull(tenant.AzureTenantLink?.AzureTenant);
    var credential = await credentialFactory.GetImmyTokenCredential(tenant.AzureTenantLink.AzureTenant, default);
    var record = await Record.ExceptionAsync(async () =>
    {
      await credential.GetTokenAsync(new(["https://graph.microsoft.com/.default"]), default);
    });
    if (hasOboTokenInStore)
    {
      // if there's an obo token for the specified scopes, that overrides everything else
      Assert.Null(record);
      Assert.Equal(AccessTokenSource.Obo, credential.GotAccessTokenFrom);
    }
    else switch (tenantPermissionLevel)
    {
      case null:
        // if no obo token and partner tenant has no permission level, then it should fail
        Assert.IsType<MissingAccessTokenException>(record);
        Assert.Null(credential.GotAccessTokenFrom);
        break;
      case AzurePermissionLevel2.DefaultAppRegistration:
        // if no obo token and partner tenant has a default permission level, then it
        // should try to reach out to Azure with the default app registration

        // NB: Getting the token fails with AzureAuthenticationException here because the
        // test tenant doesn't actually exist in azure
        Assert.IsNotType<MissingAccessTokenException>(record);
        Assert.Equal(AccessTokenSource.DefaultAppRegistration, credential.GotAccessTokenFrom);
        break;
      case AzurePermissionLevel2.CustomAppRegistration:
        // if no obo token and partner tenant has a custom permission level, then it
        // should try to reach out to Azure with the custom app registration

        // NB: Getting the token fails with AzureAuthenticationException here because the
        // test tenant doesn't actually exist in azure
        Assert.IsNotType<MissingAccessTokenException>(record);
        Assert.Equal(AccessTokenSource.CustomAppRegistration, credential.GotAccessTokenFrom);
        break;
      default:
        throw new ArgumentOutOfRangeException(nameof(tenantPermissionLevel), tenantPermissionLevel, null);
    }
  }
}
