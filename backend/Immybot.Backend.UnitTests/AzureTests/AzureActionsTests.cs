using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.UnitTests.ContextTests;
using Moq;
using User = Microsoft.Graph.Models.User;

namespace Immybot.Backend.UnitTests.AzureTests;

public class AzureActionsTests(ITestOutputHelper helper)
  : ActionsTestBase(helper, showLogsInTestOutput: false)
{
  [Fact]
  public async Task
    SyncUsersFromAzureTenant_ShouldUseAzureTenantTokenCredentialAndUpdateTenantSyncDetails()
  {
    var mspTenant = GetOrCreateMspTenant();
    var azTenant = mspTenant.AzureTenantLink!.AzureTenant;
    var graphMock = new Mock<IGraphApi>();
    var accessTokenStore = Mock.Of<IOauthAccessTokenStore>();
    Assert.NotNull(azTenant);
    var credential = new AzureTenantTokenCredential(azTenant.PrincipalId,
      null,
      accessTokenStore,
      new AzureActiveDirectoryAuthOptions(),
      new AppSettingsOptions());
    var credentialFactory = new Mock<IAzureTenantTokenCredentialFactory>();
    credentialFactory
      .Setup(f => f.GetImmyTokenCredential(azTenant, It.IsAny<CancellationToken>(), false))
      .ReturnsAsync(credential);
    graphMock.Setup(g => g.GetAllUsers(credential, default, false))
      .ReturnsAsync([
        new User() { UserPrincipalName = "<EMAIL>" },
        new User() { UserPrincipalName = "<EMAIL>" },
        new User() { UserPrincipalName = "<EMAIL>" },
      ])
      .Verifiable();

    var azureActions = GetAzureActions(credentialFactory: credentialFactory.Object,
      graphApi: graphMock.Object);

    // Sanity check: synced date is null beforehand
    Assert.Null(mspTenant.AzureTenantLink.AzureTenant?.LastGetUsersSyncResult?.AttemptDateUtc);

    var result = await azureActions.SyncUsersFromAzureTenant(azTenant, default);

    Assert.NotNull(result);
    Assert.True(result.WasSuccessful);

    // Assert that the factory is used to get the cred
    credentialFactory.Verify(f => f.GetImmyTokenCredential(azTenant, default, false),
      Times.Once);

    // Assert that the cred is passed to the graph api
    graphMock.Verify(g => g.GetAllUsers(credential, default, false), Times.Once);

    // Assert that tenant's sync-users-details are updated
    Assert.NotNull(mspTenant.AzureTenantLink.AzureTenant?.LastGetUsersSyncResult?.AttemptDateUtc);
    var t = await GetNewSqliteDbContext().AzureTenants.FindAsync(azTenant.PrincipalId);
    Assert.NotNull(t?.LastGetUsersSyncResult?.AttemptDateUtc);
  }

  [Fact]
  public async Task
    SyncUsersFromAzureTenant_ShouldSyncUsersFromDomainsSpecifiedOnAzureTenantLink_AndExcessIntoMainTenant()
  {
    var customerTenant = GetOrCreateCustomerTenant();
    var customerTenantSplit = CreateTenant(principalId: customerTenant.AzureTenantLink!.AzTenantId,
      partnerPrincipalId: customerTenant.AzureTenantLink.AzureTenant?.PartnerPrincipalId,
      limitToDomains: ["example.com"]);
    var azTenant = customerTenantSplit.AzureTenantLink!.AzureTenant;
    var accessTokenStore = Mock.Of<IOauthAccessTokenStore>();
    Assert.NotNull(azTenant);
    var credential = new AzureTenantTokenCredential(azTenant.PrincipalId,
      null,
      accessTokenStore,
      new AzureActiveDirectoryAuthOptions(),
      new AppSettingsOptions());
    var credentialFactory = new Mock<IAzureTenantTokenCredentialFactory>();
    credentialFactory
      .Setup(f => f.GetImmyTokenCredential(azTenant, It.IsAny<CancellationToken>(), false))
      .ReturnsAsync(credential);
    var graphMock = new Mock<IGraphApi>();
    graphMock.Setup(g => g.GetAllUsers(credential, default, false))
      .ReturnsAsync([
        new User() { UserPrincipalName = "<EMAIL>" },
        new User() { UserPrincipalName = "<EMAIL>" },
        new User() { UserPrincipalName = "<EMAIL>" },
      ])
      .Verifiable();

    var syncPersonsCmdMock = new Mock<ISyncAzureUsersAndImmyPersonsCmd>();
    syncPersonsCmdMock.Setup(c => c.Run(It.IsAny<Tenant>(), It.IsAny<List<User>>(), default))
      .ReturnsAsync((Tenant _, List<User> users, CancellationToken _) => users.Count)
      .Verifiable();

    var azureActions = GetAzureActions(credentialFactory: credentialFactory.Object,
      graphApi: graphMock.Object,
      syncPersonsCmd: syncPersonsCmdMock.Object);

    // Sanity check: synced date is null beforehand
    Assert.Null(customerTenantSplit.AzureTenantLink?.AzureTenant?.LastGetUsersSyncResult
      ?.AttemptDateUtc);

    var result = await azureActions.SyncUsersFromAzureTenant(azTenant, default);

    Assert.NotNull(result);
    Assert.True(result.WasSuccessful);

    Assert.Equal(1, result.ImmyTenantsSyncedUsers[customerTenant.Id]);
    Assert.Equal(2, result.ImmyTenantsSyncedUsers[customerTenantSplit.Id]);

    // Assert that the factory is used to get the cred
    credentialFactory.Verify(f => f.GetImmyTokenCredential(azTenant, default, false),
      Times.Once);

    // Assert that the cred is passed to the graph api
    graphMock.Verify();
    syncPersonsCmdMock.Verify();

    // Assert that tenant's sync-users-details are updated
    Assert.NotNull(customerTenantSplit.AzureTenantLink?.AzureTenant?.LastGetUsersSyncResult
      ?.AttemptDateUtc);
    var t = await GetNewSqliteDbContext().AzureTenants.FindAsync(azTenant.PrincipalId);
    Assert.NotNull(t?.LastGetUsersSyncResult?.AttemptDateUtc);
  }
}
