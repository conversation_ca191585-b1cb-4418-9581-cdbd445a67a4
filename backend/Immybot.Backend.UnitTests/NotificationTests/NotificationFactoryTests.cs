using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.Helpers;
using Immybot.Backend.Application.Notifications;
using Immybot.Backend.Application.Notifications.Converters;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.Shared.Lib;
using Microsoft.Extensions.DependencyInjection;
using Moq;

namespace Immybot.Backend.UnitTests.NotificationTests;

public record TestEvent(string Foobar) : ApplicationEvent;
public record TestEvent2(string Foobar) : ApplicationEvent;

public class TestEventConverter : IApplicationEventToNotificationConverter<TestEvent>
{
  public Task<ICollection<Notification>?> Convert(
    TestEvent[] applicationEvents)
  {
    return Task.FromResult<ICollection<Notification>?>(
      applicationEvents.Select(e => new Notification
      {
        Id = Guid.NewGuid(),
        Description = e.Foobar,
        Title = e.Foobar,
        UpdatedDate = e.DateUTC,
        ObjectId = e.Foobar
      }).ToArray());
  }
}

public class TestEventConverter2 : IApplicationEventToNotificationConverter<TestEvent>
{
  public Task<ICollection<Notification>?> Convert(
    TestEvent[] applicationEvents)
  {
    return Task.FromResult<ICollection<Notification>?>(
      applicationEvents.Select(e => new Notification
      {
        Id = Guid.NewGuid(),
        Description = e.Foobar + "2",
        Title = e.Foobar + "2",
        UpdatedDate = e.DateUTC,
        ObjectId = e.Foobar
      }).ToArray());
  }
}

[NotificationEventBatching(MaxBatchSize: 100, ThrottleSeconds: 1, Behavior: TplHelpers.BatchAutoTriggerBehavior.TimeoutSinceLastItem)]
public class TestEventConverter3 : IApplicationEventToNotificationConverter<TestEvent>
{
  public Task<ICollection<Notification>?> Convert(
    TestEvent[] applicationEvents)
  {
    return Task.FromResult<ICollection<Notification>?>(
      applicationEvents.Select(e => new Notification
      {
        Id = Guid.NewGuid(),
        Description = e.Foobar + "3",
        Title = e.Foobar + "3",
        UpdatedDate = e.DateUTC,
        ObjectId = e.Foobar
      }).ToArray());
  }
}

[NotificationEventBatching(MaxBatchSize: 100, ThrottleSeconds: 0.3, Behavior: TplHelpers.BatchAutoTriggerBehavior.TimeoutSinceLastItem)]
public class TestEventConverter4 : IApplicationEventToNotificationConverter<TestEvent2>
{
  public Task<ICollection<Notification>?> Convert(
    TestEvent2[] applicationEvents)
  {
    return Task.FromResult<ICollection<Notification>?>(
      applicationEvents.Select(e => new Notification
      {
        Id = Guid.NewGuid(),
        Description = e.Foobar + "4",
        Title = e.Foobar + "4",
        UpdatedDate = e.DateUTC,
        ObjectId = e.Foobar
      }).ToArray());
  }
}

public class NotificationFactoryTests : BaseUnitTests
{
  private (NotificationFactory, Mock<IDomainEventEmitter>, NotificationsCache) BuildNotificationFactory(
    (Type EventType, Type ConverterType)[] converters)
  {
    var serviceCollection = new ServiceCollection();

    // add a notification builder
    foreach (var (EventType, ConverterType) in converters)
    {
      serviceCollection.AddTransient(ConverterType);
      serviceCollection.AddTransient(typeof(IApplicationEventToNotificationConverter<>).MakeGenericType(EventType), ConverterType);
    }

    // build service provider
    var serviceProvider = serviceCollection.BuildServiceProvider();

    var eventEmitter = new Mock<IDomainEventEmitter>();
    var appLifetime = Helpers.MockHostApplicationLifetime();
    var cache = new NotificationsCache(appLifetime.Object);

    // instantiate a notification factory
    var factory = new NotificationFactory(
      serviceProvider,
      eventEmitter.Object,
      cache,
      _loggerFactory.CreateLogger<NotificationFactory>());

    return (factory, eventEmitter, cache);
  }

  [Fact]
  public async Task NotificationFactory_ShouldNotFail_ForApplicationEventTypeWithNoRegisteredConverters()
  {
    // arrange
    ApplicationEvent testEvent = new TestEvent("foobar");

    var (notificationFactory, emitter, _) = BuildNotificationFactory(Array.Empty<(Type, Type)>());

    // act
    await notificationFactory.CreateNotificationsFromApplicationEvent(testEvent, default);
    await Task.Delay(50);

    // assert
    Assert.Empty(emitter.Invocations);
  }

  [Fact]
  public async Task NotificationFactory_ShouldCreateNotification_ForSpecifiedApplicationEvent()
  {
    // arrange
    ApplicationEvent testEvent = new TestEvent("foobar");

    var (notificationFactory, emitter, _) = BuildNotificationFactory(new[]
    {
      (typeof(TestEvent), typeof(TestEventConverter)),
    });

    // act
    await notificationFactory.CreateNotificationsFromApplicationEvent(testEvent, default);
    await Task.Delay(50);

    // assert
    Assert.Single(emitter.Invocations);
  }

  [Fact]
  public async Task NotificationFactory_ShouldCreateNotification_ForSpecifiedApplicationEvent_WhenTypeIsNarrowed()
  {
    // arrange
    TestEvent testEvent = new TestEvent("foobar");

    var (notificationFactory, emitter, _) = BuildNotificationFactory(new[] { (typeof(TestEvent), typeof(TestEventConverter)) });

    // act
    await notificationFactory.CreateNotificationsFromApplicationEvent(testEvent, default);
    await Task.Delay(50);

    // assert
    Assert.Single(emitter.Invocations);
  }

  [Fact]
  public async Task NotificationFactory_ShouldCreateMultipleNotifications_WhenMultipleRegisteredConverters()
  {
    // arrange
    ApplicationEvent testEvent = new TestEvent("foobar");

    _ = new List<Notification>();

    var (notificationFactory, emitter, _) = BuildNotificationFactory(new[]
    {
      (typeof(TestEvent), typeof(TestEventConverter)),
      (typeof(TestEvent), typeof(TestEventConverter2)), // this converter adds a '2' to the description
    });

    // act
    await notificationFactory.CreateNotificationsFromApplicationEvent(testEvent, default);
    await Task.Delay(50);

    // assert
    Assert.Equal(2, emitter.Invocations.Count);
    Assert.Contains("foobar",
      emitter.Invocations.Select(n => n.Arguments[0] as NotificationEvent).Select(n => n?.Notification.Description));
    Assert.Contains("foobar2",
      emitter.Invocations.Select(n => n.Arguments[0] as NotificationEvent).Select(n => n?.Notification.Description));
  }

  [Theory]
  [CombinatorialData]
  public async Task NotificationFactory_ShouldBatchNotifications_WhenConverterSpecifiesNotificationBatchingAttribute(
    [CombinatorialValues(1, 100)] int numEvents)
  {
    // arrange
    var testEvents = new List<ApplicationEvent>();

    // create events
    for (int i = 0; i < numEvents; i++)
    {
      testEvents.Add(new TestEvent($"foobar{i}"));
    }

    var notificationEvents = new ConcurrentQueue<NotificationEvent>();

    var (notificationFactory, emitter, _) = BuildNotificationFactory(new[]
    {
      (typeof(TestEvent), typeof(TestEventConverter)),
      (typeof(TestEvent), typeof(TestEventConverter3)), // this converter has a batching attribute of 1 second
    });


    var numEventsx1Received = new ManualResetEventSlim(false);
    var numEventsx2Received = new ManualResetEventSlim(false);

    emitter
      .Setup(r => r.EmitEvent(It.IsAny<NotificationEvent>()))
      .Callback((NotificationEvent ev) =>
      {
        notificationEvents.Enqueue(ev);
        if (notificationEvents.Count >= numEvents)
        {
          numEventsx1Received.Set();
        }
        if (notificationEvents.Count == numEvents * 2)
        {
          numEventsx2Received.Set();
        }
      });


    // act
    foreach (var testEvent in testEvents)
    {
      await notificationFactory.CreateNotificationsFromApplicationEvent(testEvent, default);
    }

    var waitResult = numEventsx1Received.Wait(TimeSpan.FromSeconds(5));
    Assert.True(waitResult, "Timed out waiting for x1 notifications to be sent.");

    // assert
    if (numEvents == 100)
    {
      waitResult = numEventsx2Received.Wait(TimeSpan.FromSeconds(5));
      Assert.True(waitResult, "Timed out waiting for x2 notifications to be sent.");

      // equals batch size, should emit them all immediately from both converters
      Assert.Equal(numEvents * 2, emitter.Invocations.Count);
    }
    else
    {
      Assert.Equal(numEvents, emitter.Invocations.Count);

      waitResult = numEventsx2Received.Wait(TimeSpan.FromSeconds(5));
      Assert.True(waitResult, "Timed out waiting for x2 notifications to be sent.");
      // after batch timeout, should emit from the second converter
      Assert.Equal(numEvents * 2, emitter.Invocations.Count);
    }
  }
  [Theory]
  [CombinatorialData]
  public async Task ShouldTakeLaterNotifications_WhenMultipleNotificationsOfSameTypeAreCached(
    bool firstEventConvertsFirst,
    bool firstEventFiresFirst)
  {
    // arrange
    GetNewSqliteDbContext();
    Func<NotificationEvent, Task>? cb = null;
    var logger = _loggerFactory.CreateLogger<NotificationSaverService>();
    var ctxFactory = GetSqliteDbContextFactory();
    var domainEventReceiver = new Mock<IDomainEventReceiver>();
    var savedNotifications = new List<NotificationSavedEvent>();
    var notificationEvents = new ConcurrentQueue<NotificationEvent>();

    var (notificationFactory, emitter, cache) = BuildNotificationFactory(new[]
    {
      (typeof(TestEvent), typeof(TestEventConverter)),
      (typeof(TestEvent2), typeof(TestEventConverter4)), // this converter has a batching attribute of 1 second
    });

    var notificationsSaved = new ManualResetEventSlim(false);

    emitter
      .Setup(r => r.EmitEvent(It.IsAny<NotificationSavedEvent>()))
      .Callback((NotificationSavedEvent ev) =>
      {
        savedNotifications.Add(ev);
        // This allows us to wait for the precise amount of time needed,
        // instead of waiting for an arbitrary amount of time with Task.Delay.
        notificationsSaved.Set();
      });

    domainEventReceiver
      .Setup(r => r.Subscribe(It.IsAny<Func<NotificationEvent, Task>>()))
      .Callback((Func<NotificationEvent, Task> _cb) => cb = _cb);

    emitter
      .Setup(r => r.EmitEvent(It.IsAny<NotificationEvent>()))
      .Callback((NotificationEvent ev) =>
      {
        // The subscriber above is the NotificationSaverService.  When these events
        // are received, it sends them to a TPL batching block.  The batching is
        // sensitive to timing, with the timeout being set below on the
        // NotificationsBatchTimeoutSeconds field.
        //
        // We know 2 events will be emitted, so we'll queue them here and invoke
        // the callback for each when both are received.  This makes the batching
        // less time-sensitive when the build server is being slow.
        notificationEvents.Enqueue(ev);
        if (notificationEvents.Count == 2)
        {
          while (notificationEvents.TryDequeue(out var notificationEvent))
          {
            cb?.Invoke(notificationEvent).Forget();
          }
        }
      });

    var notificationSaverService = new NotificationSaverService(
      logger,
      domainEventReceiver.Object,
      emitter.Object,
      ctxFactory,
      Helpers.MockMetrics(new()),
      cache);

    // Overwrite this from 5 to 1 for faster tests
    notificationSaverService.NotificationsBatchTimeoutSeconds = TimeSpan.FromSeconds(.5);

    await notificationSaverService.StartAsync(default);

    // act
    ApplicationEvent ev1;
    ApplicationEvent ev2;

    if (firstEventConvertsFirst)
    {
      // event type 1 is created first
      ev1 = new TestEvent("foobar");

      await Task.Delay(5);

      // event type 2 is created last
      ev2 = new TestEvent2("foobar");
    }
    else
    {
      // event type 2 is created first
      ev1 = new TestEvent2("foobar");

      await Task.Delay(5);

      // event type 1 is created last
      ev2 = new TestEvent("foobar");
    }

    if (firstEventFiresFirst)
    {
      // fire one event - this should get cached
      await notificationFactory.CreateNotificationsFromApplicationEvent(ev1, default);

      // fire another event - this should replace the cached event because its date is later
      await notificationFactory.CreateNotificationsFromApplicationEvent(ev2, default);
    }
    else
    {
      // fire one event - this should get cached
      await notificationFactory.CreateNotificationsFromApplicationEvent(ev2, default);

      // fire another event - this should replace the cached event because its date is later
      await notificationFactory.CreateNotificationsFromApplicationEvent(ev1, default);
    }

    var waitResult = notificationsSaved.Wait(TimeSpan.FromSeconds(5));
    Assert.True(waitResult, "Timed out waiting for notifications to be saved.");

    // assert
    Assert.Single(savedNotifications);

    // Should always end up with one saved notification, being the later notification
    Assert.Equal(ev2.DateUTC, savedNotifications[0].Notification.UpdatedDate);

    await using var ctx = ctxFactory();
    var dbNotifications = ctx.Notifications.Count();
    Assert.Equal(1, dbNotifications);

    emitter.Verify(r => r.EmitEvent(It.IsAny<NotificationSavedEvent>()), Times.Once);
    emitter.Verify(r => r.EmitEvent(It.IsAny<NotificationEvent>()), Times.Exactly(2));
    emitter.VerifyNoOtherCalls();

    await notificationSaverService.StopAsync(default);
  }
}
