using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.DynamicProviders;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Lib.MetaScripts.Attributes;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Persistence;
using Immybot.Backend.UnitTests.ContextTests;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using JsonSerializer = System.Text.Json.JsonSerializer;
using ServiceCollection = Microsoft.Extensions.DependencyInjection.ServiceCollection;

namespace Immybot.Backend.UnitTests.DynamicProviderTests;

public class RecommendedProviderLinksGetterTests : ActionsTestBase
{
  private static Dictionary<string, object?> BuildParamSet(string uri, object key)
  {
    var paramSet = new Dictionary<string, object?>()
    {
      { "Uri", uri },
      { "Key", key }
    };
    return paramSet;
  }

  // This sets up everything required to actually run ConvertTargetAssignmentParamsToBindableDictionary()
  private async Task<(IRecommendedProviderLinksGetter, IActionRunContext, MockedServices)> SetupConvertTargetAssignmentParamsToBindableDictionary(
    Func<ImmybotDbContext> ctxFactory,
    Func<SoftwareDbContext> sftCtxFactory,
    DynamicIntegrationType dynamicIntegrationType,
    MockedServices? mocks = null)
  {
    mocks ??= new MockedServices();
    var serviceScopeFactoryMock = new Mock<IServiceScopeFactory> { Name = "ServiceScopeFactoryMock" };

    (IMetascriptInvoker metascriptInvoker, _) = Helpers.BuildMetascriptInvoker(
      mocks,
      ctxFactory,
      sftCtxFactory,
      serviceScopeFactoryMock.Object);

    (IDynamicFormService dynamicFormService, var actionRunContext, _) = Helpers.BuildDynamicFormService(
      mocks,
      ctxFactory,
      sftCtxFactory,
      metascriptInvoker);

    var (dynamicProviderInterceptorFactory, _) = Helpers.BuildDynamicProviderInterceptorFactory(
      mocks,
      ctxFactory,
      sftCtxFactory,
      metascriptInvoker,
      new Mock<IDomainEventEmitter>().Object,
      dynamicFormService);

    var (dynamicProviderRegistration, _) = Helpers.BuildDynamicProviderRegistrationFactory(
           ctxFactory,
           sftCtxFactory,
           mocks,
           metascriptInvoker,
           providerLinkActions: null,
           dynamicProviderInterceptorFactory);

    var (providerMetadataFactory, _) = Helpers.BuildProviderMetadataFactory(ctxFactory, sftCtxFactory, mocks, dynamicFormService);

    ServiceCollection serviceCollection = [];
    serviceCollection.AddSingleton(ctxFactory);
    serviceCollection.AddSingleton(sftCtxFactory);
    serviceCollection.AddTransient(_ => dynamicProviderRegistration);
    serviceCollection.AddTransient(_ => metascriptInvoker);
    serviceCollection.AddTransient(_ => new Mock<IDomainEventEmitter>().Object);
    serviceCollection.AddTransient(_ => dynamicProviderInterceptorFactory);
    serviceCollection.AddTransient(_ => dynamicFormService);
    serviceCollection.AddTransient(_ => providerMetadataFactory);
    serviceCollection.AddTransient(_ => new Mock<IDynamicIntegrationsGlobalStore>().Object);
    serviceCollection.AddTransient(_ => actionRunContext);
    serviceCollection.AddTransient(_ => new Mock<IRunContextFactory>().Object);

    ServiceProvider serviceProvider = serviceCollection.BuildServiceProvider();

    serviceScopeFactoryMock.Setup(a => a.CreateScope()).Returns(() => serviceProvider.CreateScope());
    var serviceScopeMock = new Mock<IServiceScope> { Name = "ServiceScopeMock" };
    serviceScopeMock.Setup(a => a.ServiceProvider).Returns(() => serviceProvider);
    serviceScopeMock.Setup(a => a.ServiceProvider.GetService(typeof(IDynamicProviderRegistrationFactory))).Returns(() => dynamicProviderRegistration);

    var (providerRegistrationService, _) = Helpers.BuildProviderRegistrationService(mocks, TimeProvider.System, serviceScopeFactoryMock.Object);

    var (providerActions, _) = Helpers.BuildProviderActions(
                                           logger: Helpers.MockLoggerFactory().CreateLogger<ProviderActions>(),
                                           immybotDbContextFactory: ctxFactory,
                                           softwareDbContextFactory: sftCtxFactory,
                                           providerRegistrationService: providerRegistrationService,
                                           dynamicFormService: dynamicFormService,
                                           serviceScopeFactory: serviceScopeFactoryMock.Object);

    var reg = await providerRegistrationService.CreateDynamicProviderRegistration(dynamicIntegrationType, CancellationToken.None);
    Assert.NotNull(reg);

    await providerRegistrationService.AddProviderRegistration(reg, CancellationToken.None);

    var providerLinksGetter = Helpers.BuildRecommendedProviderLinksGetter(ctxFactory, sftCtxFactory, dynamicFormService, providerRegistrationService, providerActions);

    return (providerLinksGetter, actionRunContext, mocks);
  }

  [Theory]
  [InlineData(true)]
  [InlineData(false)]
  public void GetMigratableTargetAssignments_ShouldReturnBetaIntegrationsWhenEnabled(bool enableBetaMigrations)
  {
    // Arrange
    Func<ImmybotDbContext> ctxFactory = GetSqliteDbContextFactory();
    Func<SoftwareDbContext> sftCtxFactory = GetSqliteSoftwareDbContextFactory();

    var providerLinksGetter = Helpers.BuildRecommendedProviderLinksGetter(ctxFactory, sftCtxFactory);

    // Enable beta migrations
    var ctx = ctxFactory();
    // Act
    ctx.ApplicationPreferences.Add(new ApplicationPreferences { EnableBetaDynamicIntegrationMigrations = enableBetaMigrations });
    ctx.SaveChanges();

    CreateGlobalMedia();
    var integrationScript = Helpers.MakeDynamicIntegrationScript();
    DynamicIntegrationType dynamicIntegrationType = CreateGlobalDynamicIntegrationType(integrationScript, 1, Guid.NewGuid(), IntegrationTag.Beta);
    var s = CreateGlobalSoftware(agentIntegrationTypeId: dynamicIntegrationType.IntegrationTypeId);

    var taParams = new Dictionary<string, DeploymentParameterValue>() {
          { "UnknownParam", new DeploymentParameterValue(JsonSerializer.SerializeToElement("WOAH")) },
        }.ToImmutableDictionary();


    CreateTargetAssignment(new TargetAssignment()
    {
      MaintenanceIdentifier = s.Identifier,
      MaintenanceType = MaintenanceType.GlobalSoftware,
      ProviderLinkIdForMaintenanceItem = dynamicIntegrationType.Id,
      TaskParameterValues = taParams!
    });

    var recommendedProviderLinks = providerLinksGetter.GetMigratableTargetAssignments();

    // Assert
    Assert.Equal(enableBetaMigrations, recommendedProviderLinks.Count != 0);
  }

  [Theory]
  [ClassData(typeof(AllPossibleTypes))]
  public async Task
    ConvertTargetAssignmentParamsToBindableDictionary_ShouldCorrectlyConvertAllPossibleTypes(
      string paramType,
      string script,
      object param,
      string expectedOutput)
  {
    // Arrange
    Func<ImmybotDbContext> ctxFactory = GetSqliteDbContextFactory();
    Func<SoftwareDbContext> sftCtxFactory = GetSqliteSoftwareDbContextFactory();
    var mocks = new MockedServices();

    if (paramType is "Person")
    {
      var tenant = GetOrCreateMspTenant();
      GetOrCreateDefaultUser(tenantId: tenant.Id);
      var person = CreatePerson(tenantId: tenant.Id,
        "<EMAIL>",
        "foo",
        "bar",
        "testprincipalid");
      param = person.Id;
      mocks.IPersonActions = new();
      mocks.IPersonActions
        .Setup(a => a.GetPersonById(person.Id, It.IsAny<CancellationToken>()))
        .ReturnsAsync(person);
    }


    CreateGlobalMedia(1);
    DynamicIntegrationType dynamicIntegrationType = CreateGlobalDynamicIntegrationType(script, 1, Guid.NewGuid());
    CreateGlobalSoftware(id: 0, agentIntegrationTypeId: dynamicIntegrationType.IntegrationTypeId);

    var targetAssignments = Helpers.BuildBaseParamSetInfo(dynamicIntegrationType, param);

    var (recommendedProviderLinksGetter, runContext, _) = await SetupConvertTargetAssignmentParamsToBindableDictionary(
      ctxFactory,
      sftCtxFactory,
      dynamicIntegrationType,
      mocks: mocks);



    var convertedTargetAssignments = await recommendedProviderLinksGetter.ConvertTargetAssignmentParamsToBindableDictionary(
      targetAssignments,
      runContext,
      CancellationToken.None);

    var convertedResult = convertedTargetAssignments[0].Params["TestParam"];
    // Assert
    switch (paramType)
    {
      case "String[]":
        var stringArray = convertedResult as string[];
        Assert.Equal(expected: expectedOutput, stringArray?[0]);
        break;

      case "Int32[]":
        var intArray = convertedResult as int[];
        Assert.Equal(param.GetType(), intArray?.GetType());
        Assert.Equal(expected: expectedOutput, intArray?[0].ToString());
        break;

      case "Person":
        var person = convertedResult as PersonDropdownValue;
        Assert.NotNull(@object: person);
        Assert.Equal(expected: param, actual: person.Id);
        break;

      case "Media":
        var media = convertedResult as MediaIdentifier;
        var paramTyped = param as MediaIdentifier;
        Assert.NotNull(@object: media);
        Assert.Equal(expected: paramTyped?.Id, actual: media.Id);
        Assert.Equal(expected: paramTyped?.DatabaseType, actual: media.DatabaseType);
        break;

      case "oauthconsent":
        var oauthConsent = convertedResult as OauthConsentParameterValue;
        Assert.Equal(expectedOutput, oauthConsent?.AccessToken);
        break;

      case "Hashtable":
        var hashTable = convertedResult as Hashtable;
        Assert.Equal(param.GetType(), hashTable?.GetType());
        var actualOutput = hashTable?["key"];
        Assert.Equal(expectedOutput, actualOutput);
        break;

      default:
        Assert.Equal(param.GetType(), convertedResult?.GetType());
        Assert.Equal(expectedOutput.ToLower(), convertedResult?.ToString()?.ToLower());
        break;
    }
  }

  [Fact]
  public void GroupUniqueParamSets_ShouldReturnGroupedParamSets()
  {
    // Arrange
    Func<ImmybotDbContext> ctxFactory = GetSqliteDbContextFactory();
    Func<SoftwareDbContext> sftCtxFactory = GetSqliteSoftwareDbContextFactory();

    var providerLinksGetter = Helpers.BuildRecommendedProviderLinksGetter(
      ctxFactory,
      sftCtxFactory,
      new Mock<IDynamicFormService>().Object,
      new Mock<IProviderRegistrationService>().Object,
      new Mock<IProviderActions>().Object);

    // Create ParamSets
    List<ParamSet> paramSets =
    [
      new ParamSet()
      {
        Id = 1,
        Params = BuildParamSet("https://test123.com/", JsonSerializer.SerializeToElement("IAMAAPIKEYKEYKEYKEYKEY")),
        IntegrationType = new DynamicIntegrationType { Name = "" },
        SoftwareName = ""
      },
      new ParamSet()
      {
        Id = 2,
        Params = BuildParamSet("https://test123.com/", JsonSerializer.SerializeToElement("IAMAAPIKEYKEYKEYKEYKEY")),
        IntegrationType = new DynamicIntegrationType { Name = "" },
        SoftwareName = ""
      },
    ];

    // Act
    var groupedProviders = providerLinksGetter.GroupUniqueParamSets(paramSets);

    // Assert
    Assert.Single(groupedProviders);
  }

  [Fact]
  public void GroupUniqueParamSets_ShouldNotGroupDifferentCases()
  {
    // Arrange
    Func<ImmybotDbContext> ctxFactory = GetSqliteDbContextFactory();
    Func<SoftwareDbContext> sftCtxFactory = GetSqliteSoftwareDbContextFactory();

    var providerLinksGetter = Helpers.BuildRecommendedProviderLinksGetter(
      ctxFactory,
      sftCtxFactory,
      new Mock<IDynamicFormService>().Object,
      new Mock<IProviderRegistrationService>().Object,
      new Mock<IProviderActions>().Object);

    // Create ParamSets
    List<ParamSet> paramSets =
    [
      new ParamSet()
      {
        Id = 1,
        Params = BuildParamSet("https://google.com", "google123"),
        IntegrationType = new DynamicIntegrationType { Name = "" },
        SoftwareName = ""
      },
      new ParamSet()
      {
        Id = 2,
        Params = BuildParamSet("HTTPS://GOOGLE.COM", "GOOGLE123"),
        IntegrationType = new DynamicIntegrationType { Name = "" },
        SoftwareName = ""
      },
      new ParamSet()
      {
        Id = 3,
        Params = BuildParamSet("HtTpS://GoOgLe.CoM", "gOoGlE123"),
        IntegrationType = new DynamicIntegrationType { Name = "" },
        SoftwareName = ""
      },
      new ParamSet()
      {
        Id = 4,
        Params = BuildParamSet("hTtPs://gOoGlE.cOm", "GoOgLe123"),
        IntegrationType = new DynamicIntegrationType { Name = "" },
        SoftwareName = ""
      }
    ];

    // Act
    var groupedProviders = providerLinksGetter.GroupUniqueParamSets(paramSets);

    // Assert
    Assert.Equal(4, groupedProviders.Count);
  }

  [Fact]
  public async Task TestParamSets_ShouldNotMigrateInvalidParamSets()
  {
    // Arrange
    Func<ImmybotDbContext> ctxFactory = GetSqliteDbContextFactory();
    Func<SoftwareDbContext> sftCtxFactory = GetSqliteSoftwareDbContextFactory();

    CreateGlobalMedia(1);
    var integrationScript = Helpers.MakeDynamicIntegrationScript();
    DynamicIntegrationType dynamicIntegrationType = CreateGlobalDynamicIntegrationType(integrationScript, 1, Guid.NewGuid(), IntegrationTag.Beta);

    var (providerLinksGetter, _, _) = await SetupConvertTargetAssignmentParamsToBindableDictionary(
         ctxFactory,
         sftCtxFactory,
         dynamicIntegrationType);

    List<GroupedParamSet> ParamsSets = new()
    {
      new GroupedParamSet()
      {
        Ids = [1],
        // Act
        // ApiUriF is a bad param. "TestParam" is the correct one
        ParamSet = new Dictionary<string, object?> { { "ApiUriF", "https://immense.net" } },
        IntegrationType = dynamicIntegrationType,
        IntegrationTypeId = dynamicIntegrationType.IntegrationTypeId,
        SoftwareName = "Test Software"
      }
    };

    var goodParamSets = await providerLinksGetter.TestParamSets(ParamsSets, CancellationToken.None);

    // Assert
    Assert.Empty(goodParamSets);
  }

  [Fact]
  public async Task TestParamSets_ShouldMigrateValidParamSets()
  {
    // Arrange
    Func<ImmybotDbContext> ctxFactory = GetSqliteDbContextFactory();
    Func<SoftwareDbContext> sftCtxFactory = GetSqliteSoftwareDbContextFactory();

    CreateGlobalMedia(1);
    var integrationScript = Helpers.MakeDynamicIntegrationScript();
    DynamicIntegrationType dynamicIntegrationType = CreateGlobalDynamicIntegrationType(integrationScript, 1, Guid.NewGuid(), IntegrationTag.Beta);

    var (providerLinksGetter, _, _) = await SetupConvertTargetAssignmentParamsToBindableDictionary(
         ctxFactory,
         sftCtxFactory,
         dynamicIntegrationType);

    List<GroupedParamSet> ParamsSets = new()
    {
      new GroupedParamSet()
      {
        Ids = [1],
        // Act
        // TestParam is the correct param
        ParamSet = new Dictionary<string, object?> { { "TestParam", "https://immense.net" } },
        IntegrationType = dynamicIntegrationType,
        IntegrationTypeId = dynamicIntegrationType.IntegrationTypeId,
        SoftwareName = "Test Software"
      }
    };

    var goodParamSets = await providerLinksGetter.TestParamSets(ParamsSets, CancellationToken.None);

    // Assert
    Assert.NotEmpty(goodParamSets);
  }
}
