using System;
using System.Linq;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.UnitTests.Shared.Lib;

namespace Immybot.Backend.UnitTests.MaintenanceTests;

public class MaintenanceSessionInitializationActionsTests : BaseUnitTests
{
  private readonly MaintenanceSessionInitializationActions _actions;

  public MaintenanceSessionInitializationActionsTests()
  {
    _actions = new MaintenanceSessionInitializationActions(
      GetSqliteDbContextFactory());
  }

  [Fact]
  public void InitializeComputerMaintenanceSession_ShouldCreateSessionWithOnboardingStageIfComputerNeedsOnboarding()
  {
    // arrange
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    var computer = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid()
    );

    // act
    var session = _actions.InitializeComputerMaintenanceSession(computer.Id);

    // assert
    Assert.NotNull(session.Stages.Where(a => a.Type == SessionStageType.Onboarding));
  }

  [Fact]
  public void InitializeComputerMaintenanceSession_ShouldCreateSessionWithDetectionStage()
  {
    // arrange
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    var computer = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid()
    );

    // act
    var session = _actions.InitializeComputerMaintenanceSession(computer.Id);

    // assert
    Assert.NotNull(session.Stages.Where(a => a.Type == SessionStageType.Detection));
  }

  [Fact]
  public void InitializeComputerMaintenanceSession_ShouldCreateSessionWithExecutionStage()
  {
    // arrange
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    var computer = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid()
    );

    // act
    var session = _actions.InitializeComputerMaintenanceSession(computer.Id);

    // assert
    Assert.NotNull(session.Stages.Where(a => a.Type == SessionStageType.Execution));
  }

  [Fact]
  public void InitializeInventoryOnlyComputerMaintenanceSession_ShouldWorkForExcludedComputers()
  {
    // arrange
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    var computer = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid(),
      excludeFromMaintenance: true
    );

    // act
    var session = _actions.InitializeInventoryOnlyComputerMaintenanceSession(computer.Id);

    // assert
    Assert.Equal(computer.Id, session.ComputerId);
    Assert.Equal(tenant.Id, session.TenantId);
    Assert.Single(session.Stages);
    Assert.Equal(SessionStageType.Inventory, session.Stages.First().Type);
  }

  [Fact]
  public void InitializeComputerMaintenanceSession_ShouldThrowForExcludedComputers()
  {
    // arrange
    var tenant = GetOrCreateMspTenant();
    var link = GetOrCreateDefaultProviderLink(tenant.Id);
    var client = GetOrCreateDefaultProviderClient(link.Id);
    var computer = CreateComputer(
      providerLinkId: link.Id,
      externalClientId: client.ExternalClientId,
      externalAgentId: "1",
      tenantId: tenant.Id,
      computerName: "1",
      deviceId: Guid.NewGuid(),
      excludeFromMaintenance: true
    );

    // act & assert
    Assert.Throws<InvalidOperationException>(() => 
      _actions.InitializeComputerMaintenanceSession(computer.Id));
  }
}
