using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.UnitTests.ContextTests;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Controllers.V1;
using Microsoft.Extensions.DependencyInjection;
using Moq;

namespace Immybot.Backend.UnitTests.ControllerTests;
public class ProviderLinksControllerTests : ActionsTestBase
{
  private readonly Func<ImmybotDbContext> _localDbFactory;
  private readonly ProviderLinksController _controller;
  private readonly IServiceProvider _serviceProvider;

  public ProviderLinksControllerTests()
  {
    _localDbFactory = GetSqliteDbContextFactory();
    var host = CreateHost();
    _serviceProvider = host.Services.CreateScope().ServiceProvider;
    var userService = _serviceProvider.GetRequiredService<IUserService>();
    var authorizer = _serviceProvider.GetRequiredService<IResourceAuthorizerFlow>();
    var subjectService = _serviceProvider.GetRequiredService<ISubjectPermissionAuthorizationService>();
    _controller = new ProviderLinksController(userService, authorizer, subjectService);
  }

  [Fact]
  public async Task AutoLinkClientsToTenants_WhenTenantExistWithDifferentCasing_ShouldUseExisting()
  {
    await using var controllerDbContext = GetNewSqliteDbContext();
    var createTenantCmd = new Mock<ICreateTenantCmd>();
    var bulkLinkRequest = new BulkLinkClientsToTenantsRequestBody();
    var msp = GetOrCreateMspTenant();
    var providerLink = GetOrCreateDefaultProviderLink(msp.Id);
    var externalClientId = Guid.NewGuid().ToString();
    bulkLinkRequest.ClientIds.Add(externalClientId);

    // This shouldn't be called if the test passes.
    createTenantCmd
      .Setup(x => x.CreateTenant(It.IsAny<ICreateTenantPayload>(), It.IsAny<int?>(), It.IsAny<CancellationToken>(), false))
      .ReturnsAsync((ICreateTenantPayload payload, int? ownerTenantId, CancellationToken _, bool _) =>
      {
        var tenant = new Tenant() { Name = payload.Name, OwnerTenantId = ownerTenantId, IsMsp = payload.IsMsp };
        // ReSharper disable once AccessToDisposedClosure
        controllerDbContext.Tenants.Add(tenant);
        // ReSharper disable once AccessToDisposedClosure
        controllerDbContext.SaveChanges();
        return tenant;
      });

    // Verify name has a lowercase letter.  Otherwise, ExternalClientName
    // will be identical case, and the test is pointless.
    Assert.Contains(msp.Name, x => char.IsLower(x));

    var client = new ProviderClient
    {
      ProviderLinkId = providerLink.Id,
      ExternalClientId = externalClientId,
      ExternalClientName = msp.Name.ToUpper()
    };
    await using var ctx = _localDbFactory();
    ctx.ProviderClients.Add(client);
    await ctx.SaveChangesAsync();


    await _controller.AutoLinkClientsToTenants(
      controllerDbContext,
      _serviceProvider.GetRequiredService<IProviderActions>(),
      createTenantCmd.Object,
      CancellationToken.None,
      providerLink.Id,
      bulkLinkRequest);

    // This should not have been called.  The tenant name and external client name
    // should have matched insensitively.
    createTenantCmd.VerifyNoOtherCalls();
  }
}
