using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Persistence;
using Immybot.Backend.UnitTests.ContextTests;
using Moq;

namespace Immybot.Backend.UnitTests.ServiceTests;
public class MigrateDeploymentsToProviderLinksServiceTests : ActionsTestBase
{
  [Fact]
  public async Task GetOrCreateProviderLinkFromRecommendation_ShouldCreateANewProviderIfNoneExist()
  {
    // Arrange
    await using var ctx = GetSqliteDbContext();
    GetOrCreateMspTenant(ctx);
    GetOrCreateDefaultUser(1);
    CreateGlobalMedia(1);
    var integrationScript = Helpers.MakeDynamicIntegrationScript();
    var dynamicIntegrationType = CreateGlobalDynamicIntegrationType(integrationScript, 1, Guid.NewGuid());

    var migratableDeploymentResult = new MigratableDeploymentResult()
    {
      IntegrationType = dynamicIntegrationType,
      IntegrationTypeId = dynamicIntegrationType.IntegrationTypeId,
      SoftwareName = "Test Software",
      Params = JsonSerializer.SerializeToElement(new Dictionary<string, object>() { { "TestParam", "https://test.com" } })
    };

    var service = new MigrateDeploymentsToProviderLinksService(
      GetSqliteDbContextFactory(),
      new Mock<IDomainEventEmitter>().Object,
      new Mock<IRecommendedProviderLinksGetter>().Object,
      new Mock<IRunContextFactory>().Object,
      Helpers.MockLoggerFactory().CreateLogger<MigrateDeploymentsToProviderLinksService>()
      );

    // Act
    await service.GetOrCreateProviderLinkFromRecommendation(ctx, migratableDeploymentResult, CancellationToken.None);

    // Assert
    Assert.NotEmpty(ctx.ProviderLinks);
  }

  [Fact]
  public async Task CheckForExistingProviderLink_ShouldUseExistingProviderIfOneExists()
  {
    await using var ctx = GetSqliteDbContext();
    var rootTenant = GetOrCreateMspTenant(ctx);
    CreateGlobalMedia(1);
    var integrationScript = Helpers.MakeDynamicIntegrationScript();
    var dynamicIntegrationType = CreateGlobalDynamicIntegrationType(integrationScript, 1, Guid.NewGuid());

    var migratableDeploymentResult = new MigratableDeploymentResult()
    {
      IntegrationType = dynamicIntegrationType,
      IntegrationTypeId = dynamicIntegrationType.IntegrationTypeId,
      SoftwareName = "Test Software",
      Params = JsonSerializer.SerializeToElement(new Dictionary<string, object>() { { "TestParam", "https://test.com" } })
    };

    var service = new MigrateDeploymentsToProviderLinksService(
      GetSqliteDbContextFactory(),
      new Mock<IDomainEventEmitter>().Object,
      new Mock<IRecommendedProviderLinksGetter>().Object,
      new Mock<IRunContextFactory>().Object,
      Helpers.MockLoggerFactory().CreateLogger<MigrateDeploymentsToProviderLinksService>()
      );

    // Act
    ctx.CreateProviderLink(new ProviderLink()
    {
      Name = "Test Provider",
      OwnerTenantId = rootTenant.Id,
      ProviderTypeId = dynamicIntegrationType.IntegrationTypeId,
      ProviderTypeFormData = JsonSerializer.SerializeToElement(new Dictionary<string, object>() { { "TestParam", "https://test.com" } })
    });

    var result = await service.CheckForExistingProviderLink(ctx, migratableDeploymentResult, CancellationToken.None);

    // Assert
    Assert.NotNull(result);
  }
}
