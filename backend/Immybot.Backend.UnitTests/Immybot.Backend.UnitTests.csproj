<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <IsTestProject>true</IsTestProject>
    <IsPackable>false</IsPackable>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebugType>embedded</DebugType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DebugType>embedded</DebugType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.TimeProvider.Testing" />
    <PackageReference Include="SonarAnalyzer.CSharp" >
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.Linq" />
    <PackageReference Include="Testcontainers" />
    <PackageReference Include="Testcontainers.PostgreSql" />
    <PackageReference Include="xunit" />
    <PackageReference Include="Xunit.Combinatorial" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Immybot.Backend.Manager.Domain\Immybot.Backend.Manager.Domain.csproj" />
    <ProjectReference Include="..\..\shared\Immybot.Shared.Primitives\Immybot.Shared.Primitives.csproj" />
    <ProjectReference Include="..\..\shared\tests\Immybot.Shared.Tests.TestingUtilities\Immybot.Shared.Tests.TestingUtilities.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Application\Immybot.Backend.Application.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Persistence\Immybot.Backend.Persistence.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Domain\Immybot.Backend.Domain.csproj" />
    <ProjectReference Include="..\Immybot.Backend.UnitTests.Shared.Sqlite\Immybot.Backend.UnitTests.Shared.Sqlite.csproj"/>
    <ProjectReference Include="..\Immybot.Backend.UnitTests.Shared\Immybot.Backend.UnitTests.Shared.csproj"/>
    <ProjectReference Include="..\Immybot.Backend.Web\Immybot.Backend.Web.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="FactoryData\DetectedSoftwareSet.json" />
    <None Remove="FactoryData\GlobalDb.json" />
    <None Remove="FactoryData\RawSoftwareInventoryOutput.json" />
    <None Update="DynamicFormServiceTests\Get-CommandParameters.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DynamicFormServiceTests\Call-GetCommandParameters.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DynamicFormServiceTests\Test-DynamicParameters.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DynamicFormServiceTests\Binds-PsObjectDropdownParameter.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DynamicFormServiceTests\Get-PSObjectDropdownParameter.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DynamicFormServiceTests\Get-HashtableDropdownParameter.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DynamicFormServiceTests\Binds-HashtableDropdownParameter.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="MetascriptTests\Scripts_ShouldHavePSBoundParameters_NotUsingGetCommandParameters\Get-TestFunctionParameters.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="MetascriptTests\Scripts_ShouldHavePSBoundParameters_NotUsingGetCommandParameters\Test-CallTestFunction.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="MetascriptTests\Scripts_ShouldHavePSBoundParameters_NotUsingGetCommandParameters\Test-Function.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ContextTests\JobArgsWithSoftwareMaintenanceItem.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ContextTests\JobArgsWithTaskMaintenanceItem.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Update="testsettings.Local.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>Immybot.Backend.MetascriptInvoker.Benchmark</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>
</Project>
