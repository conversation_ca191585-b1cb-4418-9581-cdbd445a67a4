using System;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Providers.ImmyAgentProvider.Signalr.AgentHub.Models;
using Immybot.Shared.Extensions;
using Immybot.Shared.Primitives;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.Extensions.Logging;
using NuGet.Versioning;

namespace Immybot.Backend.Providers.ImmyAgentProvider.Persistence;

public record ImmyAgentUnknownData(int ProviderLinkId, string ExternalClientId, bool FoundExistingTenant);

public interface IProviderAgentRepository
{
  Task<OpResult<AgentForConnectionEventDto?>> GetAgentForConnectionEvent(Guid externalAgentId,
    CancellationToken cancellationToken);
  Task<OpResult<ImmyAgentUnknownData>> GetImmyAgentUnknownData(AgentConnectionEvent connectionEvent);

  Task<OpResult> UndeleteAgent(int agentId);
}

public record AgentForConnectionEventDto
{
  public required int Id { get; init; }
  public required int? ComputerId { get; init; }
  public required JsonElement? InternalData { get; init; }
  public required DateTime? DeletedAt { get; init; }
  public required int ProviderLinkId { get; init; }
  public required string ExternalClientId { get; init; }
  public required SemanticVersion? Version { get; init; }
}

internal class ProviderAgentRepository : IProviderAgentRepository
{
  private readonly Func<ImmybotDbContext> _localDbFactory;
  private readonly ILogger<ProviderAgentRepository> _logger;
  private readonly SemaphoreSlim _unknownDataLock = new(1, 1);

  private static readonly Guid ImmyAgentProviderTypeId = Guid.Parse(Shared.Constants.ImmyProviderTypeId);
  private static readonly string UnknownTenantName = "Unknown immy.bot Agents";
  public ProviderAgentRepository(
    Func<ImmybotDbContext> localDbFactory,
    ILogger<ProviderAgentRepository> logger)
  {
    _localDbFactory = localDbFactory;
    _logger = logger;
  }

  public async Task<OpResult<AgentForConnectionEventDto?>> GetAgentForConnectionEvent(Guid externalAgentId,
    CancellationToken cancellationToken)
  {
    try
    {
      await using var db = _localDbFactory();
      var agentId = $"{externalAgentId}";
      var result = await db.ProviderAgents
        .AsNoTracking()
        .IgnoreQueryFilters()
        .Where(x => x.ExternalAgentId == agentId && x.ProviderLink != null &&
                    x.ProviderLink.ProviderTypeId == ImmyAgentProviderTypeId)
        .Select(a => new AgentForConnectionEventDto
        {
          Id = a.Id,
          InternalData = a.InternalData,
          DeletedAt = a.DeletedAt,
          ProviderLinkId = a.ProviderLinkId,
          ExternalClientId = a.ExternalClientId,
          Version = a.AgentVersion,
          ComputerId = a.ComputerId,

        })
        .FirstOrDefaultAsync(cancellationToken);

      return result is null
        ? OpResult.Ok<AgentForConnectionEventDto?>(null)
        : OpResult.Ok<AgentForConnectionEventDto?>(result);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error getting agent for connection event.");
      return OpResult.Fail<AgentForConnectionEventDto?>("Error getting agent for connection event.");
    }
  }

  public async Task<OpResult<ImmyAgentUnknownData>> GetImmyAgentUnknownData(AgentConnectionEvent connectionEvent)
  {
    var acquired = await _unknownDataLock.WaitAsync(TimeSpan.FromSeconds(60));
    try
    {
      await using var ctx = _localDbFactory();
      var immyAgentLinkId = (await ctx.ProviderLinks
        .FirstOrDefaultAsync(a => a.ProviderTypeId == ImmyAgentProviderTypeId))?.Id;

      if (immyAgentLinkId is null) return OpResult.Fail<ImmyAgentUnknownData>("ImmyAgent integration was not found");

      // try to find a computer that has the same computer name and serial number.
      // if we find one, use the external client id linked to that tenant. Otherwise, use the unknown tenant.
      var lowerSerial = connectionEvent.DeviceDetails.SerialNumber.ToLower();
      var lowerDeviceName = connectionEvent.DeviceDetails.DeviceName.ToLower();
      var existingTenantId = await ctx.Computers
        .AsNoTracking()
        .Where(a =>
            a.ComputerName != null &&
            a.SerialNumber != null &&
            a.ComputerName.ToLower() == lowerDeviceName &&
            a.SerialNumber.ToLower() == lowerSerial)
        .Select(a => a.TenantId)
        .FirstOrDefaultAsync();

      if (existingTenantId is not 0)
      {
        // immy agent clients can be linked to multiple tenants, so we need to check the external client id field AND the linked to tenant id field.
        var linkedClient = (await ctx.ProviderClients.FirstOrDefaultAsync(a =>
            a.ExternalClientId == existingTenantId.ToString() &&
            a.LinkedToTenantId == existingTenantId &&
            a.ProviderLinkId == immyAgentLinkId))
          ?.ExternalClientId;

        if (linkedClient is not null)
        {
          return OpResult.Ok(new ImmyAgentUnknownData(immyAgentLinkId.Value, linkedClient, true));
        }

        // if the linked client is null, then let's just create a new one, since we know the tenant to associate it with.
        var tenantName = await ctx.Tenants
          .AsNoTracking()
          .Where(a => a.Id == existingTenantId)
          .Select(a => a.Name)
          .FirstOrDefaultAsync() ?? string.Empty;

        var newClient = new ProviderClient()
        {
          ProviderLinkId = immyAgentLinkId.Value,
          ExternalClientId = existingTenantId.ToString(),
          LinkedToTenantId = existingTenantId,
          ExternalClientName = tenantName,
          HasCompletedInitialAgentSync = true,
        };
        ctx.ProviderClients.Add(newClient);
        await ctx.SaveChangesAsync();
        return OpResult.Ok(new ImmyAgentUnknownData(immyAgentLinkId.Value, newClient.ExternalClientId, true));
      }

      var existingUnknownClientId = (await ctx.ProviderClients
          .FirstOrDefaultAsync(a => a.ExternalClientName == UnknownTenantName && a.ProviderLinkId == immyAgentLinkId))
        ?.ExternalClientId;

      if (existingUnknownClientId is not null)
      {
        return OpResult.Ok(new ImmyAgentUnknownData(immyAgentLinkId.Value, existingUnknownClientId, false));
      }

      var unknownTenant = await ctx.Tenants.AsNoTracking().FirstOrDefaultAsync(a => a.Name == UnknownTenantName);

      if (unknownTenant is null)
      {
        // create the unknown tenant
        unknownTenant = new Tenant { Name = UnknownTenantName };
        ctx.Tenants.Add(unknownTenant);
        await ctx.SaveChangesAsync();
      }

      // create the unknown client
      var unknownClient = new ProviderClient
      {
        ProviderLinkId = immyAgentLinkId.Value,
        ExternalClientId = unknownTenant.Id.ToString(),
        ExternalClientName = unknownTenant.Name,
        LinkedToTenantId = unknownTenant.Id
      };
      ctx.ProviderClients.Add(unknownClient);
      await ctx.SaveChangesAsync();

      return OpResult.Ok(new ImmyAgentUnknownData(immyAgentLinkId.Value, unknownClient.ExternalClientId, false));
    }
    catch (Exception ex)
    {
      return OpResult.Fail<ImmyAgentUnknownData>(ex, "Error getting ImmyAgent unknown data.");
    }
    finally
    {
      if (acquired) _unknownDataLock.Release();
    }
  }

  public async Task<OpResult> UndeleteAgent(int agentId)
  {
    try
    {
      await using var ctx = _localDbFactory();
      await ctx.ProviderAgents
        .AsNoTracking()
        .IgnoreQueryFilters()
        .Where(a => a.Id == agentId)
        .ExecuteUpdateAsync(a => a.SetProperty(b => b.DeletedAt, (DateTime?)null));
      return OpResult.Ok();
    }
    catch(Exception ex)
    {
      return OpResult.Fail(ex, "Error undeleting agent.");
    }
  }
}
