using System;
using System.Linq;
using System.Management.Automation;
using System.Threading;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Backend.Providers.NCentralProvider;
using Immybot.Backend.Providers.NCentralProvider.NCentralAPI.Exceptions;
using Immybot.Shared.ProxyHelpers;
using Microsoft.VisualStudio.Threading;

namespace NCentralRmmProvider;

[OutputType(typeof(string))]
[Cmdlet("Get", "NCentralActivationKey")]
public class GetActivationKey : PSCmdlet
{
  [Parameter(Mandatory = true, Position = 1)]
  public IProvider? Provider { get; set; }
  [Parameter(Mandatory = true, Position = 2)]
  public string? AgentId { get; set; }

  protected override void ProcessRecord()
  {
    if (Provider is null) throw new NCentralException("Provider is missing.");
    if (AgentId is null) throw new NCentralException("AgentId is missing.");

    var providerUnwrapped = ProxyHelpers.UnwrapProxy(Provider);
    if (providerUnwrapped is null) throw new NCentralException("Provider proxy cannot be unwrapped.");
    if (providerUnwrapped is not NCentralProvider nCentralProvider) throw new NCentralException("Provider is not an NCentralProvider.");

    // Make sure our AgentID is only digits
    if (!(AgentId.All(char.IsDigit))) throw new NCentralException("AgentId is not a valid AgentId. AgentId should only contain digits!");

    var token = this.DemandVariableValue<CancellationToken>("CancellationToken");
    try
    {
      var activationKey = new JoinableTaskContext().Factory.Run(async () => await nCentralProvider.GetAgentRepairToken(AgentId, token));
      WriteObject(activationKey, true);
    }
    catch (GenericNCentralException ex)
    {
      WriteError(new ErrorRecord(
        new Exception("Error getting Activation Key from NCentralAPI. Ensure AgentId is correct."),
          ex.Message,
          ErrorCategory.NotSpecified,
          AgentId));
    }
  }
}
