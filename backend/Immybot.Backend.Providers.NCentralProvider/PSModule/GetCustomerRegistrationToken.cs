using System.Management.Automation;
using System.Threading;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.PSCmdlets;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Backend.Providers.NCentralProvider;
using Immybot.Backend.Providers.NCentralProvider.NCentralAPI.Exceptions;
using Immybot.Backend.Providers.NCentralProvider.NCentralAPI.Response_Objects;
using Immybot.Shared.ProxyHelpers;
using Microsoft.VisualStudio.Threading;

namespace NCentralRmmProvider;

[OutputType(typeof(RegistrationToken))]
[Cmdlet("Get", "NCentralRegistrationToken")]
public class GetCustomerRegistrationToken : ImmyPSCmdlet
{
  [Parameter(Mandatory = true, Position = 1)]
  public IProvider? Provider { get; set; }
  [Parameter(Mandatory = true, Position = 2)]
  public long? CustomerId { get; set; }
  [Parameter(Mandatory = false, Position = 3)]
  public SwitchParameter GenerateNewTokenIfAlreadyExists { get; set; }
  protected override void ProcessRecord()
  {
    if (Provider is null) throw new NCentralException("Provider is missing.");
    if (CustomerId is null) throw new NCentralException("CustomerId is missing");

    var providerUnwrapped = ProxyHelpers.UnwrapProxy(Provider)
      ?? throw new NCentralException("Provider proxy cannot be unwrapped.");

    if (providerUnwrapped is not NCentralProvider nCentralProvider)
      throw new NCentralException("Provider is not an NCentralProvider");

    var token = this.DemandVariableValue<CancellationToken>("CancellationToken");
    try
    {
      var registrationToken = new JoinableTaskContext()
        .Factory
        .Run(async () =>
          await nCentralProvider.GetRegistrationToken(
            CustomerId.Value,
            GenerateNewTokenIfAlreadyExists.ToBool(),
            token
          )
        );

      WriteObject(registrationToken, true);
    }
    catch (GenericNCentralException ex)
    {
      ThrowImmyTerminatingError(new ErrorRecord(
        ex,
        ex.Message,
        ErrorCategory.NotSpecified,
        CustomerId));
    }
  }
}
