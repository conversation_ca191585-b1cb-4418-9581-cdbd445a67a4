using System.Text.Json;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Immybot.Backend.Application.DbContextExtensions;
using ImmyAgentProvider;
using Immybot.Backend.Domain.Models;
using Microsoft.Extensions.Options;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Shared.Services.Startup;

namespace Immybot.Backend.Web.Common.Infrastructure;

internal class DevEntitiesInitFilter : IStartupTask
{
  private readonly Func<ImmybotDbContext> _dbFactory;
  private readonly IOptions<ImmyAgentOptions> _agentOptions;
  private readonly ILogger<DevEntitiesInitFilter> _logger;

  public DevEntitiesInitFilter(
    Func<ImmybotDbContext> dbFactory,
    IOptions<ImmyAgentOptions> agentOptions,
    ILogger<DevEntitiesInitFilter> logger)
  {
    _dbFactory = dbFactory;
    _agentOptions = agentOptions;
    _logger = logger;
  }
  public async Task ExecuteAsync(CancellationToken cancellationToken = default)
  {
    try
    {
      if (!_agentOptions.Value.UseDevTestAgents)
      {
        _logger.LogWarning(
          "{StartupTaskType} should not be run when UseDevTestAgents is disabled in the options.",
          nameof(DevEntitiesInitFilter));
        return;
      }

      if (_agentOptions.Value.DevTestAgents is not { Count: > 0 })
      {
        _logger.LogWarning("No dev/test agents are configured in the options.");
        return;
      }

      await using var ctx = _dbFactory.Invoke();

      var tenantId = ctx.GetRootTenant()?.Id ?? throw new NotSupportedException("A msp tenant is required in order to debug local computer");

      foreach (var agent in _agentOptions.Value.DevTestAgents)
      {
        if (agent.AgentDeviceId == Guid.Empty)
        {
          _logger.LogWarning("AgentDeviceId is required for dev/test agent.");
          continue;
        }

        if (agent.AgentInstallerId == Guid.Empty)
        {
          _logger.LogWarning("AgentInstallerId is required for dev/test agent.");
          continue;
        }

        await CreateDevTestAgent(tenantId, agent, ctx, cancellationToken);
      }

      // All Immy agents should be offline until the backend starts.
      await SetAllImmyAgentsOffline(ctx, cancellationToken);

    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error while generating dev entities.");
    }
  }

  private static async Task CreateDevTestAgent(
    int tenantId,
    DevTestAgent agent,
    ImmybotDbContext ctx,
    CancellationToken cancellationToken)
  {
    // ensure dev computer is created
    var devComputer = await GetOrCreateDevComputer(ctx, tenantId, agent.AgentDeviceId, cancellationToken);

    await EnsureProviderAgentCreated(
      ctx,
      devComputer,
      agent.AgentDeviceId,
      agent.AgentInstallerId,
      tenantId,
      cancellationToken);
  }

  private static async Task EnsureProviderAgentCreated(
    ImmybotDbContext ctx,
    Computer devComputer,
    Guid agentDeviceId,
    Guid agentInstallerId,
    int tenantId,
    CancellationToken cancellationToken)
  {
    // ensure ephemeral development link is created
    var providerLink = await ctx.ProviderLinks
      .IgnoreQueryFilters()
      .FirstOrDefaultAsync(x => x.ProviderTypeId == Guid.Parse(Constants.ProviderId), cancellationToken)
        ?? throw new InvalidOperationException("Provider link must exist in order to debug local computer");

    // ensure dev client is created
    var externalClientId = $"DefaultClientForLink{providerLink.Id}";
    var providerClient =
      await ctx.ProviderClients.FirstOrDefaultAsync(a => a.ProviderLinkId == providerLink.Id && a.ExternalClientId == externalClientId, cancellationToken);
    providerClient ??= ctx.CreateProviderClient(new ProviderClient
    {
      ExternalClientName = "",
      ProviderLinkId = providerLink.Id,
      ExternalClientId = externalClientId,
      LinkedToTenantId = tenantId,
    });

    // ensure dev agent is created
    var providerAgent = await ctx.ProviderAgents.FirstOrDefaultAsync(a =>
      a.ExternalAgentId == agentDeviceId.ToString() &&
        a.ProviderLinkId == providerLink.Id &&
        a.ComputerId == devComputer.Id,
      cancellationToken);

    if (providerAgent is null)
    {
      providerAgent = new ProviderAgent()
      {
        ExternalAgentId = agentDeviceId.ToString(),
        ProviderLinkId = providerLink.Id,
        ComputerId = devComputer.Id,
        IsOnline = true,
        ExternalClientId = providerClient.ExternalClientId,
        SupportsRunningScripts = true,
        InternalData = GetAgentInternalData(agentInstallerId),
        AgentVersion = new NuGet.Versioning.SemanticVersion(1, 0, 0),
      };
      ctx.ProviderAgents.Add(providerAgent);
      await ctx.SaveChangesAsync(cancellationToken);
    }
    else
    {
      providerAgent.IsOnline = true;
      await ctx.SaveChangesAsync(cancellationToken);
    }
  }

  private static JsonElement GetAgentInternalData(Guid agentInstallerId)
  {
    return JsonSerializer.Deserialize<JsonElement>(
      $@"{{""{Constants.AgentInstallerIDTag}"":""{agentInstallerId}""}}");

  }

  private static async Task<Computer> GetOrCreateDevComputer(
    ImmybotDbContext ctx,
    int tenantId,
    Guid agentDeviceId,
    CancellationToken cancellationToken)
  {
    var devComputer = await ctx.Computers
      .IgnoreQueryFilters()
      .FirstOrDefaultAsync(a =>
        a.DeviceId == agentDeviceId && a.IsSandbox,
        cancellationToken);

    if (devComputer is null)
    {
      devComputer = new Computer()
      {
        ComputerName = Environment.MachineName,
        SerialNumber = Guid.Empty.ToString(),
        IsSandbox = true,
        DeviceId = agentDeviceId,
        TenantId = tenantId,
      };

      ctx.Computers.Add(devComputer);
      await ctx.SaveChangesAsync(cancellationToken);
    }

    return devComputer;
  }

  private static async Task SetAllImmyAgentsOffline(ImmybotDbContext ctx, CancellationToken cancellationToken)
  {
    var immyProviderId = Guid.Parse(Constants.ProviderId);

    await ctx.ProviderAgents
      .Include(x => x.ProviderLink)
      .Where(x => x.ProviderLink != null && x.ProviderLink.ProviderTypeId == immyProviderId)
      .ExecuteUpdateAsync(
        x => x.SetProperty(a => a.IsOnline, false),
        cancellationToken);
  }
}
