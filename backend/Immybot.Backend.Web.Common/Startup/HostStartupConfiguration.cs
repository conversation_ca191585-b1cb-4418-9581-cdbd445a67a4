using Hangfire;
using Hangfire.Dashboard.BasicAuthorization;
using Immense.RemoteControl.Server.Extensions;
using Immybot.Backend.Application.Infrastructure;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Providers.ImmyAgentProvider.Signalr.AgentHub;
using Immybot.Backend.Web.Common.Infrastructure;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.Lib.Middleware;
using Immybot.Backend.Web.Common.Lib.SignalRHubs;
using Immybot.Shared.DataContracts.Signalr;
using Immybot.Shared.Primitives;
using MessagePipe;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Nito.Disposables.Internals;

namespace Immybot.Backend.Web.Common.Startup;

public static class HostStartupConfiguration
{
  private const string _localHostUri = "http://localhost:8080";

  /// <summary>
  /// Runs the backend process (depending on the execution mode).
  /// In Immy-backend mode, this configures the <see cref="WebApplication"/> with Immybot backend middleware."/>
  ///
  /// In PSES server mode, this configures the bare minimum to run the PSES server via Metascript Invoker.
  /// </summary>
  /// <param name="app"></param>
  /// <returns></returns>
  public static async Task<IHost> UseBackend(this IHost app)
  {
    var psesSettings = app.Services.GetRequiredService<IOptions<PowershellEditorServicesOptions>>().Value;
    if (psesSettings.ShouldLaunchAsEditorServicesServer)
    {
      return app;
    }

    if (app is not WebApplication builder)
    {
      throw new InvalidOperationException("The host must be an IApplicationBuilder to use Immybot backend.");
    }

    return await builder.UseImmybotBackend();
  }

  public static async Task<T> UseImmybotBackend<T>(this T app) where T : IApplicationBuilder, IHost
  {
    var hostEnvironment = app.ApplicationServices.GetRequiredService<IHostEnvironment>();
    var hostLifetime = app.ApplicationServices.GetRequiredService<IHostApplicationLifetime>();

    GlobalMessagePipe.SetProvider(app.Services);

    // JG: Why are we forgetting this task?
    // It looks like most of this initialization is registering event handlers.  Why not move those to IHostedServices?
    app.InitializeApplicationServices().Forget();

    app.InitializeWebServices();

    // Accept websocket requests.
    // since this is used currently only for ephemeral agents,
    // we will configure a very aggressive keep-alive. This will assist
    // with fast detection and recovery of dropped connections
    app.UseWebSockets(new WebSocketOptions()
    {
      KeepAliveInterval = TimeSpan.FromSeconds(5),
    });

    app.UseOutputCache();

    if (hostEnvironment.IsDevelopment())
    {
      app.UseCors();
      // show unauthenticated dashboard in development
      app.UseHangfireDashboard("/hangfire",
        new DashboardOptions() { Authorization = new[] { new DashboardNoAuthorizationFilter() } });
    }

    if (hostEnvironment.IsProduction())
    {
      // Require basic auth when attempting to access the hangfire dashboard in production
      app.UseHangfireDashboard("/hangfire", new DashboardOptions
      {
        Authorization = new[]
        {
            new BasicAuthAuthorizationFilter(
              new BasicAuthAuthorizationFilterOptions
              {
                RequireSsl = true,
                LoginCaseSensitive = true,
                Users = new[]
                {
                  new BasicAuthAuthorizationUser
                  {
                      Login = "Administrator",
                      // Password as SHA1 hash
                      Password = [0x4a,0x55,0x0a,0x70,0x65,0x32,0xde,0x63,0x23,0x65,0x6c,0x65,0xab,0x05,0xa8,0x73,0x64,0xe0,0x69,0x08]
                  },
                }
              })
            }
      });
    }

    // TODO: Move this to a middleware
    app.Use(async (ctx, next) =>
    {
      var appSettings = ctx.RequestServices
        .GetRequiredService<IOptionsMonitor<AppSettingsOptions>>().CurrentValue;
      // Used by the frontend for letting the user know when their instance
      // has been updated to a new version so they can refresh the page
      if (ctx.Request.Path.Equals("/app-version.txt"))
      {
        ctx.Response.StatusCode = StatusCodes.Status200OK;
        ctx.Response.ContentType = "text/plain";
        await ctx.Response.WriteAsync(appSettings.BackendVersion);
        return;
      }

      if (ctx.Request.Path.Equals("/app-insights.txt"))
      {
        ctx.Response.StatusCode = StatusCodes.Status200OK;
        ctx.Response.ContentType = "text/plain";
        await ctx.Response.WriteAsync(appSettings.AppInsightsInstrumentationKey);
        return;
      }
      await next();
    });

    // Format uncaught request exceptions as HttpProblems
    // Skip this middleware in test environments to allow exceptions to bubble up to integration tests
    //   https://github.com/dotnet/aspnetcore/issues/19217#issuecomment-952358254 (Expected behavior)
    if (!hostEnvironment.IsEnvironment(AppConstants.TestEnvironmentName))
    {
      app.UseMiddleware<HttpProblemExceptionMiddleware>();
    }

    // Change requests for static assets to serve pre-compressed if the client accepts compressed
    // TODO: move this to a middleware
    var clientDir = Path.Combine(hostEnvironment.ContentRootPath, "client");
    app.Use(async (context, next) =>
    {
      if (context.GetEndpoint() != null) { await next(); return; }
      if (!context.Request.Headers.TryGetValue("Accept-Encoding", out var acceptedEncoding)) { await next(); return; }
      var acceptedEncodings = acceptedEncoding.WhereNotNull().SelectMany(e => e.Split(",").Select(s => s.Trim())).ToList();
      var path = context.Request.Path;
      if (path.HasValue && (path.Value.EndsWith(".js") || path.Value.EndsWith(".css") || path.Value.EndsWith(".html") || path.Value.EndsWith(".svg")))
      {
        bool didRewrite = false;
        if (acceptedEncodings.Contains("br"))
        {
          var brPath = $"{path}.br";
          if (File.Exists(Path.GetFullPath(Path.Combine(clientDir, brPath.TrimStart('/')))))
          {
            // rewrite request path to brotli one
            context.Request.Path = brPath;
            didRewrite = true;
          }
        }
        if (!didRewrite && acceptedEncodings.Contains("gzip"))
        {
          var gzPath = $"{path}.gz";
          if (File.Exists(Path.GetFullPath(Path.Combine(clientDir, gzPath.TrimStart('/')))))
          {
            // rewrite request path to gzipped one
            context.Request.Path = gzPath;
          }
        }
      }
      await next();
    });

    // The remote control library needs static assets for the Viewer page.
    app.UseStaticFiles();

    // Serve static assets and set the appropriate response content type for compressed assets
    var mimeTypeProvider = new FileExtensionContentTypeProvider();
    mimeTypeProvider.Mappings[".br"] = "application/x-br";
    app.UseSpaStaticFiles(new StaticFileOptions()
    {
      ContentTypeProvider = mimeTypeProvider,
      OnPrepareResponse = context =>
      {
        var headers = context.Context.Response.Headers;
        var contentType = headers["Content-Type"];

        if (contentType == "application/x-gzip" && context.File.Name.EndsWith(".gz"))
        {
          var fileNameToTry = context.File.Name[0..^3];

          if (mimeTypeProvider.TryGetContentType(fileNameToTry, out var mimeType))
          {
            headers["Content-Encoding"] = "gzip";
            headers["Content-Type"] = mimeType;
          }
        }
        if (contentType == "application/x-br" && context.File.Name.EndsWith(".br"))
        {
          var fileNameToTry = context.File.Name[0..^3];

          if (mimeTypeProvider.TryGetContentType(fileNameToTry, out var mimeType))
          {
            headers["Content-Encoding"] = "br";
            headers["Content-Type"] = mimeType;
          }
        }
      },
    });

    // Authentication middleware should be after static files.  No need
    // to waste CPU cycles on public content.
    app.UseAuthentication();

    // Reject unauthenticated / unauthorized requests attempting to access api and UserHub
    // Set CurrentUser in HttpContext#Items dictionary

    // TODO: Authentication was already handled by UseAuthentication middleware.
    // This is more of a pre-authorization middleware. If we intended to keep this,
    // we might want to rename it to avoid confusion.  However, I'd prefer to see
    // this refactored to use the standard aspnet authorization middleware.
    app.UseMiddleware<ImmybotAuthorizationMiddleware>();

    // Route api/signalr requests to relevant controllers/hubs
    app.UseRouting();
    app.UseAuthorization();
    app.UseRemoteControlServer();
    app.UseEndpoints(endpoints =>
    {
      endpoints.MapHub<ImmyBotUserHub>("/UserHub");
      endpoints.MapHub<AgentHub>(HubRoutesV1.AgentHubPath);
      endpoints.MapControllers();
      endpoints.MapDefaultHealthEndpoints();
    });

    // Enable middleware to serve generated Swagger as a JSON endpoint.
    app.UseSwagger();
    // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.),
    // specifying the Swagger JSON endpoint.
    app.UseSwaggerUI(c =>
    {
      c.SwaggerEndpoint("/swagger/v1/swagger.json", "immy.bot api v1");
    });

    // For any requests that have not gotten matched by aspnet routing, try them as
    // SPA routes (i.e. return index.html from clients directory)
    app.UseSpa(spa =>
    {
      spa.Options.DefaultPage = "/index.html";
      spa.Options.DefaultPageStaticFileOptions = new()
      {
        ContentTypeProvider = mimeTypeProvider,
        // Send "Cache-Control: no-store" header in response when serving index.html for
        // SPA requests, so that browsers always get the latest version of the frontend
        OnPrepareResponse = context =>
        {
          var headers = context.Context.Response.Headers;
          var contentType = headers["Content-Type"];

          if (contentType == "application/x-gzip" && context.File.Name.EndsWith(".gz"))
          {
            var fileNameToTry = context.File.Name[0..^3];

            if (mimeTypeProvider.TryGetContentType(fileNameToTry, out var mimeType))
            {
              headers["Content-Encoding"] = "gzip";
              headers["Content-Type"] = mimeType;
            }
          }
          if (contentType == "application/x-br" && context.File.Name.EndsWith(".br"))
          {
            var fileNameToTry = context.File.Name[0..^3];

            if (mimeTypeProvider.TryGetContentType(fileNameToTry, out var mimeType))
            {
              headers["Content-Encoding"] = "br";
              headers["Content-Type"] = mimeType;
            }
          }
          context.Context.Response.Headers.Append("Cache-Control", "no-store");
        },
      };
      if (hostEnvironment.IsDevelopment())
      {
        var appSettings = app.ApplicationServices.GetRequiredService<IOptions<AppSettingsOptions>>()
          .Value;
        // In dev, proxy SPA requests to the frontend vue dev server
        spa.UseProxyToSpaDevelopmentServer(
          string.IsNullOrWhiteSpace(appSettings.FrontendDevServer)
            ? _localHostUri
            : appSettings.FrontendDevServer);
      }
    });

    hostLifetime.ApplicationStopped.Register(() =>
    {
      var telemetryConfig = app.Services.GetService<TelemetryConfiguration>();
      if (telemetryConfig is null)
      {
        return;
      }

      // This is to ensure that even if application terminates, telemetry is sent to the back-end.
      telemetryConfig.TelemetryChannel.Flush();
      Thread.Sleep(1000);
      telemetryConfig.Dispose();
    });


    return app;
  }
}
