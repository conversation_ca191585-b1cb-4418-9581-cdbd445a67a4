using System.Text.Json;
using System.Text.RegularExpressions;
using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.ResponseModel;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.PersonExtensions;
using Immybot.Backend.Application.DbContextExtensions.TagExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Policies;
using Immybot.Backend.Application.Lib.Scripts.EphemeralAgent;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.Lib.JsonConverters;
using Immybot.Shared.DataContracts.WindowsRegistry;
using Immybot.Shared.Extensions;
using Immybot.Shared.Primitives;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Polly.CircuitBreaker;
using Polly.Registry;
using AuditType = Immybot.Backend.Application.DbContextExtensions.AuditType;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class ComputersController(
  IPermissionFilterBuilder permissionFilterBuilder,
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IUserService userService,
  IResourceAuthorizerFlow resourceAuthorizerFlow) : Controller
{
  private static readonly JsonSerializerOptions _jsonSerializerOptions =
    new() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
  static ComputersController()
  {
    _jsonSerializerOptions.Converters.Add(new JObjectConverter());
  }

  // NB: This constructor is called for each request to the computers controller
  // meaning any items that are dependency injected in the constructor will be
  // populated for each request to the ComputersController. Thus, the only items
  // that should appear in this constructor are items that are used by all the
  // controller actions. If an item is only used by *some* of the actions, then
  // those actions should get the item from the dependency injector via the
  // [FromServices] attribute

  [SubjectPermissionAuthorize(typeof(IComputersViewAgentStatusReportPermission))]
  [HttpGet(ComputerApiRoutes.ComputerAgentStatusReport)]
  public async Task<ActionResult<List<ComputerAgentStatusDto>>> GetComputerAgentStatusReport(
    [FromServices] ImmybotDbContext ctx,
    DataSourceLoadOptions loadOptions,
    CancellationToken token,
    [FromQuery] int? tenantId = null)
  {
    var q = ctx.Computers
      .AsNoTracking()
      .AsSplitQuery();

    var filter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewInventoryReportPermission>();

    if (tenantId.HasValue)
    {
      q = q.Where(a => a.TenantId == tenantId);
    }

    var ret = q
      .Where(filter)
      .Select(a => new ComputerAgentStatusDto
      {
        Id = a.Id,
        TenantId = a.TenantId,
        ComputerName = a.ComputerName,
        TenantName = a.Tenant!.Name,
        AllAgentsConnected = a.Agents.All(b => b.IsOnline),
        SomeConnected = a.Agents.Any(b => b.IsOnline) &&
                        a.Agents.Any(b => !b.IsOnline),
        Agents = a.Agents.Select(b => new AgentStatusDto
        {
          AgentId = b.Id,
          ProviderLinkId = b.ProviderLinkId,
          IsConnected = b.IsOnline,
          Version = b.AgentVersion,
          LastUpdatedUtc = b.LastUpdatedUTC
        }).ToList()
      });

    return Ok(await DataSourceLoader.LoadAsync(ret, loadOptions, token));
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewInventoryReportPermission))]
  [HttpGet(ComputerApiRoutes.ComputerInventoryDx)]
  public async Task<ActionResult<DxComputerInventoryScriptResult[]>> ComputerInventoryDx(
    [FromServices] ImmybotDbContext ctx,
    DataSourceLoadOptions loadOptions,
    CancellationToken token)
  {
    var filter = permissionFilterBuilder
      .BuildFilterExpression<Computer, IComputersViewInventoryReportPermission>();
    var q = ctx.Computers
      .AsNoTracking()
      .IgnoreQueryFilters()
      .Where(filter)
      .Where(a => a.DeletedAt == null)
      .SelectMany(a => a.LatestInventoryScriptResults)
      .Select(a => new DxComputerInventoryScriptResult
      {
        ComputerId = a.ComputerId,
        ComputerName = a.Computer!.ComputerName,
        TenantId = a.Computer!.TenantId,
        TenantName = a.Computer.Tenant != null ? a.Computer!.Tenant.Name : String.Empty,
        TimestampUtc = a.Timestamp,
        Success = !a.LatestResultIsError,
        InventoryKey = a.InventoryKey
      });

    return Ok(await DataSourceLoader.LoadAsync(q, loadOptions, token));
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewInventoryReportPermission))]
  [HttpGet(ComputerApiRoutes.ExportComputerInventory)]
  public async Task<ActionResult> ExportComputerInventory(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IExcelExporterFactory excelExporterFactory,
    DataSourceLoadOptions loadOptions,
    CancellationToken token)
  {
    // required in order to allow export directly to response stream
    var feature = HttpContext.Features.Get<IHttpBodyControlFeature>();
    if (feature != null)
    {
      feature.AllowSynchronousIO = true;
    }

    var filter = permissionFilterBuilder
      .BuildFilterExpression<Computer, IComputersViewInventoryReportPermission>();
    var q = ctx.Computers.AsNoTracking()
      .AsSplitQuery()
      .IgnoreQueryFilters()
      .Where(filter)
      .Where(a => a.DeletedAt == null)
      .SelectMany(a => a.LatestInventoryScriptResults)
      .Select(a => new DxComputerInventoryScriptResult
      {
        ComputerId = a.ComputerId,
        ComputerName = a.Computer!.ComputerName,
        TenantId = a.Computer.TenantId,
        TenantName = a.Computer.Tenant != null ? a.Computer!.Tenant.Name : String.Empty,
        TimestampUtc = a.Timestamp,
        Success = !a.LatestResultIsError,
        InventoryKey = a.InventoryKey,
        LatestSuccessResult = a.LatestSuccessResult
      });

    var exporter =
      excelExporterFactory.Create<DxComputerInventoryScriptResult>(Response,
        "computer-inventory-export.xlsx");
    await exporter.ExportData(q, loadOptions, 500, token);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewInventoryReportPermission))]
  [HttpGet(ComputerApiRoutes.AllDetectedComputerSoftwareDx)]
  public async Task<ActionResult<DetectedComputerSoftwareResponse[]>> GetSoftwareFromInventoryDx(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewInventoryReportPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var result = ctx.DetectedComputerSoftware
      .AsNoTracking()
      .Where(a => a.ComputerId == computerId)
      .Select(DetectedComputerSoftwareResponse.Projection)
      .ToNonAsyncEnumerable();

    return Ok(result);
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.GetEphemeralAgent)]
  public async Task<ActionResult<GetEphemeralAgentResponse>> GetEphemeralAgent(
    [FromRoute] int computerId,
    [FromServices] IEphemeralAgentSessionStore ephemeralAgentSessionHandler
    )
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);

    if (!ephemeralAgentSessionHandler.TryGetEphemeralAgentSession(computerId, out var session))
    {
      return NoContent();
    }

    var res = new GetEphemeralAgentResponse(
      session.Id,
      session.IsConnected,
      session.TotalSentBytes,
      session.TotalReceivedBytes,
      session.TotalProcessedScriptCount,
      session.StartupInfo,
      session.LastActivity,
      session.CreatedAt);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpDelete(ComputerApiRoutes.DeleteEphemeralAgent)]
  public async Task<ActionResult> DeleteEphemeralAgent(
    [FromRoute] int computerId,
    [FromServices] IEphemeralAgentSessionStore ephemeralAgentSessionHandler)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePermission>(
      new DefaultKeyParameters(computerId),
      true);

    if (!ephemeralAgentSessionHandler.TryGetEphemeralAgentSession(computerId, out _))
    {
      return NotFound();
    }

    ephemeralAgentSessionHandler.EndEphemeralSession(computerId);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.GetEphemeralAgentCircuitBreaker)]
  public async Task<ActionResult<CircuitBreakerState<OpResult>>> GetEphemeralAgentCircuitBreaker(
    [FromRoute] int computerId,
    [FromServices] IPolicyRegistry<string> policyRegistry,
    [FromServices] KeyedLocker keyedLocker,
    CancellationToken token)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var policy = (ICircuitBreakerPolicy<OpResult>) await policyRegistry.GetEphemeralAgentPolicyAsync(keyedLocker, computerId, token);

    var circuitBreaker = new CircuitBreakerState<OpResult>(policy.PolicyKey,
      policy.CircuitState.ToString(),
      LastException: null,
      LastResult: policy.LastHandledResult);

    return Ok(circuitBreaker);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ComputerApiRoutes.ResetEphemeralAgentCircuitBreaker)]
  public async Task<ActionResult<CircuitBreakerState<OpResult>>> ResetEphemeralAgentCircuitBreaker(
    [FromRoute] int computerId,
    [FromServices] IPolicyRegistry<string> policyRegistry,
    [FromServices] KeyedLocker keyedLocker,
    CancellationToken token)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePermission>(
      new DefaultKeyParameters(computerId),
      true);

    var policy = (ICircuitBreakerPolicy)await policyRegistry.GetEphemeralAgentPolicyAsync(keyedLocker, computerId, token);

    policy.Reset();

    var circuitBreaker = new CircuitBreakerState<OpResult>(policy.PolicyKey, policy.CircuitState.ToString());

    return Ok(circuitBreaker);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ComputerApiRoutes.RestoreComputers)]
  public async Task<ActionResult<bool>> RestoreComputers(
    [FromServices] ImmybotDbContext ctx,
  [FromServices] IRestoreComputersCmd cmd,
  [FromBody] int[] computerIds)
  {
    var q = ctx.Computers.AsNoTracking();
    var filter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersManagePermission>();
    q = q.Where(filter);
    var permissibleComputerIds = await q.Where(a => computerIds.Contains(a.Id)).Select(a => a.Id).ToListAsync();
    return await cmd.Run(permissibleComputerIds);
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.GetAll)]
  public ActionResult<IEnumerable<ComputerSearch>> GetAll(
    [FromServices] ImmybotDbContext ctx,
    [FromQuery] string? name = null,
    [FromQuery] int? tenantId = null,
    [FromQuery] bool orderByUpdatedDate = false,
    [FromQuery] int pageSize = 25)
  {
    var q = ctx.GetAllComputers(tenantId, includeComputersForDisabledProviderLinks: true);
    var filter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersManagePermission>();
    q = q.Where(filter);

    var searchQuery = q
      .AsComputerSearch()
      .Where(a => EF.Functions.ILike(a.Name, "%" + name + "%"));

    searchQuery = orderByUpdatedDate
      ? searchQuery.OrderByDescending(a => a.UpdatedDate)
      : searchQuery.OrderByDescending(a => a.Name);

    searchQuery = searchQuery.Take(pageSize);

    return Ok(searchQuery);
  }

  /// <summary>
  /// TODO: Move this to V2 api routes or make the existing GetAll rely on this
  /// </summary>
  /// <returns></returns>
  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.GetAllPaged)]
  public async Task<ActionResult<ComputerPageResponse>> GetAllPaged(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ICachedSingleton<ApplicationPreferences> cachedPrefs,
    [FromServices] IRawSqlPermissionFilterBuilder rawSqlPermissionFilterBuilder,
    [FromQuery] string? filter = null,
    [FromQuery] int? skip = null,
    [FromQuery] string? sort = null,
    [FromQuery] int take = 50,
    [FromQuery] bool sortDesc = true,
    [FromQuery] bool onboardingOnly = false,
    [FromQuery] bool staleOnly = false,
    [FromQuery] bool devLabOnly = false,
    [FromQuery] bool includeOffline = true,
    [FromQuery] int? tenantId = null,
    [FromQuery] bool licensedOnly = false,
    [FromQuery] bool deletedOnly = false)
  {
    int? staleAgeDays = null;
    if (staleOnly)
    {
      staleAgeDays = cachedPrefs.Value.StaleComputersLastAgentConnectionAgeDays;
    }

    if (tenantId.HasValue)
    {
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IComputersViewPermission>(
        User,
        tenantId.Value,
        strict: true);
    }

    // Generate RBAC filter for raw SQL
    // This will handle the resource and tenant id filters
    var rbacFilter =
      rawSqlPermissionFilterBuilder.BuildSqlFilter<Computer, IComputersViewPermission>(resourceTableAlias: "c");

    // get count without sort, skip, take for pagination
    var count = await ctx.GetComputerListViewModelCount(
      filter: filter,
      onboardingOnly: onboardingOnly,
      staleOnly: staleOnly,
      staleAgeDays: staleAgeDays,
      devLabOnly: devLabOnly,
      includeOffline: includeOffline,
      licensedOnly: licensedOnly,
      deletedOnly: deletedOnly,
      rbacFilter: rbacFilter,
      tenantId: tenantId,
      forCount: true);

    // get results
    var q = ctx.GetComputerListViewModels(
      filter: filter,
      take: take,
      skip: skip,
      sort: sort,
      sortDesc: sortDesc,
      onboardingOnly: onboardingOnly,
      staleOnly: staleOnly,
      staleAgeDays: staleAgeDays,
      devLabOnly: devLabOnly,
      includeOffline: includeOffline,
      licensedOnly: licensedOnly,
      deletedOnly: deletedOnly,
      tenantId: tenantId,
      rbacFilter: rbacFilter);

    return Ok(new ComputerPageResponse(count, q));
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.Dx)]
  public ActionResult<LoadResult> DxGet(
    [FromServices] ImmybotDbContext ctx,
    DataSourceLoadOptions loadOptions)
  {
    // Build permission-based filter expression for database-level filtering
    var permissionFilter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();

    // Apply the permission filter to Computer entities before projection
    // This replaces the previous tenant-based filtering with comprehensive RBAC filtering
    var q = ctx.GetAllComputers() // No need to pass in tenant id - let permission filter handle access control
      .Where(permissionFilter)
      .Select(DxComputer.Populate);

    if (loadOptions.Group?.Any() ?? false)
    {
      // this fixes a strange issue with the data loader throwing an error when grouping
      // https://github.com/DevExpress/DevExtreme.AspNet.Data/issues/428
      return Ok(DataSourceLoader.Load(q.ToNonAsyncEnumerable(), loadOptions));
    }

    return Ok(DataSourceLoader.Load(q, loadOptions));
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ComputerApiRoutes.SetExcludedFromUserAffinity)]
  public async Task<ActionResult> SetExcludedFromUserAffinity(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId,
    [FromBody] SetExcludedFromUserAffinityRequestBody request)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePermission>(
      new DefaultKeyParameters(computerId),
      true);

    await ctx.SetExcludedByUserAffinity(computerId, request.IsExcluded);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ComputerApiRoutes.SetExcludedFromUserAffinityBatch)]
  public async Task<ActionResult> SetExcludedFromUserAffinityBatch(
    [FromServices] ImmybotDbContext ctx,
    [FromBody] BatchSetExcludedFromUserAffinityRequestBody request)
  {
    var filter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersManagePermission>();
    var computers = await ctx.GetComputersByIds(request.ComputerIds)
      .Where(filter)
      .Select(a => new { a.TenantId, a.Id })
      .ToListAsync();

    if (computers.Count is 0) return NotFound("No computers were found");

    await ctx.BatchSetExcludedByUserAffinity(request.ComputerIds, request.IsExcluded);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.Get)]
  public async Task<ActionResult<GetComputerResponse>> Get(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IEphemeralAgentSessionStore ephemeralAgentSessionHandler,
    [FromRoute] int computerId,
    [FromQuery] bool includeSessions = false,
    [FromQuery] bool includeAdditionalPersons = true,
    [FromQuery] bool includePrimaryPerson = true,
    [FromQuery] bool includeProviderAgents = false,
    [FromQuery] bool includeProviderAgentsDeviceUpdateFormData = false)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var isEphemeralAgentConnected = ephemeralAgentSessionHandler.DoesComputerHaveAConnectedEphemeralAgentSession(computerId);

    var licensed = ctx.IsComputerLicensed(computerId);

    var computer = ctx.GetExpandedComputerById(
      computerId,
      includeSessions: includeSessions,
      includeAdditionalPersons: includeAdditionalPersons,
      includePrimaryPerson: includePrimaryPerson,
      includeTenant: true,
      includeAgents: includeProviderAgents || includeProviderAgentsDeviceUpdateFormData,
      includeComputersForDisabledProviderLinks: true,
      includeLatestInventoryResults: true,
      includeTags: true,
      ignoreQueryFilters: true,
      includeDeleted: true);

    if (computer is null) return NotFound($"Computer with id {computerId} was not found.");
    if (computer.Computer.DeletedAt is not null)
    {
      return NotFound(computer.Computer.SuccessorComputerId is not null ?
        $"Computer with id {computerId} has been replaced by computer with id {computer.Computer.SuccessorComputerId}" :
        $"Computer with id {computerId} has been deleted.");
    }
    var vm = new GetComputerResponse(
      computer,
      isLicensed: licensed,
      isEphemeralAgentConnected: isEphemeralAgentConnected,
      includeSessions: includeSessions,
      includeAdditionalPersons: includeAdditionalPersons,
      includePrimaryPerson: includePrimaryPerson,
      includeTenant: true,
      includeAgents: includeProviderAgents,
      includeInventory: true,
      includeTags: true);

    return Ok(JsonSerializer.Serialize(vm, _jsonSerializerOptions));
  }

  [SubjectPermissionAuthorize(typeof(IComputersUserAffinityPermission))]
  [HttpGet(ComputerApiRoutes.ExportUserAffinities)]
  public async Task<ActionResult> ExportUserAffinities(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IExcelExporterFactory excelExporterFactory,
    DataSourceLoadOptions loadOptions,
    CancellationToken token)
  {
    // required in order to allow export directly to response stream
    var feature = HttpContext.Features.Get<IHttpBodyControlFeature>();
    if (feature != null)
    {
      feature.AllowSynchronousIO = true;
    }

    var filter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersUserAffinityPermission>();

    var affinities = ctx
      .Computers
      .AsNoTracking()
      .AsSplitQuery()
      .Where(filter)
      .SelectMany(a => a.UserAffinities)
      .Select(ComputerUserAffinityResponse.Project);

    var exporter =
      excelExporterFactory.Create<ComputerUserAffinityResponse>(Response,
        "user-affinity-export.xlsx");
    await exporter.ExportData(affinities, loadOptions, 500, token);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.GetUserAffinitiesDx)]
  public async Task<ActionResult<ComputerUserAffinityResponse[]>> GetUserAffinitiesDx(
    [FromServices] ImmybotDbContext ctx,
    DataSourceLoadOptions loadOptions,
    [FromQuery] int? computerId = null)
  {
    var computers = ctx.Computers
      .AsNoTracking()
      .AsSplitQuery();

    if (computerId is not null)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
        new DefaultKeyParameters(computerId.Value),
        strict: true);
      computers = computers.Where(a => a.Id == computerId.Value);
    }
    else
    {
      var filter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
      computers = computers.Where(filter);
    }

    var affinities = computers
      .SelectMany(a => a.UserAffinities)
      .Select(ComputerUserAffinityResponse.Project);

    return Ok(await DataSourceLoader.LoadAsync(affinities, loadOptions));
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.GetInventoryScriptResult)]
  public async Task<ActionResult<object>> GetInventoryScriptResult(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId,
    [FromRoute] string inventoryKey)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var result = await ctx.GetLatestInventoryResultForComputer(computerId, inventoryKey);
    if (result == null) return NotFound();

    Response.StatusCode = 200;
    Response.ContentType = "application/json";
    var writer = new Utf8JsonWriter(Response.Body);
    writer.WriteStartObject();
    GetComputerResponse.WriteInventoryScriptResult(writer, result, includeInventoryTaskMeta: true);
    writer.WriteEndObject();

    // Disposing writer will automatically flush the contents to the stream. However, the regular
    // Utf8JsonWriter#Dispose method calls `Flush` on the underlying stream, which is not allowed
    await writer.DisposeAsync();

    return new EmptyResult();
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.GetDeviceUpdateFormData)]
  public async Task<ActionResult<GetComputerDeviceUpdateFormDataResponse>> GetDeviceUpdateFormData(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int computerId,
    CancellationToken cancellationToken)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var agents = ctx.Computers
      .AsNoTracking()
      .IgnoreQueryFiltersExceptSoftDelete()
      .Where(a => a.Id == computerId)
      .Include(a => a.Agents)
      .ThenInclude(a => a.ProviderLink)
      .SelectMany(a => a.Agents);

    var res = new GetComputerDeviceUpdateFormDataResponse();
    List<Task> tasks = [];
    foreach (var agent in agents)
    {
      tasks.Add(Task.Run(async () =>
      {
        IProvider? provider = null;
        try
        {
          if (agent.ProviderLink != null)
            provider = await providerActions.GetProvider(agent.ProviderLink, cancellationToken);
        }
        catch
        {
          return;
        }

        if (provider is ISupportsUpdatingAgents agentUpdatingProvider)
        {
          var formData = await agentUpdatingProvider.GetAgentUpdateFormData(agent, cancellationToken);
          res.Devices.Add(new() { AgentId = agent.Id, DeviceUpdateFormData = formData });
        }
      }, cancellationToken));
    }

    await Task.WhenAll(tasks);

    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.GetOnboarding)]
  public ActionResult<IEnumerable<GetOnboardingComputerResponse>> GetOnboarding(
    [FromServices] ImmybotDbContext ctx)
  {
    var filter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
    var computers = ctx.GetOnboardingComputers(includeAgents: true, includeTenant: true, includeSessions: true)
      .Where(filter)
      .Select(c => new GetOnboardingComputerResponse(c))
      .OrderByDescending(c => c.UpdatedDate);
    return Ok(computers);
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.GetComputerOnlineStatus)]
  public async Task<ActionResult<bool>> GetComputerOnlineStatus(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);
    var computer = ctx.GetComputerById(computerId, includeAgents: true);
    if (computer == null) return NotFound();
    return Ok(computer.GetRunScriptAgents().Any(c => c.IsOnline));
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePrimaryPersonPermission))]
  [HttpPost(ComputerApiRoutes.UpdatePrimaryPerson)]
  public async Task<ActionResult<GetPersonResponse>> UpdatePrimaryPerson(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId,
    [FromBody] UpdateComputerPrimaryPersonRequestBody viewModel)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePrimaryPersonPermission>(
      new DefaultKeyParameters(computerId),
      true);

    GetPersonResponse? res = null;
    var computer = ctx.GetComputerById(computerId);
    if (computer == null) return NotFound();
    var user = userService.GetCurrentUser();
    if (viewModel.PrimaryPersonId.HasValue)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Person, IPersonsViewPermission>(
        new DefaultKeyParameters(viewModel.PrimaryPersonId.Value),
        true);
      var person = ctx.GetPersonById(viewModel.PrimaryPersonId.Value);
      if (person == null) return NotFound();
      res = new GetPersonResponse(person);
      computer.PrimaryPersonId = viewModel.PrimaryPersonId;
    }
    else
    {
      computer.PrimaryPersonId = null;
    }

    await ctx.SetComputerPrimaryPersonId(computerId,
      viewModel.PrimaryPersonId,
      new AuditUserDetails(user.Id, user.PrincipalId, user.DisplayName));

    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePrimaryPersonPermission))]
  [HttpPost(ComputerApiRoutes.UpdateAdditionalPersons)]
  public async Task<ActionResult> UpdateAdditionalPersons(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId,
    [FromBody] UpdateComputerAdditionalPersonsRequestBody viewModel)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePrimaryPersonPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var computer = ctx.GetComputerById(computerId, includeAdditionalPersons: true);
    if (computer == null) return NotFound();

    var newAdditionalPersons = new HashSet<ComputerPerson>();
    foreach (var personId in viewModel.AdditionalPersonIds)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Person, IPersonsViewPermission>(
        new DefaultKeyParameters(personId),
        true);
      var person = ctx.GetPersonById(personId);
      if (person == null) return NotFound("Person not found");
      newAdditionalPersons.Add(new ComputerPerson()
      {
        ComputerId = computerId,
        PersonId = personId
      });
    }

    computer.AdditionalPersons = newAdditionalPersons;
    ctx.UpdateComputer(computer);
    return Ok();
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPut(ComputerApiRoutes.Update)]
  public async Task<ActionResult<GetComputerResponse>> UpdateComputer(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IEphemeralAgentSessionStore ephemeralAgentSessionHandler,
    [FromServices] IProviderActions providerActions,
    [FromServices] IDomainEventBroker eventBroker,
    [FromServices] IMaintenanceSessionActions maintenanceSessionActions,
    [FromServices] IChangeTenantForComputersCmd changeTenantForComputersCmd,
    [FromRoute] int computerId,
    [FromBody] UpdateComputerRequestBody viewModel)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePermission>(
      new DefaultKeyParameters(computerId),
      true);

    // check if user can update computer
    var computer = ctx.GetComputerById(computerId,
      includeAdditionalPersons: true,
      includeAgents: true,
      asNoTracking: true);

    if (computer == null) return NotFound();

    if (maintenanceSessionActions.TryGetRunningSessionForComputer(computerId, out _))
    {
      return Conflict(new HttpProblem(
        title: "The computer cannot be updated",
        detail: "This computer cannot be updated at this time because there is currently a maintenance session running for it"));
    }

    // update onboarding
    if (computer.OnboardingStatus != viewModel.OnboardingStatus)
    {
      computer.OnboardingStatus = viewModel.OnboardingStatus;
      if (viewModel.OnboardingStatus == ComputerOnboardingStatus.Onboarded && computer.OnboardedDateUtc == null)
      {
        computer.OnboardedDateUtc = DateTime.UtcNow;
      }
      ctx.SetComputerOnboardingStatus(computer.Id, viewModel.OnboardingStatus);
    }

    if (viewModel.ProviderLinkUpdates.Count > 0)
    {
      var updateTasks = new List<Task<AgentUpdateResult>>();
      // update agents with new client ids and agent update form data
      foreach (var agent in computer.Agents.ToList())
      {
        var providerLinkUpdate = viewModel.ProviderLinkUpdates
          .Find(a => a.ProviderLinkId == agent.ProviderLinkId);
        if (providerLinkUpdate == null) continue;

        var providerLink = ctx.GetProviderLink(agent.ProviderLinkId);
        if (providerLink == null) continue;

        var updateClientTo =
            !string.IsNullOrEmpty(providerLinkUpdate.ClientId) &&
            providerLinkUpdate.ClientId != agent.ExternalClientId
          ? await ctx.GetClientsForProviderLink(providerLinkUpdate.ProviderLinkId)
            .FirstOrDefaultAsync(c => c.ExternalClientId == providerLinkUpdate.ClientId)
          : null;
        updateTasks.Add(providerActions.HandleAgentUpdate(
          providerLink,
          agent,
          new AgentUpdateDto
          {
            UpdateClientTo = updateClientTo,
            AgentUpdateFormData = providerLinkUpdate.DeviceUpdateFormData,
          },
          CancellationToken.None));
      }
      await Task.WhenAll(updateTasks);
      computer.Agents = ctx.GetAgentsForComputer(computer.Id) ?? [];
    }

    await changeTenantForComputersCmd.RunAsync(
      new ChangeTenantsPayload(
        viewModel.TenantId,
        [computerId]));
    computer.TenantId = viewModel.TenantId;

    // assign primary person for computer
    if (viewModel.PrimaryPersonId != computer.PrimaryPersonId && viewModel.PrimaryPersonId is { } primaryPersonId)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Person, IPersonsViewPermission>(
        new DefaultKeyParameters(primaryPersonId),
        true);
      var person = ctx.GetPersonById(primaryPersonId);
      if (person == null) return NotFound("Person not found matching primary person id");
      var currentUser = userService.GetCurrentUser();
      await ctx.SetComputerPrimaryPersonId(
        computer.Id,
        primaryPersonId,
        new AuditUserDetails(currentUser.Id,
          currentUser.PrincipalId,
          currentUser.DisplayName));
    }

    // assign additional persons
    var additionalPersons = new HashSet<int>();
    foreach (var personId in viewModel.AdditionalPersonIds)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Person, IPersonsViewPermission>(
        new DefaultKeyParameters(personId),
        true);
      var person = ctx.GetPersonById(personId);
      if (person == null) continue;
      additionalPersons.Add(personId);
    }
    ctx.UpdateComputerPersonsForComputer(computer.Id, additionalPersons);

    // update onboarding count
    var numOnboarding = ctx.ComputerOnboardingCountForTenant(computer.TenantId);
    eventBroker.NumOnboardingComputersChangedForTenant(computer.TenantId, numOnboarding);
    eventBroker.NumOnboardingComputersChanged(ctx.ComputerOnboardingCount());

    return await Get(
      ctx,
      ephemeralAgentSessionHandler,
      computerId,
      includeAdditionalPersons: true,
      includePrimaryPerson: true,
      includeProviderAgents: true,
      includeProviderAgentsDeviceUpdateFormData: true);
  }

  [SubjectPermissionAuthorize(typeof(IComputersOnboardingPermission))]
  [HttpPost(ComputerApiRoutes.SetToNeedsOnboarding)]
  public async Task<ActionResult> SetToNeedsOnboarding(
    [FromServices] IDomainEventBroker domainEventBroker,
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersOnboardingPermission>(
      new DefaultKeyParameters(computerId),
      true);

    ctx.SetComputerOnboardingStatus(computerId, ComputerOnboardingStatus.NeedsOnboarding);
    var onboarding = ctx.GetOnboardingComputers();
    var mspCount = await onboarding.CountAsync();
    var tenantId = userService.GetTenantId();
    var tenantCount = await onboarding.Where(a => a.TenantId == tenantId).CountAsync();
    domainEventBroker.NumOnboardingComputersChanged(mspCount);
    domainEventBroker.NumComputersChangedForTenant(tenantId, tenantCount);
    return Ok();
  }

  [SubjectPermissionAuthorize(typeof(IComputersChangeTenantPermission))]
  [HttpPost(ComputerApiRoutes.ChangeTenant)]
  public async Task<ActionResult<CommandResult>> ChangeTenant(
  [FromBody] ChangeTenantsPayload request,
  [FromServices] IChangeTenantForComputersCmd command,
  [FromServices] ImmybotDbContext ctx)
  {
    foreach (var computerId in request.ComputersToMove)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersChangeTenantPermission>(
        new DefaultKeyParameters(computerId),
        strict: true);
    }

    await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IComputersOnboardingPermission>(
      User,
      request.TargetTenantId,
      strict: true);

    var dest = request.TargetTenantId;
    if (dest == 0) return BadRequest("Please select the tenant you want to move the computer(s) to");

    var destTenant = await ctx.Tenants.FirstOrDefaultAsync(a => a.Id == dest);
    if (destTenant == null) return BadRequest("We can't find the tenant you selected");

    var result = await command.RunAsync(request);
    return Ok(result);
  }

  [SubjectPermissionAuthorize(typeof(IComputersOnboardingPermission))]
  [HttpPost(ComputerApiRoutes.SkipOnboarding)]
  public async Task<ActionResult<IEnumerable<SkipOnboardingResponse>>> SkipOnboarding(
    [FromBody] List<SkipOnboardingRequest> request,
    [FromServices] IComputerActions computerActions,
    [FromServices] IDomainEventBroker domainEventBroker,
    [FromServices] ImmybotDbContext ctx)
  {
    if (request.Count is 0) return BadRequest();

    foreach (var req in request)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersOnboardingPermission>(
        new DefaultKeyParameters(req.ComputerId),
        strict: true);
    }

    List<SkipOnboardingResponse> response = [];
    List<Computer> updateableComputers = [];

    var computers = await ctx.GetComputersByIds(request.Select(a => a.ComputerId).ToArray()).ToListAsync();

    // only update computers that user is authorized to see
    foreach (var computer in computers)
    {
      if (computer.OnboardingStatus != ComputerOnboardingStatus.Onboarded)
      {
        computer.OnboardingStatus = ComputerOnboardingStatus.Onboarded;
        computer.OnboardedDateUtc ??= DateTime.UtcNow;

        updateableComputers.Add(computer);
      }
      else
      {
        // handle computers already onboarded
        response.Add(new SkipOnboardingResponse(computer.Id, true, "Already onboarded"));
      }
    }

    if (updateableComputers.Count != 0)
    {
      computerActions.UpdateComputers(computers);
      foreach (var computer in updateableComputers)
      {
        // handle computers successfully onboarded
        response.Add(new SkipOnboardingResponse(computer.Id, true, "Onboarded"));
      }
    }

    var mspCount = await ctx.GetOnboardingComputers().CountAsync();
    var tenantId = userService.GetTenantId();
    var tenantCount = await ctx.GetOnboardingComputers().Where(a => a.TenantId == tenantId).CountAsync();
    domainEventBroker.NumOnboardingComputersChanged(mspCount);
    domainEventBroker.NumComputersChangedForTenant(tenantId, tenantCount);

    return Ok(response);
  }

  [SubjectPermissionAuthorize(typeof(IComputersRemoteAccessPermission))]
  [HttpGet(ComputerApiRoutes.GetScreenShareUrl)]
  public async Task<ActionResult<Uri?>> GetScreenShareUrl(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ICachedSingleton<ApplicationPreferences> cachedPrefs,
    [FromServices] ICachedCollection<TenantPreferences> cachedTenantPrefs,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int computerId,
    [FromRoute] int providerLinkId,
    CancellationToken token)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersRemoteAccessPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var computer = ctx.GetComputerById(computerId, includeAgents: true);
    if (computer == null) return NotFound();

    var agent = computer.Agents.FirstOrDefault(a => a.ProviderLinkId == providerLinkId);
    if (agent == null || agent.ProviderLink == null) return NotFound();

    var provider = await providerActions.GetProvider(agent.ProviderLink, token);
    token.ThrowIfCancellationRequested();
    if (provider is not ISupportsScreenShare remoteUrlProvider)
    {
      return BadRequest("Provider does not support opening a remote url");
    }

    var displayName = userService.GetCurrentUser().DisplayName ?? throw new MissingPrincipalException();
    var tenantPrefs = cachedTenantPrefs.Value.FirstOrDefault(a => a.TenantId == computer.TenantId);
    var requiresConsent = tenantPrefs?.RequireConsentForExternalSessionProviders ?? cachedPrefs.Value.RequireConsentForExternalSessionProviders;
    var url = await remoteUrlProvider.GetRemoteUrl(agent, computerId, displayName, requiresConsent, "", "", -1, token);
    return Ok(url.Value);
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpPost(ComputerApiRoutes.ReinventoryComputer)]
  public async Task<ActionResult<InventoryDeviceCmdResponse>> ReinventoryComputer(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IInventoryDeviceCmd inventoryDeviceCmd,
    [FromRoute] int computerId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var computer = ctx.GetComputerById(computerId);
    if (computer == null) return NotFound();

    var result = await inventoryDeviceCmd.Run(new InventoryDeviceCmdPayload(
      computerDeviceId: computer.DeviceId,
      inventoryTasks: null,
      inventoryKeys: null,
      runOnlyOutdated: false), CancellationToken.None);

    return Ok(result);
  }

  [SubjectPermissionAuthorize(typeof(IComputersOnboardingPermission))]
  [HttpGet(ComputerApiRoutes.ResolveOnboardingOverridableTargetAssignments)]
  public async Task<ActionResult<ResolveOnboardingOverridableAssignmentsResponseBody>> ResolveOnboardingOverridableTargetAssignments(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ITargetAssignmentResolver resolver,
    [FromServices] IRunContextFactory runContextFactory,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromRoute] int computerId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersOnboardingPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var currentUser = userService.GetCurrentUser();
    using var context = await runContextFactory.GenerateComputerOneOffRunContext(computerId,
      appLifetime.ApplicationStopping,
      manuallyTriggeredBy: currentUser);
    var (overridableAssignments, nextHighestPriorityAssignments) = await resolver
      .GetOnboardingOverridableTargetAssignments(context);
    return Ok(new ResolveOnboardingOverridableAssignmentsResponseBody(
      overridableAssignments
        .Select(a => (TargetAssignmentResource)(a.DatabaseType == DatabaseType.Global ? new GlobalTargetAssignmentResource(a) : new LocalTargetAssignmentResource(a)))
        .ToList(),
      nextHighestPriorityAssignments
        .Select(a => (TargetAssignmentResource)(a.DatabaseType == DatabaseType.Global ? new GlobalTargetAssignmentResource(a) : new LocalTargetAssignmentResource(a)))
        .ToList()));
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ComputerApiRoutes.DeleteComputers)]
  public async Task<ActionResult<BulkDeleteResponse>> DeleteComputers(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IDevLabActions devLabActions,
    [FromServices] IDeleteComputerCmd cmd,
    [FromBody] BulkDeleteRequest req,
    CancellationToken token)
  {
    if (req.Ids.Count == 0) return BadRequest("No computers were specified to be deleted");

    var filter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersManagePermission>();
    var computers = await ctx.GetComputersByIds(req.Ids, includeSoftDeleted: req.Permanent)
      .Where(filter)
      .Select(a => new { a.Id, a.TenantId, a.DevLabVmName, a.DevLabVmUnclaimed, a.DevLabVmClaimExpirationDateUtc, a.ComputerName })
      .ToListAsync(token);

    if (computers.Count == 0) return Ok(new BulkDeleteResponse(0));

    var user = userService.GetCurrentUser().DisplayName ?? "user";

    if (req.Permanent)
    {
      await cmd.HardDelete(computers.Select(a => a.Id), token);
    }
    else
    {
      await cmd.SoftDelete(computers.Select(a => a.Id), reason: $"Deleted by {user}");
    }

    // if any computers are claimed dev lab machines, unclaim them
    foreach (var c in computers.Where(a => !string.IsNullOrEmpty(a.DevLabVmName)))
    {
      if (string.IsNullOrEmpty(c.DevLabVmName)) continue;
      await devLabActions.UnclaimVm(c.Id, c.DevLabVmName, token);
    }

    return Ok(new BulkDeleteResponse(computers.Count));
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.GetMyComputers)]
  public ActionResult<IEnumerable<MyComputerResponse>> GetMyComputers(
    [FromServices] ImmybotDbContext ctx)
  {
    var personId = userService.GetCurrentUser().PersonId;
    if (!personId.HasValue) return BadRequest("User is not associated with a person");
    var computers = ctx.GetComputersByPrimaryPersonId(personId.Value)
      .Select(a => new MyComputerResponse
      {
        Id = a.Id,
        Name = a.ComputerName ?? a.DeviceId.ToString(),
        IsOnline = a.Agents.Where(p => p.SupportsRunningScripts).Any(r => r.IsOnline && r.ProviderLink != null && !r.ProviderLink.Disabled && r.ProviderLink.HealthStatus == HealthStatus.Healthy)
      });
    return Ok(computers);
  }

  [SubjectPermissionAuthorize(typeof(IComputersInventorySearchPermission))]
  [HttpGet(ComputerApiRoutes.SearchInventorySoftwareByUpgradeCode)]
  public ActionResult<IEnumerable<ComputerInventorySoftware>> SearchInventorySoftwareByUpgradeCode(
    [FromServices] ImmybotDbContext ctx,
    [FromQuery] Guid q)
  {
    var query = ctx.ComputerInventorySoftware.Where(a => a.UpgradeCode == q);
    return Ok(query);
  }

  [SubjectPermissionAuthorize(typeof(IComputersInventorySearchPermission))]
  [HttpGet(ComputerApiRoutes.SearchInventorySoftwareByName)]
  public ActionResult<IEnumerable<ComputerInventorySoftware>> SearchInventorySoftwareByName(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IMachineSoftwareOperations ops,
    [FromQuery] string q,
    [FromQuery] SoftwareTableNameSearchMode searchMode = SoftwareTableNameSearchMode.Contains)
  {
    IQueryable<ComputerInventorySoftware> query;
    switch (searchMode)
    {
      case SoftwareTableNameSearchMode.Contains:
        query = ctx.ComputerInventorySoftware.Where(a => a.DisplayName != null && EF.Functions.ILike(a.DisplayName, $"%{q}%"));
        break;
      case SoftwareTableNameSearchMode.Regex:
        var regexMatcher = ops.MakeRegexMatcher(q);
        var s = ctx.ComputerInventorySoftware
          .Where(a => a.DisplayName != null && Regex.IsMatch(a.DisplayName, StripNamedCaptureGroups(q), RegexOptions.IgnoreCase))
          .OrderBy(a => a.DisplayName)
          .Take(1000)
          .AsEnumerable()
          .Select(r =>
          {
            if (regexMatcher(r.DisplayName, out var displayVersion))
            {
              return new
              {
                r.DisplayName,
                r.DisplayVersion,
                r.UpgradeCode,
                r.Computers,
                CapturedDisplayVersion = displayVersion,
              };
            }
            return (object)r;
          });
        return Ok(s);
      case SoftwareTableNameSearchMode.Traditional:
        var regFix = "^" + Regex.Replace(q.Trim(), "\\*", ".*") + "$";
        query = ctx.ComputerInventorySoftware
          .Where(a => a.DisplayName != null && Regex.IsMatch(a.DisplayName, StripNamedCaptureGroups(regFix), RegexOptions.IgnoreCase));
        break;
      default:
        return BadRequest();
    }
    return Ok(query.OrderBy(a => a.DisplayName).Take(1000));
  }

  [SubjectPermissionAuthorize(typeof(IComputersInventorySearchPermission))]
  [HttpGet(ComputerApiRoutes.SearchAllInventorySoftwareByName)]
  public ActionResult<IEnumerable<ComputerInventoryAllSoftware>> SearchAllInventorySoftwareByName(
    [FromServices] ImmybotDbContext ctx,
    [FromQuery] string q,
    [FromQuery] int? tenantId = null,
    [FromQuery] SoftwareTableNameSearchMode searchMode = SoftwareTableNameSearchMode.Contains)
  {
    if (string.IsNullOrEmpty(q)) return BadRequest("q parameter is required");

    tenantId ??= userService.GetTenantId();

    var query = ctx.ComputerInventoryAllSoftware.Where(a => a.TenantId == tenantId.Value);
    switch (searchMode)
    {
      case SoftwareTableNameSearchMode.Contains:
        query = query.Where(a => a.SoftwareName != null && EF.Functions.ILike(a.SoftwareName, $"%{q}%"));
        break;
      case SoftwareTableNameSearchMode.Regex:
        query = query
          .Where(a => a.SoftwareName != null && Regex.IsMatch(a.SoftwareName, StripNamedCaptureGroups(q), RegexOptions.IgnoreCase));
        break;
      case SoftwareTableNameSearchMode.Traditional:
        var regFix = "^" + Regex.Replace(q.Trim(), "\\*", ".*") + "$";
        query = query
          .Where(a => a.SoftwareName != null && Regex.IsMatch(a.SoftwareName, StripNamedCaptureGroups(regFix), RegexOptions.IgnoreCase));
        break;
      default:
        return BadRequest();
    }

    return Ok(query.ToNonAsyncEnumerable());
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.GetComputerEvents)]
  public async Task<ActionResult<TimelineEvent[]>> GetComputerEvents(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId,
    [FromQuery] int? skip,
    [FromQuery] int take = 25)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);

    // get all events for the computer from the past 30 days.
    // Use DateTime.Today directly in the query to take advantage of the database's timezone handling.
    var events = await ctx.TimelineEvents
      .AsNoTracking()
      .Where(a => a.ObjectType == TimelineObjectType.Computer && a.ObjectId == computerId.ToString() &&
                  a.DateUTC >= DateTime.Today.AddDays(-30))
      .OrderByDescending(a => a.DateUTC)
      .PipeIf<IQueryable<TimelineEvent>>(skip.HasValue, q => q.Skip(skip!.Value))
      .Take(take)
      .ToListAsync();

    // get all events for the provider agents of this computer
    var agentIds = await ctx.ProviderAgents
      .AsNoTracking()
      .Where(a => a.ComputerId == computerId)
      .Select(a => a.Id.ToString())
      .ToListAsync();

    // get all provider events for the agents of this computer from the past 30 days.
    var agentEvents = await ctx.TimelineEvents
      .AsNoTracking()
      .Where(a => a.ObjectType == TimelineObjectType.ProviderAgent && agentIds.Contains(a.ObjectId) &&
                  a.DateUTC >= DateTime.Today.AddDays(-30))
      .OrderByDescending(a => a.DateUTC)
      .PipeIf<IQueryable<TimelineEvent>>(skip.HasValue, q => q.Skip(skip!.Value))
      .Take(take)
      .ToListAsync();

    // we'll be returning up to 2x of the take, but that's fine here.
    var combined = events.Concat(agentEvents).OrderByDescending(a => a.DateUTC).ToList();
    return Ok(combined);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ComputerApiRoutes.AddTags)]
  public async Task<ActionResult> AddTags(
    [FromServices] ImmybotDbContext ctx,
    [FromBody] AddTagsRequest request)
  {
    var ids = request.EntityIds;

    if (ids.Count == 0) return BadRequest("No computer ids were provided");
    if (request.TagIds.Count == 0) return BadRequest("No tag ids were provided");

    foreach (var id in ids)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePermission>(
        new DefaultKeyParameters(id),
        strict: true);
    }

    foreach (var tagId in request.TagIds)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tag, ITagsViewPermission>(
        new DefaultKeyParameters(tagId),
        strict: true);
    }

    var computers = await ctx.GetComputersByIds(ids).Select(a => new { a.TenantId, a.Id }).ToListAsync();
    if (computers.Count == 0) return NotFound();

    var tags = await ctx.GetTagsByIds(request.TagIds).ToListAsync();
    if (tags.Count == 0) return NotFound();

    await ctx.AddEntityTag<ComputerTag>(tags.Select(a => a.Id).ToList(), computers.Select(a => a.Id).ToList());

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ComputerApiRoutes.RemoveTags)]
  public async Task<ActionResult> RemoveTags(
    [FromServices] ImmybotDbContext ctx,
    [FromBody] RemoveTagsRequest request)
  {
    if (!ModelState.IsValid) return BadRequest(ModelState.Values.SelectMany(v => v.Errors));
    var ids = request.EntityIds;

    if (ids.Count == 0) return BadRequest("No computer ids were provided");
    if (request.TagIds.Count == 0) return BadRequest("No tag ids were provided");

    foreach (var id in ids)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePermission>(
        new DefaultKeyParameters(id),
        strict: true);
    }

    foreach (var tagId in request.TagIds)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tag, ITagsViewPermission>(
        new DefaultKeyParameters(tagId),
        strict: true);
    }

    var computers = await ctx.GetComputersByIds(ids).Select(a => new { a.TenantId, a.Id }).ToListAsync();
    if (computers.Count == 0) return NotFound();

    var tags = await ctx.GetTagsByIds(request.TagIds).ToListAsync();
    if (tags.Count == 0) return NotFound();

    await ctx.RemoveEntityTags<ComputerTag>(tags.Select(a => a.Id).ToList(), computers.Select(a => a.Id).ToList());
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ComputerApiRoutes.ExcludeFromMaintenance)]
  public async Task<ActionResult> ExcludeFromMaintenance(
    [FromServices] ImmybotDbContext ctx,
    [FromBody] ExcludeFromMaintenanceRequest req,
    [FromRoute] int computerId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePermission>(
      new DefaultKeyParameters(computerId),
      true);

    var computer = ctx.GetComputerById(computerId);
    if (computer == null) return NotFound();

    await ctx.Computers
      .AsNoTracking()
      .IgnoreQueryFilters()
      .Where(c => new[] { computerId }.Contains(c.Id))
      .UpdateFromQueryAsync(a => new Computer { ExcludeFromMaintenance = req.Exclude });

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IComputersExportPermission))]
  [HttpGet(ComputerApiRoutes.ExportComputers)]
  public async Task<ActionResult> ExportComputers(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IExcelExporterFactory excelExporterFactory,
    DataSourceLoadOptions loadOptions,
    CancellationToken token)
  {
    // required in order to allow export directly to response stream
    var feature = HttpContext.Features.Get<IHttpBodyControlFeature>();
    if (feature != null) feature.AllowSynchronousIO = true;

    var filter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersExportPermission>();
    var q = ctx.Computers
      .Where(filter)
      .AsNoTracking();

    var computers = q
      .IncludeTenantAndAzData()
      .Select(DxComputer.Populate);

    var exporter = excelExporterFactory.Create<DxComputer>(Response, "computer-export.xlsx");
    await exporter.ExportData(computers, loadOptions, 500, token);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.LaunchEphemeralAgent)]
  public async Task<ActionResult<OpResult>> LaunchEphemeralAgent(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IEphemeralAgentAcquisition agentAcquisition,
    [FromRoute] int computerId,
    CancellationToken token)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var computer = ctx.GetComputerById(computerId, includeAgents: true, asNoTracking: true);
    if (computer == null) return NotFound();
    var res = await agentAcquisition.AcquireEphemeralAgentForComputer(computer, token);
    return Ok(res.ToResult());
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewRegistryPermission))]
  [HttpPost(ComputerApiRoutes.LoadRegistryKeys)]
  public async Task<ActionResult<OpResult<RegistryKeyDto[]>>> LoadRegistryKeys(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IComputerRegistryStore registryStore,
    [FromRoute] int computerId,
    [FromBody] RegistryPayload payload,
    CancellationToken token)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewRegistryPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var computer = ctx.GetComputerById(computerId, includeAgents: true, asNoTracking: true);
    if (computer == null) return NotFound();
    var res = await registryStore.GetRegistryKeysAsync(computer, payload.KeyPath, token);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewRegistryPermission))]
  [HttpPost(ComputerApiRoutes.LoadRegistryKeyValues)]
  public async Task<ActionResult<OpResult<RegistryValueDto[]>>> LoadRegistryKeyValues(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IComputerRegistryStore registryStore,
    [FromRoute] int computerId,
    [FromBody] RegistryPayload payload,
    CancellationToken token)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewRegistryPermission>(
      new DefaultKeyParameters(computerId),
      true);
    var computer = ctx.GetComputerById(computerId, includeAgents: true, asNoTracking: true);
    if (computer == null) return NotFound();
    var res = await registryStore.GetRegistryValuesAsync(computer, payload.KeyPath, token);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ComputerApiRoutes.GetParentTenantInfo)]
  public async Task<ActionResult<ParentTenantInfo>> GetParentTenantInfo(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int computerId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      true);

    var result = await ctx.Computers
      .Where(computer => computer.Id == computerId
                         && computer.Tenant != null
                         && computer.Tenant.ParentTenantId != null)
      .Join(ctx.Tenants,
        computer => computer.Tenant!.ParentTenantId,
        tenant => tenant.Id,
        (computer, tenant) => new
        {
          ComputerTenantId = computer.TenantId,
          ParentTenant = new ParentTenantInfo(tenant.Id, tenant.Name)
        })
      .FirstOrDefaultAsync();

    if (result == null)
      return NotFound();

    return Ok(result.ParentTenant);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ComputerApiRoutes.UpdateNotes)]
  public async Task<ActionResult> UpdateNotes(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromRoute] int computerId,
    [FromBody] UpdateNotesPayload request)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePermission>(
      new DefaultKeyParameters(computerId),
      true);

    await using var ctx = dbFactory();
    var computer = ctx.GetComputerById(computerId);
    if (computer is null) return NotFound();

    var existingNote = await ctx.ComputerNotes
      .FirstOrDefaultAsync(n => n.ComputerId == computerId);

    var oldValue = existingNote?.Content ?? string.Empty;

    if (existingNote is not null)
    {
      existingNote.Content = request.Notes;
    }
    else
    {
      ctx.ComputerNotes.Add(new ComputerNote
      {
        ComputerId = computerId,
        Content = request.Notes
      });
    }

    await ctx.SaveChangesAsync();

    var user = userService.GetCurrentUser();

    await ctx.CreateAudit(new CreateAuditRequest(
      new AuditUserDetails(user.Id, user.PrincipalId, user.DisplayName),
      existingNote is null ? AuditType.Create : AuditType.Update,
      AuditObjectType.Computer,
      new { Notes = oldValue, },
      new { Notes = request.Notes },
      ["Notes"],
      computer.Id,
      computer.ComputerName ?? string.Empty,
      Message: "Adding notes"));

    return NoContent();
  }

  private static string StripNamedCaptureGroups(string q)
  {
    // postgres is not compatible with named capture groups, so we need to remove them from
    // the filter string before using the filter as a regex in an EF query
    // how: replace all occurrences of "(?<...>" with "("
    return Regex.Replace(q, @"\(\?\<\w+\>", "(");
  }

  public record ParentTenantInfo(int Id, string Name);
}
