using System.Text.Json;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.MaintenanceTaskExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Commands.Payloads.TargetAssignments;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Application.SoftwareDbContextExtensions;
using Immybot.Backend.Application.SoftwareDbContextExtensions.MaintenanceTaskExtensions;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Exceptions;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.Lib.JsonConverters;
using Immybot.Backend.Web.Common.Lib.SignalRHubs;
using Immybot.Backend.Web.Common.Lib.SignalRHubs.UserHubResources;
using Immybot.Shared.Primitives;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using MigrateToSupersedingAssignmentWhatIfResponse =
  Immybot.Backend.Web.Common.Contracts.V1.Responses.MigrateToSupersedingAssignmentWhatIfResponse;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class TargetAssignmentsController(
  IResourceAuthorizerFlow resourceAuthorizerFlow,
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IProviderActions providerActions,
  IUserService userService)
  : Controller
{
  private static readonly JsonSerializerOptions _jsonSerializerOptions =
    new() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
  static TargetAssignmentsController()
  {
    _jsonSerializerOptions.Converters.Add(new JObjectConverter());
  }

  private static string? VerifyDesiredSoftwareState(MaintenanceType maintenanceType, DesiredSoftwareState? desiredSoftwareState)
  {
    if (desiredSoftwareState == null && (
      maintenanceType == MaintenanceType.ChocolateySoftware
      || maintenanceType == MaintenanceType.GlobalSoftware
      || maintenanceType == MaintenanceType.LocalSoftware
      || maintenanceType == MaintenanceType.NiniteSoftware))
    {
      return "DesiredSoftwareState cannot be blank for software deployments";
    }
    return null;
  }

  private static string? VerifyTargetAssignment(ITargetAssignmentDetailsBase body)
  {
    return VerifyDesiredSoftwareState(body.MaintenanceType, body.DesiredSoftwareState);
  }

  /// <summary>
  /// Wipe out any data that should not be present and return an error string if any data is missing
  /// </summary>
  /// <param name="request"></param>
  /// <returns></returns>
  private static string? ScrubLocalTargetAssignmentRequest(LocalTargetAssignmentPayload request)
  {
    var err = VerifyDesiredSoftwareState(request.MaintenanceType, request.DesiredSoftwareState);

    // tenant id
    if (TargetTypesRequiringTenant.Contains(request.TargetType))
    {
      if (!request.TenantId.HasValue) err = $"Tenant is required for target type {request.TargetType}";
    }
    else if (!_targetTypesCanHaveTenant.Contains(request.TargetType))
    {
      request.TenantId = null;
    }

    return err;
  }

  private static readonly List<TargetType> _targetTypesCanHaveTenant = [TargetType.ProviderDeviceGroup];

  private static List<TargetType> TargetTypesRequiringTenant =>
  [
    TargetType.AllForTenant,
    TargetType.AzureGroup,
    TargetType.TenantFilterScript,
    TargetType.TenantMetascript,
    TargetType.SpecificTenant,
    TargetType.TenantTag
  ];

  #region Change Requests

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsManageCrossTenantWithChangeRequestsPermission),
    typeof(IChangeRequestManagePermission)
  ])]
  [HttpGet(TargetAssignmentApiRoutes.GetChangeRequestDiff)]
  public async Task<ActionResult<ChangeRequestDiff>> GetChangeRequestDiff(
    [FromRoute] int changeRequestId,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var changeRequest = changeRequestStore.GetChangeRequest(changeRequestId);
    if (changeRequest is null) return NotFound();

    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<ChangeRequest, IChangeRequestManagePermission>(
      new DefaultKeyParameters(changeRequestId),
      strict: true);

    var diff = await changeRequestStore.GetTargetAssignmentChangeRequestDiff(changeRequest, CancellationToken.None);
    return Ok(diff);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManageCrossTenantWithChangeRequestsPermission))]
  [HttpPost(TargetAssignmentApiRoutes.CreateChangeRequestForExistingDeployment)]
  public async Task<ActionResult<ChangeRequestResponse>> CreateChangeRequestForExistingDeployment(
    [FromRoute] int deploymentId,
    [FromBody] UpdateLocalTargetAssignmentPayload body,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var assignment = ctx.GetTargetAssignmentById(deploymentId);
    if (assignment == null) return NotFound();

    if (VerifyTargetAssignment(body) is { } error) return BadRequest(error);
    if (ScrubLocalTargetAssignmentRequest(body) is { } error2) return BadRequest(error2);

    var res = await changeRequestStore.CreateChangeRequestForExistingEntity(
      userService.GetCurrentUser(),
      ChangeRequestObjectType.TargetAssignment,
      assignment,
      deploymentId,
      body,
      CancellationToken.None);

    return Ok(ChangeRequestResponse.CreateResponse(res));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManageCrossTenantWithChangeRequestsPermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateChangeRequestForExistingDeployment)]
  public async Task<ActionResult<ChangeRequestResponse>> UpdateChangeRequestForExistingDeployment(
    [FromRoute] int deploymentId,
    [FromRoute] int changeRequestId,
    [FromBody] UpdateLocalTargetAssignmentPayload body,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IChangeRequestStore changeRequestStore)

  {
    var changeRequest = changeRequestStore.GetChangeRequest(changeRequestId);
    if (changeRequest is null) return NotFound();

    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<ChangeRequest, IChangeRequestManagePermission>(
      new DefaultKeyParameters(changeRequestId),
      strict: true);

    var assignment = ctx.GetTargetAssignmentById(deploymentId);
    if (assignment is null) return NotFound();

    if (VerifyTargetAssignment(body) is { } error) return BadRequest(error);
    if (ScrubLocalTargetAssignmentRequest(body) is { } error2) return BadRequest(error2);

    var res = await changeRequestStore.UpdateChangeRequestForExistingEntity(
      userService.GetCurrentUser(),
      changeRequest,
      assignment,
      body,
      CancellationToken.None);

    return Ok(ChangeRequestResponse.CreateResponse(res));
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsManageCrossTenantWithChangeRequestsPermission),
    typeof(IChangeRequestManagePermission)
  ])]
  [HttpGet(TargetAssignmentApiRoutes.GetChangeRequest)]
  public async Task<ActionResult<ChangeRequestResponse>> GetDeploymentChangeRequest(
    [FromRoute] int changeRequestId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<ChangeRequest, IChangeRequestManagePermission>(
      new DefaultKeyParameters(changeRequestId),
      strict: true);


    var changeRequest = changeRequestStore.GetChangeRequest(changeRequestId);
    if (changeRequest is null) return NotFound();

    return Ok(ChangeRequestResponse.CreateResponse(changeRequest));
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsManageCrossTenantWithChangeRequestsPermission),
    typeof(IChangeRequestManagePermission)
  ])]
  [HttpGet(TargetAssignmentApiRoutes.GetAllChangeRequests)]
  public async Task<ActionResult<IEnumerable<ChangeRequestResponse>>> GetAllChangeRequests(
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var changeRequestsDisposable = changeRequestStore
      .GetChangeRequestsForType(ChangeRequestObjectType.TargetAssignment);
    Response.RegisterForDispose(changeRequestsDisposable);

    var changeRequests = changeRequestsDisposable.Value;

    var currentUser = userService.GetCurrentUser();

    // only users who can manage change requests can see all change requests
    if (await subjectPermissionAuthorizationService.AuthorizeAsync<IChangeRequestManagePermission>(User, strict: false))
    {
      return Ok(changeRequests.Select(ChangeRequestResponse.Projection));
    }

    // otherwise the user can only see change requests they created
    changeRequests = changeRequests.Where(a => a.CreatedBy == currentUser.Id);

    return Ok(changeRequests.Select(ChangeRequestResponse.Projection));
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsManageCrossTenantWithChangeRequestsPermission),
    typeof(IChangeRequestManagePermission)
  ])]
  [HttpGet(TargetAssignmentApiRoutes.GetAllChangeRequestsForDeployment)]
  public async Task<ActionResult<IEnumerable<ChangeRequestResponse>>> GetAllChangeRequestsForDeployment(
    [FromRoute] int deploymentId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IChangeRequestStore changeRequestStore
  )
  {
    var assignment = ctx.GetTargetAssignmentById(deploymentId);
    if (assignment is null) return NotFound();

    var changeRequestsDisposable =
      changeRequestStore.GetChangeRequestsForEntity(ChangeRequestObjectType.TargetAssignment, deploymentId);
    Response.RegisterForDispose(changeRequestsDisposable);

    var changeRequests = changeRequestsDisposable.Value;

    // only users who can manage change requests can see all change requests
    if (await subjectPermissionAuthorizationService.AuthorizeAsync<IChangeRequestManagePermission>(User, strict: false))
    {
      return Ok(changeRequests.Select(ChangeRequestResponse.Projection));
    }

    // otherwise the user can only see change requests they created
    var currentUser = userService.GetCurrentUser();
    changeRequests = changeRequests.Where(a => a.CreatedBy == currentUser.Id);

    return Ok(changeRequests.Select(ChangeRequestResponse.Projection));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManageCrossTenantWithChangeRequestsPermission))]
  [HttpPost(TargetAssignmentApiRoutes.CreateChangeRequestForNewDeployment)]
  public async Task<ActionResult<ChangeRequestResponse>> CreateChangeRequestForNewDeployment(
    [FromBody] CreateLocalTargetAssignmentPayload body,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    if (body.MaintenanceType is MaintenanceType.LocalSoftware)
    {
      var softwareId = Convert.ToInt32(body.MaintenanceIdentifier);
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
        new DefaultKeyParameters(softwareId),
        true);
    }

    if (VerifyTargetAssignment(body) is { } error) return BadRequest(error);
    if (ScrubLocalTargetAssignmentRequest(body) is { } error2) return BadRequest(error2);

    var res = await changeRequestStore.CreateChangeRequestForNewEntity(
      userService.GetCurrentUser(),
      ChangeRequestObjectType.TargetAssignment,
      body,
      CancellationToken.None);

    return Ok(ChangeRequestResponse.CreateResponse(res));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManageCrossTenantWithChangeRequestsPermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateChangeRequestForNewDeployment)]
  public async Task<ActionResult<ChangeRequestResponse>> UpdateChangeRequestForNewDeployment(
    [FromRoute] int changeRequestId,
    [FromBody] UpdateLocalTargetAssignmentPayload payload,
    [FromServices] IChangeRequestStore changeRequestStore)
  {
    var changeRequest = changeRequestStore.GetChangeRequest(changeRequestId);
    if (changeRequest is null) return NotFound();

    // only the user who created the change request and users who can approve change requests can update
    var currentUser = userService.GetCurrentUser();
    if (currentUser.Id != changeRequest.CreatedBy)
      await subjectPermissionAuthorizationService.AuthorizeAsync<IChangeRequestManagePermission>(User, strict: true);

    var updatedChangeRequest =
      await changeRequestStore.UpdateChangeRequestForNewEntity(userService.GetCurrentUser(),
        changeRequest,
        payload,
        CancellationToken.None);

    return Ok(ChangeRequestResponse.CreateResponse(updatedChangeRequest));
  }

  #endregion

  [SubjectPermissionAuthorize(typeof(IDeploymentsManageMaintenanceItemOrderingPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetMaintenanceItemOrder)]
  public ActionResult<IEnumerable<MaintenanceItemOrder>> GetMaintenanceItemOrder([FromServices] IRetrieveMaintenanceItemOrdersCmd cmd)
  {
    var ordering = cmd.Run();

    return Ok(ordering.OrderBy(a => a.SortOrder));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManageMaintenanceItemOrderingPermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateMaintenanceItemOrder)]
  public ActionResult<MaintenanceItemOrder> UpdatePriority(
    [FromServices] IUpdateMaintenanceItemOrderCmd cmd,
    [FromBody] UpdateMaintenanceItemOrderPayload payload)
  {
    var update = cmd.Run(payload);

    return Ok(update);
  }

  #region recommended approvals

  [SubjectPermissionAuthorize(typeof(IDeploymentsManageRecommendationsPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetRecommendedApprovals)]
  public ActionResult<IEnumerable<GetRecommendedApprovalResponse>> GetRecommendedApprovals(
    [FromServices] ImmybotDbContext ctx)
  {
    var approvals = ctx.GetRecommendedApprovals()
      .Select(GetRecommendedApprovalResponse.Projection);
    return Ok(approvals);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsManageRecommendationsPermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateRecommendedApprovals)]
  public ActionResult<IEnumerable<GetRecommendedApprovalResponse>> UpdateRecommendedApprovals(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] IUpdateRecommendedApprovalCmd cmd,
    [FromBody] UpdateRecommendedApprovalsRequestBody body)
  {
    cmd.UpdateApprovals(body.Approvals, userService.GetCurrentUser());
    var approvalIds = body.Approvals.Select(a => a.GlobalTargetAssignmentId);
    var updatedApprovals = ctx.GetRecommendedApprovals()
      .Select(GetRecommendedApprovalResponse.Projection)
      .Where(a => approvalIds.Contains(a.GlobalTargetAssignmentId))
      .ToList();
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").RecommendedTargetAssignmentApprovalsUpdated(updatedApprovals);
    }).Forget();
    return Ok(updatedApprovals);
  }

  #endregion

  #region optional target assignment approvals

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewIndividualPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetAllOptionalTargetAssignmentApprovalsForComputer)]
  public async Task<ActionResult<IEnumerable<GetOptionalTargetAssignmentApprovalResponse>>> GetAllOptionalTargetAssignmentApprovalsForComputer(
    [FromRoute] int computerId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IFeatureTracker featureTracker)
  {
    featureTracker.IsEnabled(FeatureEnum.OptionalDeploymentFeature, strict: true);

    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      strict: true);

    var computer = ctx.GetComputerById(computerId);
    if (computer is null) return NotFound();

    var approvals = ctx
      .GetOptionalTargetAssignmentApprovalsByComputerId(computerId)
      .Select(GetOptionalTargetAssignmentApprovalResponse.Projection);

    return Ok(approvals);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewIndividualPermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateOptionalTargetAssignmentApproval)]
  public async Task<ActionResult<bool>> UpdateOptionalTargetAssignmentApproval(
    [FromRoute] int id,
    [FromBody] UpdateOptionalTargetAssignmentApprovalPayload body,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IFeatureTracker featureTracker)
  {
    featureTracker.IsEnabled(FeatureEnum.OptionalDeploymentFeature, strict: true);

    if (body.TargetType is TargetType.Computer)
    {
      var computerId = Convert.ToInt32(body.Target);
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
        new DefaultKeyParameters(computerId),
        strict: true);
      var computer = ctx.GetComputerById(computerId);
      if (computer is null) return NotFound();
    }
    else
    {
      throw new NotImplementedException("Only computer target types are current supported");
    }

    var updatedApproval = await ctx.UpdateOptionalTargetAssignmentApproval(body, id);

    if (updatedApproval is null) return NotFound();
    return NoContent();
  }

  #endregion

  #region global

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.UpdateNotesGlobal)]
  public async Task<ActionResult> UpdateNotesGlobal(
    [FromRoute] int id,
    [FromServices] SoftwareDbContext ctx,
    [FromBody] UpdateNotesPayload body,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] ITargetAssignmentStore targetAssignmentStore,
    CancellationToken token)
  {
    var assignment = ctx.GetTargetAssignmentById(id, includeNotes: true);
    if (assignment == null) return NotFound();

    var user = userService.GetCurrentUser();

    await targetAssignmentStore.UpdateNotesGlobalAsync(
      user,
      assignment,
      body.Notes,
      token);

    var query = ctx.TargetAssignments
      .AsNoTracking()
      .Include(a => a.Notes)
      .Where(a => a.Id == id);
    var resources = query.Select(GlobalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignment = (await targetPopulator.Populate(resources, CancellationToken.None)).First();

    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").GlobalDeploymentUpdated(populatedAssignment);
    }, token).Forget();

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetAllGlobal)]
  public async Task<ActionResult<IEnumerable<GlobalTargetAssignmentResource>>> GetAllGlobal(
    [FromServices] SoftwareDbContext ctx,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] ApplicationSieveProcessor appSieveProcessor,
    [FromQuery] AppSieveModel? sieveModel = null)
  {
    var assignments = ctx.GetAllTargetAssignments();

    if (sieveModel != null) assignments = appSieveProcessor.Apply(sieveModel, assignments);
    var resources = assignments.Select(GlobalTargetAssignmentResource.Projection);
    var populatedAssignments = await targetPopulator.Populate(resources, CancellationToken.None);
    return Ok(populatedAssignments);
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetGlobal)]
  public async Task<ActionResult<GlobalTargetAssignmentResource>> GetGlobal(
    [FromServices] ITargetAssignmentActions actions,
    [FromRoute] int id,
    CancellationToken token)
  {
    var assignment = await actions.GetTargetAssignmentById(id, DatabaseType.Global, token, includeNotes: true);
    if (assignment == null) return NotFound();
    return Ok(new GlobalTargetAssignmentResource(assignment));
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(TargetAssignmentApiRoutes.GetGlobalTargetAssignmentType)]
  public ActionResult<GetTargetAssignmentTypeResponse> GetGlobalTargetAssignmentType(
    [FromRoute] int id,
    [FromServices] SoftwareDbContext ctx)
  {
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    return Ok(new GetTargetAssignmentTypeResponse(assignment));
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPost(TargetAssignmentApiRoutes.CreateGlobal)]
  public async Task<ActionResult<GlobalTargetAssignmentResource>> CreateGlobal(
    [FromBody] CreateGlobalTargetAssignmentPayload body,
    [FromServices] UserBearingDbFactory<SoftwareDbContext> dbFactory,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] ITargetPopulator targetPopulator)
  {
    await using var ctx = dbFactory();

    if (VerifyTargetAssignment(body) is { } error) return BadRequest(error);

    if (body.MaintenanceType is MaintenanceType.GlobalMaintenanceTask && ctx.IsTaskDeprecated(Convert.ToInt32(body.MaintenanceIdentifier)))
    {
      return BadRequest("New deployments cannot be created for deprecated tasks.");
    }

    var created = ctx.CreateTargetAssignment(body);

    var query = ctx.TargetAssignments.Where(a => a.Id == created.Id);

    var resources = query.Select(GlobalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignment = (await targetPopulator.Populate(resources, CancellationToken.None)).First();

    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").GlobalDeploymentCreated(populatedAssignment);
    }).Forget();

    return Ok(populatedAssignment);
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpPut(TargetAssignmentApiRoutes.UpdateGlobal)]
  public async Task<ActionResult<GlobalTargetAssignmentResource>> UpdateGlobal(
    [FromRoute] int id,
    [FromBody] UpdateGlobalTargetAssignmentPayload body,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] UserBearingDbFactory<SoftwareDbContext> dbFactory,
    [FromServices] ITargetPopulator targetPopulator)
  {
    await using var ctx = dbFactory();
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    body.Id = id;

    if (VerifyTargetAssignment(body) is { } error) return BadRequest(error);

    _ = ctx.UpdateTargetAssignment(body);

    var query = ctx.TargetAssignments.Where(a => a.Id == id);
    var resources = query.Select(GlobalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignment = (await targetPopulator.Populate(resources, CancellationToken.None)).First();

    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").GlobalDeploymentUpdated(populatedAssignment);
    }).Forget();

    return Ok(populatedAssignment);
  }

  [SubjectPermissionAuthorize(typeof(IGlobalManagePermission))]
  [HttpDelete(TargetAssignmentApiRoutes.DeleteGlobal)]
  public IActionResult DeleteGlobal(
    [FromRoute] int id,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] UserBearingDbFactory<SoftwareDbContext> dbFactory)
  {
    using var ctx = dbFactory();
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    ctx.DeleteTargetAssignment(assignment);

    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").GlobalDeploymentDeleted(id);
    }).Forget();

    return NoContent();
  }

  #endregion

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsManageIndividualPermission),
    typeof(IDeploymentsManageSingleTenantPermission),
    typeof(IDeploymentsManageCrossTenantPermission)
  ])]
  [HttpPost(TargetAssignmentApiRoutes.UpdateNotesLocal)]
  public async Task<ActionResult> UpdateNotesLocal(
    [FromRoute] int id,
    [FromServices] ImmybotDbContext ctx,
    [FromBody] UpdateNotesPayload body,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] ITargetAssignmentStore targetAssignmentStore,
    CancellationToken token)
  {
    var assignment = ctx.GetTargetAssignmentById(id, includeNotes: true);
    if (assignment == null) return NotFound();

    await ThrowIfCannotManageTargetAssignment(id, assignment);

    var user = userService.GetCurrentUser();

    await targetAssignmentStore.UpdateNotesLocalAsync(
      user,
      assignment,
      body.Notes,
      token);

    var query = ctx.TargetAssignments
      .AsNoTracking()
      .Include(a => a.Notes)
      .Where(a => a.Id == id);
    var resources = query.Select(LocalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignment = (await targetPopulator.Populate(resources, CancellationToken.None)).First();

    var userTenantId = userService.GetTenantId();
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").DeploymentUpdated(populatedAssignment);
      await hubContext.Clients.Group($"Tenant:{userTenantId}").DeploymentUpdated(populatedAssignment);
    }, token).Forget();

    return NoContent();
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsViewIndividualPermission),
    typeof(IDeploymentsViewSingleTenantPermission),
    typeof(IDeploymentsViewCrossTenantPermission)
  ])]
  [HttpGet(TargetAssignmentApiRoutes.GetAllLocal)]
  public async Task<ActionResult<IEnumerable<LocalTargetAssignmentResource>>> GetAllLocal(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] ApplicationSieveProcessor appSieveProcessor,
    [FromQuery] AppSieveModel? sieveModel = null)
  {
    // handle cross-tenant scope, single-tenant scope, and individual scope assignments
    List<LocalTargetAssignmentResource> res = [];

    // handle cross-tenant assignments
    if (await subjectPermissionAuthorizationService.AuthorizeAsync<IDeploymentsViewCrossTenantPermission>(User,
          strict: false))
    {
      var crossTenantAssignments = ctx.GetAllTargetAssignments()
        .Where(a =>
          a.TenantId == null && (a.TargetType != TargetType.Computer && a.TargetType != TargetType.Person));
      if (sieveModel != null) crossTenantAssignments = appSieveProcessor.Apply(sieveModel, crossTenantAssignments);
      var resources = crossTenantAssignments.Select(LocalTargetAssignmentResource.Projection);
      var populatedAssignments = await targetPopulator.Populate(resources, CancellationToken.None);
      res.AddRange(populatedAssignments);
    }

    // handle single-tenant permissions
    if (await subjectPermissionAuthorizationService.AuthorizeAsync<IDeploymentsViewSingleTenantPermission>(User,
          strict: false))
    {
      var singleTenantAssignments = ctx.GetAllTargetAssignments();
      var filter = permissionFilterBuilder
        .BuildFilterExpression<TargetAssignment, IDeploymentsViewSingleTenantPermission>();
      singleTenantAssignments = singleTenantAssignments.Where(filter);
      if (sieveModel != null) singleTenantAssignments = appSieveProcessor.Apply(sieveModel, singleTenantAssignments);
      var resources = singleTenantAssignments.Select(LocalTargetAssignmentResource.Projection);
      var populatedAssignments = await targetPopulator.Populate(resources, CancellationToken.None);
      res.AddRange(populatedAssignments);

    }

    // how do we filter out individual assignments?
    if (await subjectPermissionAuthorizationService.AuthorizeAsync<IDeploymentsViewIndividualPermission>(User,
          strict: false))
    {
      // we don't have a foreign key to the computer or person, so we need to grab the related data manually
      // I guess we can find all assignments for computers and people and filter out the ones where the user doesn't have permission to view the tenant.
      var computerIds = await ctx
        .TargetAssignments
        .AsNoTracking()
        .Where(a => a.TargetType == TargetType.Computer)
        .Select(a => a.Target)
        .ToListAsync();

      var computerTenantMap = await ctx
        .Computers
        .AsNoTracking()
        .Where(c => computerIds.Contains(c.Id.ToString()))
        .ToDictionaryAsync(c => c.Id, c => c.TenantId);

      var personIds = await ctx
        .TargetAssignments
        .AsNoTracking()
        .Where(a => a.TargetType == TargetType.Person)
        .Select(a => a.Target)
        .ToListAsync();

      var personTenantMap = await ctx
        .Persons
        .AsNoTracking()
        .Where(a => personIds.Contains(a.Id.ToString()))
        .ToDictionaryAsync(a => a.Id, a => a.TenantId);

      // can the user manage assignments for these computers and people?
      // build out the permissible list of computer and person ids the user can see
      var uniqueTenantIds = new HashSet<int>();

      foreach (var kvp in computerTenantMap)
      {
        uniqueTenantIds.Add(kvp.Value);
      }

      foreach (var kvp in personTenantMap)
      {
        uniqueTenantIds.Add(kvp.Value);
      }

      var permissibleTenantIds = new List<int>();
      foreach (var tenantId in uniqueTenantIds)
      {
        if (await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IDeploymentsViewSingleTenantPermission>(
              User,
              tenantId,
              strict: false))
          permissibleTenantIds.Add(tenantId);
      }

      // based on the permissible tenants, see which computers and people the user can see
      var permissibleComputerIds = computerTenantMap
        .Where(kvp => permissibleTenantIds.Contains(kvp.Value))
        .Select(kvp => kvp.Key.ToString())
        .ToList();
      var permissiblePersonIds = personTenantMap
        .Where(kvp => permissibleTenantIds.Contains(kvp.Value))
        .Select(kvp => kvp.Key.ToString())
        .ToList();

      var individualAssignments = ctx.GetAllTargetAssignments()
        .Where(a =>
        (a.TargetType == TargetType.Computer && a.Target != null && permissibleComputerIds.Contains(a.Target)) ||
        (a.TargetType == TargetType.Person && a.Target != null && permissiblePersonIds.Contains(a.Target)));
      if (sieveModel != null) individualAssignments = appSieveProcessor.Apply(sieveModel, individualAssignments);
      var resources = individualAssignments.Select(LocalTargetAssignmentResource.Projection);
      var populatedAssignments = await targetPopulator.Populate(resources, CancellationToken.None);
      res.AddRange(populatedAssignments);
    }

    return Ok(res);
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsManageIndividualPermission),
    typeof(IDeploymentsManageSingleTenantPermission),
    typeof(IDeploymentsManageCrossTenantPermission)
  ])]
  [HttpPatch(TargetAssignmentApiRoutes.BatchUpdateLocal)]
  public async Task<IActionResult> BatchUpdateLocal(
    [FromBody] BatchUpdateAssignmentRequest request,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    CancellationToken token)
  {
    // Limiting this to msp admins for now due to the impending rbac changes that may require further refactoring.
    // Otherwise, we would need to check for the ability to update each individual assignment.

    // filter out any deployments the user can't manage
    var permissibleAssignmentIds = new List<int>();
    foreach (var id in request.TargetAssignmentIds)
    {
      var assignment = ctx.GetTargetAssignmentById(id);
      if (assignment is null) continue;
      try
      {
        await ThrowIfCannotManageTargetAssignment(id, assignment);
        permissibleAssignmentIds.Add(id);
      }
      catch
      {
        // swallow
      }
    }

    request.TargetAssignmentIds.Clear();
    request.TargetAssignmentIds.AddRange(permissibleAssignmentIds);

    await ctx.BatchUpdateAssignments(request, token);

    // push target assignment to frontend via ws
    var query = ctx.TargetAssignments.AsNoTracking().Where(a => request.TargetAssignmentIds.Contains(a.Id));
    var resources = query.Select(LocalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignments = (await targetPopulator.Populate(resources, CancellationToken.None)).ToList();

    var userTenantId = userService.GetTenantId();
    Task.Run(async () =>
      {
        foreach (var assignment in populatedAssignments)
        {
          await hubContext.Clients.Group($"MspTenant").DeploymentUpdated(assignment);
          await hubContext.Clients.Group($"Tenant:{userTenantId}").DeploymentUpdated(assignment);
        }
      },
      token).Forget();

    return NoContent();
  }


  [SubjectPermissionAuthorize([
    typeof(IDeploymentsViewIndividualPermission),
    typeof(IDeploymentsViewSingleTenantPermission),
    typeof(IDeploymentsViewCrossTenantPermission)
  ])]
  [HttpPost(TargetAssignmentApiRoutes.GetDuplicatesLocal)]
  public ActionResult<TargetAssignmentDuplicateResponse> GetDuplicatesLocal(
    [FromBody] DuplicateTargetAssignmentPayload req,
    [FromServices] ImmybotDbContext ctx)
  {
    // no need for authorization since they can't modify the target assignment if they do not have access to it
    var dupes = ctx.GetDuplicateTargetAssignments(req);
    return Ok(new TargetAssignmentDuplicateResponse(dupes));
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsViewIndividualPermission),
    typeof(IDeploymentsViewSingleTenantPermission),
    typeof(IDeploymentsViewCrossTenantPermission)
  ])]
  [HttpGet(TargetAssignmentApiRoutes.GetLocal)]
  public async Task<ActionResult<LocalTargetAssignmentResource>> Get(
    [FromServices] ITargetAssignmentActions actions,
    [FromRoute] int id,
    CancellationToken token)
  {
    var assignment = await actions.GetTargetAssignmentById(
      id,
      DatabaseType.Local,
      token,
      includeNotes: true,
      includeVisibility: true);

    if (assignment == null) return NotFound();

    await ThrowIfCannotSeeTargetAssignment(id, assignment);

    return Ok(new LocalTargetAssignmentResource(assignment));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsViewTargetAssignmentsForVisibilityPermission))]
  [HttpPost(TargetAssignmentApiRoutes.ResolveVisibilityTargetAssignments)]
  public async Task<ActionResult<IEnumerable<TargetAssignmentResource>>> ResolveVisibilityTargetAssignments(
    [FromBody] ResolveVisibilityTargetAssignmentsRequest request,
    [FromServices] IVisibilityAssignmentResolver resolver,
    CancellationToken token)
  {
    var currentUser = userService.GetCurrentUser();
    return Ok(await resolver.GetTargetAssignmentsForVisibility(currentUser, request, token));
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsViewIndividualPermission),
    typeof(IDeploymentsViewSingleTenantPermission),
    typeof(IDeploymentsViewCrossTenantPermission),
  ])]
  [HttpPost(TargetAssignmentApiRoutes.CalculateTargetedTenants)]
  public async Task<ActionResult<IEnumerable<GetTenantResponse>>> CalculateTargetedTenants(
    [FromServices] ITenantAssignmentActions tenantAssignmentActions,
    [FromBody] CalculateTargetsRequest body)
  {
    await ThrowIfCannotSeeTarget(
      body.TargetType,
      body.ProviderDeviceGroupType,
      body.TenantId,
      body.Target,
      allowCrossTenantChangeRequestPermission: true);

    try
    {
      var tenantsDisposable = await tenantAssignmentActions.GetTenantsInTarget(
        body.TargetType,
        body.Target,
        tenantId: body.TenantId,
        includeChildTenants: body.PropagateToChildTenants,
        allowAccessToParentTenant: body.AllowAccessToParentTenant,
        providerLinkId: body.ProviderLinkId,
        providerClientGroupType: body.ProviderClientGroupType);
      Response.RegisterForDispose(tenantsDisposable);
      var tenants = tenantsDisposable.Value.Select(GetTenantResponse.GetProjection());

      return Ok(tenants.ToNonAsyncEnumerable());
    }
    catch (NotImplementedException ex)
    {
      return BadRequest(ex.Message);
    }
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsViewIndividualPermission),
    typeof(IDeploymentsViewSingleTenantPermission),
    typeof(IDeploymentsViewCrossTenantPermission)
  ])]
  [HttpPost(TargetAssignmentApiRoutes.CalculateTargetedComputers)]
  public async Task<ActionResult<IEnumerable<CalculateTargetedComputerResponse>>> CalculateTargetedComputers(
    [FromServices] IComputerAssignmentActions computerAssignmentActions,
    [FromBody] CalculateTargetsRequest body)
  {
    await ThrowIfCannotSeeTarget(
      body.TargetType,
      body.ProviderDeviceGroupType,
      body.TenantId,
      body.Target,
      allowCrossTenantChangeRequestPermission: true);

    try
    {
      var computersDisposable = (await computerAssignmentActions.GetComputersInTarget(
        body.TargetType,
        body.TargetGroupFilter,
        body.Target,
        tenantId: body.TenantId,
        includeChildTenants: body.PropagateToChildTenants,
        allowAccessToParentTenant: body.AllowAccessToParentTenant,
        providerLinkId: body.ProviderLinkId,
        providerDeviceGroupType: body.ProviderDeviceGroupType,
        providerClientGroupType: body.ProviderClientGroupType,
        excludeOnboarding: false,
        excludeOnboarded: body.OnboardingOnly));
      Response.RegisterForDispose(computersDisposable);
      var computers = computersDisposable.Value.Select(c => new CalculateTargetedComputerResponse
      {
        Id = c.Id,
        Cn = c.ComputerName ?? string.Empty,
        On = c.Agents
          .Where(a => a.SupportsRunningScripts)
          .Any(r => r.IsOnline && r.ProviderLink != null && !r.ProviderLink.Disabled && r.ProviderLink.HealthStatus != HealthStatus.Unhealthy),
        Tn = c.Tenant != null ? c.Tenant.Name : String.Empty,
        Ti = c.TenantId,
        Ppi = c.PrimaryPersonId,
        Ppn = c.PrimaryPerson != null ? c.PrimaryPerson.DisplayName : string.Empty,
        Os = c.OperatingSystem ?? string.Empty,
        Obs = c.OnboardingStatus,
        Sn = c.SerialNumber ?? string.Empty,
        Ct = c.ChassisTypes ?? new List<int>(),
        Dr = c.DomainRole,
        Sb = c.IsSandbox
      });

      return Ok(computers.ToNonAsyncEnumerable());
    }
    catch (NotImplementedException ex)
    {
      return BadRequest(ex.Message);
    }
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsViewIndividualPermission),
    typeof(IDeploymentsViewSingleTenantPermission),
    typeof(IDeploymentsViewCrossTenantPermission)
  ])]
  [HttpPost(TargetAssignmentApiRoutes.CalculateTargetedPersons)]
  public async Task<ActionResult<IEnumerable<TargetedPerson>>> CalculateTargetedPersons(
    [FromServices] IPersonAssignmentActions personAssignmentActions,
    [FromBody] CalculateTargetsRequest body)
  {
    await ThrowIfCannotSeeTarget(
      body.TargetType,
      body.ProviderDeviceGroupType,
      body.TenantId,
      body.Target,
      allowCrossTenantChangeRequestPermission: true);

    try
    {
      var personsDisposable = (await personAssignmentActions.GetPersonsInTarget(
        body.TargetType,
        body.Target,
        tenantId: body.TenantId,
        includeChildTenants: body.PropagateToChildTenants));
      Response.RegisterForDispose(personsDisposable);
      var persons = personsDisposable.Value.Select(TargetedPerson.Projection);
      return Ok(persons.ToNonAsyncEnumerable());
    }
    catch (NotImplementedException ex)
    {
      return BadRequest(ex.Message);
    }
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsViewIndividualPermission),
    typeof(IDeploymentsViewSingleTenantPermission),
    typeof(IDeploymentsViewCrossTenantPermission)
  ])]
  [HttpGet(TargetAssignmentApiRoutes.GetLocalTargetAssignmentType)]
  public async Task<ActionResult<GetTargetAssignmentTypeResponse>> GetLocalTargetAssignmentType(
    [FromRoute] int id,
    [FromServices] ImmybotDbContext ctx)
  {
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    await ThrowIfCannotSeeTargetAssignment(id, assignment);
    return Ok(new GetTargetAssignmentTypeResponse(assignment));
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsManageIndividualPermission),
    typeof(IDeploymentsManageSingleTenantPermission),
    typeof(IDeploymentsManageCrossTenantPermission)
  ])]
  [HttpPost(TargetAssignmentApiRoutes.CreateLocal)]
  public async Task<ActionResult<LocalTargetAssignmentResource>> Create(
    [FromBody] CreateLocalTargetAssignmentPayload body,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] SoftwareDbContext globalCtx,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] ITargetAssignmentEmitter targetAssignmentEmitter,
    [FromServices] IComputerAssignmentActions computerAssignmentActions,
    [FromServices] ITargetPopulator targetPopulator)
  {
    if (body.MaintenanceType is MaintenanceType.LocalSoftware)
    {
      var softwareId = Convert.ToInt32(body.MaintenanceIdentifier);
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<LocalSoftware, ISoftwareViewPermission>(
        new DefaultKeyParameters(softwareId),
        true);
    }

    await ThrowIfCannotManageTarget(body.TargetType, body.ProviderDeviceGroupType, body.TenantId, body.Target);

    if (VerifyTargetAssignment(body) is { } error) return BadRequest(error);
    if (ScrubLocalTargetAssignmentRequest(body) is { } error2) return BadRequest(error2);

    await using var ctx = dbFactory();

    if (
      body.MaintenanceType is MaintenanceType.LocalMaintenanceTask && ctx.IsTaskDeprecated(Convert.ToInt32(body.MaintenanceIdentifier)) ||
      body.MaintenanceType is MaintenanceType.GlobalMaintenanceTask && globalCtx.IsTaskDeprecated(Convert.ToInt32(body.MaintenanceIdentifier)))
    {
      return BadRequest("New deployments cannot be created for deprecated tasks.");
    }

    var created = ctx.CreateTargetAssignment(body);

    var query = ctx.TargetAssignments
      .AsNoTracking()
      .Where(a => a.Id == created.Id);

    var resources = query.Select(LocalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignment = (await targetPopulator.Populate(resources, CancellationToken.None)).First();
    targetAssignmentEmitter.Emit(populatedAssignment);

    if (created.TargetCategory == TargetCategory.Computer &&
      created.TargetType != TargetType.Metascript &&
      created.TargetType != TargetType.FilterScript &&
      created.TargetType != TargetType.TenantFilterScript &&
      created.TargetType != TargetType.TenantMetascript)
    {
      var targetedComputers = await (await computerAssignmentActions.GetComputersInTarget(
          body.TargetType,
          body.TargetGroupFilter,
          body.Target,
          tenantId: body.TenantId,
          includeChildTenants: body.PropagateToChildTenants,
          allowAccessToParentTenant: body.AllowAccessToParentTenant,
          providerLinkId: body.ProviderLinkId,
          providerDeviceGroupType: body.ProviderDeviceGroupType,
          providerClientGroupType: body.ProviderClientGroupType,
          excludeOnboarding: false,
          excludeOnboarded: created.TargetEnforcement is TargetEnforcement.Onboarding))
        .Using(q => q.Select(a => a.Id).ToListAsync());

      ctx.SetDetectionOutdated(targetedComputers);

      Task.Run(async () =>
      {
        foreach (var compId in targetedComputers)
        {
          await hubContext.Clients.Group($"Computer:{compId}").UpdateComputer(new UpdateComputerResource
          {
            DetectionOutdated = true
          });
        }
      }).Forget();
    }

    return Ok(populatedAssignment);
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsManageIndividualPermission),
    typeof(IDeploymentsManageSingleTenantPermission),
    typeof(IDeploymentsManageCrossTenantPermission)
  ])]
  [HttpPut(TargetAssignmentApiRoutes.UpdateLocal)]
  public async Task<ActionResult<LocalTargetAssignmentResource>> UpdateTargetAssignment(
    [FromRoute] int id,
    [FromBody] UpdateLocalTargetAssignmentPayload body,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromServices] IComputerAssignmentActions computerAssignmentActions,
    [FromServices] ITargetPopulator targetPopulator)
  {
    await using var ctx = dbFactory();
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    await ThrowIfCannotManageTargetAssignment(id, assignment);
    await ThrowIfCannotManageTarget(body.TargetType, body.ProviderDeviceGroupType, body.TenantId, body.Target);
    body.Id = id;

    if (VerifyTargetAssignment(body) is { } error) return BadRequest(error);
    if (ScrubLocalTargetAssignmentRequest(body) is { } error2) return BadRequest(error2);
    var updated = ctx.UpdateTargetAssignment(body);

    // push target assignment to frontend via ws
    var query = ctx.TargetAssignments.AsNoTracking().Where(a => a.Id == id);
    var resources = query.Select(LocalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedAssignments = (await targetPopulator.Populate(resources, CancellationToken.None)).First();

    var userTenantId = userService.GetTenantId();
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").DeploymentUpdated(populatedAssignments);
      await hubContext.Clients.Group($"Tenant:{userTenantId}").DeploymentUpdated(populatedAssignments);
    }).Forget();

    if (updated is { TargetCategory: TargetCategory.Computer } &&
        updated.TargetType != TargetType.Metascript &&
        updated.TargetType != TargetType.FilterScript &&
        updated.TargetType != TargetType.TenantFilterScript &&
        updated.TargetType != TargetType.TenantMetascript)
    {
      var targetedComputers = await (await computerAssignmentActions.GetComputersInTarget(
          body.TargetType,
          body.TargetGroupFilter,
          body.Target,
          tenantId: body.TenantId,
          includeChildTenants: body.PropagateToChildTenants,
          allowAccessToParentTenant: body.AllowAccessToParentTenant,
          providerLinkId: body.ProviderLinkId,
          providerDeviceGroupType: body.ProviderDeviceGroupType,
          providerClientGroupType: body.ProviderClientGroupType,
          excludeOnboarding: false,
          excludeOnboarded: body.OnboardingOnly))
        .Using(q => q.Select(a => a.Id).ToListAsync());

      ctx.SetDetectionOutdated(targetedComputers);

      Task.Run(async () =>
      {
        foreach (var compId in targetedComputers)
        {
          await hubContext.Clients.Group($"Computer:{compId}").UpdateComputer(new UpdateComputerResource
          {
            DetectionOutdated = true
          });
        }
      }).Forget();
    }

    return Ok(populatedAssignments);
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsManageIndividualPermission),
    typeof(IDeploymentsManageSingleTenantPermission),
    typeof(IDeploymentsManageCrossTenantPermission)
  ])]
  [HttpDelete(TargetAssignmentApiRoutes.DeleteLocal)]
  public async Task<IActionResult> DeleteTargetAssignment(
    [FromRoute] int id,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext)
  {
    await using var ctx = dbFactory();
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();
    await ThrowIfCannotManageTargetAssignment(id, assignment);
    await ctx.DeleteTargetAssignment(assignment);
    await ctx.DeleteOptionalTargetAssignmentApprovalsByTargetAssignmentId(id);

    var userTenantId = userService.GetTenantId();
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").DeploymentDeleted(id);
      await hubContext.Clients.Group($"Tenant:{userTenantId}").DeploymentDeleted(id);
    }).Forget();

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsOverridePermission))]
  [HttpPost(TargetAssignmentApiRoutes.OverrideLocal)]
  public async Task<ActionResult<LocalTargetAssignmentResource>> OverrideLocalTargetAssignment(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IOverrideTargetAssignmentCmd cmd,
    [FromServices] ITargetPopulator targetPopulator,
    [FromRoute] int id,
    [FromBody] OverrideTargetAssignmentRequest req)
  {
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();

    await ThrowIfCannotManageTargetAssignment(id, assignment);

    var updatedAssignment = cmd.Run(assignment,
      req.TargetType,
      req.DesiredSoftwareState,
      req.Target,
      userService.GetCurrentUser());

    var resource = new LocalTargetAssignmentResource(updatedAssignment);
    var populated = (await targetPopulator.Populate(new List<LocalTargetAssignmentResource> { resource }.AsQueryable(),
      CancellationToken.None)).First();
    return Ok(populated);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsOverridePermission))]
  [HttpPost(TargetAssignmentApiRoutes.OverrideGlobal)]
  public async Task<ActionResult<LocalTargetAssignmentResource>> OverrideGlobal(
    [FromServices] SoftwareDbContext ctx,
    [FromServices] IOverrideTargetAssignmentCmd cmd,
    [FromServices] ITargetPopulator targetPopulator,
    [FromRoute] int id,
    [FromBody] OverrideTargetAssignmentRequest req)
  {
    var assignment = ctx.GetTargetAssignmentById(id);
    if (assignment == null) return NotFound();

    var updatedAssignment = cmd.Run(assignment,
      req.TargetType,
      req.DesiredSoftwareState,
      req.Target,
      userService.GetCurrentUser());

    var resource = new LocalTargetAssignmentResource(updatedAssignment);
    var populated = (await targetPopulator.Populate(new List<LocalTargetAssignmentResource> { resource }.AsQueryable(),
      CancellationToken.None)).First();
    return Ok(populated);
  }

  [SubjectPermissionAuthorize([
    typeof(IDeploymentsManageIndividualPermission),
    typeof(IDeploymentsManageSingleTenantPermission),
    typeof(IDeploymentsManageCrossTenantPermission)
  ])]
  [HttpPost(TargetAssignmentApiRoutes.Duplicate)]
  public async Task<ActionResult<int>> Duplicate(
    [FromServices] IDuplicateAssignmentCmd cmd,
    [FromServices] ImmybotDbContext localCtx,
    [FromServices] SoftwareDbContext globalCtx,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] IHubContext<ImmyBotUserHub, IImmyBotUserHubClient> hubContext,
    [FromBody] DuplicateAssignmentRequestBody req)
  {
    var existing = req.DatabaseType == DatabaseType.Global ?
      globalCtx.GetTargetAssignmentById(req.Id)
      : localCtx.GetTargetAssignmentById(req.Id);

    if (existing == null) return NotFound();

    await ThrowIfCannotManageTargetAssignment(req.Id, existing);

    var newAssignmentId = cmd.Run(req.Id, req.DatabaseType, userService.GetCurrentUser());

    var query = localCtx.TargetAssignments.AsNoTracking().Where(a => a.Id == newAssignmentId);
    var resource = query.Select(LocalTargetAssignmentResource.Projection).AsEnumerable().AsQueryable();
    var populatedResource = (await targetPopulator.Populate(resource, CancellationToken.None)).First();

    var userTenantId = userService.GetTenantId();
    Task.Run(async () =>
    {
      await hubContext.Clients.Group($"MspTenant").DeploymentUpdated(populatedResource);
      await hubContext.Clients.Group($"Tenant:{userTenantId}").DeploymentUpdated(populatedResource);
    }).Forget();

    return Ok(newAssignmentId);
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsSupersedePermission))]
  [HttpPost(TargetAssignmentApiRoutes.MigrateToSupersedingAssignment)]
  public async Task<ActionResult<OpResult<int>>> MigrateToSupersedingAssignment(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IMigrateToSupersedingAssignmentCmd cmd,
    [FromBody] MigrateToSupersedingAssignmentRequest req)
  {
    var existing = ctx.GetTargetAssignmentById(req.OldAssignmentId);
    if (existing is null) return NotFound();

    await ThrowIfCannotManageTargetAssignment(req.OldAssignmentId, existing);

    var res = await cmd.ExecuteAsync(req, userService.GetCurrentUser(), CancellationToken.None);
    return res.IsSuccess ?
      Ok(OpResult.Ok(res.Value.TargetAssignment.Id)) :
      Ok(OpResult.Fail(res.Reason));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsSupersedePermission))]
  [HttpPost(TargetAssignmentApiRoutes.MigrateToSupersedingAssignmentWhatIf)]
  public async Task<ActionResult<OpResult<MigrateToSupersedingAssignmentWhatIfResponse>>>
    MigrateToSupersedingAssignmentWhatIf(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] IMigrateToSupersedingAssignmentCmd cmd,
    [FromBody] MigrateToSupersedingAssignmentRequest req)
  {
    var existing = ctx.GetTargetAssignmentById(req.OldAssignmentId);
    if (existing is null) return NotFound();

    await ThrowIfCannotManageTargetAssignment(req.OldAssignmentId, existing);

    var res = await cmd.ExecuteWhatIfAsync(req, userService.GetCurrentUser(), CancellationToken.None);

    if (res.IsSuccess)
    {
      var resource = new LocalTargetAssignmentResource(res.Value.SupersedingAssignment);
      var populated =
        (await targetPopulator.Populate(new List<LocalTargetAssignmentResource> { resource }.AsQueryable(),
          CancellationToken.None)).First();

      string? tenantName = null;
      if (populated.TenantId is { } tenantId)
      {
        tenantName = ctx.GetTenantName(tenantId);
      }

      var policyDescription = PolicyDescriptionGenerator.Generate(
        populated.TargetText ?? string.Empty,
        res.Value.SupersededByTaskName,
        populated.DesiredSoftwareState,
        populated.MaintenanceTaskMode,
        populated.SoftwareSemanticVersion,
        populated.TargetType,
        populated.TargetGroupFilter,
        populated.ProviderDeviceGroupType,
        populated.ProviderClientGroupType,
        tenantName,
        populated.PropagateToChildTenants,
        populated.AllowAccessToParentTenant);

      return Ok(OpResult.Ok(
        new MigrateToSupersedingAssignmentWhatIfResponse(res.Value.IsNew, populated, policyDescription)));
    }

    return Ok(OpResult.Fail(res.Reason));
  }

  [SubjectPermissionAuthorize(typeof(IDeploymentsMigrateToIntegrationPermission))]
  [HttpPost(TargetAssignmentApiRoutes.MigrateDeploymentsToProviderLinks)]
  public async Task<ActionResult> MigrateDeploymentsToProviderLinks(
    [FromServices] IMigrateDeploymentsToProviderLinksService service,
    CancellationToken token)
  {
    await service.PerformWork(token);
    return NoContent();
  }

  /// <summary>
  /// Throws if the user cannot manage the provided target assignment.
  /// </summary>
  /// <param name="id"></param>
  /// <param name="assignment"></param>
  /// <exception cref="NotImplementedException"></exception>
  private async Task ThrowIfCannotManageTargetAssignment(int id, TargetAssignment assignment)
  {
    var targetScope = TargetAssignmentHelpers.GetTargetScopeForTargetType(
      providerActions,
      assignment.TargetType,
      assignment.ProviderDeviceGroupType);

    switch (targetScope)
    {
      // User must have the ability to manage cross-tenant deployments.
      // This is not a resource-based permission, so we use the subject permission authorization service directly.
      case TargetScope.CrossTenant:
        if (!await subjectPermissionAuthorizationService
              .AuthorizeAsync<IDeploymentsManageCrossTenantPermission>(User, strict: false) &&
            !await subjectPermissionAuthorizationService
              .AuthorizeAsync<IDeploymentsManageCrossTenantWithChangeRequestsPermission>(User,
                strict: false))
        {
          throw new SubjectPermissionAuthorizationFailedException(
            $"You do not have permission to view this cross tenant target assignment.");
        }

        break;
      // User must have permission to manage the specified target assignment.
      // This is a resource-based permission, so we use the resource authorizer flow.
      case TargetScope.SingleTenant:
        await resourceAuthorizerFlow
          .PerformAuthorizationWorkflowAsync<TargetAssignment, IDeploymentsManageSingleTenantPermission>(
            new DefaultKeyParameters(id),
            strict: true);
        break;
      // User must have permission to manage the specified target assignment.
      // This is a resource-based permission, so we use the resource authorizer flow.
      case TargetScope.Individual:
        await resourceAuthorizerFlow
          .PerformAuthorizationWorkflowAsync<TargetAssignment, IDeploymentsManageIndividualPermission>(
            new DefaultKeyParameters(id),
            strict: true);
        break;
      default:
        throw new NotImplementedException();
    }
  }

  /// <summary>
  /// Throws if the user cannot see the provided target assignment.
  /// </summary>
  /// <param name="id"></param>
  /// <param name="assignment"></param>
  /// <exception cref="NotImplementedException"></exception>
  private async Task ThrowIfCannotSeeTargetAssignment(int id, TargetAssignment assignment)
  {
    var targetScope = TargetAssignmentHelpers.GetTargetScopeForTargetType(
      providerActions,
      assignment.TargetType,
      assignment.ProviderDeviceGroupType);

    switch (targetScope)
    {
      // User must have the ability to view cross-tenant deployments.
      // This is not a resource-based permission, so we use the subject permission authorization service directly.
      case TargetScope.CrossTenant:
        await subjectPermissionAuthorizationService
          .AuthorizeAsync<IDeploymentsViewCrossTenantPermission>(User, strict: true);
        break;
      // User must have permission to view the specified target assignment.
      // This is a resource-based permission, so we use the resource authorizer flow.
      case TargetScope.SingleTenant:
        await resourceAuthorizerFlow
          .PerformAuthorizationWorkflowAsync<TargetAssignment, IDeploymentsViewSingleTenantPermission>(
            new DefaultKeyParameters(id),
            strict: true);
        break;
      // User must have permission to view the specified target assignment.
      // This is a resource-based permission, so we use the resource authorizer flow.
      case TargetScope.Individual:
        await resourceAuthorizerFlow
          .PerformAuthorizationWorkflowAsync<TargetAssignment, IDeploymentsViewIndividualPermission>(
            new DefaultKeyParameters(id),
            strict: true);
        break;
      default: throw new NotImplementedException();
    }
  }

  /// <summary>
  /// Throws if the user cannot manage the target based on the provider details.
  /// Used to verify the create and update assignment payloads
  /// </summary>
  /// <param name="targetType"></param>
  /// <param name="providerDeviceGroupType"></param>
  /// <param name="tenantId"></param>
  /// <param name="target"></param>
  /// <param name="allowCrossTenantChangeRequestPermission"></param>
  /// <exception cref="ArgumentNullException"></exception>
  /// <exception cref="InvalidOperationException"></exception>
  private async Task ThrowIfCannotManageTarget(
    TargetType targetType,
    Guid? providerDeviceGroupType,
    int? tenantId,
    string? target,
    bool allowCrossTenantChangeRequestPermission = false)
  {
    var targetScope = TargetAssignmentHelpers.GetTargetScopeForTargetType(
      providerActions,
      targetType,
      providerDeviceGroupType);

    switch (targetScope)
    {
      // User must have permission to manage cross-tenant deployments.
      // This is not a resource-based permission, so we use the subject permission authorization service directly.
      case TargetScope.CrossTenant:
        var canManageCrossTenant = await subjectPermissionAuthorizationService
          .AuthorizeAsync<IDeploymentsManageCrossTenantPermission>(User, strict: false);
        if (canManageCrossTenant) return;
        if (allowCrossTenantChangeRequestPermission)
        {
          var canSubmitCrossTenantChangeRequests = await subjectPermissionAuthorizationService
            .AuthorizeAsync<IDeploymentsManageCrossTenantWithChangeRequestsPermission>(User, strict: false);
          if (canSubmitCrossTenantChangeRequests) return;
        }

        // instead of throwing an exception here, it would be cool if the service returned the exception as part of the result so we can
        // throw an exception created by the service itself
        throw new SubjectPermissionAuthorizationFailedException(
          $"You do not have permission to perform see cross-tenant deployments.");
      // User must have permission to view the specified tenant.
      case TargetScope.SingleTenant:
        if (!tenantId.HasValue)
          throw new ArgumentNullException(nameof(tenantId));
        await subjectPermissionAuthorizationService
          .AuthorizeTenantAsync<IDeploymentsManageSingleTenantPermission>(
            User,
            tenantId.Value,
            strict: true);
        return;
      case TargetScope.Individual:
        // user must be able to manage individual-scoped deployments
        await subjectPermissionAuthorizationService
          .AuthorizeAsync<IDeploymentsManageIndividualPermission>(User, strict: true);
        switch (targetType)
        {
          // if the target is a specific computer then they must be able to see that computer
          case TargetType.Computer:
            {
              var computerId = Convert.ToInt32(target);
              await resourceAuthorizerFlow
                .PerformAuthorizationWorkflowAsync<Computer, IComputersManagePermission>(
                  new DefaultKeyParameters(computerId),
                  strict: true);
              return;
            }
          // if the target is a specific person then they must be able to see that person
          case TargetType.Person:
            {
              var personId = Convert.ToInt32(target);
              await resourceAuthorizerFlow
                .PerformAuthorizationWorkflowAsync<Person, IPersonsManagePermission>(
                  new DefaultKeyParameters(personId),
                  strict: true);
              return;
            }
          default:
            throw new NotImplementedException();
        }
      default:
        throw new NotImplementedException();
    }
  }

  /// <summary>
  /// Throws if the user cannot see the target based on the provider details.
  /// </summary>
  /// <param name="targetType"></param>
  /// <param name="providerDeviceGroupType"></param>
  /// <param name="tenantId"></param>
  /// <param name="target"></param>
  /// <param name="allowCrossTenantChangeRequestPermission"></param>
  /// <exception cref="ArgumentNullException"></exception>
  /// <exception cref="InvalidOperationException"></exception>
  private async Task ThrowIfCannotSeeTarget(
    TargetType targetType,
    Guid? providerDeviceGroupType,
    int? tenantId,
    string? target,
    bool allowCrossTenantChangeRequestPermission = false)
  {
    var targetScope = TargetAssignmentHelpers.GetTargetScopeForTargetType(
      providerActions,
      targetType,
      providerDeviceGroupType);

    switch (targetScope)
    {
      // User must have permission to manage cross-tenant deployments.
      // This is not a resource-based permission, so we use the subject permission authorization service directly.
      case TargetScope.CrossTenant:
        var canSeeCrossTenant = await subjectPermissionAuthorizationService
          .AuthorizeAsync<IDeploymentsViewCrossTenantPermission>(User, strict: false);
        if (canSeeCrossTenant) return;
        if (allowCrossTenantChangeRequestPermission)
        {
          var canSubmitCrossTenantChangeRequests = await subjectPermissionAuthorizationService
            .AuthorizeAsync<IDeploymentsManageCrossTenantWithChangeRequestsPermission>(User, strict: false);
          if (canSubmitCrossTenantChangeRequests) return;
        }

        throw new SubjectPermissionAuthorizationFailedException(
          $"You do not have permission to perform see cross-tenant deployments.");
      // User must have permission to view the specified tenant.
      case TargetScope.SingleTenant:
        if (!tenantId.HasValue)
          throw new ArgumentNullException(nameof(tenantId));
        await subjectPermissionAuthorizationService
          .AuthorizeTenantAsync<IDeploymentsViewSingleTenantPermission>(
            User,
            tenantId.Value,
            strict: true);
        return;
      case TargetScope.Individual:
        await subjectPermissionAuthorizationService
          .AuthorizeAsync<IDeploymentsViewIndividualPermission>(User, strict: true);
        switch (targetType)
        {
          // if the target is a specific computer then they must be able to see that computer
          case TargetType.Computer:
            {
              var computerId = Convert.ToInt32(target);
              await resourceAuthorizerFlow
                .PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
                  new DefaultKeyParameters(computerId),
                  strict: true);
              return;
            }
          // if the target is a specific person then they must be able to see that person
          case TargetType.Person:
            {
              var personId = Convert.ToInt32(target);
              await resourceAuthorizerFlow
                .PerformAuthorizationWorkflowAsync<Person, IPersonsViewPermission>(new DefaultKeyParameters(personId),
                  strict: true);
              return;
            }
          default:
            throw new NotImplementedException();
        }
      default:
        throw new NotImplementedException();
    }
  }
}
