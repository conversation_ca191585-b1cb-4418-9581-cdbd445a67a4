using DevExtreme.AspNet.Data;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.TagExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class TenantsController(
  IPermissionFilterBuilder permissionFilterBuilder,
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IResourceAuthorizerFlow resourceAuthorizerFlow,
  IUserService userService)
  : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(ITenantsViewPermission))]
  [HttpGet(TenantApiRoutes.GetAll)]
  public ActionResult<ICollection<GetTenantResponse>> GetTenants(
    [FromServices] ApplicationSieveProcessor sieveProcessor,
    [FromServices] ImmybotDbContext ctx,
    [FromQuery] AppSieveModel? sieveModel = null)
  {
    var q = ctx.GetAllTenants().IncludeAzData();
    var permissionFilter = permissionFilterBuilder.BuildFilterExpression<Tenant, ITenantsViewPermission>();
    q = q.Where(permissionFilter);

    var tenants = q.Select(GetTenantResponse.GetProjection());

    if (sieveModel != null) tenants = sieveProcessor.Apply(sieveModel, tenants);

    return Ok(tenants.ToNonAsyncEnumerable());
  }

  [SubjectPermissionAuthorize(typeof(ITenantsViewPermission))]
  [HttpGet(TenantApiRoutes.SoftwareFromInventory)]
  public async Task<ActionResult<GetTenantSoftwareFromInventoryResponse[]>> GetSoftwareFromInventory(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ILocalSoftwareActions localSoftware,
    [FromRoute] int id)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsViewPermission>(
      new DefaultKeyParameters(id),
      true);

    var tenant = ctx.GetTenantById(id);
    if (tenant is null)
    {
      return NotFound();
    }

    var result = await localSoftware.GetSoftwareFromInventoryResults(id);

    var response = result
      .Select(x => new GetTenantSoftwareFromInventoryResponse(x));

    return Ok(response);
  }

  /// <summary>
  /// Streams the contents of the detected computer software table as a CSV file to the client
  /// </summary>
  [SubjectPermissionAuthorize(typeof(ITenantsViewPermission))]
  [HttpGet(TenantApiRoutes.ExportSoftware)]
  public async Task<IActionResult> ExportSoftware(
    [FromServices] ImmybotDbContext ctx,
    DataSourceLoadOptions? loadOptions,
    CancellationToken token,
    [FromQuery] int? tenantId = null)
  {
    // required in order to allow export directly to response stream
    var feature = HttpContext.Features.Get<IHttpBodyControlFeature>();
    if (feature != null) feature.AllowSynchronousIO = true;

    var query = ctx.DetectedComputerSoftware
      .AsNoTracking()
      .Select(DetectedComputerSoftwareResponse.Projection);

    if (tenantId.HasValue)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsViewPermission>(
        new DefaultKeyParameters(tenantId.Value),
        strict: true);
      var tenant = ctx.GetTenantById(tenantId.Value);
      if (tenant is null) return NotFound();
      query = query.Where(a => a.TenantId == tenantId);
    }
    else if (!await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<ITenantsViewPermission>(User,
               strict: false))
    {
      var currentUser = userService.GetCurrentUser();
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsViewPermission>(
        new DefaultKeyParameters(currentUser.TenantId),
        strict: true);
      query = query.Where(a => a.TenantId == currentUser.TenantId);
    }

    var exporter = new ExcelExporter<DetectedComputerSoftwareResponse>(
      Response, "detected-computer-software.xlsx");
    await exporter.ExportData(query, loadOptions, 1000, token);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ITenantsViewPermission))]
  [HttpGet(TenantApiRoutes.AllSoftwareFromInventoryDx)]
  public async Task<ActionResult<DetectedComputerSoftwareResponse[]>> GetSoftwareFromInventoryDx(
    [FromServices] ImmybotDbContext ctx,
    DataSourceLoadOptions loadOptions,
    [FromQuery] int? tenantId = null)
  {
    var result = ctx.DetectedComputerSoftware
      .AsNoTracking()
      .Where(a => a.Computer!.DeletedAt == null)
      .Select(DetectedComputerSoftwareResponse.Projection);

    if (tenantId.HasValue)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsViewPermission>(
        new DefaultKeyParameters(tenantId.Value),
        strict: true);
      var tenant = ctx.GetTenantById(tenantId.Value);
      if (tenant is null) return NotFound();
      result = result.Where(a => a.TenantId == tenantId);
    }
    else if (!await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<ITenantsViewPermission>(User,
               strict: false))
    {
      var currentUser = userService.GetCurrentUser();
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsViewPermission>(
        new DefaultKeyParameters(currentUser.TenantId),
        strict: true);
      result = result.Where(a => a.TenantId == currentUser.TenantId);
    }

    return Ok(await DataSourceLoader.LoadAsync(result, loadOptions));
  }

  [SubjectPermissionAuthorize(typeof(ITenantsViewPermission))]
  [HttpGet(TenantApiRoutes.Get)]
  public async Task<ActionResult<GetTenantResponse>> GetTenant(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int id)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsViewPermission>(
      new DefaultKeyParameters(id),
      true);

    var tenant = ctx.GetTenantById(id, includeTags: true, includeAzData: true);
    if (tenant == null) return NotFound();
    var res = new GetTenantResponse(tenant, includeTags: true);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(ITenantsManagePermission))]
  [HttpPut(TenantApiRoutes.Update)]
  public async Task<ActionResult<GetTenantResponse>> PutTenant(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromRoute] int id,
    [FromBody] UpdateTenantPayload body,
    CancellationToken token)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsManagePermission>(
      new DefaultKeyParameters(id),
      true);
    var currentTenantId = userService.GetTenantId();
    await using var ctx = ctxFactory();
    if (body.ParentTenantId is {} parentTenantId)
    {
      var ancestorCheck = await ctx.CheckForCyclicalAncestor([id], parentTenantId, token);
      if (!ancestorCheck.IsSuccess) return BadRequest(ancestorCheck.Reason);
    }

    var updated = await ctx.UpdateTenant(body, currentTenantId, token);
    await ctx.Entry(updated).Collection(t => t.Tags).LoadAsync(token);
    await ctx.LoadAzData(updated, token);
    var res = new GetTenantResponse(updated, includeTags: true);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(IAzureOperationsUpdateContractLinksPermission))]
  [HttpPost(TenantApiRoutes.UpdateAzureTenantLink)]
  public async Task<ActionResult<GetTenantResponse>> UpdateAzureTenantLink(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromServices] IAzureActions azureActions,
    [FromServices] IUpdateAzureTenantLinkCmd cmd,
    [FromBody] UpdateAzureTenantLinkRequest body,
    CancellationToken cancellationToken)
  {
    if (body.TenantId is 0 ) return BadRequest("No TenantId provided");

    await using var ctx = ctxFactory();
    var tenant = ctx.GetTenantById(body.TenantId);
    if (tenant == null) return NotFound();

    tenant.AzureTenantLink = await cmd.UpdateAzureTenantLink(body, cancellationToken);

    if (tenant.AzureTenantLink is { AzureTenant: {} azTenant })
      await azureActions.DisambiguateAzureTenantType(azTenant,
        allowResettingCustomerToPartner: false,
        cancellationToken: cancellationToken);

    await ctx.Entry(tenant).Collection(t => t.Tags).LoadAsync(cancellationToken);

    var res = new GetTenantResponse(tenant, includeTags: true);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(ITenantsManagePermission))]
  [HttpPost(TenantApiRoutes.Create)]
  public async Task<ActionResult<GetTenantResponse>> PostTenant(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromServices] ICreateTenantCmd cmd,
    CancellationToken cancellationToken,
    [FromBody] CreateTenantRequestBody body)
  {
    await using var ctx = ctxFactory();

    if (!await ctx.GetAllTenants()
          .AnyAsync(t => t.IsMsp && t.Id == body.OwnerTenantId, cancellationToken))
      return BadRequest("Owner tenant must be an MSP");

    var tenant = await cmd.CreateTenant(
      new CreateTenantPayload(body.Name,
        PrincipalId: body.PrincipalId,
        PartnerPrincipalId: body.PartnerPrincipalId,
        LimitToDomains: body.LimitToDomains,
        Slug: body.Slug,
        ParentTenantId: body.ParentTenantId,
        IsMsp: body.IsMsp),
      body.OwnerTenantId,
      cancellationToken,
      throwOnDuplicateTenantName: true);
    return Ok(new GetTenantResponse(tenant, includeTags: true));
  }

  [SubjectPermissionAuthorize(typeof(ITenantsManagePermission))]
  [HttpPost(TenantApiRoutes.BulkCreate)]
  public async Task<ActionResult<ICollection<GetTenantResponse>>> BulkCreate(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromServices] ICreateTenantCmd createTenantCmd,
    [FromServices] IUpdateAzureTenantLinkCmd updateAzureTenantLinkCmd,
    [FromServices] IAzureActions azureActions,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromBody] BulkCreateTenantRequestBody body)
  {
    var cancellationToken = appLifetime.ApplicationStopping;
    await using var ctx = ctxFactory();

    if (!await ctx.GetAllTenants()
          .AnyAsync(t => t.IsMsp && t.Id == body.OwnerTenantId, cancellationToken))
      return BadRequest("Owner tenant must be an MSP");

    var res = new List<GetTenantResponse>();

    foreach (var tenant in body.Tenants)
    {
      var existing = await ctx.GetAllTenants()
        .FirstOrDefaultAsync(t =>
            t.Name == tenant.Name
            || (t.AzureTenantLink != null
                && t.AzureTenantLink.AzureTenant != null
                && t.AzureTenantLink.AzureTenant.PrincipalId == tenant.PrincipalId),
          cancellationToken);
      if (existing == null || existing.OwnerTenantId == body.OwnerTenantId) continue;

      return BadRequest(existing.Name == tenant.Name
        ? $"Tenant with name {tenant.Name} already exists and is owned by another tenant"
        : $"Tenant with principalId {tenant.PrincipalId} already exists and is owned by another tenant");
    }

    var partnerAzTenants = await ctx.GetPartnerAzureTenants()
      .ToListAsync(cancellationToken);

    foreach (var tenant in body.Tenants)
    {
      var existing = await ctx.GetTenantByName(tenant.Name,
        cancellationToken,
        includeTags: true,
        includeAzData: true);
      if (existing != null)
      {
        var partnerPrincipalId = tenant.PrincipalId switch
        {
          not null when tenant.PartnerPrincipalId is not null => tenant.PartnerPrincipalId,
          not null when existing.AzureTenantLink?.AzureTenant is { } existingAzTenant
            => (await azureActions.FindParentPartnerTenant(existingAzTenant,
              partnerAzTenants,
              cancellationToken))?.PrincipalId,
          _ => null
        };

        existing.AzureTenantLink = await updateAzureTenantLinkCmd.UpdateAzureTenantLink(
          new UpdateAzureTenantLinkPayload(
            existing.Id,
            tenant.PrincipalId,
            partnerPrincipalId,
            null),
          cancellationToken);
        var r = new GetTenantResponse(existing, includeTags: true);
        res.Add(r);
      }
      else
      {
        var t = await createTenantCmd.CreateTenant(
          new CreateTenantPayload(
            Name: tenant.Name,
            PrincipalId: tenant.PrincipalId,
            PartnerPrincipalId: tenant.PartnerPrincipalId,
            LimitToDomains: tenant.LimitToDomains,
            Slug: tenant.Slug),
          body.OwnerTenantId,
          cancellationToken);
        res.Add(new GetTenantResponse(t, includeTags: true));
      }
    }

    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(ITenantsManagePermission))]
  [HttpPost(TenantApiRoutes.BulkDelete)]
  public async Task<ActionResult<DeleteTenantsCmdResponse>> DeleteTenants(
    [FromServices] IDeleteTenantsCmd cmd,
    [FromBody] BulkDeleteRequest req,
    CancellationToken token)
  {
    var res = await cmd.Run(req.Ids, userService.GetCurrentUser(), token);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(ITenantsManagePermission))]
  [HttpPost(TenantApiRoutes.BulkMerge)]
  public async Task<ActionResult<CommandResult>> MergeTenants(
    [FromServices] IMergeTenantsCmd cmd,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromBody] MergeTenantsPayload req)
  {
    var res = await cmd.Run(req, appLifetime.ApplicationStopping);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(ITenantsActivationControlPermission))]
  [HttpPatch(TenantApiRoutes.ActivateTenant)]
  public ActionResult ActivateTenant(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromRoute] int id)
  {
    using var ctx = ctxFactory();
    try
    {
      ctx.ActivateTenant(id);
      return NoContent();
    }
    catch (EntityNotFoundException)
    {
      return NotFound();
    }
  }

  [SubjectPermissionAuthorize(typeof(ITenantsActivationControlPermission))]
  [HttpPatch(TenantApiRoutes.DeactivateTenant)]
  public async Task<ActionResult> DeactivateTenant(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromRoute] int id)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsActivationControlPermission>(
      new DefaultKeyParameters(id),
      true);

    await using var ctx = ctxFactory();
    try
    {
      ctx.DeactivateTenant(id);
      return NoContent();
    }
    catch (EntityNotFoundException)
    {
      return NotFound();
    }
  }

  [SubjectPermissionAuthorize(typeof(ITenantsViewPermission))]
  [HttpGet(TenantApiRoutes.GetAzureGroupsAtTenant)]
  public async Task<ActionResult<ICollection<GetAzureGroupResponse>>> GetAzureGroupsAtTenant(
    [FromQuery] string search,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IAzureActions azureActions,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromRoute] int id)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsViewPermission>(
      new DefaultKeyParameters(id),
      true);

    var tenant = ctx.GetTenantById(id, includeAzData: true);
    if (tenant == null) return NotFound();
    if (tenant.AzureTenantLink == null) return Ok(new List<GetAzureGroupResponse>());
    var groups = await azureActions.FindGroupsAtTenant(search, tenant, appLifetime.ApplicationStopping);
    return Ok(groups.Aggregate(new List<GetAzureGroupResponse>(), (agg, g) =>
    {
      if (g.Id != null && g.DisplayName != null) agg.Add(new GetAzureGroupResponse(g.Id, g.DisplayName));
      return agg;
    }));
  }

  [SubjectPermissionAuthorize(typeof(ITenantsViewPermission))]
  [HttpGet(TenantApiRoutes.GetAzureGroupAtTenant)]
  public async Task<ActionResult<GetAzureGroupResponse?>> GetAzureGroupAtTenant(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IAzureActions azureActions,
    [FromServices] IHostApplicationLifetime appLifetime,
    [FromRoute] string groupId,
    [FromRoute] int id)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsViewPermission>(
      new DefaultKeyParameters(id),
      true);

    var tenant = ctx.GetTenantById(id, includeAzData: true);
    if (tenant == null) return NotFound();
    if (tenant.AzureTenantLink == null) return Ok(null);
    var group = await azureActions.GetGroupAtTenant(tenant, groupId, appLifetime.ApplicationStopping);
    if (group is not { Id: not null, DisplayName: not null }) return NotFound();
    return new GetAzureGroupResponse(group.Id, group.DisplayName);
  }

  [SubjectPermissionAuthorize(typeof(ITenantsViewPermission))]
  [HttpGet(TenantApiRoutes.GetComputersExcludedFromMaintenance)]
  public async Task<ActionResult<ICollection<ComputerNameResponse>>> GetComputersExcludedFromMaintenance(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int id)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsViewPermission>(
      new DefaultKeyParameters(id),
      true);
    var tenant = ctx.GetTenantById(id);
    if (tenant == null) return NotFound();

    var computers = ctx.GetComputersExcludedFromMaintenance(id).Select(ComputerNameResponse.Projection);
    return Ok(computers);
  }

  [SubjectPermissionAuthorize(typeof(ITenantsManagePermission))]
  [HttpPost(TenantApiRoutes.AddTags)]
  public async Task<ActionResult> AddTags(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromBody] AddTagsRequest request)
  {
    if (!ModelState.IsValid) return BadRequest(ModelState.Values.SelectMany(v => v.Errors));
    var ids = request.EntityIds;

    if (ids.Count == 0) return BadRequest("No tenant ids were provided");
    if (request.TagIds.Count == 0) return BadRequest("No tag ids were provided");

    foreach (var tenantId in ids)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsManagePermission>(
        new DefaultKeyParameters(tenantId),
        strict: true);
    }

    foreach (var tagId in request.TagIds)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tag, ITagsViewPermission>(
        new DefaultKeyParameters(tagId),
        strict: true);
    }

    await using var ctx = ctxFactory();
    var tenantIds = await ctx.GetTenantsByIds(ids.ToArray()).Select(a => a.Id).ToListAsync();
    if (tenantIds.Count == 0) return NotFound();

    var tags = await ctx.GetTagsByIds(request.TagIds).ToListAsync();
    if (tags.Count == 0) return NotFound();

    await ctx.AddTenantTags(tags.Select(a => a.Id).ToList(), tenantIds);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ITenantsManagePermission))]
  [HttpPost(TenantApiRoutes.RemoveTags)]
  public async Task<ActionResult> RemoveTags(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromBody] RemoveTagsRequest request)
  {
    if (!ModelState.IsValid) return BadRequest(ModelState.Values.SelectMany(v => v.Errors));
    var ids = request.EntityIds;

    if (ids.Count == 0) return BadRequest("No tenant ids were provided");
    if (request.TagIds.Count == 0) return BadRequest("No tag ids were provided");

    foreach (var tenantId in ids)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsManagePermission>(
        new DefaultKeyParameters(tenantId),
        strict: true);
    }

    foreach (var tagId in request.TagIds)
    {
      await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tag, ITagsViewPermission>(
        new DefaultKeyParameters(tagId),
        strict: true);
    }

    await using var ctx = ctxFactory();
    var tenantIds = await ctx.GetTenantsByIds(ids.ToArray()).Select(a => a.Id).ToListAsync();
    if (tenantIds.Count == 0) return NotFound();


    var tags = await ctx.GetTagsByIds(request.TagIds).ToListAsync();
    if (tags.Count == 0) return NotFound();

    await ctx.RemoveEntityTags<TenantTag>(tags.Select(a => a.Id).ToList(), tenantIds);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ITenantsManagePermission))]
  [HttpPost(TenantApiRoutes.SetParentTenant)]
  public async Task<ActionResult> SetParentTenant(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromBody] SetParentTenantRequest request,
    CancellationToken token)
  {
    var ids = request.TenantIds;

    if (!ids.Any()) return BadRequest("No tenant ids were provided");
    if (request.ParentTenantId is 0) return BadRequest("No parent tenant id was provided");
    if (ids.Contains(request.ParentTenantId))
      return BadRequest("A tenant cannot be assigned as its own parent");

    await using var ctx = ctxFactory();

    // ensure that the parent tenant is not a child of any tenants being set
    var res = await ctx.CheckForCyclicalAncestor(ids, request.ParentTenantId, token);
    if (!res.IsSuccess) return BadRequest(res.Reason);

    if (await ctx.GetTenantsByIds(ids).CountAsync(token) != ids.Count)
      return NotFound();

    var parentTenant = ctx.GetTenantById(request.ParentTenantId);
    if (parentTenant is null) return NotFound();

    await ctx.SetParentTenant(ids, request.ParentTenantId, token);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ITenantsManagePermission))]
  [HttpPost(TenantApiRoutes.RemoveParentTenant)]
  public async Task<ActionResult> RemoveParentTenant(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    [FromBody] RemoveParentTenantRequest request,
    CancellationToken token)
  {
    var ids = request.TenantIds;

    if (!ids.Any()) return BadRequest("No tenant ids were provided");
    if (request.ParentTenantId is 0) return BadRequest("No parent tenant id was provided");

    await using var ctx = ctxFactory();
    if (await ctx.GetTenantsByIds(ids).Select(a => a.Id).CountAsync(token) != ids.Count)
      return NotFound();

    var parentTenant = ctx.GetTenantById(request.ParentTenantId);
    if (parentTenant is null) return NotFound();
    var tenantsToRemove = await ctx.GetTenantsByIds(ids)
      .Where(t => t.ParentTenantId == request.ParentTenantId)
      .Select(a => a.Id)
      .ToListAsync(token);
    await ctx.SetParentTenant(tenantsToRemove, null, token);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ITenantsViewPermission))]
  [HttpPost(TenantApiRoutes.ResolveAssignmentsForMaintenanceItem)]
  public async Task<ActionResult<ResolveAssignmentsForMaintenanceItemResult>> ResolveAssignmentsForMaintenanceItem(
    [FromServices] IResolveAssignmentsForMaintenanceItemCmd cmd,
    [FromServices] ImmybotDbContext ctx,
    [FromBody] ResolveAssignmentsForMaintenanceItemRequest req,
    CancellationToken cancellationToken)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Tenant, ITenantsManagePermission>(
      new DefaultKeyParameters(req.TenantId),
      strict: true);

    var tenant = ctx.GetTenantById(req.TenantId);
    if (tenant is null) return NotFound();

    var res = await cmd.Run(tenant, req.MaintenanceIdentifier, req.MaintenanceType, cancellationToken);
    return Ok(res);
  }

  [SubjectPermissionAuthorize(typeof(ITenantsViewPermission))]
  [HttpGet(TenantApiRoutes.GetComputerCounts)]
  public async Task<ActionResult<ICollection<TenantComputerCountResponse>>> GetTenantComputerCounts(
    [FromServices] ImmybotDbContext ctx)
  {
    var filter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
    var query = ctx.Computers
      .AsNoTracking()
      .Where(filter)
      .GroupBy(c => c.TenantId)
      .Select(g => new TenantComputerCountResponse
      {
        TenantId = g.Key,
        ComputerCount = g.Count()
      });

    return Ok(query);
  }
}
