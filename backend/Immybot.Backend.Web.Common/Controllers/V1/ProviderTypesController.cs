using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class ProviderTypesController(
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IUserService userService) : ControllerBase
{
  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(ProviderTypeApiRoutes.BindParameters)]
  public async Task<ActionResult<DynamicFormBindResult>> BindParameters(
    [FromServices] IDynamicFormService dynamicFormService,
    [FromServices] IProviderActions providerActions,
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] Guid providerType,
    [FromBody] IntegrationBindParametersRequest request,
    CancellationToken cancellationToken)
  {
    var suppliedParameterValues = request.ParameterValues;

    var type = providerActions.GetProviderType(providerType, includeLinkFormSchema: true);


    // Password values are not sent to the frontend. When loading a provider link details form,
    // none of the password parameters will be passed to this api method. Instead, the frontend
    // will pass a providerLinkId, and this method will use the password values stored in the database
    if (request.ProviderLinkId.HasValue)
    {
      await subjectPermissionAuthorizationService.AuthorizeAsync<IIntegrationsViewPermission>(
        User,
        strict: false);
      var link = ctx.GetProviderLink(request.ProviderLinkId.Value);
      if (link is null) return NotFound("Provider link not found");

      // update the supplied parameter values with password values that are stored in the database
      if (type.ConfigurationForm is not null)
      {
        var passwordFields = type.ConfigurationForm.ShowCommandInfo.ParameterSets
          .SelectMany(a => a.Parameters)
          .Where(a => a.IsValueStripped)
          .Select(a => a.Name).ToList();

        var passwordValuesToUseFromProviderLink = passwordFields.Where(field =>
          !suppliedParameterValues.ContainsKey(field) ||
          suppliedParameterValues[field] is { Value: null });

        foreach (var field in passwordValuesToUseFromProviderLink)
        {
          if (link.ProviderTypeFormData.TryGetProperty(field, out var jsonVal))
          {
            suppliedParameterValues[field] = new ParameterValue(jsonVal);
          }
        }
      }
    }

    DynamicFormBindResultWithConvertedParameters resultWithConvertedParameters;

    var values = suppliedParameterValues
      .Where(a => a.Value?.Value != null)
      .ToDictionary<KeyValuePair<string, ParameterValue>, string, object?>(value => value.Key,
        value => value.Value?.Value);

    if (type.IsDynamic)
    {
      var isMsp = userService.IsMspUser();
      var canAccessParentTenant =
        await subjectPermissionAuthorizationService.AuthorizeAsync<IScriptCanAccessParentTenantPermission>(
          User,
          strict: false);
      var (paramBlock, dynamicParamBlock) = providerActions.GetDynamicProviderParamBlock(providerType);
      resultWithConvertedParameters = await dynamicFormService.BindParameters(
        canAccessMspResources: isMsp,
        canAccessParentTenant: canAccessParentTenant,
        null,
        paramBlock,
        dynamicParamBlock,
        DatabaseType.Global,
        cancellationToken,
        suppliedParameterValues);
    }
    else
    {
      var formType = providerActions.GetProviderFormType(providerType);
      if (formType is null) return BadRequest("Integration does not support dynamic form type");

      resultWithConvertedParameters = dynamicFormService.BindParameters(formType, values);
    }

    var resWithoutConvertedParameters = resultWithConvertedParameters.GetBaseResult();
    return Ok(resWithoutConvertedParameters);
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(ProviderTypeApiRoutes.GetAll)]
  public async Task<ActionResult<IEnumerable<ProviderTypeDto>>> GetAllProviderTypes(
    [FromServices] IProviderActions providerActions,
    [FromQuery] bool includeLinkFormSchemas = false)
  {
    if (includeLinkFormSchemas)
    {
      await subjectPermissionAuthorizationService.AuthorizeAsync<IIntegrationsViewPermission>(User, strict: true);
    }

    return Ok(providerActions.GetAllProviderTypes(
      includeLinkFormSchemas: includeLinkFormSchemas));
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(ProviderTypeApiRoutes.GetDeviceGroups)]
  public async Task<ActionResult<ICollection<IDeviceGroup>>> GetDeviceGroups(
    [FromServices] IProviderActions providerActions,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromRoute] Guid deviceGroupTypeId,
    [FromQuery] int providerLinkId,
    CancellationToken token,
    [FromQuery] string? externalClientId = null)
  {
    var link = ctx.GetProviderLink(providerLinkId, includeClients: true);
    if (link == null) return NotFound();

    if (string.IsNullOrEmpty(externalClientId))
    {
      await subjectPermissionAuthorizationService
        .AuthorizeGlobalAsync<ITenantsViewPermission>(User, strict: true);
    }
    else
    {
      var filter = permissionFilterBuilder.BuildFilterExpression<ProviderClient, ITenantsViewPermission>();
      var found = await ctx.ProviderClients
        .AsNoTracking()
        .Where(filter)
        .Where(a => a.ExternalClientId == externalClientId && a.ProviderLinkId == providerLinkId)
        .Select(a => a.ExternalClientId)
        .AnyAsync(token);
      if (!found) return NotFound();
    }

    var groups = await providerActions
      .GetDeviceGroups(link, deviceGroupTypeId, token, externalClientId);
    return Ok(groups);
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(ProviderTypeApiRoutes.GetClientGroups)]
  public async Task<ActionResult<ICollection<IClientGroup>>> GetClientGroups(
    [FromServices] IProviderActions providerActions,
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] Guid clientGroupTypeId,
    [FromQuery] int providerLinkId,
    CancellationToken token,
    [FromQuery] int? tenantId = null)
  {
    if (tenantId is null)
    {
      await subjectPermissionAuthorizationService
        .AuthorizeGlobalAsync<ITenantsViewPermission>(User, strict: true);
    }
    else
    {
      var filter = permissionFilterBuilder.BuildFilterExpression<ProviderClient, ITenantsViewPermission>();
      var found = await ctx.ProviderClients
        .AsNoTracking()
        .Where(filter)
        .Where(a => a.LinkedToTenantId == tenantId && a.ProviderLinkId == providerLinkId)
        .Select(a => a.ExternalClientId)
        .AnyAsync(token);
      if (!found) return NotFound();
    }

    var link = ctx.GetProviderLink(providerLinkId, includeClients: true);
    if (link == null) return NotFound();

    ProviderClient? client = null;
    if (tenantId != null)
    {
      client = link.ProviderClients.FirstOrDefault(a => a.LinkedToTenantId == tenantId);
      if (client == null)
        return NotFound("No client found in the psa for the provided tenantId");
    }
    var groups = await providerActions
      .GetClientGroups(link, clientGroupTypeId, token, client?.ExternalClientId);
    return Ok(groups);
  }
}
