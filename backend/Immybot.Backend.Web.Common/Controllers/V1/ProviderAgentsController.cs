using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.AgentIdentification;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Shared.Primitives;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class ProviderAgentsController(
  IResourceAuthorizerFlow resourceAuthorizerFlow,
  IPermissionFilterBuilder permissionFilterBuilder,
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
  IUserService userService) : ControllerBase
{
  private const int _maxAttempts = 5;

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ProviderAgentApiRoutes.GetIdentificationLogs)]
  public async Task<ActionResult<IEnumerable<GetProviderAgentIdentificationLogResponse>>> GetIdentificationLogs(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int agentId)
  {
    var agentTenantId = await ctx.ProviderAgents
      .AsNoTracking()
      .Where(a => a.Id == agentId)
      .Select(a => a.ProviderClient!.LinkedToTenantId!)
      .FirstOrDefaultAsync();
    if (agentTenantId is null) return NotFound();

    await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IComputersViewPermission>(User,
      agentTenantId.Value,
      strict: false);

    var logs = ctx.AgentIdentificationLogs
      .AsNoTracking()
      .Where(a => a.ProviderAgentId == agentId).Select(GetProviderAgentIdentificationLogResponse.Projection)
      .OrderBy(a => a.TimeUtc);

    return Ok(logs);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderAgentApiRoutes.BulkDeletePendingAgents)]
  public async Task<ActionResult<BulkDeleteResponse>> BulkDeletePendingAgents(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IDeleteProviderAgentsCmd cmd,
    [FromBody] BulkDeleteRequest req)
  {
    if (req.Ids.Count == 0) return BadRequest("No agents were specified to be deleted");

    var computers = (await ctx.GetPendingProviderAgents()
      .Where(a => req.Ids.Contains(a.Id))
      .Select(a => new { a.Id, LinkedToTenantId = a.ProviderClient != null ? a.ProviderClient.LinkedToTenantId : null })
      .ToListAsync());
    foreach (var computer in computers)
    {
      if (computer.LinkedToTenantId.HasValue)
      {
        await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IComputersManagePermission>(User,
          computer.LinkedToTenantId.Value,
          strict: true);
      }
      else
      {
        await subjectPermissionAuthorizationService
          .AuthorizeGlobalAsync<IComputersManagePermission>(User, strict: true);
      }
    }

    if (computers.Count == 0) return Ok(new BulkDeleteResponse(0));

    var user = userService.GetCurrentUser().DisplayName ?? "user";
    await cmd.Run(computers.Select(a => a.Id).ToList(), $"Deleted by {user}");

    return Ok(new BulkDeleteResponse(computers.Count));
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ProviderAgentApiRoutes.GetPending)]
  public ActionResult<GetPendingResponse> GetPending(
    [FromServices] ImmybotDbContext ctx,
    [FromQuery] string? filter = null,
    [FromQuery] string? sort = null,
    [FromQuery] int take = 50,
    [FromQuery] int skip = 0,
    [FromQuery] bool sortDesc = true,
    [FromQuery] bool includeOffline = true,
    [FromQuery] int? tenantId = null,
    [FromQuery] int? providerLinkId = null,
    [FromQuery] ProviderAgentFilter? agentFilter = ProviderAgentFilter.OnlyPendingIdentification)
  {
    var permissionFilter = permissionFilterBuilder.BuildFilterExpression<Computer, IComputersViewPermission>();
    var deletedComputerQuery = ctx.GetDeletedComputers().Where(permissionFilter);

    if (tenantId is not null)
    {
      deletedComputerQuery = deletedComputerQuery.Where(a => a.TenantId == tenantId);
    }

    var deletedComputerIds = deletedComputerQuery.Select(computer => computer.Id).ToList();

    var agentQuery = agentFilter switch
    {
      ProviderAgentFilter.OnlyPendingIdentification => ctx.GetPendingProviderAgents()
        .Where(a => a.ProviderLink != null && !a.ProviderLink.Disabled && a.ProviderLink.HealthStatus == HealthStatus.Healthy),
      ProviderAgentFilter.OnlyIdentified => ctx.GetProviderAgents()
        .IgnoreQueryFilters()
        .Where(a => a.ComputerId != null && a.DeletedAt == null),
      ProviderAgentFilter.AssociatedToDeletedComputers => ctx.GetProviderAgents().Where(agent => deletedComputerIds.Contains(agent.ComputerId ?? 0)),
      ProviderAgentFilter.RequiresManualDecision => ctx.GetProviderAgents()
        .Where(a => a.ProviderLink != null && !a.ProviderLink.Disabled && a.ProviderLink.HealthStatus == HealthStatus.Healthy && a.IdentificationFailures.Any(f => !f.Resolved && f.RequiresManualResolution)),
      _ => ctx.GetProviderAgents().IgnoreQueryFilters().Where(a => a.DeletedAt == null)
    };

    agentQuery = agentQuery.TagForTelemetry();

    if (providerLinkId.HasValue)
    {
      agentQuery = agentQuery.Where(a => a.ProviderLinkId == providerLinkId.Value);
    }

    if (tenantId is not null)
    {
      agentQuery = agentQuery.Where(a => a.ProviderClient != null && a.ProviderClient.LinkedToTenantId == tenantId);
    }

    if (!includeOffline)
    {
      agentQuery = agentQuery.Where(a => a.IsOnline);
    }

    var iLikeFilter = "%" + filter + "%";

    if (!string.IsNullOrEmpty(filter))
    {
      agentQuery = agentQuery.Where(a =>
        (a.DeviceDetails.DeviceName != null && EF.Functions.ILike(a.DeviceDetails.DeviceName, iLikeFilter)) ||
        (a.DeviceDetails.SerialNumber != null && EF.Functions.ILike(a.DeviceDetails.SerialNumber, iLikeFilter)) ||
        (a.DeviceDetails.OperatingSystemName != null &&
         EF.Functions.ILike(a.DeviceDetails.OperatingSystemName, iLikeFilter)) ||
        (a.ProviderClient != null && EF.Functions.ILike(a.ProviderClient.ExternalClientName, iLikeFilter)));
    }

    // get agentCount for pagination
    var agentCount = agentQuery.Count();

    if (!string.IsNullOrEmpty(sort))
    {
      agentQuery = sort switch
      {
        "computerName" when sortDesc => agentQuery.OrderByDescending(a => a.DeviceDetails.DeviceName),
        "computerName" when !sortDesc => agentQuery.OrderBy(a => a.DeviceDetails.DeviceName),
        "dateAdded" when sortDesc => agentQuery.OrderByDescending(a => a.DateAddedUTC),
        "dateAdded" when !sortDesc => agentQuery.OrderBy(a => a.DateAddedUTC),
        "operatingSystemName" when sortDesc => agentQuery.OrderByDescending(a => a.DeviceDetails.OperatingSystemName),
        "operatingSystemName" when !sortDesc => agentQuery.OrderBy(a => a.DeviceDetails.OperatingSystemName),
        "serial" when sortDesc => agentQuery.OrderByDescending(a => a.DeviceDetails.SerialNumber),
        "serial" when !sortDesc => agentQuery.OrderBy(a => a.DeviceDetails.SerialNumber),
        "externalClientName" when sortDesc => agentQuery.OrderByDescending(a => a.ProviderClient != null ? a.ProviderClient.ExternalClientName : null),
        "externalClientName" when !sortDesc => agentQuery.OrderBy(a => a.ProviderClient != null ? a.ProviderClient.ExternalClientName : null),
        _ => agentQuery
      };
    }

    if (skip > 0)
    {
      agentQuery = agentQuery.Skip(skip);
    }

    if (take > 0)
    {
      agentQuery = agentQuery.Take(take);
    }


    var agents = agentQuery
      .Select(GetPendingAgentResponse.Projection)
      .ToList();

    var agentIds = agents.Select(a => a.Id);

    // build multiple queries
    var failures = ctx.AgentIdentificationFailures
      .AsNoTracking()
      .TagForTelemetry()
      .Where(a => !a.Resolved && agentIds.Contains(a.PendingAgentId))
      .Select(GetAgentIdentificationFailureResponse.Projection)
      .ToList();

    foreach (var agent in agents)
    {
      agent.IdentificationFailures.AddRange(failures.Where(a => a.PendingAgentId == agent.Id));
      agent.IsComputerDeleted = deletedComputerIds.Contains(agent.ComputerId ?? 0);
    }

    return Ok(new GetPendingResponse(agentCount, agents));
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ProviderAgentApiRoutes.GetPendingAgentConflictsForComputer)]
  public async Task<ActionResult<List<GetFailedPendingAgentResponse>>> GetPendingAgentConflictsForComputer(
     [FromServices] ImmybotDbContext ctx,
     [FromRoute] int computerId)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      strict: true);
    var computer = ctx.GetComputerById(computerId);
    if (computer is null) return NotFound();

    var failures = await ctx.AgentIdentificationFailures
      .AsNoTracking()
      .Where(a => a.RequiresManualResolution && !a.Resolved && a.ComputerId == computerId)
      .Select(GetAgentIdentificationFailureResponse.Projection)
      .OrderByDescending(a => a.CreatedDateUTC)
      .ToListAsync();

    if (!failures.Any()) return Ok(Array.Empty<GetFailedPendingAgentResponse>());

    var keys = failures.Select(a => a.PendingAgentId).Distinct().ToList();
    var pendings = await ctx.GetProviderAgents().Where(a => keys.Contains(a.Id))
      .Select(GetFailedPendingAgentResponse.Projection)
      .ToListAsync();

    foreach (var p in pendings)
    {
      p.IdentificationFailures.AddRange(failures.Where(a => a.PendingAgentId == p.Id));
    }

    return Ok(pendings);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderAgentApiRoutes.ResolveFailuresForAgents)]
  public async Task<ActionResult> ResolveFailuresForAgents(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IAgentIdentificationManager agentIdentificationManager,
    [FromBody] ResolveFailuresRequestBody body)
  {
    var agents = ctx.GetProviderAgents()
      .Where(c => c.IdentificationFailures.Count(f => !f.Resolved) >= _maxAttempts ||
                  c.IdentificationFailures.Any(f =>
                    !f.Resolved && f.RequiresManualResolution && f.ManualResolutionDecision == null));

    if (body.AgentIds.Any())
    {
      agents = agents.Where(a => body.AgentIds.Contains(a.Id));
    }

    var currentUser = userService.GetCurrentUser();
    if (!currentUser.IsMsp)
    {
      agents = agents.Where(a => a.ProviderClient != null && a.ProviderClient.LinkedToTenantId == currentUser.TenantId);
    }

    ctx.ResolveIdentificationFailuresForAgents(await agents.Select(a => a.Id).ToListAsync());

    foreach (var agent in agents)
    {
      await agentIdentificationManager.EnqueueAgentForIdentification(agent);
    }

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderAgentApiRoutes.ResolveFailuresForAgent)]
  public async Task<ActionResult> RetryIdentification(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IAgentIdentificationManager agentIdentificationManager,
    [FromRoute] int agentId)
  {
    var agent = await ctx.GetProviderAgents().FirstOrDefaultAsync(a => a.Id == agentId);
    if (agent == null) return NotFound();

    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<ProviderAgent, IComputersViewPermission>(
      new DefaultKeyParameters(agentId),
      strict: true);

    ctx.ResolveIdentificationFailuresForAgents([agentId]);

    await agentIdentificationManager.EnqueueAgentForIdentification(agent);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderAgentApiRoutes.ResolveFailure)]
  public async Task<ActionResult> ResolveFailure(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IPendingAgentResolverActions resolverActions,
    [FromServices] IAgentIdentificationManager agentIdentificationManager,
    [FromRoute] int failureId,
    [FromQuery] AgentIdentificationManualResolutionDecision manualResolutionDecision)
  {
    var failure = await ctx.AgentIdentificationFailures
      .AsNoTracking()
      .FirstOrDefaultAsync(a => a.Id == failureId);
    if (failure == null) return NotFound();

    var pending = await ctx.GetProviderAgents(includeClients: true)
      .FirstOrDefaultAsync(a => a.Id == failure.PendingAgentId);
    if (pending == null) return NotFound();

    var computer = await ctx.Computers
      .Include(a => a.Agents)
      .AsNoTracking()
      .FirstOrDefaultAsync(a => a.Id == failure.ComputerId);

    var existingAgent = computer?.Agents
      .FirstOrDefault(a => a.ProviderLinkId == pending.ProviderLinkId && a.Id == failure.ExistingAgentId);

    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<ProviderAgent, IComputersManagePermission>(
      new DefaultKeyParameters(pending.Id),
      strict: true);

    switch (manualResolutionDecision)
    {
      case AgentIdentificationManualResolutionDecision.DeleteExisting:
        if (computer is null) return await Identify();
        await resolverActions.ReplaceComputer(pending, computer, CancellationToken.None);
        break;
      case AgentIdentificationManualResolutionDecision.GenerateNewDeviceId:
        if (computer is null || existingAgent is null) return await Identify();
        await resolverActions.KeepBothComputers(pending, computer, existingAgent, CancellationToken.None);
        break;
      case AgentIdentificationManualResolutionDecision.OverwriteExisting:
        if (existingAgent is null) return await Identify();
        await resolverActions.ReplaceAgent(pending, existingAgent, CancellationToken.None);
        break;
    }

    ctx.ResolveIdentificationFailuresForAgents(new[] { pending.Id });

    return NoContent();

    async Task<NoContentResult> Identify()
    {
      // just re-attempt identification if the existing agent no longer exists.
      ctx.ResolveIdentificationFailuresForAgents(new[] { pending.Id });
      await agentIdentificationManager.EnqueueAgentForIdentification(pending);
      return NoContent();
    }
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpGet(ProviderAgentApiRoutes.GetPendingCounts)]
  public ActionResult<GetPendingCountsResponse> GetPendingCounts(
    [FromServices] ImmybotDbContext ctx)
  {
    IQueryable<ProviderAgent> q = ctx.GetPendingProviderAgents(includeLinks: true)
      .Where(a => a.ProviderLink != null && !a.ProviderLink.Disabled && a.ProviderLink.HealthStatus == HealthStatus.Healthy)
      .OrderByDescending(a => a.Id);

    var currentUser = userService.GetCurrentUser();
    if (!currentUser.IsMsp)
    {
      var currentTenantId = currentUser.TenantId;
      q = q.Where(a => a.ProviderClient != null && a.ProviderClient.LinkedToTenantId == currentTenantId);
    }
    var yesterday = DateTime.UtcNow.AddHours(-24);

    var total = q.Count();
    var offline = q.Count(a => !a.IsOnline);
    var failed = q
      .Count(a => a.IdentificationFailures.Count(f => !f.Resolved && a.IsOnline) >= _maxAttempts);
    var manual =
      q.Count(a => a.IsOnline && a.IdentificationFailures.Any(f => !f.Resolved && f.RequiresManualResolution));
    var recent = q.Count(a => a.DateAddedUTC > yesterday);
    var active = q
      .Count(a => a.IsOnline &&
                  a.IdentificationFailures.Count(f => !f.Resolved) < _maxAttempts &&
                  !a.IdentificationFailures.Any(f => !f.Resolved && f.RequiresManualResolution));
    return Ok(new GetPendingCountsResponse(total, offline, failed, manual, recent, active));
  }

  /// <summary>
  /// Identify agents that are marked with  requiring manual identification
  /// </summary>
  [SubjectPermissionAuthorize(typeof(IComputerIdentifyAgentsPermission))]
  [HttpPost(ProviderAgentApiRoutes.IdentifyAgents)]
  public async Task<ActionResult<OpResult>> IdentifyAgents(
    [FromServices] IIdentifyAgentCmd cmd,
    [FromBody] IdentifyAgentRequest request,
    CancellationToken token)
  {
    await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IComputerIdentifyAgentsPermission>(User,
      request.TenantId,
      strict: true);

    var res = await cmd.IdentifyAgent(request.AgentIds, request.TenantId, token);
    return Ok(res);
  }
}
