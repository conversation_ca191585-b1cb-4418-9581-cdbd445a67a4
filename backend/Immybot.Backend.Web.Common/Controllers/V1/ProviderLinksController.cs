using System.Text.Json;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Interface.Jobs;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Lib.Providers;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib.JsonConverters;
using Immybot.Shared.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class ProviderLinksController(
  IUserService userService,
  IResourceAuthorizerFlow authorizer,
  ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService) : ControllerBase
{
  private static readonly JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
  static ProviderLinksController()
  {
    _jsonSerializerOptions.Converters.Add(new JObjectConverter());
  }

  #region Private Static Methods

  private static async Task<GetProviderLinkResponse> PopulateProviderLinkResponse(
    GetProviderLinkResponse response,
    ProviderLink link,
    IProviderActions providerActions,
    bool throwIfAgentInstallerVersionNotSet,
    CancellationToken token)
  {
    try
    {
      // don't do anything if this is the test provider link
#if DEBUG
      if (response.ProviderTypeId == Guid.Empty)
        return response;
#endif

      var metadata = providerActions.GetProviderType(response.ProviderTypeId, includeLinkFormSchema: true);
      response.InputsWithStoredPasswords = new List<string>();
      var obj = response.ProviderTypeFormData;
      if (metadata.ConfigurationForm is not null)
      {
        var passwordFields = metadata.ConfigurationForm.ShowCommandInfo.ParameterSets
          .SelectMany(a => a.Parameters)
          .Where(a => a.IsValueStripped)
          .Select(a => a.Name).ToList();
        if (passwordFields.Any())
        {
          response.ProviderTypeFormData = obj.FilterKeys(key =>
          {
            if (passwordFields.Contains(key) is false)
            {
              return true;
            }

            response.InputsWithStoredPasswords.Add(key);
            return false;
          });
        }
      }

      response.ProviderPluginBaseUrl = $"/plugins/api/v1/{link.Id}";

      // capabilities
      response.ProviderCapabilities = metadata.ProviderCapabilities;
      response.SupportsDeviceUpdating = metadata.SupportsDeviceUpdating;
      response.SupportedCrossProviderInitializationLinkages = metadata.SupportedCrossProviderInitializationLinkages;
      response.SupportedCrossProviderClientLinkages = metadata.SupportedCrossProviderClientLinkages;

      var (provider, failedMessage) = await GetProvider(link, providerActions, token);

      if (provider is ISupportsGetLatestAgentVersion getLatestAgentVersionProvider)
      {
        try
        {
          response.LatestAgentVersion = getLatestAgentVersionProvider.GetLatestAgentVersion(token).ToNormalizedString();
        }
        catch (Exception ex) when (!ex.IsCancellationException(token))
        {
          if (throwIfAgentInstallerVersionNotSet) throw;
          response.GetLatestAgentVersionFailedMessage = ex.Message;
        }
      }

      if (failedMessage != null)
      {
        response.GetProviderFailedMessage = failedMessage;
      }
    }
    catch (MissingProviderTypeException)
    {
      response.GetFormSchemaFailedMessage = "The provider type specified by the integration is not supported";
    }
    return response;
  }

  private static async Task<(IProvider? provider, string? failedMessage)> GetProvider(
    ProviderLink link,
    IProviderActions providerActions,
    CancellationToken token)
  {
    try
    {
      var provider = await providerActions.GetProvider(link, token, noProviderConstruction: true);
      return (provider, null);
    }
    catch (DisallowedLinkAccessException)
    {
      return (null, "The integration has been deleted from the database and is no longer valid");
    }
    catch (MissingProviderTypeException)
    {
      return (null, "The provider type specified by the integration is not supported");
    }
    catch (ProviderConstructionFailedException)
    {
      return (null, "The provider type specified by the integration failed to initialize");
    }
    catch (GetProviderTimeoutException ex)
    {
      return (null, ex.Message);
    }
    catch (InvalidProviderFormDataException ex)
    {
      return (null, ex.Message);
    }
  }

  private static JsonElement CopyStoredPasswordInputValuesFromNewFormSchema(
    Guid providerTypeId,
    JsonElement providerTypeFormData,
    JsonElement providerTypeFormDataWithPasswords,
    IProviderActions providerActions)
  {
    var providerType = providerActions.GetProviderType(providerTypeId, includeLinkFormSchema: true);

    var options = new JsonWriterOptions
    {
      Indented = true
    };

    using var stream = new MemoryStream();
    using var writer = new Utf8JsonWriter(stream, options);

    writer.WriteStartObject();
    // first copy all the stuff in form data to the new doc
    foreach (var obj in providerTypeFormData.EnumerateObject())
    {
      obj.WriteTo(writer);
    }
    // then add any password items that are in the existing link that aren't in form data
    var parameters = providerType.ConfigurationForm?.ShowCommandInfo.ParameterSets
      .SelectMany(a => a.Parameters) ?? new List<Parameter>();
    foreach (var input in parameters)
    {
      if (!input.IsValueStripped)
      {
        continue;
      }

      if ((providerTypeFormData.TryGetProperty(input.Name, out var newEl) && newEl.ValueKind is not (JsonValueKind.Undefined or JsonValueKind.Null))
          || !providerTypeFormDataWithPasswords.TryGetProperty(input.Name, out var el))
      {
        continue;
      }

      // for any password type inputs that are on the existing provider link that aren't on the
      // provided provider link, copy the password value from the existing before verifying,
      // since the frontend doesn't have access to password type links that are
      // stored in the db
      writer.WritePropertyName(input.Name);
      el.WriteTo(writer);
    }

    writer.WriteEndObject();
    writer.Flush();
    return JsonSerializer.Deserialize<JsonElement>(stream.ToArray());
  }

  private static JsonElement CopyStoredPasswordInputValues(
    Guid providerTypeId,
    JsonElement providerTypeFormData,
    IProviderActions providerActions,
    JsonElement providerTypeFormDataWithPasswords)
  {
    return CopyStoredPasswordInputValuesFromNewFormSchema(
      providerTypeId,
      providerTypeFormData,
      providerTypeFormDataWithPasswords,
      providerActions);
  }

  #endregion Private Static Methods

  #region Provider Links

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpPost(ProviderLinkApiRoutes.ImmyAgentProviderOldRekeyEndpoint)]
  public Task<IActionResult> ImmyAgentOldRekeyRedirect(
    CancellationToken cancellationToken,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromServices] IDomainEventEmitter domainEventEmitter,
    [FromServices] IOptions<AppSettingsOptions> appSettingsOptions)
  {
    var immyAgentProvider = ctx
      .GetProviderLinks(providerTypeId: Guid.Parse(ImmyAgentProvider.Constants.ProviderId))
      .Single();

    return ProviderCustomRoute(
      cancellationToken,
      ctx,
      providerActions,
      domainEventEmitter,
      appSettingsOptions,
      immyAgentProvider.Id,
      Providers.ImmyAgentProvider.Controllers.ApiRoutes.AgentInstaller.RequestAgentRekey);
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(ProviderLinkApiRoutes.ProviderCustomRoute)]
  [HttpPost(ProviderLinkApiRoutes.ProviderCustomRoute)]
  [HttpHead(ProviderLinkApiRoutes.ProviderCustomRoute)]
  public async Task<IActionResult> ProviderCustomRoute(
    CancellationToken cancellationToken,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromServices] IDomainEventEmitter domainEventEmitter,
    [FromServices] IOptions<AppSettingsOptions> appSettingsOptions,
    [FromRoute] int providerLinkId,
    [FromRoute] string? catchAll)
  {
    var link = ctx.GetProviderLink(providerLinkId);

    if (link is null) return NotFound("The integration was not found.");

    var request = HttpContext.Request;
    var logState = new Dictionary<string, object>
    {
      { "request_method", request.Method },
      { "request_path", request.Path },
      { "request_headers", request.Headers.ToArray() },
      { "request_query", request.Query.ToArray() }
    };

    string? body = null;
    try
    {
      var contentLength = request.Headers.ContentLength ?? request.ContentLength;
      if (contentLength is null)
      {
        logState.Add("request_body", "Content-Length was not specified. Not logging request body in case it is large.");
      }
      else if (contentLength > appSettingsOptions.Value.MaxProviderRouteContentLength)
      {
        logState.Add("request_body", $"Request body is too large to log: Body: {contentLength} Bytes | Max: {appSettingsOptions.Value.MaxProviderRouteContentLength} Bytes");
      }
      else
      {
        body = await HttpContext.ReadRequestBodyAsStringAsync();
        logState.Add("request_body", body);
      }
    }
    catch
    {
      logState.Add("request_body", "Failing to parse request body");
    }

    var log = new ProviderAuditLog(
      providerLinkId,
      link.Name,
      $"Inbound HTTP Request - {catchAll}",
      Input: logState);
    domainEventEmitter.EmitEvent(new ProviderAuditLogAddedEvent(log));

    if (link.Disabled)
    {
      var msg = "The integration is currently disabled and cannot respond to the request.";
      domainEventEmitter.EmitEvent(new ProviderAuditLogAddedEvent(log with
      {
        TimeUtc = DateTime.UtcNow,
        CorrelationId = log.Id,
        ErrorMessage = msg
      }));
      return new ConflictObjectResult(msg);
    }

    if (link.HealthStatus is Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy)
    {
      const string msg = "The integration is currently unhealthy and cannot respond to the request.";
      domainEventEmitter.EmitEvent(new ProviderAuditLogAddedEvent(log with
      {
        TimeUtc = DateTime.UtcNow,
        CorrelationId = log.Id,
        ErrorMessage = msg
      }));
      return new ConflictObjectResult(msg);
    }

    var provider = await providerActions.GetProvider(link, cancellationToken);

    if (provider is not ISupportsHttpRequest httpProvider)
    {
      const string msg = "The integration does not support http requests.";
      domainEventEmitter.EmitEvent(new ProviderAuditLogAddedEvent(log with
      {
        TimeUtc = DateTime.UtcNow,
        CorrelationId = log.Id,
        ErrorMessage = msg
      }));
      return new ConflictObjectResult(msg);
    }

    try
    {
      var res = await httpProvider.HandleHttpRequest(HttpContext, body, catchAll, cancellationToken);

      domainEventEmitter.EmitEvent(new ProviderAuditLogAddedEvent(log with
      {
        TimeUtc = DateTime.UtcNow,
        CorrelationId = log.Id,
        Output = res
      }));

      return res;
    }
    catch (Exception ex)
    {
      HttpContext.Request.EnableBuffering();
      domainEventEmitter.EmitEvent(new ProviderAuditLogAddedEvent(log with
      {
        TimeUtc = DateTime.UtcNow,
        CorrelationId = log.Id,
        ErrorMessage = ExceptionHelpers.GenerateDetailedExceptionMessage(ex)
      }));
      throw;
    }
  }

  [SubjectPermissionAuthorize(typeof(INoAuthorizationPermission))]
  [HttpGet(ProviderLinkApiRoutes.OldGetAll)]
  [HttpGet(ProviderLinkApiRoutes.GetAll)]
  public async Task<IActionResult> GetAll(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    CancellationToken token,
    [FromQuery] bool includeClients = false,
    [FromQuery] bool includeUnlinkedClients = false,
    [FromQuery] bool throwIfAgentInstallerVersionNotSet = true)
  {
    IQueryable<ProviderLink> q = ctx
      .GetProviderLinks(includeDisabledLinks: true)
      .Include(a => a.UpdatedByUser!)
      .ThenInclude(a => a.Person);

    if (includeClients)
    {
      q = q.Include(a => a.ProviderClients);
    }
    var tasks = q.AsEnumerable().Select(link =>
    {
      return Task.Run(async () =>
      {
        var vm = new GetProviderLinkResponse(link, includeClients: includeClients, includeUnlinkedClients: includeUnlinkedClients);
        return await PopulateProviderLinkResponse(vm, link, providerActions, throwIfAgentInstallerVersionNotSet, token);
      });
    });

    var vms = await Task.WhenAll(tasks);
    return Ok(JsonSerializer.Serialize(vms, _jsonSerializerOptions));
  }


  [SubjectPermissionAuthorize(typeof(IIntegrationsViewPermission))]
  [HttpGet(ProviderLinkApiRoutes.OldGet)]
  [HttpGet(ProviderLinkApiRoutes.Get)]
  public async Task<IActionResult> Get(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    CancellationToken token,
    [FromQuery] bool includeClients = true,
    [FromQuery] bool includeProvidersLinkedFromThisProvider = true,
    [FromQuery] bool throwIfAgentInstallerVersionNotSet = true)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsViewPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    var link = ctx.GetProviderLink(
      id,
      includeClients: includeClients,
      includeLinkedProviders: includeProvidersLinkedFromThisProvider,
      includeLinkedFromProviders: includeProvidersLinkedFromThisProvider);
    if (link is null) return NotFound();

    var response = new GetProviderLinkResponse(
      link,
      includeClients: includeClients,
      includeProvidersLinkedFromThisProvider: includeProvidersLinkedFromThisProvider,
      includeUnlinkedClients: true);
    var vm = await PopulateProviderLinkResponse(response, link, providerActions, throwIfAgentInstallerVersionNotSet, token);
    return Ok(JsonSerializer.Serialize(vm, _jsonSerializerOptions));
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.VerifyCredentialsWithExternalProviderReference)]
  public async Task<IActionResult> VerifyCredentialsWithExternalProviderReference(
    [FromServices] IProviderActions providerActions,
    [FromServices] ImmybotDbContext ctx,
    [FromBody] CreateProviderLinkWithExternalProviderReferenceRequestBody body,
    CancellationToken token)
  {
    if (!ModelState.IsValid) return BadRequest(ModelState);

    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsManagePermission>(
      new DefaultKeyParameters(body.ProviderLink.Id),
      strict: true);

    var newLink = body.ProviderLink;
    if (body.ProviderLinkExternalReferenceData == null)
      return BadRequest("ProviderLinkExternalReferenceData is required");
    if (newLink.Id != default)
      return BadRequest("Verifying credentials for an existing integration while providing external integration reference data is not supported");
    if (newLink.ProviderTypeId == Guid.Empty)
      return BadRequest("ProviderTypeId and ProviderTypeFormData are required on the ProviderLink object");
    var externalRef = body.ProviderLinkExternalReferenceData;
    var existingLinkId = externalRef.ProviderLinkId;
    var existingLink = ctx.GetProviderLink(existingLinkId);
    var metadata = existingLink is not null ? providerActions.GetProviderType(existingLink.ProviderTypeId) : null;
    if (existingLink is null || metadata is null ||
        metadata.SupportedCrossProviderInitializationLinkages.All(t => t.ProviderTypeId != newLink.ProviderTypeId))
      return BadRequest("The specified integration does not support that provider type");
    var provider = await providerActions.GetProvider(existingLink, token);
    newLink.ProviderTypeFormData = externalRef.ProviderTypeFormData;
    if (provider is ISupportsCrossProviderInitialization initProvider)
    {
      // merge password input values from the form data provided by the existing link's external
      // provider initializer with the input values that the user specified for this provider
      newLink.ProviderTypeFormData = CopyStoredPasswordInputValues(
        newLink.ProviderTypeId,
        newLink.ProviderTypeFormData,
        providerActions,
        await initProvider.GetProviderTypeFormData(newLink.ProviderTypeId, token));
    }
    else if (provider is not ISupportsCrossProviderClientLinking)
      return BadRequest("The specified integration does not support that provider type");

    // todo: what do we do now that verify is not a thing?
    return Ok(null);
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.OldCreate)]
  [HttpPost(ProviderLinkApiRoutes.Create)]
  public async Task<IActionResult> Create(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IProviderActions providerActions,
    [FromBody] CreateProviderLinkRequestBody body,
    [FromQuery] bool throwIfAgentInstallerVersionNotSet = true)
  {
    if (!ModelState.IsValid) return BadRequest(ModelState);

    var userTenantId = userService.GetTenantId();
    await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IIntegrationsManagePermission>(User,
      userTenantId,
      strict: true);

    await using var ctx = dbFactory();

    var link = new ProviderLink
    {
      Disabled = body.ProviderTypeFormData is null,
      Name = body.Name,
      ProviderTypeId = body.ProviderTypeId,
      ProviderTypeFormData = body.ProviderTypeFormData ?? JsonSerializer.SerializeToElement(new object()),
      OwnerTenantId = userTenantId,
    };
    var newLink = ctx.CreateProviderLink(link);
    var response = new GetProviderLinkResponse(newLink, includeClients: true);
    var vm = await PopulateProviderLinkResponse(response, newLink, providerActions, throwIfAgentInstallerVersionNotSet, default);
    return Ok(JsonSerializer.Serialize(vm, _jsonSerializerOptions));
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.CreateWithExternalProviderReference)]
  public async Task<IActionResult> CreateWithExternalProviderReference(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IProviderActions providerActions,
    [FromServices] IProviderClientSyncJob syncJob,
    [FromBody] CreateProviderLinkWithExternalProviderReferenceRequestBody body,
    CancellationToken token,
    [FromQuery] bool throwIfAgentInstallerVersionNotSet = true)
  {
    if (!ModelState.IsValid) return BadRequest(ModelState);

    var userTenantId = userService.GetTenantId();
    await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IIntegrationsManagePermission>(User,
      userTenantId,
      strict: true);

    await using var ctx = dbFactory();
    var newLink = body.ProviderLink;

    newLink.OwnerTenantId = userTenantId;
    if (body.ProviderLinkExternalReferenceData == null)
      return BadRequest("ProviderLinkExternalReferenceData is required");
    if (newLink.Id != default)
      return BadRequest("Id cannot be specified on ProviderLink");
    if (newLink.ProviderTypeId == Guid.Empty)
      return BadRequest("ProviderTypeId and ProviderTypeFormData are required on ProviderLink");
    var externalRef = body.ProviderLinkExternalReferenceData;
    var existingLinkId = externalRef.ProviderLinkId;
    var existingLink = ctx.GetProviderLink(existingLinkId);
    if (existingLink is null) return NotFound("The specified integration was not found");
    var metadata = providerActions.GetProviderType(existingLink.ProviderTypeId);
    if (!metadata.SupportedCrossProviderInitializationLinkages.Any(t => t.ProviderTypeId == newLink.ProviderTypeId))
      return BadRequest("The specified integration does not support that provider type");
    var provider = await providerActions.GetProvider(existingLink, token);
    newLink.ProviderTypeFormData = externalRef.ProviderTypeFormData;
    var supportsInit = false;
    if (provider is ISupportsCrossProviderInitialization initProvider)
    {
      // merge password input values from the form data provided by the existing link's external
      // provider initializer with the input values that the user specified for this provider
      newLink.ProviderTypeFormData = CopyStoredPasswordInputValues(
        newLink.ProviderTypeId,
        newLink.ProviderTypeFormData,
        providerActions,
        await initProvider.GetProviderTypeFormData(newLink.ProviderTypeId, token));
      supportsInit = true;
    }
    else
    {
      if (provider is not ISupportsCrossProviderClientLinking)
        return BadRequest("The specified integration does not support that provider type");
    }

    newLink.LinkedFromProviders.Add(new ProviderLinkCrossReference
    {
      IsProviderLink2InitializedFromProviderLink1 = supportsInit,
      IsExternalClientLinkingEnabled = externalRef.EnableClientExternalLinking,
      ProviderLink1Id = existingLinkId,
    });

    newLink = ctx.CreateProviderLink(newLink);

    if (provider is ISupportsListingClients)
    {
      syncJob.Enqueue(newLink.Id);
    }

    newLink.ProviderTypeFormData = await providerActions
      .PreprocessProviderTypeFormData(newLink, newLink.ProviderTypeFormData, default);
    var linkUpdate = new UpdateProviderLinkRequestBody()
    {
      Id = newLink.Id,
      Name = newLink.Name,
      ProviderTypeFormData = newLink.ProviderTypeFormData,
    };
    var updatedLink = ctx.UpdateProviderLink(linkUpdate);
    if (updatedLink is null) return BadRequest("Failed to update provider link");
    await providerActions.RecreateProvider(updatedLink, default);

    var response = new GetProviderLinkResponse(newLink, includeClients: true, includeUnlinkedClients: true);

    var vm = await PopulateProviderLinkResponse(response, newLink, providerActions, throwIfAgentInstallerVersionNotSet, default);
    return Ok(JsonSerializer.Serialize(vm, _jsonSerializerOptions));
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.Reload)]
  public async Task<ActionResult> Reload(
    [FromServices] IProviderActions providerActions,
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromRoute] int id)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsManagePermission>(
      new DefaultKeyParameters(id),
      strict: true);

    await using var ctx = dbFactory();
    var existingLink = ctx.GetProviderLink(id);
    if (existingLink is null) return NotFound();
    await providerActions.ReloadProvider(existingLink, default);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsManagePermission))]
  [HttpPut(ProviderLinkApiRoutes.OldUpdate)]
  [HttpPut(ProviderLinkApiRoutes.Update)]
  public async Task<IActionResult> Update(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IProviderActions providerActions,
    [FromServices] IProviderClientSyncJob syncJob,
    [FromRoute] int id,
    [FromBody] UpdateProviderLinkRequestBody body,
    [FromQuery] bool throwIfAgentInstallerVersionNotSet = true)
  {
    if (!ModelState.IsValid) return BadRequest(ModelState);

    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsManagePermission>(
      new DefaultKeyParameters(id),
      strict: true);

    await using var ctx = dbFactory();
    body.Id = id;
    var existingLink = ctx.GetProviderLink(body.Id);
    if (existingLink is null) return NotFound();
    body.ProviderTypeFormData = CopyStoredPasswordInputValues(existingLink.ProviderTypeId, body.ProviderTypeFormData, providerActions, existingLink.ProviderTypeFormData);
    body.ProviderTypeFormData = await providerActions.PreprocessProviderTypeFormData(existingLink, body.ProviderTypeFormData, default);
    var updatedLink = ctx.UpdateProviderLink(body);
    if (updatedLink is null) return BadRequest("Failed to update provider link");

    string? errorMessage = null;
    try
    {
      await providerActions.RecreateProvider(updatedLink, default);

      var provider = await providerActions.GetProvider(updatedLink, default);
      if (provider is ISupportsListingClients)
      {
        syncJob.Enqueue(updatedLink.Id);
      }
    }
    catch (Exception ex)
    {
      errorMessage = ex.Message;
    }

    var response = new GetProviderLinkResponse(updatedLink, includeClients: true, includeUnlinkedClients: true, errorMessage: errorMessage);
    var vm = await PopulateProviderLinkResponse(response, updatedLink, providerActions, throwIfAgentInstallerVersionNotSet, default);
    return Ok(JsonSerializer.Serialize(vm, _jsonSerializerOptions));
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsManagePermission))]
  [HttpDelete(ProviderLinkApiRoutes.Delete)]
  public async Task<IActionResult> Delete(
    [FromServices] UserBearingDbFactory<ImmybotDbContext> dbFactory,
    [FromServices] IDeleteProviderLinkCmd cmd,
    [FromRoute] int id)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsManagePermission>(
      new DefaultKeyParameters(id),
      strict: true);

    await using var ctx = dbFactory();
    var link = ctx.GetProviderLink(id, includeLinkedProviders: true);
    if (link is null) return NotFound();
    await cmd.Run(link);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsViewPsaTicketsPermission))]
  [HttpGet(ProviderLinkApiRoutes.GetTechnicianPageInfoFromPsaTicket)]
  public async Task<ActionResult<TechnicianPageInfoFromPsaTicket>> GetTechnicianPageInfoFromPsaTicket(
    [FromRoute] int id,
    [FromRoute] string ticketId,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    CancellationToken token)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsViewPsaTicketsPermission>(
      new DefaultKeyParameters(id),
      strict: true);
    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();

    var details = await providerActions.GetTechnicianPageInfoFromPsaTicket(link, ticketId, token);
    return Ok(details);
  }

  #endregion Provider Links

  #region Cross-Provider Link References

  [SubjectPermissionAuthorize(typeof(IIntegrationsManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.DisableLinkedProviderClientLinking)]
  public async Task<IActionResult> DisableLinkedProviderClientLinking(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int id,
    [FromRoute] int externalLinkId)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsManagePermission>(
      new DefaultKeyParameters(id),
      strict: true);

    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsManagePermission>(
      new DefaultKeyParameters(externalLinkId),
      strict: true);

    var linkedFrom = ctx.GetProviderLink(id, includeLinkedProviders: true);
    if (linkedFrom == null) return NotFound();
    var linkedTo = ctx.GetProviderLink(externalLinkId);
    if (linkedTo == null) return NotFound();

    var existingRef = linkedFrom.ProvidersLinkedFromThisProvider
      .FirstOrDefault(r => r.ProviderLink2Id == externalLinkId);
    if (existingRef == null) return NotFound();
    var updatedRef = ctx
      .DisableProviderLinkExternalReferenceClientLinking(existingRef);
    if (updatedRef is null) return NotFound();
    updatedRef.ProviderLink2 = existingRef.ProviderLink2;
    if (updatedRef.ProviderLink2 is null) return NotFound();
    return Ok(new GetProviderLinkResponse.LinkedExternalLink(updatedRef.ProviderLink2Id, updatedRef.ProviderLink2.Name, updatedRef.ProviderLink2.ProviderTypeId, updatedRef.IsExternalClientLinkingEnabled, updatedRef.IsProviderLink2InitializedFromProviderLink1));
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.EnableLinkedProviderClientLinking)]
  public async Task<IActionResult> EnableLinkedProviderClientLinking(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int id,
    [FromRoute] int externalLinkId)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsManagePermission>(
      new DefaultKeyParameters(id),
      strict: true);

    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsManagePermission>(
      new DefaultKeyParameters(externalLinkId),
      strict: true);

    var linkedFrom = ctx.GetProviderLink(id, includeLinkedProviders: true);
    if (linkedFrom == null) return NotFound();
    var linkedTo = ctx.GetProviderLink(externalLinkId);
    if (linkedTo == null) return NotFound();

    var existingRef = linkedFrom.ProvidersLinkedFromThisProvider
      .FirstOrDefault(r => r.ProviderLink2Id == externalLinkId);
    if (existingRef == null) return NotFound();
    var updatedRef = ctx
      .EnableProviderLinkExternalReferenceClientLinking(existingRef);
    if (updatedRef is null) return NotFound();
    updatedRef.ProviderLink2 = existingRef.ProviderLink2;
    if(updatedRef.ProviderLink2 is null) return NotFound();
    return Ok(new GetProviderLinkResponse.LinkedExternalLink(updatedRef.ProviderLink2Id, updatedRef.ProviderLink2.Name, updatedRef.ProviderLink2.ProviderTypeId, updatedRef.IsExternalClientLinkingEnabled, updatedRef.IsProviderLink2InitializedFromProviderLink1));
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsManagePermission))]
  [HttpDelete(ProviderLinkApiRoutes.DeleteLinkedProviderReference)]
  public async Task<IActionResult> DeleteLinkedProviderReference(
    [FromServices] ImmybotDbContext ctx,
    [FromRoute] int id,
    [FromRoute] int externalLinkId)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsManagePermission>(
      new DefaultKeyParameters(id),
      strict: true);

    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsManagePermission>(
      new DefaultKeyParameters(externalLinkId),
      strict: true);

    var linkedFrom = ctx.GetProviderLink(id, includeLinkedProviders: true);
    if (linkedFrom == null) return NotFound();
    var linkedTo = ctx.GetProviderLink(externalLinkId);
    if (linkedTo == null) return NotFound();

    var existingRef = linkedFrom.ProvidersLinkedFromThisProvider
      .FirstOrDefault(r => r.ProviderLink2Id == externalLinkId);
    if (existingRef == null) return NotFound();
    ctx.DeleteProviderLinkExternalReference(existingRef);
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.CreateLinkedProviderReference)]
  public async Task<IActionResult> CreateLinkedProviderReference(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    [FromBody] CreateLinkedProviderReferenceRequestBody body)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsManagePermission>(
      new DefaultKeyParameters(id),
      strict: true);

    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsManagePermission>(
      new DefaultKeyParameters(body.ProviderLinkId),
      strict: true);

    var linkedFrom = ctx.GetProviderLink(id, includeLinkedProviders: true);
    if (linkedFrom == null) return NotFound();
    var linkedTo = ctx.GetProviderLink(body.ProviderLinkId);
    if (linkedTo == null) return NotFound();
    if (linkedFrom.ProvidersLinkedFromThisProvider.Any(r => r.ProviderLink2Id == body.ProviderLinkId))
      return BadRequest("An external reference already exists between these two links");

    var provider = await providerActions.GetProvider(linkedFrom, CancellationToken.None);
    var metadata = providerActions.GetProviderType(linkedFrom.ProviderTypeId);
    if (
      (
        provider is not ISupportsCrossProviderInitialization
        || !metadata.SupportedCrossProviderInitializationLinkages
          .Any(t => t.ProviderTypeId == linkedTo.ProviderTypeId)
      ) && (
        provider is not ISupportsCrossProviderClientLinking
        || !metadata.SupportedCrossProviderClientLinkages
          .Any(t => t.ProviderTypeId == linkedTo.ProviderTypeId)
      )
    )
    {
      return BadRequest("The provided link does not support that provider type");
    }

    if (body.IsCrossProviderClientExternalLinkingEnabled && (provider is not ISupportsCrossProviderClientLinking
                                                             || !metadata.SupportedCrossProviderClientLinkages.Any(t =>
                                                               t.ProviderTypeId == linkedTo.ProviderTypeId)))
      return BadRequest(
        "The provided link does not support cross-provider client external linking with the specified provider");

    var externalRef = ctx.CreateProviderLinkExternalReference(
      linkedFrom.Id,
      linkedTo.Id,
      isExternalClientLinkingEnabled: body.IsCrossProviderClientExternalLinkingEnabled);

    externalRef.ProviderLink1 = linkedFrom;
    externalRef.ProviderLink2 = linkedTo;

    return Ok(new GetProviderLinkResponse.LinkedExternalLink(externalRef.ProviderLink2Id, externalRef.ProviderLink2.Name, externalRef.ProviderLink2.ProviderTypeId, externalRef.IsExternalClientLinkingEnabled, externalRef.IsProviderLink2InitializedFromProviderLink1));
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsSyncAgentsPermission))]
  [HttpPost(ProviderLinkApiRoutes.SyncClientsFromLinkedProvider)]
  public async Task<IActionResult> SyncClientsFromLinkedProvider(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    [FromRoute] int externalLinkId)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsSyncAgentsPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsSyncAgentsPermission>(
      new DefaultKeyParameters(externalLinkId),
      strict: true);

    var linkFrom = ctx.GetProviderLink(id, includeLinkedProviders: true, includeClients: true);
    if (linkFrom == null) return NotFound();
    var linkTo = ctx.GetProviderLink(externalLinkId, includeClients: true);
    if (linkTo == null) return NotFound();
    if (!linkFrom.ProvidersLinkedFromThisProvider.Any(r => r.ProviderLink2Id == externalLinkId))
      return BadRequest("No external reference exists between these two links");

    // get cross-provider client id matches
    var clientMatches = await providerActions.GetClientsLinkedToClients(
      linkFrom, linkTo.ProviderTypeId,
      linkTo.ProviderClients.Select(a => a.ExternalClientId).ToList(),
      CancellationToken.None);

    var shouldSave = false;
    foreach (var client in clientMatches)
    {
      var linkToClientId = client.Value;
      var linkFromClientId = client.Key;
      var linkFromClient = linkFrom.ProviderClients.FirstOrDefault(a => a.ExternalClientId == linkFromClientId && a.LinkedToTenantId != null);
      if (linkFromClient != null)
      {
        var linkToClient = linkTo.ProviderClients.FirstOrDefault(a => a.ExternalClientId == linkToClientId);
        if (linkToClient != null)
        {
          linkToClient.LinkedToTenantId = linkFromClient.LinkedToTenantId;
          ctx.UpdateProviderClient(linkToClient, doSave: false);
          shouldSave = true;
        }
      }
    }

    if (shouldSave) await ctx.SaveChangesAsync();

    return NoContent();
  }
  #endregion Cross-Provider Link References

  #region Provider Clients

  [SubjectPermissionAuthorize(typeof(IIntegrationsLinkClientsPermission))]
  [HttpPost(ProviderLinkApiRoutes.FetchClientsFromProvider)]
  public async Task<IActionResult> FetchClientsFromProvider(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderClientSyncJob job,
    [FromRoute] int id)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsLinkClientsPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();
    job.Enqueue(link.Id);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsViewPermission))]
  [HttpGet(ProviderLinkApiRoutes.GetClientStatuses)]
  public async Task<IActionResult> GetClientStatuses(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    CancellationToken token)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsViewPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();

    var statuses = await providerActions.GetProviderClientStatuses(link, token);
    return Ok(statuses);
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsViewPermission))]
  [HttpGet(ProviderLinkApiRoutes.GetClientTypes)]
  public async Task<IActionResult> GetClientTypes(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    CancellationToken token)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsViewPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();

    var types = await providerActions.GetProviderClientTypes(link, token);
    return Ok(types);
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsViewPermission))]
  [HttpGet(ProviderLinkApiRoutes.GetClients)]
  public async Task<IActionResult> GetClients(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    CancellationToken token)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsViewPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    var link = ctx.GetProviderLink(id, includeClients: true);
    if (link is null) return NotFound();
    var clients = link.ProviderClients.Select(a => new GetProviderClientResponse(a));
    return Ok(clients);
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsLinkClientsPermission))]
  [HttpPost(ProviderLinkApiRoutes.LinkClientsToTenant)]
  public async Task<IActionResult> LinkClientsToTenant(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    [FromBody] LinkClientsToTenantRequestBody body)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsLinkClientsPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();
    await ctx.LinkProviderClientsToTenant(id, body.ClientIds, body.TenantId);
    if (await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsSyncAgentsPermission>(
          new DefaultKeyParameters(link.Id),
          strict: false))
    {
      // Sync provider agents if the user has permission to do so
      await providerActions.SyncProviderAgents(link, clientIds: body.ClientIds);
    }

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsLinkClientsPermission))]
  [HttpPost(ProviderLinkApiRoutes.AutoLinkClientsToTenants)]
  public async Task<IActionResult> AutoLinkClientsToTenants(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromServices] ICreateTenantCmd cmd,
    CancellationToken cancellationToken,
    [FromRoute] int id,
    [FromBody] BulkLinkClientsToTenantsRequestBody body)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsLinkClientsPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    var link = ctx.GetProviderLink(id, includeClients: true);
    if (link is null) return NotFound();

    // For each client that does not have a tenant Id, create a tenant, and update that provider
    // client. If a tenant already exists with a clients name, then use the existing tenant.
    // Useful when automate clients and control clients match so we don't end up with a bunch
    // of duplicate tenants.
    var unlinkedClients = link.ProviderClients.Where(a => a.LinkedToTenantId == null);

    // filter to selected client ids if any are present
    if (body?.ClientIds?.Any() == true)
      unlinkedClients = unlinkedClients.Where(a => body.ClientIds.Contains(a.ExternalClientId));
    var tenants = await ctx.GetAllTenants().ToListAsync(cancellationToken);
    foreach (var c in unlinkedClients)
    {
      var existingTenantByName = tenants.Find(a =>
        string.Equals(a.Name, c.ExternalClientName, StringComparison.OrdinalIgnoreCase));
      if (existingTenantByName != null)
      {
        c.LinkedToTenantId = existingTenantByName.Id;
        c.HasCompletedInitialAgentSync = false;
      }
      else if (c.ExternalClientName != null)
      {
        var tenant = await cmd.CreateTenant(
          new CreateTenantPayload(c.ExternalClientName),
          link.OwnerTenantId,
          cancellationToken);
        c.LinkedToTenantId = tenant.Id;
      }
      ctx.Attach(c).State = EntityState.Modified;
    }
    await ctx.SaveChangesAsync(cancellationToken);
    if (await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsSyncAgentsPermission>(
          new DefaultKeyParameters(link.Id),
          strict: false))
    {
      // Sync provider agents if the user has permission to do so
      await providerActions.SyncProviderAgents(link, token: cancellationToken);
    }

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsLinkClientsPermission))]
  [HttpPost(ProviderLinkApiRoutes.LinkClientToNewTenant)]
  public async Task<IActionResult> LinkClientToNewTenant(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromServices] ICreateTenantCmd cmd,
    [FromRoute] int id,
    [FromBody] LinkClientToNewTenantRequestBody body,
    CancellationToken cancellationToken)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsLinkClientsPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    var link = ctx.GetProviderLink(id, includeClients: true);
    if (link is null) return NotFound();

    var client = link.ProviderClients.FirstOrDefault(a => a.ExternalClientId == body.ExternalClientId);
    if (client == null) return NotFound("Client not found");
    var tenantName = body.TenantName ?? client.ExternalClientName;
    if (tenantName == null)
      return BadRequest("Tenant name is required if client name is not available");
    if (await ctx.Tenants.AnyAsync(t => t.Name == tenantName, cancellationToken))
      return BadRequest($"A Tenant with the name {tenantName} already exists");
    var tenant = await cmd.CreateTenant(
      new CreateTenantPayload(tenantName),
      link.OwnerTenantId,
      cancellationToken);
    client.LinkedToTenantId = tenant.Id;
    client.HasCompletedInitialAgentSync = false;
    ctx.Attach(client).State = EntityState.Modified;
    await ctx.SaveChangesAsync(cancellationToken);

    if (await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsSyncAgentsPermission>(
          new DefaultKeyParameters(link.Id),
          strict: false))
    {
      // Trigger a sync after linking if the user has permission to do so
      await providerActions.SyncProviderAgents(link, clientIds: new List<string> { client.ExternalClientId },
        cancellationToken);
    }

    return Ok(new LinkClientToNewTenantResponseBody { TenantId = client.LinkedToTenantId, TenantName = tenant.Name });
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsLinkClientsPermission))]
  [HttpPost(ProviderLinkApiRoutes.LinkExactMatchClients)]
  public async Task<ActionResult<List<ProviderClientLinkToTenantByExactNameResponse>>> LinkExactMatchClients(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ILinkExactMatchClientsCmd cmd,
    [FromRoute] int id,
    CancellationToken cancellationToken)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsLinkClientsPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    var link = ctx.GetProviderLink(id, includeClients: true);
    if (link is null) return NotFound();

    var linkedClients = await cmd.Run(
      new LinkExactMatchClientsPayload(id,
        await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsSyncAgentsPermission>(
          new DefaultKeyParameters(id),
          strict: false)
      ),
      cancellationToken);
    var response = linkedClients.Select(
      a => new ProviderClientLinkToTenantByExactNameResponse { ExternalClientId = a.ExternalClientId, LinkedToTenantId = a.LinkedToTenantId });
    return Ok(response);
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsLinkClientsPermission))]
  [HttpPost(ProviderLinkApiRoutes.UnlinkClients)]
  public async Task<IActionResult> UnlinkClients(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    [FromBody] UnlinkClientsRequestBody body)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsLinkClientsPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();
    await ctx.UnlinkProviderClients(id, body.ClientIds);
    if (await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsSyncAgentsPermission>(
          new DefaultKeyParameters(id),
          strict: false)
       )
    {
      // Sync agents afterward if the user has permission to do so
      await providerActions.SyncProviderAgents(link);
    }
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsSyncAgentsPermission))]
  [HttpPost(ProviderLinkApiRoutes.SyncAgentsForClients)]
  public async Task<IActionResult> SyncAgentsForClients(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    [FromBody] SyncAgentsForClientsRequest request)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<ProviderLink, IIntegrationsSyncAgentsPermission>(
      new DefaultKeyParameters(id),
      strict: true);

    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();
    await providerActions.SyncProviderAgents(link, clientIds: request.ClientIds);
    return NoContent();
  }

  #endregion Provider Clients

  #region Provider Agents

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.InstallAgentOnComputer)]
  public async Task<IActionResult> InstallAgentOnComputer(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromServices] IRunContextFactory runContextFactory,
    [FromRoute] int id,
    [FromRoute] int computerId)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePermission>(
      new DefaultKeyParameters(computerId),
      strict: true);

    var token = CancellationToken.None;
    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();
    var computer = ctx.GetComputerById(computerId, includeAgents: true);
    if (computer is null) return NotFound();
    if (computer.GetRunScriptAgents().Count == 0)
      return BadRequest("Computer does not have any online run-script agents to install the agent with");

    var installScript = await providerActions
      .GetAgentInstallScript(link, computer, token);
    if (installScript is null) return NotFound("Failed to get install script");

    var cacheId = Guid.NewGuid();
    using var runContext = await runContextFactory.GenerateComputerOneOffRunContext(computer.Id,
      token,
      cacheId,
      manuallyTriggeredBy: userService.GetCurrentUser());
    var res = await runContext.RunScript(installScript, 300, token);
    return Ok(res?.HadTerminatingException is true ? res.GetErrorString() : res?.ConsoleText);
  }

  [SubjectPermissionAuthorize(typeof(IComputersViewPermission))]
  [HttpPost(ProviderLinkApiRoutes.RefreshAgentOnlineStatus)]
  public async Task<IActionResult> RefreshAgentOnlineStatus(
    [FromServices] IProviderActions providerActions,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IHostApplicationLifetime lifetime,
    [FromRoute] int id,
    [FromRoute] int agentId)
  {
    var link = ctx.GetProviderLink(id);
    if (link == null) return NotFound();

    var agent = ctx.GetProviderAgent(agentId);
    if (agent == null) return NotFound("Agent not found");

    if (agent.ComputerId is not { } computerId)
      return BadRequest("Agent is not linked to a computer");

    await authorizer.PerformAuthorizationWorkflowAsync<Computer, IComputersViewPermission>(
      new DefaultKeyParameters(computerId),
      strict: true);

    if (ctx.GetComputerTenantId(computerId) is null) return NotFound();

    var isOnline = await providerActions.RefreshAgentOnlineStatus(link, agent, lifetime.ApplicationStopping);

    return Ok(isOnline);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.DeleteOfflineAgentFromComputer)]
  public async Task<IActionResult> DeleteOfflineAgentFromComputer(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromServices] ILogger<ProviderLinksController> logger,
    [FromRoute] int id,
    [FromRoute] int agentId)
  {
    var link = ctx.GetProviderLink(id);
    if (link == null) return NotFound("Provider link not found");

    var agent = ctx.GetProviderAgent(agentId);
    if (agent == null) return NotFound("Agent not found");
    if (agent.ComputerId is not { } computerId)
      return BadRequest("Agent is not linked to a computer");

    await authorizer.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePermission>(
      new DefaultKeyParameters(computerId),
      strict: true);

    var computer = ctx.GetComputerById(computerId);
    if (computer == null) return NotFound();

    if (agent.IsOnline)
      return BadRequest("Cannot delete an online agent");
    try
    {
      await providerActions.DeleteOfflineAgentFromComputer(link, agent, CancellationToken.None);
      return NoContent();
    }
    catch (NotSupportedException ex)
    {
      return BadRequest($"Couldn't delete offline agent from the computer - {ex.Message}");
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "Exception occurred while trying to delete offline agent from computer");
      return BadRequest("Couldn't delete offline agent from the computer - an exception occurred");
    }
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.GetAgentProvisioningPackageUri)]
  public async Task<IActionResult> GetAgentProvisioningPackageUri(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    [FromBody] GetProvisioningPackageUriParameters requestOptions,
    CancellationToken token)
  {
    var link = ctx.GetProviderLink(id);
    if (link == null) return NotFound();

    await ThrowIfCannotManageComputersAtTenant(providerActions, requestOptions.TargetExternalClientId, link, token);

    var uri = await providerActions.GetAgentProvisioningPackageUri(link, requestOptions, token);
    return Ok(uri);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.GetAgentExecutableUri)]
  public async Task<IActionResult> GetAgentExecutableUri(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    [FromBody] GetExecutableUriParameters requestOptions,
    CancellationToken token)
  {
    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();

    await ThrowIfCannotManageComputersAtTenant(providerActions, requestOptions.TargetExternalClientId, link, token);

    var uri = await providerActions.GetAgentExecutableUri(link, requestOptions, token);
    return Ok(uri);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.GetAgentExecutableUriWithOnboardingOptions)]
  public async Task<IActionResult> GetAgentExecutableUriWithOnboardingOptions(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    [FromBody] GetExecutableUriParametersWithOnboardingOptions requestOptions,
    CancellationToken token)
  {
    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();

    await ThrowIfCannotManageComputersAtTenant(providerActions, requestOptions.TargetExternalClientId, link, token);

    var uri = await providerActions.GetAgentExecutableUri(link, requestOptions, token);
    return Ok(uri);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.GetAgentProvisioningPackageUriWithOnboardingOptions)]
  public async Task<IActionResult> GetAgentProvisioningPackageUriWithOnboardingOptions(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id,
    [FromBody] GetProvisioningPackageUriParametersWithOnboardingOptions requestOptions,
    CancellationToken token)
  {
    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();

    await ThrowIfCannotManageComputersAtTenant(providerActions, requestOptions.TargetExternalClientId, link, token);

    var uri = await providerActions.GetAgentProvisioningPackageUri(link, requestOptions, token);
    return Ok(uri);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.GetAgentPowerShellInstallScript)]
  public async Task<IActionResult> GetAgentPowerShellInstallScript(
   [FromRoute] int id,
   [FromBody] GetPowerShellInstallScriptParameters requestOptions,
   [FromServices] ImmybotDbContext ctx,
   [FromServices] IProviderActions providerActions,
   CancellationToken token)
  {
    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();

    await ThrowIfCannotManageComputersAtTenant(providerActions, requestOptions.TargetExternalClientId, link, token);

    var script = await providerActions.GetAgentPowerShellInstallScript(link, requestOptions, token);
    return Ok(script);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.GetAgentPowerShellInstallScriptWithOnboardingOptions)]
  public async Task<IActionResult> GetAgentPowerShellInstallScriptWithOnboardingOptions(
   [FromRoute] int id,
   [FromBody] GetPowerShellInstallScriptParametersWithOnboardingOptions requestOptions,
   [FromServices] ImmybotDbContext ctx,
   [FromServices] IProviderActions providerActions,
   CancellationToken token)
  {
    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();

    await ThrowIfCannotManageComputersAtTenant(providerActions, requestOptions.TargetExternalClientId, link, token);

    var script = await providerActions.GetAgentPowerShellInstallScript(link, requestOptions, token);
    return Ok(script);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.GetAgentBashInstallScript)]
  public async Task<IActionResult> GetAgentBashInstallScript(
   [FromRoute] int id,
   [FromBody] GetBashInstallScriptParameters requestOptions,
   [FromServices] ImmybotDbContext ctx,
   [FromServices] IProviderActions providerActions,
   CancellationToken token)
  {
    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();

    await ThrowIfCannotManageComputersAtTenant(providerActions, requestOptions.TargetExternalClientId, link, token);

    var script = await providerActions.GetAgentBashInstallScript(link, requestOptions, token);
    return Ok(script);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.GetAgentBashInstallScriptWithOnboardingOptions)]
  public async Task<IActionResult> GetAgentBashInstallScriptWithOnboardingOptions(
   [FromRoute] int id,
   [FromBody] GetBashInstallScriptParametersWithOnboardingOptions requestOptions,
   [FromServices] ImmybotDbContext ctx,
   [FromServices] IProviderActions providerActions,
   CancellationToken token)
  {
    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();

    await ThrowIfCannotManageComputersAtTenant(providerActions, requestOptions.TargetExternalClientId, link, token);
    var script = await providerActions.GetAgentBashInstallScript(link, requestOptions, token);
    return Ok(script);
  }

  [SubjectPermissionAuthorize(typeof(IComputersManagePermission))]
  [HttpPost(ProviderLinkApiRoutes.GetExternalProviderAgentUrl)]
  public async Task<IActionResult> GetExternalProviderAgentUrl(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromServices] IHostApplicationLifetime lifetime,
    [FromRoute] int id,
    [FromRoute] int computerId)
  {
    await authorizer.PerformAuthorizationWorkflowAsync<Computer, IComputersManagePermission>(
      new DefaultKeyParameters(computerId),
      strict: true);

    var link = ctx.GetProviderLink(id);
    if (link == null) return NotFound();

    var computer = ctx.GetComputerById(computerId, includeAgents: true, asNoTracking: true, includeComputersForDisabledProviderLinks: true);
    if (computer is null) return NotFound();

    // for now only allow this if there is a single agent for this provider type
    // in the future, we'll update this route to accept an agent id instead

    var agents = computer.Agents.Where(a => a.ProviderLinkId == id).ToList();
    if (agents.Count is 0) return BadRequest("No agents were found on the provided computer.");
    if (agents.Count > 1) return BadRequest("Multiple agents were found on the same integraton.");

    var url = await providerActions
      .GetExternalProviderAgentUrl(link, agents[0], lifetime.ApplicationStopping);

    return Ok(url);
  }

  [SubjectPermissionAuthorize(typeof(IIntegrationsSyncAgentsPermission))]
  [HttpPost(ProviderLinkApiRoutes.SyncAgents)]
  public async Task<IActionResult> SyncAgents(
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IProviderActions providerActions,
    [FromRoute] int id)
  {
    var link = ctx.GetProviderLink(id);
    if (link is null) return NotFound();
    await providerActions.SyncProviderAgents(link);
    return NoContent();
  }

  #endregion Provider Agents

  private async Task ThrowIfCannotManageComputersAtTenant(IProviderActions providerActions,
    string targetExternalClientId,
    ProviderLink link,
    CancellationToken token)
  {
    var provider = await providerActions.GetProvider(link, token);
    if (provider is ISupportsListingClients)
    {
      throw new NotSupportedException("Not currently supported for providers that list clients");
    }

    if (!int.TryParse(targetExternalClientId, out var tenantId))
      throw new NotSupportedException("Failed to parse the target external client id into a tenant id");

    await subjectPermissionAuthorizationService.AuthorizeTenantAsync<IComputersManagePermission>(
      User,
      tenantId,
      strict: true);
  }
}
