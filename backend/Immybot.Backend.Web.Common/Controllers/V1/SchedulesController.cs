using Cronos;
using Immybot.Backend.Application.DbContextExtensions.ScheduleExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Helpers;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.Authorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationAttributes;
using Immybot.Backend.Web.Common.Contracts.V1;
using Immybot.Backend.Web.Common.Contracts.V1.Requests;
using Immybot.Backend.Web.Common.Contracts.V1.Responses;
using Immybot.Backend.Web.Common.Lib;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Web.Common.Controllers.V1;

public class SchedulesController : Controller
{
  private readonly IUserService _userService;
  private readonly ILogger<SchedulesController> _logger;

  public SchedulesController(
    IUserService userService,
    IFeatureManager featureManager,
    ILogger<SchedulesController> logger)
  {
    _userService = userService;
    _logger = logger;
    featureManager.IsEnabled(FeatureEnum.SchedulesFeature, true);
  }

  private void PopulateNextOccurenceDate(GetScheduleResponse res)
  {
    try
    {
      var cronExpressionString = res.CustomCronExpression ?? ScheduleHelpers.BuildCronExpression(res.Time ?? "", res.Day ?? 0);
      var expression = CronExpression.Parse(cronExpressionString);
      if (res.TimeZoneInfoId != null)
      {
        var timezone = TimeZoneInfo.FindSystemTimeZoneById(res.TimeZoneInfoId);
        res.NextOccurenceDate = expression.GetNextOccurrence(DateTime.UtcNow, timezone);
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Failed to get next occurence for schedule {ScheduleId}", res.Id);
    }
  }

  // GET: api/Schedule
  [SubjectPermissionAuthorize(typeof(ISchedulesViewPermission))]
  [HttpGet(ScheduleApiRoutes.GetAll)]
  public async Task<ActionResult<ICollection<GetScheduleResponse>>> Get(
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ITargetPopulator targetPopulator,
    [FromQuery] int? tenantId = null)
  {
    var filter = permissionFilterBuilder.BuildFilterExpression<Schedule, ISchedulesViewPermission>();
    var schedules = ctx.GetAllSchedules()
      .Where(filter);

    if (tenantId != null)
    {
      schedules = schedules.Where(s => s.TenantId == tenantId.Value);
    }

    var projection = schedules.Select(GetScheduleResponse.Projection);
    var populatedSchedules = await targetPopulator.Populate(projection, CancellationToken.None);

    // populate next occurence
    foreach (var schedule in populatedSchedules)
    {
      PopulateNextOccurenceDate(schedule);
    }

    return Ok(populatedSchedules);
  }

  [SubjectPermissionAuthorize(typeof(ISchedulesViewPermission))]
  [HttpGet(ScheduleApiRoutes.Get)]
  public async Task<ActionResult<GetScheduleResponse>> GetById(
    [FromRoute] int scheduleId,
    [FromServices] IResourceAuthorizerFlow resourceAuthorizerFlow,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] ImmybotDbContext ctx)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Schedule, ISchedulesViewPermission>(
      new DefaultKeyParameters(scheduleId),
      strict: true);

    var schedule = ctx.GetScheduleById(scheduleId, includeUpdatedBy: true);
    if (schedule == null) return NotFound();

    var res = new GetScheduleResponse(schedule);
    PopulateNextOccurenceDate(res);
    var populated =
      (await targetPopulator.Populate(new List<GetScheduleResponse> { res }.AsQueryable(), CancellationToken.None))
      .First();

    return Ok(populated);
  }

  [SubjectPermissionAuthorize(typeof(ISchedulesManagePermission))]
  [HttpPost(ScheduleApiRoutes.Create)]
  public async Task<ActionResult<GetScheduleResponse>> Create(
    [FromBody] CreateScheduleRequest body,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] IScheduleJobActions scheduleJobActions)
  {
    if (body.TenantId.HasValue)
      await subjectPermissionAuthorizationService.AuthorizeTenantAsync<ISchedulesManagePermission>(User,
        body.TenantId.Value,
        strict: true);
    else
      await subjectPermissionAuthorizationService.AuthorizeGlobalAsync<ISchedulesManagePermission>(User, strict: true);

    var created = scheduleJobActions.Create(body, _userService.GetCurrentUser());
    var createdWithUpdatedBy = ctx.GetScheduleById(created.Id, includeUpdatedBy: true);
    if (createdWithUpdatedBy == null) return NotFound();
    var res = new GetScheduleResponse(createdWithUpdatedBy);
    PopulateNextOccurenceDate(res);
    var populated = (await targetPopulator.Populate(new List<GetScheduleResponse> { res }.AsQueryable(), default)).First();
    return Ok(populated);
  }

  [SubjectPermissionAuthorize(typeof(ISchedulesManagePermission))]
  [HttpPut(ScheduleApiRoutes.Update)]
  public async Task<ActionResult<GetScheduleResponse>> Update(
    [FromRoute] int scheduleId,
    [FromBody] UpdateScheduleRequest body,
    [FromServices] IResourceAuthorizerFlow resourceAuthorizerFlow,
    [FromServices] ISubjectPermissionAuthorizationService subjectPermissionAuthorizationService,
    [FromServices] ITargetPopulator targetPopulator,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IScheduleJobActions scheduleJobActions)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Schedule, ISchedulesManagePermission>(
      new DefaultKeyParameters(scheduleId),
      strict: true);

    var schedule = ctx.GetScheduleById(scheduleId);
    if (schedule == null) return NotFound();
    body.Id = scheduleId;

    if (body.TenantId != schedule.TenantId)
    {
      if (body.TenantId.HasValue)
        await subjectPermissionAuthorizationService.AuthorizeTenantAsync<ISchedulesManagePermission>(User,
          body.TenantId.Value,
          strict: true);
      else
        await subjectPermissionAuthorizationService
          .AuthorizeGlobalAsync<ISchedulesManagePermission>(User, strict: true);
    }

    scheduleJobActions.Update(body, _userService.GetCurrentUser());
    var updated = ctx.GetScheduleById(scheduleId, includeUpdatedBy: true);
    if (updated == null) return NotFound();
    var res = new GetScheduleResponse(updated);
    PopulateNextOccurenceDate(res);
    var populated = (await targetPopulator.Populate(new List<GetScheduleResponse> { res }.AsQueryable(), default)).First();
    return Ok(populated);
  }

  [SubjectPermissionAuthorize(typeof(ISchedulesManagePermission))]
  [HttpDelete(ScheduleApiRoutes.Delete)]
  public async Task<IActionResult> Delete(
    [FromRoute] int scheduleId,
    [FromServices] IResourceAuthorizerFlow resourceAuthorizerFlow,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IScheduleJobActions scheduleJobActions)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Schedule, ISchedulesManagePermission>(
      new DefaultKeyParameters(scheduleId),
      strict: true);

    var schedule = ctx.GetScheduleById(scheduleId);
    if (schedule == null) return NotFound();
    scheduleJobActions.Delete(schedule, _userService.GetCurrentUser());
    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ISchedulesManagePermission))]
  [HttpPost(ScheduleApiRoutes.RunScheduleNow)]
  public async Task<IActionResult> RunScheduleNow(
    [FromRoute] int scheduleId,
    [FromServices] IResourceAuthorizerFlow resourceAuthorizerFlow,
    [FromServices] ImmybotDbContext ctx,
    [FromServices] IRunScheduleCmd runScheduleCmd)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Schedule, ISchedulesManagePermission>(
      new DefaultKeyParameters(scheduleId),
      strict: true);

    var schedule = ctx.GetScheduleById(scheduleId);
    if (schedule == null) return NotFound();
    var runningScheduleIds = ctx.GetRunningScheduleIds([scheduleId]);
    if (runningScheduleIds.Contains(scheduleId))
    {
      throw new ImmyWebException(
        new HttpProblem()
        {
          Title = "Run Schedule Now",
          Detail = "Schedule already has sessions in progress.",
          Status = System.Net.HttpStatusCode.BadRequest,
        }
      );
    }

    await runScheduleCmd.Run(schedule, CancellationToken.None);

    if (schedule.Disabled)
      _logger.LogWarning("Manual execution of disabled schedule {ScheduleId} was requested and completed", scheduleId);

    return NoContent();
  }

  [SubjectPermissionAuthorize(typeof(ISchedulesViewPermission))]
  [HttpGet(ScheduleApiRoutes.GetRunningScheduleIds)]
  public async Task<ActionResult<List<int>>> GetRunningScheduleIds(
    [FromServices] IPermissionFilterBuilder permissionFilterBuilder,
    [FromServices] ImmybotDbContext ctx)
  {
    var filter = permissionFilterBuilder.BuildFilterExpression<Schedule, ISchedulesViewPermission>();
    var scheduleIds = await ctx.GetAllSchedules()
      .Where(filter)
      .Select(a => a.Id)
      .ToListAsync();
    return Ok(ctx.GetRunningScheduleIds(scheduleIds));
  }

  [SubjectPermissionAuthorize(typeof(ISchedulesManagePermission))]
  [HttpPost(ScheduleApiRoutes.Cancel)]
  public async Task<IActionResult> CancelSchedule(
    [FromRoute] int scheduleId,
    [FromServices] IResourceAuthorizerFlow resourceAuthorizerFlow,
    [FromServices] IMaintenanceSessionActions maintenanceSessionActions)
  {
    await resourceAuthorizerFlow.PerformAuthorizationWorkflowAsync<Schedule, ISchedulesManagePermission>(
      new DefaultKeyParameters(scheduleId),
      strict: true);

    if (await maintenanceSessionActions.TryCancelSessionsForSchedule(scheduleId))
    {
      return NoContent();
    }

    throw new ImmyWebException(
      new HttpProblem()
      {
        Title = "Cancel Schedule",
        Detail = "No active sessions were found for the schedule requested to be cancelled.",
        Status = System.Net.HttpStatusCode.NotFound,
      }
    );
  }
}
