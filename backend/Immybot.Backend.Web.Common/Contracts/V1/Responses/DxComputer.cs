using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class DxComputer
{
  public Guid DeviceId { get; set; }
  public required int Id { get; set; }
  public required string ComputerName { get; set; }
  public string? PrimaryUserFirstName { get; set; }
  public string? PrimaryUserLastName { get; set; }
  public string? PrimaryUserEmail { get; set; }
  public required string TenantName { get; set; }
  public required int TenantId { get; set; }
  public int? PrimaryPersonId { get; set; }
  public string? SerialNumber { get; set; }
  public string? Manufacturer { get; set; }
  public string? Model { get; set; }
  public string? OperatingSystem { get; set; }
  public bool? HasPendingReboot { get; set; }
  public string? InternalIpAddress { get; set; }
  public string? ExternalIpAddress { get; set; }
  public string? Domain { get; set; }
  public ComputerOnboardingStatus OnboardingStatus { get; set; }
  public bool IsSandbox { get; set; }

  internal static readonly Expression<Func<Computer, DxComputer>> Populate = c => new DxComputer
  {
    Id = c.Id,
    ComputerName = c.ComputerName ?? String.Empty,
    PrimaryUserFirstName = c.PrimaryPerson != null ? c.PrimaryPerson.FirstName : null,
    PrimaryUserLastName = c.PrimaryPerson != null ? c.PrimaryPerson.LastName : null,
    PrimaryUserEmail = c.PrimaryPerson != null ? c.PrimaryPerson.EmailAddress : null,
    PrimaryPersonId = c.PrimaryPersonId,
    TenantId = c.TenantId,
    TenantName = c.Tenant != null ? c.Tenant.Name : string.Empty,
    SerialNumber = c.SerialNumber,
    OperatingSystem = c.OperatingSystem,
    DeviceId = c.DeviceId,
    HasPendingReboot = c.HasPendingReboot,
    InternalIpAddress = c.InternalIpAddress,
    ExternalIpAddress = c.ExternalIpAddress,
    Domain = c.Domain,
    OnboardingStatus = c.OnboardingStatus,
    IsSandbox = c.IsSandbox,
    Manufacturer = c.Manufacturer,
    Model = c.Model
  };
}
