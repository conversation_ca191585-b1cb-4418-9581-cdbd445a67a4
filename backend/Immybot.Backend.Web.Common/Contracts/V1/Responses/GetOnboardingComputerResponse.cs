using Immybot.Backend.Application.Interface.Extensions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetOnboardingComputerResponse
{
  public int Id { get; set; }
  public bool IsOnline { get; set; }
  public ComputerOnboardingStatus OnboardingStatus { get; set; }
  public string? ComputerName { get; set; }
  public string? TenantName { get; set; }
  public bool OnboardingFailed { get; set; }
  public int? OnboardingSessionId { get; set; }
  public string? Serial { get; set; }
  public DateTime UpdatedDate { get; set; }

  public GetOnboardingComputerResponse() { }
  public GetOnboardingComputerResponse(Computer computer)
  {
    Id = computer.Id;
    IsOnline = computer.GetRunScriptAgents().Any(r => r.IsOnline);
    OnboardingStatus = computer.OnboardingStatus;
    ComputerName = computer.GetName();
    TenantName = computer.Tenant?.Name;
    var onboardingSession = computer.Sessions.OrderByDescending(a => a.Id).FirstOrDefault();
    OnboardingFailed = onboardingSession?.SessionStatus == SessionStatus.Failed;
    OnboardingSessionId = onboardingSession?.Id;
    Serial = computer.SerialNumber;
    UpdatedDate = computer.UpdatedDate;
  }
}
