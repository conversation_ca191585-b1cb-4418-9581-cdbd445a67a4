using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using System.Text.Json;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Newtonsoft.Json;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetProviderClientResponse : IProviderClientDetails
{
  public GetProviderClientResponse() { }

  [SetsRequiredMembers]
  public GetProviderClientResponse(ProviderClient client,
    bool includeProviderLink = false)
  {
    ProviderLinkId = client.ProviderLinkId;
    ExternalClientId = client.ExternalClientId;
    LinkedToTenantId = client.LinkedToTenantId;
    ExternalClientName = client.ExternalClientName;
    InternalData = client.InternalData;
    UpdatedBy = client.UpdatedBy;
    UpdatedDateUTC = client.UpdatedDate;
    CreatedBy = client.CreatedBy;
    CreatedDateUTC = client.CreatedDate;
    Status = client.Status;
    Types = client.Types;

    if (includeProviderLink && client.ProviderLink != null)
      ProviderLink = new GetProviderLinkResponse(client.ProviderLink, includeClients: false);
  }
  public int ProviderLinkId { get; set; }
  public required string ExternalClientId { get; set; }
  public int? LinkedToTenantId { get; set; }
  public required string ExternalClientName { get; set; }
  public JsonElement? InternalData { get; set; }
  public int? UpdatedBy { get; set; }
  public DateTime UpdatedDateUTC { get; set; }
  public int? CreatedBy { get; set; }
  public DateTime CreatedDateUTC { get; set; }
  [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
  public GetProviderLinkResponse? ProviderLink { get; }

  internal static Expression<Func<ProviderClient, GetProviderClientResponse>> Projection =>
    x => new GetProviderClientResponse()
    {
      ProviderLinkId = x.ProviderLinkId,
      ExternalClientId = x.ExternalClientId,
      LinkedToTenantId = x.LinkedToTenantId,
      ExternalClientName = x.ExternalClientName,
      InternalData = x.InternalData,
      UpdatedBy = x.UpdatedBy,
      UpdatedDateUTC = x.UpdatedDate,
      CreatedBy = x.CreatedBy,
      CreatedDateUTC = x.CreatedDate,
      Status = x.Status,
      Types = x.Types,
    };

  public string? Status { get; set; }

  public List<string>? Types { get; set; }
}
