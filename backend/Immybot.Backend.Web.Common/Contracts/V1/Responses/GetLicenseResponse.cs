using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using Immybot.Backend.Application.Interface.Commands.Payloads.Licenses;
using Immybot.Backend.Domain.Models;
using NuGet.Versioning;
using Sieve.Attributes;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetLicenseResponse : ILicensePayloadBase
{
  public int Id { get; set; }

  [Sieve(CanFilter = true)]
  public required string Name { get; set; }

  public string LicenseValue { get; set; } = string.Empty;

  [Sieve(CanFilter = true)]
  public SoftwareType SoftwareType { get; set; }

  [Sieve(CanFilter = true)]
  public required string SoftwareIdentifier { get; set; }
  [Sieve(CanFilter = true)]
  public required string SoftwareName { get; set; }

  [Sieve(CanFilter = true)]
  public SemanticVersion? SemanticVersion { get; set; }
  public bool RestrictToMajorVersion { get; set; }

  public int? TenantId { get; set; }
  public string? TenantName { get; set; }

  public string? UpdatedBy { get; set; }
  public DateTime UpdatedDate { get; set; }
  public DateTime CreatedDate { get; set; }

  public GetLicenseResponse() { }

  [SetsRequiredMembers]
  public GetLicenseResponse(License license)
  {
    Id = license.Id;
    Name = license.Name;
    LicenseValue = license.LicenseValue;
    SoftwareType = license.SoftwareType;
    SoftwareIdentifier = license.SoftwareIdentifier;
    SemanticVersion = license.SemanticVersion;
    TenantId = license.TenantId;
    UpdatedBy = license.UpdatedByUser?.DisplayName;
    UpdatedDate = license.UpdatedDate;
    CreatedDate = license.CreatedDate;
    TenantName = license.Tenant?.Name;
    SoftwareName = license.SoftwareName;
    RestrictToMajorVersion = license.RestrictToMajorVersion;
  }

  internal static Expression<Func<License, GetLicenseResponse>> Projection =>
    x => new GetLicenseResponse()
    {
      Id = x.Id,
      Name = x.Name,
      LicenseValue = x.LicenseValue,
      SoftwareType = x.SoftwareType,
      SoftwareIdentifier = x.SoftwareIdentifier,
      SoftwareName = x.SoftwareName,
      SemanticVersion = x.SemanticVersion,
      RestrictToMajorVersion = x.RestrictToMajorVersion,
      TenantId = x.TenantId,
      UpdatedBy = (x.UpdatedBy != null && x.UpdatedByUser != null && x.UpdatedByUser.Person != null)
        ? x.UpdatedByUser.Person.DisplayName
        : null,
      UpdatedDate = x.UpdatedDate,
      CreatedDate = x.CreatedDate,
      TenantName = x.Tenant != null ? x.Tenant.Name : null,
    };
}
