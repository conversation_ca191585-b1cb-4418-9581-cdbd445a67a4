using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Web.Common.Contracts.V1.Responses;

public class GetUserResponse
{
  public int Id { get; set; }
  public string? Email { get; set; }
  public string? Name { get; set; }
  public int TenantId { get; set; }
  public bool IsAdmin { get; set; }
  public string? CompanyName { get; set; }
  public string? Type { get; set; }
  public string? AzurePrincipalId { get; set; }
  public bool IsExpired { get; set; }
  public DateTime? ExpirationDateUTC { get; set; }
  public int? PersonId { get; set; }
  public bool CanManageCrossTenantDeployments { get; set; }
  public bool HasManagementAccess { get; set; }
  public List<string> Roles { get; set; } = new();

  public GetUserResponse() { }

  // Keep existing constructor for backwards compatibility during transition to RBAC
  public GetUserResponse(User user) : this(user, null) { }

  // Changed the constructor to accept roles for RBAC
  // This is a temporary solution to allow frontend development to continue while we transition to RBAC
  public GetUserResponse(User user, IList<string>? roles)
  {
    Id = user.Id;
    TenantId = user.TenantId;
    // todo: remove when rbac feature flag is removed
    IsAdmin = user.IsAdmin;
    CompanyName = user.Tenant?.Name;
    Email = user.Person?.EmailAddress;
    Name = user.Person != null ? $"{user.Person.FirstName} {user.Person.LastName}" : user.ServicePrincipalId;
    Type = user.PersonId != null ? "Person" : "ServicePrincipal";
    AzurePrincipalId = user.Person?.AzurePrincipalId;
    IsExpired = user.IsExpired();
    ExpirationDateUTC = user.ExpirationDateUTC;
    PersonId = user.PersonId;
    CanManageCrossTenantDeployments = user.CanManageCrossTenantDeployments;
    HasManagementAccess = user.HasManagementAccess;

    // Add roles with null safety for backwards compatibility
    Roles = roles?.ToList() ?? new List<string>();  //RBAC
  }

  internal static Expression<Func<User, GetUserResponse>> Projection =>
    x => new GetUserResponse()
    {
      Id = x.Id,
      TenantId = x.TenantId,
      PersonId = x.PersonId,
      // todo: remove when rbac feature flag is removed
      IsAdmin = x.IsAdmin,
      CompanyName = x.Tenant != null ? x.Tenant.Name : null,
      Email = x.Person != null ? x.Person.EmailAddress : null,
      Name = x.Person != null ? $"{x.Person.FirstName} {x.Person.LastName}" : x.ServicePrincipalId ?? null,
      Type = x.PersonId != null ? "Person" : "ServicePrincipal",
      AzurePrincipalId = x.Person != null && x.Person.AzurePrincipalId != null ? x.Person.AzurePrincipalId : null,
      IsExpired = x.IsExpired(),
      ExpirationDateUTC = x.ExpirationDateUTC,
      CanManageCrossTenantDeployments = x.CanManageCrossTenantDeployments,
      HasManagementAccess = x.HasManagementAccess
    };
}
