namespace Immybot.Backend.Web.Common.Contracts.V1;

public static class ApiRoutes
{
  public const string Root = "api";
  public const string Version = "v1";
  public const string Base = Root + "/" + Version;
}

public static class UserAuthRoutes
{
  public const string Base = "auth";
  public const string Me = Base + "/me";
  public const string Refresh = Base + "/refresh";
  public const string Login = Base + "/login";
  public const string LoginCallback = Base + "/login-callback";
  public const string Logout = Base + "/logout";
  public const string LogoutCallback = Base + "/logout-callback";
}

public static class AuthApiRoutes
{
  public const string Get = ApiRoutes.Base + "/auth";
  public const string UpdateAzureTenantAuthDetails = ApiRoutes.Base + "/auth/update-azure-tenant-auth-details";
  public const string DeleteAzureTenantAuthDetails = ApiRoutes.Base + "/auth/delete-azure-tenant-auth-details";
  public const string GetAzureTenantAuthDetails = ApiRoutes.Base + "/auth/get-azure-tenant-auth-details/{azureTenantPrincipalId}";
  public const string GetImmybotIPAddresses = ApiRoutes.Base + "/auth/get-ip-addresses";
  public const string RequestAccess = ApiRoutes.Base + "/auth/request-access";
}

public static class AzureErrorsApiRoutes
{
  public const string GetAllDx = ApiRoutes.Base + "/azure-errors/dx";
  public const string GetForTenantDx = ApiRoutes.Base + "/azure-errors/for-tenant/{tenantPrincipalId}/dx";
}

public static class AuditsApiRoutes
{
  public const string GetLocalDx = ApiRoutes.Base + "/audits/local/dx";
  public const string GetGlobalDx = ApiRoutes.Base + "/audits/global/dx";
}

public static class OauthApiRoutes
{
  public const string BeginAuthCodeFlow = ApiRoutes.Base + "/oauth/begin-auth-code-flow";
  public const string FailAuthCodeFlow = ApiRoutes.Base + "/oauth/fail-auth-code-flow";
  public const string FinishAuthCodeFlow = ApiRoutes.Base + "/oauth/finish-auth-code-flow";
  public const string ListOauthAccessTokens = ApiRoutes.Base + "/oauth/oauth-access-tokens";
  public const string DeleteOauthAccessToken = ApiRoutes.Base + "/oauth/oauth-access-tokens/{id}";
  public const string GetOauthAccessToken = ApiRoutes.Base + "/oauth/oauth-access-tokens/{id}/{accessTokenId}";
  public const string RefreshOauthAccessToken = ApiRoutes.Base + "/oauth/oauth-access-tokens/{id}/refresh";
}

public static class BillingApiRoutes
{
  public const string CreateCustomerPortalSession = ApiRoutes.Base + "/billing/create-customer-portal-session";
  public const string GetSubscriptionDetails = ApiRoutes.Base + "/billing/subscription-details";
  public const string GetProductCatalogItems = ApiRoutes.Base + "/billing/product-catalog-items";
  public const string GetBillingPlatformDetails = ApiRoutes.Base + "/billing/billing-platform-details";
}

public static class ComputerApiRoutes
{
  public const string GetAll = ApiRoutes.Base + "/computers";
  public const string GetAllPaged = ApiRoutes.Base + "/computers/paged";
  public const string Dx = ApiRoutes.Base + "/computers/dx";
  public const string Get = ApiRoutes.Base + "/computers/{computerId}";
  public const string SetExcludedFromUserAffinity = ApiRoutes.Base + "/computers/{computerId}/exclude-from-user-affinity";
  public const string SetExcludedFromUserAffinityBatch = ApiRoutes.Base + "/computers/set-excluded-from-user-affinity";

  public const string ExportUserAffinities =
    ApiRoutes.Base + "/computers/user-affinities/export";
  public const string GetUserAffinitiesDx =
    ApiRoutes.Base + "/computers/user-affinities";
  public const string GetDeviceUpdateFormData = ApiRoutes.Base + "/computers/{computerId}/device-update-form-data";
  public const string GetInventoryScriptResult = ApiRoutes.Base + "/computers/{computerId}/inventory-script-results/{inventoryKey}";
  public const string AllDetectedComputerSoftwareDx = ApiRoutes.Base + "/computers/{computerId}/detected-computer-software";
  public const string GetOnboarding = ApiRoutes.Base + "/computers/onboarding";
  public const string TriggerSyncJob = ApiRoutes.Base + "/computers/sync";
  public const string GetComputerOnlineStatus = ApiRoutes.Base + "/computers/{computerId}/status";
  public const string UpdatePrimaryPerson = ApiRoutes.Base + "/computers/{computerId}/update-primary-person";
  public const string UpdateAdditionalPersons = ApiRoutes.Base + "/computers/{computerId}/update-additional-persons";
  public const string Update = ApiRoutes.Base + "/computers/{computerId}";
  public const string SetToNeedsOnboarding = ApiRoutes.Base + "/computers/{computerId}/set-to-needs-onboarding";
  public const string SkipOnboarding = ApiRoutes.Base + "/computers/skip-onboarding";
  public const string GetScreenShareUrl = ApiRoutes.Base + "/computers/{computerId}/provider-links/{providerLinkId}/screen-share-url";
  public const string ReinventoryComputer = ApiRoutes.Base + "/computers/{computerId}/reinventory";
  public const string GetMyComputers = ApiRoutes.Base + "/computers/my";
  public const string SearchAllInventorySoftwareByName = ApiRoutes.Base + "/computers/all-inventory-software/search-by-name";
  public const string SearchInventorySoftwareByName = ApiRoutes.Base + "/computers/inventory-software/search-by-name";
  public const string SearchInventorySoftwareByUpgradeCode = ApiRoutes.Base + "/computers/inventory-software/search-by-upgrade-code";
  public const string GetInventorySoftwareRegexMatchCount = ApiRoutes.Base + "/computers/inventory-software-regex-match-count";
  public const string DeleteComputers = ApiRoutes.Base + "/computers/bulk-delete";
  public const string PermanentlyDeleteComputers = ApiRoutes.Base + "/computers/bulk-delete/permanent";
  public const string GetEphemeralAgentCircuitBreaker = ApiRoutes.Base + "/computers/{computerId}/ephemeral-agent-circuit-breaker";
  public const string ResetEphemeralAgentCircuitBreaker = ApiRoutes.Base + "/computers/{computerId}/ephemeral-agent-circuit-breaker/reset";
  public const string GetComputerEvents = ApiRoutes.Base + "/computers/{computerId}/events";
  public const string ResolveOnboardingOverridableTargetAssignments = ApiRoutes.Base + "/computers/{computerId}/resolve-onboarding-overridable-target-assignments";
  public const string GetParentTenantInfo = ApiRoutes.Base + "/computers/{computerId}/parent-tenant-info";
  public const string AddTags = ApiRoutes.Base + "/computers/add-tags";
  public const string RemoveTags = ApiRoutes.Base + "/computers/remove-tags";
  public const string ChangeTenant = ApiRoutes.Base + "/computers/change-tenant";
  public const string ExcludeFromMaintenance = ApiRoutes.Base + "/computers/{computerId}/exclude-from-maintenance";
  public const string UpdateNotes = ApiRoutes.Base + "/computers/{computerId}/notes";

  public const string GetEphemeralAgent = ApiRoutes.Base + "/computers/{computerId}/ephemeral-agent";
  public const string DeleteEphemeralAgent = ApiRoutes.Base + "/computers/{computerId}/ephemeral-agent";

  public const string ExportComputers = ApiRoutes.Base + "/computers/export";

  public const string ComputerInventoryDx = ApiRoutes.Base + "/computers/inventory";
  public const string ExportComputerInventory = ApiRoutes.Base + "/computers/inventory/export";
  public const string ComputerAgentStatusReport = ApiRoutes.Base + "/computers/agent-status";

  public const string LaunchEphemeralAgent = ApiRoutes.Base + "/computers/{computerId}/launch-ephemeral-agent";
  public const string LoadRegistryKeys = ApiRoutes.Base + "/computers/{computerId}/registry/keys";
  public const string LoadRegistryKeyValues = ApiRoutes.Base + "/computers/{computerId}/registry/values";
  public const string RestoreComputers = ApiRoutes.Base + "/computers/restore";
}
public static class PreferenceApiRoutes
{
  public const string Get = ApiRoutes.Base + "/preferences";
  public const string GetTenantPreferences = ApiRoutes.Base + "/preferences/tenants/{tenantId}";
  public const string UpdateTenantPreferences = ApiRoutes.Base + "/preferences/tenants/{tenantId}";
  public const string UpdateAppPreferences = ApiRoutes.Base + "/preferences/application";
  public const string UpdateUserPreferences = ApiRoutes.Base + "/preferences/my";
}

public static class ScriptApiRoutes
{
  public const string Base = ApiRoutes.Base + "/scripts";
  public const string DxGetAll = Base + "/dx";
  public const string Search = Base + "/search";
  public const string GetAllLocalScripts = Base + "/local";
  public const string GetAllGlobalScripts = Base + "/global";
  public const string GetAllLocalScriptNames = Base + "/local/names";
  public const string GetAllGlobalScriptNames = Base + "/global/names";
  public const string GetLocalScript = Base + "/local/{scriptId}";
  public const string GetGlobalScript = Base + "/global/{scriptId}";
  public const string UpdateLocalScript = Base + "/local/{scriptId}";
  public const string UpdateGlobalScript = Base + "/global/{scriptId}";
  public const string CreateLocalScript = Base + "/local";
  public const string CreateGlobalScript = Base + "/global";
  public const string DeleteLocalScript = Base + "/local/{scriptId}";
  public const string DeleteGlobalScript = Base + "/global/{scriptId}";
  public const string ScriptSyntaxCheck = Base + "/syntax-check";
  public const string GetScriptReferenceCounts = Base + "/references/count";
  public const string GetGlobalScriptReferences = Base + "/global/{scriptId}/references";
  public const string GetLocalScriptReferences = Base + "/local/{scriptId}/references";
  public const string DuplicateScript = Base + "/duplicate";
  public const string MigrateLocalToGlobalWhatIf = Base + "/local/{scriptId}/migrate-local-to-global-what-if";
  public const string MigrateLocalToGlobal = Base + "/local/{scriptId}/migrate-local-to-global";
  public const string RunScript = Base + "/run";
  public const string EditorServicesBase = Base + "/language-service";
  public const string StartEditorServices = EditorServicesBase + "/start";
  public const string ConnectLanguageService = EditorServicesBase + "/{terminalId}/language";
  public const string ConnectDebuggerService = EditorServicesBase + "/{terminalId}/debug";
  public const string CancelScript = Base + "/debug/cancel/{cancellationId}";
  public const string GetScriptVariablesAndParameters = Base + "/default-variables";
  public const string FindFunctions = Base + "/functions";
  public const string GetFunctionSyntax = Base + "/functions/syntax";
  public const string ValidateParamBlockParameters = Base + "/validate-param-block-parameters";
  public const string DoesScriptHaveParamBlock = Base + "/does-script-have-param-block";
  public const string GetDisabledPreflightScripts = Base + "/disabled-preflight-scripts";
  public const string SetPreflightScriptEnablement = Base + "/set-preflight-script-enablement";
  public const string GetGlobalScriptAudits = Base + "/global/{scriptId}/audit";
  public const string GetLocalScriptAudits = Base + "/local/{scriptId}/audit";
}

public static class MaintenanceSessionApiRoutes
{
  public const string GetAll = ApiRoutes.Base + "/maintenance-sessions";
  public const string DxGetAll = ApiRoutes.Base + "/maintenance-sessions/dx";
  public const string Get = ApiRoutes.Base + "/maintenance-sessions/{sessionId}";
  public const string GetSessionLogs = ApiRoutes.Base + "/maintenance-sessions/{sessionId}/logs";
  public const string GetOldSessionLogs = ApiRoutes.Base + "/maintenance-sessions/{sessionId}/old-logs";
  public const string GetSessionPhases = ApiRoutes.Base + "/maintenance-sessions/{sessionId}/phases";
  public const string GetLastSessionLog = ApiRoutes.Base + "/maintenance-sessions/{sessionId}/last-log";
  public const string CancelSession = ApiRoutes.Base + "/maintenance-sessions/{sessionId}/cancel";
  public const string CancelSessions = ApiRoutes.Base + "/maintenance-sessions/cancel";
  public const string CancelAllSessions = ApiRoutes.Base + "/maintenance-sessions/cancel-all";
  public const string CancelSessionsForSchedule = ApiRoutes.Base + "/maintenance-sessions/cancel-for-schedule/{scheduleId}";
  public const string GetSessionStatusCounts = ApiRoutes.Base + "/maintenance-sessions/status-counts";
  public const string RerunSession = ApiRoutes.Base + "/maintenance-sessions/{sessionId}/rerun";
  public const string RerunSessions = ApiRoutes.Base + "/maintenance-sessions/rerun";
  public const string ResumeSession = ApiRoutes.Base + "/maintenance-sessions/{sessionId}/resume";
  public const string RerunAction = ApiRoutes.Base + "/maintenance-sessions/{sessionId}/actions/{actionId}/rerun";
}

public static class MaintenanceActionApiRoutes
{
  private const string BaseRoute = ApiRoutes.Base + "/maintenance-actions";
  public const string DxGetAll = BaseRoute + "/dx";
  public const string DxGetActionsForComputer = BaseRoute + "/dx-for-computer/{computerId}";
  public const string GetLastLogForAction = BaseRoute + "/{actionId}/last-log";
  public const string GetActionsForMaintenanceItem = BaseRoute + "/maintenance-item";
  public const string GetActionsForVersion = BaseRoute + "/version";
  public const string GetLogsForAction = BaseRoute + "/{actionId}/logs";
  public const string GetActionsNeedingAttentionForComputer = BaseRoute + "/computer/{computerId}/needs-attention";
  public const string GetLatestActionsForComputer = BaseRoute + "/latest-for-computer/{computerId}";
  public const string GetLatestActionForComputers = BaseRoute + "/latest-action-for-computers";
  public const string GetLatestActionsForTenant = BaseRoute + "/latest-for-tenant/{tenantId}";
  public const string GetLatestActionForTenants = BaseRoute + "/latest-action-for-tenants";
  public const string GetLatestNonCompliantMaintenanceActionsForTenant = BaseRoute + "/latest-non-compliant-actions-for-tenant/{tenantId}";
}

public static class AzureApiRoutes
{
  public const string GetPartnerTenantCustomers = ApiRoutes.Base + "/azure/partner-tenant-customers/{partnerPrincipalId}";
  public const string GetPartnerTenantInfos = ApiRoutes.Base + "/azure/partner-tenant-infos";
  public const string SyncAzureUsersForTenants = ApiRoutes.Base + "/azure/sync-users-from-azure-tenants";
  public const string PreconsentCustomerTenants = ApiRoutes.Base + "/azure/preconsent-customer-tenants";
  public const string SyncAzureDetailsForTenants = ApiRoutes.Base + "/azure/sync-details-from-azure-tenants";
  public const string HandleTenantConsent = ApiRoutes.Base + "/azure/tenant-consented";
  public const string DisambiguateAzureTenantType = ApiRoutes.Base + "/azure/disambiguate-azure-tenant-type";
}

public static class ApplicationLogsApiRoutes
{
  public const string GetSourceContexts = ApiRoutes.Base + "/application-logs/source-contexts";
  public const string UpdateSourceContext = ApiRoutes.Base + "/application-logs/source-context";
  public const string ClearSourceContext = ApiRoutes.Base + "/application-logs/source-context/clear";
  public const string ClearAllSourceContexts = ApiRoutes.Base + "/application-logs/source-context/clear-all";
  public const string ToggleStreaming = ApiRoutes.Base + "/application-logs/streaming";
}

public static class DevLabApiRoutes
{
  public const string UnclaimVm = ApiRoutes.Base + "/dev-lab/{computerId}/unclaim";
  public const string DownloadRdpFile = ApiRoutes.Base + "/dev-lab/{computerId}/rdp-info";
}

public static class UserApiRoutes
{
  public const string GetAll = ApiRoutes.Base + "/users";
  public const string Get = ApiRoutes.Base + "/users/{userId}";
  public const string Update = ApiRoutes.Base + "/users/{userId}";
  public const string Create = ApiRoutes.Base + "/users";
  public const string CreateFromPerson = ApiRoutes.Base + "/users/create-from-person";
  public const string Delete = ApiRoutes.Base + "/users/{userId}";
  public const string BulkDelete = ApiRoutes.Base + "/users/bulk-delete";
  public const string SubmitFeedback = ApiRoutes.Base + "/users/submit-feedback";
  public const string ImpersonateUser = ApiRoutes.Base + "/users/{userId}/impersonate";
  public const string StopImpersonatingUser = ApiRoutes.Base + "/users/stop-impersonating";
  public const string GetClaims = ApiRoutes.Base + "/users/claims";
  public const string GrantAccess = ApiRoutes.Base + "/users/grant-access";
  public const string RemoveRoles = ApiRoutes.Base + "/users/remove-roles";
  public const string AddRoles = ApiRoutes.Base + "/users/add-roles";
  public const string UpdateExpiration = ApiRoutes.Base + "/users/update-expiration";
  public const string InvalidateCache = ApiRoutes.Base + "/users/invalidate-cache";
}

public static class TagApiRoutes
{
  public const string GetAll = ApiRoutes.Base + "/tags";
  public const string Get = ApiRoutes.Base + "/tags/{tagId}";
  public const string Update = ApiRoutes.Base + "/tags/{tagId}";
  public const string Create = ApiRoutes.Base + "/tags";
  public const string Delete = ApiRoutes.Base + "/tags/{tagId}";
}

public static class SoftwareApiRoutes
{
  // software
  // local
  public const string GetAllLocalSoftware = ApiRoutes.Base + "/software/local";
  public const string GetLocalSoftware = ApiRoutes.Base + "/software/local/{softwareIdentifier}";
  public const string CreateLocalSoftware = ApiRoutes.Base + "/software/local";
  public const string UpdateLocalSoftware = ApiRoutes.Base + "/software/local/{softwareIdentifier}";
  public const string DeleteLocalSoftware = ApiRoutes.Base + "/software/local/{softwareIdentifier}";

  // global
  public const string GetAllGlobalSoftware = ApiRoutes.Base + "/software/global";
  public const string GetGlobalSoftware = ApiRoutes.Base + "/software/global/{softwareIdentifier}";
  public const string CreateGlobalSoftware = ApiRoutes.Base + "/software/global";
  public const string UpdateGlobalSoftware = ApiRoutes.Base + "/software/global/{softwareIdentifier}";
  public const string DeleteGlobalSoftware = ApiRoutes.Base + "/software/global/{softwareIdentifier}";

  // software versions
  // local
  public const string GetAllLocalSoftwareVersions = ApiRoutes.Base + "/software/local/{softwareIdentifier}/versions";
  public const string GetLocalSoftwareVersion = ApiRoutes.Base + "/software/local/{softwareIdentifier}/versions/{semanticVersion}";
  public const string GetLatestVersionForLocalSoftware = ApiRoutes.Base + "/software/local/{softwareIdentifier}/latest";
  public const string CreateLocalSoftwareVersion = ApiRoutes.Base + "/software/local/{softwareIdentifier}/versions";
  public const string FastCreateLocalSoftwareVersion = ApiRoutes.Base + "/software/local/fast-create";
  public const string UploadLocalSoftwareVersionFile = ApiRoutes.Base + "/software/local/upload";
  public const string UpdateLocalSoftwareVersion = ApiRoutes.Base + "/software/local/{softwareIdentifier}/versions/{semanticVersion}";
  public const string DeleteLocalSoftwareVersion = ApiRoutes.Base + "/software/local/{softwareIdentifier}/versions/{semanticVersion}";
  public const string AnalyzeLocalSoftwarePackage = ApiRoutes.Base + "/software/local/analyze";
  public const string GetDownloadUrlForLocalSoftwareVersion = ApiRoutes.Base + "/software/local/{softwareIdentifier}/versions/{semanticVersion}/request-download";

  // global
  public const string GetAllGlobalSoftwareVersions = ApiRoutes.Base + "/software/global/{softwareIdentifier}/versions";
  public const string GetGlobalSoftwareVersion = ApiRoutes.Base + "/software/global/{softwareIdentifier}/versions/{semanticVersion}";
  public const string GetLatestVersionForGlobalSoftware = ApiRoutes.Base + "/software/global/{softwareIdentifier}/latest";
  public const string CreateGlobalSoftwareVersion = ApiRoutes.Base + "/software/global/{softwareIdentifier}/versions";
  public const string FastCreateGlobalSoftwareVersion = ApiRoutes.Base + "/software/global/fast-create";
  public const string UploadGlobalSoftwareVersionFile = ApiRoutes.Base + "/software/global/upload";
  public const string UpdateGlobalSoftwareVersion = ApiRoutes.Base + "/software/global/{softwareIdentifier}/versions/{semanticVersion}";
  public const string DeleteGlobalSoftwareVersion = ApiRoutes.Base + "/software/global/{softwareIdentifier}/versions/{semanticVersion}";
  public const string AnalyzeGlobalSoftwarePackage = ApiRoutes.Base + "/software/global/analyze";
  public const string GetDownloadUrlForGlobalSoftwareVersion = ApiRoutes.Base + "/software/global/{softwareIdentifier}/versions/{semanticVersion}/request-download";

  // migrate local to global
  public const string MigrateLocalToGlobalWhatIf = ApiRoutes.Base + "/software/local/{softwareIdentifier}/migrate-local-to-global-what-if";
  public const string MigrateLocalToGlobal = ApiRoutes.Base + "/software/local/{softwareIdentifier}/migrate-local-to-global";
}

public static class ChangeRequestApiRoutes
{
  public const string DeleteChangeRequest = ApiRoutes.Base + "/change-requests/{id}";
  public const string ApproveChangeRequest = ApiRoutes.Base + "/change-requests/{id}/approve";
  public const string DenyChangeRequest = ApiRoutes.Base + "/change-requests/{id}/deny";
  public const string RequireChanges = ApiRoutes.Base + "/change-requests/{id}/require-changes";
  public const string CommentOnChangeRequest = ApiRoutes.Base + "/change-requests/{id}/comment";
  public const string GetAllDx = ApiRoutes.Base + "/change-requests/dx";
  public const string GetOpenCount = ApiRoutes.Base + "/change-requests/open-count";
}

public static class TargetAssignmentApiRoutes
{
  public const string CalculateTargetedComputers = ApiRoutes.Base + "/target-assignments/target-preview";
  public const string CalculateTargetedTenants = ApiRoutes.Base + "/target-assignments/tenant-target-preview";
  public const string CalculateTargetedPersons = ApiRoutes.Base + "/target-assignments/persons-target-preview";

  // local routes
  public const string GetLocalTargetAssignmentType = ApiRoutes.Base + "/target-assignments/{id}/type";
  public const string GetAllLocal = ApiRoutes.Base + "/target-assignments";
  public const string GetDuplicatesLocal = ApiRoutes.Base + "/target-assignments/duplicates";
  public const string CreateLocal = ApiRoutes.Base + "/target-assignments";
  public const string UpdateLocal = ApiRoutes.Base + "/target-assignments/{id}";
  public const string DeleteLocal = ApiRoutes.Base + "/target-assignments/{id}";
  public const string GetLocal = ApiRoutes.Base + "/target-assignments/{id}";
  public const string OverrideLocal = ApiRoutes.Base + "/target-assignments/{id}/override";
  public const string UpdateNotesLocal = ApiRoutes.Base + "/target-assignments/{id}/notes";
  public const string ResolveVisibilityTargetAssignments = ApiRoutes.Base + "/target-assignments/visibility";
  public const string BatchUpdateLocal = ApiRoutes.Base + "/target-assignments/batch-update";

  // global routes
  public const string GetGlobalTargetAssignmentType = ApiRoutes.Base + "/target-assignments/global/{id}/type";
  public const string GetAllGlobal = ApiRoutes.Base + "/target-assignments/global";
  public const string CreateGlobal = ApiRoutes.Base + "/target-assignments/global/create";
  public const string UpdateGlobal = ApiRoutes.Base + "/target-assignments/global/{id}";
  public const string DeleteGlobal = ApiRoutes.Base + "/target-assignments/global/{id}";
  public const string GetGlobal = ApiRoutes.Base + "/target-assignments/global/{id}";
  public const string OverrideGlobal = ApiRoutes.Base + "/target-assignments/global/{id}/override";
  public const string UpdateNotesGlobal = ApiRoutes.Base + "/target-assignments/global/{id}/notes";

  // recommended approvals
  public const string GetRecommendedApprovals = ApiRoutes.Base + "/target-assignments/recommended-approvals";
  public const string UpdateRecommendedApprovals = ApiRoutes.Base + "/target-assignments/recommended-approvals/update";

  // optional target assignment approvals
  public const string GetAllOptionalTargetAssignmentApprovalsForComputer = ApiRoutes.Base + "/target-assignments/optional-target-assignment-approvals/computer/{computerId}";
  public const string UpdateOptionalTargetAssignmentApproval = ApiRoutes.Base + "/target-assignments/optional-target-assignment-approvals/{id}";

  // maintenance priorities
  public const string GetMaintenanceItemOrder = ApiRoutes.Base + "/target-assignments/maintenance-item-orders";
  public const string UpdateMaintenanceItemOrder = ApiRoutes.Base + "/target-assignments/update-maintenance-item-order";
  public const string Duplicate = ApiRoutes.Base + "/target-assignments/duplicate";

  public const string MigrateToSupersedingAssignment = ApiRoutes.Base + "/target-assignments/migrate-to-superseding-assignment";
  public const string MigrateToSupersedingAssignmentWhatIf = ApiRoutes.Base + "/target-assignments/migrate-to-superseding-assignment-what-if";

  // change requests
  public const string GetChangeRequest = ApiRoutes.Base + "/target-assignments/change-request/{changeRequestId}";
  public const string GetAllChangeRequests = ApiRoutes.Base + "/target-assignments/change-requests";
  public const string GetAllChangeRequestsForDeployment = ApiRoutes.Base + "/target-assignments/{deploymentId}/change-requests";

  public const string CreateChangeRequestForExistingDeployment = ApiRoutes.Base + "/target-assignments/{deploymentId}/change-request";
  public const string UpdateChangeRequestForExistingDeployment = ApiRoutes.Base + "/target-assignments/{deploymentId}/change-request/{changeRequestId}";

  public const string CreateChangeRequestForNewDeployment = ApiRoutes.Base + "/target-assignments/change-request";
  public const string UpdateChangeRequestForNewDeployment = ApiRoutes.Base + "/target-assignments/change-request/{changeRequestId}";

  public const string GetChangeRequestDiff = ApiRoutes.Base + "/target-assignments/change-request/{changeRequestId}/diff";

  public const string MigrateDeploymentsToProviderLinks =
    ApiRoutes.Base + "/target-assignments/migrate-deployments-to-provider-links";
}

public static class SmtpConfigApiRoutes
{
  public const string GetAll = ApiRoutes.Base + "/smtp-configs";
  public const string Get = ApiRoutes.Base + "/smtp-configs/{tenantId}";
  public const string Update = ApiRoutes.Base + "/smtp-configs/{tenantId}";
  public const string Create = ApiRoutes.Base + "/smtp-configs";
  public const string Delete = ApiRoutes.Base + "/smtp-configs/{tenantId}";
  public const string SendTestEmail = ApiRoutes.Base + "/smtp-configs/send-test-email";
}

public static class ScheduleApiRoutes
{
  public const string GetAll = ApiRoutes.Base + "/schedules";
  public const string Get = ApiRoutes.Base + "/schedules/{scheduleId}";
  public const string Update = ApiRoutes.Base + "/schedules/{scheduleId}";
  public const string Create = ApiRoutes.Base + "/schedules";
  public const string Delete = ApiRoutes.Base + "/schedules/{scheduleId}";
  public const string RunScheduleNow = ApiRoutes.Base + "/schedules/{scheduleId}/run-now";
  public const string GetRunningScheduleIds = ApiRoutes.Base + "/schedules/running-schedule-ids";
  public const string Cancel = ApiRoutes.Base + "/schedules/{scheduleId}/cancel";
}

public static class MetricApiRoutes
{
  public const string GetAppMetrics = ApiRoutes.Base + "/metrics/app";
  public const string GetCircuitBreakers = ApiRoutes.Base + "/metrics/circuit-breakers";
  public const string IsolateCircuitBreaker = ApiRoutes.Base + "/metrics/circuit-breakers/isolate";
  public const string ResetCircuitBreaker = ApiRoutes.Base + "/metrics/circuit-breakers/reset";
  public const string GetAllProviderLinkMetrics = ApiRoutes.Base + "/metrics/provider-links";
}

public static class BrandingApiRoutes
{
  public const string GetAll = ApiRoutes.Base + "/brandings";
  public const string Get = ApiRoutes.Base + "/brandings/{id}";
  public const string Update = ApiRoutes.Base + "/brandings/{id}";
  public const string Create = ApiRoutes.Base + "/brandings";
  public const string Delete = ApiRoutes.Base + "/brandings/{id}";
  public const string SetDefaultBranding = ApiRoutes.Base + "/brandings/global-default/{id}";
  public const string SendTestBrandingEmail = ApiRoutes.Base + "/brandings/send-test-email";
  public const string ValidateTimeFormat = ApiRoutes.Base + "/brandings/validate-time-format/{timeFormat}";
  public const string GetSupportBranding = ApiRoutes.Base + "/brandings/support-branding";
}

public static class EmailApiRoutes
{
  public const string Postpone = ApiRoutes.Base + "/maintenance-emails/{emailGuid}/jobs/postpone";
  public const string RebootNow = ApiRoutes.Base + "/maintenance-emails/{emailGuid}/jobs/rebootnow";
  public const string UpdateNow = ApiRoutes.Base + "/maintenance-emails/{emailGuid}/jobs/now";
}

public static class ImmyBotApiRoutes
{
  public const string RunImmyService = ApiRoutes.Base + "/run-immy-service";
  public const string RunImmyServiceNew = ApiRoutes.Base + "/run-immy-service-new";
}

public static class EphemeralAgentRoutes
{
  private const string Base = ApiRoutes.Base + "/ephemeral-session";
  public const string SessionEndpointBase = Base + "/{ephemeralSessionId}/{agentInstanceId}/{providerAgentId}";
  public const string GetDevelopmentEphemeralBinary = Base + "/development/latest-ephemeral-binary";
}

public static class ImmyAgentMetadataRoutes
{
  public const string Base = $"{ApiRoutes.Base}/immy-agent-metadata";
  public const string AgentHash = $"{Base}/agent-hash";
}

public static class ApplicationLocksRoutes
{
  public const string EndpointBase = ApiRoutes.Base + "/application-locks";
  public const string EventStream = EndpointBase + "/realtime-event-stream";
  public const string RequestCancellation = EndpointBase + "/request-cancellation";
}

public static class WebHookRoutes
{
  public const string EndpointBase = ApiRoutes.Base + "/webhooks/{id}";
}

public static class PersonApiRoutes
{
  public const string Dx = ApiRoutes.Base + "/persons/dx";
  public const string GetAll = ApiRoutes.Base + "/persons";
  public const string Get = ApiRoutes.Base + "/persons/{id}";
  public const string Delete = ApiRoutes.Base + "/persons/{id}";
  public const string Put = ApiRoutes.Base + "/persons/{id}";
  public const string Create = ApiRoutes.Base + "/persons";
  public const string GetPersonsRequestingAccess = ApiRoutes.Base + "/persons/requesting-access";
  public const string GrantAccess = ApiRoutes.Base + "/persons/{personId}/grant-access";
  public const string GrantAccessRbac = ApiRoutes.Base + "/persons/grant-access-rbac";
  public const string DenyAccess = ApiRoutes.Base + "/persons/{personId}/deny-access";
  public const string GetSelfServiceItems = ApiRoutes.Base + "/persons/{id}/self-service";
  public const string AddTags = ApiRoutes.Base + "/persons/add-tags";
  public const string RemoveTags = ApiRoutes.Base + "/persons/remove-tags";
}

public static class ProviderTypeApiRoutes
{
  public const string GetAll = ApiRoutes.Base + "/provider-types";
  public const string GetDeviceGroups = ApiRoutes.Base + "/provider-types/device-group-types/{deviceGroupTypeId}/device-groups";
  public const string GetClientGroups = ApiRoutes.Base + "/provider-types/client-group-types/{clientGroupTypeId}/client-groups";
  public const string BindParameters = ApiRoutes.Base + "/provider-types/{providerType}/bind-parameters";
}

public static class ProviderLinkApiRoutes
{
  // links
  public const string ProviderLinkBase = ApiRoutes.Base + "/provider-links";
  public const string GetAll = ProviderLinkBase;
  public const string Create = ProviderLinkBase;
  public const string CreateWithExternalProviderReference = ProviderLinkBase + "/create-with-external-provider-reference";
  public const string Get = ProviderLinkBase + "/{id}";
  public const string Update = ProviderLinkBase + "/{id}";
  public const string Delete = ProviderLinkBase + "/{id}";
  public const string VerifyCredentialsWithExternalProviderReference = ProviderLinkBase + "/verify-with-external-provider-reference";
  public const string Reload = ProviderLinkBase + "/{id}/reload";

  // old manager required routes
  public const string OldRmmLinkBase = ApiRoutes.Base + "/rmm-links";
  public const string OldGetAll = OldRmmLinkBase;
  public const string OldGet = OldRmmLinkBase + "/{id}";
  public const string OldUpdate = OldRmmLinkBase + "/{id}";
  public const string OldCreate = OldRmmLinkBase;

  // cross-provider-link references
  public const string ProviderLinkXRefBase = ProviderLinkBase + "/{id}/cross-references";
  public const string GetExternalProviderLinkInitializationInfo = ProviderLinkXRefBase + "/init-info";
  public const string CreateLinkedProviderReference = ProviderLinkXRefBase + "/create";
  public const string DeleteLinkedProviderReference = ProviderLinkXRefBase + "/{externalLinkId}/delete";
  public const string DisableLinkedProviderClientLinking = ProviderLinkXRefBase + "/{externalLinkId}/disable-client-linking";
  public const string EnableLinkedProviderClientLinking = ProviderLinkXRefBase + "/{externalLinkId}/enable-client-linking";
  public const string SyncClientsFromLinkedProvider = ProviderLinkXRefBase + "/{externalLinkId}/sync-clients";

  // psa
  public const string GetTechnicianPageInfoFromPsaTicket = ProviderLinkBase + "/{id}/tickets/{ticketId}";

  // clients
  public const string ProviderLinkClientBase = ProviderLinkBase + "/{id}/clients";
  public const string GetClients = ProviderLinkClientBase;
  public const string LinkClientToNewTenant = ProviderLinkClientBase + "/link-to-new-tenant";
  public const string LinkExactMatchClients = ProviderLinkClientBase + "/link-exact-match-clients";
  public const string LinkClientsToTenant = ProviderLinkClientBase + "/link-to-tenant";
  public const string AutoLinkClientsToTenants = ProviderLinkClientBase + "/auto-link-to-tenants";
  public const string UnlinkClients = ProviderLinkClientBase + "/unlink-from-tenants";
  public const string GetClientStatuses = ProviderLinkClientBase + "/statuses";
  public const string GetClientTypes = ProviderLinkClientBase + "/types";
  public const string FetchClientsFromProvider = ProviderLinkClientBase + "/sync";
  public const string SyncAgentsForClients = ProviderLinkClientBase + "/sync-agents";

  // agents
  public const string ImmyAgentProviderOldRekeyEndpoint = "/installer/agent-rekey/request";
  public const string ProviderLinkAgentBase = ProviderLinkBase + "/{id}/agents";
  public const string GetAgentExecutableUri = ProviderLinkAgentBase + "/executable-uri";
  public const string GetAgentExecutableUriWithOnboardingOptions = ProviderLinkAgentBase + "/executable-uri-with-onboarding";
  public const string GetAgentProvisioningPackageUri = ProviderLinkAgentBase + "/provisioning-package-uri";
  public const string GetAgentProvisioningPackageUriWithOnboardingOptions = ProviderLinkAgentBase + "/provisioning-package-uri-with-onboarding";
  public const string GetAgentPowerShellInstallScript = ProviderLinkAgentBase + "/powershell-install-script";
  public const string GetAgentPowerShellInstallScriptWithOnboardingOptions = ProviderLinkAgentBase + "/powershell-install-script-with-onboarding";
  public const string GetAgentBashInstallScript = ProviderLinkAgentBase + "/bash-install-script";
  public const string GetAgentBashInstallScriptWithOnboardingOptions = ProviderLinkAgentBase + "/bash-install-script-with-onboarding";
  public const string InstallAgentOnComputer = ProviderLinkAgentBase + "/install-on-computer/{computerId}";
  public const string DeleteOfflineAgentFromComputer = ProviderLinkAgentBase + "/{agentId}/delete-offline-agent-from-computer";
  public const string RefreshAgentOnlineStatus = ProviderLinkAgentBase + "/{agentId}/refresh-device-online-status";
  public const string GetExternalProviderAgentUrl = ProviderLinkAgentBase + "/{computerId}/external-agent-url";
  public const string SyncAgents = ProviderLinkAgentBase + "/sync";

  // custom provider specific routes
  // {**catchAll} is used to capture the remaining relative path of the url and provide it as a string parameter to catchAll
  public const string ProviderCustomRoute = "plugins/api/v1/{providerLinkId}/{**catchAll}";
}


public static class TenantApiRoutes
{
  public const string Base = ApiRoutes.Base + "/tenants";
  public const string GetAll = Base;
  public const string Create = Base;
  public const string Get = Base + "/{id}";
  public const string Update = Base + "/{id}";
  public const string UpdateAzureTenantLink = Base + "/update-azure-tenant-link";
  public const string BulkCreate = Base + "/bulk-create";
  public const string BulkDelete = Base + "/bulk-delete";
  public const string BulkMerge = Base + "/bulk-merge";
  public const string ActivateTenant = Base + "/activate/{id}";
  public const string DeactivateTenant = Base + "/deactivate/{id}";
  public const string SoftwareFromInventory = Base + "/software-from-inventory/{id}";
  public const string AllSoftwareFromInventoryDx = Base + "/software-from-inventory/dx";
  public const string ExportSoftware = Base + "/software-from-inventory/export";
  public const string GetAzureGroupsAtTenant = Base + "/{id}/azure-groups";
  public const string GetAzureGroupAtTenant = Base + "/{id}/azure-groups/{groupId}";
  public const string GetComputersExcludedFromMaintenance = Base + "/{id}/computers/excluded-from-maintenance";
  public const string AddTags = Base + "/add-tags";
  public const string RemoveTags = Base + "/remove-tags";
  public const string SetParentTenant = Base + "/set-parent-tenant";
  public const string RemoveParentTenant = Base + "/remove-parent-tenant";
  public const string ResolveAssignmentsForMaintenanceItem = Base + "/resolve-assignments-for-maintenance-item";
  public const string GetComputerCounts = Base + "/computer-counts";
}

public static class LicenseApiRoutes
{
  public const string Dx = ApiRoutes.Base + "/licenses/dx";
  public const string GetAll = ApiRoutes.Base + "/licenses";
  public const string Get = ApiRoutes.Base + "/licenses/{licenseId}";
  public const string Update = ApiRoutes.Base + "/licenses/{licenseId}";
  public const string Create = ApiRoutes.Base + "/licenses";
  public const string Delete = ApiRoutes.Base + "/licenses/{licenseId}";
  public const string Upload = ApiRoutes.Base + "/licenses/upload";
  public const string GetDownloadUrl = ApiRoutes.Base + "/licenses/{licenseId}/request-download";
}

public static class DevInstanceManagementApiRoutes
{
  public const string StartHangfireServer = ApiRoutes.Base + "/dev-instance-management/start-hangfire-server";
  public const string StopHangfireServer = ApiRoutes.Base + "/dev-instance-management/stop-hangfire-server";
}

public static class ProviderAgentApiRoutes
{
  public const string Base = ApiRoutes.Base + "/provider-agents";
  public const string GetDx = Base + "/dx";
  public const string GetPendingDx = Base + "/pending-dx";
  public const string GetPending = Base + "/pending";
  public const string BulkDeletePendingAgents = Base + "/bulk-delete-pending";
  public const string GetPendingCounts = Base + "/pending-counts";
  public const string ResolveFailuresForAgents = Base + "/resolve-failures";
  public const string ResolveFailuresForAgent = Base + "/{agentId}/resolve-failures";
  public const string ResolveFailure = Base + "/resolve-failure/{failureId}";
  public const string GetPendingAgentConflictsForComputer = Base + "/{computerId}/pending-conflicts";
  public const string GetIdentificationLogs = Base + "/{agentId}/identification-logs";
  public const string IdentifyAgents = Base + "/identify";
  public const string RestoreAgentComputers = Base + "/restore-agents-computers";
}

public static class MaintenanceTaskApiRoutes
{
  public const string Search = ApiRoutes.Base + "/maintenance-tasks/search";
  public const string GetAllLocalMaintenanceTasks = ApiRoutes.Base + "/maintenance-tasks/local";
  public const string GetAllGlobalMaintenanceTasks = ApiRoutes.Base + "/maintenance-tasks/global";
  public const string GetLocalMaintenanceTask = ApiRoutes.Base + "/maintenance-tasks/local/{id}";
  public const string GetGlobalMaintenanceTask = ApiRoutes.Base + "/maintenance-tasks/global/{id}";
  public const string CreateGlobalMaintenanceTask = ApiRoutes.Base + "/maintenance-tasks/global";
  public const string CreateLocalMaintenanceTask = ApiRoutes.Base + "/maintenance-tasks/local";
  public const string UpdateGlobalMaintenanceTask = ApiRoutes.Base + "/maintenance-tasks/global/{id}";
  public const string UpdateLocalMaintenanceTask = ApiRoutes.Base + "/maintenance-tasks/local/{id}";
  public const string DeleteGlobalMaintenanceTask = ApiRoutes.Base + "/maintenance-tasks/global/{id}";
  public const string DeleteLocalMaintenanceTask = ApiRoutes.Base + "/maintenance-tasks/local/{id}";
  public const string GetReferenceCount = ApiRoutes.Base + "/maintenance-tasks/reference-count";
  public const string GetGlobalMaintenanceTaskReferences = ApiRoutes.Base + "/maintenance-tasks/global/{id}/references";
  public const string DuplicateMaintenanceTask = ApiRoutes.Base + "/maintenance-tasks/duplicate";
  public const string ValidateParamBlockParameters = ApiRoutes.Base + "/maintenance-tasks/validate-param-block-parameters";
  public const string GetParamBlockFromLocalMaintenanceTaskParameters = ApiRoutes.Base + "/maintenance-tasks/local/{id}/param-block-from-parameters";
  public const string GetParamBlockFromGlobalMaintenanceTaskParameters = ApiRoutes.Base + "/maintenance-tasks/global/{id}/param-block-from-parameters";

  // migrate local to global
  public const string MigrateLocalToGlobalWhatIf = ApiRoutes.Base + "/maintenance-tasks/local/{id}/migrate-local-to-global-what-if";
  public const string MigrateLocalToGlobal = ApiRoutes.Base + "/maintenance-tasks/local/{id}/migrate-local-to-global";
}

public static class InventoryTaskApiRoutes
{
  private const string _base = ApiRoutes.Base + "/inventory-tasks";
  public const string GetAll = _base;
  public const string CreateLocal = _base + "/local";
  public const string UpdateLocal = _base + "/local/{id}";
  public const string DeleteLocal = _base + "/local/{id}";
  public const string AddScriptToLocalInventoryTask = _base + "/local/{id}/scripts";
  public const string DeleteScriptFromLocalInventoryTask = _base + "/local/{taskId}/scripts/{inventoryKey}";
}

public static class ChocolateyApiRoutes
{
  public const string Search = ApiRoutes.Base + "/chocolatey/search";
  public const string FindPackagesById = ApiRoutes.Base + "/chocolatey/find-packages-by-id";
}

public static class NotificationApiRoutes
{
  public const string GetDx = ApiRoutes.Base + "/notifications/dx";
  public const string Acknowledge = ApiRoutes.Base + "/notifications/acknowledge";
  public const string GetUnacknowledgedNotifications = ApiRoutes.Base + "/notifications/unacknowledged";
  public const string SilenceNotification = ApiRoutes.Base + "/notifications/{type}/silence";
  public const string GetSilencedNotificationsForUser = ApiRoutes.Base + "/notifications";
  public const string RemoveSilencedNotification = ApiRoutes.Base + "/notifications/{id}/unsilence";
}

public static class SystemApiRoutes
{
  public const string GetReleases = ApiRoutes.Base + "/system/releases";
  public const string PullUpdate = ApiRoutes.Base + "/system/pull-update";
  public const string GetTimezones = ApiRoutes.Base + "/system/timezones";
  public const string RestartBackend = ApiRoutes.Base + "/system/restart-backend";
  public const string GetImmySupportAccessGrantDetails = ApiRoutes.Base + "/system/immy-support-access-grant-details";
  public const string IsImmySupportAccessGranted = ApiRoutes.Base + "/system/is-immy-support-access-granted";
  public const string RequestSessionSupport = ApiRoutes.Base + "/system/request-session-support";
  public const string RequestFormSupport = ApiRoutes.Base + "/system/request-form-support";
  public const string EnableImmySupportAccess = ApiRoutes.Base + "/system/enable-immy-support-access";
  public const string DisableImmySupportAccess = ApiRoutes.Base + "/system/disable-immy-support-access";
  public const string EnqueuePendoTrackEvent = ApiRoutes.Base + "/system/enqueue-pendo-track-event";
  public const string UpdateReleaseChannel = ApiRoutes.Base + "/system/update-release-channel";
  public const string Reset = ApiRoutes.Base + "/system/reset";
}
public static class MediaApiRoutes
{
  public const string Search = ApiRoutes.Base + "/media/search";
  public const string GetLocal = ApiRoutes.Base + "/media/local";
  public const string GetLocalById = ApiRoutes.Base + "/media/local/{id}";
  public const string UpdateLocal = ApiRoutes.Base + "/media/local/{id}";
  public const string DeleteLocal = ApiRoutes.Base + "/media/local/{id}";
  public const string CreateLocal = ApiRoutes.Base + "/media/local";
  public const string GetGlobal = ApiRoutes.Base + "/media/global";
  public const string GetGlobalById = ApiRoutes.Base + "/media/global/{id}";
  public const string UpdateGlobal = ApiRoutes.Base + "/media/global/{id}";
  public const string DeleteGlobal = ApiRoutes.Base + "/media/global/{id}";
  public const string CreateGlobal = ApiRoutes.Base + "/media/global";
  public const string RequestFileDownloadUrl = ApiRoutes.Base + "/media/requestFileDownloadUrl";
  public const string GetLocalDownloadUrl = ApiRoutes.Base + "/media/local/{id}/download-url";
  public const string GetGlobalDownloadUrl = ApiRoutes.Base + "/media/global/{id}/download-url";
  public const string UploadGlobalMedia = ApiRoutes.Base + "/media/global/upload";
  public const string UploadLocalMedia = ApiRoutes.Base + "/media/local/upload";
  public const string UploadSupportMedia = ApiRoutes.Base + "/media/support/upload";
}

public static class SyncsApiRoutes
{
  public const string TriggerUserAffinitySync = ApiRoutes.Base + "/syncs/trigger-user-affinity-sync";
  public const string TriggerAzureUserSync = ApiRoutes.Base + "/syncs/azure-user-sync";
}

public static class DynamicIntegrationTypeRoutes
{
  private const string _base = ApiRoutes.Base + "/dynamic-provider-types";
  public const string GetAll = _base;
  public const string CreateLocal = _base + "/local;";
  public const string CreateGlobal = _base + "/global";
  public const string GetLocal = _base + "/local/{id}";
  public const string GetGlobal = _base + "/global/{id}";
  public const string UpdateLocal = _base + "/local/{id}";
  public const string UpdateGlobal = _base + "/global/{id}";
  public const string DeleteLocal = _base + "/local/{id}";
  public const string DeleteGlobal = _base + "/global/{id}";
  public const string Reload = _base + "/reload";
  public const string ReloadByGlobalId = _base + "/global/{id}/reload";
  public const string ReloadByLocalId = _base + "/local/{id}/reload";

  public const string SetupTestIntegration = _base + "/test-environment/{terminalId}";
  public const string RemoveTestIntegration = _base + "/test-environment/{terminalId}";
  public const string TestIntegrationBindConfigurationForm = _base + "/test-environment/{terminalId}/bind-configuration-form";
  public const string TestIntegrationMethod = _base + "/test-environment/{terminalId}/execute-method/{method}";
}

public static class ProviderAuditLogRoutes
{
  public const string Base = ApiRoutes.Base + "/provider-audit-logs";
  public const string GetAllPaged = Base + "/paged";
  public const string Get = Base + "/{id}";
}

public static class GettingStartedApiRoutes
{
  public const string Base = ApiRoutes.Base + "/getting-started";
  public const string Checklist = Base + "/checklist";
  public const string ResetChecklist = Base + "/checklist/reset";
  public const string CompleteChecklist = Base + "/checklist/complete";
}

public static class RoleApiRoutes
{
  private const string _base = ApiRoutes.Base + "/roles";
  public const string GetRoles = _base;
  public const string GetRole = _base + "/{roleId}";
  public const string CreateRole = _base;
  public const string UpdateRole = _base + "/{roleId}";
  public const string DeleteRole = _base + "/{roleId}";
  public const string CloneRole = _base + "/{roleId}/clone";
  public const string GetPermissions = _base + "/permissions";
}
