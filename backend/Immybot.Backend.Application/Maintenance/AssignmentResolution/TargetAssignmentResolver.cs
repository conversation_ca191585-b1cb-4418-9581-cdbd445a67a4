using System.Collections.Immutable;
using System.Diagnostics;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Domain.Infrastructure;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.Logging;
using static Immybot.Backend.Domain.Helpers.ExceptionHelpers;

namespace Immybot.Backend.Application.Maintenance.AssignmentResolution;

internal class TargetAssignmentResolver(
  ImmybotDbContext _ctx,
  IProviderActions _providerActions,
  IMaintenanceActionInitializer _maintenanceActionInitializer,
  ISoftwareActions _softwareActions,
  IComputerAssignmentActions _computerAssignmentActions,
  IMaintenanceTaskActions _maintenanceTaskActions,
  ITargetAssignmentActions _targetAssignmentActions,
  IAzureActions _azureActions,
  ILogger<TargetAssignmentResolver> logger) : ITargetAssignmentResolver
{
  public static TargetAssignment? GetAdhocAssignment(TargetAssignment? targetAssignment,
    MaintenanceItem? maintenanceItem,
    MaintenanceTask? maintenanceTask = null)
  {
    if (maintenanceItem == null) return null;

    if (targetAssignment is null)
    {
      targetAssignment = new TargetAssignment
      {
        MaintenanceIdentifier = maintenanceItem.MaintenanceIdentifier,
        MaintenanceType = maintenanceItem.MaintenanceType,
      };
    }
    else
    {
      targetAssignment.MaintenanceIdentifier = maintenanceItem.MaintenanceIdentifier;
      targetAssignment.MaintenanceType = maintenanceItem.MaintenanceType;
    }

    // if this isn't a real assignment then set the database type to local, otherwise use the assignments database type
    if (targetAssignment.Id is 0)
    {
      targetAssignment.DatabaseType = DatabaseType.Local;
    }

    MaintenanceTaskAssignmentDetails? taskDetails = null;

    if (maintenanceItem is SoftwareMaintenanceItem softwareItem)
    {
      targetAssignment.LicenseId = softwareItem.Details?.LicenseId;
      targetAssignment.DesiredSoftwareState = softwareItem.Details?.DesiredSoftwareState;
      targetAssignment.SoftwareSemanticVersion = softwareItem.Details?.SoftwareSemanticVersion;
      targetAssignment.SoftwareProviderType = softwareItem.Details?.SoftwareProviderType;
      taskDetails = softwareItem.TaskConfigurationDetails;
    }
    else if (maintenanceItem is TaskMaintenanceItem taskItem)
    {
      taskDetails = taskItem.Details;
    }

    if (taskDetails != null)
    {
      if (taskDetails.MaintenanceTaskMode is not null)
      {
        targetAssignment.MaintenanceTaskMode = taskDetails.MaintenanceTaskMode;
      }

      if (taskDetails.TaskParameterValues is not null)
      {
        targetAssignment.TaskParameterValues = taskDetails.TaskParameterValues.ToImmutableDictionary();
      }
#pragma warning disable CS0618 // Type or member is obsolete
      else if (taskDetails.MaintenanceTaskParameterValues is { Count: > 0 } oldParamVals)
#pragma warning restore CS0618 // Type or member is obsolete
      {
        // we likely need to populate the parameter type and parameter name
        if (maintenanceTask is not null)
        {
          foreach (var parameterValue in oldParamVals)
          {
            var parameter = maintenanceTask.Parameters.FirstOrDefault(a => a.Id == parameterValue.MaintenanceTaskParameterId);
            if (parameter is not null)
            {
              parameterValue.ParameterName = parameter.Name;
              parameterValue.ParameterType = parameter.DataType;
            }
          }
        }

        var results = ParameterConverter
          .GetParameterValueDictionaryFromMaintenanceTaskParameterValues(
            oldParamVals.OfType<IMaintenanceTaskParameterValueDetails>().ToList());
        targetAssignment.TaskParameterValues = results.ParameterValues.ToImmutableDictionary();
      }
    }

    return targetAssignment;
  }

  /// <summary>
  /// Detects whether the computer in the runconext is part of the provided assignment
  /// <para>NB: This method is internal to allow testing</para>
  /// </summary>
  /// <param name="context"></param>
  /// <param name="extendedAssignment"></param>
  /// <returns></returns>
  internal async Task<bool> IsComputerInAssignment(IRunContext context,
    TargetAssignmentWithTaskOnboardingFlag extendedAssignment)
  {
    Software? software = null;
    ScriptContextParameters? scriptContextParameters = null;
    var assignment = extendedAssignment.TargetAssignment;

    if(assignment.TargetCategory is not TargetCategory.Computer)
      return false;

    var isDeploymentCrossTenant = TargetAssignmentHelpers.GetTargetScopeForTargetType(
      context.Args.ProviderActions,
      assignment.TargetType,
      providerDeviceGroupType: assignment.ProviderDeviceGroupType) == TargetScope.CrossTenant;

    if (isDeploymentCrossTenant &&
      context.TenantPreferences?.ExcludeFromCrossTenantDeploymentsAndSchedules is true)
    {
      context.AddLog($"Deployment #{assignment.Id} excluded. Cross-tenant deployments have been excluded for {context.TenantName}.");
      return false;
    }

    // only metascript and tenant metascript target types require fetching the software
    if (assignment.TargetType == TargetType.Metascript || assignment.TargetType == TargetType.TenantMetascript)
    {
      software = assignment.SoftwareSpecifier != null
        ? await _softwareActions.GetSoftware(
          assignment.SoftwareSpecifier.SoftwareType,
          assignment.MaintenanceIdentifier,
          context.Args.StopProcessing,
          context.Args.PolicyContext,
          context.Args.CachePolicy)
        : null;

      scriptContextParameters = new ScriptContextParameters()
      {
        Software = software,
        TargetAssignment = assignment,
      };
    }

    // if the assignment specifies that it should only target onboarding computers
    // and the context is for an already-onboarded computer, we can short-circuit
    if ((assignment.TargetEnforcement is TargetEnforcement.Onboarding || extendedAssignment.TaskIsOnboardingOnly) && (context.Args.Computer is { OnboardingStatus: ComputerOnboardingStatus.Onboarded } || (extendedAssignment.IgnoreDuringAutomaticOnboarding && context.Args.MaintenanceOnboardingConfiguration?.AutomaticOnboarding is true)))
      return false;

    try
    {
      if (assignment.TargetGroupFilter != 0)
      {
        var filter = assignment.TargetGroupFilter;
        if (filter == TargetGroupFilter.Servers && !context.IsComputerAServer) return false;
        if (filter == TargetGroupFilter.WorkstationsAndPortableDevices && (!context.IsComputerPortable && !context.IsComputerAWorkstation)) return false;
        if (filter == TargetGroupFilter.Workstations && !context.IsComputerAWorkstation) return false;
        if (filter == TargetGroupFilter.PortableDevices && !context.IsComputerPortable) return false;
        if (filter == TargetGroupFilter.DomainControllers && !context.IsDomainController) return false;
        if (filter == TargetGroupFilter.PrimaryDomainControllers && !context.IsPrimaryDomainController) return false;
      }
      switch (assignment.TargetType)
      {
        case TargetType.Computer:
          {
            return context.Args.Computer?.Id is int c && assignment.Target == c.ToString();
          }
        case TargetType.All:
          return true;
        case TargetType.Person:
          {
            return context.Args.Computer?.PrimaryPersonId is int p && assignment.Target == p.ToString();
          }
        case TargetType.Tag:
          var tagId = Convert.ToInt32(assignment.Target);
          var inComputerTag = context.Args.Computer?.ComputerTags.Exists(c => c.TagId == tagId) is true;
          if (inComputerTag) return true;
          var inTenantTag = context.Args.Tenant?.TenantTags.Exists(t => t.TagId == tagId) is true;
          if (inTenantTag) return true;
          var inPersonTag = context.Args.Computer?.PrimaryPerson?.PersonTags.Any(p => p.TagId == tagId) is true;
          return inPersonTag;
        case TargetType.TenantTag:
          if (!DoesAssignmentTargetTenant(assignment, context.TenantId)) return false;
          tagId = Convert.ToInt32(assignment.Target);
          inComputerTag = context.Args.Computer?.ComputerTags.Exists(c => c.TagId == tagId) is true;
          if (inComputerTag) return true;
          inTenantTag = context.Args.Tenant?.TenantTags.Exists(t => t.TagId == tagId) is true;
          if (inTenantTag) return true;
          inPersonTag = context.Args.Computer?.PrimaryPerson?.PersonTags.Any(p => p.TagId == tagId) is true;
          return inPersonTag;
        case TargetType.Metascript:
          {
            // Metascripts have 'Invoke-ImmyCommand' that can reach out to the computer
            // so we shouldn't run them if the computer is offline
            if (!context.IsComputerOnline) return false;

            var scriptIdentifier = ScriptIdentifier.FromJson(assignment.Target);
            if (scriptIdentifier is null) return false;

            var script = await context.Args.SoftwareActions
              .GetScript(
                scriptIdentifier.ScriptType,
                scriptIdentifier.ScriptId.ToString(),
                policyContext: context.Args.PolicyContext,
                cachePolicy: context.Args.CachePolicy,
                cancellationToken: context.Args.StopProcessing);
            if (script == null) return false;
            if (script.ScriptCategory is not ScriptCategory.MetascriptDeploymentTarget)
            {
              context.AddLog($"Skipping assignment {assignment.Id} - script {script.Id} is not a Metascript (category {script.ScriptCategory}).");
              return false;
            }
            var res = await context
              .RunScript<bool>(script, 500, context.Args.StopProcessing,
              logPrefix: !string.IsNullOrEmpty(software?.Name) ? $"{software?.Name} - " : "",
                contextParameters: scriptContextParameters);

            if (!res.HadTerminatingException) return res.OutputAsObject;

            context.AddLog($"Failed to resolve {assignment}.\n\n{res.GetErrorString()}");
            return false;
          }
        case TargetType.TenantMetascript:
          {
            // Metascripts have 'Invoke-ImmyCommand' that can reach out to the computer
            // so we shouldn't run them if the computer is offline
            if (!context.IsComputerOnline) return false;
            if (!DoesAssignmentTargetTenant(assignment, context.TenantId)) return false;

            var scriptIdentifier = ScriptIdentifier.FromJson(assignment.Target);
            if (scriptIdentifier is null) return false;

            var script = await context.Args.SoftwareActions
              .GetScript(
                scriptIdentifier.ScriptType,
                scriptIdentifier.ScriptId.ToString(),
                cachePolicy: context.Args.CachePolicy,
                policyContext: context.Args.PolicyContext,
                cancellationToken: context.Args.StopProcessing);
            if (script == null) return false;
            if (script.ScriptCategory is not ScriptCategory.MetascriptDeploymentTarget)
            {
              context.AddLog($"Skipping assignment {assignment.Id} - script {script.Id} is not a Metascript (category {script.ScriptCategory}).");
              return false;
            }
            var res = await context
              .RunScript<bool>(script, 500, context.Args.StopProcessing,
                logPrefix: !string.IsNullOrEmpty(software?.Name) ? $"{software?.Name} - " : "",
                contextParameters: scriptContextParameters);

            if (!res.HadTerminatingException) return res.OutputAsObject;

            context.AddLog($"Failed to resolve {assignment}.\n\n{res.GetErrorString()}");
            return false;
          }
        case TargetType.AzureGroup:
          {
            if (assignment.Target is null) return false;
            if (assignment.TenantId is not { } tenantId) return false;
            var tenant = await context.GetTenantById(tenantId);
            if (tenant == null) return false;
            // don't use the cache if this is an onboarding session
            // because the primary user could've been set after the cache was created
            var ignoreCache = context is ISessionRunContext { OnboardingStage: not null };
            var computersQuery = await _computerAssignmentActions
              .GetComputersInTarget(TargetType.AzureGroup, TargetGroupFilter.All, assignment.Target,
                tenantId: tenantId,
                includeChildTenants: assignment.PropagateToChildTenants,
                allowAccessToParentTenant: assignment.AllowAccessToParentTenant,
                cacheGroupId: context.Args.CacheGroupId,
                cachePolicy: ignoreCache ? null : context.Args.CachePolicy,
                policyContext: ignoreCache ? null : context.Args.PolicyContext,
                cancellationToken: context.Args.StopProcessing);

            var computerIds = computersQuery.Using(q => q.Select(c => c.Id).ToList());
            return context.ComputerId is { } computerId && computerIds.Contains(computerId);
          }
        case TargetType.AllForTenant:
          return DoesAssignmentTargetTenant(assignment, context.TenantId);
        case TargetType.ProviderDeviceGroup:
          {
            if (assignment.Target is null) return false;
            if (assignment.ProviderLinkId is not int providerLinkId) return false;
            if (assignment.ProviderDeviceGroupType is not Guid deviceGroupTypeId) return false;
            if (context.ProviderInfo.All(kvp => kvp.Key.ProviderLinkId != providerLinkId))
              return false;
            var providerInfo = context.ProviderInfo
              .First(kvp => kvp.Key.ProviderLinkId == providerLinkId);
            var agent = providerInfo.Key;
            var providerLink = agent.ProviderLink;
            if (providerLink is null) return false;
            var computerIdsInGroup = await _providerActions
              .GetDevicesInGroup(providerLink, deviceGroupTypeId, assignment.Target,
                policyContext: context.Args.PolicyContext,
                cachePolicy: context.Args.CachePolicy,
                token: context.Args.StopProcessing);
            return computerIdsInGroup.Contains(agent.ExternalAgentId);
          }
        case TargetType.ProviderClientGroup:
          {
            if (assignment.Target is null) return false;
            if (assignment.ProviderLinkId is not int providerLinkId) return false;
            if (assignment.ProviderClientGroupType is not Guid clientGroupTypeId) return false;
            var providerLink = _ctx.GetProviderLink(providerLinkId, includeClients: true);
            if (providerLink == null) return false;
            var externalClientIds = await _providerActions
              .GetClientIdsInGroup(providerLink, clientGroupTypeId, assignment.Target,
                policyContext: context.Args.PolicyContext,
                cachePolicy: context.Args.CachePolicy,
                token: context.Args.StopProcessing);
            var applicableTenantIds = providerLink.ProviderClients
              .Where(c => externalClientIds.Contains(c.ExternalClientId))
              .Select(c => c.LinkedToTenantId);
            return applicableTenantIds.Contains(context.TenantId);
          }
        case TargetType.FilterScript:
          {
            if (context.Args.Computer?.Id is not int computerId) return false;
            var computers = await _computerAssignmentActions
              .GetComputersInTarget(
                TargetType.FilterScript,
                assignment.TargetGroupFilter,
                target: assignment.Target,
                cacheGroupId: context.Args.CacheGroupId,
                cachePolicy: context.Args.CachePolicy,
                policyContext: context.Args.PolicyContext,
                filterScriptComputerId: context.ComputerId);
            return computers.Using(q => q.Any(c => c.Id == computerId));
          }
        case TargetType.TenantFilterScript:
          {
            if (context.Args.Computer?.Id is not int computerId) return false;
            if (context.TenantId != assignment.TenantId && !assignment.PropagateToChildTenants) return false;
            var computers = await _computerAssignmentActions
              .GetComputersInTarget(
                TargetType.TenantFilterScript,
                assignment.TargetGroupFilter,
                target: assignment.Target,
                tenantId: assignment.TenantId,
                includeChildTenants: assignment.PropagateToChildTenants,
                allowAccessToParentTenant: assignment.AllowAccessToParentTenant,
                cacheGroupId: context.Args.CacheGroupId,
                cachePolicy: context.Args.CachePolicy,
                policyContext: context.Args.PolicyContext,
                filterScriptComputerId: context.ComputerId);
            return computers.Using(q => q.Any(c => c.Id == computerId));
          }
      }
    }
    catch (System.Net.Http.HttpRequestException ex)
    {
      context.AddLog($"An [HttpRequestException] occurred determining if computer is in {(assignment.DatabaseType == DatabaseType.Global ? "recommended " : "")}deployment #{assignment.Id}.\n\n{ex.Message ?? ""}\n\n{ex.InnerException?.Message ?? ""}");
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      context.AddLog($"An exception occurred determining if computer is in {(assignment.DatabaseType == DatabaseType.Global ? "recommended " : "")}deployment #{assignment.Id}.\n\n{ex.Message ?? ""}\n\n{ex.InnerException?.Message ?? ""}");
    }
    return false;
  }

  public async Task<(List<TargetAssignment> overridableAssignments, List<TargetAssignment> nextHighestPriorityNonOnboardingOnly)> GetOnboardingOverridableTargetAssignments(
    IRunContext context,
    MaintenanceSpecifier? maintenanceSpecifier = null)
  {
    var (assignments, nextHighestPriorityItems) = await DetermineHighestPriorityAssignments(context,
      maintenanceSpecifier: maintenanceSpecifier,
      alsoResolveNextHighestPriorityNonOnboardingOnlyAssignments: true);

    // if we're only supposed to resolve assignments for the onboarding form, then
    // limit the resolved assignments to those that have some maintenance task parameter
    // values that allow overriding and that are onboarding-only
    return (
      assignments
        .Where(a => (a.TargetAssignment.TargetEnforcement is TargetEnforcement.Onboarding || a.TaskIsOnboardingOnly) &&
          a.TargetAssignment.TaskParameterValues?.Any(t => t.Value is { AllowOverride: true } or { RequiresOverride: true } && !a.HiddenParams.Contains(t.Key)) == true)
        .Select(a => a.TargetAssignment)
        .ToList(),
      nextHighestPriorityItems
        .Select(a => a.TargetAssignment)
        .ToList()
    );
  }

  public async Task<List<TargetAssignment>> GetOnboardingOnlyTaskTargetAssignments(
    IRunContext context,
    MaintenanceSpecifier? maintenanceSpecifier = null)
  {
    var (assignments, _) = await DetermineHighestPriorityAssignments(
      context,
      maintenanceSpecifier: maintenanceSpecifier,
      alsoResolveNextHighestPriorityNonOnboardingOnlyAssignments: false);

    var a = assignments.Where(a => a.TaskIsOnboardingOnly);

    // if automatically onboarding, then skip deployments using a task that is ignored during automatic onboarding
    if (context.Args.MaintenanceOnboardingConfiguration?.AutomaticOnboarding is true)
    {
      a = a.Where(a => !a.IgnoreDuringAutomaticOnboarding);
    }

    return a.Select(a => a.TargetAssignment).ToList();
  }

  public async Task<List<TargetAssignment>> GetOptionalTargetAssignments(IRunContext context)
  {
    var (assignments, _) =
      await DetermineHighestPriorityAssignments(context);

    // if we're only supposed to resolve assignments for the onboarding form, then
    // limit the resolved assignments to those that have some maintenance task parameter
    // values that allow overriding and that are onboarding-only
    return (
      assignments
        .Where(a => a.TargetAssignment.TargetEnforcement == TargetEnforcement.Optional)
        .Select(a => a.TargetAssignment)
        .ToList()
    );
  }

  public async Task<List<TargetAssignment>> GetTargetAssignmentsForTechnicianPod(IRunContext context)
  {
    var (assignments, _) =
      await DetermineHighestPriorityAssignments(context, includeVisibility: true, includeAdhocAssignments: true);

    return (
      assignments
        .Where(a => a.TargetAssignment.Visibility?.TechnicianPod == true)
        .Select(a => a.TargetAssignment)
        .ToList()
    );
  }

  public async Task<List<TargetAssignment>> GetTargetAssignmentsForSelfService(IRunContext context)
  {
    var (assignments, _) =
      await DetermineHighestPriorityAssignments(context, includeVisibility: true, includeAdhocAssignments: true);

    return (
      assignments
        .Where(a => a.TargetAssignment.Visibility?.SelfService == true)
        .Select(a => a.TargetAssignment)
        .ToList()
    );
  }

  public async Task<List<TargetAssignment>> GetIntegrationAgentTargetAssignments(IRunContext context)
  {
    var (assignments, _) = await DetermineHighestPriorityAssignments(
      context,
      alsoResolveNextHighestPriorityNonOnboardingOnlyAssignments: false,
      onlyForAgentIntegrationSoftware: true);

    var specifiers = await context.Args.SoftwareActions.GetIntegrationAgentMaintenanceSpecifiers(
      context.Args.StopProcessing,
      policyContext: context.Args.PolicyContext,
      cachePolicy: context.Args.CachePolicy);

    var integrationAgentAssignments = assignments.Where(a => specifiers.Contains(a.TargetAssignment.MaintenanceSpecifier));

    return integrationAgentAssignments.Select(a => a.TargetAssignment).ToList();
  }

  public async Task<List<TargetAssignment>> GetTargetAssignments(
    IStageRunContext context,
    MaintenanceSpecifier? maintenanceSpecifier = null)
  {
    var msg = "Determine Desired State";
    // append the maintenance item's name to the log message if this is for a specific item
    if (maintenanceSpecifier != null) msg += $" for {maintenanceSpecifier.MaintenanceName}";
    await using var phase = await context.BeginNewStagePhase(msg);
    try
    {
      var (assignments, _) = await DetermineHighestPriorityAssignments(context,
        maintenanceSpecifier: maintenanceSpecifier,
        phase: phase);

      context.AddLog($"Determined desired state from {assignments.Count} deployments");

      return assignments.Select(a => a.TargetAssignment).ToList();
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      context.AddLog($"Exception occurred while processing deployments: {ex.Message}");
      await phase.SetResultState(SessionPhaseStatus.Failed);
      throw;
    }
  }

  private async Task<(
    List<TargetAssignmentWithTaskOnboardingFlag> assignments,
    List<TargetAssignmentWithTaskOnboardingFlag> nextHighestPriorityNonOnboardingOnly
  )> DetermineHighestPriorityAssignments(
    IRunContext context,
    MaintenanceSpecifier? maintenanceSpecifier = null,
    bool alsoResolveNextHighestPriorityNonOnboardingOnlyAssignments = false,
    bool onlyForAgentIntegrationSoftware = false,
    bool includeVisibility = false,
    bool includeAdhocAssignments = false,
    ILogPhaseHandle? phase = null)
  {
    var seenMaintenanceItems = new Dictionary<string, TargetAssignmentWithTaskOnboardingFlag>();
    var nextHighestPriorityNonOnboardingAssignments = new Dictionary<string, TargetAssignmentWithTaskOnboardingFlag>();

    var timer = new Stopwatch();
    timer.Start();

    // fetch all applicable target assignments
    var potentialAssignments = (await _targetAssignmentActions
      .GetSortedTargetAssignments(context.Args.StopProcessing,
        cachePolicy: context.Args.CachePolicy,
        policyContext: context.Args.PolicyContext,
        excludeAssignmentsWithMissingSoftware: true,
        maintenanceSpecifier: maintenanceSpecifier,
        tenantId: context.TenantId,
        targetCategory: context.TargetCategory,
        excludeAssignmentsForDisabledProviderLinks: true,
        onlyForAgentIntegrationSoftware: onlyForAgentIntegrationSoftware,
        includeVisibility: includeVisibility
      )).ToList();

    var index = 1;
    var potentialCount = potentialAssignments.Count;
    context.AddLog(
      $"Checking {potentialCount} deployments to see if any apply to this computer. Fetched in {timer.ElapsedMilliseconds}ms.");

    var assignmentTimeDict = new Dictionary<string, long>();
    foreach (var assignmentWithFlag in potentialAssignments)
    {
      try
      {
        timer.Restart();
        var assignment = assignmentWithFlag.TargetAssignment;
        context.Args.StopProcessing.ThrowIfCancellationRequested();

        // update progress log
        if (phase is not null)
        {
          await phase.SetProgressPercentComplete(index++, potentialCount);
        }

        var maintKey = assignment.MaintenanceSpecifier.ToString();

        // if we've already determined a non-onboarding-only assignment that targets this computer,
        // then just move on
        if (seenMaintenanceItems.TryGetValue(maintKey, out var a) && (
              !alsoResolveNextHighestPriorityNonOnboardingOnlyAssignments || // we've seen an assignment and we don't care about also resolving the next-highest-priority non-onboarding-only assignment
              !(a.TargetAssignment.TargetEnforcement is TargetEnforcement.Onboarding ||
                a.TaskIsOnboardingOnly) || // we've seen a non-onboarding only assignment already
              nextHighestPriorityNonOnboardingAssignments
                .ContainsKey(
                  maintKey) || // we've seen an onboarding-only assignment already, but we've also already seen a non-onboarding-only assignment
              assignment.TargetEnforcement is TargetEnforcement.Onboarding ||
              assignmentWithFlag
                .TaskIsOnboardingOnly // we've seen an onboarding-only assignment and we we've not seen a non-onboarding-only assignment, but the assignment currently under consideration onboarding-only
            )
           )
        {
          continue;
        }

        // if the assignment is adhoc, then we don't want to pull it for maintenance
        if (assignment.TargetEnforcement is TargetEnforcement.Adhoc && !includeAdhocAssignments)
          continue;

        // if this is an optional deployment, check if the optional deployment feature is enabled and if the deployment is approved
        if (assignmentWithFlag.TargetAssignment.TargetEnforcement is TargetEnforcement.Optional)
        {
          var isDeploymentApproved = await IsOptionalAssignmentApprovedForTarget(context, assignment);
          // if the optional deployment is not approved, skip the optional deployment
          if (!isDeploymentApproved.Success)
          {
            context.AddLog(isDeploymentApproved.Reason ?? $"Failed to resolve optional deployment #{assignment.Id}, so it will be skipped.");
            continue;
          }
        }

        switch (context.TargetCategory)
        {
          case TargetCategory.Computer when !await IsComputerInAssignment(context, assignmentWithFlag):
          case TargetCategory.Tenant when !await IsTenantInAssignment(context, assignmentWithFlag):
          case TargetCategory.Person when !await IsPersonInAssignment(context, assignmentWithFlag):
            continue;
        }

        // at this point, if `a` is present, then we're looking for the next-highest-priority non-onboarding-only assignment
        if (a is null)
        {
          seenMaintenanceItems.Add(maintKey, assignmentWithFlag);
        }
        else
        {
          // found the next-highest-priority non-onboarding-only assignment for this maintenance item
          context.AddLog(
            $"Non-onboarding-only {(assignment.DatabaseType == DatabaseType.Global ? "Recommended " : "")} Deployment #{assignment.Id} would apply if the computer is not onboarding.");
          nextHighestPriorityNonOnboardingAssignments.Add(maintKey, assignmentWithFlag);
        }

        if (maintenanceSpecifier != null && (
              !alsoResolveNextHighestPriorityNonOnboardingOnlyAssignments || // we don't care about finding the next-highest-priority non-onboarding-only assignment
              a is not null // we've found the next-highest-priority non-onboarding-only assignment
            ))
        {
          // no need to continue, we found the assignment we're going to use, set progress to 100%.
          if (phase is not null)
          {
            await phase.SetProgressPercentComplete(potentialCount, potentialCount);
          }

          break;
        }
      }
      finally
      {
        var time = timer.ElapsedMilliseconds;
        var key =
          $"{assignmentWithFlag.TargetAssignment.Id}-{assignmentWithFlag.TargetAssignment.DatabaseType}";
        assignmentTimeDict.TryAdd(key, time);
      }
    }

    // log the top 10 assignments that took the longest to resolve
    var top10 = assignmentTimeDict
      .OrderByDescending(a => a.Value)
      .Take(10)
      .Select(a => "Deployment #" + a.Key + " took " + a.Value + "ms")
      .ToList();

    var top10Msg = string.Join("\n", top10);
    logger.LogTrace(
      "Target Assignment Resolution: Top 10 longest assignments to resolve:\n{TopTen}",
      top10Msg);


    return (seenMaintenanceItems.Values.ToList(), nextHighestPriorityNonOnboardingAssignments.Values.ToList());
  }

  private async Task<bool> IsTenantInAssignment(
    IRunContext context,
    TargetAssignmentWithTaskOnboardingFlag extendedAssignment)
  {
    var assignment = extendedAssignment.TargetAssignment;
    if (context.TargetCategory is not TargetCategory.Tenant ||
        extendedAssignment.TargetAssignment.TargetCategory is not TargetCategory.Tenant)
      return false;

    switch (assignment.TargetType)
    {
      case TargetType.SpecificTenant:
        return DoesAssignmentTargetTenant(assignment, context.TenantId);
      case TargetType.AllTenants:
        var isDeploymentCrossTenant = TargetAssignmentHelpers.GetTargetScopeForTargetType(
          context.Args.ProviderActions,
          assignment.TargetType,
          providerDeviceGroupType: assignment.ProviderDeviceGroupType) == TargetScope.CrossTenant;

        if (isDeploymentCrossTenant &&
            context.TenantPreferences?.ExcludeFromCrossTenantDeploymentsAndSchedules is true)
        {
          context.AddLog(
            $"Deployment #{assignment.Id} excluded. Cross-tenant deployments have been " +
            $"excluded for {context.TenantName}.");
          return false;
        }

        return true;
      case TargetType.CloudTag:
        var tagId = Convert.ToInt32(assignment.Target);
        return context.Args.Tenant.Tags.Exists(b => b.Id == tagId);
      case TargetType.CloudProviderClientGroup:
        if (assignment.ProviderLinkId is not int providerLinkId) return false;
        if (assignment.ProviderClientGroupType is not Guid clientGroupTypeId) return false;
        if (assignment.Target is null) return false;
        var providerLink = _ctx.GetProviderLink(providerLinkId, includeClients: true);
        if (providerLink == null) return false;
        var externalClientIds = await _providerActions
          .GetClientIdsInGroup(providerLink,
            clientGroupTypeId,
            assignment.Target,
            policyContext: context.Args.PolicyContext,
            cachePolicy: context.Args.CachePolicy,
            token: context.Args.StopProcessing);
        var applicableTenantIds = providerLink.ProviderClients
          .Where(a => externalClientIds.Contains(a.ExternalClientId))
          .Select(a => a.LinkedToTenantId);
        return applicableTenantIds.Contains(context.TenantId);
      default:
        throw new NotSupportedException(
          $"Deployments for tenants must target a specific tenant or all tenants. {assignment.TargetType} is not valid.");
    }
  }

  private async Task<bool> IsPersonInAssignment(
    IRunContext context,
    TargetAssignmentWithTaskOnboardingFlag extendedAssignment)
  {
    var assignment = extendedAssignment.TargetAssignment;
    if (context.TargetCategory is not TargetCategory.Person ||
        assignment.TargetCategory is not TargetCategory.Person ||
        !context.PersonId.HasValue)
      return false;

    var isDeploymentCrossTenant = TargetAssignmentHelpers.GetTargetScopeForTargetType(
      context.Args.ProviderActions,
      assignment.TargetType,
      providerDeviceGroupType: assignment.ProviderDeviceGroupType) == TargetScope.CrossTenant;

    if (isDeploymentCrossTenant &&
        context.TenantPreferences?.ExcludeFromCrossTenantDeploymentsAndSchedules is true)
    {
      context.AddLog(
        $"Deployment #{assignment.Id} excluded. Cross-tenant deployments have been " +
        $"excluded for {context.TenantName}.");
      return false;
    }

    switch (assignment.TargetType)
    {
      case TargetType.Person:
        if (string.IsNullOrEmpty(assignment.Target)) return false;
        return Convert.ToInt32(assignment.Target) == context.PersonId;
      case TargetType.All:
        return true;
      case TargetType.AllForTenant:
        return DoesAssignmentTargetTenant(assignment, context.TenantId);
      case TargetType.Tag:
        var tagId = Convert.ToInt32(assignment.Target);
        return context.Args.Person?.Tags.Any(b => b.Id == tagId) == true;
      case TargetType.TenantTag:
        if (!DoesAssignmentTargetTenant(assignment, context.TenantId)) return false;
        var tenantTagId = Convert.ToInt32(assignment.Target);
        return context.Args.Person?.Tags.Any(b => b.Id == tenantTagId) == true;
      case TargetType.AzureGroup:
        if (string.IsNullOrEmpty(assignment.Target)) return false;
        if (assignment.TenantId is null) return false;
        var tenant = await context.GetTenantById(assignment.TenantId.Value);
        if (tenant is null) return false;
        var personIds = await _azureActions.GetPersonIdsInAzureGroupAtTenant(
          tenant,
          assignment.Target,
          cancellationToken: context.Args.StopProcessing);
        return personIds.Contains(context.PersonId.Value);
      default:
        throw new NotSupportedException(
          $"Deployments for tenants must target a specific tenant or all tenants. {assignment.TargetType} is not valid.");
    }
  }

  public async Task<ImmyOperationResult> IsOptionalAssignmentApprovedForTarget(IRunContext runContext, TargetAssignment targetAssignment)
  {
    var optionalDeploymentFeatureEnabled = runContext.Args.FeatureManager.IsEnabled(FeatureEnum.OptionalDeploymentFeature);
    if (!optionalDeploymentFeatureEnabled)
    {
      return new ImmyOperationResult(false, $"Deployment #{targetAssignment.Id} has been skipped as it's target enforcement is set to optional, but the optional deployment feature is not enabled.");
    }

    // this will need to be updated to account for Person Tasks once fully implemented
    TargetType approvalTargetType = runContext.TargetCategory switch
    {
      TargetCategory.Tenant => TargetType.SpecificTenant,
      TargetCategory.Person => TargetType.Person,
      TargetCategory.Computer => TargetType.Computer,
      _ => throw new NotSupportedException($"TargetCategory {runContext.TargetCategory} is not supported.")
    };

    var approvalTarget = approvalTargetType switch
    {
      TargetType.SpecificTenant => runContext.TenantId.ToString(),
      TargetType.Computer => runContext.Args.Computer?.Id.ToString(),
      _ => throw new NotSupportedException($"TargetType {approvalTargetType} is not supported.")
    };

    if(approvalTarget is null)
      return new ImmyOperationResult(false, "Approval target could not be determined.");

    var matchingApproval = await runContext.Args.TargetAssignmentActions.GetApprovalStatusForTargetAssignment(targetAssignment.Id, approvalTarget, approvalTargetType, targetAssignment.MaintenanceType, runContext.Args.StopProcessing);

    if (matchingApproval is not null)
    {
      var resultMsg = matchingApproval switch
      {
        TargetAssignmentApprovalStatus.Pending => $"Deployment #{targetAssignment.Id} excluded. It is optional and has not been opted into.",
        TargetAssignmentApprovalStatus.Denied => $"Deployment #{targetAssignment.Id} excluded. It is optional and has been opted out of.",
        _ => null,
      };
      var isApproved = matchingApproval is TargetAssignmentApprovalStatus.Approved;
      return new ImmyOperationResult(isApproved, resultMsg);
    }

    // if approval is not found, we create a new OptionalTargetAssignmentApproval in the pending state
    var newTargetAssignmentApproval = new OptionalTargetAssignmentApprovalPayload(
      targetAssignment.Id,
      TargetAssignmentApprovalStatus.Pending,
      approvalTargetType,
      approvalTarget,
      targetAssignment.MaintenanceType
    );
    await _ctx.CreateOptionalTargetAssignmentApproval(newTargetAssignmentApproval);

    return new ImmyOperationResult(false, $"Deployment #{targetAssignment.Id} excluded. It is optional and has not been opted into.");
  }

  public async Task<List<(TargetAssignment, MaintenanceAction)>> GetMaintenanceActionsFromAssignments(
    IStageRunContext context,
    List<TargetAssignment> assignments,
    bool useExistingActionIfPresent = false)
  {
    await using var phase = await context.BeginNewStagePhase("Create Maintenance Actions");
    try
    {
      var ret = new List<(TargetAssignment, MaintenanceAction)>(assignments.Count);

      // order the assignments by priority
      List<TargetAssignment> orderedAssignments;

      var priorities = await context.GetMaintenancePriorities();

      if (priorities.Count != 0)
      {
        var maxPriority = priorities.Max(a => a.SortOrder);
        bool isOnboardingStage = context.Stage?.Type == SessionStageType.Onboarding;

        if (isOnboardingStage)
        {
          var originalOrder = assignments.Select((a, idx) => new { a, idx })
            .ToDictionary(x => x.a, x => x.idx);

          var onboardingOnlyFlags = await GetOnboardingOnlyFlagsAsync(assignments, context);

          orderedAssignments = [.. assignments
            .OrderByDescending(a => onboardingOnlyFlags[a])
            .ThenBy(a => onboardingOnlyFlags[a] ? originalOrder[a] : int.MaxValue)
            .ThenByDescending(a => a.IsCore)
            .ThenBy(a =>
            {
              var p = priorities.Find(p =>
                p.MaintenanceIdentifier == a.MaintenanceIdentifier &&
                p.MaintenanceType == a.MaintenanceType);
              return p?.Location ?? MaintenanceItemOrderLocation.DontCare;
            })
            .ThenBy(a =>
            {
              var p = priorities.Find(p =>
                p.MaintenanceIdentifier == a.MaintenanceIdentifier &&
                p.MaintenanceType == a.MaintenanceType);
              return p?.SortOrder ?? maxPriority;
            })];
        }
        else
        {
          orderedAssignments = assignments
          .OrderByDescending(a => a.IsCore)
          .ThenBy(a =>
          {
            var p = priorities.Find(p =>
              p.MaintenanceIdentifier == a.MaintenanceIdentifier &&
              p.MaintenanceType == a.MaintenanceType);
            return p?.Location ?? MaintenanceItemOrderLocation.DontCare;
          })
          .ThenBy(a =>
          {
            var p = priorities.Find(p =>
              p.MaintenanceIdentifier == a.MaintenanceIdentifier &&
              p.MaintenanceType == a.MaintenanceType);
            return p?.SortOrder ?? maxPriority;
          }).ToList();
        }
      }
      else
      {
        orderedAssignments = assignments;
      }

      var index = 1;
      var assignmentCount = assignments.Count;

      var populatedAssignments = (await context.Args.TargetPopulator.Populate(
        orderedAssignments.Select(a => new PopulatedTargetAssignment(a)).AsQueryable(),
        context.Args.StopProcessing));

      foreach (var assignment in orderedAssignments)
      {
        // if we're using existing actions, check to see if there's already an action for this assignment.
        if (useExistingActionIfPresent)
        {
          // match the action on the maintenance identifier and type and assignment id if it is present
          var existingAction = context.Session.MaintenanceActions.FirstOrDefault(a =>
            a.MaintenanceIdentifier == assignment.MaintenanceIdentifier &&
            a.MaintenanceType == assignment.MaintenanceType && (a.AssignmentId == null || a.AssignmentId == 0 || (assignment.Id != 0 && a.AssignmentId == assignment.Id)));
          if (existingAction is not null)
          {
            ret.Add((assignment, existingAction));
            continue;
          }
        }

        var populated = populatedAssignments.FirstOrDefault(a => a.TargetAssignment == assignment);

        string? assignmentLogText = null;
        if (assignment.Id != 0)
        {
          assignmentLogText = $"{(assignment.DatabaseType == DatabaseType.Global ? "Recommended " : "")}Deployment #{assignment.Id}";
        }
        await phase.SetProgressPercentComplete(index++, assignmentCount);

        MaintenanceAction action;
        if (assignment.SoftwareSpecifier != null)
        {
          if (assignment.DesiredSoftwareState is not { } desiredState)
          {
            context.AddLog($"DesiredSoftwareState is null, unable to evaluate assignment. {assignmentLogText}");
            continue;
          }
          var software = await context.Args.SoftwareActions
            .GetSoftware(
              assignment.SoftwareSpecifier.SoftwareType,
              assignment.SoftwareSpecifier.SoftwareIdentifier,
              policyContext: context.Args.PolicyContext,
              cachePolicy: context.Args.CachePolicy,
              token: context.Args.StopProcessing);

          if (software == null)
          {
            context.AddLog($"{assignmentLogText} specifies a software that no longer exists. {assignment.SoftwareSpecifier}");
            continue;
          }


          // logic to skip assignments for integration agents
          if (assignment.DesiredSoftwareState is not DesiredSoftwareState.NotPresent
            && software.AgentIntegrationTypeId is not null
            && context.AgentUpdatesStage is not null
            // if we are in the resolution stage and the session has an agent updates state
            && context.Stage?.Type is not SessionStageType.AgentUpdates)
          {
            // only skip if the provider specifies that it should resolve during the agent updates stage
            var provider = context.ProviderInfo.FirstOrDefault(a =>
              a.Key.ProviderLink?.ProviderTypeId == software.AgentIntegrationTypeId).Value;
            if (provider is ISupportsAgentUpdatesStageResolution resolutionProvider &&
                resolutionProvider.DoesResolveDuringAgentUpdatesStage())
            {
              continue;
            }
          }

          string? policyDescription = null;
          if (assignment.TargetType is not 0)
          {
            policyDescription = PolicyDescriptionGenerator.Generate(
              populated?.TargetText ?? String.Empty,
              software.Name,
              desiredState,
              null,
              assignment.SoftwareSemanticVersion,
              assignment.TargetType,
              assignment.TargetGroupFilter,
              assignment.ProviderDeviceGroupType,
              assignment.ProviderClientGroupType,
              assignment.TenantId is not null ? context.TenantName : string.Empty,
              assignment.PropagateToChildTenants,
              assignment.AllowAccessToParentTenant);

            context.AddLog(policyDescription);
          }

          // if the deployment does not require a specific version then do not include it even if it is present on the deployment
          var requiresSpecificVersion = DoesDesiredSoftwareStateRequireASpecificVersion(desiredState);
          var desiredSemanticVersion = requiresSpecificVersion ? assignment.SoftwareSemanticVersion : null;

          action = await _maintenanceActionInitializer.BeginBuildingSoftwareMaintenanceAction(context,
            software,
            desiredState,
            desiredSemanticVersion,
            targetAssignmentId: assignment.Id,
            targetAssignmentType: assignment.DatabaseType,
            targetAssignmentSoftwareProviderType: assignment.SoftwareProviderType,
            policyDescription: policyDescription);
        }
        else
        {
          if (assignment.MaintenanceTaskMode is not { } mode)
          {
            context.AddLog($"MaintenanceTaskMode is null, unable to evaluate assignment. {assignmentLogText}");
            continue;
          }
          if (int.TryParse(assignment.MaintenanceIdentifier, out int maintenanceId))
          {
            var maintenanceTask = await _maintenanceTaskActions.GetMaintenanceTask(
              assignment.MaintenanceType,
              maintenanceId,
              context.Args.CachePolicy,
              context.Args.PolicyContext,
              context.Args.StopProcessing);

            if (maintenanceTask is null)
            {
              context.AddLog($"{assignmentLogText} specifies a maintenance task that no longer exists. {assignment.MaintenanceType}-{assignment.MaintenanceIdentifier}");
              continue;
            }

            // only consider onboarding tasks during onboarding stage unless task was reran
            var isRerunningAction = context.Args.RerunningAction;
            if (!isRerunningAction && maintenanceTask.OnboardingOnly && context.Stage?.Type is not SessionStageType.Onboarding) continue;

            string? policyDescription = null;
            if (assignment.TargetType is not 0)
            {
              policyDescription = PolicyDescriptionGenerator.Generate(
                populated?.TargetText ?? String.Empty,
                maintenanceTask.Name,
                null,
                mode,
                null,
                assignment.TargetType,
                assignment.TargetGroupFilter,
                assignment.ProviderDeviceGroupType,
                assignment.ProviderClientGroupType,
                assignment.TenantId is not null ? context.TenantName : string.Empty,
                assignment.PropagateToChildTenants,
                assignment.AllowAccessToParentTenant);

              context.AddLog(policyDescription);
            }

            action = await _maintenanceActionInitializer.BeginBuildingTaskMaintenanceAction(context,
              maintenanceTask,
              maintenanceTaskMode: mode,
              targetAssignmentId: assignment.Id,
              targetAssignmentType: assignment.DatabaseType,
              policyDescription: policyDescription);
          }
          else
          {
            context.AddLog($"Maintenance Identifier \"{assignment.MaintenanceIdentifier}\" failed to be parsed as integer and cannot be applied.");
            continue;
          }
        }

        ret.Add((assignment, action));
      }
      return ret;
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      context.AddLog($"Exception occurred while creating maintenance actions: {ex.Message}");
      await phase.SetResultState(SessionPhaseStatus.Failed);
      throw;
    }
  }

  private static bool DoesDesiredSoftwareStateRequireASpecificVersion(DesiredSoftwareState desiredSoftwareState)
  {
    return desiredSoftwareState switch
    {
      DesiredSoftwareState.ThisVersion => true,
      DesiredSoftwareState.NotPresent => false,
      DesiredSoftwareState.AnyVersion => false,
      DesiredSoftwareState.NewerOrEqualVersion => true,
      DesiredSoftwareState.OlderOrEqualVersion => true,
      DesiredSoftwareState.LatestVersion => false,
      DesiredSoftwareState.NoAction => false,
      DesiredSoftwareState.UpdateIfFound => false,
      _ => false,
    };
  }

  /// <summary>
  /// Determines whether each assignment is marked as OnboardingOnly, performing the lookups in parallel for performance.
  /// </summary>
  private async Task<Dictionary<TargetAssignment, bool>> GetOnboardingOnlyFlagsAsync(
    List<TargetAssignment> assignments,
    IStageRunContext context)
  {
    var lookupTasks = assignments.Select(async assignment =>
    {
      bool isOnboardingOnly = false;

      if (assignment.MaintenanceType is not (MaintenanceType.LocalMaintenanceTask
            or MaintenanceType.GlobalMaintenanceTask)
          || !int.TryParse(assignment.MaintenanceIdentifier, out var maintenanceId))
      {
        return (assignment, isOnboardingOnly);
      }

      var maintenanceTask = await _maintenanceTaskActions.GetMaintenanceTask(
        assignment.MaintenanceType,
        maintenanceId,
        context.Args.CachePolicy,
        context.Args.PolicyContext,
        context.Args.StopProcessing);

      isOnboardingOnly = maintenanceTask?.OnboardingOnly ?? false;

      return (assignment, isOnboardingOnly);
    });

    var results = await Task.WhenAll(lookupTasks);
    return results.ToDictionary(r => r.assignment, r => r.isOnboardingOnly);
  }

  private bool DoesAssignmentTargetTenant(
    TargetAssignment assignment,
    int tenantId)
  {
    if (assignment.TenantId == tenantId) return true;

    if (assignment.TenantId is not { } assignmentTenantId) return false;
    if (assignment.PropagateToChildTenants)
    {
      return _ctx.IsTenantDescendentOf(descendentTenantId: tenantId,
        ancestorTenantId: assignmentTenantId);
    }

    return false;
  }
}
