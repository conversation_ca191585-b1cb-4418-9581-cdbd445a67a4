using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.MaintenanceSessionExtensions;
using Immybot.Backend.Application.DbContextExtensions.PersonExtensions;
using Immybot.Backend.Application.DbContextExtensions.UserExtensions;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Lib.Policies;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Polly;
using Polly.Registry;

namespace Immybot.Backend.Application.Maintenance;

internal class RunContextFactory(
  IRunContextArgs _args,
  Func<ImmybotDbContext> _ctxFactory,
  IImmyCancellationManager _immyCancellationManager,
  IReadOnlyPolicyRegistry<string> _policyRegistry,
  IMetascriptMessageHandler _metascriptMessageHandler) : IRunContextFactory
{
  public IMetascriptMessageHandler MessageHandler { get; private set; } = _metascriptMessageHandler;

  private const string SessionNotFoundExceptionText = "No session was found with id {0}";
  private const string ComputerNotFoundExceptionText = "No computer was found with id {0}";
  private const string TenantNotFoundExceptionText = "No tenant was found with id {0}";

  private (IAsyncPolicy?, Context?) GetCacheInfo(SessionJobArgs immybotParams)
  {
    // add cache
    IAsyncPolicy? cachePolicy = null;
    Context? policyContext = null;
    if (immybotParams.ScheduledId != null && immybotParams.CacheGroupId != null)
    {
      (cachePolicy, policyContext) = _policyRegistry
        .GetScheduleCachePolicyAndContext(immybotParams.CacheGroupId.Value);
    }
    else if (immybotParams.CacheGroupId != null)
    {
      (cachePolicy, policyContext) = _policyRegistry
        .GetDeploymentGroupCachePolicyAndContext(immybotParams.CacheGroupId.Value);
    }
    return (cachePolicy, policyContext);
  }

  public async Task<IRunContext> GeneratePersonRunContext(
    AuthUserDto? manuallyTriggeredBy,
    int personId,
    CancellationToken cancellationToken,
    Guid? cacheGroupId = null)
  {
    await using var dbContext = _ctxFactory();
    var person = dbContext.GetPersonById(personId);
    if (person is null)
      throw new RunContextFactoryException(
        $"Could not generate a run context because person with id {personId} was not found.");

    var tenant = dbContext.GetTenantById(person.TenantId, includeAzData: true);
    if (tenant is null)
      throw new RunContextFactoryException(
        $"Could not generate a run context because tenant with id {person.TenantId} was not found.");

    return await GenerateOneOffRunContextAsync(
      tenant: tenant,
      computer: null,
      person: person,
      software: null,
      cacheGroupId,
      cancellationToken,
      manuallyTriggeredBy: manuallyTriggeredBy);
  }

  public async Task<IRunContext> GenerateComputerOneOffRunContext(
    int computerId,
    CancellationToken cancellationToken,
    Guid? cacheGroupId = null,
    AuthUserDto? manuallyTriggeredBy = null)
  {
    var computer = await GetComputer(computerId, cancellationToken);
    var tenant = computer.Tenant;
    if (tenant is null) throw new InvalidOperationException("Tenant is not loaded for the computer.");

    // NB: Certain caches will be disabled since cacheGroupId is provided
    return await GenerateOneOffRunContextAsync(tenant,
      computer,
      person: null,
      software: null,
      cacheGroupId,
      cancellationToken,
      manuallyTriggeredBy: manuallyTriggeredBy);
  }
  public async Task<IRunContext> GenerateTenantRunContext(
    AuthUserDto? manuallyTriggeredBy,
    int tenantId,
    CancellationToken cancellationToken,
    Guid? cacheGroupId = null)
  {
    await using var dbContext = _ctxFactory();
    var tenant = dbContext.GetTenantById(tenantId, includeAzData: true);
    if (tenant is null) throw new InvalidOperationException("Tenant not found");

    return await GenerateOneOffRunContextAsync(
      tenant,
      computer: null,
      person: null,
      software: null,
      cacheGroupId,
      cancellationToken,
      manuallyTriggeredBy: manuallyTriggeredBy);
  }

  public async Task<ISessionRunContext> GenerateSessionRunContext(
    int sessionId,
    int? actionIdToRerun,
    CancellationToken contextShutdownToken,
    bool appliedOnConnect = false)
  {
    Computer? computer = null;
    Person? person = null;
    await using var dbContext = _ctxFactory();
    MaintenanceSession session = await GetSession(sessionId);
    Tenant tenant;
    if (session.ComputerId != null)
    {
      computer = await GetComputer(session.ComputerId.Value, contextShutdownToken);
      tenant = computer.Tenant ?? throw new InvalidOperationException("Tenant is not loaded for the computer.");
    }
    else if (session.PersonId is not null)
    {
      // running for person session
      person = dbContext.GetPersonById(session.PersonId.Value);
      if (person is null) throw new EntityNotFoundException($"Person {session.PersonId.Value} was not found.");
      tenant = GetTenant(person.TenantId);
    }
    else
    {
      // running for tenant session
      if (session.TenantId is null) throw new EntityNotFoundException("Session did not have a tenant id.");
      tenant = GetTenant(session.TenantId.Value);
    }

    // if an actionId is present then we are re-running the session for a particular action
    if (actionIdToRerun.HasValue)
    {
      var action = session.MaintenanceActions.FirstOrDefault(a => a.Id == actionIdToRerun);
      if (action is null) throw new InvalidOperationException($"Cannot re-run action with id #{actionIdToRerun} because it does not exist in session #{sessionId}");
      session.JobArgs.ActionIdToRerun = actionIdToRerun;
      session.JobArgs.RerunningAction = true;
      session.JobArgs.DeploymentId = action.AssignmentId;
      session.JobArgs.DeploymentType = action.AssignmentType;

      if (session.FullMaintenance)
      {
        // if the session was from a schedule or full maintenance session, then we should wipe out the session args maintenance item
        // since it could've been set by another re-run of an action
        session.JobArgs.MaintenanceItem = null;
      }

      session.JobArgs.MaintenanceItem = RerunMaintenanceActionCmd.BuildNewMaintenanceItemForActionRerun(session.JobArgs, action);
      session.JobArgs.CacheGroupId = Guid.NewGuid();
      session.JobArgs.RebootPreference = RebootPreference.Suppress;
      session.JobArgs.PromptTimeoutAction = PromptTimeoutAction.Suppress;
    }

    session.JobArgs.AppliedOnConnect = appliedOnConnect;

    return await GetSessionRunContext(session, session.JobArgs, computer, person, tenant, contextShutdownToken);
  }

  private async Task<ISessionRunContext> GetSessionRunContext(
    MaintenanceSession session,
    SessionJobArgs args,
    Computer? computer,
    Person? person,
    Tenant tenant,
    CancellationToken cancellationToken)
  {
    var manuallyTriggeredBy = await GetManuallyTriggeredBy(args.ManuallyTriggeredByUserId);

    var (cachePolicy, policyContext) = GetCacheInfo(args);

    var sessionCancellationToken = _immyCancellationManager
      .GetOrCreateSessionCancellationToken(session.Id);
    var cts = CancellationTokenSource
      .CreateLinkedTokenSource(sessionCancellationToken, cancellationToken);

    PopulateRunContextArgsFromSessionJobArgsForSessionRunContext(args,
      _args,
      computer,
      person,
      tenant,
      manuallyTriggeredBy,
      cachePolicy,
      policyContext,
      cts.Token);

    var context = new SessionRunContext(session, _args);
    context.OnDispose += () =>
    {
      cts.Dispose();
    };
    await context.Initialize();
    return context;
  }

  /// <summary>
  /// To be used for running one-off scripts against a computer or tenant
  /// </summary>
  private async Task<IRunContext> GenerateOneOffRunContextAsync(
    Tenant tenant,
    Computer? computer,
    Person? person,
    Software? software,
    Guid? cacheGroupId,
    CancellationToken cancellationToken,
    AuthUserDto? manuallyTriggeredBy = null)
  {
    cacheGroupId ??= Guid.NewGuid();
    var (cachePolicy, policyContext) = _policyRegistry
      .GetDeploymentGroupCachePolicyAndContext(cacheGroupId.Value);

    _args.Computer = computer;
    _args.Person = person;
    _args.Tenant = tenant;
    _args.Priority = Priority.Now;
    _args.CacheGroupId = cacheGroupId;
    _args.PolicyContext = policyContext;
    _args.CachePolicy = cachePolicy;
    _args.MaintenanceEmailConfiguration = new MaintenanceEmailConfiguration();
    _args.MaintenanceOnboardingConfiguration = new MaintenanceOnboardingConfiguration();
    _args.StopProcessing = cancellationToken;
    _args.ManuallyTriggeredBy = manuallyTriggeredBy;

    var context = new RunContext(_args);

    await context.Initialize();
    return context;
  }

  private async Task<MaintenanceSession> GetSession(int sessionId)
  {
    await using var dbContext = _ctxFactory();
    var session = await dbContext.GetMaintenanceSessionByIdAsync(sessionId,
      includeStages: true,
      includeActions: true,
      includeTenant: true,
      // Don't allow 'AsNoTracking' because we need actions in action dependencies to
      // be referentially equal to actions in the session
      asTracking: true);
    if (session is null)
      throw new RunContextFactoryException(string.Format(SessionNotFoundExceptionText, sessionId));
    return session;
  }

  private async Task<Computer> GetComputer(int computerId, CancellationToken token)
  {
    await using var dbContext = _ctxFactory();
    var computer = dbContext.GetComputerById(computerId,
      includeAgents: true,
      includeTenant: true,
      includeTags: true,
      includePrimaryPerson: true,
      includeAdditionalPersons: true);

    if (computer is null)
      throw new ComputerNotFoundException(string.Format(ComputerNotFoundExceptionText, computerId));

    return computer;
  }
  private Tenant GetTenant(int tenantId)
  {
    using var dbContext = _ctxFactory();
    var tenant = dbContext.GetTenantById(tenantId, includeTags: true, includeAzData: true);
    if (tenant is null)
      throw new RunContextFactoryException(string.Format(TenantNotFoundExceptionText, tenantId));
    return tenant;
  }

  private async Task<AuthUserDto?> GetManuallyTriggeredBy(int? userId)
  {
    if (!userId.HasValue) return null;

    await using var dbContext = _ctxFactory();
    return await dbContext.GetAuthUserById(userId.Value, DateTime.UtcNow);
  }

  public static void PopulateRunContextArgsFromSessionJobArgsForSessionRunContext(
    SessionJobArgs sessionJobArgs,
    IRunContextArgs runContextArgs,
    Computer? computer,
    Person? person,
    Tenant tenant,
    AuthUserDto? manuallyTriggeredBy,
    IAsyncPolicy? cachePolicy,
    Context? policyContext,
    CancellationToken token)
  {
    runContextArgs.Computer = computer;
    runContextArgs.Person = person;
    runContextArgs.Tenant = tenant;
    runContextArgs.Priority = string.IsNullOrEmpty(sessionJobArgs.MaintenanceSchedulingConfiguration?.MaintenanceTime) ? Priority.Now : Priority.Scheduled;
    runContextArgs.ScheduleId = sessionJobArgs.ScheduledId;
    runContextArgs.HasSessionFeatureBeenTracked = sessionJobArgs.HasSessionFeatureBeenTracked;
    runContextArgs.RerunFromScheduleId = sessionJobArgs.RerunFromScheduleId;
    runContextArgs.CacheGroupId = sessionJobArgs.CacheGroupId;
    runContextArgs.ManuallyTriggeredBy = manuallyTriggeredBy;
    runContextArgs.InstallWindowsUpdates = sessionJobArgs.InstallWindowsUpdates;
    runContextArgs.RebootPreference = sessionJobArgs.RebootPreference;
    runContextArgs.PromptTimeoutAction = sessionJobArgs.PromptTimeoutAction;
    runContextArgs.AutoConsentToReboots = sessionJobArgs.AutoConsentToReboots;
    runContextArgs.PromptTimeout = TimeSpan.FromMinutes(sessionJobArgs.PromptTimeoutMinutes);
    runContextArgs.CacheOnly = sessionJobArgs.CacheOnly;
    runContextArgs.SpecifiedAssignmentId = sessionJobArgs.DeploymentId;
    runContextArgs.SpecifiedAssignmentType = sessionJobArgs.DeploymentType;
    runContextArgs.CachePolicy = cachePolicy;
    runContextArgs.PolicyContext = policyContext;
    runContextArgs.RerunningAction = sessionJobArgs.RerunningAction;
    runContextArgs.MaintenanceItem = sessionJobArgs.MaintenanceItem;
    runContextArgs.MaintenanceEmailConfiguration = sessionJobArgs.MaintenanceEmailConfiguration ?? new MaintenanceEmailConfiguration();
    runContextArgs.MaintenanceOnboardingConfiguration = sessionJobArgs.MaintenanceOnboardingConfiguration ?? new MaintenanceOnboardingConfiguration();
    runContextArgs.MaintenanceAgentUpdatesConfiguration = sessionJobArgs.MaintenanceAgentUpdatesConfiguration ?? new MaintenanceAgentUpdatesConfiguration();
    runContextArgs.MaintenanceSchedulingConfiguration = sessionJobArgs.MaintenanceSchedulingConfiguration ?? new MaintenanceSchedulingConfiguration();
    runContextArgs.Repair = sessionJobArgs.Repair;
    runContextArgs.RunInventoryInDetection = sessionJobArgs.RunInventoryInDetection;
    runContextArgs.StopProcessing = token;
    runContextArgs.OfflineBehavior = sessionJobArgs.OfflineBehavior;
    runContextArgs.SuppressRebootsDuringBusinessHours = sessionJobArgs.SuppressRebootsDuringBusinessHours;
    runContextArgs.AppliedOnConnect = sessionJobArgs.AppliedOnConnect;
    runContextArgs.ManuallyResumedByPersonId = sessionJobArgs.ManuallyResumedByPersonId;
    runContextArgs.ManuallyResumed = sessionJobArgs.ManuallyResumed;
    runContextArgs.UseWinningDeployment = sessionJobArgs.UseWinningDeployment;
    runContextArgs.SessionGroupId = sessionJobArgs.SessionGroupId;
  }
}
