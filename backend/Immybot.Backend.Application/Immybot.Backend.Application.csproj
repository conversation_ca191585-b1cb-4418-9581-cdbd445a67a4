<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <DebugType>embedded</DebugType>
    <PSESBundledModulePath>PSESBundledModules</PSESBundledModulePath>
    <WarningsNotAsErrors>0436</WarningsNotAsErrors>
    <DisableMSBuildAssemblyCopyCheck>true</DisableMSBuildAssemblyCopyCheck>
  </PropertyGroup>
  <ItemGroup>
    <InternalsAssemblyName Include="System.Management.Automation" />
    <InternalsAssemblyName Include="Microsoft.Windows.PowerShell.ScriptAnalyzer*" />
    <InternalsAssemblyName Include="Microsoft.Windows.PowerShell.ScriptAnalyzer.BuiltinRules" />
    <InternalsAssemblyName Include="Microsoft.PowerShell.EditorServices" />
    <Content Include="PowerShell/**" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="$(SubmodulesPath)/PowerShellEditorServices/module/PowerShellEditorServices/**" TargetPath="$(PSESBundledModulePath)/PowerShellEditorServices/%(RecursiveDir)%(Filename)%(Extension)" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="$(SubmodulesPath)/PSScriptAnalyzer/Engine/*.ps*" TargetPath="$(PSESBundledModulePath)/PSScriptAnalyzer/%(Filename)%(Extension)" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="$(SubmodulesPath)/PSScriptAnalyzer/Engine/*.psd1" CopyToOutputDirectory="PreserveNewest" />
    <PackageReference Include="App.Metrics.Core" />
    <PackageReference Include="App.Metrics.Formatters.Json" />
    <PackageReference Include="DotNext.Threading" />
    <PackageReference Include="Azure.Messaging.EventHubs" />
    <PackageReference Include="Azure.Security.KeyVault.Keys" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" />
    <PackageReference Include="Azure.Storage.Blobs" />
    <PackageReference Include="Ben.Demystifier" />
    <PackageReference Include="Castle.Core" />
    <PackageReference Include="Castle.Core.AsyncInterceptor" />
    <PackageReference Include="CliWrap" />
    <PackageReference Include="CsvHelper" />
    <PackageReference Include="Dunet" />
    <PackageReference Include="Hangfire.AspNetCore" />
    <PackageReference Include="Hangfire.Autofac" />
    <PackageReference Include="Hangfire.Dashboard.BasicAuthorization" />
    <PackageReference Include="Hangfire.Pro" />
    <PackageReference Include="Hangfire.Pro.Redis" />
    <PackageReference Include="Hangfire.Throttling" />
    <PackageReference Include="IgnoresAccessChecksToGenerator">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Immybot.Manager.Shared" />
    <PackageReference Include="Immybot.Manager.SitesApi" />
    <PackageReference Include="LinqKit.Microsoft.EntityFrameworkCore" />
    <PackageReference Include="M31.FluentApi" />
    <PackageReference Include="MessagePipe" />
    <PackageReference Include="MessagePipe.Analyzer">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" />
    <PackageReference Include="Microsoft.AspNetCore.JsonPatch" />
    <PackageReference Include="Microsoft.AspNetCore.StaticFiles" />
    <PackageReference Include="Microsoft.Azure.Services.AppAuthentication" />
    <!--
      The following MSBuild packages (17.13.*) are bundled so that the out-of-process
      PowerShell Editor Services instance can load modern MSBuild assemblies.
      PSScriptAnalyzer depends on TaskItem.ImportMetadata, first shipped in 17.x.
      These are runtime-only for the backend executable and are NOT a compile-time
      dependency for library consumers.
    -->
    <PackageReference Include="Microsoft.Build" />
    <PackageReference Include="Microsoft.Build.Framework" />
    <PackageReference Include="Microsoft.Build.Locator" />
    <PackageReference Include="Microsoft.Build.Utilities.Core" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" />
    <PackageReference Include="Microsoft.Graph" />
    <PackageReference Include="Microsoft.Identity.Web" />
    <PackageReference Include="MonoMod.RuntimeDetour" />
    <PackageReference Include="Moq" />
    <PackageReference Include="Namotion.Reflection" />
    <PackageReference Include="NCrontab.Advanced" />
    <PackageReference Include="Nerdbank.Streams" />
    <PackageReference Include="Nito.AsyncEx" />
    <PackageReference Include="NuGet.Versioning" />
    <PackageReference Include="OmniSharp.Extensions.JsonRpc" />
    <PackageReference Include="OmniSharp.Extensions.LanguageProtocol" />
    <PackageReference Include="OmniSharp.Extensions.LanguageServer" />
    <PackageReference Include="OmniSharp.Extensions.LanguageServer.Shared" />
    <PackageReference Include="OneOf" />
    <PackageReference Include="Overby.Extensions.AsyncBinaryReaderWriter" />
    <PackageReference Include="Pastel" />
    <PackageReference Include="Polly" />
    <PackageReference Include="Polly.Caching.Memory" />
    <PackageReference Include="Polly.Contrib.DuplicateRequestCollapser" />
    <PackageReference Include="Refit.HttpClientFactory" />
    <PackageReference Include="Sieve" />
    <PackageReference Include="SonarAnalyzer.CSharp">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="StackExchange.Redis" />
    <PackageReference Include="StreamJsonRpc" />
    <PackageReference Include="System.Collections" />
    <PackageReference Include="System.Collections.Specialized" />
    <PackageReference Include="System.IO.FileSystem.Primitives" />
    <PackageReference Include="System.Reactive" />
    <PackageReference Include="System.Text.RegularExpressions" />
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" />
    <PackageReference Include="ZiggyCreatures.FusionCache" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\shared\Immybot.Shared.Abstractions.Device\Immybot.Shared.Abstractions.Device.csproj" Private="false" ReferenceOutputAssembly="true" OutputItemType="Reference" />
    <ProjectReference Include="..\..\shared\Immybot.Shared.DataContracts.WindowsRegistry\Immybot.Shared.DataContracts.WindowsRegistry.csproj" />
    <ProjectReference Include="..\..\shared\Immybot.Shared.Primitives\Immybot.Shared.Primitives.csproj" Private="false" ReferenceOutputAssembly="true" OutputItemType="Reference" />
    <ProjectReference Include="..\Immybot.Backend.Azure\Immybot.Backend.Azure.csproj"/>
    <ProjectReference Include="..\Immybot.Backend.Manager\Immybot.Backend.Manager.csproj"/>
    <ProjectReference Include="..\Immybot.Backend.Notifications.Mailer\Immybot.Backend.Notifications.Mailer.csproj" />
    <ProjectReference Include="..\Immybot.Backend.PowerShell\Immybot.Backend.PowerShell.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Providers.HaloPsaProvider\Immybot.Backend.Providers.HaloPsaProvider.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Providers.Interfaces\Immybot.Backend.Providers.Interfaces.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Domain\Immybot.Backend.Domain.csproj" Private="false" ReferenceOutputAssembly="true" OutputItemType="Reference" />
    <ProjectReference Include="..\Immybot.Backend.HtmlEmailTemplates\Immybot.Backend.HtmlEmailTemplates.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Infrastructure\Immybot.Backend.Infrastructure.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Persistence\Immybot.Backend.Persistence.csproj" />
    <ProjectReference Include="..\Immybot.Backend.GlobalSoftwarePersistence\Immybot.Backend.GlobalSoftwarePersistence.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Providers.CwAutomateProvider\Immybot.Backend.Providers.CwAutomateProvider.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Providers.CwControlProvider\Immybot.Backend.Providers.CwControlProvider.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Providers.CwManageProvider\Immybot.Backend.Providers.CwManageProvider.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Providers.ImmyAgentProvider\Immybot.Backend.Providers.ImmyAgentProvider.csproj" />
    <ProjectReference Include="..\Immybot.Backend.Providers.NCentralProvider\Immybot.Backend.Providers.NCentralProvider.csproj" />
    <ProjectReference Include="..\..\submodules\PowerShellEditorServices\src\PowerShellEditorServices.Hosting\PowerShellEditorServices.Hosting.csproj" />
    <ProjectReference Include="..\..\submodules\PowerShellEditorServices\src\PowerShellEditorServices\PowerShellEditorServices.csproj" Private="false" />
    <ProjectReference Include="..\..\submodules\PSScriptAnalyzer\Rules\Rules.csproj" DefineConstants="$(DefineConstants);CoreCLR" />
    <ProjectReference Include="..\..\submodules\PSScriptAnalyzer\PSCompatibilityCollector\Microsoft.PowerShell.CrossCompatibility\Microsoft.PowerShell.CrossCompatibility.csproj" Private="false" />
    <ProjectReference Include="..\Immybot.Backend.RBAC.Domain\Immybot.Backend.RBAC.Domain.csproj" />
  </ItemGroup>

	<ItemGroup>
		<Compile Update="Properties\Resources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>Resources.resx</DependentUpon>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Update="Properties\Resources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>Resources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<Reference Include="Hangfire.MemoryStorage">
			<HintPath>Hangfire.MemoryStorage.dll</HintPath>
		</Reference>
	</ItemGroup>

	<ItemGroup>
		<Content Remove="PowerShell\PSProviders\ScriptPSProvider.cs" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="Lib\Azure\permissions-descriptions.json" />
		<None Remove="PowerShell\Compress-ScriptBlock.ps1" />
		<None Remove="PowerShell\Formats\DateTime.format.ps1xml" />
		<None Remove="PowerShell\Formats\FileSystem.format.ps1xml" />
		<None Remove="PowerShell\Get-PowerShellVersion.ps1" />
		<None Remove="PowerShell\PSScriptAnalyzer.psm1" />
		<None Remove="PowerShell\Types\DateTime.ps1xml" />
	</ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Lib\Azure\permissions-descriptions.json" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Auth\RBAC\Authentication\" />
  </ItemGroup>
</Project>
