using System;
using System.Collections.Generic;
using System.Linq;
using System.Management.Automation;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Models;
using Microsoft.PowerShell.Commands;

namespace Immybot.Backend.Application.Interface.MetaScripts;

public record PSComputerTag(int Id, string Name);
public class PSComputer
{
  public int Id { get; set; }
  public string? Name { get; set; }
  public string? OperatingSystemName { get; set; }
  public string? SerialNumber { get; set; }
  public ComputerOnboardingStatus OnboardingStatus { get; set; }
  public Guid DeviceId { get; set; }
  public int TenantId { get; set; }
  public string? TenantPrincipalId { get; set; }
  public string? TenantName { get; set; }
  public int? PrimaryPersonId { get; set; }
  public string? PrimaryPersonName { get; set; }
  public string? PrimaryPersonEmail { get; set; }
  public string? PrimaryPersonPrincipalId { get; set; }
  public string? PrimaryPersonOnPremisesSecurityIdentifier { get; set; }
  public bool IsOnline { get; set; }
  public PSObject Inventory { get; set; } = new PSObject();
  public List<PSComputerTag> Tags = [];

  public static PSComputer FromComputer(Computer c, string[]? inventoryKeys = null)
  {
    var computer = new PSComputer
    {
      Id = c.Id,
      Name = c.ComputerName ?? c.DeviceId.ToString(),
      OperatingSystemName = c.OperatingSystem ?? string.Empty,
      SerialNumber = c.SerialNumber ?? string.Empty,
      OnboardingStatus = c.OnboardingStatus,
      DeviceId = c.DeviceId,
      TenantId = c.TenantId,
      TenantName = c.Tenant?.Name ?? string.Empty,
      PrimaryPersonId = c.PrimaryPersonId,
      PrimaryPersonName = c.PrimaryPerson?.DisplayName ?? string.Empty,
      PrimaryPersonPrincipalId = c.PrimaryPerson?.AzurePrincipalId ?? string.Empty,
      PrimaryPersonOnPremisesSecurityIdentifier = c.PrimaryPerson?.OnPremisesSecurityIdentifier ?? string.Empty,
      PrimaryPersonEmail = c.PrimaryPerson?.EmailAddress ?? string.Empty,
      IsOnline = c.GetOnlineRunScriptAgents().Any(),
      TenantPrincipalId = c.Tenant?.AzureTenantLink?.AzureTenant?.PrincipalId ?? string.Empty,
      Tags = c.ComputerTags
        .Select(a => new PSComputerTag(a.TagId, a.Tag?.Name ?? string.Empty))
        .Concat(c.Tenant?.TenantTags.Select(a => new PSComputerTag(a.TagId, a.Tag?.Name ?? string.Empty)) ??
                Enumerable.Empty<PSComputerTag>())
        .Concat(c.PrimaryPerson?.PersonTags.Select(a => new PSComputerTag(a.TagId, a.Tag?.Name ?? string.Empty)) ??
                Enumerable.Empty<PSComputerTag>()).ToList()
    };

    if (inventoryKeys != null)
    {
      var allInventoryResults = c.LatestInventoryScriptResults.ToDictionary(r => r.InventoryKey);
      foreach (var k in inventoryKeys)
      {
        if (allInventoryResults.TryGetValue(k, out var result)
          && result.LatestSuccessResult != null
          && result.LatestSuccessResult.RootElement.TryGetProperty(InventoryKeys.OutputStreamKey, out var output)
          && output.GetRawText() is { Length: > 0 } json)
        {
          var obj = JsonObject.ConvertFromJson(json, out var error);
          if (error == null)
          {
            computer.Inventory.Members.Add(new PSNoteProperty(k, obj));
          }
        }
      }
    }

    return computer;
  }
}
