using System;
using System.Collections.Generic;
using System.Management.Automation.Language;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Lib.MetaScripts.Attributes;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface;
public interface IDynamicFormService
{
  /// <summary>
  /// Binds the provided parameters to the script's param block to check if there are any errors.
  /// Also returns the show command info and a list of the parameters that apply
  /// </summary>
  /// <param name="runContext"></param>
  /// <param name="script">PowerShell script that contains a param block</param>
  /// <param name="cancellationToken"></param>
  /// <param name="specifiedParameters">parameters that we are binding to the param block</param>
  /// <param name="scriptType"></param>
  /// <param name="ignoreCache"></param>
  /// <returns></returns>
  Task<DynamicFormBindResultWithConvertedParameters> BindParameters(
    bool canAccessMspResources,
    bool canAccessParentTenant,
    IRunContext? runContext,
    string script,
    DatabaseType scriptType,
    CancellationToken cancellationToken,
    Dictionary<string, ParameterValue>? specifiedParameters = null,
    bool ignoreCache = false);

  /// <summary>
  /// Binds the provided parameters to the script's param block to check if there are any errors.
  /// Also returns the show command info and a list of the parameters that apply
  /// </summary>
  /// <param name="runContext"></param>
  /// <param name="paramBlockAst">The scripts <see cref="ParamBlockAst"/></param>
  /// <param name="dynamicParamBlockAst">The scripts <see cref="NamedBlockAst"/></param>
  /// <param name="cancellationToken"></param>
  /// <param name="specifiedParameters">parameters that we are binding to the param block</param>
  /// <param name="databaseType"></param>
  /// <param name="ignoreCache"></param>
  /// <returns></returns>
  Task<DynamicFormBindResultWithConvertedParameters> BindParameters(
    bool canAccessMspResources,
    bool canAccessParentTenant,
    IRunContext? runContext,
    ParamBlockAst? paramBlockAst,
    NamedBlockAst? dynamicParamBlockAst,
    DatabaseType databaseType,
    CancellationToken cancellationToken,
    Dictionary<string, ParameterValue>? specifiedParameters = null,
    bool ignoreCache = false);

  /// <summary>
  /// Binds the provided parameters to the provided form type to check if there are any errors.
  /// Also returns the show command info and a list of the parameters that apply.
  /// </summary>
  /// <param name="formType"></param>
  /// <param name="specifiedParameters"></param>
  /// <returns></returns>
  DynamicFormBindResultWithConvertedParameters BindParameters(Type formType, Dictionary<string, object?>? specifiedParameters = null);

  /// <summary>
  /// Retrieves only the media parameters out of a param block
  /// </summary>
  /// <param name="runContext"></param>
  /// <param name="paramBlock"></param>
  /// <param name="scriptType"></param>
  /// <param name="cancellationToken"></param>
  /// <returns></returns>
  Task<Dictionary<string, MediaIdentifier?>> GetMediaParameters(
    IActionRunContext runContext,
    string paramBlock,
    DatabaseType scriptType,
    CancellationToken cancellationToken);

}
