using Immybot.Backend.Domain.Models;
using NuGet.Versioning;

namespace Immybot.Backend.Application.Interface.Commands.Payloads.Licenses;

public interface ILicensePayloadBase
{
  string Name { get; }
  string LicenseValue { get; }
  SoftwareType SoftwareType { get; }
  string SoftwareIdentifier { get; }
  string SoftwareName { get; }
  SemanticVersion? SemanticVersion { get; }
  int? TenantId { get; }
  bool RestrictToMajorVersion { get; }
}
