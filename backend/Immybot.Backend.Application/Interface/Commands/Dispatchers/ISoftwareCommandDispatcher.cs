using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface.Commands.Dispatchers;

public interface ILocalSoftwareCommandDispatcher
{
  ICreateItemCommandResult<LocalSoftware> Dispatch(ICreateLocalSoftwarePayload payload, bool throwIfNotSuccessful = false);
  IUpdateItemCommandResult<LocalSoftware> Dispatch(IUpdateLocalSoftwarePayload payload, bool throwIfNotSuccessful = false);
  IDeleteItemCommandResult Dispatch(DeleteLocalSoftwareVersionPayload payload, bool throwIfNotSuccessful = false);
}
