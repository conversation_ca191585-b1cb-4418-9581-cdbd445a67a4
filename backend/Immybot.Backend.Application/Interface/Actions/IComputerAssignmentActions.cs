using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Primitives;
using Polly;

namespace Immybot.Backend.Application.Interface.Actions;

public interface IComputerAssignmentActions
{
  Task<DisposableValue<IQueryable<Computer>>> GetComputersInTarget(
    TargetType targetType,
    TargetGroupFilter targetGroupFilter,
    string? target = null,
    int? tenantId = null,
    bool includeChildTenants = false,
    bool allowAccessToParentTenant = false,
    int? providerLinkId = null,
    Guid? providerDeviceGroupType = null,
    Guid? providerClientGroupType = null,
    Guid? cacheGroupId = null,
    IAsyncPolicy? cachePolicy = null,
    Context? policyContext = null,
    bool excludeOnboarding = false,
    bool excludeOnboarded = false,
    bool excludeOffline = false,
    bool excludeUnlinked = false,
    int? filterScriptComputerId = null,
    bool withPrimaryPerson = false,
    CancellationToken cancellationToken = default,
    bool withAgents = false,
    bool withTenant = false,
    bool withTags = false,
    ICollection<string>? withInventoryKeyResults = null,
    bool asNoTracking = false,
    bool isForScheduleOrDeploymentResolution = true);
}
