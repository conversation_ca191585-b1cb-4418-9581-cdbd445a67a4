using System.Collections.Generic;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface.Actions;

public record TargetAssignmentWithTaskOnboardingFlag(
  TargetAssignment TargetAssignment,
  int ParentTenantPropagationDistance,
  HashSet<string> HiddenParams,
  bool TaskIsOnboardingOnly,
  bool IgnoreDuringAutomaticOnboarding);

public record TargetAssignmentWithTenantPropagationDistance(
  TargetAssignment TargetAssignment,
  int ParentTenantPropagationDistance);
