using System.Threading.Tasks;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface.Actions;

public interface IRunContextActions
{
  Task AddSessionLog(IRunContext context, SessionLog log);
  Task UpdateSession(IRunContext context, MaintenanceSession session);
  Task UpdateStage(IRunContext context, MaintenanceSessionStage stage);
  Task UpdateAction(IRunContext context, MaintenanceAction action);
  Task RemoveAction(IRunContext context, MaintenanceAction action);
  Task CreateAction(IRunContext context, MaintenanceAction action);
  Task UpdateComputer(IRunContext context, Computer computer);
  Task CreatePhase(IRunContext runContext, SessionPhase phase);
  Task UpdatePhase(IRunContext runContext, SessionPhase phase);
  Task AddMaintenanceActionActivity(IRunContext runContext, MaintenanceActionActivity activity);
}
