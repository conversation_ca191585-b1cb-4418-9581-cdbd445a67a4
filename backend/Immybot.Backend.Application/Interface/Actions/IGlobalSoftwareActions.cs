using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Shared.Primitives;
using System;
using System.Threading.Tasks;
using System.Threading;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface.Actions;

public interface IGlobalSoftwareActions : ISoftwareActions<GlobalSoftware, GlobalSoftwareVersion>
{
  GlobalSoftwareVersion CreateSoftwareVersion(ICreateGlobalSoftwareVersionPayload payload, AuthUserDto? currentUser);

  GlobalSoftware? GetById(int id,
    bool withVersions = false,
    bool withPrerequisites = false,
    bool withScripts = false,
    bool withSoftwareIcon = false);
  GlobalSoftware CreateSoftware(ICreateGlobalSoftwarePayload software, AuthUserDto? currentUser);
  GlobalSoftware? UpdateSoftware(IUpdateGlobalSoftwarePayload payload, AuthUserDto? currentUser);
  void DeleteSoftwareVersion(DeleteGlobalSoftwareVersionPayload payload, AuthUserDto currentUser);
  GlobalSoftwareVersion? UpdateSoftwareVersion(IUpdateGlobalSoftwareVersionPayload payload, AuthUserDto? currentUser);

  /// <summary>
  ///   Attempts to find software based on any of the supplied parameters.
  ///   At least one of the parameters needs to be a non-default value.
  /// </summary>
  Task<OpResult<GlobalSoftware[]>> FindSoftware(Guid upgradeCode, Guid productCode, string softwareDisplayName, CancellationToken cancellationToken);
}
