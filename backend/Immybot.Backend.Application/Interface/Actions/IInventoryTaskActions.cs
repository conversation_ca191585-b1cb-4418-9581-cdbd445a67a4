using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface.Actions;

public interface IInventoryTaskActions
{
  void RebuildCache();
  Task<ICollection<InventoryTask>> GetAllInventoryTasks(CancellationToken token);
  Task<ICollection<InventoryTask>> GetInventoryTasks(
    ICollection<(string taskIdentifier, DatabaseType taskType)> inventoryTasks, CancellationToken token);
}
