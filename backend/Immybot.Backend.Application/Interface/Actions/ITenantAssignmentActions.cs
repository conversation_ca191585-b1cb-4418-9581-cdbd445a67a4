using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Primitives;

namespace Immybot.Backend.Application.Interface.Actions;

public interface ITenantAssignmentActions
{
  Task<DisposableValue<IQueryable<Tenant>>> GetTenantsInTarget(
    TargetType targetType,
    string? target = null,
    int? tenantId = null,
    bool includeChildTenants = false,
    bool allowAccessToParentTenant = false,
    int? providerLinkId = null,
    Guid? providerClientGroupType = null,
    CancellationToken cancellationToken = default);
}
