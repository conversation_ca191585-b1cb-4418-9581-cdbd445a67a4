using Immybot.Backend.Application.Interface.Actions.Responses;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Primitives;

namespace Immybot.Backend.Application.Interface.Actions;

public interface ILocalSoftwareActions : ISoftwareActions<LocalSoftware, LocalSoftwareVersion>
{
  LocalSoftware? GetById(
    string softwareIdentifier,
    bool withVersions = false,
    bool withPrerequisites = false,
    bool withTenantSoftware = false,
    bool withScripts = false,
    bool withSoftwareIcon = false,
    bool withUpdatedByUser = false);
  LocalSoftware? GetById(
    int id,
    bool withVersions = false,
    bool withPrerequisites = false,
    bool withTenantSoftware = false,
    bool withScripts = false,
    bool withSoftwareIcon = false,
    bool withUpdatedByUser = false);
  IReadOnlyList<LocalSoftware> GetAllSoftware(
    bool withVersions = false,
    bool withPrerequisites = false,
    bool withTenantSoftware = false,
    bool withScripts = false,
    bool withSoftwareIcon = false,
    bool withUpdatedByUser = false);
  DisposableValue<IQueryable<LocalSoftware>> GetAllSoftwareQueryable(
    bool withVersions = false,
    bool withPrerequisites = false,
    bool withTenantSoftware = false,
    bool withSoftwareIcon = false,
    bool withUpdatedByUser = false);
  IReadOnlyList<LocalSoftware> GetAllSoftwareForTenant(
    int tenantId,
    bool withVersions = false,
    bool withPrerequisites = false,
    bool withSoftwareIcon = false,
    bool withUpdatedByUser = false);
  TenantSoftware? GetTenantSoftwareById(
    int tenantId,
    int softwareId,
    bool withSoftware = false,
    bool withVersions = false,
    bool withPrerequisites = false,
    bool withSoftwareIcon = false);
  LocalSoftware? GetFirstByName(
    string softwareName,
    bool withVersions = false,
    bool withPrerequisites = false,
    bool withTenantSoftware = false,
    bool withSoftwareIcon = false);
  bool IsSoftwareOwnedByTenant(int softwareId, int tenantId);
  LocalSoftware CreateSoftware(ICreateLocalSoftwarePayload software, AuthUserDto? currentUser);
  LocalSoftware? UpdateSoftware(IUpdateLocalSoftwarePayload payload, AuthUserDto? currentUser);
  LocalSoftwareVersion CreateSoftwareVersion(ICreateLocalSoftwareVersionPayload payload, AuthUserDto? currentUser);
  LocalSoftwareVersion? UpdateSoftwareVersion(IUpdateLocalSoftwareVersionPayload payload, AuthUserDto? currentUser);
  void DeleteSoftwareVersion(DeleteLocalSoftwareVersionPayload payload, AuthUserDto currentUser);
  Task<IReadOnlyList<SoftwareFromInventoryResultsModel>> GetSoftwareFromInventoryResults(int tenantId);
}
