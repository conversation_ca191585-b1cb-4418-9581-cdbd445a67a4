using System;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Manager.SitesApi;

namespace Immybot.Backend.Application.Interface.Actions;
public interface IDevLabActions
{
  /// <summary>
  /// Requests a dev lab vm from the manager
  /// </summary>
  /// <param name="token"></param>
  /// <returns></returns>
  Task<ClaimDevLabVmResponse> ClaimDevLabVm(CancellationToken token);

  /// <summary>
  /// Wipe the dev lab vm
  /// </summary>
  /// <param name="computerId"></param>
  /// <param name="vmName"></param>
  /// <param name="token"></param>
  /// <returns></returns>
  Task UnclaimVm(int computerId, string vmName, CancellationToken token);

  /// <summary>
  /// Retrieves info to allow rdp connection to the vm
  /// </summary>
  /// <param name="vmName"></param>
  /// <returns></returns>
  Task<GetRdpInfoResponse> GetRdpInfo(string vmName);

  /// <summary>
  /// Set expiration dates for all vms that do not have one
  /// </summary>
  /// <param name="expirationDate"></param>
  /// <param name="token"></param>
  /// <returns></returns>
  Task SetDevLabVmExpirations(DateTime expirationDate, CancellationToken token);

  /// <summary>
  /// Wipe all expired vms
  /// </summary>
  /// <param name="expiredAfterDate"></param>
  /// <param name="token"></param>
  /// <returns></returns>
  Task UnclaimAllExpiredVms(DateTime expiredAfterDate, CancellationToken token);
}
