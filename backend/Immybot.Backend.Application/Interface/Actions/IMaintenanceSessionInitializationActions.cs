using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface.Actions;

public interface IMaintenanceSessionInitializationActions
{
  MaintenanceSession InitializeComputerMaintenanceSession(int computerId, bool isFullMaintenance = false);
  MaintenanceSession InitializeTenantMaintenanceSession(int tenantId, bool isFullMaintenance = false);
  MaintenanceSession InitializePersonMaintenanceSession(int personId, bool isFullMaintenance = false);
  MaintenanceSession InitializeExecutionOnlyComputerMaintenanceSession(int computerId);
  MaintenanceSession InitializeDetectionOnlyComputerMaintenanceSession(int computerId);
  MaintenanceSession InitializeResolutionOnlyComputerMaintenanceSession(int computerId);
  MaintenanceSession InitializeInventoryOnlyComputerMaintenanceSession(int computerId);
  MaintenanceSession InitializeAgentUpdatesOnlyComputerMaintenanceSession(int computerId);
  MaintenanceSession InitializeDetectionOnlyTenantMaintenanceSession(int tenantId);
  MaintenanceSession InitializeDetectionOnlyPersonMaintenanceSession(int personId);
}
