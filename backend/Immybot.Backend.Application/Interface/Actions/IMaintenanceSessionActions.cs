using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface.Actions;

public interface IMaintenanceSessionActions
{
  Task FailAllRunningSessions(CancellationToken token);
  Task FailAllRunningSessionStages(CancellationToken token);
  Task FailAllPendingSessions(CancellationToken token);
  Task FailAllCreatedSessions(CancellationToken token);
  bool TryGetRunningSessionForComputer(
    int computerId,
    [NotNullWhen(true)] out MaintenanceSession? differentRunningSession);
  bool TryGetRunningCloudSessionForTenant(
    int tenantId,
    [NotNullWhen(true)] out MaintenanceSession? differentRunningSession);
  bool TryGetRunningPersonSessionForPerson(
    int personId,
    [NotNullWhen(true)] out MaintenanceSession? differentRunningSession);
  Task TryCancelSession(int sessionId);
  Task TryCancelSessions(List<int> sessionIds);
  Task<bool> TryCancelSessionsForSchedule(int scheduleId);

  /// <summary>
  /// This method is ultimate responsible for updating the database with cancellation statuses
  /// </summary>
  /// <param name="sessionId"></param>
  /// <returns></returns>
  Task HandleMaintenanceSessionCancellation(int sessionId);
}
