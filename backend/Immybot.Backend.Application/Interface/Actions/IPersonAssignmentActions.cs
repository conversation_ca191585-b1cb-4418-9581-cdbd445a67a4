using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Primitives;

namespace Immybot.Backend.Application.Interface.Actions;

public interface IPersonAssignmentActions
{
  Task<DisposableValue<IQueryable<Person>>> GetPersonsInTarget(
    TargetType targetType,
    string? target = null,
    int? tenantId = null,
    bool includeChildTenants = false,
    CancellationToken cancellationToken = default);
}
