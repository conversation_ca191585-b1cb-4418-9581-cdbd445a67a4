using NuGet.Versioning;
using System.Collections.Generic;
using System.Threading.Tasks;
using Polly;
using System.Threading;
using System;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;

namespace Immybot.Backend.Application.Interface.Actions;

public interface ISoftwareActions<TSoftware, TSoftwareVersion>
  where TSoftware : Software
  where TSoftwareVersion : SoftwareVersion
{
  IReadOnlyList<TSoftware> GetAllSoftware(bool withVersions = false, bool withPrerequisites = false, bool withScripts = false, bool withSoftwareIcon = false, bool withUpdatedByUser = false, Guid? agentIntegrationTypeId = null);
  TSoftware? GetById(string identifier, bool withVersions = false, bool withPrerequisites = false, bool withScripts = false, bool withSoftwareIcon = false, bool withUpdatedByUser = false);
  void DeleteSoftware(TSoftware software, AuthUserDto? currentUser);
  IReadOnlyList<TSoftwareVersion> GetAllSoftwareVersions(bool populateSoftware = false);

  TSoftwareVersion? GetSoftwareVersion(string softwareIdentifier,
    SemanticVersion? semanticVersion,
    bool populateSoftware = false);
  TSoftwareVersion? GetLatestSoftwareVersion(string softwareIdentifier, SemanticVersion? atOrBelow = null, bool populateSoftware = false);
}
public interface ISoftwareActions
{
  string NiniteExecutablePath { get; }
  Script GetNiniteSoftwareList { get; }
  Script NiniteDetectionAction { get; }
  Script NiniteTestExecutablePath { get; }

  Task<SoftwareVersion?> GetLatestSoftwareVersion(
    SoftwareType softwareType,
    string softwareIdentifier,
    CancellationToken token,
    SemanticVersion? atOrBelow = null,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null);

  LocalSoftware? GetNiniteSoftware();
  Task<Software?> GetSoftware(
    SoftwareType softwareType,
    string softwareIdentifier,
    CancellationToken token,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null);

  Task<Software?> GetAgentSoftwareByIntegrationTypeId(Guid integrationTypeId,
    CancellationToken token,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null);

  void CacheWindowsPatch(WindowsPatch patch);
  Task<SoftwareVersion?> GetSoftwareVersion(
    SoftwareType softwareType,
    string softwareIdentifier,
    SemanticVersion version,
    CancellationToken token,
    bool populateSoftware = false,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null);

  Task<Script?> GetScript(
    DatabaseType scriptType,
    string scriptIdentifier,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null,
    CancellationToken cancellationToken = default);
  Task PopulateSoftwareScripts(Software software);
  Task PopulateSoftwareVersionScripts(SoftwareVersion version);

  Task<List<MaintenanceSpecifier>> GetIntegrationAgentMaintenanceSpecifiers(
    CancellationToken cancellationToken,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null);
}
