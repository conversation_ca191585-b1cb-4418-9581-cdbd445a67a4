using Immybot.Backend.Application.Interface.Commands.Payloads.TargetAssignments;
using Polly;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface.Actions;
public interface ITargetAssignmentActions
{
  /// <summary>
  /// Attempts to find an assignment matching the specified maintenance item and target.
  /// If multiple are found the first one is returned.
  /// </summary>
  /// <param name="assignment"></param>
  /// <param name="taskId"></param>
  /// <param name="taskType"></param>
  /// <returns></returns>
  Task<TargetAssignment?> GetOtherTargetAssignmentMatchingTargetOptions(
    TargetAssignment assignment,
    string maintenanceIdentifier,
    MaintenanceType maintenanceType);

  Task<TargetAssignment?> GetTargetAssignmentById(
    int id,
    DatabaseType databaseType,
    CancellationToken token,
    bool includeNotes = false,
    bool includeVisibility = false,
    IAsyncPolicy? cachePolicy = null,
    Context? policyContext = null);

  ICollection<TargetAssignment> GetLocalTargetAssignmentsByIds(List<int> ids);
  ICollection<TargetAssignment> GetGlobalTargetAssignmentsByIds(List<int> ids);

  Task<ICollection<TargetAssignmentWithTaskOnboardingFlag>> GetSortedTargetAssignments(
    CancellationToken token,
    IAsyncPolicy? cachePolicy = null,
    Context? policyContext = null,
    bool excludeAssignmentsWithMissingSoftware = false,
    MaintenanceSpecifier? maintenanceSpecifier = null,
    bool onlySoftware = false,
    bool onlyMaintenanceTasks = false,
    TargetCategory? targetCategory = null,
    int? tenantId = null,
    bool excludeAssignmentsForDisabledProviderLinks = false,
    bool onlyForAgentIntegrationSoftware = false,
    bool excludeOnboardingOnly = false,
    bool includeVisibility = false);

  Task<TargetAssignmentApprovalStatus?> GetApprovalStatusForTargetAssignment(
    int targetAssignmentId,
    string target,
    TargetType targetType,
    MaintenanceType maintenanceType,
  CancellationToken token);


  TargetAssignment? GetLocalTargetAssignmentById(int targetAssignmentId, bool includeLicense = false);
  TargetAssignment CreateLocalTargetAssignment(CreateLocalTargetAssignmentPayload payload, AuthUserDto user);

  TargetAssignment? UpdateLocalTargetAssignment(UpdateLocalTargetAssignmentPayload payload, AuthUserDto user);

  Task ExcludeLocalTargetAssignment(int targetAssignmentId);
}
