using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Primitives;
using Microsoft.Graph.Models;
using OneOf;
using Polly;

namespace Immybot.Backend.Application.Interface.Actions;

public record AzureTenantDetailsSyncResult(
  string PrincipalId,
  bool WasSuccessful,
  AzureError? FailedReason,
  AzureTenantInfo? TenantInformationSynced);
public record AzureTenantUserSyncResult(
  string PrincipalId,
  bool WasSuccessful,
  AzureError? FailedReason,
  int NumUsersSynced,
  Dictionary<int, int> ImmyTenantsSyncedUsers);
public record TenantInfoResult(string PrincipalId, OneOf<AzureTenantInfo, AzureError> Result);
public record GdapRelationshipRole(string RoleDefinitionId, string? RoleDefinitionName);
public record GdapRelationshipAccessAssignment(string AccessContainerId, string AccessContainerType);
public record AzureGdapRelationshipDetails(string Id,
  string DisplayName,
  string Status,
  int? DurationNumDays,
  DateTime? CreatedDateTimeUtc,
  DateTime? EndDateTimeUtc,
  string? AutoExtendDuration,
  ICollection<GdapRelationshipRole> Roles,
  ICollection<GdapRelationshipAccessAssignment> AccessAssignments);
public record AzureTenantCustomer(
  string TenantId,
  string DisplayName,
  string Domain,
  ICollection<AzureGdapRelationshipDetails> GdapRelationships);
public record AzureTenantCustomersResult(
  string PartnerPrincipalId,
  OneOf<ICollection<AzureTenantCustomer>, AzureError> Result);
public record AzureCustomerCheckAccessResult(string CustomerPrincipalId, List<ExpectedRoleAssignment> ExpectedRoleAssignments);
public record MultiCustomerPreconsentResult(
  string PartnerPrincipalId,
  OneOf<ICollection<AzureCustomerPreconsentResult>, AzureError> Result);
public record GetPartnerCenterOrgResult(OneOf<PartnerOrganizationProfile, AzureError> Result);
public record TenantGroupsDisplayNameResult(string PrincipalId, OpResult<Dictionary<string, string>> Groups);
public class GetTenantGroupDisplayNamesPayload
  : AzureTenant
{
  public GetTenantGroupDisplayNamesPayload(ICollection<string> groupIds)
  {
    GroupIds = groupIds;
  }
  public ICollection<string> GroupIds { get; }
}

public interface IAzureActions
{
  Task<List<int>> GetPersonIdsInAzureGroupAtTenant(
    Tenant tenant,
    string azureGroupId,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null,
    CancellationToken cancellationToken = default);

  Task<List<int>> GetComputerIdsInAzureGroupAtTenant(
    Tenant tenant,
    string azureGroupId,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null,
    CancellationToken cancellationToken = default);

  Task<AzureTenantUserSyncResult> SyncUsersFromAzureTenant(
    AzureTenant azTenant,
    CancellationToken cancellationToken);

  /// <summary>
  /// Returns dictionary of partner tenant id to list of delegated admin customers
  /// </summary>
  Task<List<AzureTenantCustomersResult>> GetPartnerTenantsDelegatedAdminCustomers(
    AzureTenant partnerTenant,
    CancellationToken cancellationToken);

  Task<List<TenantInfoResult>> GetPartnerTenantsTenantInformations(CancellationToken cancellationToken);

  Task SyncAllTenantsAzureData(bool syncUsers, CancellationToken cancellationToken);
  Task<AzureTenantDetailsSyncResult> SyncAzureDetailsForTenant(AzureTenant azTenant, CancellationToken cancellationToken);
  Task<List<TenantGroupsDisplayNameResult>> GetGroupDisplayNamesAtTenantsByIds(
    ICollection<GetTenantGroupDisplayNamesPayload> tenantGroups,
    CancellationToken cancellationToken);
  Task<AzureAuthParameters> GetAzureTenantAuthParameters(Tenant tenant, string resourceUri);
  Task<AzureAuthParameters> GetAzureTenantAuthParameters(Tenant tenant, AzureResourceAlias endpoint = AzureResourceAlias.AzureAD);
  Task<List<Group>> GetGroupsAtTenant(Tenant tenant, CancellationToken cancellationToken);

  Task<Group?> GetGroupAtTenant(
    Tenant tenant,
    string groupId,
    CancellationToken cancellationToken);

  Task<List<Group>> FindGroupsAtTenant(
    string search,
    Tenant tenant,
    CancellationToken cancellationToken);
  Task<ICollection<string>> GetAllCustomerIdsForPartnerTenant(AzureTenant azTenant, CancellationToken cancellationToken);
  Task<AzureTenant?> FindParentPartnerTenant(AzureTenant azTenant, List<AzureTenant> allAzTenants, CancellationToken cancellationToken);
  Task DisambiguateAzureTenantType(
    AzureTenant azTenant,
    bool allowResettingCustomerToPartner,
    CancellationToken cancellationToken);
  Task<GetPartnerCenterOrgResult> GetPartnerCenterOrganizationDetails(Oauth2AccessToken accessToken, CancellationToken cancellationToken);

  Task<MultiCustomerPreconsentResult> PreconsentCustomerTenants(
    AzureTenant partnerAzTenant,
    ICollection<AzureTenant> customerAzTenants,
    CancellationToken cancellationToken);
}
