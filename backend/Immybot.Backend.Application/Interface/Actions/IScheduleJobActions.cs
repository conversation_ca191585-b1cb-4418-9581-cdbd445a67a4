using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface.Actions;

public interface IScheduleJobActions
{
  Schedule Create(ICreateSchedulePayload payload, AuthUserDto currentUser);
  void Delete(Schedule schedule, AuthUserDto currentUser);
  void RescheduleAll();
  void UnscheduleJob(Schedule schedule);
  Schedule? Update(IUpdateSchedulePayload payload, AuthUserDto currentUser);
}
