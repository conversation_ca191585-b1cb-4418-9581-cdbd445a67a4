using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Persistence;
using Polly;

namespace Immybot.Backend.Application.Interface.Actions;

public interface IScriptActions
{
  List<ScriptReference> GetScriptReferences(
    Script script,
    ImmybotDbContext localCtx,
    SoftwareDbContext globalCtx);
  ScriptReferenceCounts GetScriptReferenceCounts(
    Script script,
    ImmybotDbContext localCtx,
    SoftwareDbContext globalCtx);
  /// <summary>
  /// Retrieves the script with the given <paramref name="scriptId"/> from the global database.
  /// If <see cref="Script.ScriptCacheName"/> and <see cref="Script.ScriptHash"/> are set and
  /// the script is a device inventory script, then <see cref="Script.PublicStorageDownloadUrl"/>
  /// and <see cref="Script.ScriptHash"/> will be populated with values for downloading the
  /// signed script from this instance's public storage
  /// </summary>
  Script? GetGlobalScriptById(int scriptId, SoftwareDbContext? globalCtx = null);
  ICollection<Script> GetAllLocalScripts(ImmybotDbContext? localCtx = null);
  ICollection<Script> GetAllGlobalScripts(SoftwareDbContext? globalCtx = null);
  Script? GetLocalScriptById(int scriptId, ImmybotDbContext? localCtx = null);
  Task<Script?> GetScript(
    DatabaseType scriptType,
    string scriptIdentifier,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null,
    CancellationToken token = default);

  Task<List<Script>> GetScriptsByCategory(
    ScriptCategory category,
    CancellationToken cancellationToken,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null);

  Task<List<ScriptIdentifier>> GetDisabledPreflightScripts(
    CancellationToken cancellationToken,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null);

  Task<List<ScriptAuthorization>> GetLocalScriptAuthorizations(
    int scriptId,
    CancellationToken cancellationToken,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null);
}
