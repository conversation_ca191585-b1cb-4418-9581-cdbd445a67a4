using System;
using System.Collections;
using System.Collections.Generic;
using System.Management.Automation;
using System.Management.Automation.Language;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using DotNext;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Lib.Providers;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Persistence;
using Immybot.Backend.Providers.Interfaces;
using Polly;

namespace Immybot.Backend.Application.Interface.Actions;

public interface IProviderActions
{
  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  /// <exception cref="GetProviderTimeoutException"></exception>
  Task<IProvider?> GetProvider(
    ProviderLink link,
    CancellationToken token,
    TimeSpan? timeout = null,
    bool allowUnhealthy = false,
    bool noProviderConstruction = false);

  /// <summary>
  /// Gets all enabled provider links that implement the specified interface type.
  /// </summary>
  /// <typeparam name="T">The interface type to check for.</typeparam>
  /// <param name="token">Cancellation token.</param>
  /// <returns>A list of provider links that implement the specified interface.</returns>
  Task<List<ProviderLink>> GetProviderLinksOfType<T>(CancellationToken token) where T : class;

  /// <summary>
  /// Gets all enabled provider instances that implement the specified interface type.
  /// </summary>
  /// <typeparam name="T">The interface type to check for.</typeparam>
  /// <param name="token">Cancellation token.</param>
  /// <returns>A list of provider instances that implement the specified interface.</returns>
  Task<List<T>> GetProvidersOfType<T>(CancellationToken token) where T : class;

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  /// <exception cref="GetProviderTimeoutException"></exception>
  Task<IProvider?> ReloadProvider(ProviderLink link, CancellationToken token, TimeSpan? timeout = null);

  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  ICollection<ProviderTypeDto> GetAllProviderTypes(bool includeLinkFormSchemas = false);

  /// <exception cref="MissingProviderTypeException"></exception>
  ProviderTypeDto GetProviderType(Guid providerTypeId, bool includeLinkFormSchema = false);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task RecreateProvider(ProviderLink link, CancellationToken token);
  Task RemoveProvider(int linkId);
  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<ICollection<IClientGroup>> GetClientGroups(
    ProviderLink link,
    Guid clientGroupTypeId,
    CancellationToken token,
    string? companyId = null);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<ICollection<string>> GetClientIdsInGroup(
    ProviderLink link,
    Guid clientGroupTypeId,
    string clientGroupId,
    CancellationToken token,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<Dictionary<int, Dictionary<Guid, Dictionary<string, string>>>> GetGroupNamesForClientGroupTypesByIds(
    Dictionary<int, Dictionary<Guid, ICollection<string>>> clientGroupTypeIdMap,
    ImmybotDbContext ctx,
    CancellationToken token);

  Dictionary<Guid, (string name, string description)> GetAllClientGroupTypeNamesAndDescriptions();

  Type? GetClientGroupType(Guid providerTypeId, Guid clientGroupTypeId);
  Task<AgentUpdateResult> HandleAgentUpdate(
    ProviderLink link,
    ProviderAgent agent,
    AgentUpdateDto agentUpdateDto,
    CancellationToken token);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task DeleteOfflineAgentFromComputer(ProviderLink providerLink, ProviderAgent agent, CancellationToken token);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<Uri> GetAgentExecutableUri(ProviderLink providerLink, GetExecutableUriParameters requestOptions, CancellationToken token);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<Uri> GetAgentExecutableUri(ProviderLink providerLink, GetExecutableUriParametersWithOnboardingOptions requestOptions, CancellationToken token);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<Uri> GetAgentProvisioningPackageUri(ProviderLink providerLink, GetProvisioningPackageUriParameters requestOptions, CancellationToken token);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<Uri> GetAgentProvisioningPackageUri(ProviderLink providerLink, GetProvisioningPackageUriParametersWithOnboardingOptions requestOptions, CancellationToken token);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<IScript> GetAgentPowerShellInstallScript(ProviderLink providerLink, GetPowerShellInstallScriptParameters requestOptions, CancellationToken token);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<IScript> GetAgentPowerShellInstallScript(ProviderLink providerLink, GetPowerShellInstallScriptParametersWithOnboardingOptions requestOptions, CancellationToken token);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<IScript> GetAgentBashInstallScript(ProviderLink providerLink, GetBashInstallScriptParameters requestOptions, CancellationToken token);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<IScript> GetAgentBashInstallScript(ProviderLink providerLink, GetBashInstallScriptParametersWithOnboardingOptions requestOptions, CancellationToken token);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<Script?> GetAgentInstallScript(ProviderLink providerLink, Computer computer, CancellationToken token);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<ICollection<IDeviceGroup>> GetDeviceGroups(
    ProviderLink link,
    Guid deviceGroupTypeId,
    CancellationToken token,
    string? clientId = null);

  /// <exception cref="MissingDeviceGroupTypeException"></exception>
  TargetScope GetTargetScopeForDeviceGroupType(Guid deviceGroupTypeId);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<ICollection<string>> GetDevicesInGroup(
    ProviderLink link,
    Guid deviceGroupTypeId,
    string deviceGroupId,
    CancellationToken token,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<Dictionary<int, Dictionary<Guid, Dictionary<string, string>>>> GetGroupNamesForDeviceGroupTypesByIds(
    Dictionary<int, Dictionary<Guid, ICollection<string>>> deviceGroupTypeIdMap,
    ImmybotDbContext ctx,
    CancellationToken token);
  Dictionary<Guid, string> GetAllDeviceGroupTypeNames();
  Type? GetDeviceGroupType(Guid providerTypeId, Guid deviceGroupTypeId);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<Dictionary<string, string>> GetClientsLinkedToClients(
    ProviderLink link,
    Guid psaProviderTypeId,
    ICollection<string> psaCompanyIds,
    CancellationToken token,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null);

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  Task<JsonElement> PreprocessProviderTypeFormData(
    ProviderLink providerLink,
    JsonElement formData,
    CancellationToken token);
  Task<bool> RefreshAgentOnlineStatus(
    ProviderLink providerLink,
    ProviderAgent agent,
    CancellationToken token);

  Task<string> GetExternalProviderAgentUrl(
    ProviderLink providerLink,
    ProviderAgent agent,
    CancellationToken token);

  Task<PSObject?> GetExtraDataAccessibleInMetascripts(ProviderLink providerLink, CancellationToken cancellationToken);

  /// <summary>
  /// Gets the <see cref="IProvider"/> implementation for the provided <paramref name="link"/>,
  /// casted to <see cref="IRunScriptProvider"/>. Returns null if the provider does not
  /// implement <see cref="IRunScriptProvider"/>
  /// </summary>
  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  /// <exception cref="GetProviderTimeoutException"></exception>
  Task<IRunScriptProvider?> GetRunScriptProvider(ProviderLink link, CancellationToken token, TimeSpan? timeout = null);

  Task<IEnumerable<IClientStatus>> GetProviderClientStatuses(ProviderLink link, CancellationToken token);
  Task<IEnumerable<IClientType>> GetProviderClientTypes(ProviderLink link, CancellationToken token);
  Type? GetProviderFormType(Guid providerTypeId);

  (ParamBlockAst, NamedBlockAst?) GetDynamicProviderParamBlock(Guid providerTypeId);

  Task SyncProviderAgents(
    ProviderLink link,
    ICollection<string>? clientIds = null,
    CancellationToken token = default);

  Task<string?> GetTenantInstallToken(ProviderLink link, string clientId, CancellationToken token);
  Task<string?> GetTenantUninstallToken(ProviderLink link, string clientId, CancellationToken token);
  Task<string?> GetAgentUninstallToken(ProviderLink link, string agentId, CancellationToken token);
  Task<Hashtable> GetAuthHeader(ProviderLink link, CancellationToken token);
  Task<DynamicVersion[]> GetDynamicVersions(ProviderLink link, string clientId, CancellationToken token);
  Task<Uri> GetLatestAgentVersionDownloadUrl(ProviderLink providerLink, CancellationToken token);

  Task<TechnicianPageInfoFromPsaTicket> GetTechnicianPageInfoFromPsaTicket(ProviderLink providerLink,
    string ticketId,
    CancellationToken token);

  void EnsureProviderClientExists(int providerLinkId,
    string externalClientId,
    bool supportsListingExternalClients);

  Task<Optional<ProviderSupportFormBranding>> GetSupportBrandingDetails(
    CancellationToken token);

  Task<NuGet.Versioning.SemanticVersion> GetLatestAgentVersion(ProviderLink providerLink, CancellationToken token);
}
