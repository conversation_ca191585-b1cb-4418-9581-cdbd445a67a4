using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Providers.Interfaces;

namespace Immybot.Backend.Application.Interface.Actions;

/// <summary>
/// Creates a new uuid and sets that as the UUID on the device
/// </summary>
/// <returns>
/// The device's new uuid if the operation was successful or null
/// if it results in an AgentIdentificationFailure
/// </returns>
internal interface IAgentIdentificationActions
{
  Task<Guid?> AddImmybotDeviceId(Dictionary<string, object?> state,
    int providerLinkId,
    IRunScriptProvider provider,
    ProviderAgent pending,
    int? existingAgentId,
    int? computerId,
    string? onboardingCorrelationId,
    CancellationToken token,
    bool requiresManualDecision = false);
}
