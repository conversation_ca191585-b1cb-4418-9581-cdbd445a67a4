namespace Immybot.Backend.Application.Interface.Azure;
public interface IAzureBlobStorageSasService
{
  public string GetLicenseDownloadUrl(string blobName, string? policyName = null);
  public string GetLocalSoftwareDownloadUrl(string blobName, string? policyName = null);
  public string GetGlobalSoftwareDownloadUrl(string blobName, string? policyName = null);
  public string GetLocalMediaDownloadUrl(string blobName, string? policyName = null);
  public string GetGlobalMediaDownloadUrl(string blobName, string? policyName = null);
  public string GetSupportMediaDownloadUrl(string blobName, string? policyName = null);
}
