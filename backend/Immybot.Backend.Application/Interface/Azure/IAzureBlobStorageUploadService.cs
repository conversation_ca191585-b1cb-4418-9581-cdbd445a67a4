using Immybot.Backend.Application.Lib.Azure;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Interface.Azure;

public interface IAzureBlobStorageUploadService
{
  // Step 1
  Task PrepareCloudBlockBlobForLicense(string identifier, AzureLicenseUploadData licenseData);
  Task PrepareCloudBlockBlobForSoftware(DatabaseType databaseType, string identifier, AzureUploadData packageData);
  Task PrepareCloudBlockBlobForMedia(DatabaseType databaseType, string identifier, AzureUploadData packageData);
  Task PrepareCloudBlockBlobForPublicMedia(DatabaseType databaseType, string identifier, AzureUploadData packageData);
  Task PrepareCloudBlockBlobForSupportMedia(string identifier, AzureUploadData packageData);

  // Step 2
  Task UploadChunkForBlob(string identifier, IFormFile file);

  // Step 3 (optional)
  string GetMD5Hash(string identifier);

  // Step 3
  Task<string> CommitBlocks(string identifier);

  Task CopyLocalSoftwareBlobFromLocalBlobStorageToGlobalBlobStorage(string blobName);

  Task<bool> DeleteMediaBlobIfExists(Media media);
}
