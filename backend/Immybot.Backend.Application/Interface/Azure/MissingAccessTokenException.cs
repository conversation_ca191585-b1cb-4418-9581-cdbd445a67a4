using System;
using System.Runtime.Serialization;

namespace Immybot.Backend.Application.Interface.Azure;

/// <summary>
/// This is the exception that we throw from the
/// <see cref="Immybot.Backend.Application.Oauth.OauthAccessTokenStore"/> when
/// a permission is required that we don't currently have.
///<br />
/// This gets handled (e.g.) in the frontend by the AuthTokenAcquisition store/modal/logic
/// which prompts the user to consent to the missing scopes
/// </summary>
[Serializable]
public class MissingAccessTokenException : Exception
{
  private const string _defaultMessage = "One or more permission scopes need to be provided";
  public string? TenantId { get; }
  public string? RequiredScopes { get; }
  public string? RequiredClientId { get; }

  public MissingAccessTokenException(string tenantId, string? requiredScopes, string? requiredClientId) : this()
  {
    TenantId = tenantId;
    RequiredScopes = requiredScopes;
    RequiredClientId = requiredClientId;
  }

  public MissingAccessTokenException() : this(_defaultMessage)
  {
  }

  public MissingAccessTokenException(string? message) : base(message)
  {
  }


  public MissingAccessTokenException(string? message, Exception? innerException)
    : base(message, innerException)
  {
  }

  [Obsolete("Obsolete")]
  protected MissingAccessTokenException(SerializationInfo info, StreamingContext context)
    : base(info, context)
  {
    TenantId = (string?)info.GetValue(nameof(TenantId), typeof(string));
    RequiredClientId = (string?)info.GetValue(nameof(RequiredClientId), typeof(string));
    RequiredScopes = (string?)info.GetValue(nameof(RequiredScopes), typeof(string));
  }

  [Obsolete(
    "This API supports obsolete formatter-based serialization. It should not be called or extended by application code.",
    DiagnosticId = "SYSLIB0051",
    UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
  public override void GetObjectData(SerializationInfo info, StreamingContext context)
  {
    base.GetObjectData(info, context);

    info.AddValue(nameof(TenantId), TenantId, typeof(string));
    info.AddValue(nameof(RequiredClientId), RequiredClientId, typeof(string));
    info.AddValue(nameof(RequiredScopes), RequiredScopes, typeof(string));
  }
}
