using System;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Maintenance;
using Microsoft.Extensions.DependencyInjection;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Application.Services;

public class InteractiveScriptExecutionService(
  ICachedSingleton<ApplicationPreferences> cachedAppPrefs,
  IOptions<AppSettingsOptions> appSettings,
  Func<ImmybotDbContext> ctxFactory,
  IServiceScopeFactory serviceScopeFactory,
  IImmyCancellationManager immyCancellationManager)
{
  public async Task<string?> RunInteractive(
  <PERSON><PERSON><PERSON> script,
  ScriptContextParameters runContextScriptParameters,
  Guid cancellationId,
  AuthUserDto? manuallyTriggeredBy,
  CancellationToken cancellationToken,
  Guid? terminalId = null)
  {
    // Here we are creating an entry in the cancellation manager for the cancellationId provided,
    // and marrying it to the web request's cancellation token so that if the web request is cancelled, or
    // the user cancels the script via the API, it gets cancelled
    var immyCancellationToken = immyCancellationManager
      .GetOrCreateScriptCancellationToken(cancellationId);
    using var cts = CancellationTokenSource
      .CreateLinkedTokenSource(immyCancellationToken, cancellationToken);
    try
    {
      var runInFullLanguageMode = appSettings.Value.UseFullLanguagePowerShellModeForInteractiveScriptExecution;

      // There will ultimately be 2 pathways
      // 1. Scripts (Filter specifically) do not require a RunContext and will directly call MetascriptInvoker.RunMetascript()
      // 2. (Default) Scripts will require a RunContext and will use the runContext.RunScript() which ultimately calls MetascriptInvoker.RunMetascript()
      return script.ScriptCategory switch
      {
        ScriptCategory.FilterScriptDeploymentTarget => await RunFilterScriptDeploymentTargetMetascriptInteractive(
          script,
          runContextScriptParameters,
          manuallyTriggeredBy,
          terminalId,
          runInFullLanguageMode,
          cts.Token),
        _ => await RunMetascriptInteractive(
          script,
          runContextScriptParameters,
          manuallyTriggeredBy,
          terminalId,
          runInFullLanguageMode,
          cts.Token),
      };
    }
    finally
    {
      immyCancellationManager.RemoveScriptCancellationToken(cancellationId);
    }
  }

  private async Task<string?> RunMetascriptInteractive(
    Script script,
    ScriptContextParameters scriptContextParameters,
    AuthUserDto? manuallyTriggeredBy,
    Guid? terminalId,
    bool runInFullLanguageMode,
    CancellationToken cancellationToken)
  {
    var scriptTimeouts = cachedAppPrefs.Value.DefaultScriptTimeouts;
    IStageRunContext? stageRunCtx = null;
    IRunContext? runContext = null;
    using var scope = serviceScopeFactory.CreateScope();
    var runContextFactory = scope.ServiceProvider.GetRequiredService<IRunContextFactory>();
    try
    {
      if (scriptContextParameters.Session?.Id is int sessionId)
      {
        using var sessionCtx = await runContextFactory
          .GenerateSessionRunContext(sessionId, null, cancellationToken);

        // if the execution stage is null then don't bother running the script under an action
        if (sessionCtx.ExecutionStage is not null)
        {
          stageRunCtx = await sessionCtx.Extend(sessionCtx.ExecutionStage);
          runContext = stageRunCtx;
        }

        if (scriptContextParameters.Action?.Id is int actionId && stageRunCtx is not null)
        {
          await using var ctx = ctxFactory();
          var action = await ctx.MaintenanceActions.AsNoTracking().FirstOrDefaultAsync(a => a.Id == actionId, cancellationToken: cancellationToken);
          if (action is not null)
          {
            runContext = await stageRunCtx.Extend(action);
          }
        }
      }

      // if we don't have a run context from the session, then creat a one off run context
      if (runContext is null)
      {
        // No existing session (likely run from debugger) must generate RunContext
        if (script.ScriptExecutionContext == ScriptExecutionContext.CloudScript)
        {
          if (scriptContextParameters.Tenant?.Id is not int tenantId || tenantId == 0)
            throw new ArgumentException("ScriptContextParameters must include tenantId if script execution context is CloudScript", nameof(scriptContextParameters));
          runContext = await runContextFactory
            .GenerateTenantRunContext(manuallyTriggeredBy, tenantId, cancellationToken);
        }
        else
        {
          // Must target a computer
          if (scriptContextParameters.Computer?.Id is not int computerId || computerId == 0)
            throw new ArgumentException("ScriptContextParameters must include computerId if script execution context is not CloudScript", nameof(scriptContextParameters));
          // No sessionId, likely run from Computer Details->Terminal tab
          runContext = await runContextFactory
            .GenerateComputerOneOffRunContext(computerId, cancellationToken, manuallyTriggeredBy: manuallyTriggeredBy);
        }
      }

      if (terminalId.HasValue)
      {
        runContext.Args.MetascriptInvoker.SetTerminalId(terminalId.Value);
      }

      var result = await runContext.Args.MetascriptInvoker.RunMetascript<object>(
        runContext.CanAccessMspResources(),
        runContext.CanAccessParentTenant(),
        await script.SetRunContextParameters(runContext, contextParameters: scriptContextParameters, cancellationToken),
        cancellationToken,
        TimeSpan.FromSeconds(script.Timeout ?? scriptTimeouts.Action),
        runContext: runContext,
        againstComputer: scriptContextParameters.Computer,
        runInFullLanguageMode: runInFullLanguageMode);
      return result.ConsoleText;
    }
    finally
    {
      runContext?.Dispose();
      stageRunCtx?.Dispose();
    }
  }

  private async Task<string?> RunFilterScriptDeploymentTargetMetascriptInteractive(
    Script script,
    ScriptContextParameters runContextScriptParameters,
    AuthUserDto? manuallyTriggeredBy,
    Guid? terminalId,
    bool runInFullLanguageMode,
    CancellationToken cancellationToken)
  {
    using var scope = serviceScopeFactory.CreateScope();
    var metascriptInvoker = scope.ServiceProvider.GetRequiredService<IMetascriptInvoker>();
    if (terminalId is not null)
      metascriptInvoker.SetTerminalId(terminalId.Value);

    // ComputerId should be null when running interactively.
    // It is only available as a parameter to limit the output of Get-ImmyComputer to a single computer
    // as a hack to prevent memory bloat when many sessions try to resolve the same filter script simultaneously
    var filterScriptResult = await metascriptInvoker.GetComputersForFilterScript(
      canAccessMspResources: manuallyTriggeredBy?.IsMsp ?? false,
      canAccessParentTenant: runContextScriptParameters.TargetAssignment?.AllowAccessToParentTenant ?? false,
    script,
      cancellationToken,
      limitToTenantId: runContextScriptParameters.Tenant?.Id,
      runInFullLanguageMode: runInFullLanguageMode);
    return filterScriptResult?.ConsoleText;
  }
}
