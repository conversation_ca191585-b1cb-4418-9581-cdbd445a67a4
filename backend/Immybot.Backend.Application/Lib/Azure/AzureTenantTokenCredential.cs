using System.Threading;
using System.Threading.Tasks;
using Azure.Core;
using Azure.Identity;
using Immybot.Backend.Application.Interface.Azure;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.Azure;

internal record TenantAppReg(
  string AppId,
  string AppSecret,
  AzurePermissionLevel2 AzurePermissionLevel,
  bool IsTenantConsented);

/// <summary>
/// Attempts to get an access token:
/// <list type="number">
/// <item>
/// If onlyUsePartnerCenterRefresh is true, only try to get an access token from refreshing an
/// existing partner center OBO token
/// </item>
/// <item>
/// If there's an access token in the store for the given scopes for this tenant, return it
/// </item>
/// <item>
/// If the tenant has been consented to an app registration (custom or default), use that
/// </item>
/// <item>
/// If this tenant is a customer and the parent partner has a refreshable partner center OBO token
/// in the store, use that
/// </item>
/// <item>
/// If the tenant hasn't been consented but the partner has a preferred app registration, use that
/// </item>
/// </list>
/// </summary>
public class AzureTenantTokenCredential : TokenCredential
{
  private static readonly JoinableTaskContext _joinableTaskContext = new();
  private readonly IOauthAccessTokenStore _accessTokenStore;
  private readonly AzureActiveDirectoryAuthOptions _azAdOpts;
  private readonly AppSettingsOptions _appSettingsOptions;

  public string TenantPrincipalId { get; }
  public string? PartnerPrincipalId { get; }
  public AccessTokenSource? GotAccessTokenFrom { get; private set; }
  public string? ResolvedClientId { get; private set; }

  // If this is null, the tenant does not have any azure permission level set
  // so we should not try to get an access token from azure unless there is an
  // access token in the store for the tenant or a refresh token in the store for
  // the tenant's partner tenant
  internal TenantAppReg? TenantAppReg { private get; init; }
  public bool OnlyUsePartnerCenterRefresh { get; internal init; }
  public string? TenantPreferredAzureClientId => TenantAppReg?.AppId;
  public AzurePermissionLevel2? TenantAzurePermissionLevel => TenantAppReg?.AzurePermissionLevel;

  internal AzureTenantTokenCredential(
    string tenantPrincipalId,
    string? partnerPrincipalId,
    IOauthAccessTokenStore accessTokenStore,
    AzureActiveDirectoryAuthOptions azAdOpts,
    AppSettingsOptions appSettingsOptions)
  {
    TenantPrincipalId = tenantPrincipalId;
    PartnerPrincipalId = partnerPrincipalId;
    _accessTokenStore = accessTokenStore;
    _azAdOpts = azAdOpts;
    _appSettingsOptions = appSettingsOptions;
  }
  public override AccessToken GetToken(
    TokenRequestContext requestContext,
    CancellationToken cancellationToken)
  {
    return _joinableTaskContext.Factory.Run(async delegate
    {
      return await GetTokenAsync(requestContext, cancellationToken);
    });
  }

  public override async ValueTask<AccessToken> GetTokenAsync(
    TokenRequestContext requestContext,
    CancellationToken cancellationToken)
  {
    AccessToken? accessToken;

    if (OnlyUsePartnerCenterRefresh)
    {
      // Hint given from partner center preconsent process that we should use a partner center
      // refresh token from the partner tenant to get an access token for this tenant instead of
      // this tenant's preferred app registration
      if (PartnerPrincipalId == null)
        throw new OauthException(
          $"Cannot acquire an access token for an azure customer using a partner " +
          $"center refresh token because there is no partner principal id specified " +
          $"for tenant {TenantPrincipalId}");
      accessToken = await UsePartnerCenterRefresh(
        PartnerPrincipalId,
        requestContext,
        cancellationToken);
      return accessToken.Value;
    }

    // 1
    accessToken = await TryObo(requestContext, cancellationToken);
    if (accessToken != null) return accessToken.Value;

    // 2
    if (TenantAppReg?.IsTenantConsented is true)
    {
      accessToken = await TryTenantAppReg(requestContext, cancellationToken);
      if (accessToken != null) return accessToken.Value;
    }

    // 3
    if (PartnerPrincipalId != null)
    {
      try
      {
        accessToken = await UsePartnerCenterRefresh(
          PartnerPrincipalId,
          requestContext,
          cancellationToken);
        return accessToken.Value;
      }
      catch (MissingAccessTokenException)
      {
        // ignore these, we'll try the tenant's preferred app registration next
      }
    }

    // 4
    if (TenantAppReg?.IsTenantConsented is not true)
    {
      accessToken = await TryTenantAppReg(requestContext, cancellationToken);
      if (accessToken != null) return accessToken.Value;
    }

    throw new MissingAccessTokenException(
      TenantPrincipalId,
      requiredScopes: string.Join(' ', requestContext.Scopes),
      requiredClientId: null);
  }

  private async Task<AccessToken?> TryObo(
    TokenRequestContext requestContext,
    CancellationToken cancellationToken)
  {
    try
    {
      var accessToken = await _accessTokenStore.GetAccessTokenForScopes(
        requestContext.TenantId ?? TenantPrincipalId,
        requestContext.Scopes,
        null,
        cancellationToken);
      GotAccessTokenFrom = AccessTokenSource.Obo;
      ResolvedClientId = accessToken.ClientId;
      return new AccessToken(accessToken.Token, accessToken.ExpiresOn);
    }
    catch (MissingAccessTokenException)
    {
      return null;
    }
  }

  private async Task<AccessToken?> TryTenantAppReg(
    TokenRequestContext requestContext,
    CancellationToken cancellationToken)
  {
    if (TenantAppReg is
      {
        AppId: { } appId,
        AppSecret: { } appSecret,
        AzurePermissionLevel: AzurePermissionLevel2.DefaultAppRegistration or AzurePermissionLevel2.CustomAppRegistration,
      })
    {
      var credential = new ClientSecretCredential(TenantPrincipalId, appId, appSecret);
      GotAccessTokenFrom = TenantAppReg.AzurePermissionLevel == AzurePermissionLevel2.CustomAppRegistration
        ? AccessTokenSource.CustomAppRegistration
        : AccessTokenSource.DefaultAppRegistration;
      ResolvedClientId = appId;
      return await credential.GetTokenAsync(requestContext, cancellationToken);
    }
    return null;
  }

  private async Task<AccessToken> UsePartnerCenterRefresh(
    string partnerPrincipalId,
    TokenRequestContext requestContext,
    CancellationToken cancellationToken)
  {
    // We're authenticating as an azure customer

    // Try using an obo partner center refresh token to generate a new access token for
    // this customer. We specify the client id to use for the new access token in order
    // to ensure we're using the same client that the partner center preconsent process
    // uses during preconsent
    var partnerAccessToken = await _accessTokenStore
      .GenerateAccessTokenFromRefreshTokenForScopes(
        partnerPrincipalId,
        requestContext.TenantId ?? TenantPrincipalId,
        [_appSettingsOptions.PartnerCenterUserImpersonationScope],
        requestContext.Scopes,
        _azAdOpts.ClientId,
        _azAdOpts.ClientId,
        cancellationToken);

    // TODO: set this new access token in the store? Probably not, but we should at least
    // cache it on this instance of ImmyTokenCredential and add in a hint to disable the cache
    // E.g. to allow the caller to generate new access token from a partner center refresh token
    // and then use that same token until the caller indicates it needs to be refreshed again
    // (e.g. when using the token to add new permissions/consent to the customer)
    GotAccessTokenFrom = AccessTokenSource.OboPartnerCenter;
    ResolvedClientId = partnerAccessToken.ClientId;
    return new AccessToken(partnerAccessToken.Token, partnerAccessToken.ExpiresOn);
  }
}
