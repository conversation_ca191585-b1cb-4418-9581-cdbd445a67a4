using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Application.Lib.Azure;

internal class PartnerCenterAuthHeaderHandler : DelegatingHandler
{
  internal const string Oauth2AccessTokenIdRequestOptionKey = "oauth2AccessTokenId";
  internal const string PartnerTenantIdRequestOptionKey = "partnerTenantId";
  internal const string ApplicationClientIdOptionKey = "cpvApplicationId";
  private readonly AppSettingsOptions _appSettings;
  private readonly IOauthAccessTokenStore _accessTokenStore;

  public PartnerCenterAuthHeaderHandler(
    IOptions<AppSettingsOptions> appSettings,
    IOauthAccessTokenStore accessTokenStore)
  {
    _appSettings = appSettings.Value;
    _accessTokenStore = accessTokenStore;
  }

  protected override async Task<HttpResponseMessage> SendAsync(
    HttpRequestMessage request,
    CancellationToken cancellationToken)
  {
    AuthToken accessToken;
    // If the request has an oauth2AccessTokenId property, use that to get the access token
    if (request.Options.TryGetValue(
      new HttpRequestOptionsKey<int>(Oauth2AccessTokenIdRequestOptionKey),
      out var accessTokenId))
    {
      accessToken = await _accessTokenStore
        .GetAccessTokenForOauthAccessTokenId(accessTokenId, cancellationToken);
    }
    // Get the tenantId that was provided to the IPartnerCenterApi method
    else if (request.Options.TryGetValue(
      new HttpRequestOptionsKey<string>(PartnerTenantIdRequestOptionKey),
      out var partnerTenantId))
    {
      request.Options.TryGetValue(
        new HttpRequestOptionsKey<string>(ApplicationClientIdOptionKey),
        out var specificClientId);
      var scope = _appSettings.PartnerCenterUserImpersonationScope;
      accessToken = await _accessTokenStore
        .GetAccessTokenForScopes(partnerTenantId, [scope], specificClientId, cancellationToken);
    }
    else
      throw new NotImplementedException(
        "PartnerCenterAuthHeaderHandler requires a partnerTenantId property " +
        "or oauth2AccessTokenId property to be set on the request");

    if (accessToken.ExpiresOn < DateTimeOffset.UtcNow)
    {
      throw new NotImplementedException(
        "PartnerCenterAuthHeaderHandler does not yet support refreshing access tokens");
    }

    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken.Token);
    return await base.SendAsync(request, cancellationToken);
  }
}
