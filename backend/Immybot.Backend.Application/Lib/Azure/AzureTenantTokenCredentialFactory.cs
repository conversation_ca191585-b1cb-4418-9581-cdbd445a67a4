using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.KeyVaultRepositories;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Application.Lib.Azure;

public interface IAzureTenantTokenCredentialFactory
{
  /// <summary>
  /// If <paramref name="onlyUsePartnerCenterRefresh"/> is true and the tenant is a customer,
  /// the returned credential will only try to get an access token from refreshing an existing
  /// partner center OBO token. Otherwise, it will try to get an access token from the store or
  /// use client credentials flow to get an access token from the tenant's preferred app
  /// registration (custom or default), then fall back to partner center refresh
  /// </summary>
  Task<AzureTenantTokenCredential> GetImmyTokenCredential(
    AzureTenant tenant,
    CancellationToken cancellationToken,
    bool onlyUsePartnerCenterRefresh = false);
}

internal class AzureTenantTokenCredentialFactory(
  IOauthAccessTokenStore _accessTokenStore,
  IAzureTenantAuthDetailsRepository _azureTenantAuthDetailsRepository,
  IOptions<AzureActiveDirectoryAuthOptions> azureActiveDirectoryAuthOptions,
  IOptions<AppSettingsOptions> appSettingsOptions) : IAzureTenantTokenCredentialFactory
{
  // NB: These are not long-lived caches: they are only used to prevent multiple requests for
  // the same tenant during the same request. They are not persisted across requests.
  private readonly Dictionary<string, AzureTenantAuthDetails> _azureTenantAuthDetailsCache = [];
  private readonly SemaphoreSlim _azureTenantAuthDetailsCacheAccessLock = new(1, 1);
  private readonly AzureActiveDirectoryAuthOptions _azAdOpts = azureActiveDirectoryAuthOptions.Value;
  private readonly AppSettingsOptions _appSettingsOptions = appSettingsOptions.Value;

  public async Task<AzureTenantTokenCredential> GetImmyTokenCredential(
    AzureTenant tenant,
    CancellationToken cancellationToken,
    bool onlyUsePartnerCenterRefresh = false)
  {
    var tenantAppReg = await GetTenantPreferredAppReg(
      tenant.AzureTenantType,
      tenant.PrincipalId,
      tenant.PartnerPrincipalId,
      onlyUsePartnerCenterRefresh ? null : tenant.ConsentDetails.ConsentedWith,
      cancellationToken);
    return new AzureTenantTokenCredential(
      tenant.PrincipalId,
      tenant.PartnerPrincipalId,
      _accessTokenStore,
      _azAdOpts,
      _appSettingsOptions)
    {
      OnlyUsePartnerCenterRefresh = onlyUsePartnerCenterRefresh,
      TenantAppReg = tenantAppReg,
    };
  }

  private async Task<TenantAppReg?> GetTenantPreferredAppReg(
    AzTenantType? azureTenantType,
    string principalId,
    string? partnerPrincipalId,
    AppRegistrationType? consentedWith,
    CancellationToken cancellationToken)
  {
    var partnerAuthDetails = await GetPartnerAuthDetails(azureTenantType, principalId, partnerPrincipalId, cancellationToken);
    if (partnerAuthDetails == null) return null;
    if (azureTenantType is AzTenantType.Customer && consentedWith is AppRegistrationType.Backend)
      return new TenantAppReg(_azAdOpts.ClientId, _azAdOpts.ClientSecret, AzurePermissionLevel2.DefaultAppRegistration, consentedWith != null);
    return partnerAuthDetails is
    {
      SelectedPermissionLevel: AzurePermissionLevel2.CustomAppRegistration,
      CustomAppRegAppId: { Length: > 0 } appId,
      CustomAppRegSecret: { Length: > 0 } appSecret,
    }
      ? new TenantAppReg(appId, appSecret, AzurePermissionLevel2.CustomAppRegistration, consentedWith != null)
      : new TenantAppReg(_azAdOpts.ClientId, _azAdOpts.ClientSecret, AzurePermissionLevel2.DefaultAppRegistration, consentedWith != null);
  }

  private async Task<AzureTenantAuthDetails?> GetPartnerAuthDetails(
    AzTenantType? azureTenantType,
    string principalId,
    string? partnerPrincipalId,
    CancellationToken cancellationToken)
  {
    var p = azureTenantType is AzTenantType.Partner or AzTenantType.Standalone or null
      ? principalId
      : partnerPrincipalId;
    if (string.IsNullOrWhiteSpace(p)) return null;

    await _azureTenantAuthDetailsCacheAccessLock.WaitAsync(cancellationToken);
    try
    {
      if (_azureTenantAuthDetailsCache.TryGetValue(p, out var cachedAuthDetails))
        return cachedAuthDetails;

      var authDetails = await _azureTenantAuthDetailsRepository
        .GetAzureTenantAuthDetailsByAzurePrincipalId(p, cancellationToken);

      if (authDetails is not null)
      {
        _azureTenantAuthDetailsCache[p] = authDetails;
      }

      return authDetails;
    }
    finally
    {
      _azureTenantAuthDetailsCacheAccessLock.Release();
    }
  }
}
