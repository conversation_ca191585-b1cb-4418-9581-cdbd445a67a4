using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.PersonExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Persistence;
using Immybot.Shared.JsonDeepEqual;
using Immybot.Shared.Primitives;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Application.Lib;

public class MigrateDeploymentsToProviderLinksService : IMigrateDeploymentsToProviderLinksService
{
  private readonly Func<ImmybotDbContext> _ctxFactory;
  private readonly IDomainEventEmitter _domainEventEmitter;
  private readonly IRecommendedProviderLinksGetter _providerLinksGetter;
  private readonly IRunContextFactory _runContextFactory;
  private readonly ILogger<MigrateDeploymentsToProviderLinksService> _logger;

  public MigrateDeploymentsToProviderLinksService(
    Func<ImmybotDbContext> ctxFactory,
    IDomainEventEmitter domainEventEmitter,
    IRecommendedProviderLinksGetter providerLinksGetter,
    IRunContextFactory runContextFactory,
    ILogger<MigrateDeploymentsToProviderLinksService> logger)
  {
    _domainEventEmitter = domainEventEmitter;
    _ctxFactory = ctxFactory;
    _logger = logger;
    _providerLinksGetter = providerLinksGetter;
    _runContextFactory = runContextFactory;
  }

  public async Task PerformWork(CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogTrace("Starting {ServiceName}", nameof(MigrateDeploymentsToProviderLinksService));
      await using var ctx = _ctxFactory();

      var rootTenant = ctx.GetRootTenant();
      var runContext =
        await _runContextFactory.GenerateTenantRunContext(manuallyTriggeredBy: null, rootTenant.Id, cancellationToken);

      var recommendedProviderLinks = await _providerLinksGetter.GetRecommendedProviderLinks(runContext, cancellationToken);
      _logger.LogTrace("Found {Count} recommended provider links", recommendedProviderLinks.Count);

      var successfulMigrations = new List<MigratableDeploymentResultDto>();
      foreach (var recommendation in recommendedProviderLinks)
      {
        var providerLinkResult = await GetOrCreateProviderLinkFromRecommendation(ctx, recommendation, cancellationToken);
        if (providerLinkResult is null) continue;

        var updateDeploymentsResult = await UpdateRecommendedTargetAssignments(ctx, providerLinkResult.Id, recommendation, cancellationToken);
        if (!updateDeploymentsResult.IsSuccess) continue;

        successfulMigrations.Add(new MigratableDeploymentResultDto(providerLinkResult.ProviderTypeId,
                                                                   providerLinkResult.Id,
                                                                   recommendation.SoftwareName,
                                                                   recommendation.TargetAssignmentIds));
      }

      successfulMigrations.ForEach(migration =>
        _domainEventEmitter.EmitEvent(new IntegrationRecommendationEvent([migration])));
      _logger.LogTrace("Finished {ServiceName}. Migrated {Count} deployments.", nameof(MigrateDeploymentsToProviderLinksService), successfulMigrations.Count);
    }
    catch (Exception e)
    {
      _logger.LogError(e, "Error occurred during {ServiceName}", nameof(MigrateDeploymentsToProviderLinksService));
    }
  }

  internal async Task<ProviderLink?> GetOrCreateProviderLinkFromRecommendation(
    ImmybotDbContext ctx,
    MigratableDeploymentResult result,
    CancellationToken token)
  {
    var providerLink = await CheckForExistingProviderLink(ctx, result, token);
    if (providerLink is not null) return providerLink;

    // If no existing provider link, create a new one
    providerLink = await CreateProviderLinkFromRecommendation(result);

    return providerLink;
  }

  internal async Task<ProviderLink?> CheckForExistingProviderLink(
    ImmybotDbContext ctx,
    MigratableDeploymentResult result,
    CancellationToken token)
  {
    var recommendationParams = result.Params.Deserialize<Dictionary<string, object?>>();
    if (recommendationParams is null) return null;

    var providerLinks = await ctx.ProviderLinks
      .AsNoTracking()
      .Where(a => a.ProviderTypeId == result.IntegrationTypeId)
      .ToListAsync(token);

    foreach (var providerLink in providerLinks)
    {
      var providerLinkFormData = providerLink.ProviderTypeFormData.Deserialize<Dictionary<string, object?>>();
      if (providerLinkFormData is null) continue;

      // If any keys don't match, skip this provider link
      if (recommendationParams.Keys.Any(key => !providerLinkFormData.ContainsKey(key))) continue;

      // If any values don't match, skip this provider link

      var areValuesDifferent = recommendationParams.Any(pair =>
      {
        var key = pair.Key;
        var deploymentValue = pair.Value;
        providerLinkFormData.TryGetValue(key, out var providerLinkValue);

        return !JsonDeepEqualAssert.Equal(deploymentValue, providerLinkValue);
      });

      if (areValuesDifferent)
      {
        continue;
      }

      _logger.LogTrace("Found existing provider link {ProviderLinkId} for recommendation {@Recommendation}.", providerLink.Id, result);
      return providerLink;
    }

    return null;
  }

  internal async Task<ProviderLink?> CreateProviderLinkFromRecommendation(MigratableDeploymentResult result)
  {
    try
    {
      await using var ctx = _ctxFactory();

      var nextProviderLinkNameId = await ctx.ProviderLinks
        .AsNoTracking()
        .CountAsync(a => a.ProviderTypeId == result.IntegrationTypeId);
      var providerLinkName = $"{result.SoftwareName} Integration {nextProviderLinkNameId}";

      var nextProviderLinkId = await ctx.ProviderLinks
        .AsNoTracking()
        .IgnoreQueryFilters()
        .Select(a => a.Id)
        .DefaultIfEmpty()
        .MaxAsync() + 1;

      // Create a provider link
      var newProviderLinkResult = ctx.CreateProviderLink(new ProviderLink()
      {
        Id = nextProviderLinkId,
        Name = providerLinkName,
        OwnerTenantId = ctx.GetRootTenant().Id,
        ProviderTypeId = result.IntegrationTypeId,
        ProviderTypeFormData = JsonSerializer.SerializeToElement(result.Params),

      });
      _logger.LogTrace("Created new provider link {ProviderLinkId} for recommendation {@Recommendation}.", newProviderLinkResult.Id, result);
      return newProviderLinkResult;
    }
    catch (Exception e)
    {
      _logger.LogError(e, "Failed to create provider link from recommendation {@Recommendation}.", result);
    }

    return null;
  }

  internal async Task<OpResult> UpdateRecommendedTargetAssignments(
    ImmybotDbContext ctx,
    int newProviderLinkId,
    MigratableDeploymentResult result,
    CancellationToken token)
  {
    foreach (var ta in result.TargetAssignmentIds)
    {
      try
      {
        var deployment = await ctx.TargetAssignments.FirstOrDefaultAsync(a => a.Id == ta, token);
        if (deployment is null) continue;

        deployment.ProviderLinkIdForMaintenanceItem = newProviderLinkId;
        deployment.TaskParameterValues = null;
      }
      catch (Exception e)
      {
        _logger.LogError(e, "Failed to migrate target assignment id {Id}.", ta);
        return OpResult.Fail(e, "Failed to migrate target assignment");
      }
    }

    await ctx.SaveChangesAsync(token);

    _logger.LogTrace("Successfully migrated {Count} target assignments to new provider link {IntegrationTypeId}.", result.TargetAssignmentIds.Count, result.IntegrationTypeId);
    return OpResult.Ok();
  }
}
