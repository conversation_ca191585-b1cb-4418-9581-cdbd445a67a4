using System;
using System.Collections.Generic;
using System.Linq;
using System.Management.Automation;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Lib.MetaScripts.Modules;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts;

internal abstract class RunContextPSCmdlet : ServiceScopePSCmdlet
{
  protected Guid? ScriptOutputCorrelationId { get; private set; }

  // should never be null since we perform a null check in BeginProcessing()
  protected IRunContext RunContext { get; private set; } = default!;

  protected void ProcessComputer(PSComputer[]? psComputers, Action<Computer> action)
  {
    CancellationToken.ThrowIfCancellationRequested();
    var computers = new List<Computer>();

    if ((psComputers == null && RunContext.IsComputerTarget) || (psComputers?.Length == 1 && psComputers[0].Id == RunContext.Args.Computer?.Id))
    {
      if (RunContext.Args.Computer is null)
      {
        throw new ComputerNotFoundException($"This session is for a computer, but the computer could not be found. Contact ImmyBot support for assistance.");
      }
      computers.Add(RunContext.Args.Computer);
    }
    else if (psComputers is not null)
    {
      computers.AddRange(LocalDbContext.GetComputersByIds(psComputers.Select(c => c.Id).ToList(), includeAgents: true));
    }
    else if (RunContext.IsTenantTarget)
    {
      if (!this.TryGetVariableValue(SessionStateEntries.ComputerVariableEntry, out var psObjectComputer))
        throw new ComputerNotFoundException(
          "The computer provided from the cloud script was not a PSObject. Contact ImmyBot support for assistance.");

      if (psObjectComputer.BaseObject is not PSComputer psComputer)
        throw new ComputerNotFoundException("The computer provided from the cloud script was not a PSComputer. Contact ImmyBot support for assistance.");

      var c = LocalDbContext.GetComputerById(psComputer.Id, asNoTracking: true,
        includeAgents: true);

      if (c is null) throw new ComputerNotFoundException($"Computer #{psComputer.Id} '{psComputer.Name} could not be found.");
      computers.Add(c);
    }
    else
    {
      throw new ComputerNotFoundException("A computer to execute against was not provided.");
    }

    var canAccessParentTenant = ValidateCanUseParentTenant(out var parentTenant, true);
    foreach (var computer in computers)
    {
      CancellationToken.ThrowIfCancellationRequested();
      // ensure computer's tenantId matches the run context's tenantId
      if (computer.TenantId != RunContext.TenantId)
      {
        if (canAccessParentTenant && parentTenant != null && computer.TenantId == parentTenant.Id)
        {
          // the computer belongs to the parent tenant and access is allowed
          action.Invoke(computer);
        }
        else
        {
          WriteError(
            new ErrorRecord(
              new Exception("Cross-Tenant Exception"),
              "CrossTenantException",
              ErrorCategory.InvalidOperation,
              computer));
          continue;
        }
      }
      else
      {
        // computer belongs to the current tenant
        action.Invoke(computer);
      }
    }
  }

  protected override void BeginProcessing()
  {
    base.BeginProcessing();

    if (!this.TryGetVariableValue(SessionStateEntries.RunContextVariableEntry, out var runContext))
    {
      throw new ArgumentException($"'RunContext' variable is missing or is not of type {nameof(IRunContext)}");
    }

    RunContext = runContext;

    ScriptOutputCorrelationId = this.TryGetVariableValueNullable(SessionStateEntries.ScriptOutputCorrelationIdVariableEntry);
  }

  /// <summary>
  /// Will cancel the execution of the entire session this script is running in.
  /// NOTE: This will throw an <see cref="InvalidOperationException"/> if the script is not running in the context of a session (RunContext is not of type <see cref="ActionRunContext"/>).
  /// </summary>
  protected void CancelSession()
  {
    var sessionId = (RunContext is ActionRunContext r) ? r.SessionId : throw new InvalidOperationException("Cannot cancel session if script is not running in the context of a session.");
    var cancellationManager = TryGetService<IImmyCancellationManager>();
    new JoinableTaskContext().Factory.Run(async () => await cancellationManager.CancelSession(sessionId));
  }
}
