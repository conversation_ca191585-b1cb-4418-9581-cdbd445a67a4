using System.Runtime.InteropServices;
using Immybot.Shared.Services.Startup;
using MonoMod.RuntimeDetour;

namespace Immybot.Backend.Application.Lib.MetaScripts;

public class PowerShellHookService : EarlyStartupHostedService
{
  // MUST be static to apply hooks to all threads
  private static readonly List<Hook> _hooks = new();

  public override async Task StartingAsync(CancellationToken cancellationToken)
  {
    await Task.Yield();

    // This is a workaround for the ARM architecture, which doesn't work with the hook library.
    // Metascripts will not work on ARM until this is resolved.
    if (RuntimeInformation.ProcessArchitecture == Architecture.X64)
    {
      _hooks.Add(PowerShellHookFactory.BindParameterHook());
      _hooks.Add(PowerShellHookFactory.LookupCommandProcessorHook());
      _hooks.Add(PowerShellHookFactory.GetProviderInstanceHook());
      _hooks.Add(PowerShellHookFactory.GetSystemLockdownPolicyHook());
      _hooks.Add(PowerShellHookFactory.GetDynamicParametersHook());
      _hooks.Add(PowerShellHookFactory.LoadAssemblyFromPathHook());
      _hooks.Add(PowerShellHookFactory.GetCommandDiscoveryPreferenceHook());
      _hooks.Add(PowerShellHookFactory.GetModulePathHook([
        MetascriptInvokerDefaults.PSModulePath,
        MetascriptInvokerDefaults.PSESBundledModulesPath,
      ]));
      _hooks.Add(PowerShellHookFactory.ModuleCmdletBaseGetResolvedPathHook());
      _hooks.Add(PowerShellHookFactory.MakePathHook());


      _hooks.Add(PowerShellHookFactory.ResolveRootedFilePathHook());
      _hooks.Add(PowerShellHookFactory.ItemExistsHook());
      _hooks.Add(PowerShellHookFactory.CompleteFileNameHook());
      _hooks.Add(PowerShellHookFactory.ItemExistsDynamicParameterHook());
      _hooks.Add(PowerShellHookFactory.PSMethodInvocationConstraintsGetHashCodeHook());
      _hooks.Add(PowerShellHookFactory.ValidateCompatibleLanguageModeHook());
    }
  }

  public override async Task StoppedAsync(CancellationToken cancellationToken)
  {
    try
    {
      foreach (var hook in _hooks)
        hook.Dispose();

      _hooks.Clear();
    }
    catch (Exception)
    {
      // ignore
    }
  }
}
