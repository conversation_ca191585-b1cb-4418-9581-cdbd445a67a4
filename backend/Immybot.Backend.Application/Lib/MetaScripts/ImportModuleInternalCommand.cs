using System;
using System.IO;
using System.Linq;
using System.Management.Automation;
using System.Management.Automation.Runspaces;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Scripts;
using Microsoft.PowerShell.Commands;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts;

[Cmdlet(VerbsData.Import, "Module")]
internal class ImportModuleInternalCommand : ServiceScopePSCmdlet
{
  [Parameter(Position = 0, Mandatory = true, ValueFromPipeline = true)]
  public string Name { get; set; } = string.Empty;
  [Parameter()]
  public SwitchParameter PassThru { get; set; } = false;
  [Parameter()]
  public SwitchParameter Force { get; set; } = false;
  [Parameter()]
  public Version? RequiredVersion { get; set; }

  private const string _azureAdStandardPreview = "AzureAD.Standard.Preview";
  private const string _azureAdShort = "AzureAD";

  protected override void ProcessRecord()
  {
    if (Array.Exists(MetascriptInvokerDefaults.ApprovedModules,
          approvedModuleName => Name.Contains(approvedModuleName, StringComparison.CurrentCultureIgnoreCase)))
    {
      try
      {
        // if we are dealing with azure ad, then convert the name to the actual path to the module
        if (string.Equals(Name, _azureAdStandardPreview, StringComparison.CurrentCultureIgnoreCase) || string.Equals(Name, _azureAdShort, StringComparison.CurrentCultureIgnoreCase))
        {
          Name = Path.Combine(MetascriptInvokerDefaults.ModulesPath,
            _azureAdStandardPreview);
        }

        using var ps = PowerShell.Create(RunspaceMode.CurrentRunspace);
        var command = new Command(new CmdletInfo("Import-ModuleInternal", typeof(ImportModuleCommand)),  false)
        {
          /// DK 2024-06-13 Line of code that is where they set the visibility of a command. https://github.com/PowerShell/PowerShell/blob/bd8b0bd42163a9a6f3fc32001662d845b7f7fff0/src/System.Management.Automation/engine/Modules/ModuleCmdletBase.cs#L7033
          /// We need to set the visibility of the command to public so that it can be used in the current runspace
          CommandInfo = { Visibility = SessionStateEntryVisibility.Public }
        };
        ps.AddCommand(command).AddParameter("Name", Name).Invoke();
        ps.AddCommand("Import-ModuleInternal").AddParameter("Name", Name).Invoke();
      }
      catch (CmdletInvocationException ex)
      {
        WriteError(ex.ErrorRecord);
      }
    }
    else if (!Name.EndsWith(".psm1") && !Name.EndsWith(".psd1") && !Name.EndsWith(".dll"))
    {
      bool includeLocalScripts = this.TryGetVariableValueNullable(SessionStateEntries.IncludeLocalScriptsVariableEntry) ?? false;
      int? limitToTenantId = this.TryGetVariableValueNullable(SessionStateEntries.LimitToScriptsForTenantVariableEntry);
      var joinableContext = new JoinableTaskContext();
      Script? module = joinableContext.Factory.Run(async () =>
      {
        return await this.FindScriptByNameCmd.RunAsync(
          args: new Interface.Commands.FindScriptByNameCmdPayload(Name, ScriptCategory.Module, ScriptLanguage.PowerShell, GlobalOnly: !includeLocalScripts, LimitToScriptsForTenant: limitToTenantId),
          token: CancellationToken
        );
      });

      if (module != null)
      {
        using var ps = PowerShell.Create(RunspaceMode.CurrentRunspace);
        ///
        /// DK 2023-02-21 - Note to future self. You will be tempted to pipe this into Import-ModuleInternal (the real Import-Module)
        ///  You will want to do this because you read Example 4: Make a dynamic module available to Get-Module on this page:
        ///   https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/new-module?view=powershell-7.3#example-4-make-a-dynamic-module-available-to-get-module
        /// You will lose 3 hours of your life trying to figure out why you can't call the functions in the module
        /// You will discover that in InitialSessionState.cs:~3214 the Visibility is set to this.DefaultCommandVisibility which is Private for ConstrainedLanguageMode presumably
        /// You will find that private property ps.Runspace.SessionState*._currentScope._functions contains the function you are looking for and sure enough, the visibility is private
        /// Adding -PassThru doesn't work
        /// Adding Export-ModuleMember -Function * doesn't work
        /// Import-ModuleInternal -Global doesn't work
        /// Import-ModuleInternal -Force doesn't work
        /// Import-ModuleInternal -Scope Local doesn't work
        /// Import-ModuleInternal -Scope Global doesn't work
        /// Import-ModuleInternal -Function * doesn't work
        ps.AddCommand("New-Module").AddParameter("Name", Name).AddParameter("ScriptBlock", ScriptBlock.Create(module.Action)).Invoke();
      }
    }
    else
    {
      base.WriteError(new ErrorRecord(new Exception($"Loading of module {Name} blocked. File based modules are not allowed in the Metascript context"), "", ErrorCategory.PermissionDenied, Name));
    }
  }
}
