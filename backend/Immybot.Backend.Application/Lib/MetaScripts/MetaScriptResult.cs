using System.Management.Automation;
using Newtonsoft.Json;
using static Microsoft.PowerShell.Commands.JsonObject;

namespace Immybot.Backend.Application.Lib.MetaScripts;

public class MetaScriptResultBase
{
  internal static readonly ConvertToJsonContext JsonContext = new(5, false, true);

  /// <summary>
  /// The errors the powershell runspace
  /// </summary>
  public List<ErrorRecord> ErrorCollection { get; set; } = [];

  public ErrorRecord? TerminatingErrorRecord { get; set; }
  public Exception? TerminatingException => TerminatingErrorRecord?.Exception;

  /// <summary>
  /// True if the error stream is not empty
  /// </summary>
  public bool HadTerminatingException => TerminatingErrorRecord != null;

  /// <summary>
  /// output as it would appear in the console
  /// </summary>
  public string? ConsoleText { get; set; }

  public MetaScriptResultBase(
    List<ErrorRecord>? errorCollection,
    string? consoleText,
    ErrorRecord? terminatingErrorRecord = null)
  {
    ErrorCollection = new List<ErrorRecord>(errorCollection ?? []);
    try
    {
      TerminatingErrorRecord = terminatingErrorRecord;
      ConsoleText = consoleText?.Replace("\r\n", "\n");
    }
    catch (Exception ex)
    {
      ErrorCollection?.Add(new ErrorRecord(ex,
        ex.GetType().ToString(),
        ErrorCategory.ParserError,
        null));
    }
  }

  /// <summary>
  /// Converts the error stream into a json string
  /// </summary>
  /// <returns></returns>
  public string GetErrorString()
  {
    if (TerminatingErrorRecord == null)
      return string.Empty;

    // Script explicitly threw an error (e.g., "throw 'my error message'")
    if (TerminatingErrorRecord.CategoryInfo.Category == ErrorCategory.OperationStopped)
    {
      return TerminatingErrorRecord.Exception?.Message ?? string.Empty;
    }

    // System error occurred (e.g., missing script, failed operation)
    // Show full error details including context
    return TerminatingErrorRecord.ToString();
  }
}

public class MetaScriptResult<T> : MetaScriptResultBase
{
  private static T? ConvertTo(object obj)
  {
    // handle bool return type specially
    // we don't need to deserialize json for this which would cost us at around a 10x memory overhead and 4x cpu overhead at minimum
    if (typeof(T) == typeof(bool))
    {
      return obj switch
      {
        bool b => (T)Convert.ChangeType(b, typeof(T)),
        _ => (T?)Convert.ChangeType(bool.Parse(obj.ToString() ?? string.Empty), typeof(T))
      };
    }

    try
    {
      // try to cast first, then do json serialization if that fails
      return (T)obj;
    }
    catch (NullReferenceException ex)
    {
      throw new InvalidCastException($"Unable to cast $null to {typeof(T).Name}", ex);
    }
    catch (InvalidCastException)
    {
      try
      {
        string serializedOutput;

        // don't try to serialize types.  It can cause OOM exceptions
        if (obj is Type type)
        {
          serializedOutput = type.FullName ?? string.Empty;
        }
        else
        {
          serializedOutput = ConvertToJson(obj, JsonContext);
        }

        // If our type is DynamicVersion[] we need to add square brackets
        // If we don't, the deserializer will fail since it expects an array
        // TODO: There has to be a better way to do this
        if (typeof(T).Name == "DynamicVersion[]")
        {
          // Add square brackets to seralizedOutput
          serializedOutput = $"[{serializedOutput}]";
        }

        return JsonConvert.DeserializeObject<T>(serializedOutput);
      }
      catch (JsonReaderException ex)
      {
        string message = ex.Message;
        if (message.Contains("Path"))
          message = message.Split("Path")[0];
        throw new InvalidCastException(message, ex);
      }
    }
  }

  public MetaScriptResult(
    List<object>? outputCollection,
    List<ErrorRecord>? errorCollection,
    string? consoleText,
    ErrorRecord? terminatingErrorRecord = null) : base(
    errorCollection,
    consoleText,
    terminatingErrorRecord)
  {
    try
    {
      if (outputCollection == null || !outputCollection.Any())
      {
        return;
      }

      foreach (var item in outputCollection)
      {
        try
        {
          var converted = ConvertTo(item);
          if (converted is not null)
            OutputAsCollection.Add(converted);
        }
        catch (Exception ex)
        {
          ErrorCollection.Add(new ErrorRecord(ex, ex.Message, ErrorCategory.ParserError, item));
        }
      }
    }
    catch (Exception ex)
    {
      ErrorCollection.Add(new ErrorRecord(ex, ex.GetType().ToString(), ErrorCategory.ParserError, null));
    }
  }

  /// <summary>
  /// output as a collection of objects
  /// </summary>
  public ICollection<T> OutputAsCollection { get; } = new List<T>();

  /// <summary>
  /// output as a single object
  /// </summary>
  public T? OutputAsObject => OutputAsCollection.LastOrDefault();


  public string GetSerializedOutputAsObject() => ConvertToJson(OutputAsObject, JsonContext);
  public string GetSerializedOutputAsCollection() => ConvertToJson(OutputAsCollection, JsonContext);
}
