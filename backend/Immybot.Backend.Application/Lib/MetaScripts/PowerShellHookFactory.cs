using System.Collections.ObjectModel;
using System.Management.Automation;
using System.Management.Automation.Internal;
using System.Management.Automation.Provider;
using System.Management.Automation.Security;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.Loader;
using System.Security;
using System.Text.Json;
using Immybot.Backend.Domain.Models;
using Microsoft.PowerShell.Commands;
using MonoMod.RuntimeDetour;
using ExecutionContext = System.Management.Automation.ExecutionContext;

namespace Immybot.Backend.Application.Lib.MetaScripts;

public static class PowerShellHookFactory
{
  private static readonly JsonSerializerOptions options = new()
  {
    Converters = { new ObjectToInferredTypesJsonConverter() }
  };

  /// <summary>
  /// This hook forces all powershell to run untrusted and in constrained language mode
  /// unless InitialSessionState.LanguageMode is set to FullLanguage. (We do this in PSES to allow for full language mode)
  /// e.g. Foreach-Object -Parallel typically runs in FullLanguage mode, but this hook reverts it to ConstrainedLanguage mode
  /// </summary>
  public static Hook GetSystemLockdownPolicyHook() => new(
    source: () => SystemPolicy.GetSystemLockdownPolicy(),
    target: () => SystemEnforcementMode.Enforce
  );

  /// <summary>
  /// Hooking here to prevent all filesystem access for non-global scripts.
  /// We need to ensure that modules loaded from the filesystem such as AzureAD.Standard.Preview.psm1 are able
  /// to load and that PSES can load.
  /// </summary>
  /// <returns></returns>
  /// <exception cref="SecurityException"></exception>
  public static Hook GetProviderInstanceHook() => new(
    source: () => default(SessionStateInternal)!.GetProviderInstance(default(ProviderInfo)),
    target: (Func<SessionStateInternal, ProviderInfo, CmdletProvider> orig,
      SessionStateInternal @this,
      ProviderInfo provider) =>
    {
      if (provider.FullName == "Microsoft.PowerShell.Core\\FileSystem")
      {
        PSVariable? databaseType = @this.GetVariable("DatabaseType");
        PSVariable? scriptName = @this.GetVariable("ScriptName");
        // If the script name is null, then we are likely in pses.
        // All scripts we run in immy should have a non-null name.
        if (databaseType?.Value is DatabaseType.Global || scriptName?.Value is null)
        {
          return orig(@this, provider);
        }
      }

      throw new SecurityException("Cannot access filesystem");
    }
  );

  /// <summary>
  /// We hook here to prevent all application commands from being executed, such as /bin/bash on linux, or docker.exe
  /// </summary>
  /// <returns></returns>
  /// <exception cref="SecurityException"></exception>
  public static Hook LookupCommandProcessorHook() => new(
    source: () =>
      default(CommandDiscovery)!.LookupCommandProcessor(default, default, default, default),
    target: (
      Func<CommandDiscovery, CommandInfo, CommandOrigin, bool?, SessionStateInternal,
        CommandProcessorBase> orig,
      CommandDiscovery @this,
      CommandInfo commandInfo,
      CommandOrigin commandOrigin,
      bool? useLocalScope,
      SessionStateInternal sessionStateInternal) =>
    {
      if (commandInfo.CommandType is CommandTypes.Application)
        throw new SecurityException("Cannot access filesystem");
      return orig(@this, commandInfo, commandOrigin, useLocalScope, sessionStateInternal);
    }
  );

  /// <summary>
  /// Used to convert JSON objects at the PowerShell level
  /// This allows for Splatting parameters provided by the form to functions
  /// Example:
  /// param($a, $b)
  /// Test-Function @PSBoundParameters
  /// </summary>
  /// <returns></returns>
  public static Hook BindParameterHook()
  {
    return new(
    source: () => default(ParameterBinderBase)!.BindParameter(default(CommandParameterInternal), default(CompiledCommandParameter), default(ParameterBindingFlags)),
    target: (Func<ParameterBinderBase, CommandParameterInternal, CompiledCommandParameter, ParameterBindingFlags, bool> orig, ParameterBinderBase @this, CommandParameterInternal parameter, CompiledCommandParameter parameterMetadata, ParameterBindingFlags flags)
    =>
    {
      try
      {
        var converted = parameter.ArgumentValue;
        if (converted is ParameterValue parameterValue)
        {
          converted = parameterValue.Value;
        }

        if (converted is JsonElement jsonElement)
        {
          converted = (jsonElement.ValueKind switch
          {
            // JsonElement.Deserialize will throw an exception when the target type is SwitchParameter
            // It does this without even trying to pass the value to  our ObjectToInferredTypesJsonConverter
            // Therefore we need to handle True and False values ourselves
            JsonValueKind.True => jsonElement.GetBoolean(),
            JsonValueKind.False => jsonElement.GetBoolean(),
            JsonValueKind.String when parameterMetadata.Type == typeof(DateTime) => jsonElement.GetDateTime(),
            JsonValueKind.Null when parameterMetadata.Type == typeof(bool) => false,
            _ => jsonElement.Deserialize(parameterMetadata.Type, options)
          });
          parameter.SetArgumentValue(null, converted);
        }
        return orig(@this, parameter, parameterMetadata, flags);
      }
      catch
      {
        return orig(@this, parameter, parameterMetadata, flags);
      }
    }
  );
  }

  /// <summary>
  /// Prevents leaking filesystem information via tabcompletion2 "/"
  /// Also prevents FileSystem lookups all together when path-like strings are passed to tab completion
  /// </summary>
  /// <returns></returns>
  [MethodImpl(MethodImplOptions.NoOptimization | MethodImplOptions.NoInlining)]
  public static Hook CompleteFileNameHook() => new Hook(
          // Prevents leaking filesystem information via tab completion
          source: () => CompletionCompleters.CompleteFilename(default, default, default),
          target: (Func<CompletionContext, bool, HashSet<string>, IEnumerable<CompletionResult>> orig, CompletionContext context, bool containerOnly, HashSet<string> extension) =>
          {
            // Prevent file system lookup
            return CommandCompletion.EmptyCompletionResult;
          });

  [MethodImpl(MethodImplOptions.NoOptimization | MethodImplOptions.NoInlining)]
  public static Hook GetCommandDiscoveryPreferenceHook() => new(
            source: () => CommandDiscovery.GetCommandDiscoveryPreference(default, default, default),
            target: (ExecutionContext context, VariablePath variablePath, string environmentVariable) =>
            {
              return PSModuleAutoLoadingPreference.ModuleQualified;
            }
            );
  /// <summary>
  /// Normally GetModulePath checks if $env:PSModulePath is not set and sets it as appropriate. (Based on Windows/Linux and the User Profile)
  ///
  /// This implementation returns the provided paths instead of the default paths
  ///
  /// Normally this would return
  /// </summary>
  /// <param name="paths"></param>
  /// <returns></returns>
  [MethodImpl(MethodImplOptions.NoOptimization | MethodImplOptions.NoInlining)]
  public static Hook GetModulePathHook(List<string> paths) => new(
            source: () => ModuleIntrinsics.GetModulePath(default(bool), default(ExecutionContext)),
            target: (Func<bool, ExecutionContext, IEnumerable<string>> orig, bool checkModulePath, ExecutionContext context) =>
            {
              return paths;
            }
            );
  /// <summary>
  /// Normally SessionStateInternal.GetProvider validates the private readonly PSSnapinName equals the ModuleName, preventing Core providers like the FileSystem from being replaced
  ///   - The PSSnapInName comes from the initial loading of the assembly containing the FileSystem provider
  /// Hooking this method allows us to replace the FileSystem provider with a custom provider
  /// </summary>
  /// <returns></returns>
  [MethodImpl(MethodImplOptions.NoOptimization | MethodImplOptions.NoInlining)]
  public static Hook GetProviderHook() => new(
          source: () => default(SessionStateInternal)!.GetProvider(default(PSSnapinQualifiedName)),
          target: (Func<SessionStateInternal, PSSnapinQualifiedName, Collection<ProviderInfo?>> orig, SessionStateInternal @this, PSSnapinQualifiedName providerName) =>
          {
            Collection<ProviderInfo?>? result = null;
            if (providerName.ShortName.EndsWith("FileSystem"))
            {
              result = [@this.ProviderList.FirstOrDefault(a => a.FullName.EndsWith("FileSystem"))];
            }
            else
            {
              result = orig(@this, providerName);
            }
            return result;
          }
          );
  /// <summary>
  /// Fixes ProviderNotFoundException that would happen when hovering over cmdlets.
  /// Happens because the FileSystem provider is Private
  ///  - This feels like a bug in PowerShell's LocationGlobber because it ironically doesn't happen if the provider isn't loaded, but then there are module loading issues
  /// </summary>
  /// <returns></returns>
  [MethodImpl(MethodImplOptions.NoOptimization | MethodImplOptions.NoInlining)]
  public static Hook GetProviderPathHook() => new(
    source: typeof(LocationGlobber).GetMethod(nameof(LocationGlobber.GetProviderPath),
      BindingFlags.Instance | BindingFlags.NonPublic,
      [
        typeof(string), typeof(CmdletProviderContext), typeof(ProviderInfo).MakeByRefType(),
        typeof(PSDriveInfo).MakeByRefType()
      ])!,
          target: (LocationGlobber @this, string path, CmdletProviderContext context, out ProviderInfo provider, out PSDriveInfo drive) =>
          {
            return @this.GetProviderPath(path: path, context: context, isTrusted: true, provider: out provider, drive: out drive);
          });
  /// <summary>
  /// Short circuits provider logic for ItemExistsDynamicParameter
  /// </summary>
  /// <returns></returns>
  [MethodImpl(MethodImplOptions.NoOptimization | MethodImplOptions.NoInlining)]
  public static Hook ItemExistsDynamicParameterHook()
  {
    return new(
              source: () => default(SessionStateInternal)!.ItemExistsDynamicParameters(default, default),
            target: (SessionStateInternal sessionStateInternal, string path, CmdletProviderContext context) =>
            {
              return (object)Path.Exists(path);
            }
          );
  }

  /// <summary>
  /// Fixes a memory-leak bug where PSInvokeMemberBinder.s_binderCache grows enormously introduced in Powershell 7.3.0 in this PR https://github.com/PowerShell/PowerShell/pull/12412
  /// </summary>
  [MethodImpl(MethodImplOptions.NoOptimization | MethodImplOptions.NoInlining)]
  public static Hook PSMethodInvocationConstraintsGetHashCodeHook() => new(
    source: typeof(PSMethodInvocationConstraints).GetMethod(nameof(GetHashCode))!,
    target: (PSMethodInvocationConstraints @this) =>
      HashCode.Combine(
        @this.MethodTargetType,
        @this.ParameterTypes.SequenceGetHashCode(),
        @this.GenericTypeParameters.SequenceGetHashCode()
      ));

  /// <summary>
  /// Short circuits provider logic for ItemExists
  /// </summary>
  /// <returns></returns>
  [MethodImpl(MethodImplOptions.NoOptimization | MethodImplOptions.NoInlining)]
  public static Hook ItemExistsHook() => new(source: () => default(SessionStateInternal)!.ItemExists(default, default),
            target: (SessionStateInternal @this, string path, CmdletProviderContext context) =>
            {
              return Path.Exists(path);
            });


  /// <summary>
  /// Prevents calling FileSystemProvider's implementation of MakePath (which may be expensive)
  /// </summary>
  /// <returns></returns>
  [MethodImpl(MethodImplOptions.NoOptimization | MethodImplOptions.NoInlining)]
  public static Hook MakePathHook() => new Hook(
           source: () => default(SessionStateInternal)!.MakePath(default, default, default),
           target: (SessionStateInternal sessionStateInternal, string parent, string child, CmdletProviderContext context) =>
           {
             // Don't return null if the path doesn't exist, this leads to PSArgumentNullException when hovering on Functions/Cmdlets
             return Path.Combine(parent, child);
           }
           );
  /// <summary>
  /// Replaces ModuleCmdletBase.GetResolvedPath with Path.GetFullPath
  /// This may introduce bugs as Path.GetFullPath does not expand wildcards like ModuleCmdletBase.GetResolvedPath, however this is desirable for security reasons
  /// </summary>
  /// <returns></returns>
  [MethodImpl(MethodImplOptions.NoOptimization | MethodImplOptions.NoInlining)]
  public static Hook ModuleCmdletBaseGetResolvedPathHook() => new Hook(
            source: () => ModuleCmdletBase.GetResolvedPath(default, default),
            target: (string filePath, ExecutionContext context) =>
            {
              return Path.GetFullPath(filePath);
            });

  /// <summary>
  /// Prevents recursive directory traversal on module load
  /// </summary>
  /// <returns></returns>
  [MethodImpl(MethodImplOptions.NoOptimization | MethodImplOptions.NoInlining)]
  public static Hook ResolveRootedFilePathHook() => new Hook(
      source: () => ModuleCmdletBase.ResolveRootedFilePath(default, default),
      target: (Func<string, ExecutionContext, string> orig, string path, ExecutionContext context) =>
      {
        if (string.IsNullOrEmpty(path))
          return null;
        try
        {
          if (Path.IsPathFullyQualified(path))
          {
            if (Path.Exists(path))
              return path;
            else
              return null;
          }
          else
          {
            var resolvedPath = Path.Combine(MetascriptInvokerDefaults.AssemblyFolder, path);
            if (Path.Exists(resolvedPath))
              return resolvedPath;
            resolvedPath = path switch
            {
              "AzureAD.Standard.Preview.psm1" => Path.Combine(MetascriptInvokerDefaults.ModulesPath, "AzureAD.Standard.Preview", "AzureAD.Standard.Preview.psm1"),
              "PSScriptAnalyzer.psm1" => Path.Combine(MetascriptInvokerDefaults.PSESBundledModulesPath, "PSScriptAnalyzer", "PSScriptAnalyzer.psm1"),
              "bin/Core/Microsoft.PowerShell.EditorServices.Hosting.dll" => Path.Combine(MetascriptInvokerDefaults.AssemblyFolder, "Microsoft.PowerShell.EditorServices.Hosting.dll"),
              "PowerShellEditorServices.Commands.psm1" => Path.Combine(MetascriptInvokerDefaults.PSESBundledModulesPath, "PowerShellEditorServices", "Commands", "PowerShellEditorServices.Commands.psm1"),
              _ => Path.Combine(MetascriptInvokerDefaults.AssemblyFolder, "runtimes", OperatingSystem.IsWindows() ? "win" : "unix", "lib", "net9.0", path),
            };
            if (Path.Exists(resolvedPath))
              return resolvedPath;

            return null;
          }

        }
        catch (RuntimeException)
        {
          return path;
        }
      }
      );
  /// <summary>
  /// Enables Incremental Parameter Binding
  /// </summary>
  /// <returns></returns>
  [MethodImpl(MethodImplOptions.NoOptimization | MethodImplOptions.NoInlining)]
  public static Hook GetDynamicParametersHook()
  {
    return new Hook(
    source: () => default(PSScriptCmdlet)!.GetDynamicParameters(),
    target: (Func<PSScriptCmdlet, object?> getDynamicParameters, PSScriptCmdlet psScriptCmdlet) =>
    {
      // The following code is based on the implementation of PSScriptCmdlet.GetDynamicParameters (CompiledScriptBlock.cs:~2407)
      // This appears to be necessary to prevent a NullReferenceException when calling certain methods in the CmdletParameterBinderController
      ScriptBlock _scriptBlock = psScriptCmdlet._scriptBlockRef();
      if (!_scriptBlock.HasDynamicParameters)
        return null;

      List<object> resultList = new();
      var _functionContext = psScriptCmdlet._functionContextRef();

      var parameterBinder = new IncrementalParameterBinder(psScriptCmdlet);
      _functionContext._outputPipe = new Pipe();
      using var objectStream = new ObjectStream();
      var objectWriter = new ObjectWriter(objectStream);
      // Handle the output from the dynamic parameter block as the parameters are emitted
      // If the emitted object is a RuntimeDefinedParameter, then bind it immediately
      // Otherwise, add it to the resultList and let PowerShell handle the binding
      // Note: This won't get hit if the parameters are created with New-Parameter unless -PassThru or -SkipBind is specified and the user emits the returned object
      // New-Parameter handles the binding itself so incremental binding can work with legacy syntax New-ParameterCollection @(New-Parameter -Name "Name" -Type "String")
      // New syntax can omit New-ParameterCollection and simply use New-Parameter -Name "Name" -Type "String" directly
      // Additionally, this allows the user to use Get-Command to retrieve the parameters from a different function, convert them to RuntimeDefinedParameters, and use them in a calling function
      // We have a helper function in global called Get-CommandParameters that does this
      // TODO: Since the primary use case for this is parameter reuse, a better way to do this would be to allow users to create classes that inherit from PSCmdlet via a new 'class' ScriptCategory
      objectStream.DataReady += (sender, args) =>
      {
        var thisObjectStream = sender as ObjectStream;
        while (thisObjectStream?.Count > 0)
        {
          var obj = PSObject.Base(thisObjectStream.Read());

          if (obj is RuntimeDefinedParameter runtimeDefinedParameter)
          {
            parameterBinder.BindRuntimeDefinedParameter(runtimeDefinedParameter);
          }
          else
          {
            resultList.Add(obj);
          }
        }
      };
      _functionContext._outputPipe.ExternalWriter = objectWriter;
      var dict = new PSBoundParametersDictionary();
      foreach (var a in parameterBinder.UnboundArguments)
      {
        // Prevent null reference exception when the parameters are specified via commandline and are still separated by spaces
        // We try to avoid this by using .AddParameters() but it will happen during intellisense/tabcompletion
        if (a.ParameterNameSpecified)
          dict.Add(a.ParameterName, a.ArgumentValue);
      }

      psScriptCmdlet.InternalState.Internal.SetVariableAtScope(
          new PSVariable("Arguments", dict),
          "global",
          true,
          CommandOrigin.Internal);

      // store the parameter binder as a global variable so that nested scripts can access it
      psScriptCmdlet.InternalState.Internal.SetVariableAtScope(
        new PSVariable("ParameterBinderController", parameterBinder),
        "global",
        true,
        CommandOrigin.Internal);
      var clause = psScriptCmdlet._runOptimizedRef() ? _scriptBlock.DynamicParamBlock : _scriptBlock.UnoptimizedDynamicParamBlock;
      // This is the operative change: passing parameterBinderHelper in via $_
      // This is not easily obtained from within New-Parameter as the CurrentCommandProcessor is overridden in a stack variable by the nature of having called another commands
      // The overwriting of the command processor happens in DoPrepare, which is after the command's construction but before BeginProcessing
      // Hooking GetDynamicParameters allows us to inject the parameterBinderHelper into the dynamic parameter block
      psScriptCmdlet.RunClause(clause, AutomationNull.Value, AutomationNull.Value);
      // This preserves reverse compatibility with the default behavior of returning the result of the dynamic parameter block when not using Incremental Binding
      if (resultList is [{ } result])
      {
        return result;
      }
      // This _should_ be null when using Incremental Binding as the parameters were bound as they were emitted from the dynamic parameter block (in DynamicParameterStreamOutputHandler above)
      return (object?)null;
    });
  }

  private static readonly Dictionary<string, Assembly> _loadedAssemblies = new();

  public static Hook LoadAssemblyFromPathHook()
  {
    return new Hook(
        source: () => default(AssemblyLoadContext)!.LoadFromAssemblyPath(default!),
        target: (Func<AssemblyLoadContext, string, Assembly> orig, AssemblyLoadContext @this, string assemblyPath) =>
        {
          lock (_loadedAssemblies)
          {
            if (@this == AssemblyLoadContext.Default &&
                _loadedAssemblies.TryGetValue(assemblyPath, out var alreadyLoadedAssembly))
            {
              return alreadyLoadedAssembly;
            }

            var assembly = orig(@this, assemblyPath);
            if (@this == AssemblyLoadContext.Default)
              _loadedAssemblies[assemblyPath] = assembly;
            return assembly;
          }
        }
    );
  }

  public static Hook ValidateCompatibleLanguageModeHook()
  {
    var methodInfo = typeof(CommandProcessorBase).GetMethod("ValidateCompatibleLanguageMode",
      BindingFlags.NonPublic | BindingFlags.Static);

    return new Hook(
      source: methodInfo!,
      target: (ScriptBlock scriptBlock,
        ExecutionContext context,
        InvocationInfo invocationInfo) =>
      { });
  }
}
