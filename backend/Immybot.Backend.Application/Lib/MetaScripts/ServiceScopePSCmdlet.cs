using System;
using System.Linq;
using System.Management.Automation;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Lib.Helpers;
using Immybot.Backend.Application.Lib.MetaScripts.Modules;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.PSCmdlets;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Application.Lib.MetaScripts;

internal abstract class ServiceScopePSCmdlet : ImmyPSCmdlet, IDisposable
{
  protected ServiceScopePSCmdlet()
  {
    _errorPrefixDefault = new(GetErrorPrefixDefault);
  }

  private ImmybotDbContext? _localDbContext;
  private SoftwareDbContext? _globalDbContext;
  private IFindScriptByNameCmd? _findScriptByNameCmd;
  private IProviderActions? _providerActions;
  private IProviderStore? _providerStore;
  private IServiceScope? _serviceScope;
  private IDynamicFormService? _dynamicFormService;
  private IFeatureTracker? _featureTracker;
  private bool _disposed;

  protected CancellationToken CancellationToken { get; private set; }
  protected bool CanAccessMspResources { get; private set; }
  protected bool CanAccessParentTenant { get; private set; }
  protected IServiceScope ServiceScope
    => _serviceScope ?? throw new InvalidOperationException(
      $"ServiceScope not initialized. Ensure {nameof(BeginProcessing)} has been called first.");
  protected Guid ScriptSelfCancellationId { get; private set; }
  protected ImmybotDbContext LocalDbContext =>
    _localDbContext ??=
    TryGetService<ImmybotDbContext>(nameof(LocalDbContext));

  protected SoftwareDbContext GlobalDbContext =>
    _globalDbContext ??=
    TryGetService<SoftwareDbContext>(nameof(GlobalDbContext));

  protected IFindScriptByNameCmd FindScriptByNameCmd =>
    _findScriptByNameCmd ??=
    TryGetService<IFindScriptByNameCmd>();

  protected IProviderActions ProviderActions =>
    _providerActions ??=
    TryGetService<IProviderActions>();

  protected IProviderStore ProviderStore =>
  _providerStore ??=
  TryGetService<IProviderStore>();

  protected IDynamicFormService DynamicFormService =>
    _dynamicFormService ??= TryGetService<IDynamicFormService>();

  protected IFeatureTracker FeatureTracker =>
    _featureTracker ??= TryGetService<IFeatureTracker>();

  protected ILogger<T> GetLogger<T>() => TryGetService<ILogger<T>>();

  protected int? InternalTenantId { get; private set; }

  protected override void BeginProcessing()
  {
    CancellationToken = this.DemandVariableValue(SessionStateEntries.CancellationTokenVariableEntry);
    CanAccessMspResources = this.DemandVariableValue(SessionStateEntries.CanAccessMspResourcesVariableEntry);
    CanAccessParentTenant = this.DemandVariableValue(SessionStateEntries.CanAccessParentTenantVariableEntry);
    _serviceScope = this.DemandVariableValue(SessionStateEntries.ServiceScopeFactoryVariableEntry).CreateScope();
    ScriptSelfCancellationId = this.TryGetVariableValueNullable(SessionStateEntries.ScriptSelfCancellationIdVariableEntry) ?? Guid.Empty;
    InternalTenantId = this.TryGetVariableValueNullable(SessionStateEntries.LimitToScriptsForTenantVariableEntry);
  }

  /// <summary>
  /// Will cancel the execution of *this* script.
  /// </summary>
  protected void CancelScript()
  {
    var cancellationManager = TryGetService<IImmyCancellationManager>();
    // This call has a risk of hanging in Powershell land b/c of syncronization context issues.
    // see https://stackoverflow.com/questions/52450528/cancellationtokensource-cancel-hangs
    // for more info.

    // Wrap the CancelScript call in a Task.Run to avoid the deadlock.
    Task.Run(() => cancellationManager.CancelScript(ScriptSelfCancellationId)).Forget();
  }

  protected ProviderTypeDto? GetProviderTypeByNameOrTag(string providerTypeName)
  {
    if (string.IsNullOrWhiteSpace(providerTypeName))
      return null;
    var providerTypes = ProviderActions.GetAllProviderTypes();
    var providerType = providerTypes.FirstOrDefault(a => a.DisplayName == providerTypeName || a.Tag == providerTypeName);
    if (providerType == null)
    {
      string msg = $"ProviderType '{providerTypeName}' not found.{Environment.NewLine}Available ProviderType are: {Environment.NewLine}{string.Join(Environment.NewLine, providerTypes.Select(a => "\t" + a.Tag))}";
      Exception exc = new Exception(msg);
      WriteError(new ErrorRecord(
        exc, "ProviderTypeNotFound", ErrorCategory.InvalidArgument, providerTypeName));
      return null;
    }
    else
    {
      return providerType;
    }
  }

  protected virtual void Dispose(bool disposing)
  {
    if (_disposed) return;
    if (disposing)
    {
      _serviceScope?.Dispose();
    }
    _disposed = true;
  }

  private string GetErrorPrefixDefault()
  {
    CmdletAttribute a = (GetType().GetCustomAttributes(typeof(CmdletAttribute), false).FirstOrDefault() as CmdletAttribute)
      ?? throw new CmdletInvocationException("Cmdlet is missing CmdletAttribute.");

    return $"{a.VerbName}-{a.NounName}";
  }

  private readonly Lazy<string> _errorPrefixDefault;

  protected virtual string ErrorMessagePrefix => _errorPrefixDefault.Value;

  protected virtual string ErrorIdPrefix => _errorPrefixDefault.Value;

  protected virtual ErrorCategory ErrorCategoryDefault
  {
    get => ErrorCategory.InvalidOperation;
  }

  private string prefixErrorMessage(string message) => string.IsNullOrEmpty(ErrorMessagePrefix) ? message : $"{ErrorMessagePrefix}: {message}";
  private string prefixErrorId(string id) => string.IsNullOrEmpty(ErrorIdPrefix) ? id : $"{ErrorIdPrefix}-{id}";

  protected void WriteError(Exception exception, string? id = null, ErrorCategory? category = null, object? targetObject = null)
  {
    WriteError(
      new ErrorRecord(
        exception,
        prefixErrorId(id ?? string.Empty),
        category ?? ErrorCategoryDefault,
        targetObject));
  }

  protected void WriteError(string message, string? id = null, ErrorCategory? category = null, object? targetObject = null)
  {
    WriteError(
      new Exception(prefixErrorMessage(message)),
      id,
      category,
      targetObject);
  }

  public void Dispose()
  {
    Dispose(true);
    GC.SuppressFinalize(this);
  }

  /// <summary>
  /// <para>
  ///   Attempts to get a service from <see cref="ServiceScope"/>.
  /// </para>
  /// <para>
  ///   <see cref="BeginProcessing"/> must be called first to populate <see cref="ServiceScope"/>.
  /// </para>
  /// </summary>
  /// <typeparam name="T"></typeparam>
  /// <param name="callerMemberName">
  /// If omitted, efaults to the method/property name of the caller.
  /// </param>
  /// <returns></returns>
  /// <exception cref="InvalidOperationException">
  /// Thrown if <see cref="ServiceScope"/> is null, or if the supplied type could not be resolved from the DI container.
  /// </exception>
  protected T TryGetService<T>([CallerMemberName] string callerMemberName = "")
    where T : class
  {
    return ServiceScope.ServiceProvider.GetRequiredService<T>();
  }

  /// <summary>
  /// Provides a unique human-readable string who's purpose is to identify the parent execution of the script.
  /// examples:
  /// "Session => https://demo.immy.bot/tenants/1/sessions/1"
  /// "Session => https://demo.immy.bot/computers/1/sessions/1"
  /// "Script Editor [MetaScript] targeting {Computer #1 - Demo Computer} triggered by Jon Doe"
  /// "Script Editor [CloudScript] targeting {Tenant #1 - Demo Tenant} triggered by Jon Doe"
  /// </summary>
  /// <return>Unique human-readable identifier</return>
  /// <param name="callerName">Information about the caller.If left null, will be the method name of the member caller. Will only be used if no RunContext is present to provided identifying infromation.</param>
  protected string GetRunContextScriptInvocationInfo([CallerMemberName] string callerName = "")
  {
    // This is located here instead of inside the RunContextCmdlet because we want to be able to potentially call this from say a FilterScript as well.
    if (!this.TryGetVariableValue(SessionStateEntries.RunContextVariableEntry, out var runContext))
      return $"Member caller: {callerName}";

    // If we are in the context of a session, we can simply provide the session link to adequately identify the caller.
    if (runContext is ActionRunContext sessionContext)
    {
      var appOpts = ServiceScope.ServiceProvider.GetRequiredService<IOptionsMonitor<AppSettingsOptions>>().CurrentValue;

      var sessionLink = SessionLinkHelpers.GetSessionUriString(appOpts.RootUrl, sessionContext.SessionId, sessionContext.ComputerId, sessionContext.TenantId);

      return $"Session => {sessionLink}";
    }
    // Not in a session (Executing directly in script editor I believe is only other situation),
    // so we will just provide other identifying details.
    var scriptType = runContext.IsComputerTarget ? "MetaScript" : "CloudScript";
    var targetName =
      runContext.IsComputerTarget ?
      $"Computer #{runContext.ComputerId} - {runContext.ComputerName}"
      : $"Tenant #{runContext.TenantId} - {runContext.TenantName}";
    var triggeredBy = runContext.Args.ManuallyTriggeredBy;
    return
      $"Script Editor [{scriptType}] targeting {{{targetName}}} triggered by {triggeredBy?.DisplayName ?? "Unknown User"}";
  }

  /// <summary>
  /// Returns false when the current user is not an MSP user or there is no run context.
  /// </summary>
  /// <param name="tenant">Might be null even if return value is true</param>
  protected bool ValidateCanUseMspTenant(out Tenant? tenant)
  {
    if (!InternalTenantId.HasValue)
    {
      WriteError(new ErrorRecord(
        new Exception(
          "No tenant was specified for this script"),
        "1",
        ErrorCategory.PermissionDenied,
        null));
      tenant = null;
      return false;
    }

    if (!CanAccessMspResources)
    {
      WriteError(new ErrorRecord(
        new Exception(
          "Only MSP users and sessions with 'Allow Access to MSP Resources' " +
          "are allowed to use -UseMspTenant"),
        "1",
        ErrorCategory.PermissionDenied,
        null));
      tenant = null;
      return false;
    }
    tenant = LocalDbContext.GetOwnerTenant(InternalTenantId.Value, mspOnly: true);
    return true;
  }

  /// <summary>
  /// Returns false when the current user is not allowed to access the parent tenant or there is no run context.
  /// </summary>
  /// <param name="tenant">Might be null even if return value is true</param>
  /// <param name="suppressErrors">If true, will not write any errors to the pipeline</param>
  protected bool ValidateCanUseParentTenant(out Tenant? tenant, bool suppressErrors = false)
  {
    tenant = null;

    if (!InternalTenantId.HasValue)
    {
      if (!suppressErrors)
      {
        WriteError(new ErrorRecord(
          new Exception("No tenant was specified for this script"),
          "1",
          ErrorCategory.PermissionDenied,
          null));
      }
      return false;
    }

    if (!CanAccessParentTenant)
    {
      if (!suppressErrors)
      {
        WriteError(new ErrorRecord(
          new Exception(
            "The 'UseParentTenant' switch can only be used by an MSP admin."),
          "1",
          ErrorCategory.PermissionDenied,
          null));
      }
      return false;
    }

    var currentTenant = LocalDbContext.GetTenantById(InternalTenantId.Value);
    if (currentTenant == null)
    {
      if (!suppressErrors)
      {
        WriteError(new ErrorRecord(
          new Exception($"Tenant with ID={InternalTenantId.Value} was not found."),
          "1",
          ErrorCategory.ObjectNotFound,
          null));
      }
      return false;
    }

    tenant = LocalDbContext.GetParentTenant(currentTenant);
    return true;
  }
}
