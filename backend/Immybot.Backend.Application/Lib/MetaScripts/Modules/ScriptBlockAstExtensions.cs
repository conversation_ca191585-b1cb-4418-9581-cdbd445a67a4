using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Management.Automation.Language;
using System.Text;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

/// <summary>
/// Internal methods from ScriptBlockAst in PowerShell source.
/// With modifications and bug fix for identifiers which require ${...} fornmat.
/// Origin: class ScriptBlockAst, PowerShell/src/System.Management.Automation/engine/parser/ast.cs:783
/// Note: Requires updating after upgrading to PowerShell 7.3, see TODO.
/// </summary>
internal static class ScriptBlockAstExtensions
{

  public static string ToStringForSerialization(this ScriptBlockAst @this)
  {
    string result = @this.ToString();
    if (@this.Parent != null)
    {
      // Parent is FunctionDefinitionAst or ScriptBlockExpressionAst
      // The extent includes curlies which we want to exclude.
      Debug.Assert(result[0] == '{' && result[result.Length - 1] == '}');
      result = result[1..^1];
    }

    return result;
  }

  public static string ToStringForSerialization(this ScriptBlockAst @this, IEnumerable<VariableExpressionAst> usingVars, IEnumerable<string> @params, int initialStartOffset, int initialEndOffset)
  {
    Debug.Assert(usingVars is not null && usingVars.Any() && @params is not null);
    Debug.Assert(initialStartOffset < initialEndOffset && initialStartOffset >= @this.Extent.StartOffset && initialEndOffset <= @this.Extent.EndOffset);

    // astElements contains
    //  -- UsingVariable
    //  -- ParamBlockAst
    var astElements = new List<Ast>(usingVars);
    if (@this.ParamBlock != null)
      astElements.Add(@this.ParamBlock);
    else
      Debug.Assert(!@params.Any());

    int indexOffset = @this.Extent.StartOffset;
    int startOffset = initialStartOffset - indexOffset;
    int endOffset = initialEndOffset - indexOffset;

    string script = @this.ToString();
    var newScript = new StringBuilder();

    foreach (var ast in astElements.OrderBy(static ast => ast.Extent.StartOffset))
    {
      int astStartOffset = ast.Extent.StartOffset - indexOffset;
      int astEndOffset = ast.Extent.EndOffset - indexOffset;

      // Skip the ast that is before the section that we care about
      if (astStartOffset < startOffset) { continue; }
      // We are done processing the section that we care about
      if (astStartOffset >= endOffset) { break; }

      var varAst = ast as VariableExpressionAst;
      if (varAst != null)
      {
        string newVarName = varAst.ToSignedSurrogateForUsing();

        newScript.Append(script.AsSpan(startOffset, astStartOffset - startOffset));
        newScript.Append(newVarName);
        startOffset = astEndOffset;
      }
      else
      {
        var paramAst = ast as ParamBlockAst;
        Debug.Assert(paramAst != null, $"Only ParamBlockAst and VariableExpressionAst are supported. Found {ast.GetType()}");

        int currentOffset;
        bool appendComma = false;

        if (paramAst.Parameters.Count == 0)
        {
          currentOffset = astEndOffset - 1;
        }
        else
        {
          var firstParam = paramAst.Parameters[0];
          currentOffset = firstParam.Attributes.Count == 0 ? firstParam.Name.Extent.StartOffset - indexOffset : firstParam.Attributes[0].Extent.StartOffset - indexOffset;
          appendComma = true;
        }

        newScript.Append(script.AsSpan(startOffset, currentOffset - startOffset));
        newScript.Append(string.Join(", ", @params));
        if (appendComma)
          newScript.Append(",\n");
        startOffset = currentOffset;
      }
    }

    newScript.Append(script.AsSpan(startOffset, endOffset - startOffset));
    string result = newScript.ToString();

    if (@this.Parent != null && initialStartOffset == @this.Extent.StartOffset && initialEndOffset == @this.Extent.EndOffset)
    {
      // Parent is FunctionDefinitionAst or ScriptBlockExpressionAst
      // The extent includes curlies which we want to exclude.
      Debug.Assert(result[0] == '{' && result[result.Length - 1] == '}');
      result = result.Substring(1, result.Length - 2);
    }

    return result;
  }

  //TODO: Add conditions regarding CleanBlock after upgrading to PS 7.3, treat the same as BeginBlock, e.g. { CleanBlock: null }
  public static PipelineAst? GetSimplePipeline(this ScriptBlockAst @this)
    => @this is { BeginBlock: null } and { ProcessBlock: null } and { DynamicParamBlock: null } and { EndBlock: not { Traps.Count: > 0 } and { Statements: { Count: 1 } statements } } ? statements[0] as PipelineAst : null;

  public static PipelineAst? GetSimplePipeline(this ScriptBlockAst @this, out string? errorId, out string? errorMsg)
  {
    errorMsg = errorId = null;

    if (@this is { BeginBlock: null } and { ProcessBlock: null } and { DynamicParamBlock: null } and { EndBlock: not { Traps.Count: > 0 } and { Statements: { Count: 1 } statements } } && statements[0] is PipelineAst pipelineAst)
      return pipelineAst;
    else
      (errorMsg, errorId) =
        @this switch
        {
          not ({ BeginBlock: null } and { ProcessBlock: null } and { DynamicParamBlock: null }) => ("Can only convert one clause.", "CanConvertOneClauseOnly"),
          not { EndBlock.Statements.Count: > 0 } => ("Cannot convert empty pipeline", "CantConvertEmptyPipeline"),
          { EndBlock.Traps.Count: > 0 } => ("Cannot convert a scripting block with a trap.", "CantConvertScriptBlockWithTrap"),
          _ => ("Can only convert one pipeline.", "CanOnlyConvertOnePipeline")
        };

    return null;
  }

  /// <summary>
  /// Replaces and derives from (:1636)
  ///   private string GetWithInputHandlingForInvokeCommandImpl(Tuple<List<VariableExpressionAst>, string> usingVariablesTuple)
  /// </summary>
  public static string GetForInvokeCommandImpl(this ScriptBlockAst @this, IEnumerable<VariableExpressionAst> usingVars, IEnumerable<string> @params)
  {
    return @this.ToStringForSerialization(usingVars, @params, @this.Extent.StartOffset, @this.Extent.EndOffset);
  }
}

