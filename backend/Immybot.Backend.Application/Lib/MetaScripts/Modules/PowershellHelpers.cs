using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Management.Automation;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

internal static class PowerShellHelpers
{
  internal static PSObject ConvertKeyValueDictionaryToPSObject(IDictionary<string, object?> values)
  {
    var obj = new PSObject();
    foreach (var item in values)
    {
      obj.Members.Add(new PSNoteProperty(item.Key, item.Value));
    }

    return obj;
  }

  internal static PSObject ConvertFormSchemaInputValuesToPSObject(
    ICollection<ProviderLinkFormSchemaInputValue>? formDataValues)
  {
    var obj = new PSObject();
    if (formDataValues == null || !formDataValues.Any()) return obj;

    foreach (var item in formDataValues)
    {
      obj.Members.Add(new PSNoteProperty(item.Name, item.Value));
    }

    return obj;
  }

  internal static void ThrowIfNull(object? obj, string paramName)
  {
    if (obj == null) throw new ArgumentNullException(paramName, $"{paramName} is required but was not provided.");
  }

  internal static IEnumerable<PSObject> ConvertFromComputerToPSObject(IEnumerable<Computer> computers, string[]? inventoryKeys) =>
    computers
      .Select(a => PSComputer.FromComputer(a, inventoryKeys))
      .Select(ConvertFrom);

  internal static PSObject ConvertFrom(PSComputer computer)
  {
    var psComputer = new PSObject(computer);
    psComputer.Members.Add(new PSNoteProperty("Name", computer.Name));
    psComputer.Members.Add(
        new PSMemberSet("PSStandardMembers", new PSMemberInfo[]
        {
              new PSPropertySet("DefaultDisplayPropertySet", new string[]{ "Id", "Name", "OperatingSystemName", "OnboardingStatus", "SerialNumber", "IsOnline", "TenantId", "TenantName", "PrimaryPersonName", "PrimaryPersonId", "PrimaryPersonOnPremisesSecurityIdentifier", "Tags"  })
        }));
    return psComputer;
  }

  [SuppressMessage("Major Bug", "S2583:Conditionally executed code should be reachable")]
  [SuppressMessage("Major Code Smell", "S2589:Boolean expressions should not be gratuitous")]
  internal static ArrayList GetParamVariable(ScriptBlock scriptBlock)
  {
    //    Tokenize the script
    var tokens = PSParser.Tokenize(scriptBlock.ToString(), out _).Where(t => t.Type.ToString() != "NewLine" && t.Type.ToString() != "Comment");

    var state = 0;
    var bracket = 0;
    var awaitVariable = false;
    var tokensToReturn = new ArrayList();
    foreach (var token in tokens)
    {
      // using state machine method
      switch (state)
      {
        case 0:
          {
            //// search for sttribute start or param
            if (token.Type.ToString().ToString() == "Keyword" && token.Content == "param")
            {
              state = 3; // collect variables start
              awaitVariable = true; // catch variable name after param(
            }
            else if (token.Type.ToString() == "Operator" && token.Content == "[")
            {
              //attribute start
              state = 1; // check for attribute token
              bracket++;
            }
            else
            {
              // no param found, break
              state = -1;
            }
            break;
          }
        case 1:
          {
            // Attribute token check. may be excessive?
            if (token.Type.ToString() == "Attribute")
            {
              state = 2; // wait for close attribute block
            }
            break;
          }
        case 2:
          {
            // await attribte end
            if (token.Type.ToString() == "Operator")
            {
              if (token.Content == "[")
              {
                bracket++;
              }
              else if (token.Content == "]")
              {
                bracket--;
                if (bracket == 0)
                {
                  // catched attribute close bracket
                  state = 0; // back to param() search
                }
              }
            }
            break;
          }
        case 3:
          {
            // inside params
            if (token.Type.ToString() == "GroupStart" && token.Content == "(")
            {
              bracket++;
            }
            else if (token.Type.ToString() == "GroupEnd" && token.Content == ")")
            {
              bracket--;
              if (bracket == 0)
              {
                // param() closed, exiting
                state = -1;
              }
            }
            else if (token.Type.ToString() == "Operator" && token.Content == "[")
            {
              bracket += 2; //count square brackets
            }
            else if (token.Type.ToString() == "Operator" && token.Content == "]")
            {
              bracket -= 2; //count square brackets
            }
            else if (token.Type.ToString() == "GroupStart" && (token.Content == "{" || token.Content == "@{"))
            {
              bracket += 2; //count curly brackets
            }
            else if (token.Type.ToString() == "GroupEnd" && token.Content == "}")
            {
              bracket -= 2; //count curly brackets
            }
            else if (token.Type.ToString() == "Operator" && token.Content == "," && (bracket == 1))
            {
              awaitVariable = true; // await variable name after comma without extra brackets
            }
            else if (token.Type.ToString() == "Variable" && (bracket == 1) && awaitVariable)
            {
              awaitVariable = false;
              tokensToReturn.Add(token.Content);
            }
          }
          break;
      }
      if (state == -1) { break; }
    }
    return tokensToReturn;
  }
}
