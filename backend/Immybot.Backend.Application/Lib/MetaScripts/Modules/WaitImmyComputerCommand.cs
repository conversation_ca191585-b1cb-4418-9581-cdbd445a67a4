using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Management.Automation;
using System.Threading;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Events;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Providers.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

[Cmdlet("Wait", "ImmyComputer")]
internal class WaitImmyComputerCommand : RunContextPSCmdlet
{
  private IDomainEventReceiver? _eventReceiver;
  private Guid _connectedEventHandle;
  private Guid _disconnectedEventHandle;

  private readonly List<Computer> _computersToProcess = [];
  private readonly ConcurrentDictionary<string, AutoResetEvent> _waitHandles = new();
  private readonly ConcurrentDictionary<int, string> _providerLinkNameLookup = new();
  private readonly PriorityQueue<string, DateTime> _eventWriteQueue = new();
  private readonly object _eventQueueLock = new();

  [Parameter(ValueFromPipeline = true, ValueFromPipelineByPropertyName = true)]
  public PSComputer[]? Computer { get; set; }

  [Parameter(HelpMessage = "The time to wait. Default is 30 minutes.")]
  public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(30);

  [Parameter(HelpMessage = "The time to wait for a connected event before attempting manual agent refresh polling. Default is 3 minutes.")]
  public TimeSpan WaitForEventTimeout { get; set; } = TimeSpan.FromMinutes(3);

  [Parameter(HelpMessage = "The time to wait between polling. Default is 30 seconds.")]
  public TimeSpan PollPeriodInterval { get; set; } = TimeSpan.FromSeconds(30);

  [Parameter(HelpMessage = "The operation to wait on.")]
  public WaitImmyComputerType For { get; set; } = WaitImmyComputerType.Reboot;

  private void ProcessComputerForReboot(Computer a)
  {
    foreach (var agent in a.Agents)
    {
      _providerLinkNameLookup.TryAdd(agent.ProviderLinkId, agent.ProviderLink?.Name ?? string.Empty);
    }

    var agents = a.Agents.Select(a => $"{a.ProviderLinkId}-{a.ExternalClientId}-{a.ExternalAgentId}");

    var waitHandle = new AutoResetEvent(false);

    foreach (var key in agents)
    {
      _waitHandles.TryAdd(key, waitHandle);
    }
  }
  private bool ShouldProcessAgentEvent(AgentEvent agentEvent, out string key)
  {
    key = $"{agentEvent.ProviderLinkId}-{agentEvent.ClientId}-{agentEvent.AgentId}";

    if (_waitHandles.ContainsKey(key)) return true;

    return false;
  }
  private void EmptyEventLogQueue()
  {
    lock (_eventQueueLock)
    {
      while (_eventWriteQueue.TryDequeue(out var evtMsg, out var evtTime))
      {
        // Add the ISO stamp there if we need precise timing info for debugging,
        // as these events may appear out-of-order compared to events originating from main thread.
        // We make a best-effort to ensure this queue is empty by calling it anytime main thread wants to
        // write progress, and in our finally block of EndProcessing.
        WriteProgress($"{{{evtTime.ToString("o")}}} {evtMsg}");
      }
    }
  }
  private void WriteProgress(string msg)
  {
    EmptyEventLogQueue();
    WriteProgress(new ProgressRecord(1, "Wait-ImmyComputer", msg));
  }
  private void WriteAgentEventProgress(int providerLinkId, bool connected)
  {
    // We write to a queue, b/c you cannot/shouldn't access WriteProgress or similar on another
    // thread, and this method is invoked by a callback from the DomainReceiver.
    lock (_eventQueueLock)
    {
      _eventWriteQueue.Enqueue($"Agent Event: [{_providerLinkNameLookup[providerLinkId]}] => {(connected ? "Connected" : "Disconnected")}", DateTime.UtcNow);
    }
  }

  private void EndProcessingReboot()
  {
    try
    {
      _eventReceiver = ServiceScope.ServiceProvider.GetRequiredService<IDomainEventReceiver>();
      _connectedEventHandle = _eventReceiver.AddEventHandler<AgentConnectedSupportingScriptExecutionEvent>(ev =>
      {
        if (!ShouldProcessAgentEvent(ev, out var key)) return;

        WriteAgentEventProgress(ev.ProviderLinkId, connected: true);
        var waitHandle = _waitHandles[key];
        waitHandle.Set();
      });

      _disconnectedEventHandle = _eventReceiver.AddEventHandler<AgentDisconnectedEvent>(ev =>
      {
        if (!ShouldProcessAgentEvent(ev, out _)) return;
        WriteAgentEventProgress(ev.ProviderLinkId, connected: false);
      });
      foreach (var computer in _computersToProcess)
      {
        ProcessComputerForReboot(computer);
      }

      if (_waitHandles.Values.Count == 0)
      {
        WriteError(new ErrorRecord(new Exception("There were no available agents to wait for a reboot"), "RebootComputer", ErrorCategory.InvalidArgument, null));
        return;
      }

      var timeoutLength = (For == WaitImmyComputerType.Reboot) ? Timeout : WaitForEventTimeout;

      try
      {
        WaitHandleHelpers.CancelableWaitAll(_waitHandles.Values.Distinct().ToArray(), CancellationToken, timeoutLength);
      }
      catch (TimeoutException) when (For == WaitImmyComputerType.RebootWithPollingFallback && !CancellationToken.IsCancellationRequested)
      {
        var providers = _computersToProcess.Single().Agents.Select(a => (a.ProviderLink!.Name, a.ProviderLink, ProviderAgent: a)).ToArray();
        var providersSupportingRefresh = providers
          .Where(p => ProviderActions.GetProviderType(p.ProviderLink.ProviderTypeId)
            .ProviderCapabilities.Contains(nameof(ISupportsRefreshAgentOnlineStatus)))
          .ToArray();

        var supportedProvidersString = string.Join(", ", providersSupportingRefresh.Select(x => x.Name));
        var nonRefreshProviderString = string.Join(", ", providers.Except(providersSupportingRefresh).Select(x => x.Name));
        var refreshListString = $"[{supportedProvidersString}]{(nonRefreshProviderString == "" ? "" : $". [{nonRefreshProviderString}] do not support refreshing of Agent connectivity.")}";

        WriteProgress($"No connectivity changes after waiting {timeoutLength.TotalSeconds} seconds. Starting manual refresh of connectivity state from [{refreshListString}]");

        using var remainingTimeoutCts = new CancellationTokenSource(Timeout - WaitForEventTimeout);
        using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(remainingTimeoutCts.Token, CancellationToken);
        while (!combinedCts.IsCancellationRequested)
        {
          var providerManualRefreshResults =
            providersSupportingRefresh.Select(
              p => (p, IsOnline: new JoinableTaskContext().Factory.Run(async () => await ProviderActions.RefreshAgentOnlineStatus(
                p.ProviderLink, p.ProviderAgent, combinedCts.Token))))
            .ToArray();

          foreach (var ((providerName, _, _), isOnline) in providerManualRefreshResults)
          {
            WriteWarning($"[{providerName}] manual refresh => {(isOnline ? "Online" : "Offline")}");
          }
          var onlineReported = providerManualRefreshResults.Count(x => x.IsOnline);
          if (onlineReported > 0)
          {
            WriteProgress($"{onlineReported} providers reported online status after manual refresh. Waiting complete.");
            return;
          }

          WriteProgress($"Manual refresh yielded no online providers. Will try again in {PollPeriodInterval.TotalSeconds} seconds.");
          try
          {
            WaitHandleHelpers.CancelableWaitAll(_waitHandles.Values.Distinct().ToArray(), combinedCts.Token, PollPeriodInterval);
            // If we got here, that means at least one waitHandle has comeback true indicating an agent is online.
            WriteProgress("One or more providers has reported an online status event. Waiting complete.");
            return;
          }
          catch (TimeoutException)
          {
            WriteProgress($"Starting manual refresh of connectivity state from {refreshListString}");
          }
          catch (OperationCanceledException)
          {
            break;
          }
        }

        if (remainingTimeoutCts.IsCancellationRequested) throw new TimeoutException();
        if (CancellationToken.IsCancellationRequested) throw new OperationCanceledException();
      }
    }
    finally
    {
      _eventReceiver?.RemoveEventHandler<AgentConnectedSupportingScriptExecutionEvent>(_connectedEventHandle);
      _eventReceiver?.RemoveEventHandler<AgentDisconnectedEvent>(_disconnectedEventHandle);
      foreach (var wh in _waitHandles.Values) wh.Close();
      EmptyEventLogQueue();
    }
  }

  protected override void ProcessRecord()
  {
    ProcessComputer(Computer, a =>
    {
      _computersToProcess.Add(a);
    });
  }

  protected override void EndProcessing()
  {
    try
    {
      switch (For)
      {
        case WaitImmyComputerType.RebootWithPollingFallback:
          if (_computersToProcess.Count > 1) throw new ArgumentException("RebootWithPollingFallback currently doesn't support multiple computers.");
          if (Timeout < WaitForEventTimeout.Add(PollPeriodInterval)) throw new ArgumentException("RebootWithPollingFallback must have a Timeout that exceeds WaitForEventTimeout by at least PollPeriodInterval to operate.");
          EndProcessingReboot();
          break;
        case WaitImmyComputerType.Reboot:
          EndProcessingReboot();
          break;
      }
    }
    finally
    {
      base.EndProcessing();
    }
  }
}

public enum WaitImmyComputerType
{
  Reboot = 1,
  RebootWithPollingFallback = 2,
}
