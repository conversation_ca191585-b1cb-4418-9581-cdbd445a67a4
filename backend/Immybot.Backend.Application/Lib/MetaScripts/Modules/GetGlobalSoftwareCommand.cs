using System;
using System.Linq;
using System.Management.Automation;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Lib.Helpers.Extensions;
using Immybot.Backend.Domain.Models;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

[Cmdlet(VerbsCommon.Get, "GlobalSoftware")]
[OutputType(typeof(GlobalSoftware))]
internal class GetGlobalSoftwareCommand : RunContextPSCmdlet
{
  private IGlobalSoftwareActions? _globalSoftwareActions;
  private ILogger<GetGlobalSoftwareCommand>? _logger;

  [Parameter(Position = 0, ValueFromPipeline = true)]
  public PSObject[] InputObject { get; set; } = Array.Empty<PSObject>();

  protected override void BeginProcessing()
  {
    base.BeginProcessing();

    _globalSoftwareActions = TryGetService<IGlobalSoftwareActions>();
    _logger = TryGetService<ILogger<GetGlobalSoftwareCommand>>();
  }

  protected override void ProcessRecord()
  {
    ValidateState();
    // From here on, we know the services in the private fields exist,
    // so we can access them as not-null with "!".

    using var logScope = _logger!.BeginScope(nameof(GetGlobalSoftwareCommand));

    foreach (var psObject in InputObject)
    {
      var systemCompResult = psObject.TryGetPropertyValue<int>("SystemComponent");
      if (systemCompResult.IsSuccess && systemCompResult.Value == 1)
      {
        // Ignore SystemComponents.
        continue;
      }

      var (upgradeCode, productCode, softwareDisplayName) = TryGetPropertyValues(psObject);

      if (upgradeCode == Guid.Empty && productCode == Guid.Empty && string.IsNullOrWhiteSpace(softwareDisplayName))
      {
        WriteWarning("Invalid input object.  Object must contain an UpgradeCode, ProductCode, or DisplayName.");
        continue;
      }

      var joinableContext = new JoinableTaskContext();
      var result = joinableContext.Factory.Run(async () =>
      {
        return await _globalSoftwareActions!.FindSoftware(upgradeCode, productCode, softwareDisplayName, CancellationToken);
      });

      if (result.IsSuccess)
      {
        // Darren mentioned wanting to publish an event in the future to track this.
        // For now, we'll log a warning.
        if (result.Value.Length > 1)
        {
          _logger!.LogWarning(
            "Multiple ({count}) global software records were found for " +
              "upgrade code ({upgradeCode}), product code ({productCode}), " +
              "or software name ({softwareName}).",
            result.Value.Length,
            upgradeCode,
            productCode,
            softwareDisplayName);
        }

        WriteObject(result.Value[0]);
      }
    }
  }

  private (Guid upgradeCode, Guid productCode, string softwareDisplayName) TryGetPropertyValues(PSObject psObject)
  {
    Guid upgradeCode;
    Guid productCode;
    var softwareDisplayName = string.Empty;

    var result = psObject.TryGetPropertyValue<Guid>("UpgradeCode");
    if (!result.IsSuccess)
    {
      var resultAsString = psObject.TryGetPropertyValue<string>("UpgradeCode");
      if (!Guid.TryParse(resultAsString.Value, out upgradeCode) && upgradeCode != Guid.Empty)
      {
        _logger!.LogWarning("Unable to parse upgrade code \"{code}\".", result.Value);
      }
    }
    else
    {
      upgradeCode = result.Value;
    }

    if (upgradeCode == Guid.Empty)
    {
      _logger!.LogDebug(result.Exception, "Unable to get UpgradeCode from software PSObject.");
    }

    result = psObject.TryGetPropertyValue<Guid>("ProductCode");
    if (!result.IsSuccess)
    {
      var resultAsString = psObject.TryGetPropertyValue<string>("ProductCode");
      if (!Guid.TryParse(resultAsString.Value, out productCode) && productCode != Guid.Empty)
      {
        _logger!.LogWarning("Unable to parse product code \"{code}\".", result.Value);
      }
    }
    else
    {
      productCode = result.Value;
    }

    if (productCode == Guid.Empty)
    {
      _logger!.LogDebug(result.Exception, "Unable to get ProductCode from software PSObject.");
    }

    var displayNameResult = psObject.TryGetPropertyValue<string>("DisplayName");
    if (displayNameResult.IsSuccess)
    {
      softwareDisplayName = displayNameResult.Value;
    }
    else
    {
      _logger!.LogDebug(displayNameResult.Exception, "Unable to get DisplayName from software PSObject.");
    }

    return (upgradeCode, productCode, softwareDisplayName);
  }

  private void ValidateState()
  {
    if (_globalSoftwareActions is null)
    {
      throw new InvalidOperationException($"Private field \"{nameof(_globalSoftwareActions)}\" is unexpectedly null.");
    }

    if (_logger is null)
    {
      throw new InvalidOperationException($"Private field \"{nameof(_logger)}\" is unexpectedly null.");
    }
  }
}
