using System;
using System.Management.Automation;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.MetaScripts.Attributes;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

/// <summary>
/// Use this to invoke a scriptblock and cache the result in a local or global ImmyCache.
/// </summary>
[Cmdlet("Invoke", "AtomicCommand")]
[ProhibitFunctionOverride(ProhibitGlobal = true, ProhibitLocal = true)]
[OutputType(typeof(object))]
internal class InvokeAtomicCommandCommand : ServiceScopePSCmdlet
{
  [Parameter(Mandatory = true, HelpMessage = "The unique key name for this lock. Remember, this lock works across this instance for all Cloud & Meta scripts, so be cognizant of key choice.")]
  public required string Key { get; set; }

  [Parameter(Mandatory = true, HelpMessage = "ScriptBlock that will be executed once the exclusive lock is acquired.")]
  public required ScriptBlock ScriptBlock { get; set; }

  private readonly static TimeSpan _watchdogTimerInterval = TimeSpan.FromSeconds(20);
  private readonly CmdletXThreadHandler<InvokeAtomicCommandCommand> _handler;

  public InvokeAtomicCommandCommand()
  {
    _handler = new(cmdlet: this);
  }
  protected override void BeginProcessing()
  {
    base.BeginProcessing();

    if (string.IsNullOrWhiteSpace(Key))
      throw new ArgumentException("Key must be a non null/empty/whitespace string.");

    var WroteWatchdogNotEnabledMessage = false;
    if (!this.TryGetVariableValue(SessionStateEntries.RunContextVariableEntry, out var runContext))
    {
      WriteVerbose("Cmdlet is not running in a RunContext. Watchdog will not be enabled.");
      WroteWatchdogNotEnabledMessage = true;
    }
    var sessionContext = runContext as SessionRunContext;
    if (sessionContext is null && !WroteWatchdogNotEnabledMessage)
      WriteVerbose("Cmdlet is not running in the context of a session. Watchdog will not be enabled.");

    using (_handler)
    {
      _ = Task.Run(async () =>
      {
        try
        {
          var locker = ServiceScope!.ServiceProvider.GetRequiredService<KeyedLocker>();


          var lockOpts = CreateKeyedLockOptions
          .WithCallerName(GetRunContextScriptInvocationInfo("Invoke-AtomicCommand"))
          .WithDefaultEventFilter()
          .WithWatchdogConfig(sessionContext is not null ? new(Timeout: _watchdogTimerInterval + TimeSpan.FromSeconds(1), Strategy: KeyedLockOptions.WatchdogStrategy.WatchdogTriggersCancellationWithLockAbort) : null)
          .WithInputTokenCancellationStrategy(KeyedLockOptions.TokenStrategy.PropagateInputTokenCancellationWithLockAbort);

          using (var lockHandle = await locker.LockAsync(LockKeys.InvokeAtomicCommand.With(Key), evt => _handler.WriteVerbose(evt.ToString()), lockOpts, token: CancellationToken))
          {
            using var terminatorRegistration = lockHandle.CancellationToken.Register(() =>
            {
              var releaseReason = CancellationToken.IsCancellationRequested ? "Cancellation was requested" : "Watchdog detected deadlock";
              try
              {
                runContext?.AddLog($"Lock handle for atomic operation was forcefully released : {releaseReason}");
                // Hit this script the with sledgehammer. This will cause the metascript invoker calling this script to bail out,
                // forcing this cmdlet to exit. This is important because if we don't do this, it is possible the scriptblock could be continuing
                // to use resources it shouldn't, defeating the purpose of the lock.
                CancelScript();
              }
              catch (Exception ex)
              {
                // I doubt this will ever be able to write the error (since for it to get here, PoSH usually has to be locked up) but it's worth a shot.
                _handler.WriteError(new(ex, "ForcefulLockReleaseErr", ErrorCategory.WriteError, Key));
              }
            });

            // Background task to handle feeding the watchdog
            if (sessionContext is not null)
            {
              _ = Task.Run(async () =>
              {
                using var timer = new PeriodicTimer(_watchdogTimerInterval);
                while (await timer.WaitForNextTickAsync(lockHandle.CancellationToken))
                {
                  if (_handler.IsCompleted) break;

                  _handler.WriteVerbose("Watchdog feed alarm has been triggered. Consider ways to shorten lock dwell-time if this message appears regularly.");
                  // Use the session status to determine if the script should be considered deadlocked.
                  if (sessionContext.Session.SessionStatus == SessionStatus.Running)
                  {
                    lockHandle.TryFeedWatchdog();
                    _handler.WriteVerbose("Session status indicates it is still running. Watchdog has been fed.");
                    continue;
                  }
                  _handler.WriteWarning($"Watchdog was not fed! SessionStatus: {sessionContext.Session.SessionStatus}. Lock will be forcefully released soon.");
                }

              });
            }

            var res = await _handler.InvokeFuncAsync((_) => ScriptBlock.Invoke(), lockHandle.CancellationToken);
            _handler.WriteObject(res);
          }
        }
        catch (Exception ex)
        {
          _handler.WriteError(new(ex, "InvokeAtomicOpErr", ErrorCategory.WriteError, Key));
        }
        finally
        {
          _handler.Complete();
        }
      });


      // Token is not supplied here as doing so will cause the _handler object to be disposed of prematurely,
      // which means logs or output may not be written as the scriptblock usually needs some time to complete.
      // Additionally, by having the object be inaccessible, we will cause some pretty serious exceptions to be thrown as Powershell will not realize
      // the pointer is dereferenced and will try to access it anyway.
      _handler.BlockAndProcessRequests();
    }
  }
}
