using System;
using System.Management.Automation;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Azure.Domain.BlobStorage;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

/// <summary>
/// Creates a SAS Uri that can be used to crud files from the server or a computer
/// </summary>
[Cmdlet("New", "ImmyUploadSasUri")]
[OutputType(typeof(string))]
internal class NewImmyUploadSasUriCommand : ServiceScopePSCmdlet
{
  /// <summary>
  /// If <PERSON><PERSON>b<PERSON><PERSON> is present, then we create a BlobUri, otherwise we create a ContainerUri
  /// </summary>
  [Parameter(Mandatory = false)]
  public string? BlobName { get; set; }

  /// <summary>
  /// Available permissions = "racwdl"
  /// r - Read
  /// a - Add
  /// c - Create
  /// w - Write
  /// d - Delete
  /// l - List
  /// </summary>
  [Parameter(Mandatory = false)]
  public string? Permission { get; set; }

  /// <summary>
  /// Set the expiration date for the SAS Uri
  /// </summary>
  [Parameter(Mandatory = false)]
  public DateTime? ExpiryTime { get; set; }

  protected override void BeginProcessing()
  {
    base.BeginProcessing();
    var dbOpts = ServiceScope.ServiceProvider.GetRequiredService<IOptionsMonitor<DatabaseOptions>>().CurrentValue;
    var appSettings = ServiceScope.ServiceProvider.GetRequiredService<IOptionsMonitor<AppSettingsOptions>>().CurrentValue;
    var blobServiceClient = ServiceScope.ServiceProvider.GetRequiredService<LocalBlobServiceClient>();
    if (string.IsNullOrEmpty(BlobName))
    {
      // default permission to read/write/list
      if (string.IsNullOrEmpty(Permission)) Permission = "rwl";

      if (Permission.Contains("d"))
      {
        WriteError(new ErrorRecord(new Exception("Delete permission is only allowed when specifying the BlobName"), "1", ErrorCategory.InvalidArgument, Permission));
        return;
      }

      // generate container SAS
      var uri = BlobHelpers.GetContainerUri(
        blobServiceClient,
        appSettings.ImmyUploadStorageContainerName,
        Permission,
        ExpiryTime ?? DateTime.UtcNow.AddDays(1),
        createContainerIfNotExists: true)?.ToString();

      // fix to query container
      // https://stackoverflow.com/questions/25038429/azure-shared-access-signature-signature-did-not-match/
      uri += $"&comp=list&restype=container";
      WriteObject(uri);
    }
    else
    {
      // default permission to write
      if (string.IsNullOrEmpty(Permission)) Permission = "rw";

      // generate blob SAS
      var uri = BlobHelpers.GetBlobUri(
        blobServiceClient,
        dbOpts.LocalBlobStorageConnectionString,
        appSettings.ImmyUploadStorageContainerName,
        BlobName,
        Permission,
        ExpiryTime ?? DateTime.UtcNow.AddDays(1),
        createContainerIfNotExists: true);

      // hokey fix since we need uri.LocalPath to not be url-encoded
      var uriFixed = uri.Scheme + "://" + uri.Host + uri.LocalPath + uri.Query;
      WriteObject(uriFixed);
    }
  }
}
