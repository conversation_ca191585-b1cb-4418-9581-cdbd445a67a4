using System;
using System.Management.Automation;
using OtpNet;
using Immybot.Backend.Application.Lib.Helpers.Extensions;
using OneOf;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

[Cmdlet(VerbsCommon.Get, "OTP")]
internal class GetOtpCommand : PSCmdlet, IDynamicParameters
{
  [Parameter(Mandatory = true, HelpMessage = "Type of OTP to generate. TOTP (Time-based One Time Password), or HOTP (HMAC-based One Time Password)")]
  public OtpType Type { get; set; }

  [Parameter(Mandatory = true, HelpMessage = "Type of Key input, either Base32 string (KeyString) or byte array (KeyBytes).")]
  public KeyFormat KeyType { get; set; }

  [Parameter(HelpMessage = "Hash Mode to use. Default is SHA1.")]
  public OtpHashMode HashMode { get; set; } = OtpHashMode.Sha1;

  [Parameter(HelpMessage = "How many digits you'd like the code to be. RFC specifies 8, however 6 has become the standard, and is the default.")]
  public int Digits { get; set; } = 6;

  private readonly RuntimeDefinedParameterDictionary _dynamicParamsSet = [];


  private byte[] KeyBytes => KeyType == KeyFormat.ByteArray ? _dynamicParamsSet.GetDynamicParamOrDefault<byte[]>("KeyBytes")! : Base32Encoding.ToBytes(_dynamicParamsSet.GetDynamicParamOrDefault<string>("KeyString")!);

  public object GetDynamicParameters()
  {
    if (KeyType == KeyFormat.Base32)
      _dynamicParamsSet.AddRuntimeParameter<string>(paramName: "KeyString", mandatory: true, helpMessage: "Base32 formatted key.");
    else
      _dynamicParamsSet.AddRuntimeParameter<byte[]>(paramName: "KeyBytes", mandatory: true, helpMessage: "Byte array formatted key.");


    if (Type == OtpType.TOTP)
    {
      _dynamicParamsSet.AddRuntimeParameter<DateTime>(paramName: "Timestamp", helpMessage: "The time to generate the TOTP for. by default, and generally, you just want DateTime.UtcNow.");
      _dynamicParamsSet.AddRuntimeParameter<int>(paramName: "Window", helpMessage: "The token window to generate the TOTP for. Default is 30 seconds.");
    }
    else
    {
      _dynamicParamsSet.AddRuntimeParameter<long>(paramName: "Counter", mandatory: true, helpMessage: "The counter to generate the HOTP for.");
    }

    return _dynamicParamsSet;
  }

  protected override void ProcessRecord()
  {
    // There isn't much point in using OneOf here, but it felt fun. Code is *maybe* a tad bit shorter.
    OneOf<Totp, Hotp> otp = Type switch
    {
      OtpType.TOTP => new Totp(KeyBytes, step: _dynamicParamsSet.GetDynamicParamOrDefault("Window", defaultValue: 30), mode: HashMode, totpSize: Digits),
      OtpType.HOTP => new Hotp(KeyBytes, mode: HashMode, hotpSize: Digits),
      _ => throw new NotImplementedException(),
    };

    var computedOtp = otp.Match(
      totp => totp.ComputeTotp(_dynamicParamsSet.GetDynamicParamOrDefault("Timestamp", defaultValue: DateTime.UtcNow)),
      hotp => hotp.ComputeHOTP(_dynamicParamsSet.GetDynamicParamOrDefault<long>("Counter"))
      );

    WriteObject(computedOtp);
  }
  public enum OtpType
  {
    TOTP,
    HOTP
  }
  public enum KeyFormat
  {
    Base32,
    ByteArray
  }
}

