using System;
using System.Management.Automation;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Shared.Primitives.Helpers;
using Platform = Immybot.Backend.Domain.Models.Platform;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

[Cmdlet(VerbsCommon.Get, "AgentInstallScript")]
internal class GetAgentInstallScriptMetaScript : RunContextPSCmdlet
{
  [Parameter(Mandatory = true, Position = 0)]
  [Alias("RmmLinkId")]
  public int ProviderLinkId { get; set; }

  protected override void ProcessRecord()
  {
    var link = LocalDbContext.GetProviderLink(ProviderLinkId);

    if (link == null)
    {
      WriteError(
        new ErrorRecord(
          new Exception($"An integration with id {ProviderLinkId} does not exist."),
          "1", ErrorCategory.InvalidData, ProviderLinkId));
      return;
    }

    var tenantId = RunContext.TenantId;

    var externalClientId = tenantId.ToString();

    var provider = TaskHelper.RunAsJoinableTask(async () => await ProviderActions.GetProvider(link, CancellationToken));

    // if the provider supports listing clients, then it should use the providers external client id, instead of the tenant id
    if (provider is ISupportsListingClients)
    {
      var clientId = LocalDbContext.GetProviderClientExternalClientId(ProviderLinkId, tenantId);
      if (string.IsNullOrEmpty(clientId))
      {
        WriteError(
          new ErrorRecord(
            new Exception($"Tenant {tenantId} is not linked to a client for the specified provider."),
            "1", ErrorCategory.InvalidData, ProviderLinkId));
        return;
      }
      externalClientId = clientId;
    }

    IScript script;
    try
    {
      script = TaskHelper.RunAsJoinableTask(async () => await ProviderActions.GetAgentPowerShellInstallScript(
        link,
        new GetPowerShellInstallScriptParametersWithOnboardingOptions
        {
          Platform = Platform.Windows,
          TargetExternalClientId = externalClientId,
          OnboardingOptions = new AgentOnboardingOptions
          {
            PrimaryPersonId = RunContext.Args.Computer?.PrimaryPersonId
          }
        },
        token: CancellationToken));
    }
    catch (NotSupportedException)
    {
      script = TaskHelper.RunAsJoinableTask(async () => await ProviderActions.GetAgentPowerShellInstallScript(
        link,
        new GetPowerShellInstallScriptParameters
        {
          Platform = Platform.Windows,
          TargetExternalClientId = externalClientId
        },
        token: CancellationToken));
    }

    WriteObject(ScriptBlock.Create(script.Script));
  }
}
