using System;
using System.Collections;
using System.Linq;
using System.Management.Automation;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Application.Lib.Helpers;
using Immybot.Backend.Application.Lib.SessionLogs;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

[Cmdlet("Connect", "ImmyAzureAD")]
[OutputType(typeof(string))]
internal class ConnectImmyAzureADCommand : ServiceScopePSCmdlet
{
  [Parameter(HelpMessage = "Connect to Azure AD using the MSP Tenant")]
  public SwitchParameter UseMSPTenant { get; set; }
  protected override void ProcessRecord()
  {
    var azureActions = ServiceScope.ServiceProvider.GetRequiredService<IAzureActions>();

    Tenant? tenant = null;

    if (UseMSPTenant && !ValidateCanUseMspTenant(out tenant)) return;

    if (tenant is null && InternalTenantId.HasValue)
    {
      // try fetching it from the internal tenant id
      tenant = LocalDbContext.GetTenantById(InternalTenantId.Value, includeAzData: true);
    }

    if (tenant == null)
    {
      WriteError(new ErrorRecord(new Exception("A tenant was not provided to the runspace"), "1", ErrorCategory.InvalidArgument, null));
      return;
    }

    if (tenant?.AzureTenantLink?.AzureTenant?.PrincipalId is not { Length: > 0 } principalId)
    {
      WriteError(new ErrorRecord(
        new Exception("An Azure-linked tenant was not provided to the runspace"), "1", ErrorCategory.InvalidArgument, tenant));
      return;
    }

    var adGraphAccessToken = new JoinableTaskContext().Factory.Run(async () =>
      await azureActions.GetAzureTenantAuthParameters(tenant,
        endpoint: AzureResourceAlias.AzureAD));

    // The AzureAD module is required in order to call `Connect-AzureAD`
    // If the module is not loaded, then load it now
    if (!this.Context.Modules.ModuleTable.Keys.Any(a => a.Contains("AzureAD.Standard.Preview")))
    {
      InvokeCommand.InvokeScript("Import-Module AzureAD -Force -ErrorAction Stop");
    }

    var res = InvokeCommand.InvokeScript(
      "Connect-AzureAD" +
      $" -TenantId '{principalId}'" +
      $" -AadAccessToken '{adGraphAccessToken.Token}'" +
      $" -AccountId {adGraphAccessToken.ClientId}");

    WriteObject(res);
  }
}

[Cmdlet(VerbsCommon.Get, "ImmyAuthToken", DefaultParameterSetName = "Endpoint")]
[OutputType(typeof(AzureAuthParameters))]
internal class GetImmyAuthTokenCommand : ServiceScopePSCmdlet
{
  [Parameter(Position = 0, Mandatory = false, ParameterSetName = "Endpoint")]
  [ValidateSet("MSGraph", "AzureAD", "KeyVault")]
  public AzureResourceAlias Endpoint { get; set; } = AzureResourceAlias.MSGraph;


  [Parameter(Position = 0, Mandatory = true, ParameterSetName = "ResourceUri")]
  public Uri? ResourceUri { get; set; }

  [Parameter(Position = 1, HelpMessage = "Retrieve the auth header from the MSP tenant instead of the current tenant")]
  public SwitchParameter UseMSPTenant { get; set; }

  [Parameter()]
  SwitchParameter AsHashTable { get; set; }
  protected override void ProcessRecord()
  {
    var azureActions = ServiceScope.ServiceProvider.GetRequiredService<IAzureActions>();

    Tenant? tenant = null;

    if (UseMSPTenant && !ValidateCanUseMspTenant(out tenant)) return;

    if (tenant is null && InternalTenantId.HasValue)
    {
      // try fetching it from the internal tenant id
      tenant = LocalDbContext.GetTenantById(InternalTenantId.Value, includeAzData: true);
    }

    if (tenant == null)
    {
      WriteError(new ErrorRecord(new Exception("A tenant was not provided to the runspace"), "1", ErrorCategory.InvalidArgument, null));
      return;
    }

    if (tenant.AzureTenantLink?.AzureTenant?.PrincipalId is not { Length: > 0 })
    {
      WriteError(new ErrorRecord(new Exception($"Tenant: {tenant.Name} is not mapped to Azure. Map the customer under Settings->Azure and try again."), "1", ErrorCategory.InvalidArgument, tenant));
      return;
    }

    var accessToken = new JoinableTaskContext().Factory.Run(async delegate
    {
      return ResourceUri != null
        ? await azureActions.GetAzureTenantAuthParameters(tenant, resourceUri: ResourceUri.ToString())
        : await azureActions.GetAzureTenantAuthParameters(tenant, endpoint: Endpoint);
    });
    if (AsHashTable)
    {
      var bearerToken = "Bearer " + accessToken.Token;
      var authHeader = new Hashtable
      {
        { "Authorization", bearerToken }
      };
      WriteObject(authHeader);
    }
    else
    {
      WriteObject(accessToken);
    }
  }
}


[Cmdlet(VerbsCommon.Get, "ImmyAzureAuthHeader", DefaultParameterSetName = "Endpoint")]
[OutputType(typeof(System.Collections.IDictionary))]
internal class GetImmyAzureAuthHeaderCommand : ServiceScopePSCmdlet
{
  [Parameter(Position = 0, Mandatory = false, ParameterSetName = "Endpoint")]
  [ValidateSet("MSGraph", "AzureAD", "KeyVault")]
  public AzureResourceAlias Endpoint { get; set; } = AzureResourceAlias.MSGraph;


  [Parameter(Position = 0, Mandatory = true, ParameterSetName = "ResourceUri")]
  public Uri? ResourceUri { get; set; }

  [Parameter(Position = 1, HelpMessage = "Retrieve the auth header from the MSP tenant instead of the current tenant")]
  public SwitchParameter UseMSPTenant { get; set; }

  protected override void ProcessRecord()
  {
    var azureActions = ServiceScope.ServiceProvider.GetRequiredService<IAzureActions>();

    Tenant? tenant = null;
    if (UseMSPTenant && !ValidateCanUseMspTenant(out tenant)) return;


    if (tenant == null && InternalTenantId.HasValue)
    {
      // try fetching it from the internal tenant id
      tenant = LocalDbContext.GetTenantById(InternalTenantId.Value, includeAzData: true);
    }

    if (tenant == null)
    {
      WriteError(new ErrorRecord(new Exception("A tenant was not provided to the runspace"), "1", ErrorCategory.InvalidArgument, null));
      return;
    }

    if (tenant.AzureTenantLink?.AzureTenant?.PrincipalId is not { Length: > 0 })
    {
      WriteError(new ErrorRecord(new Exception($"Tenant: {tenant.Name} is not mapped to Azure. Map the customer under Settings->Azure and try again."), "1", ErrorCategory.InvalidArgument, tenant));
      return;
    }

    var accessToken = new JoinableTaskContext().Factory.Run(async delegate
    {
      return ResourceUri != null
        ? await azureActions.GetAzureTenantAuthParameters(tenant, resourceUri: ResourceUri.ToString())
        : await azureActions.GetAzureTenantAuthParameters(tenant, endpoint: Endpoint);
    });
    var bearerToken = "Bearer " + accessToken.Token;
    var authHeader = new Hashtable
    {
      { "Authorization", bearerToken }
    };
    WriteObject(authHeader);
  }
}

[Cmdlet("ConvertFrom", "Base64")]
internal class ConvertFromBase64Command : PSCmdlet
{
  [Parameter(
      Mandatory = true,
      Position = 0)]
  public required string InputString { get; set; }
  [Parameter(
     Mandatory = false, ParameterSetName = "AsString")]
  public SwitchParameter AsString { get; set; }
  [Parameter(
      Mandatory = false,
      ParameterSetName = "AsString")]
  [ValidateSet("ASCII", "Unicode", "UTF-8")]
  public string Encoding { get; set; } = "UTF-8";
  protected override void ProcessRecord()
  {
    var bytes = Convert.FromBase64String(InputString);
    if (!AsString.IsPresent)
    {
      WriteObject(bytes, true);
    }
    else
    {
      switch (Encoding)
      {
        case "UTF-8":
          WriteObject(System.Text.Encoding.UTF8.GetString(bytes));
          break;
        case "ASCII":
          WriteObject(System.Text.Encoding.ASCII.GetString(bytes));
          break;
        case "Unicode":
          WriteObject(System.Text.Encoding.Unicode.GetString(bytes));
          break;
      }
    }
  }
}

[Cmdlet("ConvertTo", "Base64")]
[OutputType(typeof(string))]
internal class ConvertToBase64Command : PSCmdlet
{
  [Parameter(Position = 0,
    ValueFromPipeline = true)]
  public required PSObject InputObject { get; set; }
  [Parameter(
      Mandatory = false,
      Position = 1)]
  [ValidateSet("ASCII", "Unicode", "UTF-8")]
  public string Encoding { get; set; } = "UTF-8";

  protected override void ProcessRecord()
  {
    if (InputObject.BaseObject is byte[] inputBytes)
    {
      WriteObject(Convert.ToBase64String(inputBytes));
      return;
    }

    if (InputObject.BaseObject is string inputStr)
    {
      switch (Encoding)
      {
        case "ASCII":
          WriteObject(Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes(inputStr)));
          break;
        case "Unicode":
          WriteObject(Convert.ToBase64String(System.Text.Encoding.Unicode.GetBytes(inputStr)));
          break;
        case "UTF-8":
          WriteObject(Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(inputStr)));
          break;
        default:
          WriteError(new ErrorRecord(
            new ArgumentException("InputObject must be a string or byte[]"), "1", ErrorCategory.InvalidArgument, InputObject));
          break;
      }
    }
  }
}

[Cmdlet("Get", "KeyedHash")]
internal class GetKeyedHashCommand : PSCmdlet
{
  [Parameter(Mandatory = true, ValueFromPipeline = true, ParameterSetName = "InputBytesKeyBytes")]
  [Parameter(Mandatory = true, ValueFromPipeline = true, ParameterSetName = "InputBytesKeyString")]
  public required byte[] InputBytes { get; set; }

  [Parameter(Mandatory = true, ValueFromPipeline = true, ParameterSetName = "InputStringKeyString")]
  [Parameter(Mandatory = true, ValueFromPipeline = true, ParameterSetName = "InputStringKeyBytes")]
  public required string InputString { get; set; }

  [Parameter(ParameterSetName = "InputStringKeyString")]
  [Parameter(ParameterSetName = "InputStringKeyBytes")]
  [ValidateSet("ASCII", "Unicode", "UTF-8", "Base64")]
  [Alias("Encoding")]
  public string InputEncoding { get; set; } = "UTF-8";

  [Parameter(Mandatory = true, ParameterSetName = "InputBytesKeyBytes")]
  [Parameter(Mandatory = true, ParameterSetName = "InputStringKeyBytes")]
  public required byte[] KeyBytes { get; set; }
  [Parameter(Mandatory = true, ParameterSetName = "InputStringKeyString")]
  [Parameter(Mandatory = true, ParameterSetName = "InputBytesKeyString")]
  public required string KeyString { get; set; }

  [Parameter()]
  [ValidateSet("ASCII", "Unicode", "UTF-8", "Base64")]
  public string KeyEncoding { get; set; } = "UTF-8";

  [Parameter()]
  [ValidateSet("HMACSHA1", "HMACMD5", "HMACSHA256", "HMACSHA384", "HMACSHA512")]
  public string Algorithm { get; set; } = "HMACSHA1";

  [Parameter()]
  public SwitchParameter AsBase64 { get; set; }

  protected override void ProcessRecord()
  {
    HashAlgorithm hashAlgorithm;

    byte[] inputBytes = !string.IsNullOrEmpty(InputString) ? TextHelpers.GetBytes(InputString, InputEncoding) : InputBytes;
    byte[] keyBytes = !string.IsNullOrEmpty(KeyString) ? TextHelpers.GetBytes(KeyString, KeyEncoding) : KeyBytes;

    hashAlgorithm = Algorithm switch
    {
      "HMACSHA1" => new HMACSHA1(keyBytes),
      "HMACMD5" => new HMACMD5(keyBytes),
      "HMACSHA256" => new HMACSHA256(keyBytes),
      "HMACSHA384" => new HMACSHA384(keyBytes),
      "HMACSHA512" => new HMACSHA512(keyBytes),
      _ => throw new NotImplementedException(),
    };

    try
    {
      byte[] result = hashAlgorithm.ComputeHash(inputBytes);

      if (AsBase64)
        WriteObject(Convert.ToBase64String(result));
      else
        WriteObject(result);
    }
    finally
    {
      hashAlgorithm.Dispose();
    }
  }
}

[Cmdlet("Get", "Hash")]
internal class GetHashCommand : PSCmdlet
{
  [Parameter(Mandatory = true, ValueFromPipeline = true, ParameterSetName = "InputBytes")]
  public required byte[] InputBytes { get; set; }

  [Parameter(Mandatory = true, ValueFromPipeline = true, ParameterSetName = "InputString")]
  public required string InputString { get; set; }

  [Parameter(ParameterSetName = "InputString")]
  [ValidateSet("ASCII", "Unicode", "UTF-8", "Base64")]
  [Alias("Encoding")]
  public string InputEncoding { get; set; } = "UTF-8";

  [Parameter()]
  [ValidateSet("MD5", "SHA1", "SHA256", "SHA384", "SHA512")]
  public string Algorithm { get; set; } = "MD5";

  [Parameter()]
  public SwitchParameter AsBase64 { get; set; }
  [Parameter()]
  public SwitchParameter AsHex { get; set; }

  protected override void ProcessRecord()
  {
    byte[] inputBytes = !string.IsNullOrEmpty(InputString) ? TextHelpers.GetBytes(InputString, InputEncoding) : InputBytes;

    HashAlgorithm hashAlgorithm = Algorithm switch
    {
      "MD5" => MD5.Create(),
      "SHA1" => SHA1.Create(),
      "SHA256" => SHA256.Create(),
      "SHA384" => SHA384.Create(),
      "SHA512" => SHA512.Create(),
      _ => throw new NotImplementedException(),
    };

    try
    {
      byte[] result = hashAlgorithm.ComputeHash(inputBytes);

      if (AsBase64 && AsHex)
        throw new ArgumentException("AsBase64 and AsHex cannot be used together");

      if (AsHex)
        WriteObject(Convert.ToHexString(result));
      else if (AsBase64)
        WriteObject(Convert.ToBase64String(result));
      else
        WriteObject(result);
    }
    finally
    {
      hashAlgorithm.Dispose();
    }
  }
}

[Cmdlet(VerbsCommon.Add, "UriQueryParameter")]
internal class AddUriQueryParameter : PSCmdlet
{
  [Parameter(Position = 1, Mandatory = true, ValueFromPipeline = true)]
  public required Uri Uri { get; set; }
  [Parameter(Position = -1)]
  public Hashtable Parameter { get; set; } = [];
  [Parameter(Mandatory = false, HelpMessage = "Specified parameters will be completely removed from existing query parameters before Parameter(s) are added.")]
  public string[] OverwriteParameters { get; set; } = [];
  protected override void ProcessRecord()
  {
    // Create a http name value collection
    var nvCollection = System.Web.HttpUtility.ParseQueryString(Uri.Query);
    foreach (string key in Parameter.Keys)
    {
      // Removing a key that doesn't exist doesn't throw an exception so we can just call Remove
      foreach (var overwriteKey in OverwriteParameters)
      {
        if (!Parameter.ContainsKey(overwriteKey))
          WriteError(new ErrorRecord(new ArgumentException($"Overwrite-parameter '{overwriteKey}' does not exist in the input table"), "1", ErrorCategory.InvalidArgument, overwriteKey));
        nvCollection.Remove(overwriteKey);
      }
      var value = Parameter[key];
      if (value is Array values)
      {
        foreach (var v in values)
        {
          nvCollection.Add(key, v.ToString());
        }
      }
      else
        nvCollection.Add(key, Parameter[key]?.ToString());
    }
    // Build the uri
    var uriRequest = new UriBuilder(Uri);
    uriRequest.Query = nvCollection.ToString();
    WriteObject(uriRequest.Uri.ToString());
  }
}

[Cmdlet("Stop", "ImmySession")]
[OutputType(typeof(string))]
internal class StopImmySessionCommand : RunContextPSCmdlet
{
  [Parameter]
  public string PendingMessage { get; set; } = "Cancelling session #{SessionId}...";

  [Parameter]
  public string SuccessMessage { get; set; } = "Cancelled session #{SessionId} successfully.";

  [Parameter]
  public string FailureMessage { get; set; } = "Failed to cancel session #{SessionId}.";

  protected override void ProcessRecord()
  {
    if (RunContext is ISessionRunContext sessionRunContext)
    {
      string InsertSessionId(string s) => s.Replace("{SessionId}", sessionRunContext.SessionId.ToString());

      if (RunContext is IActionRunContext actionRunContext)
      {
        new JoinableTaskContext().Factory.Run(() => actionRunContext.CompleteAction(MaintenanceActionResult.Cancelled,
          resultReasonMsg: InsertSessionId(PendingMessage)));
      }

      Domain.Helpers.TaskExtensions.Forget(Task.Run(async () =>
      {
        using var scope =
          this.DemandVariableValue(SessionStateEntries.ServiceScopeFactoryVariableEntry).CreateScope();
        var sessionLogUpdateQueue =
          scope.ServiceProvider.GetRequiredService<ISessionLogUpdateQueue>();
        var sessionActions = scope.ServiceProvider.GetRequiredService<IMaintenanceSessionActions>();
        var now = DateTime.UtcNow;
        await sessionActions.TryCancelSession(sessionRunContext.SessionId);
        await sessionLogUpdateQueue.AddSessionLog(
          new SessionLog
          {
            Message = InsertSessionId(SuccessMessage),
            MaintenanceSessionId = sessionRunContext.SessionId,
            Time = now
          });
      }));
    }
    else
      WriteError(
        new ErrorRecord(
          new Exception("Unable to cancel session outside of run context."),
          "cancel-session-fail-out-of-run-context",
          ErrorCategory.InvalidOperation,
          null));
  }
}

[Cmdlet(VerbsData.Expand, "String")]
[OutputType(typeof(string))]
internal class ExpandStringCommand : PSCmdlet
{
  [Parameter(
      Mandatory = true,
      Position = 0,
    ValueFromPipeline = true)]
  public required string InputString { get; set; }

  protected override void ProcessRecord()
  {
    WriteObject(InvokeCommand.ExpandString(InputString));
  }
}

[Cmdlet(VerbsCommon.New, "LiteralString")]
[OutputType(typeof(LiteralString))]
internal class NewLiteralStringCommand : PSCmdlet
{
  [Parameter(
      Mandatory = true,
      Position = 0,
    ValueFromPipeline = true)]
  public required string InputString { get; set; }

  protected override void ProcessRecord()
  {
    WriteObject(new LiteralString(InputString));
  }
}

/// <summary>
/// Darren's code
/// </summary>
[Cmdlet(VerbsCommon.Get, "UnboundParameters")]
public class GetUnboundParametersCommand : PSCmdlet, IDynamicParameters
{
  [Parameter()]
  public object? InputObject { get; set; }

  public object? GetDynamicParameters()
  {
    return InputObject;
  }
  protected override void ProcessRecord()
  {
    if (Context.CurrentCommandProcessor is CommandProcessor processor)
    {
      WriteObject(processor.CmdletParameterBinderController?.get_UnboundArguments());
    }
  }
}

[Cmdlet(VerbsCommon.Join, "ScriptBlock")]
[OutputType(typeof(ScriptBlock))]
internal class JoinScriptBlockCommand : PSCmdlet
{
  [Parameter(Position = 0, ValueFromPipeline = true, ValueFromRemainingArguments = true, Mandatory = true)]
  public ScriptBlock? ScriptBlock { get; set; }

  private readonly StringBuilder _builder = new();

  protected override void BeginProcessing()
  {
    base.BeginProcessing();
    _builder.Clear();
  }

  protected override void ProcessRecord()
  {
    if (ScriptBlock is not null)
      _builder.AppendLine(ScriptBlock.ToString());
  }

  protected override void EndProcessing()
  {
    if (_builder.Length > 0)
    {
      WriteObject(ScriptBlock.Create(_builder.ToString()));
      _builder.Clear();
    }
    base.EndProcessing();
  }
}

[Cmdlet(VerbsCommon.Set, "ImmySession")]
internal class SetImmySession : RunContextPSCmdlet
{
  [Parameter(Position = 0, Mandatory = true)]
  public RebootPreference RebootPreference { get; set; }

  protected override void ProcessRecord()
  {
    if (this.RunContext?.Args?.RebootPreference is RebootPreference)
    {
      var oldRebootPreference = this.RunContext.Args.RebootPreference;
      this.RunContext.Args.RebootPreference = RebootPreference;
      this.InvokeCommand.InvokeScript("Set-Variable -Name 'RebootPreference' -Value $args[0] -Scope Global -Force", RebootPreference);
      WriteVerbose($"Updated RebootPreference from {oldRebootPreference} to {RebootPreference}");
    }
    else
    {
      WriteWarning("Unable to set reboot preference outside of a session.");
    }
  }
}
