using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Management.Automation;
using System.Net.Mail;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.PersonExtensions;
using Immybot.Backend.Application.DbContextExtensions.SmtpConfigExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Shared.PowerShell.Attributes;
using Immybot.Shared.Primitives;
using Immybot.Shared.Primitives.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

[Cmdlet("Send", "ImmyEmail")]
internal class SendImmyEmailCommand : RunContextPSCmdlet
{
  [Parameter(Mandatory = true, Position = 0)]
  public string Subject { get; set; } = string.Empty;

  [Parameter(Mandatory = true, Position = 1)]
  public string Body { get; set; } = string.Empty;

  [Parameter(Mandatory = false, Position = 2)]
  public List<string> To { get; set; } = [];

  [Parameter(Mandatory = false, Position = 3)]
  public List<string> Bcc { get; set; } = [];

  protected override void ProcessRecord()
  {
    var (branding, fromAddress) = LocalDbContext.GetCurrentBrandingForTenant(RunContext.TenantId);
    if (string.IsNullOrEmpty(fromAddress))
    {
      WriteObject(Mailer.DefaultBrandingMissingFromAddressMessage);
      return;
    }

    var smtp = LocalDbContext.GetApplicableSmtpConfigForTenant(RunContext.TenantId);
    if (smtp is null) throw new InvalidOperationException("Smtp could not be found");
    var (template, _) = EmailTemplate.GetBrandedTemplate(Body, Subject, branding);

    try
    {
      if (To.Count != 0)
      {
      // to was supplied so let's not use the run context to send the email
        var smtpActions = ServiceScope.ServiceProvider.GetRequiredService<ISmtpConfigActions>();

        var mailer = new Mailer(
          smtp.Port,
          smtp.Host,
          smtp.EnableSSL,
          smtp.Timeout,
          smtp.UseAuthentication,
          smtp.Username,
          string.IsNullOrEmpty(smtp.PasswordHash) ? null : smtpActions.GetPassword(smtp.PasswordHash));

        var bcc = new MailAddressCollection();
        foreach (var bccAddr in Bcc)
        {
          bcc.Add(bccAddr);
        }

        var to = new MailAddressCollection();
        foreach (var toAddr in To)
        {
          to.Add(toAddr);
        }
        var from = new MailAddress(fromAddress);
        var sender = from;

        mailer.Send(template, Subject, bcc, to, from, sender, true);
      }
      else
      {
        // send through immy email
        var immyEmail = ServiceScope.ServiceProvider.GetRequiredService<IImmyEmail>();
        new JoinableTaskContext().Factory.Run(async () =>
          await immyEmail.SendEmail(RunContext, template, branding.MascotName ?? string.Empty, fromAddress, Subject));
      }
      WriteObject("Sent");
    }
    catch (Exception ex)
    {
      ErrorCategory errorCategory;
      if (ex is SmtpException smtpEx)
      {
        errorCategory = smtpEx.StatusCode switch
        {
          SmtpStatusCode.GeneralFailure => ErrorCategory.OperationTimeout,
          SmtpStatusCode.TransactionFailed => ErrorCategory.OperationTimeout,
          _ => ErrorCategory.ResourceUnavailable
        };
      }
      else
      {
        errorCategory = ErrorCategory.NotSpecified;
      }

      var errorRecord = new ErrorRecord(ex, "SendEmailFailed", errorCategory, null);
      WriteError(errorRecord);
    }
  }
}

[Cmdlet(VerbsCommon.Get, "ProviderAgent")]
[OutputType(typeof(PSProviderAgent))]
internal class GetProviderAgentMetaScriptCommand : RunContextPSCmdlet
{
  [Parameter(ValueFromPipeline = true, ValueFromPipelineByPropertyName = true, Mandatory = false)]
  public PSComputer[] Computer { get; set; } = [];

  [Parameter(Position = 1, Mandatory = false)]
  public string? ProviderType { get; set; }

  protected override void ProcessRecord()
  {
    ProcessComputer(Computer, computer =>
    {
      // ensure computer's tenantId matches the run context's tenantId
      if (computer.TenantId != RunContext.TenantId)
      {
        WriteError(
          new ErrorRecord(
            new Exception("Cross-Tenant Exception"), "CrossTenantException", ErrorCategory.InvalidOperation, computer));
        return;
      }

      List<ProviderAgent> agents;
      if (!string.IsNullOrEmpty(ProviderType))
      {
        var providerType = GetProviderTypeByNameOrTag(ProviderType);
        agents = computer.Agents.Where(a => a.ProviderLink?.ProviderTypeId == providerType?.ProviderTypeId).ToList();
      }
      else
      {
        agents = computer.Agents.ToList();
      }
      var providerTypes = ProviderActions.GetAllProviderTypes();
      foreach (var a in agents)
      {
        var providerType = providerTypes.First(b => b.ProviderTypeId == a.ProviderLink?.ProviderTypeId);
        var dto = new PSProviderAgent
        {
          // Core identification properties
          ProviderLinkId = a.ProviderLinkId,
          ExternalAgentId = a.ExternalAgentId,
          ExternalClientId = a.ExternalClientId,
          ComputerId = a.ComputerId,

          // Provider information
          ProviderLinkName = a.ProviderLink?.Name ?? string.Empty,
          ProviderType = providerType.DisplayName,

          // Client information
          ExternalClientName = a.ProviderClient?.ExternalClientName,

          // Status and capabilities
          IsOnline = a.IsOnline,
          SupportsRunningScripts = a.SupportsRunningScripts,
          AgentVersion = a.AgentVersion?.ToString(),

          // Device details
          DeviceName = a.DeviceDetails?.DeviceName,
          OperatingSystemName = a.DeviceDetails?.OperatingSystemName,
          SerialNumber = a.DeviceDetails?.SerialNumber,
          Manufacturer = a.DeviceDetails?.Manufacturer,
          DeviceId = a.DeviceDetails?.DeviceId,
          Domain = a.DeviceDetails?.Domain,
          AzureTenantId = a.DeviceDetails?.AzureTenantId,

          // Internal data
          InternalData = a.InternalData,

          // Timestamps
          DateAddedUTC = a.DateAddedUTC,
          LastUpdatedUTC = a.LastUpdatedUTC
        };
        WriteObject(dto, true);
      }
    });
  }
}

/// <summary>
/// MSP ONLY
/// Retrieves an IProvider and details about the provider link to be used in meta scripts
/// </summary>
[Cmdlet(VerbsCommon.Get, "ProviderInfo")]
[OutputType(typeof(ProviderInfo))]
internal class GetProviderInfoCommand : RunContextPSCmdlet
{
  [Parameter(
    Mandatory = true,
    Position = 0)]
  public required string ProviderType { get; set; }

  [Parameter(
    Mandatory = false,
    Position = 1)]
  public SwitchParameter IncludeClients
  {
    get { return _includeClients; }
    set { _includeClients = value; }
  }
  private bool _includeClients;

[Parameter(
    Mandatory = false,
    Position = 2)]
  public SwitchParameter IgnoreLinkedProvider
  {
    get { return _ignoreLinkedProvider; }
    set { _ignoreLinkedProvider = value; }
  }
  private bool _ignoreLinkedProvider;

  private class ProviderInfo
  {
    [Obsolete("Kept for backwards-compatibility.  Use ProviderLinkId instead in your scripts.")]
    public int RmmLinkid { get; set; }

    public int ProviderLinkId { get; set; }
    public string? LinkName { get; set; }
    public string? ProviderType { get; set; }
    public IRunScriptProvider? Provider { get; set; }
    public ICollection<Client> Clients { get; set; }
    public PSObject? Configuration { get; set; }
    public PSObject? ExtraData { get; set; }

    public PSObject ToPSObject()
    {
      var psObj = new PSObject(this);
      var fieldsToDisplay = new List<string> { "LinkName", "ProviderType" };
      if (this.Clients.Count > 0)
        fieldsToDisplay.Add("Clients");

      psObj.Members.Add(
        new PSMemberSet("PSStandardMembers", new PSMemberInfo[]
        {
            new PSPropertySet("DefaultDisplayPropertySet", fieldsToDisplay.ToArray())
        }));

      return psObj;
    }

    public ProviderInfo()
    {
      Clients = new HashSet<Client>();
    }

    public record Client(string Name, string ClientId, int? TenantId);
  }

  private PSObject WrapProvider(ProviderLink providerLink, ProviderTypeDto providerType, int tenantId, CancellationToken cancellationToken)
  {
    PSObject? providerLinkData = null;

    if (providerType.ConfigurationForm is not null)
    {
      var dict = new Dictionary<string, object?>();
      foreach (var item in providerLink.ProviderTypeFormData.EnumerateObject())
      {
        if (item.Value.ValueKind == JsonValueKind.Null) continue;

        // Remove sensitive data from output
        if (ProviderActions.GetProviderFormType(providerLink.ProviderTypeId) is { } formType)
        {
          var prop = formType.GetProperty(item.Name);
          if (prop?.GetCustomAttributes(typeof(PasswordAttribute), inherit: true).Length != 0)
          {
            dict.TryAdd(item.Name, "********");
            continue;
          }
        }
        dict.TryAdd(item.Name, item.Value);
      }
      if (ProviderActions.GetProviderFormType(providerLink.ProviderTypeId) is { } type)
      {
        // Handle static (C#) integrations
        var res = DynamicFormService.BindParameters(type, dict);
        providerLinkData = PowerShellHelpers.ConvertKeyValueDictionaryToPSObject(res.ConvertedParameters);
      }
      else
      {
        // Handle Dynamic (Powershell) integrations
        providerLinkData = PowerShellHelpers.ConvertKeyValueDictionaryToPSObject(dict);
      }
    }

    var providerLinkExtraData = new JoinableTaskContext().Factory.Run(async () => await ProviderActions
      .GetExtraDataAccessibleInMetascripts(providerLink, cancellationToken)
    );

    IRunScriptProvider? provider = null;
    try
    {
      provider = new JoinableTaskContext().Factory.Run(async () => await ProviderActions
        .GetRunScriptProvider(providerLink, cancellationToken)
        );
    }
    catch (Exception ex) when (!(ex is OperationCanceledException || ex is TaskCanceledException))
    {
      // don't include the provider in the response since it failed to be retrieved.
    }

    var info = new ProviderInfo()
    {
#pragma warning disable CS0618 // Type or member is obsolete
      RmmLinkid = providerLink.Id,
#pragma warning restore CS0618 // Type or member is obsolete
      ProviderLinkId = providerLink.Id,
      LinkName = providerLink.Name,
      ProviderType = providerType.DisplayName,
      Provider = provider,
      Configuration = providerLinkData,
      ExtraData = providerLinkExtraData,
    };

    if (IncludeClients)
    {
      IEnumerable<ProviderClient> clients;

      if (CanAccessMspResources)
      {
        clients = providerLink.ProviderClients;
      }
      else
      {
        // if triggered by non-msp tenant, then only include clients linked to the non-msp tenant
        clients = providerLink.ProviderClients.Where(a => a.LinkedToTenantId == tenantId);
      }

      info.Clients = clients
        .Select(a => new ProviderInfo.Client(a.ExternalClientName, a.ExternalClientId, a.LinkedToTenantId)).ToList();
    }

    return info.ToPSObject();
  }

  protected override void ProcessRecord()
  {
    var tenant = RunContext.Args.Tenant;
    var providerType = GetProviderTypeByNameOrTag(ProviderType);

    if (providerType is null) return;

    // If a ProviderLinkIdForMaintenanceItem is present, only return that provider. Unless _ignoreLinkedProvider is set to true
    if (!_ignoreLinkedProvider && this.TryGetVariableValueNullable(SessionStateEntries.ProviderLinkIdForMaintenanceItemVariableEntry) is { } providerLinkId)
    {
      var providerLink = ProviderStore.GetProviderLink(providerLinkId, includeClients: IncludeClients);
      if (providerLink is null) return;

      var providerTypeDto = ProviderActions.GetProviderType(providerType.ProviderTypeId, true);
      var providerInfo = WrapProvider(providerLink, providerTypeDto, tenant.Id, CancellationToken);
      WriteObject(providerInfo);

      return;
    }

    using var linksDisposable = ProviderStore.GetProviderLinks(
      providerTypeId: providerType.ProviderTypeId,
      includeClients: IncludeClients);

    foreach (var link in linksDisposable.Value)
    {
      CancellationToken.ThrowIfCancellationRequested();

      providerType = ProviderActions
        .GetAllProviderTypes(includeLinkFormSchemas: true)
        .FirstOrDefault(a => a.ProviderTypeId == link.ProviderTypeId);
      if(providerType is null) continue;

      var provider = WrapProvider(link, providerType, tenant.Id, CancellationToken);
      WriteObject(provider);
    }
  }
}

[Cmdlet(VerbsCommon.Find, "ImmyComputerWithDeviceId")]
[OutputType(typeof(ExpandedComputer))]
internal class FindImmyComputerWithDeviceid : RunContextPSCmdlet
{
  [Parameter(Mandatory = true, Position = 0)]
  public Guid DeviceId { get; set; }

  protected override void ProcessRecord()
  {
    var query = LocalDbContext.Computers
      .AsNoTracking()
      .Where(a => a.DeviceId == DeviceId);

    if (RunContext.ComputerId is { } computerId)
    {
      query = query.Where(a => a.Id != computerId);
    }

    if (CanAccessMspResources)
    {
      query = query.Where(a => a.TenantId == RunContext.TenantId);
    }

    var computer = query.AsExpandedComputers().FirstOrDefault();

    WriteObject(computer);
  }
}

[Cmdlet("Merge", "ImmyComputers")]
[OutputType(typeof(ExpandedComputer))]
internal class MergeImmyComputersMetaScript : RunContextPSCmdlet
{
  [Parameter(Position = 0, Mandatory = true)]
  public int ComputerId { get; set; }

  protected override void ProcessRecord()
  {
    if (!RunContext.IsComputerTarget)
    {
      WriteWarning("Not available in cloud scripts");
      return;
    }

    if (RunContext.ComputerId is not { } currentComputerId)
    {
      WriteWarning("No computer ID was found in the run context.");
      return;
    }

    var otherComputerId = ComputerId;

    if (!CanAccessMspResources)
    {
      // ensure both computers are from the tenant who triggered this
      var tenantIdOnComputerToMerge = LocalDbContext.Computers
        .AsNoTracking()
        .Select(a => new { a.Id, a.TenantId })
        .FirstOrDefault(a => a.Id == ComputerId)?
        .TenantId;

      if (RunContext.TenantId != tenantIdOnComputerToMerge)
      {
        WriteWarning("You are not allowed to merge these computers.");
        return;
      }
    }

    var computerToKeepId = currentComputerId < otherComputerId ? currentComputerId : otherComputerId;
    var computerToMergeId = currentComputerId < otherComputerId ? otherComputerId : currentComputerId;

    var cmd = ServiceScope.ServiceProvider.GetRequiredService<IMergeComputersCmd>();
    var res = new JoinableTaskContext().Factory.Run(async () => await cmd.Run(computerToKeepId, computerToMergeId));
    if (res.Message is not null)
    {
      WriteWarning(res.Message);
    }

    WriteObject(res.Success);
  }
}


[Cmdlet("Set", "ImmyDeviceId")]
internal class SetImmyDeviceIdCommand : RunContextPSCmdlet
{
  [Parameter(Mandatory = true, Position = 0)]
  public Guid DeviceId { get; set; }

  protected override void ProcessRecord()
  {
    if (RunContext.Args.Computer is null)
    {
      WriteError(
         new ErrorRecord(
           new Exception("Can only be run within a meta script context for a computer."),
           "SetImmyDeviceIdException",
           ErrorCategory.InvalidOperation,
           null));

      return;
    }

    // This prevents a dev/test device ID from getting overwritten
    // by inventory scripts.
    var agentOptions = ServiceScope.ServiceProvider.GetRequiredService<IOptions<ImmyAgentOptions>>();
    if (agentOptions.Value.UseDevTestAgents &&
        agentOptions.Value.DevTestAgents.Any(x => x.AgentDeviceId == RunContext.Args.Computer.DeviceId))
    {
      return;
    }

    var numRowsAffected = LocalDbContext.Computers
     .AsNoTracking()
     .IgnoreQueryFilters()
     .Where(a => a.Id == RunContext.Args.Computer.Id)
     .UpdateFromQuery(a => new Computer { DeviceId = DeviceId });

    if (numRowsAffected <= 0)
    {
      WriteWarning("The Device ID was not updated.");
    }
  }
}

[Cmdlet(VerbsCommon.Get, "Person")]
[OutputType(typeof(IDictionary))]
internal class GetPersonCloudScript : RunContextPSCmdlet
{
  [Parameter(Position = 0)]
  public Guid? AzureObjectId { get; set; }

  [Parameter(Position = 1)]
  public int? Id { get; set; }

  [Parameter(Position = 2)]
  public string? Upn { get; set; }

  [Parameter(Position = 3)]
  public string? OnPremisesSecurityIdentifier { get; set; }

  protected override void ProcessRecord()
  {
    var tenantId = RunContext.TenantId;

    var q = LocalDbContext.GetAllPersons()
      .Where(a => a.TenantId == tenantId);

    if (Id.HasValue)
      q = q.Where(a => a.Id == Id);

    if (AzureObjectId.HasValue)
      q = q.Where(a => a.AzurePrincipalId == AzureObjectId.ToString());

    if (!string.IsNullOrEmpty(Upn))
      q = q.Where(a => a.EmailAddress == Upn);

    if (!string.IsNullOrEmpty(OnPremisesSecurityIdentifier))
      q = q.Where(a => a.OnPremisesSecurityIdentifier == OnPremisesSecurityIdentifier);

    var res = q.Select(p => new
    {
      Id = p.Id,
      Name = p.FirstName + " " + p.LastName,
      AzureObjectId = p.AzurePrincipalId,
      Upn = p.EmailAddress,
      OnPremisesSecurityIdentifier = p.OnPremisesSecurityIdentifier
    }).ToList();

    WriteObject(res, true);
  }
}

[Cmdlet(VerbsCommon.New, "Person")]
internal class NewPersonCloudScript : RunContextPSCmdlet
{
  [Parameter(Position = 1, Mandatory = true)]
  public Guid AzureObjectId { get; set; }

  [Parameter(Position = 2, Mandatory = true)]
  public required string FirstName { get; set; }

  [Parameter(Position = 3, Mandatory = true)]
  public required string LastName { get; set; }

  [Parameter(Position = 4, Mandatory = true)]
  public required string Upn { get; set; }

  [Parameter(Position = 5)]
  public string? OnPremisesSecurityIdentifier { get; set; }

  [Parameter(Position = 6)]
  public int? TenantId { get; set; }

  protected override void ProcessRecord()
  {
    var tenantIdToUse = RunContext.TenantId;

    if (CanAccessMspResources && TenantId.HasValue)
    {
      tenantIdToUse = TenantId.Value;
    }

    var person = LocalDbContext.CreatePerson(new CreatePersonPayload(
      tenantIdToUse,
      AzureObjectId.ToString(),
      FirstName,
      LastName,
      Upn,
      OnPremisesSecurityIdentifier: OnPremisesSecurityIdentifier));

    var personResult = new
    {
      Id = person.Id,
      Name = person.FirstName + " " + person.LastName,
      AzureObjectId = person.AzurePrincipalId,
      Upn = person.EmailAddress,
      OnPremisesSecurityIdentifier = person.OnPremisesSecurityIdentifier
    };

    WriteObject(personResult);
  }
}

[Cmdlet(VerbsCommon.Set, "ImmyPrimaryUser")]
[OutputType(typeof(bool))]
internal class SetImmyPrimaryUserMetaScript : RunContextPSCmdlet
{
  [Parameter(Position = 0, ValueFromPipeline = true)]
  public int Id { get; set; }

  protected override void ProcessRecord()
  {
    var computerId = RunContext.ComputerId;
    if (!computerId.HasValue) throw new InvalidOperationException("No computer id was specified");

    var tenantId = RunContext.TenantId;

    var person = LocalDbContext.GetPersonById(Id);
    if (person is null) throw new InvalidOperationException($"Person with id {Id} does not exist");

    if (person.TenantId != tenantId)
    {
      throw new InvalidOperationException($"Person with id {Id} does not belong to tenant {RunContext.TenantName}");
    }

    var triggeredByName = RunContext is SessionRunContext sessionRunContext
      ? $"Maintenance Session #{sessionRunContext.SessionId}"
      : "MetaScript";
    if (RunContext.Args.ManuallyTriggeredBy is not null)
    {
      triggeredByName += " - " + RunContext.Args.ManuallyTriggeredBy;
    }

    new JoinableTaskContext().Factory.Run(async () =>
      await LocalDbContext.SetComputerPrimaryPersonId(computerId.Value,
        Id,
        new AuditUserDetails(null, null, triggeredByName)));

    WriteObject(true);
  }
}

[Cmdlet(VerbsCommon.Clear, "ImmyPrimaryUser")]
[OutputType(typeof(bool))]
internal class ClearImmyPrimaryUserMetaScript : RunContextPSCmdlet
{
  protected override void ProcessRecord()
  {
    var computerId = RunContext.ComputerId;
    if (!computerId.HasValue) throw new InvalidOperationException("No computer id was specified");

    var triggeredByName = RunContext is SessionRunContext sessionRunContext
      ? $"Maintenance Session #{sessionRunContext.SessionId}"
      : "MetaScript";

    if (RunContext.Args.ManuallyTriggeredBy is not null)
    {
      triggeredByName += " - " + RunContext.Args.ManuallyTriggeredBy;
    }

    new JoinableTaskContext().Factory.Run(async () =>
      await LocalDbContext.SetComputerPrimaryPersonId(computerId.Value,
        null,
        new AuditUserDetails(null, null, triggeredByName)));

    WriteObject(true);
  }
}

[Cmdlet(VerbsCommon.Get, "ImmyComputer")]
[OutputType(typeof(IDictionary))]
internal class GetImmyComputerMetaScript : RunContextPSCmdlet
{
  [Parameter(Position = 0, HelpMessage = "Specifies the target group filter to apply when retrieving computers")]
  public TargetGroupFilter? TargetGroupFilter { get; set; } = null;

  [Parameter(Position = 1, HelpMessage = "Specifies which inventory keys to include in the results")]
  [ValidateSet(typeof(SupportedInventoryKeys))]
  public string[]? InventoryKeys { get; set; } = null;

  [Parameter(Position = 2, HelpMessage = "Specifies the DeviceId of a specific computer to retrieve")]
  public Guid? DeviceId { get; set; }

  [Parameter(HelpMessage = "Include offline computers in the results")]
  public SwitchParameter IncludeOffline { get; set; } = false;

  [Parameter(HelpMessage = "Limit the results to only onboarding computers")]
  public SwitchParameter OnboardingOnly { get; set; } = false;

  [Parameter(HelpMessage = "Include tags in the computer information")]
  public SwitchParameter IncludeTags { get; set; } = false;

  [Parameter(Position = 3, HelpMessage = "Retrieve computers from the Parent tenant instead of the current tenant")]
  public SwitchParameter UseParentTenant { get; set; }

  protected override void ProcessRecord()
  {
    Tenant? tenant = null;

    if (UseParentTenant && !FeatureTracker.IsEnabled(FeatureEnum.GetImmyComputerUseParentTenantFeature))
    {
      WriteWarning(
        "The feature to allow parent tenant access is not enabled. The -UseParentTenant switch will be ignored. Contact Immybot support for assistance.");
      UseParentTenant = false;
    }

    if (UseParentTenant && RunContext is IActionRunContext actionRunContext)
    {
      var targetAssignment = TaskHelper.RunAsJoinableTask(async () => await actionRunContext.GetTargetAssignment());
      var adhocAllowAccess = actionRunContext.Session.JobArgs.AllowAccessToParentTenant;

      var deploymentAllowAccess = (targetAssignment?.AllowAccessToParentTenant ?? false) || adhocAllowAccess;

      if (!deploymentAllowAccess)
      {
        WriteWarning("This script cannot use the parent tenant because the deployment does not allow access to parent tenants.");
        UseParentTenant = false;
      }
    }

    if (UseParentTenant && !ValidateCanUseParentTenant(out tenant)) return;

    if (UseParentTenant && tenant is not null)
    {
      WriteDebug(
        $"-UseParentTenant was specified. Computers from parent tenant {tenant.Name} will be fetched instead.");
    }

    if (tenant == null && InternalTenantId.HasValue)
    {
      tenant = LocalDbContext.GetTenantById(InternalTenantId.Value);

      if (tenant == null)
      {
        WriteError(new ErrorRecord(
          new Exception("A tenant was not provided to the runspace"),
          "1", ErrorCategory.InvalidArgument, null));
        return;
      }
    }

    if (DeviceId != null)
    {
      var query = LocalDbContext.Computers
        .AsNoTracking()
        .IgnoreQueryFilters()
        .Where(a => a.DeviceId == DeviceId);

      if (InventoryKeys?.ToList() is { Count: > 0 } keysToInclude)
        query = query.Include(a => a.LatestInventoryScriptResults
          .Where(r => keysToInclude.Contains(r.InventoryKey)));

      if (IncludeTags)
      {
        query = query.Include(a => a.ComputerTags).ThenInclude(a => a.Tag);
        query = query.Include(a => a.Tenant!.TenantTags).ThenInclude(a => a.Tag);
        query = query.Include(a => a.PrimaryPerson!.PersonTags).ThenInclude(a => a.Tag);
      }

      if (!UseParentTenant && !CanAccessMspResources)
      {
        query = query.Where(a => a.TenantId == RunContext.TenantId);
      }

      var computer = query.FirstOrDefault();
      if (computer != null)
      {
        if (UseParentTenant && computer.TenantId == RunContext.TenantId)
        {
          WriteWarning(
            $"'UseParentTenant' is selected, but DeviceId '{DeviceId}' belongs to the current tenant: " +
            $"{RunContext.TenantName} (ID: {RunContext.TenantId})."
          );
        }

        var psObj = PowerShellHelpers.ConvertFromComputerToPSObject(new List<Computer> { computer }, InventoryKeys);
        WriteObject(psObj, true);
      }
      return;
    }

    var computerAssignmentActions = ServiceScope.ServiceProvider.GetRequiredService<IComputerAssignmentActions>();
    var isMetascript = RunContext.IsComputerTarget;
    var isCloudScript = !isMetascript;
    var tenantId = tenant?.Id;

    if (isCloudScript) // cloud script
    {
      new JoinableTaskContext().Factory.Run(() => computerAssignmentActions.GetComputersInTarget(
          TargetType.All,
          TargetGroupFilter ?? Domain.Models.TargetGroupFilter.All,
          tenantId: tenantId,
          excludeOffline: !IncludeOffline,
          excludeOnboarded: OnboardingOnly,
          withAgents: true,
          withTenant: true,
          withTags: IncludeTags,
          withPrimaryPerson: true,
          withInventoryKeyResults: InventoryKeys,
          asNoTracking: true,
          isForScheduleOrDeploymentResolution: false))
        .Using(computersListResult =>
        {
          if (!Stopping)
            WriteObject(PowerShellHelpers.ConvertFromComputerToPSObject(computersListResult.ToList(), InventoryKeys), true);
        });
    }
    else // meta script
    {
      if (TargetGroupFilter == null)
      {
        if (UseParentTenant)
        {
          new JoinableTaskContext().Factory.Run(() => computerAssignmentActions.GetComputersInTarget(
              TargetType.All,
              Domain.Models.TargetGroupFilter.All,
              tenantId: tenantId,
              excludeOffline: !IncludeOffline,
              excludeOnboarded: OnboardingOnly,
              withPrimaryPerson: true,
              withAgents: true,
              withTenant: true,
              withTags: IncludeTags,
              withInventoryKeyResults: InventoryKeys,
              asNoTracking: true,
              isForScheduleOrDeploymentResolution: false))
            .Using(computersListResult =>
            {
              if (!Stopping)
                WriteObject(PowerShellHelpers.ConvertFromComputerToPSObject(computersListResult.ToList(), InventoryKeys), true);
            });
        }
        else
        {
          if (RunContext.Args.Computer?.Id is int runContextComputerId)
          {
            var computer = LocalDbContext.GetComputerById(runContextComputerId,
              asNoTracking: true,
              excludeOnboarded: OnboardingOnly,
              includeAgents: true,
              includeTenant: true,
              includeTags: IncludeTags,
              includePrimaryPerson: true,
              withInventoryKeyResults: InventoryKeys);

            if (Stopping) return;
            if (computer != null)
              WriteObject(PowerShellHelpers.ConvertFrom(PSComputer.FromComputer(computer, InventoryKeys)), true);
            else
              WriteWarning($"Computer with ID {runContextComputerId} could not be found.");
          }
        }
        return;
      }

      new JoinableTaskContext().Factory.Run(() => computerAssignmentActions.GetComputersInTarget(
          TargetType.All,
          TargetGroupFilter.Value,
          tenantId: tenantId,
          excludeOffline: !IncludeOffline,
          excludeOnboarded: OnboardingOnly,
          withPrimaryPerson: true,
          withAgents: true,
          withTenant: true,
          withTags: IncludeTags,
          withInventoryKeyResults: InventoryKeys,
          asNoTracking: true,
          isForScheduleOrDeploymentResolution: false))
        .Using(computers =>
        {
          if (!Stopping)
            WriteObject(PowerShellHelpers.ConvertFromComputerToPSObject(computers.ToList(), InventoryKeys), true);
        });
    }
  }
}
