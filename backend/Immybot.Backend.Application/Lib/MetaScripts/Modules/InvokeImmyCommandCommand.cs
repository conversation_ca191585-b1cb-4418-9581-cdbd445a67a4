using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Management.Automation;
using System.Management.Automation.Language;
using System.Management.Automation.Remoting;
using System.Management.Automation.Remoting.Internal;
using System.Management.Automation.Runspaces;
using System.Text;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Lib.Helpers.Extensions;
using Immybot.Backend.Application.Lib.Scripts;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Abstractions.Device.Exceptions;
using Immybot.Shared.Scripts;
using Immybot.Shared.Telemetry;
using Microsoft.Extensions.Logging;
using StreamJsonRpc;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

[Cmdlet("Invoke", "ImmyCommand")]
[OutputType(typeof(string))]
internal class InvokeImmyCommandCommand : RunContextPSCmdlet, IDynamicParameters
{
  public InvokeImmyCommandCommand()
  {
    _scriptObject = new Script { Name = "" };
    _psDataWriter = new PSTaskDataStreamWriter(this);
  }

  private bool _disposed;

  [Parameter(Position = 0, Mandatory = true)]
  public required ScriptBlock ScriptBlock { get; set; }

  [Parameter()]
  public ArrayList ArgumentList { get; set; } = [];

  [Parameter(ValueFromPipeline = true, ValueFromPipelineByPropertyName = true)]
  public PSComputer[]? Computer { get; set; }

  [Parameter()]
  public string ContextString { get; set; } = "System";

  [Parameter(HelpMessage = "The number of seconds to allow each computer to run the ScriptBlock before giving up.")]
  public int Timeout { get; set; } = 120;

  [Parameter(HelpMessage = "Run the ScriptBlock simultaneously on all computers piped to Invoke-ImmyCommand (capped at 100 at a time)")]
  public SwitchParameter Parallel = false;

  [Parameter(HelpMessage = "Override the default timeout value (in seconds) for waiting for the script to start. This only has an effect when service bus script results are enabled in app preferences. The default value is specified in app preferences")]
  public int? ConnectTimeout { get; set; } = null;

  [Parameter(HelpMessage = "Disable warnings that are generated when a device does not start within the ConnectTimeout. This only has an effect when service bus script results are enabled in app preferences.")]
  public SwitchParameter DisableConnectTimeoutWarnings = false;

  [Parameter(HelpMessage = "Includes variables from the parent runspace")]
  public SwitchParameter IncludeLocals = false;

  [Parameter(HelpMessage = "The type of script (CommandLine/PowerShell)")]
  [Alias("ScriptLanguage")]
  public ScriptLanguage ScriptType = ScriptLanguage.PowerShell;

  [Parameter(HelpMessage = "The name of the script (used for metric tracking)")]
  public string? ScriptName;

  [Parameter]
  public ComputerCircuitBreakerPolicy CircuitBreakerPolicy = ComputerCircuitBreakerPolicy.Unrestricted;

  [Parameter(HelpMessage = "If the computer is offline, we will wait the specified timespan for an agent to connect before continuing.")]
  public TimeSpan? AgentConnectionWaitTimeout { get; set; }

  private readonly Script _scriptObject;
  private readonly PSTaskDataStreamWriter _psDataWriter;
  private readonly List<Computer> _computersToProcess = [];
  private static readonly string _terminateFromNoLoggedUserParamName = "TerminateFromNoLoggedOnUser";
  private bool _shouldTerminateOnNoLoggedOnUser;
  private readonly RuntimeDefinedParameterDictionary _dynamicParamsSet = [];

  public object GetDynamicParameters()
  {
    if (ContextString.Equals("user", StringComparison.InvariantCultureIgnoreCase))
      _dynamicParamsSet.AddRuntimeParameter<SwitchParameter>(_terminateFromNoLoggedUserParamName, helpMessage: "Determines if a NoLoggedOnUser error should result in a terminating exception.");

    return _dynamicParamsSet;
  }

  protected override void BeginProcessing()
  {
    var comparer = StringComparer.OrdinalIgnoreCase;
    ScriptName ??= this.TryGetVariableValue(SessionStateEntries.ScriptNameVariableEntry);
    var scriptCategory = this.TryGetVariableValueNullable(SessionStateEntries.ScriptCategoryVariableEntry);
    var skipPreflight = this.TryGetVariableValueNullable(SessionStateEntries.SkipPreflightVariableEntry) ?? false;
    var skipBusinessHoursCheck = this.TryGetVariableValueNullable(SessionStateEntries.SkipBusinessHoursCheckVariableEntry) ?? false;
    var parameters =
      IncludeLocals.IsPresent && IncludeLocals.ToBool()
        ? this.TryGetVariableValue(SessionStateEntries.ScriptVariablesVariableEntry)
          ?? new Dictionary<string, object?>(comparer)
        : new Dictionary<string, object?>(comparer);
    _shouldTerminateOnNoLoggedOnUser = _dynamicParamsSet.GetDynamicParamOrDefault<SwitchParameter>(_terminateFromNoLoggedUserParamName).IsPresent;
    base.BeginProcessing();

    ScriptBlock newScriptBlock = GetUsingParameters(ScriptBlock, parameters);
    var scriptBlockAst = (ScriptBlockAst)newScriptBlock.Ast;

    string scriptBlockString;
    if (scriptBlockAst.ParamBlock is not null || ScriptType is not ScriptLanguage.PowerShell)
      scriptBlockString = newScriptBlock.ToString();
    else
      scriptBlockString = $"Param({string.Join(",", parameters.Keys.Select(a => VariableUtil.ToSigned(a)))})\n{newScriptBlock}";

    var debugPreference = this.TryGetVariableValue<object>("DebugPreference");
    var verbosePreference = this.TryGetVariableValue<object>("VerbosePreference");

    if (debugPreference != null)
      parameters.TryAdd("DebugPreference", debugPreference);
    if (verbosePreference != null)
      parameters.TryAdd("VerbosePreference", verbosePreference);

    var combinedParameters = new Dictionary<string, object?>
    {
      { "Parameters", parameters },
      { "ArgumentList", ArgumentList }
    };

    var context = ContextString.ToLower().Contains("user")
      ? ScriptExecutionContext.CurrentUser
      : ScriptExecutionContext.System;

    _scriptObject.Name = ScriptName ?? string.Empty;
    _scriptObject.Action = scriptBlockString;
    _scriptObject.ScriptLanguage = ScriptType;
    _scriptObject.ScriptExecutionContext = context;
    _scriptObject.Variables = combinedParameters;
    _scriptObject.Timeout = Timeout;
    _scriptObject.ScriptCategory = scriptCategory ?? ScriptCategory.MaintenanceTaskSetter;
    _scriptObject.SkipPreflight = skipPreflight;
    _scriptObject.SkipBusinessHoursCheck = skipBusinessHoursCheck;
  }

  private static string EscapeExpandString(string input)
  {
    return input.Replace("`", "``").Replace("$", "`$");
  }

  private sealed class UsingVariable
  {
    public string UsingSurrogateVarName;
    public string MetaScriptVarName;
    public object? Value;
    public UsingVariable(string usingSurrogateVarName, string metaScriptVarName, object? value = null)
    {
      UsingSurrogateVarName = usingSurrogateVarName;
      MetaScriptVarName = metaScriptVarName;
      Value = value;
    }
  }

  private ScriptBlock GetUsingParameters(ScriptBlock scriptBlock, IDictionary<string, object?> parameters)
  {
    if (ScriptType != ScriptLanguage.PowerShell)
      return scriptBlock;

    // This returns each $using token.
    // If the same $using token is referenced 3 times, it will be listed 3 times
    var usingExpressions = scriptBlock.Ast
      .FindAll(ast => ast is UsingExpressionAst, true)
      .Cast<UsingExpressionAst>();

    if (!usingExpressions.Any())
      return scriptBlock;
    else
    {
      var usingVariableExpressions = usingExpressions.Select(a => a.SubExpression).Cast<VariableExpressionAst>().ToArray();
      string[] signedParameterNames = parameters.Keys.Select(a => VariableUtil.ToSigned(a)).ToArray();
      if (scriptBlock.Ast is not ScriptBlockAst scriptBlockAst)
      {
        throw new InvalidOperationException("scriptBlock.Ast is not a ScriptBlockAst (should not be possible).");
      }

      StringBuilder scriptBlockText;
      if (scriptBlockAst.ParamBlock is null)
      {
        // PowerShell's AST processing requires that no parameters are passed when there's no param block
        // So we get the base script without parameters first, then manually add the param block
        // This avoids AST processing issues while still maintaining proper $using variable handling
        var baseScript = scriptBlockAst.GetForInvokeCommandImpl(usingVariableExpressions, []);
        scriptBlockText = new StringBuilder($"param({string.Join(",", signedParameterNames)})\n{baseScript}");
      }
      else
      {
        scriptBlockText = new StringBuilder(scriptBlockAst.GetForInvokeCommandImpl(usingVariableExpressions, signedParameterNames));
      }
      var uniqueUsingVariableExpressions = usingVariableExpressions.DistinctBy(a => a.VariablePath.UserPath.ToLower());
      foreach (var varExpr in uniqueUsingVariableExpressions)
      {
        var variable = new UsingVariable(varExpr.ToSurrogateForUsing(), varExpr.VariablePath.UserPath);

        if (varExpr.VariablePath.IsDriveQualified && varExpr.VariablePath.DriveName.Equals("function", StringComparison.OrdinalIgnoreCase))
        {
          using var ps = PowerShell.Create(RunspaceMode.CurrentRunspace);
          ps.AddCommand("Get-Command").AddParameter("Name", varExpr.VariablePath.UnqualifiedPath());
          if (ps.Invoke<object>().FirstOrDefault() is PSObject { BaseObject: FunctionInfo functionInfo })
          {
            variable.Value = functionInfo.ScriptBlock;
          }
          else
            this.WriteWarning($"Value for ${variable.MetaScriptVarName} cannot be retrieved because it has not been set in the local runspace.");
        }
        else if (this.MyInvocation.CommandOrigin == CommandOrigin.Runspace)
        {
          variable.Value = this.GetVariableValue(variable.MetaScriptVarName);
        }
        else
        {
          var sessionStateVariable = this.SessionState.PSVariable.Get(variable.MetaScriptVarName);
          if (sessionStateVariable == null)
            this.WriteWarning($"Value for ${variable.MetaScriptVarName} cannot be retrieved because it has not been set in the local runspace.");
          else
            variable.Value = sessionStateVariable.Value;
        }

        if (variable.Value is ScriptBlock nestedScriptBlock)
        {
          ScriptBlock updatedScriptBlock = GetUsingParameters(nestedScriptBlock, parameters);
          // Since PowerShell intentionally deserializes ScriptBlocks into strings, and we automatically expand strings on the client, we must escape the ScriptBlock to preserve the syntax
          // If we don't do this, the variables in the ScriptBlock are replaced with empty string before we can even run [ScriptBlock]::Create()
          variable.Value = EscapeExpandString(updatedScriptBlock.ToString());
        }

        this.WriteDebug($"Adding Param: {variable.UsingSurrogateVarName} with value: {variable.Value}");

        if (!parameters.TryAdd(variable.UsingSurrogateVarName, variable.Value))
          this.WriteWarning($"Parameter '{variable.UsingSurrogateVarName}' already exists with value {parameters[variable.UsingSurrogateVarName]}. Skipping");
      }

      return ScriptBlock.Create(scriptBlockText.ToString());
    }
  }

  /// <summary>
  /// ProcessRecord will process either the computer from the pipeline, or the computer from the run context
  /// </summary>
  protected override void ProcessRecord()
  {
    base.ProcessRecord();
    ProcessComputer(Computer, _computersToProcess.Add);
  }

  private async Task ProcessComputer(Computer? a, bool catchExceptions)
  {
    try
    {
      var result = await Telemetry.WithActivity(ActivityType.Script,
        $"Invoke-ImmyCommand -> {nameof(RunContext.RunScriptAgainstComputerSerialized)}",
        async activity =>
        {
          activity?.AddTag("script.name", _scriptObject.Name);
          activity?.AddTag("script.id", _scriptObject.Id);
          activity?.AddTag("script.type", _scriptObject.ScriptType);
          activity?.AddTag("script.timeout_sec", Timeout);
          activity?.AddTag("script.agent_connection_wait_timeout_sec", AgentConnectionWaitTimeout?.TotalSeconds);
          activity?.AddTag("script.run_context.type", RunContext.GetType().Name);
          activity?.AddTag("script.computer.id", a?.Id);
          activity?.AddTag("script.computer.name", a?.ComputerName);

          return await RunContext.RunScriptAgainstComputerSerialized(
            _scriptObject,
            Timeout,
            CancellationToken,
            computer: a,
            psDataWriter: _psDataWriter,
            circuitBreakerPolicy: CircuitBreakerPolicy,
            agentConnectionWaitTimeout: AgentConnectionWaitTimeout
          );
        });

      if (result.DeserializationException is { } dx)
      {
        _psDataWriter.Add(new PSStreamObject(PSStreamObjectType.Warning,
          $"Error deserializing script response from {a?.ComputerName}: {dx.Message}"));
      }
    }
    // no logged on user exception should not be treated as terminating unless otherwise specified
    catch (RemoteInvocationException ex) when (ex.Is<NoLoggedOnUserException>(out var errorData) || ex.Is<CurrentUserException>(out errorData))
    {
      if (_shouldTerminateOnNoLoggedOnUser)
      {
        WritePSDataWriterError(ex, errorData.TypeName!, ErrorCategory.OperationStopped, a);
        throw;
      }

      // Using WriteCustomWarning to avoid threading issues with the base method, which can't be called outside main thread
      WritePSDataWriterWarning(ex.Message);
    }
    catch (Exception) when (!catchExceptions)
    {
      throw;
    }
    catch (EphemeralRpcFailedToStartTimeoutException ex)
    {
      if (!DisableConnectTimeoutWarnings)
      {
        WritePSDataWriterError(ex, nameof(EphemeralRpcFailedToStartTimeoutException), ErrorCategory.WriteError, a);
      }
    }
    catch (Exception ex) when (!ex.IsCancellationException())
    {
      GetLogger<InvokeImmyCommandCommand>()
        .LogError(ex,
          "Exception occurred while running a script on {ComputerName}",
          a?.ComputerName);
      WritePSDataWriterError(ex, ex.GetType().ToString(), ErrorCategory.WriteError, a);
    }
  }

  protected override void EndProcessing()
  {
    try
    {
      var options = new ExecutionDataflowBlockOptions
      {
        // TODO: Could add a parameter to cmdlet for this, to allow the user to specify batch size
        // For now, just keep immybot from exploding if the user runs against thousands of
        // computers by limiting them to only 100 computers at a time
        MaxDegreeOfParallelism = Parallel ? 100 : 1,
        CancellationToken = CancellationToken,
      };
      // If we have more than a single computer to process, we should throw a terminating exception
      // if the invocation fails.
      var processingMultiple = _computersToProcess.Count > 1;
      var ab = new ActionBlock<Computer>(c => ProcessComputer(c, catchExceptions: processingMultiple), options);

      // we still rely on the completion of the action block below to know when to close the data writer.
      // In situations where we have multiple targets, we may not / should not throw a terminating exception as only a subset of the
      // computers may have actually failed.
      // see: https://learn.microsoft.com/en-us/powershell/scripting/developer/cmdlet/cmdlet-error-reporting?view=powershell-7.3
      foreach (var computer in _computersToProcess) ab.Post(computer);
      ab.Complete();
      try
      {
        // Blocks until the task has completed. Pushes all data received into the cmdlet
        do
        {
          // This timeout is here just in the event all the output has been written to the pipeline,
          // but somehow we looped right at the moment the task wasn't marked as completed. That would cause us to block forever.
          _psDataWriter.WaitAndWriteSingle(TimeSpan.FromMilliseconds(250));
        } while (!ab.Completion.IsCompleted);
        if (!ab.Completion.IsFaulted) return;
        var err = new ErrorRecord(ab.Completion.Exception!.InnerException, "FailureToExecuteScriptAgainstComputer", ErrorCategory.OperationStopped, processingMultiple ? null : _computersToProcess[0].ComputerName);
        if ((err.Exception is PreflightScriptFailedException or EphemeralAgentSessionFailedToConnectException or AgentsOfflineException) || err.Exception.IsCancellationException())
        {
          ThrowImmyTerminatingError(err.Exception);
        }
        else
        {
          ThrowImmyTerminatingError(err);
        }
      }
      finally
      {
        // Indicate that we are done writing to the pipeline, and empty any remaining items to the buffer
        _psDataWriter.Close();
        _psDataWriter.WriteImmediate();
      }
    }
    finally
    {
      base.EndProcessing();
    }
  }

  /// <summary>
  /// Handle pipeline stop signal
  /// </summary>
  protected override void StopProcessing()
  {
    // if ps process tells us to stop, close the ps data writer
    _psDataWriter?.Close();
    _psDataWriter?.WriteImmediate();
  }

  protected override void Dispose(bool disposing)
  {
    if (_disposed) return;
    if (disposing)
    {
      _psDataWriter?.Dispose();
    }
    _disposed = true;
    base.Dispose(disposing);
  }

  private void WritePSDataWriterError(Exception ex, string errorId, ErrorCategory errorCategory, Computer? computer)
  {
    _psDataWriter.Add(
        new PSStreamObject(
          PSStreamObjectType.Error, new RemotingErrorRecord(
            new ErrorRecord(ex, errorId, errorCategory, computer?.Id),
            new OriginInfo(computer?.ComputerName, Guid.Empty))));
  }

  private void WritePSDataWriterWarning(string message)
  {
    _psDataWriter.Add(new PSStreamObject(PSStreamObjectType.Warning, message));
  }
}
