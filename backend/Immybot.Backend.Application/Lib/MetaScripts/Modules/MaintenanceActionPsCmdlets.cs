using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Management.Automation;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.Helpers;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Models;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

internal record InformationRecordInfo(object MessageData, string[] Tags, InformationRecord SourceRecord);

internal sealed record MaintenanceActionInfo
(
  int Id,
  string? Name,
  SemanticVersion? DetectedVersion,
  SemanticVersion? DesiredVersion,
  DesiredSoftwareState? DesiredSoftwareState,
  MaintenanceActionStatus Status,
  MaintenanceActionReason Reason,
  MaintenanceActionResult Result,
  MaintenanceActionResultReason? ResultReason,
  int? ParentId,
  IDictionary Parameters
)
{
  public MaintenanceActionInfo(MaintenanceAction a)
    : this(
        a.Id,
        a.MaintenanceDisplayName,
        a.DetectedVersion.ToAutomationSemanticVersion(formatExceptionMessagePrefix: a.MaintenanceDisplayName),
        a.DesiredVersion.ToAutomationSemanticVersion(formatExceptionMessagePrefix: a.MaintenanceDisplayName),
        a.DesiredSoftwareState,
        a.ActionStatus,
        a.ActionReason,
        a.ActionResult,
        a.ActionResultReason,
        a.ParentId,
        (string.IsNullOrWhiteSpace(a.Parameters) ? null : PSSerializer.Deserialize(a.Parameters) as IDictionary) ?? new Dictionary<string, object>())
  { }
}

[Cmdlet(VerbsCommon.Set, "ImmyMaintenanceActionProgress")]
internal class SetImmyMaintenanceActionProgressCommand : RunContextPSCmdlet
{
  [Parameter]
  public decimal? ManualProgressPercentComplete { get; set; } = null;

  [Parameter]
  public string? ManualProgressStatus { get; set; } = null;

  protected override void ProcessRecord()
  {
    if (RunContext is not IActionRunContext actionRunContext)
    {
      WriteError("Must be inside an action run context.",
        id: "Fail-OutOfActionRunContext");
      return;
    }

    var lockKey = LockKeys.MaintenanceAction.With(actionRunContext.SessionId, actionRunContext.ActionId);
    var lockOptions = CreateKeyedLockOptions
      .WithCallerName(GetRunContextScriptInvocationInfo())
      .ReceivingSelfEmittedEvents()
      .WithWatchdog(timeout: TimeSpan.FromSeconds(30),
        strategy: KeyedLockOptions.WatchdogStrategy.WatchdogTriggersCancellationWithLockAbort)
      .WithoutInputTokenPropagation();
    using var lockHandle = new JoinableTaskContext().Factory.Run(async () =>
    {
      return await actionRunContext.Args.KeyedLocker.LockAsync(lockKey, (ev) =>
      {
        var msg = ev.ToString();
        actionRunContext.AddLog(msg);
      }, lockOptions, lockWaitTimeout: null, token: actionRunContext.Args.StopProcessing);
    });

    MaintenanceAction action = actionRunContext.Action;

    if (!action.UsesManualProgressControl) return;

    if (ManualProgressPercentComplete is { } progressPercentComplete && actionRunContext.CurrentActionPhase is not null)
      new JoinableTaskContext().Factory.Run(() => actionRunContext.CurrentActionPhase.SetProgressPercentComplete(
        progressPercentComplete,
        progressStatus: ManualProgressStatus,
        isManualProgressControl: true));
  }
}

internal abstract class ImmyMaintenanceActionResolvingCommand : RunContextPSCmdlet
{
  [Parameter(Position = 0, ParameterSetName = "ByName")]
  public string? Name { get; set; }

  [Parameter(ParameterSetName = "ById")]
  public int? ActionId { get; set; }

  [Parameter(ParameterSetName = "Parent")]
  public SwitchParameter Parent { get; set; }

  protected abstract void ProcessAction(IActionRunContext context, MaintenanceAction action);

  protected override void ProcessRecord()
  {
    if (RunContext is IActionRunContext actionRunContext)
    {
      MaintenanceAction? action;

      if (Name is not null)
      {
        if (actionRunContext.Session.MaintenanceActions.FirstOrDefault(x => x.MaintenanceDisplayName == Name) is { } namedAction)
          action = namedAction;
        else
        {
          WriteError($"Unable to find maintenance action '{Name}'.", "Fail-ResolveName");
          return;
        }
      }
      else if (ActionId is { } actionId)
      {
        if (actionRunContext.Session.MaintenanceActions.FirstOrDefault(x => x.Id == actionId) is { } identifiedAction)
          action = identifiedAction;
        else
        {
          WriteError($"Unable to find maintenance action with Id = '{actionId}'.", "Fail-ResolveId");
          return;
        }
      }
      else if (Parent)
      {
        if (actionRunContext.Action.ParentId is { } parentId && actionRunContext.Session.MaintenanceActions.FirstOrDefault(x => x.Id == parentId) is { } parentAction)
          action = parentAction;
        else
        {
          WriteError($"No parent on current action.", "Fail-ResolveParent");
          return;
        }
      }
      else
      {
        WriteError($"No argument present indicating target action.", "Fail-NoActionArgument");
        return;
      }

      ProcessAction(actionRunContext, action);
    }
    else
      WriteError($"Unable execute outside of action run context.", "Fail-OutOfActionRunContext");
  }

  protected static async Task AddDependency(IActionRunContext context, MaintenanceAction from, MaintenanceAction to)
  {
    MaintenanceActionDependency dependency = new()
    {
      Dependent = from,
      DependsOn = to,
      DependentId = from.Id,
      DependsOnId = to.Id
    };
    to.Dependents.Add(dependency);
    from.DependsOn.Add(dependency);
    from.DependsOnActions.Add(to);

    await context.UpdateAction(to);
    await context.UpdateAction(from);
  }
}

[Cmdlet(VerbsCommon.Add, "ImmyMaintenanceActionDependent")]
internal class AddImmyMaintenanceActionDependentCommand : ImmyMaintenanceActionResolvingCommand
{
  protected override void ProcessAction(IActionRunContext context, MaintenanceAction action)
  {
    new JoinableTaskContext().Factory.Run(() => AddDependency(context, action, context.Action));
  }
}

[Cmdlet(VerbsCommon.Add, "ImmyMaintenanceActionDependency")]
internal class AddImmyMaintenanceActionDependencyCommand : ImmyMaintenanceActionResolvingCommand
{
  protected override void ProcessAction(IActionRunContext context, MaintenanceAction action)
  {
    new JoinableTaskContext().Factory.Run(() => AddDependency(context, context.Action, action));
  }
}

[Cmdlet(VerbsCommon.Add, "ImmyMaintenanceActionChild")]
internal class AddImmyMaintenanceActionChild : RunContextPSCmdlet
{
  [Parameter(Mandatory = true, Position = 0)]
  public string? Name { get; set; }

  [Parameter(HelpMessage = "The test result for this child action")]
  public bool? TaskTestResult { get; set; } = null;

  [Parameter(HelpMessage = "Set this to true if TaskTestResult has been determined but is null. Leave false if the test result should be determined by running the maintenance task's test method with the specified action parameters")]
  public bool HasDeterminedTaskTestResult { get; set; } = false;

  [Parameter(HelpMessage = "The get result for this child action. Setting this or HasDeterminedTaskGetResult will cause actions with Mode:Monitor to complete in the detection stage instead of getting scheduled for execution")]
  public string? TaskGetResult { get; set; } = null;

  [Parameter(HelpMessage = "Set this to true if TaskGetResult has been determined but is null. Leave false if the get result should be determined by running the maintenance task's get method with the specified action parameters")]
  public bool HasDeterminedTaskGetResult { get; set; } = false;

  [Parameter(HelpMessage = "Object to pass to maintenance task get/set/test scripts when this child action runs. Retrieve from $ActionParameters variable.")]
  public object? Parameters { get; set; }

  [Parameter(HelpMessage = "Allow progress to be manually set via Set-ImmyMaintenanceActionProgress cmdlet")]
  public SwitchParameter UseManualProgressControl { get; set; } = false;

  protected override void ProcessRecord()
  {
    if (Name == null)
    {
      WriteError("Unable to add child maintenance action - Name is required");
      return;
    }
    if (RunContext is not IActionRunContext {
      MaintenanceType: MaintenanceType.LocalMaintenanceTask or MaintenanceType.GlobalMaintenanceTask
    } actionRunContext)
    {
      WriteError($"Unable to add maintenance action '{Name}' outside of a maintenance task action.", "Fail-OutOfActionRunContext");
      return;
    }
    if (actionRunContext.Action.ParentId is not null)
    {
      // NB: This is to prevent someone from writing a script that adds child maintenance actions
      // infinitely-recursively. Perhaps we can add some recursion detection logic if we end up
      // needing the ability to add children from children
      WriteError($"Unable to add child maintenance action '{Name}' from a child maintenance action.", "Fail-NoNestedChildActions");
      return;
    }
    if (actionRunContext.Session.MaintenanceActions.FirstOrDefault(x => x.MaintenanceDisplayName == Name && x.ParentId == actionRunContext.ActionId) is not null)
    {
      WriteError($"Unable to add maintenance action '{Name}' - name is already in use.", "Fail-NameInUse");
      return;
    }
    var parentAction = actionRunContext.Action;

    MaintenanceAction action = new()
    {
      MaintenanceDisplayName = Name,
      MaintenanceSessionId = parentAction.MaintenanceSessionId,
      MaintenanceSession = parentAction.MaintenanceSession,
      DetectedVersion = parentAction.DetectedVersion,
      DesiredVersion = parentAction.DesiredVersion,
      DesiredSoftwareState = parentAction.DesiredSoftwareState,
      AssignmentId = parentAction.AssignmentId,
      AssignmentType = parentAction.AssignmentType,

      // Child action should have same maintenance task & mode as its parent.
      // I.e. the child and its parent will use the same test/set/get script
      MaintenanceIdentifier = parentAction.MaintenanceIdentifier,
      MaintenanceType = parentAction.MaintenanceType,
      MaintenanceTaskMode = parentAction.MaintenanceTaskMode,

      // If the test/get results are already set when this action goes through detection,
      // the detection resolver will short-circuit and not run the action's maintenance task
      TaskTestResult = TaskTestResult,
      MaintenanceTaskGetResult = TaskGetResult,
      HasDeterminedTaskTestResult = TaskTestResult != null || HasDeterminedTaskTestResult,
      HasDeterminedTaskGetResult = TaskGetResult != null || HasDeterminedTaskGetResult,

      // Child-specific properties
      Parameters = Parameters is not null ? PSSerializer.Serialize(Parameters) : null,
      ParentId = parentAction.Id,
      UsesManualProgressControl = UseManualProgressControl,

      // Type/Reason/Status will get filled in when this action goes through the detection resolver
      ActionType = MaintenanceActionType.Undetermined,
      ActionReason = MaintenanceActionReason.Undetermined,
      ActionStatus = MaintenanceActionStatus.NotStarted,
    };

    new JoinableTaskContext().Factory.Run(() => RunContext.AddMaintenanceAction(action));

    MaintenanceActionDependency dependency = new()
    {
      Dependent = actionRunContext.Action,
      DependsOn = action,
      DependentId = actionRunContext.ActionId,
      DependsOnId = action.Id
    };

    action.Dependents.Add(dependency);
    actionRunContext.Action.DependsOn.Add(dependency);
    actionRunContext.Action.DependsOnActions.Add(action);

    new JoinableTaskContext().Factory.Run(async () =>
    {
      await RunContext.UpdateAction(action);
      await RunContext.UpdateAction(actionRunContext.Action);
    });
  }
}

[Cmdlet(VerbsCommon.Get, "ImmyMaintenanceActionChildren")]
[OutputType(typeof(MaintenanceActionInfo[]))]
internal class GetImmyMaintenanceActionChildrenCommand : RunContextPSCmdlet
{
  protected override void ProcessRecord()
  {
    if (RunContext is IActionRunContext actionRunContext)
    {
      MaintenanceActionInfo[] children = actionRunContext.Session.MaintenanceActions.Where(x => x.ParentId == actionRunContext.ActionId).Select(x => new MaintenanceActionInfo(x)).ToArray();
      WriteObject(children);
    }
    else
      WriteError("Must be inside an action run context.", "Fail-OutOfActionRunContext");
  }
}
