using System;
using System.Collections.ObjectModel;
using System.Management.Automation;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.MetaScripts.Attributes;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

/// <summary>
/// Use this to invoke a scriptblock and cache the result in a local or global ImmyCache.
/// </summary>
[Cmdlet("Invoke", "CommandCached")]
[ProhibitFunctionOverride(ProhibitGlobal = true, ProhibitLocal = true)]
[OutputType(typeof(object))]
internal class InvokeCommandCachedCommand : ServiceScopePSCmdlet
{
  [Parameter(Mandatory = true)]
  public required string CacheKey { get; set; }

  [Parameter(Mandatory = true, HelpMessage = "ScriptBlock that will be executed when There is nothing cached under that key, or the previous value reached expiration.")]
  public required ScriptBlock ScriptBlock { get; set; }

  [Parameter(Mandatory = true, HelpMessage = "Time-To-Live for this cached item represented as a Timespan. May be created from 'New-Timespan'")]
  public TimeSpan TTL { get; set; }

  [Parameter(HelpMessage = "Bust the cache for this key, regardless if it hasn't expired yet.")]
  public SwitchParameter ForceUpdate { get; set; }

  private readonly CmdletXThreadHandler<InvokeCommandCachedCommand> _handler;
  private readonly static TimeSpan _watchdogTimerInterval = TimeSpan.FromSeconds(10);

  public InvokeCommandCachedCommand()
  {
    _handler = new(cmdlet: this);
  }
  protected override void BeginProcessing()
  {
    base.BeginProcessing();
    var WroteWatchdogNotEnabledMessage = false;
    if (!this.TryGetVariableValue(SessionStateEntries.RunContextVariableEntry, out var runContext))
    {
      WriteVerbose("Cmdlet is not running in a RunContext. Watchdog will not be enabled.");
      WroteWatchdogNotEnabledMessage = true;
    }
    var sessionContext = runContext as SessionRunContext;
    if (sessionContext is null && !WroteWatchdogNotEnabledMessage)
      WriteVerbose("Cmdlet is not running in the context of a session. Watchdog will not be enabled.");

    using (_handler)
    {
      _ = Task.Run(async () =>
      {
        try
        {
          var cacheRepo = ServiceScope!.ServiceProvider.GetRequiredService<IImmyCacheRepository>();
          var immyCache = await cacheRepo.GetDefaultRepositoryAsync();

          var lockOpts = CreateKeyedLockOptions
          .WithCallerName(GetRunContextScriptInvocationInfo("Invoke-CommandCached"))
          .WithDefaultEventFilter()
          .WithWatchdogConfig(sessionContext is not null ? new(Timeout: _watchdogTimerInterval + TimeSpan.FromSeconds(1), Strategy: KeyedLockOptions.WatchdogStrategy.WatchdogTriggersCancellationWithLockAbort) : null)
          .WithInputTokenCancellationStrategy(KeyedLockOptions.TokenStrategy.PropagateInputTokenCancellationWithLockAbort);


          var res =
          await immyCache.GetOrCreateAsync(
            CacheKey, async (lockHandle) =>
            {
              using var terminatorRegistration = lockHandle.CancellationToken.Register(() =>
              {
                var releaseReason = CancellationToken.IsCancellationRequested ? "Cancellation was requested" : "Watchdog detected deadlock";
                runContext!.AddLog($"Lock handle for atomic operation was forcefully released : {releaseReason}");

                CancelScript();
              });

              // Background task to handle feeding the watchdog
              if (sessionContext is not null)
              {
                _ = Task.Run(async () =>
                {
                  using var timer = new PeriodicTimer(_watchdogTimerInterval);
                  while (await timer.WaitForNextTickAsync(lockHandle.CancellationToken))
                  {
                    if (_handler.IsCompleted) break;

                    _handler.WriteVerbose("Watchdog feed alarm has been triggered. Consider ways to shorten lock dwell-time if this message appears regularly.");

                    if (sessionContext.Session.SessionStatus == SessionStatus.Running)
                    {
                      lockHandle.TryFeedWatchdog();
                      _handler.WriteVerbose("Session status indicates it is still running. Watchdog has been fed.");
                      continue;
                    }
                    _handler.WriteWarning($"Watchdog was not fed! SessionStatus: {sessionContext.Session.SessionStatus}. Lock will be forcefully released soon.");
                  }
                });
              }

              var res = await _handler.InvokeFuncAsync((_) => ScriptBlock.Invoke());
              return res;
            }, TTL, ForceUpdate, evt => _handler.WriteVerbose(evt.ToString()), lockOpts, token: CancellationToken
            );

          if (res is Collection<PSObject> resCollection)
          {
            foreach (var obj in resCollection)
              _handler.WriteObject(obj);

            return;
          }

          _handler.WriteObject(res);
        }
        catch (Exception ex)
        {
          _handler.WriteError(new(ex, "ImmyCacheValueSetErr", ErrorCategory.WriteError, CacheKey));
        }
        finally
        {
          _handler.Complete();
        }
      });


      // This will block until complete is called on the output
      // Token is not supplied here as doing so will cause the _handler object to be disposed of prematurely,
      // which means logs or output may not be written as the scriptblock usually needs some time to complete.
      // Additionally, by having the object be inaccessible, we will cause some pretty serious exceptions to be thrown as Powershell will not realize
      // the pointer is dereferenced and will try to access it anyway.
      _handler.BlockAndProcessRequests();
    }
  }
}
