using System;
using System.Management.Automation;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Domain.Infrastructure;
using Microsoft.Extensions.DependencyInjection;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

[Cmdlet("New", "ImmyWebHook")]
internal class NewImmyWebHookCmdlet : RunContextPSCmdlet
{
  [Parameter(Position = 0,HelpMessage = "How long the WebHook should exist. The default is 30 minutes")]
  public TimeSpan LifeTime { get; set; } = TimeSpan.FromSeconds(1800);

  protected override void ProcessRecord()
  {
    var webhookFactory = ServiceScope.ServiceProvider.GetRequiredService<IWebHookService>();
    WriteObject(webhookFactory.CreateWebHook(LifeTime));
  }
}

[Cmdlet("Wait", "ImmyWebHook")]
internal class WaitImmyWebHookCmdlet : RunContextPSCmdlet
{
  [Parameter(Position = 0,Mandatory = true, ValueFromPipeline = true)]
  public required WebHook WebHook { get; set; }
  [Parameter(Position = 1, HelpMessage = "The number of seconds to wait for a webhook event. Default is the lifetime of the webhook.")]
  public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(1800);

  protected override void EndProcessing()
  {
    try
    {
      WebHookTriggeredEvent? receivedEvent = null;
      using var waitHandle = new SemaphoreSlim(0, 1);
      var eventReceiver = ServiceScope.ServiceProvider.GetRequiredService<IDomainEventReceiver>();
      using var _ = eventReceiver.Subscribe<WebHookTriggeredEvent>(ev =>
      {
        if (ev.WebHookId != WebHook.Id)
        {
          return Task.CompletedTask;
        }

        receivedEvent = ev;
        waitHandle.Release();
        return Task.CompletedTask;
      });
      waitHandle.Wait(Timeout, CancellationToken);

      if (receivedEvent is not null)
      {
        WriteObject(receivedEvent);
      }
      else
      {
        WriteWarning("WebHook not triggered within timeout period");
      }
    }
    finally
    {
      base.EndProcessing();
    }
  }
}
