using System;
using System.Collections;
using System.Management.Automation;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

[Cmdlet(VerbsCommon.Get, "ImmyComputer")]
[OutputType(typeof(IDictionary))]
internal class GetImmyComputerFilterScriptCommand : ServiceScopePSCmdlet
{
  private IComputerAssignmentActions? _computerAssignmentActions;
  private int? _tenantId;
  private bool _includeChildTenants;
  private int? _computerId;

  /// <summary>
  /// Optional array of inventory keys that can return additional computer data
  /// </summary>
  [Parameter(Position = 0)]
  [ValidateSet(typeof(SupportedInventoryKeys))]
  public string[]? InventoryKeys { get; set; } = null;

  [Parameter(Position = 1, HelpMessage = "Specifies the target group filter to apply when retrieving computers")]
  public TargetGroupFilter? TargetGroupFilter { get; set; } = null;

  [Parameter(HelpMessage = "Limit the results to only onboarding computers")]
  public SwitchParameter OnboardingOnly { get; set; } = false;

  [Parameter()]
  public SwitchParameter IncludeTags { get; set; } = false;

  /// <summary>
  /// Default constructor used by powershell
  /// </summary>
  public GetImmyComputerFilterScriptCommand() { }

  /// <summary>
  /// Setup dependencies
  /// </summary>
  protected override void BeginProcessing()
  {
    base.BeginProcessing();
    _tenantId = this.TryGetVariableValueNullable(SessionStateEntries.FilterScriptTenantIdVariableEntry);
    _computerId = this.TryGetVariableValueNullable(SessionStateEntries.FilterScriptComputerIdVariableEntry);
    _includeChildTenants = this.TryGetVariableValueNullable(SessionStateEntries.FilterScriptIncludeChildTenantsVariableEntry) ?? false;
    _computerAssignmentActions = ServiceScope.ServiceProvider.GetRequiredService<IComputerAssignmentActions>();
  }

  /// <summary>
  /// Main logic goes here instead of ProcessRecord() since this command does not take anything down the pipeline.
  /// </summary>
  protected override void ProcessRecord()
  {
    CancellationToken.ThrowIfCancellationRequested();
    var targetType = TargetType.All;
    string? target = null;
    if (_computerId.HasValue)
    {
      targetType = TargetType.Computer;
      target = _computerId.Value.ToString();
    }
    else if (_tenantId.HasValue)
    {
      targetType = TargetType.AllForTenant;
    }

    if (_computerAssignmentActions is null)
      throw new InvalidOperationException("ComputerAssignmentActions is not initialized");

    new JoinableTaskContext().Factory.Run(() => _computerAssignmentActions.GetComputersInTarget(
        targetType,
        TargetGroupFilter ?? Domain.Models.TargetGroupFilter.All,
        target: target,
        tenantId: _tenantId,
        includeChildTenants: _includeChildTenants,
        withInventoryKeyResults: InventoryKeys,
        withPrimaryPerson: true,
        withTenant: true,
        withTags: IncludeTags,
        excludeOnboarded: OnboardingOnly,
        asNoTracking: true))
      .Using(computers =>
      {
        var response = PowerShellHelpers.ConvertFromComputerToPSObject(computers, InventoryKeys);
        WriteObject(response, true);
      });
  }
}
