using System;
using System.Management.Automation;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.MetaScripts.Attributes;
using Microsoft.Extensions.DependencyInjection;
using OneOf;

namespace Immybot.Backend.Application.Lib.MetaScripts.Modules;

/// <summary>
/// Use this to alter the expiry of a cache key.
/// </summary>
[Cmdlet(VerbsCommon.Set, "CacheKeyExpiration")]
[ProhibitFunctionOverride(ProhibitGlobal = true, ProhibitLocal = true)]
[OutputType(typeof(bool))]
internal class SetCacheKeyExpiration : RunContextPSCmdlet
{
  [Parameter(Mandatory = true)]
  public required string CacheKey { get; set; }
  [Parameter(Mandatory = true,
    HelpMessage = "Either a DateTime of when they key should expire, or a Timespan relative to now. If supplied with an empty Timespan, will be treated the same as -ExpireNow switch.",
    ParameterSetName = "NewTTL")]
  public OneOf<DateTime, TimeSpan> TTL { get; set; }
  [Parameter(HelpMessage = "Forces the key to expire now.", ParameterSetName = "ExpiresNow")]
  public SwitchParameter ExpiresNow { get; set; }
  private readonly CmdletXThreadHandler<SetCacheKeyExpiration> _handler;

  public SetCacheKeyExpiration()
  {
    _handler = new(cmdlet: this);
  }
  protected override void BeginProcessing()
  {
    base.BeginProcessing();

    using (_handler)
    {
      _ = Task.Run(async () =>
      {
        try
        {
          var cacheRepo = ServiceScope!.ServiceProvider.GetRequiredService<IImmyCacheRepository>();
          var immyCache = await cacheRepo.GetDefaultRepositoryAsync();
          var res = await immyCache.TryAlterKeyExpiryAsync(CacheKey, ExpiresNow.IsPresent ? TimeSpan.Zero : TTL, log: _handler.WriteVerbose, callerName: GetRunContextScriptInvocationInfo(), token: CancellationToken);
          _handler.WriteObject(res);
        }
        catch (Exception ex)
        {
          _handler.WriteError(new(ex, "ImmyCacheValueSetErr", ErrorCategory.WriteError, CacheKey));
        }
        finally
        {
          _handler.Complete();
        }
      });


      // This will block until complete is called on the output
      // Token is not supplied here as doing so will cause the _handler object to be disposed of prematurely,
      // which means logs or output may not be written as the scriptblock usually needs some time to complete.
      // Additionally, by having the object be inaccessible, we will cause some pretty serious exceptions to be thrown as Powershell will not realize
      // the pointer is dereferenced and will try to access it anyway.
      _handler.BlockAndProcessRequests();
    }
  }
}
