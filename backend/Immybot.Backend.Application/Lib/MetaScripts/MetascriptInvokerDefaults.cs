using System.Management.Automation;
using System.Management.Automation.Runspaces;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text.Json;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Lib.MetaScripts.Attributes;
using Immybot.Backend.Application.Lib.Providers;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Shared.Primitives;
using Immybot.Shared.Scripts;
using Immybot.Shared.PowerShell.Attributes;
using Immybot.Shared.Primitives.Attributes;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Newtonsoft.Json.Linq;

namespace Immybot.Backend.Application.Lib.MetaScripts;

public static class ModuleIntrinsicAccessors
{
  [UnsafeAccessor(UnsafeAccessorKind.StaticMethod)]
  public static extern string SetModulePath(ModuleIntrinsics m);
}

/// <summary>
/// Default powershell data for the metascript invoker
/// </summary>
public class MetascriptInvokerDefaults
{
  public static readonly string[] ApprovedModules =
  [
    "PSReadLine",
    "PowerShellEditorServices.psd1",
    "PowerShellEditorServices.Commands.psd1",
    "ScriptAnalyzer.dll",
    "Microsoft.Open",
    "Immybot.Backend.Application.DbContextExtension",
    "Microsoft.Open.Azure.AD.CommonLibrary.dll",
    "Microsoft.Graph",
    "Microsoft.Graph.Authentication",
    "Microsoft.Graph.Users",
    "Microsoft.Graph.Groups",
    "ImmyAnalyzerRules",
    "AzureAD.Standard.Preview",
    "AzureAD",
  ];

  [MethodImpl(MethodImplOptions.NoInlining)]
  public MetascriptInvokerDefaults()
  {
    // Add custom attributes to the built-in type accelerators
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(MediaAttribute));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(PasswordAttribute));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(DropdownAttribute));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(PersonAttribute));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(DisplayNameAttribute));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(OauthConsentAttribute));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(JsonElement));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(VerifyProviderResult));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(HealthCheckResult));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(IProviderClientDetails));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(IProviderAgentDetails));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(ScriptLanguage));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(IntegrationClient));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(ObjectResult));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(OpResult));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(JObject));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(ScriptTimeoutAttribute));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(JobSettingsAttribute));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(IDomainEventReceiver));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(ContentResult));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(SupportTicketCreationResult));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(SupportFormBranding));
    PowerShellTypeHelpers.AddCoreTypeAndTypeAccelerator(typeof(PSProviderAgent));
    PowerShellTypeHelpers.AddTypeAccelerator(typeof(IActionResult), nameof(IActionResult));
    PowerShellTypeHelpers.AddTypeAccelerator(typeof(Microsoft.AspNetCore.Http.HttpContext), nameof(Microsoft.AspNetCore.Http.HttpContext));
    PowerShellTypeHelpers.AddTypeAccelerator(typeof(IClientGroup), nameof(IClientGroup));

    // Populate default aliases/formats/variables that should be cloned when we create new InitialSessionStates
    InitialSessionState ss = InitialSessionState.CreateDefault2();
    DefaultAliases = new InitialSessionStateEntryCollection<SessionStateAliasEntry>(
      ss.Commands.Where(a => a is SessionStateAliasEntry).Cast<SessionStateAliasEntry>()
    )
    {
      new SessionStateAliasEntry("ac", "Add-Content", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("clear", "Clear-Host"),
      new SessionStateAliasEntry("compare", "Compare-Object", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("cpp", "Copy-ItemProperty", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("diff", "Compare-Object", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("gsv", "Get-Service", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("sleep", "Start-Sleep", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("sort", "Sort-Object", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("start", "Start-Process", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("sasv", "Start-Service", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("spsv", "Stop-Service", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("tee", "Tee-Object", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("write", "Write-Output", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("cat", "Get-Content"),
      new SessionStateAliasEntry("cp", "Copy-Item", string.Empty, ScopedItemOptions.AllScope),
      new SessionStateAliasEntry("ls", "Get-ChildItem"),
      new SessionStateAliasEntry("man", "help"),
      new SessionStateAliasEntry("mount", "New-PSDrive"),
      new SessionStateAliasEntry("mv", "Move-Item"),
      new SessionStateAliasEntry("ps", "Get-Process"),
      new SessionStateAliasEntry("rm", "Remove-Item"),
      new SessionStateAliasEntry("rmdir", "Remove-Item"),
      new SessionStateAliasEntry("cnsn", "Connect-PSSession", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("dnsn", "Disconnect-PSSession", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("ogv", "Out-GridView", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("shcm", "Show-Command", string.Empty, ScopedItemOptions.ReadOnly),
      new SessionStateAliasEntry("kill", "Stop-Process")
    };

    // Autocomplete will fail if there isn't an implementation of TabExpansion2
    // The default TabExpansion2 implementation may not be available in a Constrained Runspace, therefore we check and add it if not.
    // Note: Attempting to set the visibility of these commands to Private will cause Autocomplete to fail
    DefaultFunctions =
    [
      ss.Commands.FirstOrDefault(a => a.Name.ToLower() == "tabexpansion2")!
    ];

    DefaultFormats = new InitialSessionStateEntryCollection<SessionStateFormatEntry>
    {
      ss.Formats.Where(a => !a.FileName.Contains("DotNetTypes"))
    };

    // Bring in the necessary variables for PowerShell to operate correctly from InitialSessionState.BuiltInVariables
    // See: https://github.com/PowerShell/PowerShell/blob/master/src/System.Management.Automation/engine/InitialSessionState.cs
    DefaultVariables = new InitialSessionStateEntryCollection<SessionStateVariableEntry>
    {
      ss.Variables.LookUpByName("OutputEncoding"),
      ss.Variables.LookUpByName("ConfirmPreference"),
      ss.Variables.LookUpByName("DebugPreference"),
      ss.Variables.LookUpByName("ErrorActionPreference"),
      ss.Variables.LookUpByName("ProgressPreference"),
      ss.Variables.LookUpByName("VerbosePreference"),
      ss.Variables.LookUpByName("WarningPreference"),
      ss.Variables.LookUpByName("InformationPreference"),
      new SessionStateVariableEntry("ErrorView",
        ErrorView.ConciseView,
        ss.Variables.LookUpByName("ErrorView")[0].Description),
      ss.Variables.LookUpByName("NestedPromptLevel"),
      ss.Variables.LookUpByName("WhatIfPreference"),
      ss.Variables.LookUpByName("FormatEnumerationLimit"),
    };
  }

  public static readonly string ExecutingAssemblyLocation = Assembly.GetExecutingAssembly().Location!;
  public static readonly string AssemblyFolder = Path.GetDirectoryName(ExecutingAssemblyLocation)!;
  public static readonly string FormatsPath = Path.Combine(AssemblyFolder, "PowerShell", "Formats");
  public static readonly string ModulesPath = Path.Combine(AssemblyFolder, "PowerShell", "Modules");
  public static readonly string TypesPath = Path.Combine(AssemblyFolder, "PowerShell", "Types");
  public static readonly string CustomRulePath = Path.Combine(ModulesPath, "ImmyAnalyzerRules");
  public static readonly string PSScriptAnalyzerModulePath = Path.Combine(Path.GetFullPath(AssemblyFolder), "PSScriptAnalyzer");
  public static readonly string PSESBundledModulesPath = Path.Combine(AssemblyFolder, "PSESBundledModules");
  public static readonly string StartEditorServicesScriptPath = Path.Combine(PSESBundledModulesPath, "PowerShellEditorServices", "Start-EditorServices.ps1");
  public static readonly string PSModulePath = string.Join((OperatingSystem.IsWindows() ? ';' : ':'), ModulesPath, PSESBundledModulesPath);

  public InitialSessionStateEntryCollection<SessionStateAliasEntry> DefaultAliases { get; }
  public InitialSessionStateEntryCollection<SessionStateFormatEntry> DefaultFormats { get; }
  public InitialSessionStateEntryCollection<SessionStateCommandEntry> DefaultFunctions { get; }
  public InitialSessionStateEntryCollection<SessionStateVariableEntry> DefaultVariables { get; }
}
