using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Management.Automation;
using System.Management.Automation.Runspaces;
using System.Threading;
using CwAutomateProvider;
using Immybot.Backend.Application.DynamicProviders;
using Immybot.Backend.Application.Lib.MetaScripts.Modules;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Providers.CwControlProvider.PSModule;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.PowerShell.Commands;
using Microsoft.PowerShell.Commands.Utility;
using Microsoft.PowerShell.CrossCompatibility.Commands;
using Microsoft.PowerShell.EditorServices.Commands;
using Microsoft.Windows.PowerShell.ScriptAnalyzer.Commands;
using NCentralRmmProvider;

namespace Immybot.Backend.Application.Lib.MetaScripts;

public static class SessionStateEntries
{
  public static readonly SessionStateFormatEntry RegistryFormatEntry =
    new(Path.Combine(MetascriptInvokerDefaults.FormatsPath, "Registry.format.ps1xml"));

  public static readonly SessionStateFormatEntry FileSystemFormatEntry =
    new(Path.Combine(MetascriptInvokerDefaults.FormatsPath, "FileSystem.format.ps1xml"));

  public static readonly SessionStateFormatEntry VersionFormatEntry =
    new(Path.Combine(MetascriptInvokerDefaults.FormatsPath, "Version.format.ps1xml"));

  public static readonly SessionStateFormatEntry DateTimeFormatEntry =
    new(Path.Combine(MetascriptInvokerDefaults.FormatsPath, "DateTime.format.ps1xml"));

  public static readonly SessionStateFormatEntry ImmyBotFormatEntry =
    new(Path.Combine(MetascriptInvokerDefaults.FormatsPath, "ImmyBot.format.ps1xml"));

  private static SessionStateFunctionEntry? _writeErrorFunctionEntry;

  public static SessionStateFunctionEntry WriteErrorFunctionEntry
  {
    get
    {
      if (_writeErrorFunctionEntry is not null) return _writeErrorFunctionEntry;

      var writeErrorMetaData = new CommandMetadata(typeof(WriteErrorCommand));
      var writeErrorProxy = ProxyCommand.Create(writeErrorMetaData);
      _writeErrorFunctionEntry = new SessionStateFunctionEntry("Write-Error", writeErrorProxy);
      return _writeErrorFunctionEntry;
    }
  }

  public static readonly SessionStateCmdletEntry TeeObjectCommandEntry =
    new("Tee-Object", typeof(TeeObjectCmdlet), null);

  public static readonly SessionStateCmdletEntry SetStrictModeCommandEntry =
    new("Set-StrictMode", typeof(SetStrictModeCommand), null);

  public static readonly SessionStateCmdletEntry GetOtpCommandEntry =
    new("Get-Otp", typeof(GetOtpCommand), null);

  public static readonly SessionStateCmdletEntry ExpandStringCommandEntry =
    new("Expand-String", typeof(ExpandStringCommand), null);

  public static readonly SessionStateCmdletEntry JoinStringCommandEntry =
    new("Join-String", typeof(JoinStringCommand), null);

  public static readonly SessionStateCmdletEntry NewObjectCommandEntry =
    new("New-Object", typeof(NewObjectCommand), null);

  public static readonly SessionStateCmdletEntry NewLiteralStringCommandEntry =
    new("New-LiteralString", typeof(NewLiteralStringCommand), null);

  public static readonly SessionStateCmdletEntry GetGlobalSoftwareCommandEntry =
    new("Get-GlobalSoftware", typeof(GetGlobalSoftwareCommand), null);

  public static readonly SessionStateCmdletEntry SetImmySessionCommandEntry =
    new("Set-ImmySession", typeof(SetImmySession), null);

  public static readonly SessionStateCmdletEntry NewImmyWebHookCommandEntry =
    new("New-ImmyWebHook", typeof(NewImmyWebHookCmdlet), null);

  public static readonly SessionStateCmdletEntry WaitImmyWebHookCommandEntry =
    new("Wait-ImmyWebHook", typeof(WaitImmyWebHookCmdlet), null);

  public static readonly SessionStateCmdletEntry ImportModuleInternalCommandEntry =
    new("Import-ModuleInternal", typeof(ImportModuleCommand), null)
    {
      Visibility = SessionStateEntryVisibility.Private
    };

  public static readonly SessionStateCmdletEntry ImportModuleCommandEntry =
    new("Import-Module", typeof(ImportModuleInternalCommand), null);

  public static readonly SessionStateCmdletEntry NewModuleCommandEntry =
    new("New-Module", typeof(NewModuleCommand), null);

  public static readonly SessionStateAliasEntry ImportModuleAliasEntry =
    new("Microsoft.PowerShell.Core\\Import-Module", "Import-Module");

  public static readonly SessionStateCmdletEntry GetModuleCommandEntry =
    new("Get-Module", typeof(GetModuleCommand), null);

  public static readonly SessionStateCmdletEntry OutHostCommandEntry =
    new("Out-Host", typeof(OutHostCommand), null);

  public static readonly SessionStateFunctionEntry ClearHostFunctionEntry =
    new("Clear-Host",
      // lang=powershell
      """

      $RawUI = $Host.UI.RawUI
      $RawUI.CursorPosition = @{X=0;Y=0}
      $RawUI.SetBufferContents(
          @{Top = -1; Bottom = -1; Right = -1; Left = -1},
          @{Character = ' '; ForegroundColor = $rawui.ForegroundColor; BackgroundColor = $rawui.BackgroundColor})
      # .Link
      # https://go.microsoft.com/fwlink/?LinkID=2096480
      # .ExternalHelp System.Management.Automation.dll-help.xml


      """);

  public static readonly SessionStateCmdletEntry ConnectImmyAzureADCommandEntry =
    new("Connect-ImmyAzureAD", typeof(ConnectImmyAzureADCommand), null);

  public static readonly SessionStateCmdletEntry GetImmyAuthTokenCommandEntry =
    new("Get-ImmyAuthToken", typeof(GetImmyAuthTokenCommand), null);

  public static readonly SessionStateCmdletEntry GetImmyAzureAuthHeaderCommandEntry =
    new("Get-ImmyAzureAuthHeader", typeof(GetImmyAzureAuthHeaderCommand), null);

  public static readonly SessionStateCmdletEntry GetTypeDataCommandEntry =
    new("Get-Type", typeof(GetTypeDataCommand), null);

  public static readonly SessionStateCmdletEntry TraceCommandCommandEntry =
    new("Trace-Command", typeof(TraceCommandCommand), null);

  public static readonly SessionStateCmdletEntry WhereObjectCommandEntry =
    new("Where-Object", typeof(WhereObjectCommand), null);

  public static readonly SessionStateCmdletEntry SelectObjectCommandEntry =
    new("Select-Object", typeof(SelectObjectCommand), null);

  public static readonly SessionStateCmdletEntry ForEachObjectCommandEntry =
    new("ForEach-Object", typeof(ForEachObjectCommand), null);

  public static readonly SessionStateCmdletEntry FormatListCommandEntry =
    new("Format-List", typeof(FormatListCommand), null);

  public static readonly SessionStateCmdletEntry FormatTableCommandEntry =
    new("Format-Table", typeof(FormatTableCommand), null);

  public static readonly SessionStateCmdletEntry FormatHexCommandEntry =
    new("Format-Hex", typeof(FormatHex), null);

  public static readonly SessionStateCmdletEntry OutNullCommandEntry =
    new("Out-Null", typeof(OutNullCommand), null);

  public static readonly SessionStateCmdletEntry ConvertToJsonCommandEntry =
    new("ConvertTo-Json", typeof(ConvertToJsonCommand), null);

  public static readonly SessionStateCmdletEntry ConvertFromJsonCommandEntry =
    new("ConvertFrom-Json", typeof(ConvertFromJsonCommand), null);

  public static readonly SessionStateCmdletEntry ConvertToBase64CommandEntry =
    new("ConvertTo-Base64", typeof(ConvertToBase64Command), null);

  public static readonly SessionStateCmdletEntry ConvertFromBase64CommandEntry =
    new("ConvertFrom-Base64", typeof(ConvertFromBase64Command), null);

  public static readonly SessionStateCmdletEntry AddUriQueryParameterCommandEntry =
    new("Add-UriQueryParameter", typeof(AddUriQueryParameter), null);

  public static readonly SessionStateCmdletEntry StopImmySessionCommandEntry =
    new("Stop-ImmySession", typeof(StopImmySessionCommand), null);

  public static readonly SessionStateCmdletEntry JoinScriptBlockCommandEntry =
    new("Join-ScriptBlock", typeof(JoinScriptBlockCommand), null);

  public static readonly SessionStateCmdletEntry NewImmyUploadSasUriCommandEntry =
    new("New-ImmyUploadSasUri", typeof(NewImmyUploadSasUriCommand), null);

  public static readonly SessionStateCmdletEntry InvokeCommandCachedCommandEntry =
    new("Invoke-CommandCached", typeof(InvokeCommandCachedCommand), null);

  public static readonly SessionStateCmdletEntry SetCacheKeyExpirationCommandEntry =
    new("Set-CacheKeyExpiration", typeof(SetCacheKeyExpiration), null);

  public static readonly SessionStateCmdletEntry InvokeAtomicCommandCommandEntry =
    new("Invoke-AtomicCommand", typeof(InvokeAtomicCommandCommand), null);

  public static readonly SessionStateCmdletEntry GetHashCommandEntry =
    new("Get-Hash", typeof(GetHashCommand), null);

  public static readonly SessionStateCmdletEntry GetKeyedHashCommandEntry =
    new("Get-KeyedHash", typeof(GetKeyedHashCommand), null);

  public static readonly SessionStateCmdletEntry SplitPathCommandEntry =
    new("Split-Path", typeof(SplitPathCommand), null);

  public static readonly SessionStateCmdletEntry RegisterArgumentCompleterCommandEntry =
    new("Register-ArgumentCompleter", typeof(RegisterArgumentCompleterCommand), null);

  public static readonly SessionStateCmdletEntry ExportPSSessionCommandEntry =
    new("Export-PSSession", typeof(ExportPSSessionCommand), null);

  public static readonly SessionStateCmdletEntry ConvertFromStringDataCommandEntry =
    new("ConvertFrom-StringData", typeof(ConvertFromStringDataCommand), null);

  public static readonly SessionStateCmdletEntry NewParameterCollectionCommandEntry =
    new("New-ParameterCollection", typeof(NewParameterCollectionCommand), null);

  public static readonly SessionStateAliasEntry NewRuntimeDefinedParameterCollectionAliasEntry =
    new("New-RuntimeDefinedParameterCollection", "New-ParameterCollection");

  public static readonly SessionStateCmdletEntry NewParameterCommandEntry =
    new("New-Parameter", typeof(NewParameterCommand), null);

  public static readonly SessionStateAliasEntry NewRuntimeDefinedParameterAliasEntry =
    new("New-RuntimeDefinedParameter", "New-Parameter");

  public static readonly SessionStateCmdletEntry SetParameterCommandEntry =
    new("Set-Parameter", typeof(SetParameterCommand), null);

  public static readonly SessionStateAliasEntry SetRuntimeDefinedParameterAliasEntry =
    new("Set-RuntimeDefinedParameter", "Set-Parameter");

  public static readonly SessionStateCmdletEntry NewTextParameterCommandEntry =
    new("New-TextParameter", typeof(NewTextParameterCommand), null);

  public static readonly SessionStateCmdletEntry NewNumberParameterCommandEntry =
    new("New-NumberParameter", typeof(NewNumberParameterCommand), null);

  public static readonly SessionStateCmdletEntry NewDateTimeParameterCommandEntry =
    new("New-DateTimeParameter", typeof(NewDateTimeParameterCommand), null);

  public static readonly SessionStateCmdletEntry NewCheckboxParameterCommandEntry =
    new("New-CheckboxParameter", typeof(NewCheckboxParameterCommand), null);

  public static readonly SessionStateCmdletEntry NewBooleanParameterCommandEntry =
    new("New-BooleanParameter", typeof(NewBooleanParameterCommand), null);

  public static readonly SessionStateCmdletEntry NewKeyValueParameterCommandEntry =
    new("New-KeyValueParameter", typeof(NewKeyValueParameterCommand), null);

  public static readonly SessionStateCmdletEntry NewUriParameterCommandEntry =
    new("New-UriParameter", typeof(NewUriParameterCommand), null);

  public static readonly SessionStateCmdletEntry NewRadioParameterCommandEntry =
    new("New-RadioParameter", typeof(NewRadioParameterCommand), null);

  public static readonly SessionStateCmdletEntry NewMediaParameterCommandEntry =
    new("New-MediaParameter", typeof(NewMediaParameterCommand), null);

  public static readonly SessionStateCmdletEntry NewPasswordParameterCommandEntry =
    new("New-PasswordParameter", typeof(NewPasswordParameterCommand), null);

  public static readonly SessionStateCmdletEntry NewOauthConsentParameterCommandEntry =
    new("New-OauthConsentParameter", typeof(NewOauthConsentParameterCommand), null);

  public static readonly SessionStateCmdletEntry GetOauthAccessTokenCommandCommandEntry =
    new("Get-OauthAccessToken", typeof(GetOauthAccessTokenCommand), null);

  public static readonly SessionStateCmdletEntry NewDropdownParameterCommandEntry =
    new("New-DropdownParameter", typeof(NewDropdownParameterCommand), null);

  public static readonly SessionStateCmdletEntry NewPersonParameterCommandEntry =
    new("New-PersonParameter", typeof(NewPersonParameterCommand), null);

  public static readonly SessionStateCmdletEntry NewHelpTextCommandEntry =
    new("New-HelpText", typeof(NewHelpTextCommand), null);

  public static readonly SessionStateCmdletEntry GetIntegrationAgentInstallTokenCommandEntry =
    new("Get-IntegrationAgentInstallToken", typeof(GetIntegrationAgentInstallTokenCmdlet), null);

  public static readonly SessionStateCmdletEntry GetIntegrationTenantUninstallTokenCommandEntry =
    new("Get-IntegrationTenantUninstallToken",
      typeof(GetIntegrationTenantUninstallTokenCmdlet),
      null);

  public static readonly SessionStateCmdletEntry GetIntegrationAgentUninstallTokenCommandEntry =
    new("Get-IntegrationAgentUninstallToken",
      typeof(GetIntegrationAgentUninstallTokenCmdlet),
      null);

  public static readonly SessionStateCmdletEntry GetIntegrationDynamicVersionsCommandEntry =
    new("Get-IntegrationDynamicVersions", typeof(GetIntegrationDynamicVersionsCmdlet), null);

  public static readonly SessionStateCmdletEntry GetIntegrationAuthenticatedDownloadCommandEntry =
    new("Get-IntegrationAuthenticatedDownload",
      typeof(GetIntegrationAuthenticatedDownloadCmdlet),
      null);

  public static readonly SessionStateCmdletEntry CompareToImmyBotVersionCommandEntry =
    new("CompareTo-ImmyBotVersion", typeof(CompareToImmyBotVersionCommand), null);

  public static readonly SessionStateCmdletEntry GetProviderInfoCommandEntry =
    new("Get-ProviderInfo", typeof(GetProviderInfoCommand), null);

  public static readonly SessionStateAliasEntry GetRmmInfoAliasEntry =
    new("Get-RmmInfo", "Get-ProviderInfo");

  public static readonly SessionStateCmdletEntry GetImmyBotAgentFileVersionCommandEntry =
    new("Get-ImmyBotAgentFileVersion", typeof(GetImmyBotAgentFileVersionCmdlet), null);

  public static readonly SessionStateCmdletEntry GetAgentInstallScriptCommandEntry =
    new("Get-AgentInstallScript", typeof(GetAgentInstallScriptMetaScript), null);

  public static readonly SessionStateAliasEntry GetRmmInstallScriptAliasEntry =
    new("Get-RmmInstallScript", "Get-AgentInstallScript");

  public static readonly SessionStateCmdletEntry SendImmyEmailCommandEntry =
    new("Send-ImmyEmail", typeof(SendImmyEmailCommand), null);

  public static readonly SessionStateCmdletEntry GetProviderAgentMetaScriptCommandEntry =
    new("Get-ProviderAgent", typeof(GetProviderAgentMetaScriptCommand), null);

  public static readonly SessionStateAliasEntry GetRmmComputerAliasEntry =
    new("Get-RmmComputer", "Get-ProviderAgent");

  public static readonly SessionStateCmdletEntry RefreshComputerSystemInfoCommandEntry =
    new("Refresh-ComputerSystemInfo", typeof(RefreshComputerSystemInfoCommand), null);

  public static readonly SessionStateCmdletEntry WaitImmyComputerCommandEntry =
    new("Wait-ImmyComputer", typeof(WaitImmyComputerCommand), null);

  public static readonly SessionStateCmdletEntry SetImmyDeviceIdCommandEntry =
    new("Set-ImmyDeviceId", typeof(SetImmyDeviceIdCommand), null);


  public const string AddImmyMaintenanceActionChildCmdletName = "Add-ImmyMaintenanceActionChild";

  public static readonly SessionStateCmdletEntry SetImmyMaintenanceActionProgressCommandEntry =
    new("Set-ImmyMaintenanceActionProgress", typeof(SetImmyMaintenanceActionProgressCommand), null);

  public static readonly SessionStateCmdletEntry AddImmyMaintenanceActionDependencyCommandEntry =
    new("Add-ImmyMaintenanceActionDependency",
      typeof(AddImmyMaintenanceActionDependencyCommand),
      null);

  public static readonly SessionStateCmdletEntry AddImmyMaintenanceActionDependentCommandEntry =
    new("Add-ImmyMaintenanceActionDependent",
      typeof(AddImmyMaintenanceActionDependentCommand),
      null);

  public static readonly SessionStateCmdletEntry AddImmyMaintenanceActionChildCommandEntry =
    new(AddImmyMaintenanceActionChildCmdletName, typeof(AddImmyMaintenanceActionChild), null);

  public static readonly SessionStateCmdletEntry GetImmyMaintenanceActionChildrenCommandEntry =
    new("Get-ImmyMaintenanceActionChildren", typeof(GetImmyMaintenanceActionChildrenCommand), null);

  public static readonly SessionStateCmdletEntry InvokeImmyCommandCommandEntry =
    new("Invoke-ImmyCommand", typeof(InvokeImmyCommandCommand), null);

  public static readonly SessionStateCmdletEntry GetImmyComputerMetascriptCommandEntry =
    new("Get-ImmyComputer", typeof(GetImmyComputerMetaScript), null);

  public static readonly SessionStateCmdletEntry MergeImmyComputersMetascriptCommandEntry =
    new("Merge-ImmyComputers", typeof(MergeImmyComputersMetaScript), null);

  public static readonly SessionStateCmdletEntry GetPersonCloudScriptCommandEntry =
    new("Get-Person", typeof(GetPersonCloudScript), null);

  public static readonly SessionStateCmdletEntry NewPersonCloudScriptCommandEntry =
    new("New-Person", typeof(NewPersonCloudScript), null);

  public static readonly SessionStateCmdletEntry SetImmyPrimaryUserMetascriptCommandEntry =
    new("Set-ImmyPrimaryUser", typeof(SetImmyPrimaryUserMetaScript), null);

  public static readonly SessionStateCmdletEntry NewDynamicIntegrationCommandEntry =
    new("New-DynamicIntegration", typeof(NewDynamicIntegrationCommand), null);

  public static readonly SessionStateCmdletEntry AddDynamicIntegrationCapabilityCommandEntry =
    new("Add-DynamicIntegrationCapability", typeof(AddDynamicIntegrationCapabilityCommand), null);

  public static readonly SessionStateCmdletEntry GetDynamicIntegrationCapabilityCommandEntry =
    new("Get-DynamicIntegrationCapability", typeof(GetDynamicIntegrationCapabilityCommand), null);

  public static readonly SessionStateCmdletEntry NewClientGroupCommandEntry =
    new("New-ClientGroup", typeof(NewClientGroupCommand), null);

  public static readonly SessionStateCmdletEntry NewVerifyIntegrationResultCommandEntry =
    new("New-VerifyIntegrationResult", typeof(NewVerifyIntegrationResultCommand), null);

  public static readonly SessionStateCmdletEntry NewHealthyResultCommandEntry =
    new("New-HealthyResult", typeof(NewHealthyResultCommand), null);

  public static readonly SessionStateCmdletEntry NewUnhealthyResultCommandEntry =
    new("New-UnhealthyResult", typeof(NewUnhealthyResultCommand), null);

  public static readonly SessionStateCmdletEntry NewIntegrationClientCommandEntry =
    new("New-IntegrationClient", typeof(NewIntegrationClientCommand), null);

  public static readonly SessionStateCmdletEntry NewIntegrationAgentCommandEntry =
    new("New-IntegrationAgent", typeof(NewIntegrationAgentCommand), null);

  public static readonly SessionStateCmdletEntry GetImmyComputerFilterScriptCommandEntry =
    new("Get-ImmyComputer", typeof(GetImmyComputerFilterScriptCommand), null);

  public static readonly SessionStateCmdletEntry NewSupportFormBrandingEntry =
    new("New-SupportFormBranding", typeof(NewSupportFormBranding), null);

  #region Integration & Module Cmdlets

  public static readonly SessionStateCmdletEntry GetScriptAnalyzerRuleCommandEntry =
    new("Get-ScriptAnalyzerRule", typeof(GetScriptAnalyzerRuleCommand), null);

  public static readonly SessionStateCmdletEntry InvokeFormatterCommandEntry =
    new("Invoke-Formatter", typeof(InvokeFormatterCommand), null);

  public static readonly SessionStateCmdletEntry InvokeScriptAnalyzerCommandEntry =
    new("Invoke-ScriptAnalyzer", typeof(InvokeScriptAnalyzerCommand), null);

  public static readonly SessionStateCmdletEntry GetUnboundParametersCommandEntry =
    new("Get-UnboundParameters", typeof(GetUnboundParametersCommand), null);

  public static readonly SessionStateCmdletEntry GetCwaRestPagesCommandEntry =
    new("Get-CWARestPages", typeof(GetCwaRestPagesCommand), null);

  public static readonly SessionStateCmdletEntry InvokeCwaRestMethodCommandEntry =
    new("Invoke-CWARestMethod", typeof(InvokeCwaRestMethodCommand), null);

  public static readonly SessionStateCmdletEntry GetCwControlFieldsCmdletEntry =
    new("Get-CWControlFields", typeof(GetCwControlFields), null);

  public static readonly SessionStateCmdletEntry GetNCentralActivationKeyCmdletEntry =
    new("Get-NCentralActivationKey", typeof(GetActivationKey), null);

  public static readonly SessionStateCmdletEntry GetNCentralRegistrationTokenCmdletEntry =
    new("Get-NCentralRegistrationToken", typeof(GetCustomerRegistrationToken), null);

  public static readonly SessionStateCmdletEntry ConvertToPSCompatibilityJsonCommandEntry =
    new("ConvertTo-PSCompatibilityJson", typeof(ConvertToPSCompatibilityJsonCommand), null);

  public static readonly SessionStateCmdletEntry ConvertFromPSCompatibilityJsonCommandEntry =
    new("ConvertFrom-PSCompatibilityJson", typeof(ConvertFromPSCompatibilityJsonCommand), null);

  public static readonly SessionStateCmdletEntry NewPSCompatibilityProfileCommandEntry =
    new("New-PSCompatibilityProfile", typeof(NewPSCompatibilityProfileCommand), null);

  public static readonly SessionStateCmdletEntry StartEditorServicesCommandEntry =
    new("Start-EditorServices", typeof(StartEditorServicesCommand), null);

  #endregion

  #region Private Variables

  public static readonly ImmyVariableEntry<IServiceScopeFactory> ServiceScopeFactoryVariableEntry =
    new("ServiceScopeFactory",
      "Service Scope Factory",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<bool> CanAccessMspResourcesVariableEntry =
    new("CanAccessMspResources",
      "Can Access Msp Resources",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<bool> CanAccessParentTenantVariableEntry =
    new("CanAccessParentTenant",
      "Can Access Parent Tenant",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<bool> IncludeLocalScriptsVariableEntry =
    new("IncludeLocalScripts",
      "Whether or not to include local function and modules. Global Scripts should not reference local scripts.",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<Guid> ScriptSelfCancellationIdVariableEntry =
    new("ScriptSelfCancellationId",
      "Script Cancellation Identifier",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<CancellationToken> CancellationTokenVariableEntry =
    new("CancellationToken",
      "Cancellation Token",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<int> FilterScriptComputerIdVariableEntry =
    new("FilterScriptComputerId",
      "Filter Script's Computer ID",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<int> FilterScriptTenantIdVariableEntry =
    new("FilterScriptTenantId",
      "Filter Script's Tenant ID",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<bool> FilterScriptIncludeChildTenantsVariableEntry =
    new("FilterScriptIncludeChildTenants",
      "Include Child Tenants",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<ScriptCategory> ScriptCategoryVariableEntry =
    new(nameof(Script.ScriptCategory),
      "The category of the script",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<int> ProviderLinkIdForMaintenanceItemVariableEntry =
    new(nameof(Script.ProviderLinkIdForMaintenanceItem),
      "The provider link id of the maintenance item linked to this script",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<bool> SkipPreflightVariableEntry =
    new(nameof(Script.SkipPreflight),
      "Whether this script can skip preflight checks",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<bool> SkipBusinessHoursCheckVariableEntry =
    new(nameof(Script.SkipBusinessHoursCheck),
      "Whether this script can skip business hours checks",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<DatabaseType> DatabaseTypeVariableEntry =
    new("DatabaseType",
      "Global or Local script",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<Guid> ScriptOutputCorrelationIdVariableEntry =
    new("ScriptOutputCorrelationId",
      "Script-output Correlation Identifier",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<int> ManuallyTriggeredByUserIdVariableEntry =
    new("ManuallyTriggeredByUserId",
      "Manually Triggered By User Id",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<IRunContext> RunContextVariableEntry =
    new("RunContext",
      "ImmyBot RunContext",
      SessionStateEntryVisibility.Private);

  public static readonly ImmyVariableEntry<int> LimitToScriptsForTenantVariableEntry =
    new("LimitToScriptsForTenant",
      "If local scripts are allowed, should they be limited to a specific tenant id.",
      SessionStateEntryVisibility.Private);

  #endregion

  #region Public Variables

  public static readonly ImmyVariableEntry<PSModuleAutoLoadingPreference> PsModuleAutoLoadingPreferenceVariableEntry =
    new(nameof(PSModuleAutoLoadingPreference),
      "Defines the preference options for the Module Auto-loading feature",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<string> ImmyScriptPathVariableEntry =
    new("ImmyScriptPath",
      "Path relative to %PROGRAMDATA%\\ImmyBot\\Scripts wherein Immy scripts and logs are placed",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<NuGet.Versioning.SemanticVersion> ImmyBotVersionVariableEntry =
    new("ImmyBotVersion",
      "ImmyBot Version",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<Guid> TerminalIdVariableEntry =
    new("TerminalId",
      "TerminalId",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<string> ScriptNameVariableEntry =
    new("ScriptName",
      "Script name injected by ImmyBot",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<IDictionary<string, object?>> ScriptVariablesVariableEntry =
    new("ScriptVariables",
      "Script variables injected by ImmyBot",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<Hashtable> IntegrationContextVariableEntry =
    new("IntegrationContext",
      "Integration Context",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<DynamicIntegrationTypeProperties> DynamicIntegrationTypePropertiesVariableEntry =
    new("DynamicIntegrationTypeProperties",
      "Dynamic Integration Type Properties",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<PSObject> ComputerVariableEntry =
    new("Computer",
      "Computer",
      SessionStateEntryVisibility.Public,
      ScopedItemOptions.None); // `$Computer = Get-ImmyComputer` is common

  public static readonly ImmyVariableEntry<Guid> SessionGroupIDVariableEntry =
    new("SessionGroupID",
      "Used to correlate groups of sessions that were all spawned by the same Schedule",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<int> ActionIdVariableEntry =
    new("ActionId",
      "Id of the maintenance action",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<string> ActionNameVariableEntry =
    new("ActionName",
      "Name of the maintenance action",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<MaintenanceActionStatus> ActionStatusVariableEntry =
    new("ActionStatus",
      "Status of the maintenance action",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<MaintenanceActionReason> ActionReasonVariableEntry =
    new("ActionReason",
      "Reason for the status of the maintenance action",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<MaintenanceActionResult> ActionResultVariableEntry =
    new("ActionResult",
      "Result of the maintenance action",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<MaintenanceActionResultReason> ActionResultReasonVariableEntry =
    new("ActionResultReason",
      "Reason for the result of the maintenance action",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<object> ActionParametersVariableEntry =
    new("ActionParameters",
      $"The parameters supplied by the call to {AddImmyMaintenanceActionChildCmdletName}",
      SessionStateEntryVisibility.Public,
      ScopedItemOptions.ReadOnly);

  public static readonly ImmyVariableEntry<SemanticVersion> ActionDetectedVersionVariableEntry =
    new("ActionDetectedVersion",
      "The detected version",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<SemanticVersion> ActionDesiredVersionVariableEntry =
    new("ActionDesiredVersion",
      "The desired version",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<DesiredSoftwareState> ActionDesiredSoftwareStateVariableEntry =
    new("ActionDesiredSoftwareState",
      "The desired software state",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<int> SessionIdVariableEntry =
    new("SessionId",
      "Id of the maintenance session",
      SessionStateEntryVisibility.Public);

  public static readonly ImmyVariableEntry<bool> SessionIsRepairingVariableEntry =
    new("SessionIsRepairing",
      "Is this a repair session",
      SessionStateEntryVisibility.Public);

  #endregion
}
