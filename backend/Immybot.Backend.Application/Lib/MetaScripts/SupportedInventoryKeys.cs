using System;
using System.Linq;
using System.Management.Automation;
using System.Threading;
using Immybot.Backend.Application.Interface.Actions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts;

public class SupportedInventoryKeys : IValidateSetValuesGenerator
{
  public string[] GetValidValues()
  {
    using PowerShell ps = PowerShell.Create(RunspaceMode.CurrentRunspace);
    var serviceScopeFactory =
      ps.Runspace.InitialSessionState.Variables["ServiceScopeFactory"][0].Value as IServiceScopeFactory;
    if (serviceScopeFactory is null) throw new InvalidOperationException("ServiceScopeFactory is null");
    using var serviceScope = serviceScopeFactory.CreateScope();
    var inventoryTaskActions = serviceScope.ServiceProvider.GetRequiredService<IInventoryTaskActions>();
    var inventoryTasks = new JoinableTaskContext().Factory.Run(async () =>
      await inventoryTaskActions.GetAllInventoryTasks(CancellationToken.None));
    var inventoryScripts = inventoryTasks.SelectMany(a => a.<PERSON>);
    var inventoryKeys = inventoryScripts.Select(a => a.InventoryKey);
    return inventoryKeys.ToArray();
  }
}
