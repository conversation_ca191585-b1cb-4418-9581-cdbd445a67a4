using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Management.Automation;
using System.Management.Automation.Language;
using System.Text;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using OmniSharp.Extensions.LanguageServer.Protocol;
using OmniSharp.Extensions.LanguageServer.Protocol.Models;
using StreamJsonRpc;

namespace Immybot.Backend.Application.Lib.MetaScripts;

public class LanguageServerInterceptor : ILanguageServer
{
  public TimeSpan TimeSinceLastActivity => DateTime.UtcNow - LastActivity;
  public DateTime LastActivity { get; private set; } = DateTime.UtcNow;

  private readonly ILanguageServer _languageServer;
  private readonly JsonRpc _browserJsonRpc;
  private readonly Func<ImmybotDbContext> _ctxFactory;
  private readonly Func<SoftwareDbContext> _globalCtxFactory;
  private readonly string _workspacePath;

  /// <summary>
  /// This routine builds a fragment of the config file
  /// for a particular key. It returns a formatted string that includes
  /// a comment describing the key as well as the key and its value.
  /// </summary>
  internal static string ConfigFragment(string key, object value, string indent = "\t")
  {
    var sb = new StringBuilder($"{indent}# {key}{Environment.NewLine}{indent}{key} = ");
    if (value is IEnumerable<string> enumerable)
    {
      sb.AppendLine("@(");
      sb.Append($"{indent}\t'");
      sb.AppendJoin($"',{Environment.NewLine}{indent}\t'", enumerable);
      sb.Append($"'{Environment.NewLine}{indent})");
    }
    else
    {
      sb.Append($"{value}{Environment.NewLine}");
    }
    return sb.ToString();
  }
  public bool IsInitialized { get; private set; }
  public LanguageServerInterceptor(JsonRpc languageServer, JsonRpc browser, Func<ImmybotDbContext> ctxFactory,
    Func<SoftwareDbContext> globalCtxFactory, string pipeId)
  {
    _languageServer = languageServer.Attach<ILanguageServer>(new()
    {
      // If this is not set to true, "params": will be an array and PSES will throw:
      // Newtonsoft.Json.JsonSerializationException: Cannot deserialize the current JSON array (e.g. [1,2,3]) into type 'OmniSharp.Extensions.LanguageServer.Protocol.Models.DefinitionParams' because the type requires a JSON object (e.g. {"name":"value"}) to deserialize correctly.
      ServerRequiresNamedArguments = true,
    });
    _browserJsonRpc = browser;
    _ctxFactory = ctxFactory;
    _globalCtxFactory = globalCtxFactory;

    /*
      PSScriptAnalyzer.psd1 must be in the same directory as the executing assembly
      PSScriptAnalyzerRules.psd1 must be in the root of the PSES Workspace Path
    */
    _workspacePath = Path.Combine(Path.GetTempPath(), $"workspace_{pipeId}");
    Directory.CreateDirectory(_workspacePath);
  }

  public async Task LogBrowserTerminal(MessageType type, string message)
  {
    await _browserJsonRpc.NotifyWithParameterObjectAsync(WindowNames.LogMessage, new LogMessageParams()
    {
      Message = message,
      Type = type,
    });
  }

  public async Task ShowBrowserMessage(MessageType type, string message)
  {
    await _browserJsonRpc.NotifyWithParameterObjectAsync(WindowNames.ShowMessage, new ShowMessageParams()
    {
      Message = message,
      Type = type,
    });
  }

  public async Task<string?> ShowBrowserMessageRequest(MessageType type, string message, string? primaryOption, string? secondaryOption)
  {
    var actionOptions = new string?[] { primaryOption, secondaryOption };
    var result = await _browserJsonRpc.InvokeWithParameterObjectAsync<MessageActionItem?>(WindowNames.ShowMessageRequest, new ShowMessageRequestParams()
    {
      Message = message,
      Type = type,
      Actions = new Container<MessageActionItem>(actionOptions.Where(x => x is not null).Select(x => new MessageActionItem() { Title = x!}))
    });

    return result?.Title;
  }

  [JsonRpcMethod(GeneralNames.Initialize)]
  public async Task<InitializeResult> Initialize(InitializeParams parameters)
  {
    IsInitialized = true;
    return await _languageServer.Initialize(parameters);
  }

  // Intercept shutdown and set IsInitialized to false
  [JsonRpcMethod(GeneralNames.Shutdown)]
  public async Task Shutdown()
  {
    await _languageServer.Shutdown();
    // Graceful shutdown. Prevents us from resending shutdown and exit and causing an exception
    IsInitialized = false;
    if (Directory.Exists(_workspacePath))
    {
      Directory.Delete(_workspacePath, true);
    }
  }

  [JsonRpcMethod(TextDocumentNames.DidChange)]
  public async Task DidChange(Container<TextDocumentContentChangeEvent> contentChanges, TextDocumentIdentifier textDocument)
  {
    LastActivity = DateTime.UtcNow;
    await _languageServer.DidChange(contentChanges, textDocument);
  }

  [JsonRpcMethod(TextDocumentNames.Hover)]
  public async Task<Hover> Hover(Position position, TextDocumentIdentifier textDocument)
  {
    LastActivity = DateTime.UtcNow;
    return await _languageServer.Hover(position, textDocument);
  }

  [JsonRpcMethod(TextDocumentNames.Definition)]
  public async Task<LocationOrLocationLinks> GetDefinition(Position position, TextDocumentIdentifier textDocument)
  {
    LastActivity = DateTime.UtcNow;
    // In development PSES has access to the FileSystem and will scan modules for the function
    // This causes timeouts and makes it look like this logic doesn't work.
    // To prevent this, we use Task.WhenAny to simultaneously lookup the definition from our database and also from PSES
    return await new[]
    {
        _languageServer.GetDefinition(position, textDocument),
        GetDefinitionFromDatabase(position, textDocument)
    }.GetFirstResultOrDefault(rm => rm.Any()) ?? new();
  }

  internal async Task<LocationOrLocationLinks> GetDefinitionFromDatabase(Position position, TextDocumentIdentifier textDocument)
  {
    var script = await _browserJsonRpc.InvokeAsync<GetContentResponse>("textDocument/getContent");

    if (string.IsNullOrEmpty(script.Script)) return new();

    // parse script action
    var scriptBlock = ScriptBlock.Create(script.Script);
    position.Line++;
    var found = scriptBlock.Ast.FindAll((ast) =>
    {
      if (ast is not CommandAst) return false;
      return ast.Extent.StartLineNumber <= position.Line && position.Line <= ast.Extent.EndLineNumber && ast.Extent.StartColumnNumber <= position.Character;
    }, true).LastOrDefault();

    if (found is null) return new();

    var functionName = (found as CommandAst)?.GetCommandName().ToLower();
    if (functionName is null) return new();

    FunctionScriptDto? functionScript = null;

    // prefer local
    if (script.DatabaseType is DatabaseType.Local)
    {
      await using var ctx = _ctxFactory();
      functionScript = await ctx.Scripts
      .AsNoTracking()
      .Where(a => a.Name.ToLower() == functionName)
      .Select(a => new FunctionScriptDto(a.Id, a.ScriptType, a.Name, a.Action))
      .FirstOrDefaultAsync();
    }

    // then global
    if (functionScript is null)
    {
      await using var globalCtx = _globalCtxFactory();
      functionScript = await globalCtx.Scripts
        .AsNoTracking()
        .Where(a => a.Name.ToLower() == functionName)
        .Select(a => new FunctionScriptDto(a.Id, a.ScriptType, a.Name, a.Action))
        .FirstOrDefaultAsync();
    }

    if (functionScript is null) return new();

    var location = new Location
    {
      Uri = new Uri($"inmemory://{functionScript.ScriptType.ToString().ToLower()}/{functionScript.Id}.ps1"),
      Range = new OmniSharp.Extensions.LanguageServer.Protocol.Models.Range
      {
        Start = new Position { Line = 0, Character = 0 },
        End = new Position { Line = 0, Character = 0 },
      }
    };
    return new LocationOrLocationLinks(location);
  }

}
