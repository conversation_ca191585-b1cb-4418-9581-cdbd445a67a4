using System;
using System.Management.Automation;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Manager;
using Immybot.Backend.Manager.Domain;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Application.Lib.MetaScripts;

[Cmdlet(VerbsCommon.Get, "ImmyBotAgentFileVersion")]
internal class GetImmyBotAgentFileVersionCmdlet : ServiceScopePSCmdlet
{
  protected override void EndProcessing()
  {
    var appSettingsOptions = TryGetService<IOptions<AppSettingsOptions>>();
    if (appSettingsOptions.Value.UseLocalDevelopmentAgentInstaller &&
        AgentVersionHelper.TryGetDevAgentVersion(out var devAgentVersion))
    {
      WriteObject(devAgentVersion.ToString());
    }
    else
    {
      var managerProvidedSettings = TryGetService<IManagerProvidedSettings>();
      if (managerProvidedSettings.CurrentRelease is null)
        throw new InvalidOperationException("Could not determine current ImmyBot version.");
      WriteObject(managerProvidedSettings.CurrentRelease.ImmyAgentInstallerFileVersion);
    }
  }
}
