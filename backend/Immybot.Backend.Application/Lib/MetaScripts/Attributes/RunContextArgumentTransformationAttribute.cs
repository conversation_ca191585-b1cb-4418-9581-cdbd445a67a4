using System.Management.Automation;
using Immybot.Backend.Application.Maintenance;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.Application.Lib.MetaScripts.Attributes;

public abstract class RunContextArgumentTransformationAttribute : ServiceScopeArgumentTransformationAttribute
{
  protected override object? Transform(IServiceScope serviceScope, EngineIntrinsics engineIntrinsics, object? inputData)
  {
    if (engineIntrinsics.SessionState.PSVariable.GetValue("RunContext") is not IRunContext runContext)
    {
      throw new PSInvalidOperationException("RunContext is not available");
    }
    return Transform(runContext, serviceScope, engineIntrinsics, inputData);
  }

  protected abstract object? Transform(IRunContext runContext, IServiceScope serviceScope, EngineIntrinsics engineIntrinsics, object? inputData);
}
