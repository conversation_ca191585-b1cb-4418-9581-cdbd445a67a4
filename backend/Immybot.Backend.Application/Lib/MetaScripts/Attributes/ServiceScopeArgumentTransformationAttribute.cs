using System.Management.Automation;
using System.Text.Json;
using System.Threading;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.Application.Lib.MetaScripts.Attributes;

public abstract class ServiceScopeArgumentTransformationAttribute : ArgumentTransformationAttribute
{

  /// <summary>
  /// Case-insensitive serialization options
  /// </summary>
  internal static readonly JsonSerializerOptions CaseInsensitiveSerializerOptions = new() { PropertyNameCaseInsensitive = true };
  internal CancellationToken CancellationToken;
  internal int? ManuallyTriggeredByUserId;
  internal int? InternalTenantId;
  internal bool CanAccessMspResources;
  internal bool CanAccessParentTenant;

  public override object? Transform(EngineIntrinsics engineIntrinsics, object inputData)
  {
    using var scope = ((IServiceScopeFactory)engineIntrinsics.SessionState.PSVariable.GetValue("ServiceScopeFactory")).CreateScope();
    CancellationToken = (CancellationToken)engineIntrinsics.SessionState.PSVariable.GetValue("CancellationToken");
    ManuallyTriggeredByUserId = (int?)engineIntrinsics.SessionState.PSVariable.GetValue("ManuallyTriggeredByUserId");
    CanAccessMspResources = (bool?)engineIntrinsics.SessionState.PSVariable.GetValue("CanAccessMspResources") ?? false;
    CanAccessParentTenant = (bool?)engineIntrinsics.SessionState.PSVariable.GetValue("CanAccessParentTenant") ?? false;

    var ret = Transform(scope, engineIntrinsics, inputData);
    return ret;

  }

  protected abstract object? Transform(IServiceScope serviceScope, EngineIntrinsics engineIntrinsics, object? inputData);
}
