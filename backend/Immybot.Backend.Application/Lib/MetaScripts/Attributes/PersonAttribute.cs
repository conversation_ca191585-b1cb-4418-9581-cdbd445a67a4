using System;
using System.Management.Automation;
using System.Text.Json;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts.Attributes;

[AttributeUsage(AttributeTargets.Parameter)]
public class PersonAttribute : RunContextArgumentTransformationAttribute
{
  internal static readonly JsonSerializerOptions NumberSerializerOptions = new() { PropertyNameCaseInsensitive = true, NumberHandling = System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString };
  protected override object? Transform(IRunContext runContext, IServiceScope serviceScope, EngineIntrinsics engineIntrinsics, object? inputData)
  {
    // nothing to transform if the input data is already a PersonDropdownValue
    if (inputData is PersonDropdownValue) return inputData;

    int? personId = null;
    if (inputData is int data)
    {
      personId = data;
    }
    if (inputData is long)
    {
      personId = Convert.ToInt32(inputData);
    }
    else
    {
      JsonElement? jsonElement = null;

      if (inputData is JsonElement element)
      {
        jsonElement = element;
      }
      else if (inputData is ParameterValue parameterValue && parameterValue.Value is JsonElement)
        jsonElement = (JsonElement)parameterValue.Value;

      if (jsonElement is not null)
      {
        switch (jsonElement.Value.ValueKind)
        {
          case JsonValueKind.Number:
            personId = jsonElement.Value.Deserialize<int?>(NumberSerializerOptions);
            break;
          case JsonValueKind.Object:
            var person = jsonElement.Value.Deserialize<PersonDropdownValue>();
            if (person is not null) personId = person.Id;
            break;
        }
      }
    }

    if (personId is not null)
    {
      return new JoinableTaskContext().Factory.Run(async () =>
      {
        var person = await runContext.GetPersonById(personId.Value);
        if (person is not null)
        {
          if (!runContext.CanAccessMspResources() && person.TenantId != runContext.TenantId) return null;
          return new PersonDropdownValue(
          person.Id,
          person.TenantId,
          person.FirstName + " " + person.LastName,
          person.AzurePrincipalId,
          person.EmailAddress,
          person.OnPremisesSecurityIdentifier);
        }
        return null;
      }
      );
    }
    return null;
  }
}
