using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Management.Automation;
using System.Threading;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Primitives.Helpers;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.Application.Lib.MetaScripts.Attributes;

/// <summary>
/// Custom PowerShell attribute used to render dynamic dropdowns
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
public class DropdownAttribute : RunContextArgumentTransformationAttribute
{
  public string? LabelPropertyName { get; set; }
  public string? IdPropertyName { get; set; }
  public ScriptBlock? ScriptBlock { get; set; }

  internal List<object>? ValidValues { get; set; }
  public bool Ordered { get; set; }

  public bool ShowAsRadioButtons { get; set; }

  internal bool ValidValuesHasNestedIdProperty { get; private set; }

  public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(20);

  public bool MultiSelect { get; set; } = false;

  public IEnumerable<ParameterDropdownTextValue> GetValidDropdownValues(IRunContext? runContext, Dictionary<string, object?>? specifiedParameters, DatabaseType databaseType, CancellationToken cancellationToken)
  {
    List<object> outputCollection;
    _originalValuesForDropdown = new(StringComparer.OrdinalIgnoreCase);
    if (ValidValues is not null)
    {
      outputCollection = ValidValues.ToList();
    }
    else if (ScriptBlock is not null)
    {
      var dropdownScript = new Script
      {
        Name = "Dropdown Script Block",
        Action = ScriptBlock.ToString(),
        ScriptExecutionContext = ScriptExecutionContext.CloudScript,
        Variables = specifiedParameters ?? [],
        ScriptType = databaseType,
        Timeout = Convert.ToInt32(Timeout.TotalSeconds),
        OutputType = ScriptOutputType.Table
      };

      if (runContext is null)
        throw new NotSupportedException("RunContext is required to run dropdown scripts.");
      var dropdownScriptResult = TaskHelper.RunAsJoinableTask(async () => await runContext.RunScript(dropdownScript, dropdownScript.Timeout.Value, cancellationToken));

      if (dropdownScriptResult.HadTerminatingException)
      {
        throw new ValidationException(dropdownScriptResult.GetErrorString());
      }

      var scriptOutputCollection = dropdownScriptResult.OutputAsCollection;
      if (scriptOutputCollection?.Count is 1 && scriptOutputCollection.First() is Hashtable ht1)
      {
        outputCollection = BuildValidValuesFromHashtable(ht1);
        LabelPropertyName = "Key";
        IdPropertyName = "Value";
      }
      else
      {
        outputCollection = scriptOutputCollection?.OfType<object>().ToList() ?? [];
      }
    }
    else
    {
      throw new ValidationException("Dropdown attribute must have either a valid values list or a script block to run");
    }

    // populate parameter info
    var dropdownValueLabelDict = new OrderedDictionary();

    foreach (var outputData in outputCollection)
    {
      var outputDataPsObject = new PSObject(outputData);

      // get the text to show in the dropdown
      string? dropdownLabel;
      if (!string.IsNullOrEmpty(LabelPropertyName))
      {
        if (outputData is Hashtable ht)
        {
          var caseInsensitiveHashTable = new Hashtable(ht, StringComparer.OrdinalIgnoreCase);
          var labelProperty = caseInsensitiveHashTable[LabelPropertyName] ?? throw new ValidationException($"Specified label property: {IdPropertyName} does not exist on object {outputData}");
          dropdownLabel = labelProperty.ToString();
        }
        else
        {
          var labelPropertyNameLower = LabelPropertyName.ToLower();
          var labelProperty = outputDataPsObject.Members.FirstOrDefault(a => a.Name.ToLower() == labelPropertyNameLower) ?? throw new ValidationException($"Specified label property: {LabelPropertyName} does not exist on object {outputData}");
          dropdownLabel = labelProperty.Value.ToString();
        }

        if (string.IsNullOrEmpty(dropdownLabel)) throw new ValidationException($"Specified label property: {LabelPropertyName} does not have a value on object {outputData}");
      }
      else
      {
        dropdownLabel = outputData.ToString();
      }

      if (string.IsNullOrEmpty(dropdownLabel)) throw new ValidationException($"A label property could not be determined for object {outputData}");

      // get the value to use when a dropdown option is selected
      string? dropdownValue;
      if (!string.IsNullOrEmpty(IdPropertyName))
      {
        var idPropertyNameLower = IdPropertyName.ToLower();
        // if we have a nested id property, then we dig into data -> Value -> IdPropertyName
        if (ValidValuesHasNestedIdProperty)
        {
          var valueProperty = outputDataPsObject.Members.First(a => a.Name == "Value");

          // if the value is a hashtable then get the value at the index of IdPropertyName
          if (valueProperty.Value is Hashtable ht)
          {
            var caseInsensitiveHashTable = new Hashtable(ht, StringComparer.OrdinalIgnoreCase);
            var idProperty = caseInsensitiveHashTable[IdPropertyName] ?? throw new ValidationException($"Specified id property: {IdPropertyName} does not exist on object {outputData}");
            dropdownValue = idProperty.ToString();
          }
          // otherwise get the value of the IdPropertyName member
          else
          {
            var idProperty = new PSObject(valueProperty.Value).Members.FirstOrDefault(a => a.Name.ToLower() == idPropertyNameLower) ?? throw new ValidationException($"Specified id property: {IdPropertyName} does not exist on object {outputData}");
            dropdownValue = idProperty.Value.ToString();
          }
        }
        // if we don't have a nested id then get the value of the IdPropertyName member
        else
        {
          if (outputData is Hashtable ht2)
          {
            var caseInsensitiveHashTable = new Hashtable(ht2, StringComparer.OrdinalIgnoreCase);
            var idProperty = caseInsensitiveHashTable[IdPropertyName] ?? throw new ValidationException($"Specified id property: {IdPropertyName} does not exist on object {outputData}");
            dropdownValue = idProperty.ToString();
          }
          else
          {
            var idProperty = outputDataPsObject.Members.FirstOrDefault(a => a.Name.ToLower() == idPropertyNameLower) ?? throw new ValidationException($"Specified id property: {IdPropertyName} does not exist on object {outputData}");
            dropdownValue = idProperty.Value.ToString();

          }
        }
      }
      else
      {
        // if IdPropertyName wasn't specified and the value is our valid values hashtable object, then grab its value
        if (outputData is HashtableKeyValuePair hashTableKeyValuePair)
        {
          dropdownValue = hashTableKeyValuePair.Value?.ToString();
        }
        // otherwise to string the entire value and use that
        else
        {
          dropdownValue = outputData.ToString();
        }
      }

      if (string.IsNullOrEmpty(dropdownValue)) throw new ValidationException($"A value could not be determined for object {outputData}");

      dropdownValueLabelDict[dropdownValue] = dropdownLabel;
      var valueToAdd = outputData is HashtableKeyValuePair { Value: { } keyValuePairValue } ? keyValuePairValue : outputData;
      if (!_originalValuesForDropdown.Contains(dropdownValue))
        _originalValuesForDropdown.Add(dropdownValue, valueToAdd);
      else
        _originalValuesForDropdown[dropdownValue] = valueToAdd;
    }
    var validDropdownValues = new List<ParameterDropdownTextValue>();
    foreach (DictionaryEntry entry in dropdownValueLabelDict)
      validDropdownValues.Add(new ParameterDropdownTextValue(entry.Value?.ToString() ?? string.Empty, entry.Key.ToString() ?? string.Empty));


    // order it if it is not already ordered
    if (!Ordered)
    {
      validDropdownValues = validDropdownValues.OrderBy(a => string.IsNullOrWhiteSpace(a.Text))
      .ThenBy(a => a.Text).ToList();
    }
    return validDropdownValues;

  }



  public DropdownAttribute(ScriptBlock scriptBlock)
  {
    ScriptBlock = scriptBlock;
  }

  public DropdownAttribute(ScriptBlock scriptBlock, string? idPropertyName, string? labelPropertyName)
  {
    ScriptBlock = scriptBlock;
    LabelPropertyName = labelPropertyName;
    IdPropertyName = idPropertyName;
  }

  public DropdownAttribute(ScriptBlock scriptBlock, string? idPropertyName, string? labelPropertyName, TimeSpan? timeout)
  {
    ScriptBlock = scriptBlock;
    LabelPropertyName = labelPropertyName;
    IdPropertyName = idPropertyName;
    if (timeout is not null) Timeout = timeout.Value;
  }

  public DropdownAttribute(object[] validValues)
  {
    ValidValues = validValues.ToList();
  }

  public DropdownAttribute(object[] validValues, string? idPropertyName, string? labelPropertyName)
  {
    ValidValues = validValues.ToList();
    LabelPropertyName = labelPropertyName;
    IdPropertyName = idPropertyName;

  }

  public DropdownAttribute(object[] validValues, string? idPropertyName, string? labelPropertyName, TimeSpan? timeout)
  {
    ValidValues = validValues.ToList();
    LabelPropertyName = labelPropertyName;
    IdPropertyName = idPropertyName;
    if (timeout is not null) Timeout = timeout.Value;

  }

  public DropdownAttribute(Hashtable validValues)
  {
    ValidValues = BuildValidValuesFromHashtable(validValues);
    LabelPropertyName = "Key";
    IdPropertyName = "Value";
  }

  public DropdownAttribute(Hashtable validValues, TimeSpan? timeout)
  {
    ValidValues = BuildValidValuesFromHashtable(validValues);
    LabelPropertyName = "Key";
    IdPropertyName = "Value";
    if (timeout is not null) Timeout = timeout.Value;
  }

  public DropdownAttribute(Hashtable validValues, string? idPropertyName)
  {
    ValidValuesHasNestedIdProperty = !string.IsNullOrEmpty(idPropertyName);
    ValidValues = BuildValidValuesFromHashtable(validValues);
    LabelPropertyName = "Key";
    IdPropertyName = idPropertyName;
  }

  public DropdownAttribute(Hashtable validValues, string? idPropertyName, TimeSpan? timeout)
  {
    ValidValuesHasNestedIdProperty = !string.IsNullOrEmpty(idPropertyName);
    ValidValues = BuildValidValuesFromHashtable(validValues);
    LabelPropertyName = "Key";
    IdPropertyName = idPropertyName;
    if (timeout is not null) Timeout = timeout.Value;
  }

  public static List<object> BuildValidValuesFromHashtable(Hashtable validValues)
  {
    var vals = new List<object>();
    foreach (var key in validValues.Keys)
    {
      if (key?.ToString() is not { } keyString) continue;
      vals.Add(new HashtableKeyValuePair(keyString, validValues[key]));
    }

    return vals;
  }
  public object? GetDropdownValueById(string id)
  {
    try
    {
      return _originalValuesForDropdown?[id];
    }
    catch
    {
      return null;
    }
  }

  public List<object> GetDropdownValuesByIds(string[] ids)
  {
    var values = new List<object>();
    foreach (var id in ids)
    {
      var value = GetDropdownValueById(id);
      if (value != null)
      {
        values.Add(value);
      }
    }
    return values;
  }

  /// <summary>
  /// The original key value pairs to rehydrate from when a value is selected
  /// </summary>
  private OrderedDictionary? _originalValuesForDropdown;


  protected override object? Transform(IRunContext runContext, IServiceScope serviceScope, EngineIntrinsics engineIntrinsics, object? inputData)
  {
    if (inputData is null)
    {
      throw new ValidationException("Select an option");
    }

    // fetch original values for dropdown if they haven't been fetched yet
    if (_originalValuesForDropdown is null)
    {
      var databaseType = engineIntrinsics.SessionState.PSVariable.GetValue("DatabaseType") as DatabaseType?;
      GetValidDropdownValues(runContext, new(), databaseType ?? DatabaseType.Global, default);
    }

    if (inputData is object[] selectedItems)
    {
      if (MultiSelect)
      {
        return GetDropdownValuesByIds(selectedItems.Select(a => a.ToString() ?? string.Empty).ToArray());
      }

      throw new ValidationException(
        "This dropdown does not allow multi-select and only one value can be selected");
    }

    if (inputData is Hashtable ht)
    {
      var caseInsensitiveHashTable = new Hashtable(ht, StringComparer.OrdinalIgnoreCase);
      inputData = caseInsensitiveHashTable[IdPropertyName ?? "Value"];
      if (inputData is null)
        throw new ValidationException(
          "Dropdown parameter's argument transformation Failed. The value property was expected in the hashtable and was not present.");

      var htId = inputData.ToString();
      return htId is null ? null : GetDropdownValueById(htId);
    }

    if (inputData is PSObject psObject)
    {
      var psObjId = psObject.Properties[IdPropertyName ?? "Value"]?.Value?.ToString();
      return psObjId is null ? null : GetDropdownValueById(psObjId);
    }

    var idValue = inputData.ToString();
    return idValue is null ? null : GetDropdownValueById(idValue);
  }
}

public record HashtableKeyValuePair(string Key, object? Value);
