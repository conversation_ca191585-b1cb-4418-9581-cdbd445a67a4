using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Management.Automation;
using System.Text.Json;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Domain.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.Threading;
using Newtonsoft.Json;

namespace Immybot.Backend.Application.Lib.MetaScripts.Attributes;

[AttributeUsage(AttributeTargets.Parameter)]
public class OauthConsentAttribute : ServiceScopeArgumentTransformationAttribute
{
  public Dictionary<string, object> ExtraQueryParameters { get; set; } = new();
  public Uri AuthorizationEndpoint { get; set; }
  public Uri TokenEndpoint { get; set; }
  public required string ResponseType { get; set; }
  public string? ClientId { get; set; }
  public string? Resource { get; set; }
  public string? Scope { get; set; }

  public OauthConsentAttribute()
  {
    AuthorizationEndpoint ??= new Uri("https://login.microsoftonline.com/organizations/oauth2/v2.0/authorize");
    TokenEndpoint ??= new Uri("https://login.microsoftonline.com/organizations/oauth2/v2.0/token");
  }

  public ImmutableDictionary<string, string> GetExtraQueryParameterStrings()
  {
    var dict = ImmutableDictionary.CreateBuilder<string, string>();
    foreach (var key in ExtraQueryParameters.Keys)
    {
      if (ExtraQueryParameters[key] is not { } val)
      {
        continue;
      }

      // if the value is not primitive, then serialize it
      var t = val.GetType();
      bool isPrimitiveType = t.IsPrimitive || t.IsValueType || (t == typeof(string));
      dict[key] = isPrimitiveType ? val.ToString()! : JsonConvert.SerializeObject(val);
    }
    return dict.ToImmutable();
  }

  protected override object? Transform(IServiceScope serviceScope, EngineIntrinsics engineIntrinsics, object? inputData)
  {
    JsonElement jsonElement;
    if (inputData is JsonElement data)
    {
      jsonElement = data;
    }
    else if (inputData is ParameterValue { Value: not null } parameterValue)
      jsonElement = (JsonElement)parameterValue.Value;
    else
      return inputData;

    return new JoinableTaskContext().Factory.Run(async () =>
    {
      var oauthAccessTokenStore = serviceScope.ServiceProvider.GetService<IOauthAccessTokenStore>();

      OauthConsentParameterValueIdentifier? oauth2AccessTokenId = null;

      try
      {
        oauth2AccessTokenId = jsonElement
          .Deserialize<OauthConsentParameterValueIdentifier>(CaseInsensitiveSerializerOptions);
      }
      catch (System.Text.Json.JsonException)
      {
        // ignore, attempt to parse below
      }

      if (oauth2AccessTokenId?.Id is null or 0 || string.IsNullOrEmpty(oauth2AccessTokenId.AccessTokenId))
      {
        try
        {
          // perhaps we were passed the oauth2 access token response
          var consent = jsonElement
            .Deserialize<Oauth2AccessTokenResponseWithExpirationDate>(CaseInsensitiveSerializerOptions);
          if (consent?.AccessToken != null)
          {
            return new OauthConsentParameterValue(
              consent.AccessTokenId,
              consent.AccessToken,
              consent.TokenType,
              consent.IdToken,
              consent.ExpiresAtUtc);
          }
        }
        catch
        {
          // ignore
        }
      }

      if (string.IsNullOrEmpty(oauth2AccessTokenId?.AccessTokenId) || oauth2AccessTokenId.Id is 0)
        return null;

      var oauth2AccessToken = await oauthAccessTokenStore!
        .GetOauthAccessTokenById(oauth2AccessTokenId.Id, CancellationToken);

      // Extra security: user must also specify the access token id (which is not guessable)
      // We also check if the user has access to the token below
      if (oauth2AccessToken.AccessTokenId != oauth2AccessTokenId.AccessTokenId) return null;

      // If run context has access to MSP resources or the token was created by
      // the user who ran the script, then allow access to the auth token value
      if (!CanAccessMspResources
        && oauth2AccessToken.CreatedBy != ManuallyTriggeredByUserId)
        return null;

      var accessToken = await oauthAccessTokenStore
        .HydrateOauthAccessToken(oauth2AccessToken, CancellationToken);

      return new OauthConsentParameterValue(
        accessToken.AccessTokenId,
        accessToken.AccessToken,
        accessToken.TokenType,
        accessToken.IdToken,
        accessToken.ExpiresAtUtc);
    });
  }
}
