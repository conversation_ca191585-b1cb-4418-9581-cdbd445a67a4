using System.Diagnostics;
using System.Management.Automation;
using System.Management.Automation.Runspaces;
using Immybot.Backend.Application.DynamicProviders;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Shared.Services;
using Immybot.Shared.Telemetry;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Application.Lib.MetaScripts;

interface IMetascriptRunspaceServer : IObjectPoolServer<MetascriptRunspaceServerItem, MetascriptRunspaceServerRequest>;

record MetascriptRunspaceServerItem(
  Runspace Runspace,
  CustomPSHost Host
);

record MetascriptRunspaceServerRequest(
  Script Script,
  bool CanAccessMspResources,
  bool CanAccessParentTenant,
  int? TenantId = null,
  Guid? SelfCancellationId = null,
  Guid? ScriptOutputCorrelationId = null,
  Computer? AgainstComputer = null,
  int? FilterScriptTenantId = null,
  int? FilterScriptComputerId = null,
  bool FilterScriptIncludeChildTenants = false,
  IRunContext? RunContext = null,
  ICustomPSHostUIHandler? UIHandler = null,
  bool RunInFullLanguageMode = false,
  CancellationToken CancellationToken = default
);

class WeakReferenceCache<TKey, TValue>
  where TKey : notnull
  where TValue : class
{
  private readonly Dictionary<TKey, WeakReference<TValue>> _map = new();

  public async Task<TValue> GetOrCreateAsync(TKey key, Func<TKey, Task<TValue>> factory)
  {
    if (_map.TryGetValue(key, out var weakReference) && weakReference.TryGetTarget(out var value))
      return value;

    value = await factory(key);
    _map[key] = new(value);
    return value;
  }

  public void Clear() => _map.Clear();
}

class MetascriptRunspaceServer(
  IFunctionScriptManager functionScriptManager,
  IDynamicProviderStore dynamicProviderStore,
  IInitialSessionStateFactory initialSessionStateFactory,
  IOptionsMonitor<RunspacePoolOptions> runspacePoolOptions,
  ILogger<MetascriptRunspaceServer> logger,
  TimeProvider timeProvider)
  : ObjectPoolServer<
      MetascriptRunspaceServerItem,
      MetascriptRunspaceServerRequest,
      MetascriptRunspaceAttributes,
      SessionStateFunctionEntry[],
      RunspacePoolOptions>(logger, timeProvider, runspacePoolOptions),
    IMetascriptRunspaceServer
{
  private readonly WeakReferenceCache<MetascriptRunspaceAttributes, InitialSessionState> _initialSessionStateCache = new();
  private long? _functionScriptCacheVersion;

  protected override async Task WaitForMaintenance(CancellationToken cancellationToken)
  {
    var newFunctionScriptCacheVersion = await functionScriptManager.WaitForCacheChangeAsync(_functionScriptCacheVersion, cancellationToken);
    if (newFunctionScriptCacheVersion != _functionScriptCacheVersion)
    {
      _functionScriptCacheVersion = newFunctionScriptCacheVersion;
      _initialSessionStateCache.Clear();
      Invalidate();
    }
  }

  protected override MetascriptRunspaceAttributes DeriveKey(MetascriptRunspaceServerRequest request) =>
    MetascriptRunspaceExtensions.GetRunspaceAttributes(
      request.Script.ScriptExecutionContext,
      request.Script.ScriptCategory,
      request.Script.ScriptType,
      request.Script.DynamicProviderStoreId,
      request.CanAccessMspResources,
      request.CanAccessParentTenant,
      request.RunInFullLanguageMode);

  protected override async Task<MetascriptRunspaceServerItem> CreateObjectAsync(MetascriptRunspaceAttributes key)
  {
    // TODO something weird's going on with activities here where there are powershell cmdlet spans inside script db query spans inside this span...
    //   maybe we should block sending activity over to the runspace thread (and/or ensure it gets set correctly when actually invoking the powershell elsewhere?)
    using var activity = Telemetry.StartActivity(ActivityType.Script, "Create runspace");

    var initialSessionState = await _initialSessionStateCache.GetOrCreateAsync(key,
      k => initialSessionStateFactory.CreateInitialSessionStateAsync(k, CancellationToken.None));

    var host = new CustomPSHost();
    var runspace = MetascriptRunspaceExtensions.CreateAndOpenRunspace(
      initialSessionState,
      host,
      runspacePoolOptions.CurrentValue.RunspaceThreadOptions,
      shouldCloneInitialSessionState: false); // otherwise the cache would probably be useless

    // HACK run dummy script to ensure our hooks have been injected into the pipeline execution thread
    using (Activity.Current?.StartEvent("script.runspace.create.dummy_script"))
    {
      using var powershell = PowerShell.Create(runspace);
      powershell.AddScript("1");
      await powershell.InvokeAsync();
    }

    Runspace.DefaultRunspace = null; // DefaultRunspace is ThreadStatic, so it won't be useful to the borrower ... and leaving this set is troublesome

    return new(runspace, host);
  }

  protected override async Task DestroyObjectAsync(MetascriptRunspaceServerItem @object) =>
    @object.Runspace.Dispose();

  protected override async Task<SessionStateFunctionEntry[]> OnBorrowingAsync(MetascriptRunspaceServerRequest request, MetascriptRunspaceServerItem @object, CancellationToken token)
  {
    using var activity = Activity.Current?.StartEvent("script.runspace.setup");

    var tenantedLocalScripts = request.TenantId is null
      ? []
      : await functionScriptManager.GetAddableScriptsAsync(@object.Runspace.InitialSessionState.Commands, false, false, request.TenantId.Value, token);

    @object.Runspace.SetScriptSelfCancellationIdBasedVariables(request.SelfCancellationId ?? Guid.Empty);
    @object.Runspace.SetScriptOutputCorrelationIdBasedVariables(request.ScriptOutputCorrelationId);
    @object.Runspace.SetComputerBasedVariables(request.AgainstComputer);
    @object.Runspace.SetFilterBasedVariables(request.FilterScriptTenantId, request.FilterScriptComputerId, request.FilterScriptIncludeChildTenants);
    @object.Runspace.SetRunContextBasedVariables(request.RunContext);
    @object.Runspace.SetTenantBasedVariables(request.TenantId);
    @object.Runspace.SetScriptBasedVariables(request.Script, dynamicProviderStore);
    @object.Runspace.SetCancellationTokenBasedVariables(request.CancellationToken);

    foreach (var entry in tenantedLocalScripts)
      @object.Runspace.ExecutionContext.EngineSessionState.AddSessionStateEntry(entry);

    @object.Host.UIHandler = request.UIHandler;

    // set this just before handing out runspace so this thread static local (Runspace.DefaultRunspace) is set on the borrower's thread for running PowerShell that may depend on it
    Runspace.DefaultRunspace = @object.Runspace;

    return tenantedLocalScripts;
  }

  protected override async Task<bool> OnReturnedAsync(MetascriptRunspaceServerItem @object, SessionStateFunctionEntry[] state)
  {
    using var _ = Activity.Current?.StartEvent("script.runspace.return");

    Runspace.DefaultRunspace = null;
    @object.Host.UIHandler = null;

    foreach (var entry in state)
      @object.Runspace.ExecutionContext.EngineSessionState.RemoveFunction(entry.Name, true);

    return true;
  }

  // based on InitialSessionState.ResetRunspaceState, but with the slow/unnecessary/breaking stuff removed
  protected override async Task ResetObjectForReuseAsync(MetascriptRunspaceServerItem @object)
  {
    var runspace = @object.Runspace;

    // NOTE: this includes clearing all variables
    runspace.ExecutionContext.EngineSessionState.InitializeSessionStateInternalSpecialVariables(true);

    // don't need to do this since our initial session state includes all built-in variables we want
    // foreach (var variable in InitialSessionState.BuiltInVariables)
    //   runspace.ForceSetVariable(variable, variable.Value);

    runspace.ExecutionContext.EngineSessionState.InitializeFixedVariables();

    foreach (var variable in runspace.InitialSessionState.Variables)
      runspace.ForceSetVariable(variable, variable.Value);

    // this variable is already set in InitializeFixedVariables,
    //   and this method relies on both Runspace.DefaultRunspace being correctly set and the language mode being FullLanguage (see CheckVariableChangeInConstrainedLanguage)
    // InitialSessionState.CreateQuestionVariable(context);

    // this guy is very expensive, and we were having to explicitly unset the drive after ResetRunspaceState to not break `Foreach-Object -Parallel` when it was setting up runspaces
    // InitialSessionState.SetSessionStateDrive(context, true);

    runspace.ExecutionContext.ResetManagers();
    runspace.ExecutionContext.PSDebugTraceLevel = 0;
    runspace.ExecutionContext.PSDebugTraceStep = false;
  }
}
