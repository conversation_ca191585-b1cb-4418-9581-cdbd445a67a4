using System.Linq;
using Immybot.Backend.Application.DynamicProviders;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Backend.Application.Lib.MetaScripts;
internal abstract class IntegrationPSCmdlet : RunContextPSCmdlet
{
  protected ProviderLink ProviderLink { get; private set; } = default!;
  private int _providerLinkId;
  protected override void BeginProcessing()
  {
    base.BeginProcessing();
    if (this.TryGetVariableValueNullable(SessionStateEntries.ProviderLinkIdForMaintenanceItemVariableEntry) is not { } providerLinkId)
      throw new DynamicProviderException("An integration is not linked to this script. If this script was run during a maintenance session, re-save the deployment associated with this script's action to ensure the integration is linked.");

    _providerLinkId = providerLinkId;

    ProviderLink = new JoinableTaskContext().Factory.Run(async () =>  await RunContext.GetProviderLinkById(providerLinkId)) ??
      throw new DynamicProviderException($"Failed to find integration #{providerLinkId}");
  }

  private string? _integrationClientId;

  protected string GetIntegrationClientId()
  {
    if (_integrationClientId is not null)
    {
      return _integrationClientId;
    }

    if (this.RunContext is null)
      throw new DynamicProviderException("RunContext is required for this integration cmdlet.");

    var clients = LocalDbContext.ProviderClients
      .AsNoTracking()
      .Where(a => a.LinkedToTenantId == RunContext.TenantId && a.ProviderLinkId == _providerLinkId)
      .Select(a => a.ExternalClientId)
      .ToList();

    if (!clients.Any())
      throw new DynamicProviderException($"{this.RunContext.TenantName} is not mapped under Integrations->{ProviderLink.Name}->Clients");
    if (clients.Count > 1)
      throw new DynamicProviderException($"{this.RunContext.TenantName} is linked to more than one client in {ProviderLink.Name}. Either ensure a 1:1 mapping under Integrations->{ProviderLink.Name}->Clients or specify ExternalClientId.");

    _integrationClientId = clients[0];
    return _integrationClientId;
  }


  private string? _integrationAgentId;

  protected string GetIntegrationAgentId()
  {
    if (_integrationAgentId is not null)
    {
      return _integrationAgentId;
    }

    if (this.RunContext is null)
      throw new DynamicProviderException("RunContext is null");

    var agent = LocalDbContext.ProviderAgents
      .AsNoTracking()
      .Where(a => a.ComputerId == RunContext.ComputerId && a.ProviderLinkId == _providerLinkId)
      .Select(a => a.ExternalAgentId)
      .FirstOrDefault();

    _integrationAgentId = agent ?? throw new DynamicProviderException($"{this.RunContext.ComputerName} does not have a {ProviderLink.Name} agent");
    return agent;
  }
}
