using System;
using System.Globalization;
using System.Linq;
using System.Management.Automation;
using System.Management.Automation.Host;
using System.Management.Automation.Runspaces;
using System.Threading;
using Immybot.Backend.Application.DynamicProviders;
using Immybot.Backend.Application.Lib.Helpers;
using Immybot.Backend.Application.Lib.MetaScripts.Modules;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Microsoft.PowerShell;

namespace Immybot.Backend.Application.Lib.MetaScripts;

public static class MetascriptRunspaceExtensions
{
  public static MetascriptRunspaceAttributes GetRunspaceAttributes(
    ScriptExecutionContext scriptExecutionContext,
    ScriptCategory scriptCategory,
    DatabaseType scriptType,
    string? dynamicProviderStoreId = null,
    bool canAccessMspResources = false,
    bool canAccessParentTenant = false,
    bool runInFullLanguageMode = false)
  {
    var attributes = MetascriptRunspaceAttributes.IncludeGlobalFunctionScripts;

    if (scriptCategory == ScriptCategory.FilterScriptDeploymentTarget)
      attributes |= MetascriptRunspaceAttributes.FilterScript;

    if (scriptCategory is ScriptCategory.Integration)
    {
      attributes |= MetascriptRunspaceAttributes.IncludeDynamicProviderStuff;

      if (!string.IsNullOrEmpty(dynamicProviderStoreId))
        attributes |= MetascriptRunspaceAttributes.IncludeDynamicProviderStoreStuff;
    }

    if (scriptType == DatabaseType.Local)
      attributes |= MetascriptRunspaceAttributes.IncludeTenantlessLocalFunctionScripts;

    if (scriptExecutionContext is ScriptExecutionContext.Metascript or ScriptExecutionContext.CloudScript)
      attributes |= MetascriptRunspaceAttributes.MetaScriptOrCloudScript;

    if (runInFullLanguageMode)
      attributes |= MetascriptRunspaceAttributes.RunInFullLanguageMode;

    if (canAccessMspResources)
      attributes |= MetascriptRunspaceAttributes.CanAccessMspResources;

    if (canAccessParentTenant)
      attributes |= MetascriptRunspaceAttributes.CanAccessParentTenant;

    return attributes;
  }

  public static Runspace CreateAndOpenRunspace(
    InitialSessionState initialSessionState,
    PSHost? host = null,
    PSThreadOptions threadOptions = PSThreadOptions.Default,
    bool shouldCloneInitialSessionState = true)
  {
    host ??= new DefaultHost(CultureInfo.CurrentCulture, CultureInfo.CurrentUICulture);

    var runspace = shouldCloneInitialSessionState
      ? RunspaceFactory.CreateRunspace(host, initialSessionState)
      : RunspaceFactory.CreateRunspaceFromSessionStateNoClone(host, initialSessionState); // for performance if we're explicitly sharing initial session states between runspaces

    runspace.ThreadOptions = threadOptions;
    runspace.Open();

    return runspace;
  }

  public static void ForceSetVariable(this Runspace runspace, PSVariable variable) =>
    runspace.ExecutionContext.EngineSessionState.GlobalScope.SetVariableForce(
      variable,
      runspace.ExecutionContext.EngineSessionState);

  public static void ForceSetVariable(this Runspace runspace, SessionStateVariableEntry variable, object? value) =>
    runspace.ForceSetVariable(
      new PSVariable(variable.Name, value, variable.Options, variable.Attributes, variable.Description)
      {
        Visibility = variable.Visibility
      });

  public static void ForceSetVariable<T>(this Runspace runspace, ImmyVariableEntry<T> entry, T? value)
    where T : class => runspace.ForceSetVariable(entry.ToPSVariable(value));

  public static void ForceSetVariable<T>(this Runspace runspace, ImmyVariableEntry<T> entry, T? value)
    where T : struct => runspace.ForceSetVariable(entry.ToPSVariable(value));

  // TODO WithTerminalId wasn't used anywhere in master?
  public static void SetTerminalBasedVariables(this Runspace runspace,
    Guid? terminalId)
  {
    if (terminalId is not null)
      runspace.ForceSetVariable(SessionStateEntries.TerminalIdVariableEntry,
        terminalId.Value);
  }

  public static void SetScriptSelfCancellationIdBasedVariables(this Runspace runspace,
    Guid scriptSelfCancellationId)
  {
    runspace.ForceSetVariable(SessionStateEntries.ScriptSelfCancellationIdVariableEntry,
      scriptSelfCancellationId);
  }

  public static void SetCancellationTokenBasedVariables(this Runspace runspace,
    CancellationToken cancellationToken)
  {
    runspace.ForceSetVariable(SessionStateEntries.CancellationTokenVariableEntry,
      cancellationToken);
  }

  public static void SetFilterBasedVariables(this Runspace runspace,
    int? filterScriptTenantId = null,
    int? filterScriptComputerId = null,
    bool filterScriptIncludeChildTenants = false)
  {
    runspace.ForceSetVariable(SessionStateEntries.FilterScriptTenantIdVariableEntry,
      filterScriptTenantId);
    runspace.ForceSetVariable(SessionStateEntries.FilterScriptComputerIdVariableEntry,
      filterScriptComputerId);
    runspace.ForceSetVariable(SessionStateEntries.FilterScriptIncludeChildTenantsVariableEntry,
      filterScriptIncludeChildTenants);
  }

  public static void SetScriptBasedVariables(this Runspace runspace,
    Script script,
    IDynamicProviderStore? dynamicProviderStore)
  {
    runspace.ForceSetVariable(SessionStateEntries.ScriptNameVariableEntry,
      script.Name);
    runspace.ForceSetVariable(SessionStateEntries.ScriptVariablesVariableEntry,
      script.Variables);

    if (script.Variables is { } scriptVariables)
      foreach (var variable in scriptVariables)
        runspace.ForceSetVariable(
          new ImmyVariableEntry<object>(
            variable.Key,
            "Script variable injected by ImmyBot",
            SessionStateEntryVisibility.Public,
            ScopedItemOptions.None), // it's not uncommon for scripts to overwrite their parameters' values
          variable.Value);

    runspace.ForceSetVariable(SessionStateEntries.ScriptCategoryVariableEntry,
      script.ScriptCategory);
    runspace.ForceSetVariable(SessionStateEntries.ProviderLinkIdForMaintenanceItemVariableEntry,
      script.ProviderLinkIdForMaintenanceItem);
    runspace.ForceSetVariable(SessionStateEntries.SkipPreflightVariableEntry,
      script.SkipPreflight);
    runspace.ForceSetVariable(SessionStateEntries.SkipBusinessHoursCheckVariableEntry,
      script.SkipBusinessHoursCheck);
    runspace.ForceSetVariable(SessionStateEntries.DatabaseTypeVariableEntry,
      script.ScriptType);
    runspace.ForceSetVariable(SessionStateEntries.IntegrationContextVariableEntry,
      script.DynamicProviderStoreId is null ? null : dynamicProviderStore?.GetState(script.DynamicProviderStoreId));
    runspace.ForceSetVariable(SessionStateEntries.DynamicIntegrationTypePropertiesVariableEntry,
      script.DynamicIntegrationTypeProperties);
  }

  public static void SetComputerBasedVariables(this Runspace runspace,
    Computer? computer)
  {
    runspace.ForceSetVariable(SessionStateEntries.ComputerVariableEntry,
      computer is null
        ? null
        : PowerShellHelpers.ConvertFromComputerToPSObject([computer], null).FirstOrDefault());
  }

  public static void SetScriptOutputCorrelationIdBasedVariables(this Runspace runspace,
    Guid? scriptOutputCorrelationId)
  {
    runspace.ForceSetVariable(SessionStateEntries.ScriptOutputCorrelationIdVariableEntry,
      scriptOutputCorrelationId);
  }

  public static void SetRunContextBasedVariables(this Runspace runspace,
    IRunContext? runContext)
  {
    runspace.ForceSetVariable(SessionStateEntries.ManuallyTriggeredByUserIdVariableEntry,
      runContext?.Args?.ManuallyTriggeredBy?.Id);
    runspace.ForceSetVariable(SessionStateEntries.RunContextVariableEntry,
      runContext);
    runspace.ForceSetVariable(SessionStateEntries.SessionGroupIDVariableEntry,
      runContext?.Args?.CacheGroupId);

    if (runContext is IActionRunContext actionRunContext)
    {
      runspace.ForceSetVariable(SessionStateEntries.ActionIdVariableEntry,
        actionRunContext.ActionId);
      runspace.ForceSetVariable(SessionStateEntries.ActionNameVariableEntry,
        actionRunContext.Action.MaintenanceDisplayName);
      runspace.ForceSetVariable(SessionStateEntries.ActionStatusVariableEntry,
        actionRunContext.Action.ActionStatus);
      runspace.ForceSetVariable(SessionStateEntries.ActionReasonVariableEntry,
        actionRunContext.Action.ActionReason);
      runspace.ForceSetVariable(SessionStateEntries.ActionResultVariableEntry,
        actionRunContext.Action.ActionResult);
      runspace.ForceSetVariable(SessionStateEntries.ActionResultReasonVariableEntry,
        actionRunContext.Action.ActionResultReason);
      runspace.ForceSetVariable(SessionStateEntries.ActionParametersVariableEntry,
        actionRunContext.Action.Parameters is null
          ? null
          : PSSerializer.Deserialize(actionRunContext.Action.Parameters));

      if (actionRunContext.Action.SoftwareType is not null)
      {
        runspace.ForceSetVariable(SessionStateEntries.ActionDetectedVersionVariableEntry,
          actionRunContext.Action.DetectedVersion.ToAutomationSemanticVersion(
            formatExceptionMessagePrefix: actionRunContext.Action.MaintenanceDisplayName));
        runspace.ForceSetVariable(SessionStateEntries.ActionDesiredVersionVariableEntry,
          actionRunContext.Action.DesiredVersion.ToAutomationSemanticVersion(
            formatExceptionMessagePrefix: actionRunContext.Action.MaintenanceDisplayName));
        runspace.ForceSetVariable(SessionStateEntries.ActionDesiredSoftwareStateVariableEntry,
          actionRunContext.Action.DesiredSoftwareState);
      }
    }

    if (runContext is ISessionRunContext sessionRunContext)
    {
      runspace.ForceSetVariable(SessionStateEntries.SessionIdVariableEntry,
        sessionRunContext.SessionId);
      runspace.ForceSetVariable(SessionStateEntries.SessionIsRepairingVariableEntry,
        sessionRunContext.Args.Repair);
    }
  }

  public static void SetTenantBasedVariables(this Runspace runspace,
    int? tenantId)
  {
    runspace.ForceSetVariable(SessionStateEntries.LimitToScriptsForTenantVariableEntry,
      tenantId);
  }
}
