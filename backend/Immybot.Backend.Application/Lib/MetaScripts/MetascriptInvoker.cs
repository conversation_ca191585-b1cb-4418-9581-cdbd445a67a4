using System.Collections;
using System.Diagnostics;
using System.Management.Automation;
using System.Management.Automation.Runspaces;
using System.Text;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Maintenance;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Domain;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Infrastructure.Configuration.Application;
using Immybot.Shared.Scripts;
using Immybot.Shared.Telemetry;
using Microsoft.Extensions.Options;
using Polly;

namespace Immybot.Backend.Application.Lib.MetaScripts;

internal class MetascriptInvoker(
  IMetascriptRunspaceServer _metascriptRunspaceServer,
  ICachedSingleton<ApplicationPreferences> _cachedAppPrefs,
  IMetascriptMessageHandler _metascriptMessageHandler,
  IOptionsMonitor<EphemeralAgentSettings> _ephemeralAgentSettings,
  IPowershellLoader _powershellLoader,
  IImmyCancellationManager _immyCancellationManager,
  IPowerShellErrorEventHubService _powerShellErrorEventHubService) : IMetascriptInvoker
{
  public Guid? TerminalId { get; private set; }
  public IMetascriptMessageHandler MetascriptMessageHandler => _metascriptMessageHandler;

  public void SetTerminalId(Guid terminalId)
  {
    // IMetascriptInvoker and IMetascriptMessageHandler are both transient, meaning
    // each invoker will have its own handler, so we're good to keep state on the handler
    TerminalId = terminalId;
    MetascriptMessageHandler.SetTerminalId(terminalId);
  }

  public async Task<MetaScriptResult<T>> RunMetascript<T>(
    bool canAccessMspResources,
    bool canAccessParentTenant,
    Script script,
    CancellationToken cancellationToken,
    TimeSpan timeout,
    IRunContext? runContext = null,
    Guid? scriptOutputCorrelationId = null,
    Computer? againstComputer = null,
    int? filterScriptComputerId = null,
    int? filterScriptTenantId = null,
    bool filterScriptIncludeChildTenants = false,
    IAsyncPolicy? cachePolicy = null,
    Context? policyContext = null,
    Guid? cacheId = null,
    bool runInFullLanguageMode = false)
  {
    againstComputer ??= runContext?.Args?.Computer;
    var tenantId = runContext?.TenantId ?? filterScriptTenantId;
    var tenantName = runContext?.TenantName;

    using var activity = Telemetry.StartActivity(ActivityType.Script,
      $"Invoke script `{script.Name}`",
      new()
      {
        { "script.name", script.Name },
        { "script.id", script.Id },
        { "script.type", script.ScriptType },
        { "script.tenant.id", tenantId },
        { "script.tenant.name", tenantName },
        { "script.computer.id", againstComputer?.Id },
        { "script.timeout_ms", timeout.TotalMilliseconds },
        { "script.run_context.type", runContext?.GetType().Name }
      });

    if (activity != null)
      foreach (var parameter in script.Parameters)
        activity.AddTag($"script.parameter.{parameter.Key}", parameter.Value);

    // This script cancellationToken is used to facilitate cancellation of the script from the
    // inside-out. A variable will be supplied to the script providing the cancellationId, which,
    // it can then use to request itself to be terminated. This was added primarily for the
    // Invoke-AtomicCommand and Invoke-CachedCommand, which need the ability to forcefully
    // terminate themselves in the situations of cancellation or watchdog deadlock detection.
    var selfCancellationId = Guid.NewGuid();
    var scriptSelfCancellationToken = _immyCancellationManager
      .GetOrCreateScriptCancellationToken(selfCancellationId);

    using var timeoutCts = new CancellationTokenSource(timeout);
    using var linkedCts = CancellationTokenSource
      .CreateLinkedTokenSource(cancellationToken, timeoutCts.Token, scriptSelfCancellationToken);

    CancellationToken powerShellCancellationToken = cancellationToken;

    if (script.ScriptExecutionContext is ScriptExecutionContext.CloudScript or ScriptExecutionContext.Metascript)
    {
      powerShellCancellationToken = linkedCts.Token;
    }

    var actionRunContext = runContext as IActionRunContext;
    var activitiesEnabled = actionRunContext?.ApplicationPreferences.EnableMaintenanceActionActivities is true;
    Action<MaintenanceActionActivity>? actionActivityHandler =
      actionRunContext?.ActionId is not null && activitiesEnabled
        ? actionActivity =>
        {
          actionActivity.MaintenanceSessionId = actionRunContext.SessionId;
          actionActivity.MaintenanceActionId = actionRunContext.ActionId;
          actionActivity.ScriptName = script.Name;
          runContext?.AddMaintenanceActionActivity(actionActivity).Forget();
        }
        : null;

    var outputStringBuilder = new StringBuilder();
    var uiHandler = new UIHandler(runContext,
      actionActivityHandler,
      MetascriptMessageHandler,
      outputStringBuilder,
      scriptOutputCorrelationId);

    await using var runspaceRental = await _metascriptRunspaceServer.BorrowAsync(
      new(
        Script: script,
        CanAccessMspResources: canAccessMspResources,
        CanAccessParentTenant: canAccessParentTenant,
        TenantId: tenantId,
        SelfCancellationId: selfCancellationId,
        ScriptOutputCorrelationId: scriptOutputCorrelationId,
        AgainstComputer: againstComputer,
        FilterScriptComputerId: filterScriptComputerId,
        FilterScriptTenantId: filterScriptTenantId,
        FilterScriptIncludeChildTenants: filterScriptIncludeChildTenants,
        RunContext: runContext,
        UIHandler: uiHandler,
        RunInFullLanguageMode: runInFullLanguageMode,
        CancellationToken: cancellationToken),
      cancellationToken
    );

    using var powershell = PowerShell.Create(runspaceRental.Object.Runspace);

    switch (script.ScriptExecutionContext)
    {
      case ScriptExecutionContext.System:
        {
          if (script.ScriptLanguage == ScriptLanguage.CommandLine)
          {
            script.Action = _powershellLoader.GetPowerShellScriptTemplate("Invoke-CommandLine")
              .Replace("{0}", script.Action.Replace("\n'@", "\n '@")).Replace("{1}",
                _cachedAppPrefs.Value.GetImmyScriptBasePathWithPSVar(_ephemeralAgentSettings.CurrentValue
                  .ScriptFolderSuffix));
            script.ScriptLanguage = ScriptLanguage.PowerShell;
          }

          var p = powershell.AddCommand(SessionStateEntries.InvokeImmyCommandCommandEntry.Name)
            .AddParameter("Verbose", true)
            .AddParameter("Timeout", script.Timeout ?? timeout.TotalSeconds)
            .AddParameter("IncludeLocals", true)
            .AddParameter("ScriptBlock", ScriptBlock.Create(script.Action))
            .AddParameter("ScriptType", script.ScriptLanguage);

          if (script.ErrorActionPreference is ActionPreference.Stop)
          {
            p.AddParameter("ErrorAction", "Stop");
          }

          if (againstComputer != null)
            p.AddParameter("Computer", PSComputer.FromComputer(againstComputer));
          break;
        }
      case ScriptExecutionContext.CurrentUser:
        {
          var p = powershell.AddCommand(SessionStateEntries.InvokeImmyCommandCommandEntry.Name)
            .AddParameter("Verbose", true)
            .AddParameter("Timeout", script.Timeout ?? timeout.TotalSeconds)
            .AddParameter("Context", "User")
            .AddParameter("IncludeLocals", true)
            .AddParameter("ScriptBlock", ScriptBlock.Create(script.Action));

          if (script.ErrorActionPreference is ActionPreference.Stop)
          {
            p.AddParameter("ErrorAction", "Stop");
          }

          if (againstComputer != null)
            p.AddParameter("Computer", PSComputer.FromComputer(againstComputer));
          break;
        }
      default:
        powershell.AddScript(script.Action);

        if (script.Parameters.Count > 0)
        {
          // We exclude null parameters from the param block, because depending on the parameter's type, null is not an acceptable value, even when the parameter isn't mandatory.
          // Excluding a parameter is not the same as passing in null, because the parameter won't be included in the command at all.
          // Until we have a better way to handle this, we will exclude null parameters from the param block.
          var nonNullParamBlockParameters =
            script.Parameters.Where(a => a.Value != null).ToDictionary();
          if (nonNullParamBlockParameters.Count > 0)
          {
            powershell.AddParameters(nonNullParamBlockParameters);
          }
        }

        break;
    }

    var results = new List<object>();
    powershell
      .AddCommand(new CmdletInfo("Tee-Object", typeof(TeeObjectCmdlet)))
      .AddParameter("Collector", results)
      .AddParameter("Type", typeof(T))
      .AddCommand("Out-Default"); // If you take Out-Default out we don't get ConsoleOut and the invocation of PowerShell to write $error will hang perpetually

    powershell.Commands.Commands[^1].MergeUnclaimedPreviousCommandResults
      = PipelineResultTypes.Error | PipelineResultTypes.Output;
    ErrorRecord? terminatingException = null;
    string? cancelMessage = null;
    string? timeoutMessage = null;
    var consoleText = string.Empty;
    try
    {
      // start powershell invocation / wait for cancel token / ps process / timeout - whichever happens first
      try
      {
        using (Activity.Current?.StartEvent("script.invoke"))
          await powershell
            .InvokeAsync()
            .WithCancellation(powerShellCancellationToken);

        activity?.SetStatus(ActivityStatusCode.Ok);
      }
      catch (Exception ex) when (ex.IsCancellationException())
      {
        activity?.SetStatus(ActivityStatusCode.Error);
        activity?.AddException(ex);

        await powershell.StopAsync(_ => { }, null); // NOTE: don't use the synchronous .Stop() because it can run cmdlet stuff on this thread
        if (script.ScriptExecutionContext is ScriptExecutionContext.CloudScript or ScriptExecutionContext.Metascript &&
            timeoutCts.IsCancellationRequested)
          timeoutMessage = $"Script timed out after {timeout.TotalSeconds} seconds.";
        else
          cancelMessage = "Script was canceled";

        if ((timeoutMessage ?? cancelMessage) is { } msg)
          activity?.AddTag("script.error_message", msg);
      }

      // 1. throw "error"
      //   - powershell.InvocationStateInfo.Reason contains error
      //   - powershell.InvocationStateInfo.State is Failed
      // 2. Write-Error "error" -ErrorAction Stop (Terminating)
      //   - powershell.InvocationStateInfo.Reason contains error in the form of an ActionPreferenceStopException
      //   - powershell.InvocationStateInfo.State is Failed
      // 3. Write-Error "error"
      //   - powershell.HadErrors is true but there is no exception in the invocationStateInfo or error stream
      //   - we need to add it manually to the error stream so the caller knows about the error
      //   - These are handled by setting MergeUnclaimedPreviousCommandResults on Out-Default
      if (powershell.HadErrors &&
          powershell.InvocationStateInfo
            .Reason is not null) // There are 3 types of errors we are interested in showing
        throw powershell.InvocationStateInfo.Reason;

      if (powershell.Runspace.SessionStateProxy.GetVariable("error") is ArrayList errors)
      {
        foreach (var error in errors)
        {
          if (error is not ErrorRecord record) continue;
          if (powershell.Streams.Error.Contains(record)) continue;
          powershell.Streams.Error.Add(record);
        }
      }
    }
    catch (ProviderNotFoundException ex) when (ex.ItemName == @"Microsoft.PowerShell.Core\Registry")
    {
      // The Registry provider isn't available in Linux
      // This is technically a non-terminating error that we found in powershell.Streams.Error
      // It is unclear how it got into the error stream in the first place, but it is likely either during deserialization or formatting
      // It became a terminating error when we re-threw it
      // By suppressing this error, we prevent registry manipulation scripts from failing
      activity?.SetStatus(ActivityStatusCode.Ok);
      activity?.AddTag("script.ignored_exception", ex.Message);
    }
    catch (RuntimeException ex)
    {
      if (ex.InnerException is CmdletInvocationException
          {
            InnerException: ParameterBindingValidationException parameterBindingValidationException
          })
      {
        // Gets the meat of the error message when parameter binding errors occur
        ex = parameterBindingValidationException;
      }

      activity?.SetStatus(ActivityStatusCode.Error);
      activity?.AddException(ex);
      activity?.AddTag("error.category", ex.ErrorRecord.CategoryInfo.Category.ToString());

      // timeouts and cancellations trigger RuntimeExceptions
      // but we handle them separately
      if (string.IsNullOrEmpty(timeoutMessage) && string.IsNullOrEmpty(cancelMessage))
      {
        terminatingException = ex.ErrorRecord;
        powershell.Commands.Clear();
        powershell
          .AddCommand("Out-String")
          .AddParameter("InputObject", terminatingException)
          .AddCommand("Write-Host")
          .AddParameter("ForegroundColor", "Red");
        await powershell.InvokeAsync();
      }
    }
    catch (Exception ex)
    {
      consoleText += ex.Message is { Length: > 0 } msg ? msg : ex.ToString();
      activity?.SetStatus(ActivityStatusCode.Error);
      activity?.AddException(ex);

      uiHandler.HandlePsHostEvent(new ExceptionPsHostEvent(
        Exception: ex,
        ContextMessage: "Exception caught in Metascript Invoker"));
    }
    finally
    {
      consoleText += outputStringBuilder.ToString();
      _immyCancellationManager.RemoveScriptCancellationToken(selfCancellationId);
    }

    // if we have a cancel or timeout message then send it to the available channels
    if (cancelMessage != null || timeoutMessage != null)
    {
      var cancelOrTimeoutMessage = (cancelMessage ?? timeoutMessage)!;
      var ex = new Exception(cancelOrTimeoutMessage);

      // add msg to consoleText
      consoleText = string.IsNullOrEmpty(consoleText)
        ? cancelOrTimeoutMessage
        : string.Join("\r\n", consoleText, cancelOrTimeoutMessage);

      // push msg to signalr & session log
      uiHandler.HandlePsHostEvent(new ExceptionPsHostEvent(Exception: ex));

      var err = new ErrorRecord(
        ex,
        cancelMessage != null ? "OperationStopped" : "OperationTimeout",
        cancelMessage != null ? ErrorCategory.OperationStopped : ErrorCategory.OperationTimeout,
        null);

      // append to error stream
      powershell.Streams.Error.Add(err);
      terminatingException = err;
    }

    _powerShellErrorEventHubService.AddEvents(runContext, script, powershell.Streams.Error);

    try
    {
      return new MetaScriptResult<T>(
        results,
        new List<ErrorRecord>(powershell.Streams.Error),
        consoleText,
        terminatingException);
    }
    finally
    {
      powershell.Streams.Error.Dispose();
    }
  }

  class UIHandler(
    IRunContext? runContext,
    Action<MaintenanceActionActivity>? actionActivityHandler,
    IMetascriptMessageHandler? metascriptMessageHandler,
    StringBuilder outputStringBuilder,
    Guid? correlationId) : ICustomPSHostUIHandler
  {
    public void HandlePsHostEvent(PSHostEvent evt)
    {
      runContext?.HandlePsHostEvent(evt, correlationId);
      metascriptMessageHandler?.HandlePsHostEvent(evt, correlationId);
    }

    public void HandleMaintenanceActionActivity(MaintenanceActionActivity activity)
    {
      actionActivityHandler?.Invoke(activity);
    }

    public void HandleOutput(string output)
    {
      outputStringBuilder.Append(output);
    }
  }
}
