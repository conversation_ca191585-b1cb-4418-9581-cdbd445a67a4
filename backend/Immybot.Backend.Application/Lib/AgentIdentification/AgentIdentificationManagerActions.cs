using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Lib.Policies;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.Providers.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Polly;
using Polly.Registry;

namespace Immybot.Backend.Application.Lib.AgentIdentification;
internal class AgentIdentificationManagerActions : IAgentIdentificationManagerActions
{
  private readonly Func<ImmybotDbContext> _ctxFactory;
  private readonly IServiceScopeFactory _serviceScopeFactory;
  private readonly IAsyncPolicy _cachePolicy;

  public AgentIdentificationManagerActions(
    Func<ImmybotDbContext> ctxFactory,
    IServiceScopeFactory serviceScopeFactory,
    IPolicyRegistry<string> policyRegistry)

  {
    _ctxFactory = ctxFactory;
    _serviceScopeFactory = serviceScopeFactory;
    _cachePolicy = policyRegistry.GetAgentIdentificationProviderLinkCachePolicy();
  }

  private async Task<ProviderLink?> GetCachedProviderLink(int providerLinkId)
  {
    var context = new Context();
    context.SetCacheKey(new object[] { ContextKeys.AgentIdentification, providerLinkId });

    return await _cachePolicy.ExecuteAsync((policyContext) =>
    {
      using var ctx = _ctxFactory();
      var id = Convert.ToInt32(context[ContextKeys.ProviderLinkId]);
      var providerLink = ctx.GetProviderLink(id);
      return Task.FromResult(providerLink);
    }, context.ForProviderId(providerLinkId));
  }

  public async Task<IRunScriptProvider?> GetRunScriptProvider(int providerLinkId)
  {
    var providerLink = await GetCachedProviderLink(providerLinkId);
    if (providerLink is null) return null;

    using var scope = _serviceScopeFactory.CreateScope();
    var providerActions = scope.ServiceProvider.GetRequiredService<IProviderActions>();
    return await providerActions.GetRunScriptProvider(providerLink, CancellationToken.None, TimeSpan.FromSeconds(30));
  }

  public async Task<List<ProviderAgent>> GetPendingAgents()
  {
    await using var ctx = _ctxFactory();

    return await ctx.ProviderAgents
      .AsNoTracking()
      .TagForTelemetry()
      .Where(a => a.ComputerId == null && !a.RequireManualIdentification)
      .Include(r => r.IdentificationFailures)
      .Include(r => r.ProviderClient)
      .Include(r => r.ProviderLink)
      .Where(r =>
        // Omit unlinked agents
        r.ProviderClient!.LinkedToTenantId != null &&
        // Omit offline
        r.IsOnline &&
        // Omit disabled or unhealthy providers
        !r.ProviderLink!.Disabled && r.ProviderLink.HealthStatus != HealthStatus.Unhealthy &&
        // Omit agents that don't support running scripts
        r.SupportsRunningScripts &&
        // Omit devices that keep failing
        // Omit devices that are pending a manual resolution
        r.IdentificationFailures.Count(f => !f.Resolved) < 5 &&
        !r.IdentificationFailures.Any(f => !f.Resolved && f.RequiresManualResolution) &&
        !r.IdentificationFailures.Any(f => !f.Resolved && f.UnsupportedDeviceType))
      .ToListAsync();
  }
}
