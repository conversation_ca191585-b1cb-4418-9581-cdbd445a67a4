using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hangfire;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.PersonExtensions;
using Immybot.Backend.Application.DbContextExtensions.TagExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Interface.Jobs;
using Immybot.Backend.Application.Jobs;
using Immybot.Backend.Application.Lib.AgentIdentification;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Lib.Providers;
using Immybot.Backend.Application.Lib.Scripts;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Primitives;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Persistence;
using Immybot.Backend.Providers.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Polly;

namespace Immybot.Backend.Application.Lib;

internal class PendingAgentResolverActions : IPendingAgentResolverActions
{
  private readonly Func<ImmybotDbContext> _ctxFactory;
  private readonly IAgentIdentificationLogHandler _agentIdentificationLogHandler;
  private readonly IImmyServiceJob _immyServiceJob;
  private readonly ICachedSingleton<ApplicationPreferences> _cachedPrefs;
  private readonly ICachedCollection<TenantPreferences> _cachedTenantPrefs;
  private readonly IDomainEventEmitter _eventEmitter;
  private readonly ILogger<PendingAgentResolverActions> _logger;
  private readonly IDeleteComputerCmd _deleteComputerCmd;
  private readonly IScriptInvoker _scriptInvoker;
  private readonly IProviderActions _providerActions;
  private readonly ILoggerFactory _loggerFactory;
  private readonly IBackgroundJobClient _backgroundJobClient;
  private readonly IAgentIdentificationActions _computerIdentificationActions;
  private readonly IPowershellLoader _powershellLoader;
  private readonly IPendoEventManagementService _pendoEventManagement;

  public PendingAgentResolverActions(
    Func<ImmybotDbContext> ctxFactory,
    IAgentIdentificationLogHandler agentIdentificationLogHandler,
    IImmyServiceJob immyServiceJob,
    ICachedSingleton<ApplicationPreferences> cachedPrefs,
    ICachedCollection<TenantPreferences> cachedTenantPrefs,
    IDomainEventEmitter eventEmitter,
    ILogger<PendingAgentResolverActions> logger,
    IDeleteComputerCmd deleteComputerCmd,
    IScriptInvoker scriptInvoker,
    IProviderActions providerActions,
    ILoggerFactory loggerFactory,
    IBackgroundJobClient backgroundJobClient,
    IAgentIdentificationActions computerIdentificationActions,
    IPowershellLoader powershellLoader,
    IPendoEventManagementService pendoEventManagement)
  {
    _ctxFactory = ctxFactory;
    _agentIdentificationLogHandler = agentIdentificationLogHandler;
    _immyServiceJob = immyServiceJob;
    _cachedPrefs = cachedPrefs;
    _cachedTenantPrefs = cachedTenantPrefs;
    _eventEmitter = eventEmitter;
    _logger = logger;
    _deleteComputerCmd = deleteComputerCmd;
    _scriptInvoker = scriptInvoker;
    _providerActions = providerActions;
    _loggerFactory = loggerFactory;
    _backgroundJobClient = backgroundJobClient;
    _computerIdentificationActions = computerIdentificationActions;
    _powershellLoader = powershellLoader;
    _pendoEventManagement = pendoEventManagement;
  }

  private void AddIdentificationLog(
    ProviderAgent providerAgent,
    string message,
    AgentIdentificationLogType logType = AgentIdentificationLogType.Verbose)
  {
    _agentIdentificationLogHandler.AddAgentIdentificationLog(new AgentIdentificationLog
    {
      ProviderAgentId = providerAgent.Id,
      Message = message,
      LogType = logType,
      TimeUtc = DateTime.UtcNow
    },
    providerAgent.DeviceDetails.DeviceName ?? string.Empty);
  }

  private static Dictionary<string, object?> GetLoggerState(ProviderAgent pending)
  {
    var state = new Dictionary<string, object?>
    {
      ["Routine"] = "Agent Identification Resolver",
      ["ProviderLinkId"] = pending.ProviderLinkId.ToString(),
      ["ExternalClientId"] = pending.ExternalClientId,
      ["PendingId"] = pending.Id.ToString(),
      ["DeviceId"] = pending.DeviceDetails.DeviceId?.ToString() ?? "",
    };

    return state;
  }

  private void EmitOnboardingCorrelationComputerCreatedEvent(string correlationId, int computerId)
  {
    if (!string.IsNullOrEmpty(correlationId))
    {
      _eventEmitter.EmitEvent(new CorrelatedOnboardingComputerCreatedEvent(
        correlationId,
        computerId));
    }
  }

  public async Task CreateComputer(ProviderAgent pending, CancellationToken token)
  {
    using var _ = _logger.BeginScope(GetLoggerState(pending));

    // ReSharper disable once ConditionalAccessQualifierIsNonNullableAccordingToAPIContract
    if (pending.DeviceDetails?.DeviceId is not { } deviceId)
    {
      const string noDeviceIdMsg = "Unable to create computer - the agent's device details does not have a DeviceId set";
      _logger.LogError(noDeviceIdMsg);
      AddIdentificationLog(pending, noDeviceIdMsg);
      return;
    }

    const string msg = "Creating a new computer with device id {deviceId} and assigning the new agent to it.";
    _logger.LogDebug(msg, deviceId);
    AddIdentificationLog(pending, msg.Replace("{deviceId}", deviceId.ToString()));

    Computer computer;
    bool isOnboardingEnabled;

    await using (var ctx = _ctxFactory())
    {
      // Prevent setting primary person from different tenant
      // Clear primary person ID if person's tenant doesn't match the provider client's tenant
      if (pending.OnboardingOptions?.PrimaryPersonId.HasValue == true)
      {
        var person = ctx.GetPersonById(pending.OnboardingOptions.PrimaryPersonId.Value);
        var providerClient = await ctx.ProviderClients
          .FirstOrDefaultAsync(pc => pc.ProviderLinkId == pending.ProviderLinkId
                            && pc.ExternalClientId == pending.ExternalClientId, token);

        if (person != null && providerClient?.LinkedToTenantId != null && person.TenantId != providerClient.LinkedToTenantId)
        {
          pending.OnboardingOptions.PrimaryPersonId = null;
          AddIdentificationLog(pending, $"Cleared primary person {person.Id} due to tenant mismatch.");
        }
      }

      isOnboardingEnabled = IsOnboardingEnabled(pending, ctx);

      ctx.AssignAgentToNewComputer(
        pending.Id,
        deviceId,
        isOnboardingEnabled,
        pending.DeviceDetails.OperatingSystemName ?? string.Empty,
        pending.DeviceDetails.DeviceName ?? string.Empty,
        pending.DeviceDetails.SerialNumber ?? string.Empty,
        pending.DeviceDetails.DomainRole,
        pending.DeviceDetails.IsSandbox,
        primaryPersonId: pending.OnboardingOptions?.PrimaryPersonId,
        isDevLab: pending.OnboardingOptions?.IsDevLab ?? false);
      var computerWithNewAgent = ctx.GetComputerByDeviceId(deviceId);
      if (computerWithNewAgent is null)
      {
        AddIdentificationLog(pending, "Failed to create new computer with the agent.", AgentIdentificationLogType.Error);
        return;
      }

      computerWithNewAgent.ChassisTypes = pending.DeviceDetails.ChassisTypes?.ToList();
      computerWithNewAgent.DomainRole = pending.DeviceDetails.DomainRole;
      await ctx.Computers
        .Where(a => a.Id == computerWithNewAgent.Id)
        .ExecuteUpdateAsync(u =>
            u.SetProperty(a => a.ChassisTypes, computerWithNewAgent.ChassisTypes)
              .SetProperty(a => a.DomainRole, computerWithNewAgent.DomainRole),
          token);
      computer = computerWithNewAgent;
    }

    AddIdentificationLog(pending, $"Successfully assigned agent to a new computer with id {computer.Id}");
    await HandlePostResolution(pending, computer.Id, deviceId, isOnboardingEnabled);
  }

  public async Task AssignAgentToComputer(ProviderAgent pending, Computer computer, CancellationToken token)
  {
    using var _ = _logger.BeginScope(GetLoggerState(pending));

    const string msg = "Assigning a new agent to an existing computer with id {computerId}.";
    _logger.LogDebug(msg, computer.Id);
    AddIdentificationLog(pending, msg.Replace("{computerId}", computer.Id.ToString()));

    bool isOnboardingEnabled;

    await using (var ctx = _ctxFactory())
    {
      ctx.AssignAgentToComputer(pending.Id, computer.Id);
      AddIdentificationLog(pending, $"Successfully assigned agent to an existing computer with id {computer.Id}");

      isOnboardingEnabled = IsOnboardingEnabled(pending, ctx);
    }

    await HandlePostResolution(pending, computer.Id, computer.DeviceId, isOnboardingEnabled, allowOnboarding: pending.OnboardingOptions?.AutomaticallyOnboard is true);
  }

  public async Task DedupeComputer(
    ProviderAgent pending,
    Computer computer,
    ProviderAgent existingAgent,
    CancellationToken token)
  {
    await using var ctx = _ctxFactory();

    var state = GetLoggerState(pending);
    using var _ = _logger.BeginScope(state);

    const string msg = "Disambiguating this agent {pendingId} from existing agent {existingAgentId}.";
    _logger.LogDebug(msg, pending.Id, existingAgent.Id);
    AddIdentificationLog(pending, msg.Replace("{pendingId}", pending.Id.ToString()).Replace("{existingAgentId}", existingAgent.Id.ToString()));

    // if the computer has other online agents from other providers, we can use those to detect whether they point to the same computer as the new agent
    // if it does, then we can delete the existing offline agent knowing that the new one is good

    var runScriptProviders = new Dictionary<int, IRunScriptProvider>();
    var computerWithAllAgents = ctx.GetComputerByDeviceId(computer.DeviceId, includeAgents: true);

    var tasks = computerWithAllAgents!.GetRunScriptAgentsForHealthyProviders().Select(async agent =>
    {
      if (!agent.IsOnline) return;
      try
      {
        HydrationException.ThrowIfNull(agent.ProviderLink, nameof(agent.ProviderLink));
        var runScriptProvider = await _providerActions.GetRunScriptProvider(agent.ProviderLink, token, TimeSpan.FromSeconds(30));
        if (runScriptProvider is not null)
          runScriptProviders.Add(agent.ProviderLinkId, runScriptProvider);
      }
      catch (GetProviderTimeoutException)
      {
        AddIdentificationLog(pending, $"A timeout occurred while fetching the api for provider link {agent.ProviderLink?.Name}. This provider link will not be used.", logType: AgentIdentificationLogType.Verbose);
      }
    });

    await Task.WhenAll(tasks);

    // if the all existing agents are offline, we need the user to manually decide what to do
    if (!runScriptProviders.Any())
    {
      var existingOfflineAgentMsg = $"All agents for existing computer #{computer.Id} are offline.  A manual decision is required. If the agent was simply re-installed, then choose 'Agent re-installed'.  If this is the same device but has been repurposed for a new client, then overwrite the existing computer by choosing 'Wiped'.  If this is a clone or new device, then choose 'Cloned'.";
      AddIdentificationLog(pending, existingOfflineAgentMsg, logType: AgentIdentificationLogType.Error);
      var failure = new AgentIdentificationFailure
      {
        PendingAgentId = pending.Id,
        ComputerId = computer.Id,
        ExistingAgentId = existingAgent.Id,
        Message = existingOfflineAgentMsg,
        RequiresManualResolution = true
      };

      ctx.AgentIdentificationFailures.Add(failure);
      await ctx.SaveChangesAsync(token);

      if (!string.IsNullOrEmpty(pending.OnboardingOptions?.OnboardingCorrelationId))
      {
        _eventEmitter.EmitEvent(new CorrelatedOnboardingAgentIdentificationFailedEvent(
          pending.OnboardingOptions.OnboardingCorrelationId,
          failure));
      }
      return;
    }

    const string msg2 = "Existing computer has online agents - will create new task to try to determine if the existing agents are from the same computer as this pending agent by adding a dedupe marker through the existing agents and reading it from the pending agent.";
    _logger.LogDebug(msg2);
    AddIdentificationLog(pending, msg2);

    var isolatedLogger = _loggerFactory.CreateLogger<PendingAgentResolverActions>();
    var newState = new Dictionary<string, object?>(state);
    using var _1 = isolatedLogger.BeginScope(state);

    string? marker;
    try
    {
      const string dedupeDropMsg = "Dropping dedupe marker on computer through existing agents";
      isolatedLogger.LogDebug(dedupeDropMsg);
      AddIdentificationLog(pending, dedupeDropMsg);
      var script = _powershellLoader.GetPowerShellScript("Set-WindowsDedupeMarker", 60);
      var scriptResult = await _scriptInvoker
      .RunScriptAsync(
        runScriptProviders,
        computer,
        script,
        defaultTimeout: 60,
        token,
        agentConnectionWaitTimeoutSeconds: 180);
      marker = scriptResult.AllStreams.LastOrDefault()?.ToString();
      newState["DedupeMarker"] = marker ?? "";
      const string finMsg = "Finished dropping marker";
      AddIdentificationLog(pending, finMsg);
      isolatedLogger.LogDebug(finMsg);
    }
    catch (Exception ex) when (ex is not (OperationCanceledException or TaskCanceledException))
    {
      const string exMsg = "Error occurred while attempting to run DropDedupeMarker script for agent with id {existingAgentId}. Failed attempt to differentiate existing agent and pending agent.";
      isolatedLogger.LogError(ex, exMsg, existingAgent.Id);
      var exMsgReplaced = exMsg.Replace("{existingAgentId}", existingAgent.Id.ToString());
      AddIdentificationLog(pending, $"{exMsgReplaced}. {ex.Message}", logType: AgentIdentificationLogType.Error);
      var failure = new AgentIdentificationFailure
      {
        PendingAgentId = pending.Id,
        ComputerId = computer.Id,
        ExistingAgentId = existingAgent.Id,
        Message = $"{exMsg} {ex.Message}",
      };
      ctx.AgentIdentificationFailures.Add(failure);
      await ctx.SaveChangesAsync(token);

      if (!string.IsNullOrEmpty(pending.OnboardingOptions?.OnboardingCorrelationId))
      {
        _eventEmitter.EmitEvent(new CorrelatedOnboardingAgentIdentificationFailedEvent(
          pending.OnboardingOptions.OnboardingCorrelationId,
          failure));
      }
      isolatedLogger.LogDebug("Added agent id failure");
      return;
    }

    if (string.IsNullOrEmpty(marker))
    {
      const string noResMsg = "DropDedupeMarker script did not return anything. Failed attempt to differentiate existing agent and pending agent";
      isolatedLogger.LogDebug(noResMsg);
      AddIdentificationLog(pending, noResMsg, logType: AgentIdentificationLogType.Error);
      var failure = new AgentIdentificationFailure
      {
        PendingAgentId = pending.Id,
        ComputerId = computer.Id,
        ExistingAgentId = existingAgent.Id,
        Message = noResMsg,
      };
      ctx.AgentIdentificationFailures.Add(failure);
      await ctx.SaveChangesAsync(token);

      if (!string.IsNullOrEmpty(pending.OnboardingOptions?.OnboardingCorrelationId))
      {
        _eventEmitter.EmitEvent(new CorrelatedOnboardingAgentIdentificationFailedEvent(
          pending.OnboardingOptions.OnboardingCorrelationId,
          failure));
      }
      isolatedLogger.LogDebug("Added agent id failure");
      return;
    }


    var providerLink = ctx.GetProviderLink(pending.ProviderLinkId);

    if (providerLink is null)
    {
      AddIdentificationLog(pending, "Failed to retrieve a provider link for the pending agent", logType: AgentIdentificationLogType.Error);
      return;
    }

    var provider = await _providerActions.GetRunScriptProvider(providerLink, token);

    if (provider is null)
    {
      AddIdentificationLog(pending, "Failed to retrieve a provider for the pending agent", logType: AgentIdentificationLogType.Error);
      return;
    }

    // Then try to read the marker from the pending agent
    string? marker2;
    try
    {
      const string readDedupeMarkerMsg = "Reading dedupe marker from computer through pending agent";
      isolatedLogger.LogDebug(readDedupeMarkerMsg);
      AddIdentificationLog(pending, readDedupeMarkerMsg);
      var script = _powershellLoader.GetPowerShellScript("Get-WindowsDedupeMarker", 60);
      var scriptResult = await _scriptInvoker
      .RunScriptAsync(new Dictionary<int, IRunScriptProvider>()
      {
        { pending.ProviderLinkId, provider }
      },
      pending,
      script,
      defaultTimeout: 60,
      token,
      agentConnectionWaitTimeoutSeconds: 180);
      // ReSharper disable once ConditionalAccessQualifierIsNonNullableAccordingToAPIContract
      marker2 = scriptResult.AllStreams?.LastOrDefault()?.ToString();
      newState["DedupeMarkerRead"] = marker2 ?? "";
      isolatedLogger.LogDebug("Finished reading marker");
    }
    catch (Exception ex) when (ex is not (OperationCanceledException or TaskCanceledException))
    {
      isolatedLogger.LogError(ex,
        "Error occurred while attempting to run ReadDedupeMarker script for " +
        "ProviderAgent#Id={PendingAgentId}. Failed attempt to differentiate existing agent and " +
        "pending agent.",
        pending.Id);
      AddIdentificationLog(pending, $"Error occurred while attempting to run ReadDedupeMarker script for ProviderAgent#Id={pending.Id}. Failed attempt to differentiate existing agent and pending agent.", logType: AgentIdentificationLogType.Error);

      var failure = new AgentIdentificationFailure
      {
        PendingAgentId = pending.Id,
        ComputerId = computer.Id,
        ExistingAgentId = existingAgent.Id,
        Message = $"Error occurred while attempting to run ReadDedupeMarker script for " +
                  $"ProviderAgent#Id={pending.Id}. Failed attempt to differentiate existing " +
                  $"agent and pending agent. {ex.Message}",
      };
      ctx.AgentIdentificationFailures.Add(failure);
      await ctx.SaveChangesAsync(token);

      if (!string.IsNullOrEmpty(pending.OnboardingOptions?.OnboardingCorrelationId))
      {
        _eventEmitter.EmitEvent(new CorrelatedOnboardingAgentIdentificationFailedEvent(
          pending.OnboardingOptions.OnboardingCorrelationId,
          failure));
      }
      isolatedLogger.LogDebug("Added agent id failure");
      return;
    }
    if (marker == marker2)
    {
      const string matchMsg = "Marker read from pending agent was the same as the marker dropped on the computer through the existing agent - the two computers are the same";
      isolatedLogger.LogDebug(matchMsg);
      AddIdentificationLog(pending, matchMsg);
      await ReplaceAgent(pending, existingAgent, token);
      return;
    }

    // This computer is probably a clone of the existing computer
    // Add a new immybot device id to the new computer to differentiate it

    const string noMatchMsg = "Marker read from pending agent was not the same as the marker dropped on the existing agent - the two computers are separate devices";
    isolatedLogger.LogDebug(noMatchMsg);
    AddIdentificationLog(pending, noMatchMsg);

    AddIdentificationLog(pending, "Adding ImmyBot Device Id to device.");
    var newDeviceId = await _computerIdentificationActions.AddImmybotDeviceId(state: newState,
              providerLinkId: pending.ProviderLinkId,
              provider: provider,
              pending: pending,
              existingAgentId: existingAgent.Id,
              computerId: computer.Id,
              onboardingCorrelationId: pending.OnboardingOptions?.OnboardingCorrelationId,
              token: token);

    if (newDeviceId is { } d)
    {
      pending.DeviceDetails.DeviceId = d;
      AddIdentificationLog(pending, $"Successfully added ImmyBot Device Id {d} to device.");
      await CreateComputer(pending, token);
    }
  }

  public async Task ReplaceComputer(ProviderAgent pending, Computer computer, CancellationToken token)
  {
    await KeepBothComputers(pending, computer, null, token, replacingComputer: true);
  }

  private bool IsOnboardingEnabled(ProviderAgent providerAgent, ImmybotDbContext ctx)
  {
    if (providerAgent.IsMemberOfInitialDeviceSync)
    {
      AddIdentificationLog(providerAgent, "The agent is part of the initial device sync for this client. Onboarding is disabled.");
      return false;
    }

    var tenantId = providerAgent.ProviderClient?.LinkedToTenantId ??
       providerAgent.GetIdentificationDetails().LinkedToTenantId ??
       ctx.ProviderClients
         .Where(a => a.ProviderLinkId == providerAgent.ProviderLinkId && a.ExternalClientId == providerAgent.ExternalClientId)
         .Select(a => a.LinkedToTenantId)
         .FirstOrDefault();

    if (!tenantId.HasValue)
    {
      AddIdentificationLog(providerAgent, "The agent is not linked to a tenant. Onboarding is disabled.");
      return false;
    }

    var isGlobalOnboardingEnabled = _cachedPrefs.Value.EnableOnboarding;
    if (!isGlobalOnboardingEnabled)
    {
      AddIdentificationLog(providerAgent, "Onboarding is globally disabled in preferences.");
      return false;
    }

    var tenantOnboardingEnabled = _cachedTenantPrefs.Value.FirstOrDefault(a => a.TenantId == tenantId)?.EnableOnboarding == true;

    if (!tenantOnboardingEnabled)
    {
      AddIdentificationLog(providerAgent, $"Onboarding is disabled in linked tenant #{tenantId} preferences.");
      return false;
    }

    return true;
  }

  /// <summary>
  /// This method handles performing logic that should be taken after a
  /// pending agent is resolved to an existing agent
  /// </summary>
  private async Task HandlePostResolution(
    ProviderAgent pending,
    int computerId,
    Guid deviceId,
    bool isOnboardingEnabled,
    bool allowOnboarding = true)
  {
    if (!string.IsNullOrEmpty(pending.OnboardingOptions?.OnboardingCorrelationId))
    {
      EmitOnboardingCorrelationComputerCreatedEvent(pending.OnboardingOptions.OnboardingCorrelationId, computerId);

      _pendoEventManagement.Enqueue(
        PendoEventManagementService.PendoEvents.ImmyComputerAddedEvent()
      );
    }

    await using var ctx = _ctxFactory();

    // update primary person if present
    if (pending.OnboardingOptions?.PrimaryPersonId is { } primaryPersonId)
    {
      AddIdentificationLog(pending, $"Setting primary person with id {primaryPersonId}");
      await ctx.SetComputerPrimaryPersonId(computerId,
        primaryPersonId,
        new AuditUserDetails(null, null, "Agent Identification Resolver"));
    }

    // update additional persons
    if (pending.OnboardingOptions?.AdditionalPersonIds is { Count: > 0 } personIds)
    {
      AddIdentificationLog(pending, $"Setting additional persons with ids {string.Join(", ", personIds)}");
      ctx.UpdateComputerPersonsForComputer(computerId, personIds);
    }

    if (pending.OnboardingOptions?.Tags is { Count: > 0 } tags)
    {
      AddIdentificationLog(pending, $"Setting tags with ids {string.Join(", ", tags)}");
      await ctx.AddEntityTag<ComputerTag>(tags, new List<int> { computerId });
    }

    AddIdentificationLog(pending, $"Enqueueing required system inventory job");
    _backgroundJobClient.Enqueue<IInventoryTaskJob>(j => j.RunHighPriority(
      computerId,
      new InventoryDeviceCmdPayload(
        deviceId,
        null,
        null,
        true,
        false,
         1),
      CancellationToken.None));

    // only perform onboarding steps if we are allowing it
    // set computer onboarding status if onboarding is enabled
    if (allowOnboarding && isOnboardingEnabled && (pending.GetIdentificationDetails().ExistingComputerHasOtherOnlineAgents != true || pending.OnboardingOptions?.AutomaticallyOnboard is true))
    {
      AddIdentificationLog(pending, $"Setting the computer to need onboarding");
      ctx.SetComputerOnboardingStatus(computerId, ComputerOnboardingStatus.NeedsOnboarding);

      // auto onboard if required
      if (pending.OnboardingOptions?.AutomaticallyOnboard is true)
      {
        AddIdentificationLog(pending, $"Computer should be automatically onboarded. Enqueueing maintenance...");
        var jobArgs = new SessionJobArgs
        {
          ComputerId = computerId,
          InstallWindowsUpdates = true,
          CacheGroupId = Guid.NewGuid(),
          OfflineBehavior = ComputerOfflineMaintenanceSessionBehavior.ApplyOnConnect,
          RebootPreference = pending.OnboardingOptions?.OnboardingSessionRebootPreference ?? default,
          PromptTimeoutAction = pending.OnboardingOptions?.OnboardingSessionPromptTimeoutAction ?? PromptTimeoutAction.Suppress,
          AutoConsentToReboots = pending.OnboardingOptions?.OnboardingSessionAutoConsentToReboots ?? default,
          PromptTimeoutMinutes = pending.OnboardingOptions?.OnboardingSessionPromptTimeoutMinutes ?? 5,
          MaintenanceEmailConfiguration = pending.OnboardingOptions?.OnboardingSessionSendFollowUpEmail == true ? new()
          {
            SendFollowUpEmail = true,
          } : null,
          MaintenanceOnboardingConfiguration = new MaintenanceOnboardingConfiguration { AutomaticOnboarding = true }
        };
        var newSession = _immyServiceJob.EnqueueOnboarding(new EnqueueNewSessionPayload(jobArgs));

        // immyServiceJob#Enqueue sets the session id on the passed-in job args when
        // it creates a new session, so we can use that to know the new session id
        if (!string.IsNullOrEmpty(pending.OnboardingOptions?.OnboardingCorrelationId))
        {
          _eventEmitter.EmitEvent(new CorrelatedOnboardingSessionCreatedEvent(
            pending.OnboardingOptions.OnboardingCorrelationId,
            newSession.Id,
            computerId));
        }
      }
    }
  }

  public async Task ReplaceAgent(ProviderAgent pending, ProviderAgent existingAgent, CancellationToken token)
  {
    await using var ctx = _ctxFactory();
    try
    {

      if (existingAgent.ComputerId is not { } computerId)
      {
        const string notLinkedMsg = "Unable to overwrite existing agent - the agent to overwrite is not linked to a computer";
        _logger.LogError(notLinkedMsg);
        AddIdentificationLog(pending, notLinkedMsg, logType: AgentIdentificationLogType.Error);
        return;
      }

      const string msg = "Overwriting the existing agent on computer {computerId}";
      _logger.LogDebug(msg, computerId);
      AddIdentificationLog(pending, msg.Replace("{computerId}", computerId.ToString()));

      // delete existing agent
      await ctx.ProviderAgents
        .Where(a => a.Id == existingAgent.Id)
        .SoftDeleteAsync("Replace Agent: " + msg.Replace("{computerId}", computerId.ToString()));
      const string deleteMsg = "Deleted existing agent";
      _logger.LogDebug(deleteMsg);
      AddIdentificationLog(pending, deleteMsg);

      if (!string.IsNullOrEmpty(pending.OnboardingOptions?.OnboardingCorrelationId))
      {
        _eventEmitter.EmitEvent(new CorrelatedOnboardingExistingComputerDeterminedEvent(
          pending.OnboardingOptions.OnboardingCorrelationId,
          computerId));
      }

      ctx.AssignAgentToComputer(pending.Id, computerId);

      var isOnboardingEnabled = IsOnboardingEnabled(pending, ctx);

      await HandlePostResolution(pending, computerId, pending.DeviceDetails.DeviceId!.Value, isOnboardingEnabled, allowOnboarding: pending.OnboardingOptions?.AutomaticallyOnboard is true);

      _eventEmitter.EmitEvent(new ReplaceExistingAgentIdentificationResolutionEvent(computerId, existingAgent.Id));

      const string finMsg = "Assigned agent to computer {computerId}";
      AddIdentificationLog(pending, finMsg.Replace("{computerId}", computerId.ToString()));
      _logger.LogDebug(finMsg, computerId);
    }
    catch (DbUpdateConcurrencyException ex)
    {
      const string exMsg = "Failed to overwrite existing agent due to a concurrency issue. {exMessage}";
      AddIdentificationLog(pending, exMsg.Replace("{exMessage}", ex.Message), logType: AgentIdentificationLogType.Error);
      _logger.LogError(ex, exMsg, ex.Message);
    }
  }

  public async Task KeepBothComputers(ProviderAgent pending, Computer computer, ProviderAgent? existingAgent, CancellationToken token, bool replacingComputer = false)
  {
    var state = GetLoggerState(pending);
    using var _ = _logger.BeginScope(state);

    if (replacingComputer)
    {
      _logger.LogDebug(
        "Replacing computer #{computerId}. Deleting old computer and data and then creating a new one.",
        computer.Id.ToString());
      var msg =
        $"Replacing computer #{computer.Id}. Deleting old computer and data and then creating a new one.";
      AddIdentificationLog(pending,
        msg);
      // delete existing
      await _deleteComputerCmd.SoftDelete(new List<int> { computer.Id }, reason: msg);
      AddIdentificationLog(pending, $"Deleted computer #{computer.Id}");
    }
    else
    {
      _logger.LogDebug(
        "Generating a new computer for this agent, separate from existing computer #{computerId}.",
        computer.Id.ToString());
      AddIdentificationLog(pending,
        $"Generating a new computer for this agent, separate from existing computer #{computer.Id}.");
    }

    await using var ctx = _ctxFactory();
    try
    {
      // fetch provider
      var link = ctx.GetProviderLink(pending.ProviderLinkId);
      if (link is null) return;

      var provider = await _providerActions.GetRunScriptProvider(link, token);

      if (provider is null)
      {
        AddIdentificationLog(pending, "Failed to retrieve a provider for the pending agent", logType: AgentIdentificationLogType.Error);
        return;
      }

      AddIdentificationLog(pending, "Adding ImmyBot Device Id to device");

      // retry up to 3 times to add a new immybot device id to the new computer
      var retryPolicy = Policy
        .HandleResult<Guid?>((a) => !a.HasValue)
        .WaitAndRetryAsync(
          3,
          sleepDurationProvider: _ => TimeSpan.FromSeconds(30),
          onRetry: (_, retryNumber, _) =>
          {
            AddIdentificationLog(pending, $"Failed to add ImmyBot Device Id to device. Retrying...", logType: AgentIdentificationLogType.Error);
          });

      var newDeviceIdGuid = await retryPolicy.ExecuteAsync(async () => await _computerIdentificationActions.AddImmybotDeviceId(
        state: state,
        providerLinkId: pending.ProviderLinkId,
        provider: provider,
        pending: pending,
        existingAgentId: existingAgent?.Id,
        computerId: computer.Id,
        onboardingCorrelationId: pending.OnboardingOptions?.OnboardingCorrelationId,
        token: token,
        requiresManualDecision: true));

      if (newDeviceIdGuid is { } newDeviceId)
      {
        const string newMsg = "Successfully added new ImmyBot Device Id";
        _logger.LogDebug(newMsg);
        AddIdentificationLog(pending, newMsg);
        AddIdentificationLog(pending, "Creating new computer");

        bool isOnboardingEnabled = IsOnboardingEnabled(pending, ctx);
        ctx.AssignAgentToNewComputer(
          pending.Id,
          newDeviceId,
          isOnboardingEnabled,
          pending.DeviceDetails.OperatingSystemName ?? string.Empty,
          pending.DeviceDetails.DeviceName ?? string.Empty,
          pending.DeviceDetails.SerialNumber ?? string.Empty,
          pending.DeviceDetails.DomainRole,
          pending.DeviceDetails.IsSandbox,
          primaryPersonId: pending.OnboardingOptions?.PrimaryPersonId,
          isDevLab: pending.OnboardingOptions?.IsDevLab ?? false);

        var newComputer = ctx.GetComputerByDeviceId(newDeviceId);

        if (newComputer is null)
        {
          AddIdentificationLog(pending, $"Failed to retrieve new computer.", AgentIdentificationLogType.Error);
          return;
        }

        newComputer.ChassisTypes = pending.DeviceDetails.ChassisTypes?.ToList();
        newComputer.DomainRole = pending.DeviceDetails.DomainRole;
        await ctx.Computers
          .Where(a => a.Id == newComputer.Id)
          .ExecuteUpdateAsync(u =>
              u.SetProperty(a => a.ChassisTypes, newComputer.ChassisTypes)
                .SetProperty(a => a.DomainRole, newComputer.DomainRole),
            token);

        AddIdentificationLog(pending, $"Successfully assigned agent to new computer #{newComputer.Id}");

        // if we are replacing an existing computer, then update the old computer with the new computer's id as its successor
        if (replacingComputer)
        {
          await ctx.SetComputerSuccessor(computer.Id, newComputer.Id);
        }

        await HandlePostResolution(pending, newComputer.Id, newComputer.DeviceId, isOnboardingEnabled);

        if (existingAgent?.Id is not null)
        {
          if (replacingComputer)
          {
            _eventEmitter.EmitEvent(new ReplaceExistingComputerIdentificationResolutionEvent(computer.Id, existingAgent.Id, newComputer.Id));
          }
          else
          {
            _eventEmitter.EmitEvent(new KeepBothAgentsIdentificationResolutionEvent(computer.Id, existingAgent.Id, newComputer.Id));
          }
        }
      }
      else
      {
        _logger.LogDebug(
          "Failed attempt to generate an ImmybotDeviceID on pending agent in order to differentiate it from existing agent: response from make-device-id script was not a valid guid: '{NewDeviceIdGuid}'",
          newDeviceIdGuid);

        var parseFailedMsg = $"Failed attempt to generate an ImmybotDeviceID on pending agent in order to differentiate it from existing agent: response from make-device-id script was not a valid guid: '{newDeviceIdGuid}'";
        AddIdentificationLog(pending, parseFailedMsg, logType: AgentIdentificationLogType.Error);

        _eventEmitter.EmitEvent(new AgentIdentificationResolutionFailedEvent(
          computer.Id,
          pending.Id,
          parseFailedMsg
        ));
      }
    }
    catch (Exception ex) when (!(ex is OperationCanceledException || ex is TaskCanceledException))
    {
      _logger.LogError(ex,
        "Error occurred while attempting to run MakeImmybotDeviceId script for agent id {PendingId}. Failed attempt to generate an ImmybotDeviceId on pending agent in order to differentiate it from existing agent. A manual decision is now required.",
        pending.Id);

      var exMsg = $"Error occurred while attempting to run MakeImmybotDeviceId script for agent id {pending.Id}. Failed attempt to generate an ImmybotDeviceId on pending agent in order to differentiate it from existing agent. A manual decision is now required.";
      AddIdentificationLog(pending, exMsg, logType: AgentIdentificationLogType.Error);

      var failure = new AgentIdentificationFailure
      {
        PendingAgentId = pending.Id,
        ComputerId = computer.Id,
        ExistingAgentId = existingAgent?.Id,
        Message = $"{exMsg} {ex.Message}",
        RequiresManualResolution = true,
      };

      ctx.AgentIdentificationFailures.Add(failure);
      await ctx.SaveChangesAsync(token);

      if (!string.IsNullOrEmpty(pending.OnboardingOptions?.OnboardingCorrelationId))
      {
        _eventEmitter.EmitEvent(new CorrelatedOnboardingAgentIdentificationFailedEvent(
          pending.OnboardingOptions.OnboardingCorrelationId,
          failure));
      }

      _eventEmitter.EmitEvent(new AgentIdentificationResolutionFailedEvent(
        computer.Id,
        pending.Id,
        $"{exMsg} {ex.Message}"));
    }
  }
}
