using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Lib.DynamicForms;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Domain.Providers.Attributes;
using Immybot.Backend.Providers.Interfaces;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Application.Lib.Providers;

/// <summary>
/// This factory is responsible for generating metadata for a given provider type.
/// It used to be a static method in <see cref="ProviderFactory"/>, but was moved here since it's grown to potentially run async
/// operations using the <see cref="IDynamicFormService"/>.
/// </summary>
internal interface IProviderMetadataFactory
{
  Task<ProviderMetadata> MakeMetadata(Type providerType, CancellationToken cancellationToken, bool ignoreCache = true);
}

/// <inheritdoc />
internal class ProviderMetadataFactory : IProviderMetadataFactory
{
  private readonly IDynamicFormService _dynamicFormService;
  private readonly ILogger<ProviderMetadataFactory> _logger;

  public ProviderMetadataFactory(
    IDynamicFormService dynamicFormService,
    ILogger<ProviderMetadataFactory> logger)
  {
    _dynamicFormService = dynamicFormService;
    _logger = logger;
  }

  public async Task<ProviderMetadata> MakeMetadata(Type providerType, CancellationToken cancellationToken, bool ignoreCache = true)
  {
    if (!providerType.IsDefined(typeof(ProviderAttributeBase)))
    {
      throw new ProviderAttributeMissingException("The provider implementation does not have any custom attribute that inherits from ProviderAttribute");
    }
    if (providerType.GetCustomAttribute<ProviderAttributeBase>() is not { } providerTypeAttr)
    {
      throw new ProviderAttributeMissingException($"The provider implementation does not have the required custom attribute '{typeof(ProviderAttributeBase)}'");
    }

    _logger.LogInformation("Making provider metadata for type {ProviderType}", providerTypeAttr.DisplayName);

    var types = providerType.GetInterfaces();
    var deviceUpdateAttr = Array.Exists(types, i => i == typeof(ISupportsUpdatingAgents))
      ? providerType.GetCustomAttribute<AgentUpdateFormSchemaAttribute>()
      : null;
    var agentGroupMetadatas = types
      .Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(ISupportsDeviceGrouping<>))
      .SelectMany(i => GetAgentGroupMetadatas(providerTypeAttr.ProviderTypeId, i))
      .ToList();
    var clientGroupMetadatas = types
      .Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(ISupportsClientGrouping<>))
      .SelectMany(i => GetClientGroupMetadatas(providerTypeAttr.ProviderTypeId, i))
      .ToList();
    ICollection<SupportedCrossProviderLinkage> crossProviderClientLinkages
      = Array.Exists(types, i => i == typeof(ISupportsCrossProviderClientLinking))
        ? providerType
          .GetCustomAttributes<SupportedCrossProviderClientLinkageAttribute>()
          .Select(a => new SupportedCrossProviderLinkage(a.ProviderTypeId, a.Description))
          .ToList()
        : Array.Empty<SupportedCrossProviderLinkage>();
    ICollection<SupportedCrossProviderLinkage> crossProviderInitializationLinkages
      = Array.Exists(types, i => i == typeof(ISupportsCrossProviderInitialization))
        ? providerType
          .GetCustomAttributes<SupportedCrossProviderInitializationAttribute>()
          .Select(a => new SupportedCrossProviderLinkage(a.ProviderTypeId, a.Description))
          .ToList()
        : Array.Empty<SupportedCrossProviderLinkage>();

    DynamicFormBindResultWithConvertedParameters? configurationForm = providerTypeAttr switch
    {
      DynamicProviderAttribute d => await _dynamicFormService.BindParameters(
        true,
        true,
        null,
        d.ParamBlock,
        d.DatabaseType,
        cancellationToken, null,
        ignoreCache: ignoreCache),
      ProviderAttribute p => _dynamicFormService.BindParameters(p.FormType),
      _ => null
    };

    var source = ProviderTypeSource.BuiltIn;
    if (providerTypeAttr is DynamicProviderAttribute dynamicProviderAttribute)
    {
      source = dynamicProviderAttribute.DatabaseType is DatabaseType.Global ? ProviderTypeSource.Global : ProviderTypeSource.Local;
    }

    var metadata = new ProviderMetadata(
      providerTypeAttr.DocsUrl,
      providerTypeAttr is DynamicProviderAttribute,
      source,
      providerTypeAttr.ProviderTypeId,
      providerType,
      providerTypeAttr.DisplayName,
      providerTypeAttr.Tag,
      configurationForm,
      providerTypeAttr.LogoSrc,
      providerTypeAttr,
      agentGroupMetadatas,
      clientGroupMetadatas,
      deviceUpdateAttr != null,
      deviceUpdateAttr?.FormSchema,
      providerTypeAttr.SupportsScreenShare ? providerTypeAttr.LogoSrc : null, // a little bit of a HACK, but the const base64 that was getting duplicated at each caller was 800kb and bad, so this shares the same one
      CanManage: providerTypeAttr.CanManage,
      ProviderCapabilities: types
        .Where(t => t.GetCustomAttribute<IntegrationCapabilityAttribute>() != null)
        .Select(t => t.Name).Distinct().ToList(),
      SupportedCrossProviderInitializationLinkages: crossProviderInitializationLinkages,
      SupportedCrossProviderClientLinkages: crossProviderClientLinkages);

    return metadata;
  }

  private static List<AgentGroupMetadata> GetAgentGroupMetadatas(Guid providerTypeId, Type type)
  {
    var deviceGroupType = type.GetGenericArguments()[0];
    var deviceGroupAttributes = deviceGroupType.GetCustomAttributes<DeviceGroupAttribute>();
    if (deviceGroupAttributes == null || !deviceGroupAttributes.Any())
    {
      throw new InvalidOperationException($"Device group with type='{type}' does not have the required DeviceGroup class attribute");
    }
    return deviceGroupAttributes
      .Select(deviceGroupAttribute => new AgentGroupMetadata(
        deviceGroupType,
        providerTypeId,
        type,
        deviceGroupAttribute.Id,
        deviceGroupAttribute.DisplayName,
        deviceGroupAttribute.Description,
        deviceGroupAttribute.TargetScope))
      .ToList();
  }

  private static List<ClientGroupMetadata> GetClientGroupMetadatas(Guid providerTypeId, Type type)
  {
    var clientGroupType = type.GetGenericArguments()[0];
    var clientGroupAttributes = clientGroupType.GetCustomAttributes<ClientGroupAttribute>();

    if (clientGroupAttributes == null || !clientGroupAttributes.Any())
    {
      throw new InvalidOperationException($"Client group with type='{type}' does not have the required ClientGroup class attribute");
    }
    return clientGroupAttributes
      .Select(a => new ClientGroupMetadata(
        clientGroupType,
        providerTypeId,
        type,
        a.Id,
        a.DisplayName,
        a.Description))
      .ToList();
  }
}
