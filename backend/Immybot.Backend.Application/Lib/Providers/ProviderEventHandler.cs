using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence;
using Immybot.Backend.Providers.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using static Immybot.Backend.Domain.Helpers.LoggerHelpers;

namespace Immybot.Backend.Application.Lib.Providers;

internal static class ProviderAgentExtensions
{
  /// <summary>
  /// in case the agents we were given contain duplicate entries of link id and agent id,
  /// we need to remove the duplicates before we try to upsert them, because the db will throw a constraint error
  /// causing the entire batch to fail.
  /// </summary>
  /// <param name="agents"></param>
  /// <returns></returns>
  internal static IEnumerable<ProviderAgent> ExcludeDuplicates(this IEnumerable<ProviderAgent> agents)
    => agents
      .GroupBy(d => new { d.ProviderLinkId, d.ExternalAgentId })
      .Select(g => g.First());
}

internal class ProviderEventHandler : IProviderAgentEventHandler
{
  private readonly Func<ImmybotDbContext> _contextFactory;
  private readonly ILoggerFactory _loggerFactory;
  private readonly IProviderLinkDataConsistencyLocker _providerLinkLocker;
  private readonly IDomainEventEmitter _eventEmitter;
  private readonly ICachedSingleton<ProviderLinkNames> _linkNamesCache;
  private readonly IOptionsMonitor<AppSettingsOptions> _appOptsMonitor;
  private AppSettingsOptions AppOpts => _appOptsMonitor.CurrentValue;

  public ProviderEventHandler(
    Func<ImmybotDbContext> contextFactory,
    ILoggerFactory loggerFactory,
    IProviderLinkDataConsistencyLocker providerLinkLocker,
    IDomainEventEmitter eventEmitter,
    ICachedSingleton<ProviderLinkNames> linkNamesCache,
    IOptionsMonitor<AppSettingsOptions> appOptsMonitor)
  {
    _contextFactory = contextFactory;
    _loggerFactory = loggerFactory;
    _providerLinkLocker = providerLinkLocker;
    _eventEmitter = eventEmitter;
    _linkNamesCache = linkNamesCache;
    _appOptsMonitor = appOptsMonitor;
  }

  private ILogger GetLogger() => _loggerFactory.CreateLogger<ProviderEventHandler>();

  /// <summary>
  /// If this method works well, then we should remove devices update and devices created and only use this instead.
  /// We would need to re-structure the agent classes appropriately.
  /// </summary>
  /// <param name="providerLinkId"></param>
  /// <param name="agents"></param>
  /// <param name="providerLinkLockKey"></param>
  /// <param name="token"></param>
  /// <returns></returns>
  public async Task AgentsUpsertedAsync(
    int providerLinkId,
    ICollection<IProviderAgentDetails> agents,
    Guid providerLinkLockKey,
    CancellationToken token)
  {
    var logger = GetLogger();
    using var _ = logger.BeginScope(MakeScope(new { providerLinkId }));
    logger.LogTrace("Received event with {AgentsCount} agents", agents.Count);
    try
    {
      await using var ctx = _contextFactory();
      // Only agents that belong to clients that are linked to tenants can be synced into immy
      // By extension: if a client ever becomes unlinked, its agents should be removed from immy
      var enabledClients = await ctx.GetClientsForProviderLink(providerLinkId)
        .Where(c => c.LinkedToTenantId != null)
        .ToDictionaryAsync(c => c.ExternalClientId, token);

      var existingAgents = await ctx.GetAgentsForProviderLink(providerLinkId)
        .IgnoreQueryFilters()
        .Select(r => $"{r.ExternalClientId}-{r.ExternalAgentId}")
        .ToListAsync(token);

      if (AppOpts.DisableSyncingNewProviderAgents)
        agents = agents
          .Where(d => existingAgents.Contains($"{d.ExternalClientId}-{d.ExternalAgentId}"))
          .ToList();

      var now = DateTime.UtcNow;
      var validDevices = agents
        .Where(c => OperatingSystemHelpers.IsWindowsOperatingSystem(c.DeviceDetails?.OperatingSystemName))
        .Where(d =>
          // only allow devices that have an agent in the db that has
          // been linked to a tenant
          enabledClients.ContainsKey(d.ExternalClientId) &&
          // remove any items from this list that already exist as agents
          // in this provider link (this could happen if, e.g., the device sync job
          // runs while a computer is being onboarded / updated to a new client
          // and the device sync job syncs the "new" device before the onboarding
          // logic updates it)

          // TODO: This seems wrong - we're excluding all agents that already exist in the
          // database but we should be creating or updating them.
          !existingAgents.Contains($"{d.ExternalClientId}-{d.ExternalAgentId}"))
        .Select((d => new ProviderAgent
        {
          ProviderLinkId = providerLinkId,
          ExternalClientId = d.ExternalClientId,
          DateAddedUTC = now,
          ExternalAgentId = d.ExternalAgentId,
          InternalData = d.InternalData,
          IsOnline = d.IsOnline,
          LastUpdatedUTC = d.LastUpdatedUTC,
          DeviceDetails = d.DeviceDetails,
          OnboardingOptions = d.OnboardingOptions,
          IsMemberOfInitialDeviceSync = !enabledClients[d.ExternalClientId].HasCompletedInitialAgentSync,
          SupportsRunningScripts = d.SupportsRunningScripts,
          AgentVersion = d.AgentVersion,
          RequireManualIdentification = d.RequireManualIdentification,
#pragma warning disable CS0618 // Type or member is obsolete
          SupportsOnlineStatus = true
#pragma warning restore CS0618 // Type or member is obsolete
        }))
        .ExcludeDuplicates()
        .ToList();

      // device may exist, so we should perform an upsert
      await using var transaction = await ctx.Database.BeginTransactionAsync(token);
      try
      {
        await ctx.BulkMergeAsync(validDevices, options =>
        {
          options.ColumnInputExpression = c => (new
          {
            c.ProviderLinkId,
            c.ExternalClientId,
            c.ExternalAgentId,
            c.DateAddedUTC,
            c.InternalData,
            c.IsOnline,
            c.LastUpdatedUTC,
            c.DeviceDetails,
            c.OnboardingOptions,
            c.IsMemberOfInitialDeviceSync,
            c.SupportsRunningScripts,
            c.AgentVersion,
            c.RequireManualIdentification,
#pragma warning disable CS0618 // Type or member is obsolete
            c.SupportsOnlineStatus
#pragma warning restore CS0618 // Type or member is obsolete
          });

          options.ColumnPrimaryKeyExpression = c => (new { c.ProviderLinkId, c.ExternalClientId, c.ExternalAgentId });
          // TODO: figure out how to add a callback for created devices so we can trigger a
          // CorrelatedOnboardingPendingAgentCreatedEvent event appropriately
        }, token);
        await transaction.CommitAsync(token);
      }
      catch (Exception ex) when (!(ex is OperationCanceledException))
      {
        await transaction.RollbackAsync(token);
        throw;
      }
    }
    catch (Exception ex) when (!(ex is OperationCanceledException))
    {
      logger.LogError(ex, "Error occurred while processing upsert devices");
      if (ex is InvalidLinkLockKeyException) throw;
    }
  }

  public async Task AgentsCreatedAsync(
    bool providerSupportsListingClients,
    int providerLinkId,
    ICollection<IProviderAgentDetails> agents,
    Guid? providerLinkLockKey,
    CancellationToken token)
  {
    if (AppOpts.DisableSyncingNewProviderAgents) return;
    var logger = GetLogger();
    using var _ = logger.BeginScope(MakeScope(new { providerLinkId }));
    logger.LogTrace("Received event with {AgentsCount} agents", agents.Count);
    try
    {
      if (agents.Count == 0) return;
      if (providerLinkLockKey is not null && !_providerLinkLocker.ValidateLockKey(providerLinkId, providerLinkLockKey.Value))
      {
        throw new InvalidLinkLockKeyException("The provided link lock key is not valid - must provide the lock key returned by the IProviderQueryHandler#AcquireLinkLock method");
      }

      await using var ctx = _contextFactory();
      Dictionary<string, ProviderClient> enabledClients;
      if (!providerSupportsListingClients)
      {
        // Providers that don't support listing clients externally have devices that are mapped
        // straight to immybot tenants. In that case, the agent's "external client id" actually
        // refers to an immybot tenant id, so we should allow the devices to be created even if
        // there's not a provider client for the agent but there is a tenant matching the agent's
        // "external client id"
        var tenantIds = agents.Select(d => Convert.ToInt32(d.ExternalClientId)).ToArray();
        enabledClients = await ctx
          .GetTenantsByIds(tenantIds)
          .ToDictionaryAsync(t => t.Id.ToString(),
            t =>
              new ProviderClient
              {
                HasCompletedInitialAgentSync = true, ExternalClientId = t.Id.ToString(), ExternalClientName = t.Name
              },
            token);
      }
      else
      {
        // Only agents that belong to clients that are linked to tenants can be synced into immy
        // By extension: if a client ever becomes unlinked, its agents should be removed from immy
        enabledClients = await ctx.GetClientsForProviderLink(providerLinkId)
          .Where(c => c.LinkedToTenantId != null)
          .ToDictionaryAsync(c => c.ExternalClientId, token);
      }
      var existingAgents = await ctx.GetAgentsForProviderLink(providerLinkId)
        .IgnoreQueryFilters()
        .Select(r => $"{r.ExternalClientId}-{r.ExternalAgentId}")
        .ToListAsync(token);

      var now = DateTime.UtcNow;
      var validDevices = agents
        .Where(c => OperatingSystemHelpers.IsWindowsOperatingSystem(c.DeviceDetails?.OperatingSystemName))
        .Where(d =>
          // only allow devices that have an agent in the db that has
          // been linked to a tenant
          enabledClients.ContainsKey(d.ExternalClientId) &&
          // remove any items from this list that already exist as agents
          // in this provider link (this could happen if, e.g., the device sync job
          // runs while a computer is being onboarded / updated to a new client
          // and the device sync job syncs the "new" device before the onboarding
          // logic updates it)
          !existingAgents.Contains($"{d.ExternalClientId}-{d.ExternalAgentId}"))
        .Select(d => new ProviderAgent
        {
          ProviderLinkId = providerLinkId,
          ExternalClientId = d.ExternalClientId,
          ExternalAgentId = d.ExternalAgentId,
          DateAddedUTC = now,
          InternalData = d.InternalData,
          IsOnline = d.IsOnline,
          LastUpdatedUTC = d.LastUpdatedUTC,
          AgentVersion = d.AgentVersion,
          DeviceDetails = d.DeviceDetails,
          // Z.EntityFramework complains about null owned references
          OnboardingOptions = d.OnboardingOptions ?? new(),
          IsMemberOfInitialDeviceSync = !enabledClients[d.ExternalClientId].HasCompletedInitialAgentSync,
          SupportsRunningScripts = d.SupportsRunningScripts,
          RequireManualIdentification = d.RequireManualIdentification
        })
        .ExcludeDuplicates()
        .ToList();

      logger.LogInformation("{NumValidDevices} agents are valid and will be saved to the database",
        validDevices.Count);
      if (validDevices.Count == 0)
      {
        var skippedAgents = agents.Except(validDevices).ToList();
        foreach (var skippedAgent in skippedAgents)
        {
          logger.LogWarning(
            "Agent with externalAgentId={ExternalAgentId} under externalClientId={ExternalClientId} was skipped because it was invalid. OS: {OperatingSystemName}",
            skippedAgent.ExternalAgentId,
            skippedAgent.ExternalClientId,
            skippedAgent.DeviceDetails?.OperatingSystemName);
        }

        return;
      }
      ctx.CreateProviderAgents(validDevices);

      var providerLinkName = _linkNamesCache.Value?.GetValueOrDefault(providerLinkId) ??
                             await ctx.ProviderLinks
                               .Where(l => l.Id == providerLinkId)
                               .Select(l => l.Name)
                               .FirstOrDefaultAsync(token) ??
                             string.Empty;

      validDevices.ForEach(d =>
      {
        var client = enabledClients.GetValueOrDefault(d.ExternalClientId);
        if (d.OnboardingOptions?.OnboardingCorrelationId is { Length: > 0 } o)
        {
          _eventEmitter.EmitEvent(
            new CorrelatedOnboardingPendingAgentCreatedEvent(o, d, providerLinkName,
              client?.ExternalClientName));
        }

        var identificationDetails = d.GetIdentificationDetails();
        identificationDetails.LinkedToTenantId = client?.LinkedToTenantId;
        _eventEmitter.EmitEvent(new AgentCreatedEvent(d));
      });
    }
    catch (Exception ex) when (!(ex is OperationCanceledException))
    {
      logger.LogError(ex, "Error occurred while processing new agents");
      if (ex is InvalidLinkLockKeyException) throw;
    }
  }

  public async Task AgentsDeletedAsync(
    int providerLinkId,
    ICollection<(string clientId, string agentId)> clientsAgentIds,
    Guid? providerLinkLockKey,
    CancellationToken token)
  {
    var logger = GetLogger();
    using var _ = logger.BeginScope(MakeScope(new { providerLinkId }));
    logger.LogTrace("Received event with {AgentsCount} agents", clientsAgentIds.Count);
    try
    {
      if (clientsAgentIds.Count == 0) return;
      if (providerLinkLockKey is not null && !_providerLinkLocker.ValidateLockKey(providerLinkId, providerLinkLockKey.Value))
      {
        throw new InvalidLinkLockKeyException("The provided link lock key is not valid - must provide the lock key returned by the IProviderQueryHandler#AcquireLinkLock method");
      }

      await using var ctx = _contextFactory();
      var deletes = clientsAgentIds.Select(d => $"{d.clientId}-{d.agentId}").ToList();

      var agentsToDelete = await ctx.GetAgentsForProviderLink(providerLinkId)
        .IgnoreQueryFilters()
        .Where(c => c.DeletedAt == null)
        .Where(c => deletes.Contains(c.ProviderClient!.ExternalClientId + "-" + c.ExternalAgentId))
        .Select(c => new
        {
          c.Id,
          c.ExternalAgentId,
          c.ExternalClientId,
          c.ProviderLinkId,
          c.ComputerId
        })
        .ToListAsync(token);
      var numAgentsDeleted = await ctx.GetAgentsForProviderLink(providerLinkId)
        .IgnoreQueryFilters()
        .Where(c => deletes.Contains(c.ProviderClient!.ExternalClientId + "-" + c.ExternalAgentId))
        .SoftDeleteAsync("Deleted from integration sync", token);

      foreach (var agent in agentsToDelete)
      {
        _eventEmitter.EmitEvent(new AgentDeletedEvent(agent.Id, agent.ComputerId, providerLinkId, agent.ExternalClientId, agent.ExternalAgentId));
      }

      logger.LogInformation("Deleted {NumAgentsDeleted} devices from the agents table",
        numAgentsDeleted);
    }
    catch (Exception ex) when (!(ex is OperationCanceledException))
    {
      logger.LogError(ex, "Error occurred while processing deleted devices");
      if (ex is InvalidLinkLockKeyException) throw;
    }
  }

  public async Task AgentsUpdatedAsync(
    int providerLinkId,
    ICollection<IProviderAgentDetails> agents,
    Guid? providerLinkLockKey,
    CancellationToken token)
  {
    var logger = GetLogger();
    using var _ = logger.BeginScope(MakeScope(new { providerLinkId }));
    logger.LogTrace("Received event with {AgentsCount} agents", agents.Count);
    try
    {
      if (agents.Count == 0) return;
      if (providerLinkLockKey is not null && !_providerLinkLocker.ValidateLockKey(providerLinkId, providerLinkLockKey.Value))
      {
        throw new InvalidLinkLockKeyException("The provided link lock key is not valid - must provide the lock key returned by the IProviderQueryHandler#AcquireLinkLock method");
      }

      await using var ctx = _contextFactory();
      var ids = agents.Select(d => $"{d.ExternalClientId}-{d.ExternalAgentId}").ToArray();
      var existingAgents = await ctx
        .GetAgentsForProviderLink(providerLinkId)
        .IgnoreQueryFilters()
        .Where(a => ids.Contains(a.ExternalClientId + "-" + a.ExternalAgentId))
        .ToListAsync(token);

      var newAgentClientIds = agents.Select(a => a.ExternalClientId).Distinct().ToList();

      // get clients that are enabled so we can make sure the updated agents actually have
      // existing, linked clients in immybot
      var enabledClients = await ctx.GetClientsForProviderLink(providerLinkId)
        .Where(c => c.LinkedToTenantId != null && newAgentClientIds.Contains(c.ExternalClientId))
        .Select(c => c.ExternalClientId)
        .ToHashSetAsync(cancellationToken: token);

      var agentsToUpdate = new List<IProviderAgentDetails>();
      var agentIdsToDelete = new List<int>();

      // filter non-existent agents and out unlinked agents
      foreach (IProviderAgentDetails newVal in agents)
      {
        var existing = existingAgents
          .Find(c => c.ExternalClientId == newVal.ExternalClientId && c.ExternalAgentId == newVal.ExternalAgentId);
        if (existing == null)
        {
          logger.LogWarning(
            "Received update for non-existing agent with externalAgentId={ExternalAgentId} " +
            "under externalClientId={ExternalClientId}",
            newVal.ExternalAgentId,
            newVal.ExternalClientId);
          continue;
        }

        if (!enabledClients.Contains(newVal.ExternalClientId))
        {
          logger.LogWarning(
            "Agent was updated to a client that hasn't been enabled in immybot - agent will be deleted from immybot");
          agentIdsToDelete.Add(existing.Id);
          continue;
        }

        agentsToUpdate.Add(newVal);
      }

      if (agentsToUpdate.Count != 0)
      {
        await using var transaction = await ctx.Database.BeginTransactionAsync(token);
        try
        {
          await ctx.ProviderAgents.BulkUpdateAsync(agentsToUpdate.Select(a => new ProviderAgent
          {
            DeletedAt = null,
            DeletedReason = null,
            ProviderLinkId = providerLinkId,
            ExternalClientId = a.ExternalClientId,
            ExternalAgentId = a.ExternalAgentId,
            LastUpdatedUTC = a.LastUpdatedUTC,
            AgentVersion = a.AgentVersion,
            SupportsRunningScripts = a.SupportsRunningScripts,
            DeviceDetails = new DeviceDetails
            {
              DeviceName = a.DeviceDetails.DeviceName,
              OperatingSystemName = a.DeviceDetails.OperatingSystemName,
              SerialNumber = a.DeviceDetails.SerialNumber,
              Manufacturer = a.DeviceDetails.Manufacturer,
            },
          }), bulkOperation =>
          {
            bulkOperation.ColumnPrimaryKeyExpression = c => new
            {
              c.ProviderLinkId,
              c.ExternalClientId,
              c.ExternalAgentId
            };
            bulkOperation.ColumnInputExpression = c => new
            {
              c.LastUpdatedUTC,
              c.AgentVersion,
              c.SupportsRunningScripts,
              c.DeviceDetails.DeviceName,
              c.DeviceDetails.OperatingSystemName,
              c.DeviceDetails.SerialNumber,
              c.DeviceDetails.Manufacturer,
              c.DeletedAt,
              c.DeletedReason,
            };
          }, token);
          await transaction.CommitAsync(token);
        }
        catch
        {
          await transaction.RollbackAsync(token);
          throw;
        }
      }

      if (agentIdsToDelete.Count != 0)
      {
        await ctx.ProviderAgents.Where(a => agentIdsToDelete.Contains(a.Id)).DeleteFromQueryAsync(token);
      }
    }
    catch (Exception ex) when (!(ex is OperationCanceledException))
    {
      logger.LogError(ex, "Error occurred while processing updated agents");
      if (ex is InvalidLinkLockKeyException) throw;
    }
  }

  public async Task AgentsConnectedAsync(
    int providerLinkId,
    ICollection<(string clientId, string agentId)> agents,
    Guid? providerLinkLockKey,
    CancellationToken token)
  {
    var logger = GetLogger();
    using var _ = logger.BeginScope(MakeScope(new { providerLinkId }));
    logger.LogTrace("Received event with {AgentsCount} agents", agents.Count);

    try
    {
      if (agents.Count == 0) return;

      if (providerLinkLockKey is not null && !_providerLinkLocker.ValidateLockKey(providerLinkId, providerLinkLockKey.Value))
      {
        throw new InvalidLinkLockKeyException("The provided link lock key is not valid - must provide the lock key returned by the IProviderQueryHandler#AcquireLinkLock method");
      }

      await using var ctx = _contextFactory();

      var connects = agents.Select(d => $"{d.clientId}-{d.agentId}").ToList();

      // fetch existing from db (before updating)
      var existing = (await ctx.GetAgentsForProviderLink(providerLinkId)
        .IgnoreQueryFilters()
        .Where(c => connects.Contains(c.ExternalClientId + "-" + c.ExternalAgentId) && !c.IsOnline)
        .Select(c => new { c.ExternalAgentId, c.ExternalClientId, c.ComputerId, c.SupportsRunningScripts })
        .ToArrayAsync(token))
        .ToHashSet();

      // update offline to online
      var numUpdatedAgents = await ctx.GetAgentsForProviderLink(providerLinkId)
        .IgnoreQueryFilters()
        .Where(c => connects.Contains(c.ExternalClientId + "-" + c.ExternalAgentId) && !c.IsOnline)
        .ExecuteUpdateAsync(a =>
            a.SetProperty(b => b.IsOnline, true)
              .SetProperty(b => b.DeletedAt, (DateTime?)null)
              .SetProperty(b => b.DeletedReason, (string?)null),
          token);

      logger.LogTrace("Set {numUpdatedAgents} agents to 'connected'", numUpdatedAgents);

      foreach (var agent in agents)
      {
        // if the agent is already online then do nothing
        var currentAgent = existing.FirstOrDefault(a => a.ExternalClientId == agent.clientId && a.ExternalAgentId == agent.agentId);
        if (currentAgent is null) continue;

        // emit agent has connected event
        _eventEmitter.EmitEvent(new AgentConnectedEvent(providerLinkId, agent.clientId, agent.agentId));

        if (currentAgent.SupportsRunningScripts)
          _eventEmitter.EmitEvent(new AgentConnectedSupportingScriptExecutionEvent(currentAgent.ComputerId, providerLinkId, agent.clientId, agent.agentId));
      }
    }
    catch (Exception ex) when (!(ex is OperationCanceledException))
    {
      logger.LogError(ex, "Error occurred while processing connected agents");
      if (ex is InvalidLinkLockKeyException) throw;
    }
  }

  public async Task AgentsDisconnectedAsync(
    int providerLinkId,
    ICollection<(string clientId, string agentId)> agentIds,
    Guid? providerLinkLockKey,
    CancellationToken token)
  {
    var logger = GetLogger();
    using var _ = logger.BeginScope(MakeScope(new { providerLinkId }));
    logger.LogTrace("Received event with {AgentsCount} agents", agentIds.Count);

    try
    {
      if (agentIds.Count == 0) return;

      if (providerLinkLockKey is not null && !_providerLinkLocker.ValidateLockKey(providerLinkId, providerLinkLockKey.Value))
      {
        throw new InvalidLinkLockKeyException("The provided link lock key is not valid - must provide the lock key returned by the IProviderQueryHandler#AcquireLinkLock method");
      }

      await using var ctx = _contextFactory();

      var disconnects = agentIds.Select(d => $"{d.clientId}-{d.agentId}").ToList();

      var existing = await ctx.GetAgentsForProviderLink(providerLinkId)
        .IgnoreQueryFilters()
        .Where(c => disconnects.Contains(c.ExternalClientId + "-" + c.ExternalAgentId) && c.IsOnline)
        .Select(c => new { c.ExternalAgentId, c.ExternalClientId, c.ComputerId, c.SupportsRunningScripts })
        .ToListAsync(token);

      var numUpdatedAgents = await ctx.GetAgentsForProviderLink(providerLinkId)
        .IgnoreQueryFilters()
        .Where(c => disconnects.Contains(c.ExternalClientId + "-" + c.ExternalAgentId) && c.IsOnline)
        .ExecuteUpdateAsync(a =>
            a.SetProperty(b => b.IsOnline, false)
              .SetProperty(b => b.DeletedAt, (DateTime?)null)
              .SetProperty(b => b.DeletedReason, (string?)null),
          token);

      logger.LogTrace("Set {numUpdatedAgents} agents to 'disconnected'", numUpdatedAgents);

      foreach (var agent in agentIds)
      {
        // if the agent is already offline then do nothing
        var currentAgent =
          existing.Find(a => a.ExternalClientId == agent.clientId && a.ExternalAgentId == agent.agentId);
        if (currentAgent is null) continue;

        // emit agent has disconnected event
        _eventEmitter.EmitEvent(new AgentDisconnectedEvent(providerLinkId, agent.clientId, agent.agentId));
      }
    }
    catch (Exception ex) when (!(ex is OperationCanceledException))
    {
      logger.LogError(ex, "Error occurred while processing disconnected agents");
      if (ex is InvalidLinkLockKeyException) throw;
    }
  }

  public void ProviderClientsAgentsSyncing(
    int providerLinkId,
    ICollection<string> knownClientIds,
    CancellationToken token)
  {
    token.ThrowIfCancellationRequested();
  }

  public async Task ProviderClientsAgentsDoneSyncingAsync(
    int providerLinkId,
    ICollection<string> knownClientIds,
    CancellationToken token)
  {
    // This client just had a successful sync. If the client has
    // never been synced before, we need to update its status to indicate
    // it has had a successful device sync, so that future
    // synced agents can get onboarded appropriately
    await using var ctx = _contextFactory();
    token.ThrowIfCancellationRequested();
    await ctx.GetClientsForProviderLink(providerLinkId)
      .Where(c =>
        knownClientIds.Contains(c.ExternalClientId) &&
        !c.HasCompletedInitialAgentSync)
      .ExecuteUpdateAsync(a => a.SetProperty(b => b.HasCompletedInitialAgentSync, true), token);
  }
}
