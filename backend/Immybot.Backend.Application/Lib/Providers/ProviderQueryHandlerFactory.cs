using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Configuration.Provider;
using System.Diagnostics;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence;
using Immybot.Backend.Providers.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using static Immybot.Backend.Domain.Helpers.LoggerHelpers;

namespace Immybot.Backend.Application.Lib.Providers;

internal class ProviderQueryHandlerFactory
  : IProviderQueryHandlerFactory<IProviderQueryHandler>
{
  private readonly ConcurrentDictionary<QueryHandler, SemaphoreSlim> _queryHandlerAcquiredLocks = new();

  private readonly Func<ImmybotDbContext> _contextFactory;
  private readonly ILoggerFactory _loggerFactory;
  private readonly IProviderLinkDataConsistencyLocker _providerLinkLocker;
  private readonly IOptions<AppSettingsOptions> _appSettings;

  public ProviderQueryHandlerFactory(
    Func<ImmybotDbContext> contextFactory,
    IProviderLinkDataConsistencyLocker providerLinkLocker,
    ILoggerFactory loggerFactory,
    IOptions<AppSettingsOptions> appSettings)
  {
    _contextFactory = contextFactory;
    _providerLinkLocker = providerLinkLocker;
    _loggerFactory = loggerFactory;
    _appSettings = appSettings;
  }

  private static readonly ConcurrentDictionary<QueryHandler, ProviderQueryHandlerFactory> _queryHandlerParents = new();

  internal class QueryHandler : IProviderQueryHandler
  {
    internal readonly int _providerLinkId;
    private readonly ILogger<QueryHandler> _logger;
    internal readonly Type _providerType;
    private readonly Guid _lockKey;

    private readonly CancellationTokenSource _cancellationTokenSource;

    private DateTime? _timedOutDateUtc;

    public Guid LockKey => ThrowIfTimedOut(_lockKey);

    private T ThrowIfTimedOut<T>(T? input, [CallerMemberName] string memberName = "")
    {
      if (input is null)
        throw new InvalidOperationException("Provider query handler method has not fully initialized.");
      if (_cancellationTokenSource.IsCancellationRequested)
      {
        var now = DateTime.UtcNow;
        var diff = (now - (_timedOutDateUtc ?? DateTime.UtcNow)).TotalSeconds;
        _logger.LogError("Query handler is already timed out. Method '{method}' was called {diff} seconds after it timed out. ", memberName, diff);
        throw new QueryHandlerTimedOutException("Query handler lock timed out and is not longer valid.");
      }
      return input;
    }

    public Func<CancellationToken, ICollection<IProviderAgentDetails>> GetKnownAgents { get => ThrowIfTimedOut(_getKnownAgents); internal set { _getKnownAgents = value; } }

    public Func<DoesAgentExistArgs, CancellationToken, Task<bool>> DoesAgentExist
    {
      get => ThrowIfTimedOut(_doesAgentExist);
      internal set { _doesAgentExist = value; }
    }

    public Func<CancellationToken, ICollection<IProviderClientDetails>> GetEnabledClients { get => ThrowIfTimedOut(_getEnabledClients); internal set { _getEnabledClients = value; } }

    public Func<CancellationToken, ICollection<(string clientId, string deviceId, bool isOnline, DateTime lastUpdatedUTC)>> GetKnownAgentStatuses { get => ThrowIfTimedOut(_getKnownAgentStatuses); internal set { _getKnownAgentStatuses = value; } }
    public Func<CancellationToken, ICollection<string>> GetEnabledClientIds { get => ThrowIfTimedOut(_getEnabledClientIds); internal set { _getEnabledClientIds = value; } }

    public Func<CancellationToken, JsonElement?> GetProviderData { get => ThrowIfTimedOut(_getProviderData); internal set { _getProviderData = value; } }

    public Func<CancellationToken, Task<List<string>>> GetExcludedCapabilities
    {
      get => ThrowIfTimedOut(_getExcludedCapabilities);
      internal set { _getExcludedCapabilities = value; }
    }
    public Func<CancellationToken, JsonElement> GetProviderTypeFormData { get => ThrowIfTimedOut(_getProviderTypeFormData); internal set { _getProviderTypeFormData = value; } }
    public Action<JsonElement?, CancellationToken> UpdateProviderData { get => ThrowIfTimedOut(_updateProviderData); internal set { _updateProviderData = value; } }

    public QueryHandler(Guid lockKey, Type providerType, int providerLinkId, ILogger<QueryHandler> logger, IOptions<AppSettingsOptions> appSettings)
    {
      _lockKey = lockKey;
      _providerType = providerType;
      _providerLinkId = providerLinkId;
      _logger = logger;

      var stackTrace = appSettings.Value.EnableProviderQueryHandlerEnhancedTimeoutErrorLogging
        ? EnhancedStackTrace.Current() // this guy is very expensive
        : null;

      _cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(60));
      _cancellationTokenSource.Token.Register(() =>
      {
        _timedOutDateUtc = DateTime.UtcNow;
        _logger.LogError("Query handler timed out after 60 seconds with stack trace: {stackTrace}", stackTrace);
        ReleaseQueryHandlerLocks(this);
      });
    }

    private bool disposedValue;
    private Func<CancellationToken, ICollection<IProviderAgentDetails>>? _getKnownAgents;
    private Func<DoesAgentExistArgs, CancellationToken, Task<bool>>? _doesAgentExist;
    private Func<CancellationToken, ICollection<IProviderClientDetails>>? _getEnabledClients;
    private Func<CancellationToken, ICollection<(string clientId, string deviceId, bool isOnline, DateTime lastUpdatedUTC)>>? _getKnownAgentStatuses;
    private Func<CancellationToken, ICollection<string>>? _getEnabledClientIds;
    private Func<CancellationToken, JsonElement?>? _getProviderData;
    private Func<CancellationToken, Task<List<string>>>? _getExcludedCapabilities;
    private Func<CancellationToken, JsonElement>? _getProviderTypeFormData;
    private Action<JsonElement?, CancellationToken>? _updateProviderData;

    protected virtual void Dispose(bool disposing)
    {
      if (!disposedValue)
      {
        if (disposing)
        {
          // free managed
          _cancellationTokenSource.Dispose();
        }
        // free unmanaged
        ReleaseQueryHandlerLocks(this);
        disposedValue = true;
      }
    }

    ~QueryHandler()
    {
      // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
      Dispose(disposing: false);
    }

    public void Dispose()
    {
      // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
      Dispose(disposing: true);
      GC.SuppressFinalize(this);
    }
  }
#pragma warning disable S3398
  private static void ReleaseQueryHandlerLocks(QueryHandler qh)
#pragma warning restore S3398
  {
    if (_queryHandlerParents.Remove(qh, out var factory) && factory._queryHandlerAcquiredLocks.Remove(qh, out var sem))
    {
      factory._providerLinkLocker.RemoveLockKey(qh._providerLinkId);
      sem.Release();
    }
  }

  public Func<CancellationToken, IProviderQueryHandler> MakeFactory(
    int providerLinkId)
  {
    return (token) => MakeQueryHandler(typeof(IProvider), providerLinkId, token);
  }

  /// <summary>
  /// Creates a new QueryHandler that can be passed to provider implementations
  /// and provider jobs to allow those jobs to access data from the ImmyBot database
  /// pertaining to their link
  /// </summary>
  /// <param name="providerLinkId"></param>
  /// <returns></returns>
  private QueryHandler MakeQueryHandler(
    Type providerType,
    int providerLinkId,
    CancellationToken token)
  {
    var logger = _loggerFactory.CreateLogger<QueryHandler>();
    var sem = _providerLinkLocker.GetProviderLinkLock(providerLinkId);
    sem.Wait(token);

    _providerLinkLocker.RemoveLockKey(providerLinkId);
    var lockKey = _providerLinkLocker.ManufactureLockKey(providerLinkId);
    var q = new QueryHandler(lockKey, providerType, providerLinkId, logger, _appSettings);
    _queryHandlerParents.TryAdd(q, this);
    _queryHandlerAcquiredLocks.TryAdd(q, sem);

    q.GetProviderData = (token) =>
    {
      token.ThrowIfCancellationRequested();
      using var ctx = _contextFactory();
      var link = ctx.GetProviderLinkInternalData(providerLinkId);
      if (link == null) throw new ProviderException("No provider link found for provided id: " + providerLinkId);
      return link.InternalData;
    };

    q.GetExcludedCapabilities = async (cancellationToken) =>
    {
      token.ThrowIfCancellationRequested();
      await using var ctx = _contextFactory();
      var excludedCapabilities = await ctx.ProviderLinks.AsNoTracking().Where(a => a.Id == providerLinkId)
        .Select(l => l.ExcludedCapabilities).FirstOrDefaultAsync(cancellationToken);
      return excludedCapabilities ?? [];
    };

    q.GetProviderTypeFormData = (token) =>
    {
      token.ThrowIfCancellationRequested();
      using var ctx = _contextFactory();
      var link = ctx.GetProviderLink(providerLinkId);
      if (link == null) throw new ProviderException("No provider link found for provided id: " + providerLinkId);
      return link.ProviderTypeFormData;
    };

    q.UpdateProviderData = (data, token) =>
    {
      token.ThrowIfCancellationRequested();
      using var ctx = _contextFactory();
      var link = ctx.GetProviderLinkInternalData(providerLinkId);
      if (link == null) throw new ProviderException("No provider link found for provided id: " + providerLinkId);
      var entry = ctx.Entry(link);
      entry.State = EntityState.Unchanged;
      entry.CurrentValues.SetValues(new { InternalData = data });
      ctx.SaveChanges();
    };
    q.DoesAgentExist = async (doesAgentExistArgs, token) =>
    {
      token.ThrowIfCancellationRequested();
      using var _ = logger.BeginScope(MakeScope(new { providerLinkId }));
      await using var ctx = _contextFactory();
      return await ctx.GetAgentsForProviderLink(providerLinkId)
        .IgnoreQueryFilters()
        .AnyAsync(a =>
            a.ExternalAgentId == doesAgentExistArgs.ExternalAgentId &&
            a.ExternalClientId == doesAgentExistArgs.ExternalClientId,
          token);
    };
    q.GetKnownAgents = (token) =>
    {
      token.ThrowIfCancellationRequested();
      using var _ = logger.BeginScope(MakeScope(new { providerLinkId }));
      using var ctx = _contextFactory();
      var agents = ctx.GetAgentsForProviderLink(providerLinkId).IgnoreQueryFilters();
      return ((IEnumerable<IProviderAgentDetails>)agents).ToList();
    };
    q.GetKnownAgentStatuses = (token) =>
    {
      token.ThrowIfCancellationRequested();
      using var _ = logger.BeginScope(MakeScope(new { providerLinkId }));
      using var ctx = _contextFactory();
      var devices = ctx.GetAgentsForProviderLink(providerLinkId)
        .IgnoreQueryFilters()
        .Select(d => new { d.ExternalClientId, d.ExternalAgentId, d.IsOnline, d.LastUpdatedUTC })
        .ToList();
      return devices
        .Select(d => (d.ExternalClientId, d.ExternalAgentId, d.IsOnline, d.LastUpdatedUTC))
        .ToList();
    };
    q.GetEnabledClients = (token) =>
    {
      token.ThrowIfCancellationRequested();
      using var ctx = _contextFactory();
      return (
        (IEnumerable<IProviderClientDetails>)
        ctx.GetClientsForProviderLink(providerLinkId)
          .Where(c => c.LinkedToTenantId != null)
      ).ToList();
    };
    q.GetEnabledClientIds = (token) =>
    {
      token.ThrowIfCancellationRequested();
      using var ctx = _contextFactory();
      return ctx.GetClientsForProviderLink(providerLinkId)
        .Where(c => c.LinkedToTenantId != null) // only ones that have been linked to a Tenant
        .Select(c => c.ExternalClientId)
        .ToList();
    };

    return q;
  }
}
