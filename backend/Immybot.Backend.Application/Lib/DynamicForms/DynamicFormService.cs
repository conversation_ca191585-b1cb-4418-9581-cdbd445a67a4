using System.Management.Automation;
using System.Management.Automation.Language;
using System.Management.Automation.Runspaces;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Application.Lib.MetaScripts.Attributes;
using Immybot.Backend.Application.Lib.MetaScripts.Modules;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Shared.Extensions;
using Immybot.Shared.Primitives;
using Immybot.Shared.Telemetry;
using Microsoft.PowerShell.Commands;
using Microsoft.VisualStudio.Threading;
using Microsoft.Windows.PowerShell.ScriptAnalyzer.Extensions;
using Namotion.Reflection;

namespace Immybot.Backend.Application.Lib.DynamicForms;

internal class DynamicFormService(
  IMetascriptRunspaceServer metascriptRunspaceServer,
  ICommandInfoConverter commandInfoConverter,
  IMetascriptMessageHandler messageHandler) : IDynamicFormService
{
  public DynamicFormBindResultWithConvertedParameters BindParameters(Type formType, Dictionary<string, object?>? specifiedParameters = default)
  {
    // get show-command info object
    const string getParametersFromTypeCommandName = "Get-ParametersFromType";
    var bindErrors = new Dictionary<string, string>();

    var iss = InitialSessionState.Create();
    iss.Types.Add(new SessionStateTypeEntry(new TypeData(typeof(LiteralString)), false));
    iss.Commands.Add(new SessionStateCmdletEntry("Get-Command", typeof(GetCommandCommand), null));
    iss.Commands.Add(new SessionStateCmdletEntry(getParametersFromTypeCommandName, typeof(GetParametersFromType), null));
    iss.Commands.Add(new SessionStateCmdletEntry("Get-UnboundParameters", typeof(GetUnboundParametersCommand), null));
    iss.LanguageMode = PSLanguageMode.ConstrainedLanguage;

    using var runspace = MetascriptRunspaceExtensions.CreateAndOpenRunspace(iss,
      threadOptions: PSThreadOptions.UseCurrentThread); // UseCurrentThread since we're calling powershell.Invoke() synchronously
    using var powershell = PowerShell.Create(runspace);

    powershell.Runspace.SessionStateProxy.SetVariable("InputObject", formType);

    powershell.AddCommand("Get-Command").AddParameter("Name", getParametersFromTypeCommandName);
    var result = powershell.Invoke();
    var psBindResult = result.LastOrDefault();

    if (psBindResult?.BaseObject is not CommandInfo commandInfo) throw new Exception("Command Info cannot be null");

    // bind parameters
    powershell
      .AddCommand("Get-UnboundParameters")
      .AddParameter("InputObject", Activator.CreateInstance(formType));
    if (specifiedParameters is not null)
      powershell.AddParameters(specifiedParameters);
    try
    {
      powershell.Invoke();
    }
    catch (ParameterBindingException ex) // Includes ParameterBindingValidationException
    {
      PopulateBindErrorsFromExceptionErrorId(ex.Message, ex.ErrorId, bindErrors);
    }

    var parameterSets = new JoinableTaskContext().Factory.Run(
      async () => await commandInfoConverter.GetParameterSets(
        parameterSets: commandInfo.ParameterSets,
        scriptBlockAst: null,
        runContext: null,
        scriptType: DatabaseType.Local,
        CancellationToken.None));

    // set default value from class properties
    var instance = Activator.CreateInstance((formType));
    foreach (var commandParameterInfo in parameterSets.SelectMany(a => a.Parameters))
    {
      var defaultValue = instance.TryGetPropertyValue<object>(commandParameterInfo.Name);
      commandParameterInfo.DefaultValue = defaultValue;
    }

    var showCommandInfo = new ShowCommandInfo(string.Empty, parameterSets);
    return new DynamicFormBindResultWithConvertedParameters(bindErrors, showCommandInfo, ConvertedParameters: specifiedParameters ?? [], "__AllParameterSets");
  }

  public async Task<DynamicFormBindResultWithConvertedParameters> BindParameters(
    bool canAccessMspResources,
    bool canAccessParentTenant,
    IRunContext? runContext,
    string script,
    DatabaseType scriptType,
    CancellationToken cancellationToken,
    Dictionary<string, ParameterValue>? specifiedParameters = null,
    bool ignoreCache = false)
  {
    return await BindParameters(canAccessMspResources,
      canAccessParentTenant,
      script,
      cancellationToken,
      runContext,
      scriptType,
      specifiedParameters);
  }

  private async Task<DynamicFormBindResultWithConvertedParameters> BindParameters(
    bool canAccessMspResources,
    bool canAccessParentTenant,
    string script,
    CancellationToken cancellationToken,
    IRunContext? runContext = null,
    DatabaseType databaseType = DatabaseType.Local,
    Dictionary<string, ParameterValue>? specifiedParameters = null)
  {
    var inertScript = GetInertScript(script);
    Guid terminalId = runContext?.Args.MetascriptInvoker.TerminalId ?? Guid.Empty;

    using var activity = Telemetry.StartActivity(ActivityType.Script,
      $"{nameof(DynamicFormService)}.{nameof(BindParameters)}",
      new()
      {
        { "script.body", inertScript },
        { "script.type", databaseType },
        { "script.tenant.id", runContext?.TenantId },
        { "script.tenant.name", runContext?.TenantName },
        { "script.computer.id", runContext?.Args.Computer?.Id },
        { "script.terminal.id", terminalId },
        { "script.run_context.type", runContext?.GetType().Name }
      });

    // cache based on hash of script / script type / tenant / and specified parameters
    DynamicFormBindResultWithConvertedParameters? returnValue = null;
    var inertScriptBlock = ScriptBlock.Create(inertScript);
    var inertScriptBlockAst = (inertScriptBlock.Ast as ScriptBlockAst);

    Dictionary<string, string> bindErrors = new();
    Dictionary<string, object?>? unwrappedParameters = specifiedParameters?
      .Where(a => a.Value?.Value != null)
      .ToDictionary(o => o.Key, p => (object?)p.Value?.Value);

    var formScript = new Script
    {
      Name = "Run-InertScript",
      Action = inertScript,
      ScriptType = databaseType,
      ScriptExecutionContext = ScriptExecutionContext.CloudScript,
      Parameters = unwrappedParameters ?? []
    };

    // Call SetRunContextParameters to ensure variables are added to the script. These variables are expected
    // to be available from within a dynamicparam block
    formScript = await formScript.SetRunContextParameters(runContext, null, cancellationToken);

    var uiHandler = new BasicCustomPSHostUIHandler(
      runContext?.Args.MetascriptMessageHandler ?? messageHandler.Mutate(x => x.SetTerminalId(terminalId)),
      terminalId
    );

    await using var runspaceRental = await metascriptRunspaceServer.BorrowAsync(new(
      Script: formScript,
      CanAccessMspResources: canAccessMspResources,
      CanAccessParentTenant: canAccessParentTenant,
      TenantId: runContext?.TenantId,
      ScriptOutputCorrelationId: terminalId,
      AgainstComputer: runContext?.Args.Computer,
      RunContext: runContext,
      UIHandler: uiHandler
    ), cancellationToken);

    using var powershell = PowerShell.Create(runspaceRental.Object.Runspace);
    var functionInfo = new FunctionInfo("MyFunc", inertScriptBlock, powershell.Runspace.ExecutionContext);

    // The following code is based off CommandInfo.GetMergedCommandParameterMetadata (Called from Get-Command -ArgumentList)
    // Similar code is found in PseudoParameterBinder.PrepareCommandElements (Called by tabexpansion2 for code completion) and CmdletParameterBinderController.BindCommandLineParameters (The real implementation)
    // We can't use CommandInfo.GetMergedCommandParameterMetadata directly as it disregards the winning parameter set which we use in our frontend
    // We also can't use PseudoParameterBinder.PrepareCommandElements as it expects a string like from a terminal, so we would need to build out "MyFunc -param1 value1 -param2 value2" which will get hairy for non-primitive types
    // We also can't use CmdletParameterBinderController.BindCommandLineParameters as it expects a CommandRuntime.Pipeline which we don't have
    // By instantiating the CommandProcessor ourselves, we maintain access to the CmdletParameterBinderController which has the winning parameter set
    var commandProcessor = new CommandProcessor(functionInfo, powershell.Runspace.ExecutionContext, useLocalScope: true, fromScriptFile: false, functionInfo.ScriptBlock.SessionStateInternal ?? powershell.Runspace.ExecutionContext.EngineSessionState);

    try
    {
      ParameterBinderController.AddArgumentsToCommandProcessor(commandProcessor, [CreatePSBoundParametersDictionary(unwrappedParameters)]);
      powershell.Runspace.ExecutionContext.CurrentCommandProcessor = commandProcessor; // Needed for 39/60 tests, seemingly any test involving dynamicparams

      commandProcessor.SetCurrentScopeToExecutionScope(); // Needed for BindParameters_ShouldAllowNestedDynamicParameters
      commandProcessor.CmdletParameterBinderController.BindCommandLineParameters(commandProcessor.arguments);
    }
    catch (ParameterBindingException ex)
    {
      // handle missing mandatory parameters manually and emitting an error message of "Required"
      if (ex.ErrorId == "MissingMandatoryParameter")
      {
        if (!commandProcessor.CmdletParameterBinderController.HandleUnboundMandatoryParameters(
              out var missingParameters))
        {
          foreach (var parameter in missingParameters)
          {
            bindErrors.TryAdd(parameter.Parameter.Name, "Required");
          }
        }
      }
      else
      {
        if (ex.InnerException is CmdletInvocationException { InnerException: ParameterBindingValidationException parameterBindingValidationException })
        {
          ex = parameterBindingValidationException;
        }
        var errMsg = ex.InnerException?.Message ?? ex.Message;
        uiHandler.HandlePsHostEvent(new ExceptionPsHostEvent(ex, errMsg));
        var paramName = string.IsNullOrEmpty(ex.ParameterName) ? ex.ErrorId : ex.ParameterName.Trim();
        bindErrors.TryAdd(paramName, errMsg);
      }
    }
    finally
    {
      commandProcessor.RestorePreviousScope();
    }
    var boundParameters = commandProcessor.CmdletParameterBinderController.CommandLineParameters.GetValueToBindToPSBoundParameters() as PSBoundParametersDictionary;
    var commandParameterSetInfos = CommandInfo.GetParameterMetadata(functionInfo.CommandMetadata, commandProcessor.CmdletParameterBinderController.BindableParameters);
    var parameterSetName = commandProcessor.CmdletParameterBinderController.CurrentParameterSetName;
    ParameterSet[] flattenedParameterSets = await commandInfoConverter.GetParameterSets( // Flattens parameter attributes into easy to use objects for frontend. This is essentially recreating Get-Command -ShowCommandInfo does to make the output useful to Show-Command
        commandParameterSetInfos,
        inertScriptBlockAst,
        runContext: runContext,
        scriptType: databaseType,
        cancellationToken: cancellationToken,
        specifiedParameters: unwrappedParameters);
    // Get default values for any parameters not explicitly bound
    // Using the name boundArguments as the arguments actually contain the values, not the parameters (At least, that's where you find the values in CmdletParameterBinderController)
    var boundArguments = new Dictionary<string, object?>();
    if (functionInfo.ParameterSets is not null && Array.Find(flattenedParameterSets, set => set.Name == parameterSetName) is { } winningParameterSetFlattened)
    {
      foreach (var flattenedParameter in winningParameterSetFlattened.Parameters)
      {
        if (boundParameters?.TryGetValue(flattenedParameter.Name, out var value) ?? false)
        {
          boundArguments.Add(flattenedParameter.Name, value);
        }
        else
        {
          // Parameter not bound, try to get default value
          boundArguments.Add(flattenedParameter.Name, flattenedParameter.DefaultValue);
        }
      }
    }
    if (parameterSetName == "__AllParameterSets")
      parameterSetName = string.Empty;
    var showCommandInfo = new ShowCommandInfo(inertScript,
      flattenedParameterSets);
    returnValue = new(
        BindErrors: bindErrors,
        ShowCommandInfo: showCommandInfo,
        ParameterSetName: parameterSetName,
        ConvertedParameters: boundArguments
        );
    return returnValue;
  }

  public Task<DynamicFormBindResultWithConvertedParameters> BindParameters(
    bool canAccessMspResources,
    bool canAccessParentTenant,
    IRunContext? runContext,
    ParamBlockAst? paramBlockAst,
    NamedBlockAst? dynamicParamBlockAst,
    DatabaseType databaseType,
    CancellationToken cancellationToken,
    Dictionary<string, ParameterValue>? specifiedParameters = null,
    bool ignoreCache = false)
  {
    var script = GetInertScript(paramBlockAst, dynamicParamBlockAst);
    return BindParameters(canAccessMspResources,
      canAccessParentTenant,
      script,
      cancellationToken,
      runContext,
      databaseType,
      specifiedParameters);
  }

  /// <inheritdoc />
  public async Task<Dictionary<string, MediaIdentifier?>> GetMediaParameters(IActionRunContext runContext, string paramBlock, DatabaseType scriptType, CancellationToken cancellationToken)
  {
    var assignment = await runContext.GetTargetAssignment();
    var task = await runContext.GetMaintenanceTask();
    if (assignment is null || task is null) return [];

    var bind = await BindParameters(runContext.CanAccessMspResources(), runContext.CanAccessParentTenant(), runContext, paramBlock, scriptType, cancellationToken);

    var mediaParameters = new Dictionary<string, MediaIdentifier?>();

    if (bind.ShowCommandInfo.ParameterSets is { } parameterSets)
    {
      foreach (var parameterSet in parameterSets)
      {
        foreach (var parameter in parameterSet.Parameters)
        {
          if (parameter.ParameterType.Name == "Media")
          {
            var specifiedMedia =
              bind.ConvertedParameters.FirstOrDefault(a => a.Key == parameter.Name).Value as MediaIdentifier;
            mediaParameters.TryAdd(parameter.Name, specifiedMedia ?? parameter.DefaultValue as MediaIdentifier);
          }
        }
      }
    }
    return mediaParameters;
  }

  private static PSBoundParametersDictionary CreatePSBoundParametersDictionary(IDictionary<string, object?>? specifiedParameters)
  {
    PSBoundParametersDictionary psBoundParameterDictionary = new PSBoundParametersDictionary();
    if (specifiedParameters is not null)
    {
      foreach (var parameter in specifiedParameters)
      {
        psBoundParameterDictionary.Add(parameter.Key, parameter.Value);
      }
    }
    return psBoundParameterDictionary;
  }

  [Cmdlet(VerbsCommon.Get, "ParametersFromType")]
  private sealed class GetParametersFromType : PSCmdlet, IDynamicParameters
  {
    public object? GetDynamicParameters()
    {
      var inputObject = this.DemandVariableValue<Type>("InputObject");
      return Activator.CreateInstance(inputObject);
    }
  }

  private static void PopulateBindErrorsFromExceptionErrorId(string? exceptionMessage, string? errorId, Dictionary<string, string> bindErrors)
  {
    switch (errorId)
    {
      case "AmbiguousParameterSet":
        bindErrors.TryAdd("AmbiguousParameterSet", exceptionMessage ?? "");
        break;
      case "ParameterArgumentValidationError":
        if (!string.IsNullOrEmpty(exceptionMessage))
        {
          var errorParts = exceptionMessage.Replace("Cannot validate argument on parameter ", "").Split(("\'"));
          bindErrors.TryAdd(errorParts[1], errorParts[2].Trim('.'));
        }
        break;
      case "MissingMandatoryParameter":
        // thrown from PowerShell in CmdletParameterBinderController.cs:2860, 2958, 2979
        if (!string.IsNullOrEmpty(exceptionMessage))
        {
          var missingParameters = exceptionMessage.TrimEnd('.').Split(": ").LastOrDefault()?.Split(" ").ToList();
          missingParameters?.ForEach(field =>
          {
            bindErrors.TryAdd(field, $"Required");
          });
        }
        break;
      default:
        bindErrors.TryAdd("UncaughtException", exceptionMessage ?? "");
        break;
    }
  }

  /// <summary>
  /// Returns a script containing only the param block, dynamic param block, and optionally the CmdletBinding attribute.
  /// Removes all logic from the script. The body of the script can be replaced with logic that returns the bound values of each parameter.
  /// </summary>
  /// <param name="script"></param>
  /// <returns></returns>
  private static string GetInertScript(string script)
  {
    return GetInertScript(ScriptBlock.Create(script)) ?? string.Empty;
  }

  private static string? GetInertScript(ScriptBlock scriptBlock)
  {
    if (scriptBlock.Ast is ScriptBlockAst scriptBlockAst)
    {
      return GetInertScript(scriptBlockAst.ParamBlock, scriptBlockAst.DynamicParamBlock);
    }
    return null;
  }

  private static string GetInertScript(ParamBlockAst? paramBlockAst, NamedBlockAst? dynamicParamBlockAst)
  {
    var paramBlockString = paramBlockAst?.Extent?.ToString();
    var dynamicParamBlockString = dynamicParamBlockAst?.Extent?.ToString();
    var cmdletBindingAttribute = paramBlockAst?.GetCmdletBindingAttributeAst()?.Extent.ToString();

    var returnValue = $"{cmdletBindingAttribute}\n{paramBlockString}\n{dynamicParamBlockString}";

    return !string.IsNullOrEmpty(dynamicParamBlockString)
        ? $"{returnValue}\nprocess {{ }}"
        : returnValue;
  }
}
