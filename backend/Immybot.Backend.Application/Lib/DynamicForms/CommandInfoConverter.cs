using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Management.Automation;
using System.Management.Automation.Internal;
using System.Management.Automation.Language;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.MetaScripts.Attributes;
using Immybot.Backend.Application.Maintenance;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Shared.PowerShell.Attributes;
using Immybot.Shared.Primitives.Attributes;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Application.Lib.DynamicForms;
public class CommandInfoConverter : ICommandInfoConverter
{

  /// <summary>
  /// Common powershell parameters that we don't want to consider when binding
  /// </summary>
  private static readonly string[] _commonParameters = typeof(CommonParameters).GetProperties().Select(p => p.Name).ToArray();
  private readonly AzureActiveDirectoryAuthOptions _azAdAuthOpts;

  public CommandInfoConverter(
    IOptions<AzureActiveDirectoryAuthOptions> azAdAuthOpts)
  {
    _azAdAuthOpts = azAdAuthOpts.Value;
  }

  /// <inheritdoc />
  public async Task<ShowCommandInfo> ConvertToShowCommandInfo(
    CommandInfo cmdInfo,
    IRunContext? runContext,
    string script,
    DatabaseType scriptType,
    CancellationToken cancellationToken,
    Dictionary<string, object?>? specifiedParameters = null,
    bool runDropdownScripts = true)
  {
    var scriptBlock = ScriptBlock.Create(script);
    var scriptBlockAst = scriptBlock.Ast as ScriptBlockAst;
    return new ShowCommandInfo(cmdInfo.Definition, await GetParameterSets(
      cmdInfo.ParameterSets,
      scriptBlockAst,
      runContext,
      scriptType,
      cancellationToken,
      specifiedParameters: specifiedParameters,
      runDropdownScripts: runDropdownScripts));
  }

  public async Task<ParameterSet[]> GetParameterSets(
    ICollection<CommandParameterSetInfo>? parameterSets,
    ScriptBlockAst? scriptBlockAst,
    IRunContext? runContext,
    DatabaseType scriptType,
    CancellationToken cancellationToken,
    Dictionary<string, object?>? specifiedParameters = null,
    bool runDropdownScripts = true)
  {
    List<ParameterSet> returnParameterSets = new List<ParameterSet>(parameterSets?.Count ?? 0);

    foreach (CommandParameterSetInfo parameterSetInfo in parameterSets ?? [])
    {
      var parameterSet = new ParameterSet
      {
        Name = parameterSetInfo.Name,
        IsDefault = parameterSetInfo.IsDefault,
        Parameters = await GetParameterInfo(
          parameterSetInfo.Parameters,
          scriptBlockAst,
          runContext,
          scriptType,
          cancellationToken,
          specifiedParameters: specifiedParameters,
          runDropdownScripts: runDropdownScripts)
      };
      returnParameterSets.Add(parameterSet);
    }

    return returnParameterSets.ToArray();
  }

  private async Task<Parameter[]> GetParameterInfo(
    ReadOnlyCollection<CommandParameterInfo> parameters,
    ScriptBlockAst? scriptBlockAst,
    IRunContext? runContext,
    DatabaseType scriptType,
    CancellationToken cancellationToken,
    Dictionary<string, object?>? specifiedParameters = null,
    bool runDropdownScripts = true)
  {
    List<Parameter> parameterObjs = new List<Parameter>(parameters.Count);
    foreach (CommandParameterInfo parameter in parameters)
    {
      // skip common parameters
      if (_commonParameters.Contains(parameter.Name)) continue;
      var parameterObj = await HandleParameter(scriptBlockAst, runContext, scriptType, specifiedParameters, runDropdownScripts, parameter, cancellationToken);

      parameterObjs.Add(parameterObj);
    }

    return parameterObjs.ToArray();
  }

  public async Task<Parameter> HandleParameter(ScriptBlockAst? scriptBlockAst,
    IRunContext? runContext,
    DatabaseType scriptType,
    Dictionary<string, object?>? specifiedParameters,
    bool runDropdownScripts,
    CommandParameterInfo parameter,
    CancellationToken cancellationToken)
  {
    Parameter parameterObj = new Parameter
    {
      Name = parameter.Name,
      IsMandatory = parameter.IsMandatory,
      ValueFromPipeline = parameter.ValueFromPipeline,
      Position = parameter.Position,
      ParameterType = GetParameterType(parameter.ParameterType),
    };
    HandleAllowNullAttribute(parameter, parameterObj);
    HandleValidateSetAttribute(parameter, parameterObj);
    HandleParameterAttribute(parameter, parameterObj);
    HandleHelpText(parameter, parameterObj);
    HandleMediaAttribute(parameter, parameterObj);
    HandlePasswordAttribute(parameter, parameterObj);
    HandlePersonAttribute(parameter, parameterObj);
    HandleConsentAttribute(parameter, parameterObj);
    HandleNameAttribute(parameter, parameterObj);
    var dropdownAttribute = await HandleDropdownAttribute(
      parameter,
      parameterObj,
      runContext,
      specifiedParameters,
      scriptType,
      cancellationToken);

    // Set the default value of the parameter object
    SetParameterDefaultValue(parameterObj, parameter, dropdownAttribute, scriptBlockAst);
    return parameterObj;
  }

  private static void HandlePersonAttribute(CommandParameterInfo parameter, Parameter parameterObj)
  {
    // person attribute
    var personDropdownAttribute = parameter.Attributes.OfType<PersonAttribute>().LastOrDefault();
    if (personDropdownAttribute is not null)
    {
      parameterObj.ParameterType.FullName = "Person";
    }
  }

  private static void HandleNameAttribute(CommandParameterInfo parameter, Parameter parameterObj)
  {
    // friendly name attribute
    var nameAttribute = parameter.Attributes.OfType<DisplayNameAttribute>().LastOrDefault();
    if (nameAttribute is not null)
    {
      parameterObj.DisplayName = nameAttribute.Name;
    }
  }

  private static void HandlePasswordAttribute(CommandParameterInfo parameter, Parameter parameterObj)
  {
    // password attribute
    var passwordAttribute = parameter.Attributes.OfType<PasswordAttribute>().LastOrDefault();
    if (passwordAttribute is not null)
    {
      parameterObj.ParameterType.FullName = "Password";
      parameterObj.IsValueStripped = passwordAttribute.StripValue;
    }
  }

  private void HandleConsentAttribute(CommandParameterInfo parameter, Parameter parameterObj)
  {
    // consent attribute
    var consentAttribute = parameter.Attributes.OfType<OauthConsentAttribute>().LastOrDefault();
    if (consentAttribute is null) return;
    parameterObj.OauthConsentParameterData = new OauthConsentData(
      consentAttribute.AuthorizationEndpoint,
      consentAttribute.TokenEndpoint,
      consentAttribute.Resource,
      consentAttribute.Scope,
      consentAttribute.ClientId ?? _azAdAuthOpts.ClientId,
      consentAttribute.GetExtraQueryParameterStrings());
    parameterObj.ParameterType.FullName = "OauthConsent";
  }

  private static void HandleMediaAttribute(CommandParameterInfo parameter, Parameter parameterObj)
  {
    // media attribute
    var mediaAttribute = parameter.Attributes.OfType<MediaAttribute>().LastOrDefault();
    if (mediaAttribute is not null)
    {
      parameterObj.ParameterType.FullName = "Media";

      // check for default values
      var defaultMediaId = mediaAttribute.DefaultMediaId;
      var defaultMediaType = mediaAttribute.DefaultMediaType;

      if (defaultMediaId.HasValue && defaultMediaType.HasValue)
      {
        parameterObj.DefaultValue = new MediaIdentifier(defaultMediaId.Value, defaultMediaType.Value);
      }
    }
  }

  private static void HandleHelpText(CommandParameterInfo parameter, Parameter parameterObj)
  {
    // HelpText attribute
    var helpTextAttribute = parameter.Attributes.OfType<HelpTextAttribute>().LastOrDefault();
    if (helpTextAttribute is not null)
    {
      parameterObj.ShowRawHelpText = helpTextAttribute.Raw;
      parameterObj.OnlyForHelpText = true;
    }
  }

  private static void HandleParameterAttribute(CommandParameterInfo parameter, Parameter parameterObj)
  {
    // parameter attribute
    var parameterAttribute = parameter.Attributes.OfType<ParameterAttribute>().LastOrDefault();
    parameterObj.Hidden = parameterAttribute?.DontShow ?? false;
    parameterObj.HelpText = parameterAttribute?.HelpMessage;
  }

  private static void HandleValidateSetAttribute(CommandParameterInfo parameter, Parameter parameterObj)
  {
    // validate set attribute
    bool hasParameterSet = false;
    IList<string> validValues = new List<string>();
    var validateSetAttribute = parameter.Attributes.OfType<ValidateSetAttribute>().LastOrDefault();
    if (validateSetAttribute != null)
    {
      hasParameterSet = true;
      validValues = validateSetAttribute.ValidValues;
    }

    parameterObj.HasParameterSet = hasParameterSet;
    parameterObj.ValidParamSetValues = validValues.ToArray();
  }

  private static void HandleAllowNullAttribute(CommandParameterInfo parameter, Parameter parameterObj)
  {
    parameterObj.AllowNull = parameter.Attributes.OfType<AllowNullAttribute>().Any();
  }

  private static Task<DropdownAttribute?> HandleDropdownAttribute(
    CommandParameterInfo parameter,
    Parameter parameterObj,
    IRunContext? runContext,
    Dictionary<string, object?>? specifiedParameters,
    DatabaseType databaseType,
    CancellationToken cancellationToken
    )
  {
    // dropdown attribute - custom attribute that is used to provide a dynamic dropdown of key values
    // which will resolve to a hydrated object in the powershell script at runtime
    var dropdownAttribute = parameter.Attributes.OfType<DropdownAttribute>().LastOrDefault();
    if (dropdownAttribute is null) return Task.FromResult<DropdownAttribute?>(null);
    parameterObj.HasDropdownParameterSet = true;
    parameterObj.ShowDropdownAsRadioButtons = dropdownAttribute.ShowAsRadioButtons;
    parameterObj.ParameterType.IsArray = dropdownAttribute.MultiSelect;
    parameterObj.ValidDropdownValues = dropdownAttribute.GetValidDropdownValues(runContext, specifiedParameters, databaseType, cancellationToken).ToList();

    return Task.FromResult<DropdownAttribute?>(dropdownAttribute);
  }

  private static void SetParameterDefaultValue(
    Parameter parameterObj,
    CommandParameterInfo parameter,
    DropdownAttribute? dropdownAttribute,
    ScriptBlockAst? scriptBlockAst)
  {
    // default value
    if (parameterObj.ParameterType.Name == "Media")
    {
      // media default value is already set
    }
    // for dynamic parameter default values, the default value may be a ps object for simple types such as boolean, int32, string.
    // In those cases, we want to convert the ps object to its base type
    else if (parameter.IsDynamic)
    {
      SetDynamicParameterDefaultValue(parameterObj, parameter, dropdownAttribute);
    }
    else
    {
      SetStaticParameterDefaultValue(parameterObj, parameter, scriptBlockAst);
    }
  }

  private static void SetStaticParameterDefaultValue(Parameter parameterObj, CommandParameterInfo parameter, ScriptBlockAst? scriptBlockAst)
  {
    var paramAst = scriptBlockAst?.ParamBlock.Parameters.FirstOrDefault(a => a.Name.VariablePath.UserPath == parameter.Name);
    if (paramAst?.DefaultValue is HashtableAst hashtableAst)
    {
      parameterObj.DefaultValue =
        new Hashtable(hashtableAst.KeyValuePairs.ToDictionary(a => a.Item1.SafeGetValue(),
          a => a.Item2.SafeGetValue()));
    }
    else if (paramAst?.DefaultValue is { } defaultValue)
    {
      parameterObj.DefaultValue = defaultValue.SafeGetValue();
    }
  }

  private static void SetDynamicParameterDefaultValue(Parameter parameterObj, CommandParameterInfo parameter, DropdownAttribute? dropdownAttribute)
  {
    var defaultValueAttribute = parameter.Attributes.OfType<DefaultValueAttribute>().LastOrDefault();
    if (defaultValueAttribute?.DefaultValue is not { } defaultValue) return;

    if (dropdownAttribute is not null)
    {
      if (dropdownAttribute.MultiSelect)
      {
        SetDynamicParameterDropdownMultiSelectDefaultValue(parameterObj, dropdownAttribute, defaultValue);
      }
      else
      {
        SetDynamicParameterDropdownDefaultValue(parameterObj, dropdownAttribute, defaultValueAttribute, defaultValue);
      }
    }
    else if (defaultValueAttribute.DefaultValue is Hashtable hashtable)
    {
      parameterObj.DefaultValue = hashtable
        .Cast<DictionaryEntry>()
        .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }
    else if (parameter.ParameterType.Name == "String")
    {
      parameterObj.DefaultValue = defaultValueAttribute.DefaultValue.ToString();
    }
    else if (parameter.ParameterType.Name == "Int32")
    {

      if (defaultValueAttribute.DefaultValue is PSObject psObj)
      {
        parameterObj.DefaultValue = Convert.ToInt32(psObj.BaseObject);
      }
      else
      {
        parameterObj.DefaultValue = Convert.ToInt32(defaultValueAttribute.DefaultValue);
      }
    }
    else if (parameter.ParameterType.Name == "Boolean")
    {
      if (defaultValueAttribute.DefaultValue is PSObject psObj)
      {
        parameterObj.DefaultValue = Convert.ToBoolean(psObj.BaseObject);
      }
      else
      {
        parameterObj.DefaultValue = Convert.ToBoolean(defaultValueAttribute.DefaultValue);
      }
    }
    else if (parameter.ParameterType.Name == "Uri")
    {
      SetDynamicParameterUriDefaultValue(parameterObj, defaultValueAttribute);
    }
    else
    {
      parameterObj.DefaultValue = defaultValueAttribute.DefaultValue;
    }
  }

  private static void SetDynamicParameterUriDefaultValue(Parameter parameterObj, DefaultValueAttribute? defaultValueAttribute)
  {
    object? uriObject;
    Uri? uriValue = null;
    if (defaultValueAttribute?.DefaultValue is PSObject psObj)
    {
      uriObject = psObj.BaseObject;
    }
    else
    {
      uriObject = defaultValueAttribute?.DefaultValue;
    }

    if (uriObject is Uri uri)
    {
      uriValue = uri;
    }
    else
    {
      var uriString = uriObject?.ToString();
      if (!string.IsNullOrEmpty(uriString))
      {
        uriValue = new Uri(uriString);
      }
    }

    parameterObj.DefaultValue = uriValue;
  }

  private static void SetDynamicParameterDropdownDefaultValue(Parameter parameterObj, DropdownAttribute dropdownAttribute, DefaultValueAttribute defaultValueAttribute, object defaultValue)
  {
    string? defaultValueKey = null;
    // we need to determine how to identify the default value.
    if (!string.IsNullOrEmpty(dropdownAttribute.IdPropertyName))
    {
      if (defaultValue is Hashtable ht)
      {
        var htCaseInsensitive = new Hashtable(ht, StringComparer.OrdinalIgnoreCase);
        defaultValueKey = htCaseInsensitive[dropdownAttribute.IdPropertyName]?.ToString();
      }
      else
      {
        var idPropertyNameLower = dropdownAttribute.IdPropertyName.ToLower();
        var psObj = new PSObject(defaultValue);
        var idProperty = psObj.Members.FirstOrDefault(a => a.Name.ToLower() == idPropertyNameLower);
        if (idProperty is not null)
        {
          defaultValueKey = idProperty.Value.ToString();
        }
      }
    }
    else
    {
      defaultValueKey = defaultValueAttribute.DefaultValue.ToString();
    }
    var dropdownValue = parameterObj.ValidDropdownValues.Find(a => a.Value == defaultValueKey);
    parameterObj.DefaultValue = dropdownValue?.Value;
    parameterObj.DefaultValueText = dropdownValue?.Text;
  }

  private static void SetDynamicParameterDropdownMultiSelectDefaultValue(Parameter parameterObj, DropdownAttribute dropdownAttribute, object defaultValue)
  {
    var defaultValueKeys = new List<string>();
    // we need to determine how to identify the default value.
    if (!string.IsNullOrEmpty(dropdownAttribute.IdPropertyName))
    {
      if (defaultValue is Hashtable[] hts)
      {
        foreach (var ht in hts)
        {
          var htCaseInsensitive = new Hashtable(ht, StringComparer.OrdinalIgnoreCase);
          var key = htCaseInsensitive[dropdownAttribute.IdPropertyName]?.ToString();
          if (key is not null) defaultValueKeys.Add(key);
        }
      }
      else if (defaultValue is object[] defaultValues)
      {
        foreach (var df in defaultValues)
        {
          var idPropertyNameLower = dropdownAttribute.IdPropertyName.ToLower();
          var psObj = new PSObject(df);
          var idProperty = psObj.Members.FirstOrDefault(a => a.Name.ToLower() == idPropertyNameLower);
          if (idProperty?.Value?.ToString() is { } v)
          {
            defaultValueKeys.Add(v);
          }
        }
      }
    }
    else if (defaultValue is object[] defaultValues)
    {
      foreach (var df in defaultValues)
      {
        if (df.ToString() is { } v)
          defaultValueKeys.Add(v);
      }
    }
    else
    {
      if (defaultValue.ToString() is { } v)
        defaultValueKeys.Add(v);
    }

    var dfList = new List<ParameterDropdownTextValue>();
    foreach (var key in defaultValueKeys)
    {
      var dropdownValue = parameterObj.ValidDropdownValues.Find(a => a.Value == key);
      if (dropdownValue is not null)
      {
        dfList.Add(dropdownValue);
      }
      parameterObj.DefaultValue = dfList.Select(a => a.Value).ToArray();
      parameterObj.DefaultValueText = string.Join(", ", dfList.Select(a => a.Text));
    }
  }

  public static ParameterType GetParameterType(Type parameterType)
  {
    var returnParameterType = new ParameterType();
    bool isEnum = parameterType.IsEnum;
    bool isArray = parameterType.IsArray;

    returnParameterType.FullName = parameterType.FullName ?? "";
    returnParameterType.IsEnum = isEnum;
    returnParameterType.IsArray = isArray;

    ArrayList enumValues = (isEnum) ?
        new ArrayList(Enum.GetValues(parameterType)) : [];
    returnParameterType.EnumValues = enumValues;

    bool hasFlagAttribute = (isArray) && ((parameterType.GetCustomAttributes(typeof(FlagsAttribute), true)).Length > 0);

    returnParameterType.HasFlagAttribute = hasFlagAttribute;

    // Recurse into array elements.
    object? elementType = (isArray) ?
        GetParameterType(parameterType.GetElementType()!) : null;
    returnParameterType.ElementType = elementType!;

    bool implementsDictionary = (!isEnum && !isArray && (parameterType.IsAssignableFrom(typeof(IDictionary))));
    returnParameterType.ImplementsDictionary = implementsDictionary;

    return returnParameterType;
  }
}
