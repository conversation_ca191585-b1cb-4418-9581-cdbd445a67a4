using System;
using System.Collections.Generic;
using System.Linq;
using System.Management.Automation;
using System.Management.Automation.Language;
using System.Reflection;
using System.Reflection.Emit;
using System.Text.Json;
using System.Threading;
using DotNext;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Lib.MetaScripts;
using Immybot.Backend.Application.Lib.Providers;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Domain.Providers.Attributes;
using Immybot.Backend.Providers.Interfaces;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.VisualStudio.Threading;
using NuGet.Versioning;
using Sieve.Extensions;
using MethodAttributes = System.Reflection.MethodAttributes;
using PropertyAttributes = System.Reflection.PropertyAttributes;
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable UnusedAutoPropertyAccessor.Global
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace Immybot.Backend.Application.DynamicProviders;

[Cmdlet(VerbsCommon.Get, "DynamicIntegrationCapability")]
internal class GetDynamicIntegrationCapabilityCommand : ServiceScopePSCmdlet, IDynamicParameters
{
  [Parameter()]
  [ValidateSet(typeof(DynamicProviderCapabilitiesSet))]
  public string Interface { get; set; }
  public object GetDynamicParameters()
  {
    var parameterDictionary = new RuntimeDefinedParameterDictionary();
    if (string.IsNullOrEmpty(Interface))
      return parameterDictionary;
    var requiredMethods = DynamicProviderRegistrationFactory.GetRequiredMethods(Interface);
    if (requiredMethods is null) return parameterDictionary;

    foreach (var name in requiredMethods.Select(a => a.Name))
    {
      parameterDictionary.Add(name, new RuntimeDefinedParameter(name, typeof(ScriptBlock), null));
    }
    return parameterDictionary;
  }

  protected override void ProcessRecord()
  {
    WriteObject(new DynamicProviderCapabilitiesSet().GetValidValues(), true);
  }
}
[Cmdlet(VerbsCommon.Add, "DynamicIntegrationCapability")]
internal class AddDynamicIntegrationCapabilityCommand : ServiceScopePSCmdlet, IDynamicParameters
{
  [Parameter(Mandatory = true, ValueFromPipeline = true)]
  public IDynamicProviderRegistration Registration { get; set; }
  [Parameter(Mandatory = true)]
  [ValidateSet(typeof(DynamicProviderCapabilitiesSet))]
  public string Interface { get; set; }

  public object GetDynamicParameters()
  {
    var parameterDictionary = new RuntimeDefinedParameterDictionary();
    if (string.IsNullOrEmpty(Interface)) return parameterDictionary;
    type = DynamicProviderRegistrationFactory.GetSupportingInterfaceTypeByName(Interface);
    if (type is null) return parameterDictionary;
    if (type.Equals(typeof(ISupportsClientGrouping<>)))
    {
      // ISupportsClientGrouping is a generic interface whose implementing type
      // must be closed with a type that implements IClientGroup
      // The class that implements IClientGroup must have the ClientGroupAttribute
      // The ClientGroupAttribute requires a Guid, TargetScope, string, and string

      var properties = typeof(ClientGroupAttribute).GetProperties();
      foreach (var param in properties)
      {
        // Skip Id as we will generate it
        if (param.Name == nameof(ClientGroupAttribute.Id))
          continue;
        if (param.Name == nameof(ClientGroupAttribute.TypeId))
          continue;
        // Add remaining ClientGroupAttribute parameters as dynamic parameters
        parameterDictionary.Add(param.Name,
          new RuntimeDefinedParameter(param.Name,
            param.PropertyType,
            [
              new ParameterAttribute() { Mandatory = !param.PropertyType.IsNullable() }
            ]
          )
        );
      }
    }

    var requiredMethods = DynamicProviderRegistrationFactory.GetRequiredMethods(Interface);
    if (requiredMethods is null) return parameterDictionary;

    foreach (var methodInfo in requiredMethods)
    {
      var name = methodInfo.Name;
      var returnType = methodInfo.ReturnType;

      // Check if method returns Optional<T> or Task<Optional<T>>
      var isOptional = returnType.IsOptionalReturnType(out _);

      parameterDictionary.Add(name,
        new(name,
          typeof(ScriptBlock),
          [
            new ParameterAttribute() { Mandatory = !isOptional, HelpMessage = "The script block to execute" },
            new ArgumentCompleterAttribute(typeof(MethodScriptCompleter))
          ]));
    }
    return parameterDictionary;
  }

  private Type? type;
  private static int counter;
  private static string DynamicClassAssemblyName = "ImmyBot Runtime Types";
  internal static Type CreateGroupType(string typeName, string groupId, string displayName, string description)
  {
    var assemblyName = new AssemblyName(DynamicClassAssemblyName)
    {
      // We could generate a unique name, but a unique version works too.
      Version = new Version(1, 0, 0, Interlocked.Increment(ref counter))
    };
    var assembly = AssemblyBuilder.DefineDynamicAssembly(assemblyName,
        AssemblyBuilderAccess.Run, new List<CustomAttributeBuilder>());
    var module = assembly.DefineDynamicModule(DynamicClassAssemblyName);
    var typeBuilder = module.DefineType(typeName, System.Reflection.TypeAttributes.Public | System.Reflection.TypeAttributes.Class);
    var attributeConstructorInfo = typeof(ClientGroupAttribute).GetConstructor([typeof(string)])!;
    typeBuilder.SetCustomAttribute(new CustomAttributeBuilder(
      con: attributeConstructorInfo,
      constructorArgs: [groupId],
      namedProperties: [typeof(ClientGroupAttribute).GetProperty("DisplayName")!, typeof(ClientGroupAttribute).GetProperty("Description")!],
      propertyValues: [displayName, description]
      ));
    typeBuilder.AddInterfaceImplementation(typeof(IClientGroup));
    typeBuilder.DefineDefaultConstructor(MethodAttributes.Public);
    var constructorBuilder = typeBuilder.DefineConstructor(MethodAttributes.Public | MethodAttributes.SpecialName | MethodAttributes.RTSpecialName, CallingConventions.Standard, [typeof(string), typeof(string)]);
    var ctorIl = constructorBuilder.GetILGenerator();
    var i = 1;
    foreach (var property in typeof(IClientGroup).GetProperties())
    {
      // Generate private backing field
      var fieldBuilder = typeBuilder.DefineField("_" + property.Name, property.PropertyType, FieldAttributes.Private);
      var propertyBuilder = typeBuilder.DefineProperty(property.Name, PropertyAttributes.None, property.PropertyType, null);
      var getterMethodName = "get_" + property.Name;
      var getPropMthdBldr = typeBuilder.DefineMethod(getterMethodName,
                MethodAttributes.Public | MethodAttributes.SpecialName | MethodAttributes.Virtual | MethodAttributes.Final | MethodAttributes.NewSlot | MethodAttributes.HideBySig, property.PropertyType,
                Type.EmptyTypes);
      typeBuilder.DefineMethodOverride(getPropMthdBldr, typeof(IClientGroup).GetMethod(getterMethodName)!);
      var getIl = getPropMthdBldr.GetILGenerator();
      // Implement constructor that maps arguments to backing fields
      ctorIl.Emit(OpCodes.Ldarg_0);
      ctorIl.Emit(OpCodes.Ldarg, i++);
      ctorIl.Emit(OpCodes.Stfld, fieldBuilder);
      // Implement getter to return backing field value
      getIl.Emit(OpCodes.Ldarg_0);
      getIl.Emit(OpCodes.Ldfld, fieldBuilder);
      getIl.Emit(OpCodes.Ret);
      propertyBuilder.SetGetMethod(getPropMthdBldr);
    }
    ctorIl.Emit(OpCodes.Ret);
    return typeBuilder.CreateType();
  }
  protected override void EndProcessing()
  {
    if (type is null) return;
    string interfaceId = string.Empty;
    if (type.Equals(typeof(ISupportsClientGrouping<>)))
    {
      // Here we will have the values required to construct
      // our new class and the required ClientGroupAttribute
      var PSBoundParameters = MyInvocation.BoundParameters;
      interfaceId = Guid.NewGuid().ToString();
      var clientGroupType = CreateGroupType(
        typeName: (string)PSBoundParameters["DisplayName"],
        groupId: interfaceId,
        displayName: (string)PSBoundParameters["DisplayName"],
        description: (string)PSBoundParameters["Description"]
      );
      var closedType = type.MakeGenericType(clientGroupType);

      // We need to store the closed type in the dynamic provider store
      // so it can be used to create instances of that type in New-ClientGroup
      // DynamicProviderInterceptor will recognize that the method is on a Generic type
      // and use the groupId to find the appropriate method
      // New-ClientGroup is passed the Type to construct via the DynamicProviderStore
      var dynamicProviderStore = this.ServiceScope.ServiceProvider.GetService(typeof(IDynamicProviderStore)) as IDynamicProviderStore;
      if (dynamicProviderStore is null)
        throw new InvalidOperationException("Unable to retrieve instance of IDynamicProviderStore");
      var ht = dynamicProviderStore.GetState("ClientGroupTypes");
      var groupTypeKey = interfaceId;
      if (ht.ContainsKey(groupTypeKey))
        ht[groupTypeKey] = clientGroupType;
      else
        ht.Add(groupTypeKey, clientGroupType);
      type = closedType;
    }
    Registration.AddInterfaceToProxy(type);
    var requiredMethods = type.GetMethods();

    foreach (var method in requiredMethods)
    {
      // Only add method scripts for parameters that were actually provided
      // This allows Optional methods to be omitted during registration when not provided
      if (MyInvocation.BoundParameters.ContainsKey(method.Name))
      {
        var scriptBlock = (ScriptBlock)MyInvocation.BoundParameters[method.Name];
        var methodName = DynamicProviderInterceptor.GetInterfaceMethodName(method);
        Registration.AddMethodToProxy(methodName, scriptBlock);
      }
    }
  }
}

[Cmdlet(VerbsCommon.New, "DynamicIntegration")]

internal class NewDynamicIntegrationCommand : ServiceScopePSCmdlet
{
  [ArgumentCompleter(typeof(InitScriptArgumentCompleter))]
  [Parameter(Mandatory = true)] public ScriptBlock Init { get; set; }

  [ArgumentCompleter(typeof(HealthCheckScriptArgumentCompleter))]
  [Parameter(Mandatory = true)] public ScriptBlock HealthCheck { get; set; }

  protected override void ProcessRecord()
  {
    ScriptBlockAst scriptBlockAst = (ScriptBlockAst)Init.Ast;
    if (!this.TryGetVariableValue(SessionStateEntries.DynamicIntegrationTypePropertiesVariableEntry, out var properties))
      throw new DynamicProviderException("Failed to retrieve dynamic integration type properties");

    var terminalId = this.TryGetVariableValueNullable(SessionStateEntries.TerminalIdVariableEntry);

    var factory = TryGetService<IDynamicProviderRegistrationFactory>();
    var registration = factory.Create(
      properties.ProviderTypeId,
      properties.DatabaseType,
      properties.Name,
      tag: properties.Name.Replace(" ", ""),
      scriptBlockAst.ParamBlock,
      scriptBlockAst.DynamicParamBlock,
      properties.LogoMediaId.ToString(),
      properties.Secrets,
      docsUrl: properties.DocsUrl,
      terminalId: terminalId);

    registration.AddMethodToProxy("Init", Init);
    registration.AddMethodToProxy("HealthCheck", HealthCheck);
    WriteObject(registration);
  }
}

[Cmdlet(VerbsCommon.New, "ClientGroup")]

internal class NewClientGroupCommand : ServiceScopePSCmdlet
{
  [Parameter(Mandatory = true)]
  public string Name { get; set; }

  [Parameter(Mandatory = true)]
  public string ExternalID { get; set; }

  protected override void ProcessRecord()
  {
    var clientGroupAttributeConstructorArguments = this.SessionState.PSVariable.GetValue("ClientGroupAttributeConstructorArguments") as IList<CustomAttributeTypedArgument>;
    var groupId = clientGroupAttributeConstructorArguments?.FirstOrDefault().Value;
    if (groupId == null)
      return;
    var dynamicProviderStore = this.ServiceScope.ServiceProvider.GetService(typeof(IDynamicProviderStore)) as IDynamicProviderStore;
    var ht = dynamicProviderStore?.GetState($"ClientGroupTypes");
    var groupType = ht?[groupId] as Type;
    var clientGroup = Activator.CreateInstance(groupType!, Name, ExternalID);
    WriteObject(clientGroup);
  }
}

[Cmdlet(VerbsCommon.New, "VerifyIntegrationResult")]
internal class NewVerifyIntegrationResultCommand : PSCmdlet
{
  [Parameter(Mandatory = true)]
  public string Message { get; set; }
  [Parameter(Mandatory = true)]
  public bool WasSuccessful { get; set; }

  protected override void ProcessRecord()
  {
    WriteObject(new VerifyProviderResult()
    {
      Message = this.Message,
      WasSuccessful = this.WasSuccessful
    });
  }
}

[Cmdlet(VerbsCommon.New, "HealthyResult")]
internal class NewHealthyResultCommand : PSCmdlet
{
  protected override void ProcessRecord()
  {
    WriteObject(HealthCheckResult.Healthy());
  }
}

[Cmdlet(VerbsCommon.New, "UnhealthyResult")]
internal class NewUnhealthyResultCommand : PSCmdlet
{
  [Parameter(Mandatory = false)]
  public string? Message { get; set; }
  protected override void ProcessRecord()
  {
    WriteObject(HealthCheckResult.Unhealthy(description: Message));
  }
}

[Cmdlet(VerbsCommon.New, "IntegrationClient")]
internal class NewIntegrationClientCommand : PSCmdlet
{
  [Parameter(Mandatory = true)] public string ClientId { get; set; }
  [Parameter(Mandatory = true)] public string ClientName { get; set; }
  [Parameter] public string? Status { get; set; }
  [Parameter] public ICollection<string> Types { get; set; } = new List<string>();
  [Parameter] public JsonElement? InternalData { get; set; }

  protected override void ProcessRecord()
  {
    var client = new IntegrationClient(ClientId, ClientName, Status, Types.ToList(), InternalData);
    WriteObject(client);
  }
}

[Cmdlet(VerbsCommon.New, "IntegrationAgent")]
internal class NewIntegrationAgentCommand : PSCmdlet
{
  [Parameter(Mandatory = true)] public string Name { get; set; }
  [Parameter(Mandatory = false)] public string SerialNumber { get; set; }
  // ReSharper disable once InconsistentNaming
  [Parameter(Mandatory = true)] public string OSName { get; set; }
  [Parameter(Mandatory = false)] public string Manufacturer { get; set; }
  [Parameter(Mandatory = true)]
  public string ClientId { get; set; }

  [Parameter(Mandatory = true)]
  public string AgentId { get; set; }

  [Parameter(Mandatory = false)] public bool IsOnline { get; set; } = true;

  [Parameter(Mandatory = false)]
  public string? AgentVersion { get; set; }

  [Parameter(Mandatory = false)] public bool SupportsRunningScripts { get; set; } = false;

  [Obsolete("No longer used")]
  [Parameter(Mandatory = false)]
  public bool SupportsOnlineStatus { get; set; } = false;

  [Parameter(Mandatory = false)]
  public string? Domain { get; set; }

  [Parameter(Mandatory = false)]
  public string? AzureTenantId { get; set; }

  [Parameter(Mandatory = false)]
  public JsonElement? InternalData { get; set; }
  protected override void ProcessRecord()
  {
    var agent = new ProviderAgent()
    {
      ExternalClientId = ClientId,
      ExternalAgentId = AgentId,
      IsOnline = IsOnline,
      AgentVersion = AgentVersion == null ? null : NuGetVersion.Parse(AgentVersion),
      SupportsRunningScripts = SupportsRunningScripts,
      InternalData = InternalData,
      DeviceDetails = new DeviceDetails
      {
        DeviceName = Name,
        OperatingSystemName = OSName,
        Manufacturer = Manufacturer,
        SerialNumber = SerialNumber,
        Domain = Domain,
        AzureTenantId = AzureTenantId
      }
    };

    WriteObject(agent);
  }
}



[Cmdlet(VerbsCommon.Get, "IntegrationAgentInstallToken")]
internal class GetIntegrationAgentInstallTokenCmdlet : IntegrationPSCmdlet
{
  [Parameter(Mandatory = false)] public string? ExternalClientId { get; set; }

  protected override void ProcessRecord()
  {
    // fetch instance of provider
    var clientId = string.IsNullOrEmpty(ExternalClientId) ? GetIntegrationClientId() : ExternalClientId;
    var installToken =
      new JoinableTaskContext().Factory.Run(async () =>
        await ProviderActions.GetTenantInstallToken(ProviderLink, clientId, CancellationToken));

    WriteObject(installToken);
  }

}

[Cmdlet(VerbsCommon.Get, "IntegrationTenantUninstallTokenCmdlet")]
internal class GetIntegrationTenantUninstallTokenCmdlet : IntegrationPSCmdlet
{
  [Parameter(Mandatory = false)] public string ExternalClientId { get; set; } = default!;

  protected override void ProcessRecord()
  {
    string clientId = string.IsNullOrEmpty(ExternalClientId) ? GetIntegrationClientId() : ExternalClientId;
    var uninstallToken =
      new JoinableTaskContext().Factory.Run(async () =>
        await ProviderActions.GetTenantUninstallToken(ProviderLink, clientId, CancellationToken));

    WriteObject(uninstallToken);
  }
}

[Cmdlet(VerbsCommon.Get, "IntegrationAgentUninstallTokenCmdlet")]
internal class GetIntegrationAgentUninstallTokenCmdlet : IntegrationPSCmdlet
{
  [Parameter(Mandatory = false)] public string ExternalAgentId { get; set; } = default!;

  protected override void ProcessRecord()
  {
    string agentId = string.IsNullOrEmpty(ExternalAgentId) ? GetIntegrationAgentId() : ExternalAgentId;
    var uninstallToken =
      new JoinableTaskContext().Factory.Run(async () =>
        await ProviderActions.GetAgentUninstallToken(ProviderLink, agentId, CancellationToken));

    WriteObject(uninstallToken);
  }
}

[Cmdlet(VerbsCommon.Get, "IntegrationAuthenticatedDownload")]
internal class GetIntegrationAuthenticatedDownloadCmdlet : IntegrationPSCmdlet
{
  protected override void ProcessRecord()
  {
    var authHeader =
      new JoinableTaskContext().Factory.Run(async () =>
             await ProviderActions.GetAuthHeader(ProviderLink, CancellationToken));

    WriteObject(authHeader);
  }
}

[Cmdlet(VerbsCommon.Get, "IntegrationDynamicVersions")]
internal class GetIntegrationDynamicVersionsCmdlet : IntegrationPSCmdlet
{
  [Parameter(Mandatory = false)] public string ExternalClientId { get; set; } = default!;

  protected override void ProcessRecord()
  {
    string clientId = string.IsNullOrEmpty(ExternalClientId) ? GetIntegrationClientId() : ExternalClientId;
    var versions =
      new JoinableTaskContext().Factory.Run(async () =>
        await ProviderActions.GetDynamicVersions(ProviderLink, clientId, CancellationToken));

    WriteObject(versions, true);
  }
}

[Cmdlet(VerbsCommon.New, "SupportFormBranding")]
internal class NewSupportFormBranding : PSCmdlet
{
  [Parameter] public string SupportSidebarTitle { get; set; }
  [Parameter] public string HeaderAlertMessage { get; set; }
  [Parameter] public string DescriptionPlaceholderText { get; set; }
  [Parameter] public string DescriptionAlertMessage { get; set; }
  [Parameter] public bool ShowConfirmationCheckbox { get; set; }
  [Parameter] public string FooterMessage { get; set; }
  [Parameter] public string SessionSupportButtonTitle { get; set; }
  [Parameter] public bool ShowSessionSupportConfirmCheckbox { get; set; }
  protected override void ProcessRecord()
  {
    var branding = new SupportFormBranding(
      SupportSidebarTitle: SupportSidebarTitle,
      HeaderAlertMessage: HeaderAlertMessage,
      DescriptionPlaceholderText: DescriptionPlaceholderText,
      DescriptionAlertMessage: DescriptionAlertMessage,
      FooterMessage: FooterMessage,
      ShowConfirmationCheckbox: ShowConfirmationCheckbox,
      SessionSupportButtonTitle: SessionSupportButtonTitle,
      ShowSessionSupportConfirmCheckbox: ShowSessionSupportConfirmCheckbox
    );
    WriteObject(branding);
  }
}
