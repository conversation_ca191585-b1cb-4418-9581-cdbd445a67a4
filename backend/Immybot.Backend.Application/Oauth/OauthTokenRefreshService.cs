using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Persistence.Shared;

namespace Immybot.Backend.Application.Oauth;

/// <summary>
/// For each OAuth token in the database:
/// <list type="bullet">
/// <item>
/// If the token is refreshable and refresh token expires soon:
/// -> Refresh the access/refresh/id tokens; update in key vault
/// </item>
/// <item>
/// Else if the access token is expired and token is not refreshable:
/// -> Delete the token from the database and key vault
/// </item>
/// </list>
/// </summary>
internal class OauthTokenRefreshService : BackgroundService
{
  // Run the checker every minute
  private readonly TimeSpan _sleepingInterval = TimeSpan.FromMinutes(1);
  private readonly Func<ImmybotDbContext> _ctxFactory;
  private readonly IOauthAccessTokenStore _oauthAccessTokenStore;
  private readonly ILogger<OauthTokenRefreshService> _logger;

  public OauthTokenRefreshService(
    Func<ImmybotDbContext> ctxFactory,
    IOauthAccessTokenStore oauthAccessTokenStore,
    ILogger<OauthTokenRefreshService> logger)
  {
    _ctxFactory = ctxFactory;
    _oauthAccessTokenStore = oauthAccessTokenStore;
    _logger = logger;
  }

  protected override async Task ExecuteAsync(CancellationToken stoppingToken)
  {
    _logger.LogInformation("OAuthTokenRefreshService background service is starting");
    // After setting up the initial state ⬆️, unblock app initialization and start the main loop ⬇️
    await Task.Yield();

    while (!stoppingToken.IsCancellationRequested)
    {
      try
      {
        await PerformWork(stoppingToken);
      }
      catch (Exception ex) when (!ex.IsCancellationException(stoppingToken))
      {
        _logger.LogError(ex,
          "Error occurred during while performing token refresh loop");
      }
      await Task.Delay(_sleepingInterval, stoppingToken);
    }
  }

  private async Task PerformWork(CancellationToken cancellationToken)
  {
    var now = DateTime.UtcNow;
    await using var ctx = _ctxFactory();
    var accessTokens = await ctx.OauthAccessTokens
      .AsNoTracking()
      .TagForTelemetry(nameof(OauthTokenRefreshService))
      .ToListAsync(cancellationToken);
    foreach (var token in accessTokens)
    {
      if (cancellationToken.IsCancellationRequested) break;
      try
      {
        if (token.AllowSilentRefresh && token.RefreshTokenId != null)
        {
          if (token.RefreshTokenExpiresAtUtc < now)
          {
            _logger.LogInformation("Refreshing oauth token {oauthTokenId} because the refresh token expiration date is coming up", token.Id);
            try {
                await _oauthAccessTokenStore.RefreshTokensForOauthAccessTokenId(token.Id, cancellationToken);
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Failed to refresh token {oauthTokenId} - token will be deleted", token.Id);
                await _oauthAccessTokenStore.DeleteOauthAccessToken(token.Id, cancellationToken);
            }
          }
        }
        else
        {
          if (token.AccessTokenExpiresAtUtc < now)
          {
            _logger.LogInformation("Deleting oauth token {oauthTokenId} because it is expired", token.Id);
            await _oauthAccessTokenStore.DeleteOauthAccessToken(token.Id, cancellationToken);
          }
        }
      }
      catch(OauthException ex) when (!ex.IsCancellationException(cancellationToken))
      {
        _logger.LogError(ex, "An OAuth exception occured. {oauthTokenId} {@response}", token.Id, ex.ErrorResponse);
      }
      catch (Exception ex ) when (!ex.IsCancellationException(cancellationToken))
      {
        _logger.LogError(ex, "Error occurred while performing token refresh or deletion for oauth token {oauthTokenId}", token.Id);
      }
    }
  }
}
