using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.Application.Oauth;

internal static class OauthServicesConfiguration
{
  public static void AddOauthServices(this IServiceCollection services)
  {
    services.AddSingleton<IOauthHooksRepository, OauthHooksRepository>();
    services.AddTransient<IOauthConsentService, OauthConsentService>();
    services.AddTransient<IOauthClientSecretRepository, OauthClientSecretRepository>();
    services.AddTransient<IAuthTokenSecretsRepository, AuthTokenSecretsRepository>();
    services.AddTransient<IOauthAccessTokenStore, OauthAccessTokenStore>();
    services.AddHostedService<OauthHookExpirationService>();
    services.AddHostedService<OauthTokenRefreshService>();
  }
}
