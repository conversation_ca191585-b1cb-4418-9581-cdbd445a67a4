using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Azure;
using Azure.Security.KeyVault.Secrets;
using Immybot.Backend.Application.Interface.Azure;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.Application.Oauth;

public record AuthToken(string Token, DateTimeOffset ExpiresOn, string ClientId);

/// <summary>
/// Stores the token values associated with <see cref="Oauth2AccessToken"/>
/// access/refresh/identity token ids
/// </summary>
public interface IAuthTokenSecretsRepository
{
  Task<AuthToken?> GetTokenById(string tokenId, CancellationToken cancellationToken);
  Task UpsertTokenById(string tokenId, AuthToken token, CancellationToken cancellationToken);
  Task DeleteTokenById(string tokenId, CancellationToken cancellationToken);
}

/// <inheritdoc/>
internal class AuthTokenSecretsRepository(SecretClient _kvSecretClient) : IAuthTokenSecretsRepository
{
  private static readonly JsonSerializerOptions _jsonSerializerOptions = new()
  {
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
  };
  private const string _secretNamePrefix = "auth-token-";
  /// <exception cref="ArgumentNullException"></exception>
  private static string GetSecretName(string tokenId)
  {
    if (tokenId is null)
    {
      throw new ArgumentNullException(nameof(tokenId));
    }

    return $"{_secretNamePrefix}{tokenId}";
  }

  public async Task<AuthToken?> GetTokenById(string tokenId, CancellationToken cancellationToken)
  {
    try
    {
      var secret = await _kvSecretClient
        .GetSecretAsync(GetSecretName(tokenId), cancellationToken: cancellationToken);
      if (secret.Value is null) return null;
      if (secret.Value.Properties.ExpiresOn is { } e && e < DateTimeOffset.UtcNow)
      {
        return null;
      }
      var details = secret.Value.Value switch
      {
        { } v => JsonSerializer.Deserialize<AuthToken>(v, _jsonSerializerOptions),
        _ => null
      };
      return details;
    }
    catch (RequestFailedException ex) when (ex.ErrorCode == "SecretNotFound")
    {
      return null;
    }
  }

  public async Task UpsertTokenById(
    string tokenId,
    AuthToken token,
    CancellationToken cancellationToken)
  {
    var secretName = GetSecretName(tokenId);
    try
    {
      await _kvSecretClient.SetSecretAsync(
        new KeyVaultSecret(
          secretName,
          JsonSerializer.Serialize(token, _jsonSerializerOptions)),
        cancellationToken: cancellationToken);
    }
    catch (RequestFailedException ex) when (ex.ErrorCode == "Conflict")
    {
      // secret already exists in deleted state, recover it and try again
      var recoverOperation = await _kvSecretClient.StartRecoverDeletedSecretAsync(
        secretName,
        cancellationToken: cancellationToken);
      await recoverOperation.WaitForCompletionAsync(cancellationToken: cancellationToken);

      await _kvSecretClient.SetSecretAsync(
        new KeyVaultSecret(
          secretName,
          JsonSerializer.Serialize(token, _jsonSerializerOptions)),
        cancellationToken: cancellationToken);
    }
  }

  public async Task DeleteTokenById(string tokenId, CancellationToken cancellationToken)
  {
    var secretName = GetSecretName(tokenId);
    try
    {
      var deleteOperation = await _kvSecretClient.StartDeleteSecretAsync(
        secretName,
        cancellationToken: cancellationToken);

      if (deleteOperation is not null)
      {
        await deleteOperation.WaitForCompletionAsync(cancellationToken: cancellationToken);
      }
    }
    catch (RequestFailedException ex) when (ex.ErrorCode == "SecretNotFound")
    {
      // ignore
    }
  }
}
