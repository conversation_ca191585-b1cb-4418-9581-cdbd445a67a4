using Immybot.Backend.Application.Interface.Azure;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Oauth;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Application.Oauth;

public record CreateAccessTokenPayload(
  AuthUserDto? CreatedByUser,
  OauthConsentData ConsentData,
  Oauth2AccessTokenResponse AccessToken,
  string TenantPrincipalId,
  bool AllowSilentRefresh);

public record Oauth2AccessTokenResponseWithExpirationDate(
  DateTimeOffset ExpiresAtUtc,
  string AccessTokenId,
  string AccessToken,
  string TokenType,
  string RefreshToken,
  string IdToken,
  int ExpiresIn,
  string ClientInfo) : Oauth2AccessTokenResponse(AccessTokenId,
  AccessToken,
  TokenType,
  RefreshToken,
  IdToken,
  ExpiresIn,
  ClientInfo);

/// <summary>
/// Provides access to <see cref="Oauth2AccessToken"/> objects stored in the database and key
/// vault and methods to manage them (create, delete, refresh)
/// </summary>
public interface IOauthAccessTokenStore
{
  /// <summary>
  /// Attempt to retrieve an access token for the given scope from db/key vault.
  /// Throw a MissingAccessTokenException if no access token is available
  /// </summary>
  /// <exception cref="MissingAccessTokenException"></exception>
  Task<AuthToken> GetAccessTokenForScopes(
    string tenantPrincipalId,
    string[] scopes,
    string? specificClientId,
    CancellationToken cancellationToken,
    bool refreshable = false);

  /// <summary>
  /// Attempt to retrieve a refresh token for the partner center user impersonation scope
  /// for the given partner principal id from db/key vault, then use that to get an access token
  /// for the given customer principal id and scopes.
  /// Throw a MissingAccessTokenException if no partner center refresh token is available
  /// </summary>
  /// <exception cref="MissingAccessTokenException"></exception>
  Task<AuthToken> GenerateAccessTokenFromRefreshTokenForScopes(
    string partnerPrincipalId,
    string customerPrincipalId,
    string[] scopesRequiredOnRefreshToken,
    string[] scopesForAccessToken,
    string? specificClientIdForRefreshToken,
    string? specificClientIdForAccessToken,
    CancellationToken cancellationToken);

  /// <summary>
  /// Attempt to retrieve the access token for the given oauth2 access token id from db/key vault.
  /// Throw a MissingAccessTokenException if the access token is not available
  /// </summary>
  /// <exception cref="MissingAccessTokenException"></exception>
  Task<AuthToken> GetAccessTokenForOauthAccessTokenId(
    int oauth2AccessTokenId,
    CancellationToken cancellationToken);

  /// <summary>
  /// Constructs an <see cref="Oauth2AccessTokenResponse"/> from the given
  /// <see cref="Oauth2AccessToken"/> and the access/refresh/identity tokens in key vault.
  /// Throw a MissingAccessTokenException if the tokens in key vault are not available
  /// </summary>
  /// <exception cref="MissingAccessTokenException"></exception>
  Task<Oauth2AccessTokenResponseWithExpirationDate> HydrateOauthAccessToken(
    Oauth2AccessToken accessToken,
    CancellationToken cancellationToken,
    bool forceRefresh = false);

  /// <summary>
  /// Refresh the access token and refresh token for the given oauth2 access token id
  /// and update the database and key vault with the new values.
  /// Throw a MissingAccessTokenException if the tokens in key vault are not available
  /// </summary>
  /// <exception cref="MissingAccessTokenException"></exception>
  Task<Oauth2AccessToken> RefreshTokensForOauthAccessTokenId(
    int oauthAccessTokenId,
    CancellationToken cancellationToken);

  /// <summary>
  /// List all oauth access tokens in the database. Does not include the actual token values
  /// </summary>
  Task<List<Oauth2AccessToken>> ListOauthAccessTokens(CancellationToken cancellationToken);

  /// <summary>
  /// Retrieves the oauth access token with the given id from the database. Does not include the
  /// actual token values
  /// </summary>
  /// <exception cref="EntityNotFoundException"></exception>
  Task<Oauth2AccessToken> GetOauthAccessTokenById(int id, CancellationToken cancellationToken);

  /// <summary>
  /// Retrieves the oauth access token with the given id from the database. Does not include the
  /// actual token values
  /// </summary>
  /// <exception cref="EntityNotFoundException"></exception>
  Task<Oauth2AccessToken> GetOauthAccessTokenByAccessTokenId(string accessTokenId, CancellationToken cancellationToken);

  /// <summary>
  /// Inserts a new oauth2 access token into the database (and puts the values in key vault)
  /// </summary>
  Task<Oauth2AccessToken> CreateOauthAccessToken(
    CreateAccessTokenPayload payload,
    CancellationToken cancellationToken);

  /// <summary>
  /// Deletes the oauth2 access token with the given id from the database (and deletes the values
  /// from key vault)
  /// </summary>
  Task DeleteOauthAccessToken(
    int oauthAccessTokenId,
    CancellationToken cancellationToken);

  /// <summary>
  /// Sets the user to use for audit logging when creating/deleting access tokens
  /// </summary>
  void SetUser(AuthUserDto user);
}

/// <inheritdoc cref="IOauthAccessTokenStore" />
internal class OauthAccessTokenStore : IOauthAccessTokenStore
{
  // How long before an access token expires should we attempt to refresh it:
  private static readonly TimeSpan _fiveMinutesTimespan = TimeSpan.FromMinutes(5);
  private readonly Func<ImmybotDbContext> _ctxFactory;
  private readonly IAuthTokenSecretsRepository _authTokenSecretsRepository;
  private readonly IOauthConsentService _oauthConsentService;
  private readonly TimeProvider _timeProvider;
  private readonly ILogger<OauthAccessTokenStore> _logger;
  private AuthUserDto? _user;

  public OauthAccessTokenStore(
    Func<ImmybotDbContext> ctxFactory,
    IAuthTokenSecretsRepository authTokenSecretsRepository,
    IOauthConsentService oauthConsentService,
    TimeProvider timeProvider,
    ILogger<OauthAccessTokenStore> logger)
  {
    _ctxFactory = ctxFactory;
    _authTokenSecretsRepository = authTokenSecretsRepository;
    _oauthConsentService = oauthConsentService;
    _timeProvider = timeProvider;
    _logger = logger;
  }

  public void SetUser(AuthUserDto user)
  {
    _user = user;
  }

  public async Task<List<Oauth2AccessToken>> ListOauthAccessTokens(CancellationToken cancellationToken)
  {
    await using var ctx = _ctxFactory();
    return await ctx.OauthAccessTokens
      .AsNoTracking()
      .TagForTelemetry()
      .ToListAsync(cancellationToken);
  }

  public async Task<AuthToken> GetAccessTokenForScopes(
    string tenantPrincipalId,
    string[] scopes,
    string? specificClientId,
    CancellationToken cancellationToken,
    bool refreshable = false)
  {
    await using var ctx = _ctxFactory();

    var _scopes = scopes.ToHashSet(StringComparer.OrdinalIgnoreCase);

    if (refreshable)
    {
      _scopes.Add("offline_access");
    }

    var scopePatterns = _scopes.Select(s => $"%{s.Replace("%", "\\%")}%").ToList();

    // Get representation of the access token from the database
    var tokenRep = await ctx.OauthAccessTokens
      .AsNoTracking()
      .OrderByDescending(a => a.AccessTokenExpiresAtUtc)
      .FirstOrDefaultAsync(a => a.TenantPrincipalId == tenantPrincipalId
        && a.ConsentData.Scopes != null
        && scopePatterns.All(s => EF.Functions.ILike(a.ConsentData.Scopes, s))
        && (specificClientId == null || a.ConsentData.ClientId == specificClientId), cancellationToken)
      ?? throw new MissingAccessTokenException(tenantPrincipalId, string.Join(' ', _scopes), specificClientId);

    // if access token is expired or expiring soon
    if (tokenRep.AccessTokenExpiresAtUtc - _timeProvider.GetUtcNow().UtcDateTime < _fiveMinutesTimespan)
    {
      if (tokenRep.AllowSilentRefresh && tokenRep.RefreshTokenId != null)
      {
        await RefreshTokensForOauthAccessTokenId(tokenRep.Id, cancellationToken);
      }
      else if (tokenRep.AccessTokenExpiresAtUtc < _timeProvider.GetUtcNow().UtcDateTime)
      {
        throw new MissingAccessTokenException(tenantPrincipalId, string.Join(' ', _scopes), specificClientId);
      }
    }

    // Get the actual token value from key vault
    var accessToken = await _authTokenSecretsRepository
      .GetTokenById(tokenRep.AccessTokenId, cancellationToken)
      ?? throw new MissingAccessTokenException(
        tenantPrincipalId,
        string.Join(' ', _scopes),
        specificClientId);

    return accessToken;
  }

  public async Task<AuthToken> GenerateAccessTokenFromRefreshTokenForScopes(
    string partnerPrincipalId,
    string customerPrincipalId,
    string[] scopesRequiredOnRefreshToken,
    string[] scopesForAccessToken,
    string? specificClientIdForRefreshToken,
    string? specificClientIdForAccessToken,
    CancellationToken cancellationToken)
  {
    await using var ctx = _ctxFactory();

    var requiredScopes = scopesRequiredOnRefreshToken
      .Concat(new[] { "offline_access" })
      .ToHashSet(StringComparer.OrdinalIgnoreCase);

    var scopePatterns = requiredScopes.Select(s => $"%{s.Replace("%", "\\%")}%").ToList();

    // Get representation of the access token from the database
    var tokenRep = await ctx.OauthAccessTokens
      .AsNoTracking()
      .OrderByDescending(a => a.AccessTokenExpiresAtUtc)
      .FirstOrDefaultAsync(a => a.TenantPrincipalId == partnerPrincipalId
        && a.ConsentData.Scopes != null
        && scopePatterns.All(s => EF.Functions.ILike(a.ConsentData.Scopes, s))
        && (specificClientIdForRefreshToken == null || a.ConsentData.ClientId == specificClientIdForRefreshToken), cancellationToken)
      ?? throw new MissingAccessTokenException(partnerPrincipalId, string.Join(' ', requiredScopes), specificClientIdForRefreshToken);

    if (tokenRep.RefreshTokenId == null || tokenRep.RefreshTokenExpiresAtUtc < _timeProvider.GetUtcNow().UtcDateTime)
    {
      throw new MissingAccessTokenException(partnerPrincipalId, string.Join(' ', requiredScopes), specificClientIdForRefreshToken);
    }

    // if refresh token is expired or expiring soon
    if (tokenRep.AllowSilentRefresh && tokenRep.RefreshTokenExpiresAtUtc - _timeProvider.GetUtcNow().UtcDateTime <
        _fiveMinutesTimespan)
    {
      await RefreshTokensForOauthAccessTokenId(tokenRep.Id, cancellationToken);
    }
    else if (tokenRep.RefreshTokenExpiresAtUtc < _timeProvider.GetUtcNow().UtcDateTime)
    {
      throw new MissingAccessTokenException(
        partnerPrincipalId,
        string.Join(' ', requiredScopes),
        specificClientIdForRefreshToken);
    }

    // Get the actual token value from key vault
    var refreshToken = await _authTokenSecretsRepository
      .GetTokenById(tokenRep.RefreshTokenId, cancellationToken)
      ?? throw new MissingAccessTokenException(
        partnerPrincipalId,
        string.Join(' ', requiredScopes),
        specificClientIdForRefreshToken);

    // Refresh the token
    var consentData = tokenRep.ConsentData with
    {
      ClientId = specificClientIdForAccessToken ?? tokenRep.ConsentData.ClientId,
      Scopes = string.Join(' ', scopesForAccessToken),

      // TODO: add support for other non-azure token endpoints
      TokenEndpoint = new Uri($"https://login.microsoftonline.com/{customerPrincipalId}/oauth2/v2.0/token")
    };

    var authResponse = await _oauthConsentService.RedeemRefreshTokenForAccessTokenAsync(
      new(consentData, refreshToken.Token),
      cancellationToken);

    if (authResponse.Value is Oauth2AccessTokenErrorResponse errorResponse)
    {
      throw new OauthException(errorResponse);
    }
    else if (authResponse.Value is Oauth2AccessTokenResponse accessTokenResponse)
    {
      var expiresAt = DateTimeOffset.UtcNow.AddSeconds(accessTokenResponse.ExpiresIn);
      return new AuthToken(accessTokenResponse.AccessToken, expiresAt, consentData.ClientId);
    }
    else
    {

      _logger.LogError("Unexpected response type from {service}: {responseType}",
        nameof(_oauthConsentService),
        authResponse.ToString());
      throw new InvalidOperationException("Unexpected response type from OAuth consent service");
    }
  }

  public async Task<AuthToken> GetAccessTokenForOauthAccessTokenId(
    int oauth2AccessTokenId,
    CancellationToken cancellationToken)
  {
    var tokenRep = await GetOauthAccessTokenById(oauth2AccessTokenId, cancellationToken);

    // if access token is expired or expiring soon
    if (tokenRep.AccessTokenExpiresAtUtc - _timeProvider.GetUtcNow().UtcDateTime < _fiveMinutesTimespan)
    {
      if (tokenRep.AllowSilentRefresh && tokenRep.RefreshTokenId != null)
      {
        await RefreshTokensForOauthAccessTokenId(tokenRep.Id, cancellationToken);
      }
      else if (tokenRep.AccessTokenExpiresAtUtc < _timeProvider.GetUtcNow().UtcDateTime)
      {
        throw new MissingAccessTokenException(
          tokenRep.TenantPrincipalId,
          tokenRep.ConsentData.Scopes,
          null);
      }
    }

    return await _authTokenSecretsRepository
      .GetTokenById(tokenRep.AccessTokenId, cancellationToken)
      ?? throw new MissingAccessTokenException(
        tokenRep.TenantPrincipalId,
        tokenRep.ConsentData.Scopes,
        null);
  }

  public async Task<Oauth2AccessTokenResponseWithExpirationDate> HydrateOauthAccessToken(
    Oauth2AccessToken accessToken,
    CancellationToken cancellationToken,
    bool forceRefresh = false)
  {
    // if we are force refreshing, only perform the refresh if the token was last refreshed more than one minute ago
    var force = forceRefresh &&
                _timeProvider.GetUtcNow().UtcDateTime - accessToken.UpdatedDate > TimeSpan.FromMinutes(1);
    // if access token is expired or expiring soon

    if (force || (accessToken.AccessTokenExpiresAtUtc - _timeProvider.GetUtcNow().UtcDateTime < _fiveMinutesTimespan
                  && accessToken is { AllowSilentRefresh: true, RefreshTokenId: not null }))
    {
      await RefreshTokensForOauthAccessTokenId(accessToken.Id, cancellationToken);
    }

    var token = await _authTokenSecretsRepository
      .GetTokenById(accessToken.AccessTokenId, cancellationToken)
      ?? throw new MissingAccessTokenException(
        accessToken.TenantPrincipalId,
        accessToken.ConsentData.Scopes,
        null);

    if (token.ExpiresOn < DateTimeOffset.UtcNow)
    {
      throw new MissingAccessTokenException(
        accessToken.TenantPrincipalId,
        accessToken.ConsentData.Scopes,
        null);
    }

    var response = new Oauth2AccessTokenResponseWithExpirationDate(
      accessToken.AccessTokenExpiresAtUtc.ToDateTimeOffset(TimeZoneInfo.Utc),
      accessToken.AccessTokenId,
      token.Token,
      accessToken.TokenType,
      string.Empty,
      string.Empty,
      0,
      string.Empty);

    if (accessToken.RefreshTokenId is { } refreshTokenId)
    {
      var refreshToken = await _authTokenSecretsRepository
        .GetTokenById(refreshTokenId, cancellationToken)
        ?? throw new MissingAccessTokenException(
          accessToken.TenantPrincipalId,
          accessToken.ConsentData.Scopes,
          null);
      response = response with { RefreshToken = refreshToken.Token };
    }

    if (accessToken.IdentityTokenId is { } identityTokenId)
    {
      var identityToken = await _authTokenSecretsRepository
        .GetTokenById(identityTokenId, cancellationToken)
        ?? throw new MissingAccessTokenException(
          accessToken.TenantPrincipalId,
          accessToken.ConsentData.Scopes,
          null);
      response = response with { IdToken = identityToken.Token };
    }

    return response;
  }

  public async Task<Oauth2AccessToken> GetOauthAccessTokenByAccessTokenId(
    string accessTokenId,
    CancellationToken cancellationToken)
  {
    await using var ctx = _ctxFactory();
    var token = await ctx.OauthAccessTokens
                  .AsNoTracking()
                  .FirstOrDefaultAsync(t => t.AccessTokenId == accessTokenId, cancellationToken)
                ?? throw new EntityNotFoundException("OAuth token not found in database");

    return token;
  }

  public async Task<Oauth2AccessToken> CreateOauthAccessToken(
    CreateAccessTokenPayload payload,
    CancellationToken cancellationToken)
  {
    await using var ctx = _ctxFactory();
    if (_user != null) ctx.SetUser(_user);
    // Would be nice if we could just make IOAuthAccessTokenStore a scoped service
    // but that's a much bigger change (b/c of dependency in DynamicFormService)
    if (payload.CreatedByUser != null) ctx.SetUser(payload.CreatedByUser);

    var expiresAt = DateTimeOffset.UtcNow.AddSeconds(payload.AccessToken.ExpiresIn);

    var accessToken = new Oauth2AccessToken
    {
      AccessTokenId = Guid.NewGuid().ToString(),
      TokenType = payload.AccessToken.TokenType,
      AccessTokenExpiresAtUtc = expiresAt.DateTime,
      TenantPrincipalId = payload.TenantPrincipalId,
      ConsentData = payload.ConsentData,
      AllowSilentRefresh = payload.AllowSilentRefresh,
    };

    await _authTokenSecretsRepository.UpsertTokenById(
      accessToken.AccessTokenId,
      new(payload.AccessToken.AccessToken, expiresAt, payload.ConsentData.ClientId),
      cancellationToken);

    if (payload.AccessToken.RefreshToken is { Length: > 0 } refreshToken)
    {
      // azure identity platform docs say RTs expire after 90 days, but we've had issues
      // with them expiring before that time period.  So we'll just assume they expire
      // after 24 hours. This is a bit more secure anyway.
      var refreshExpiresAt = DateTimeOffset.UtcNow.AddSeconds(payload.AccessToken.ExpiresIn*0.8);
      accessToken.RefreshTokenId = Guid.NewGuid().ToString();
      accessToken.RefreshTokenExpiresAtUtc = refreshExpiresAt.DateTime;
      await _authTokenSecretsRepository.UpsertTokenById(
        accessToken.RefreshTokenId,
        new(refreshToken, refreshExpiresAt, payload.ConsentData.ClientId),
        cancellationToken);
    }

    if (payload.AccessToken.IdToken is { Length: > 0 } identityToken)
    {
      accessToken.IdentityTokenId = Guid.NewGuid().ToString();
      await _authTokenSecretsRepository.UpsertTokenById(
        accessToken.IdentityTokenId,
        new(identityToken, expiresAt, payload.ConsentData.ClientId),
        cancellationToken);
    }

    ctx.OauthAccessTokens.Add(accessToken);
    await ctx.SaveChangesAsync(cancellationToken);
    return accessToken;
  }

  public async Task<Oauth2AccessToken> GetOauthAccessTokenById(
    int id,
    CancellationToken cancellationToken)
  {
    await using var ctx = _ctxFactory();
    var token = await ctx.OauthAccessTokens
      .AsNoTracking()
      .FirstOrDefaultAsync(t => t.Id == id, cancellationToken)
      ?? throw new EntityNotFoundException("OAuth token not found in database");

    return token;
  }

  public async Task<Oauth2AccessToken> RefreshTokensForOauthAccessTokenId(
    int oauthAccessTokenId,
    CancellationToken cancellationToken)
  {
    await using var ctx = _ctxFactory();
    if (_user != null) ctx.SetUser(_user);
    var token = await ctx.OauthAccessTokens
      .FindAsync(new object[] { oauthAccessTokenId }, cancellationToken)
      ?? throw new EntityNotFoundException("OAuth token not found in database");

    if (!token.AllowSilentRefresh || token.RefreshTokenId == null)
    {
      throw new ValidationException("Cannot refresh access token that is not refreshable");
    }

    // Get the actual token value from key vault
    var refreshToken = await _authTokenSecretsRepository
      .GetTokenById(token.RefreshTokenId, cancellationToken)
      ?? throw new ValidationException("Refresh token not found in key vault");

    // Refresh the token
    var authResponse = await _oauthConsentService.RedeemRefreshTokenForAccessTokenAsync(
      new(token.ConsentData, refreshToken.Token),
      cancellationToken);

    if (authResponse.Value is Oauth2AccessTokenErrorResponse errorResponse)
    {
      if (errorResponse.Error == "invalid_grant")
      {
        // refresh token is invalid, delete it
        _logger.LogInformation("Deleting refresh oauth token for {oauthTokenId} due to invalid_grant.", token.Id);
        await DeleteOauthAccessToken(oauthAccessTokenId, cancellationToken);
        return token;
      }

      throw new OauthException(errorResponse);
    }
    else if (authResponse.Value is Oauth2AccessTokenResponse accessTokenResponse)
    {
      var expiresAt = DateTimeOffset.UtcNow.AddSeconds(accessTokenResponse.ExpiresIn);
      await _authTokenSecretsRepository.UpsertTokenById(
        token.AccessTokenId,
        new(accessTokenResponse.AccessToken, expiresAt, token.ConsentData.ClientId),
        cancellationToken);

      token.AccessTokenExpiresAtUtc = expiresAt.DateTime;

      if (accessTokenResponse.RefreshToken is { Length: > 0 } refreshToken2)
      {
        // azure identity platform docs say RTs expire after 90 days, but we've had issues
        // with them expiring before that time period.  So we'll just assume they expire
        // after 24 hours. This is a bit more secure anyway.
        var refreshExpiresAt = DateTimeOffset.UtcNow.AddSeconds(accessTokenResponse.ExpiresIn*0.8);
        token.RefreshTokenExpiresAtUtc = refreshExpiresAt.DateTime;
        await _authTokenSecretsRepository.UpsertTokenById(
          token.RefreshTokenId,
          new(refreshToken2, refreshExpiresAt, token.ConsentData.ClientId),
          cancellationToken);
      }

      if (accessTokenResponse.IdToken is { Length: > 0 } identityToken)
      {
        token.IdentityTokenId ??= Guid.NewGuid().ToString();
        await _authTokenSecretsRepository.UpsertTokenById(
          token.IdentityTokenId,
          new(identityToken, expiresAt, token.ConsentData.ClientId),
          cancellationToken);
      }

      await ctx.SaveChangesAsync(cancellationToken);
      return token;
    }
    else
    {
      _logger.LogError("Unexpected response type from {service}: {responseType}",
        nameof(_oauthConsentService),
        authResponse.ToString());
      throw new InvalidOperationException("Unexpected response type from OAuth consent service");
    }
  }
  public async Task DeleteOauthAccessToken(
    int oauthAccessTokenId,
    CancellationToken cancellationToken)
  {
    await using var ctx = _ctxFactory();
    if (_user != null) ctx.SetUser(_user);
    var token = await ctx.OauthAccessTokens
      .FindAsync(new object[] { oauthAccessTokenId }, cancellationToken)
      ?? throw new EntityNotFoundException("OAuth token not found in database");

    await _authTokenSecretsRepository.DeleteTokenById(token.AccessTokenId, cancellationToken);

    if (token.RefreshTokenId is { } refreshTokenId)
    {
      await _authTokenSecretsRepository.DeleteTokenById(refreshTokenId, cancellationToken);
    }

    if (token.IdentityTokenId is { } identityTokenId)
    {
      await _authTokenSecretsRepository.DeleteTokenById(identityTokenId, cancellationToken);
    }

    ctx.OauthAccessTokens.Remove(token);
    await ctx.SaveChangesAsync(cancellationToken);
  }
}
