using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.KeyVaultRepositories;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Z.EntityFramework.Plus;

namespace Immybot.Backend.Application.Commands;

internal class MergeTenantsCmd : IMergeTenantsCmd
{
  private readonly ILogger<MergeTenantsCmd> _logger;
  private readonly UserBearingDbFactory<ImmybotDbContext> _ctxFactory;
  private readonly IAzureTenantAuthDetailsRepository _azureTenantAuthDetailsRepository;

  public MergeTenantsCmd(
    ILogger<MergeTenantsCmd> logger,
    UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    IAzureTenantAuthDetailsRepository azureTenantAuthDetailsRepository)
  {
    _logger = logger;
    _ctxFactory = ctxFactory;
    _azureTenantAuthDetailsRepository = azureTenantAuthDetailsRepository;
  }

  // copy over all data from the tenants to merge to the target tenant
  public Task<CommandResult> Run(
    MergeTenantsPayload payload,
    CancellationToken cancellationToken)
  {
    if (payload is null)
    {
      throw new ArgumentNullException(nameof(payload));
    }

    var dest = payload.TargetTenantId;
    if (dest == 0) throw new ArgumentException("TargetTenantId must be provided");
    var source = payload.TenantsToMerge.Where(a => a != dest).ToArray();

    return RunInternal(source, dest, cancellationToken);
  }

  private async Task<CommandResult> RunInternal(int[] source, int dest, CancellationToken cancellationToken)
  {
    await using var dbContext = _ctxFactory();
    if (!await dbContext.Tenants.AnyAsync(a => a.Id == dest, cancellationToken))
      throw new ArgumentException("TargetTenantId specified does not exist");

    var sourcePrincipalIds = await dbContext.Tenants
      .Where(a => source.Contains(a.Id))
      .Select(a => a.AzureTenantLink == null ? null : a.AzureTenantLink!.AzureTenant!.PrincipalId)
      .Where(p => p != null)
      .ToListAsync(cancellationToken);
    try
    {
      var strategy = dbContext.Database.CreateExecutionStrategy();
      await strategy.ExecuteAsync(async (token) =>
      {
        await using var transaction = await dbContext.Database.BeginTransactionAsync(token);

        // brandings
        await dbContext.Brandings
          .Where(a => a.TenantId != null && source.Contains(a.TenantId.Value))
          .UpdateAsync(a => new Branding { TenantId = dest }, token);

        // computers
        await dbContext.Computers
          .Where(a => source.Contains(a.TenantId))
          .UpdateAsync(a => new Computer { TenantId = dest }, token);

        // licenses
        await dbContext.Licenses
          .Where(a => a.TenantId != null && source.Contains(a.TenantId.Value))
          .ExecuteUpdateAsync(a => a.SetProperty(b => b.TenantId, dest), token);

        // maintenance actions
        await dbContext.MaintenanceActions
          .Where(a => a.TenantId != null && source.Contains(a.TenantId.Value))
          .ExecuteUpdateAsync(a => a
              .SetProperty(b => b.TenantId, dest),
            token);

        // maintenance sessions
        await dbContext.MaintenanceSessions
          .Where(a => a.TenantId != null && source.Contains(a.TenantId.Value))
          .UpdateAsync(a => new MaintenanceSession { TenantId = dest }, token);

        // maintenance tasks
        await dbContext.TenantMaintenanceTasks
          .Where(a => source.Contains(a.TenantId))
          .UpdateAsync(a => new TenantMaintenanceTask { TenantId = dest }, token);

        // media
        await dbContext.TenantMedia
          .Where(a => source.Contains(a.TenantId))
          .UpdateAsync(a => new TenantMedia { TenantId = dest }, token);

        // persons
        await dbContext.Persons
          .Where(a => source.Contains(a.TenantId))
          .ExecuteUpdateAsync(a =>
              a.SetProperty(b => b.TenantId, dest),
            token);

        // provider clients
        await dbContext.GetProviderClients()
          .Where(a => a.LinkedToTenantId != null && source.Contains(a.LinkedToTenantId.Value))
          .ExecuteUpdateAsync(a => a.SetProperty(b => b.LinkedToTenantId, dest), token);

        // provider links
        await dbContext.GetProviderLinks()
          .Where(a => source.Contains(a.OwnerTenantId))
          .ExecuteUpdateAsync(a =>
              a.SetProperty(b => b.OwnerTenantId, dest),
            token);

        // schedules
        await dbContext.Schedules
          .Where(a => a.TenantId != null && source.Contains(a.TenantId.Value))
          .UpdateAsync(a => new Schedule { TenantId = dest }, token);

        // scripts
        await dbContext.TenantScripts
          .Where(a => source.Contains(a.TenantId))
          .UpdateAsync(a => new TenantScript { TenantId = dest }, token);

        // smtp configs
        await dbContext.SmtpConfigs
          .Where(a => source.Contains(a.TenantId))
          .UpdateAsync(a => new SmtpConfig { TenantId = dest }, token);

        // software
        await dbContext.TenantSoftware
          .Where(a => source.Contains(a.TenantId))
          .UpdateAsync(a => new TenantSoftware { TenantId = dest }, token);

        // target assignments
        await dbContext.TargetAssignments
          .Where(a => a.TenantId != null && source.Contains(a.TenantId.Value))
          .ExecuteUpdateAsync(a => a.SetProperty(b => b.TenantId, dest), token);

        // users
        await dbContext.Users
          .Where(a => source.Contains(a.TenantId))
          .UpdateAsync(a => new User { TenantId = dest }, token);

        if (await dbContext.Tenants.Where(a => a.IsMsp && source.Contains(a.Id)).Select(t => t.OwnerTenantId).ToListAsync(token) is { Count: > 0 } srcMspTenantOwners)
        {
          // we are merging an MSP tenant into another tenant, so we need to make the target
          // tenant an MSP tenant by setting the target tenant's isMsp to true

          if (srcMspTenantOwners.Contains(null))
          {
            // if any of the source tenants are ownerless, make the dest tenant ownerless
            await dbContext.Tenants
              .Where(a => a.Id == dest)
              .ExecuteUpdateAsync(s => s.SetProperty(a => a.IsMsp, true).SetProperty(a => a.OwnerTenantId, (int?)null), token);
          }
          else
          {
            // if none of the source tenants are ownerless, don't change the dest tenant's owner
            await dbContext.Tenants
              .Where(a => a.Id == dest)
              .ExecuteUpdateAsync(s => s.SetProperty(a => a.IsMsp, true), token);
          }
        }

        // if the destination tenant is a child of a source tenant, set the destination tenant's
        // owner to the owner of that source tenant (or owner of that if it's a child of a source tenant, etc.)
        var destTenant = await dbContext.Tenants.FirstOrDefaultAsync(a => a.Id == dest, token);
        if (destTenant is { OwnerTenantId: int o } && source.Contains(o))
        {
          var seenTenantIds = new HashSet<int> { dest };
          while (true)
          {
            if (seenTenantIds.Contains(o))
            {
              // we've already seen this tenant, so we've found a cycle
              throw new InvalidOperationException("Cannot merge tenants due to cyclical tenant owners");
            }
            seenTenantIds.Add(o);
            var parent = await dbContext.Tenants.FirstOrDefaultAsync(t => t.Id == o, token);
            if (parent?.OwnerTenantId is not int p || !source.Contains(p))
            {
              // this parent's owner is not a source tenant (or the owner is null), so we've
              // found the top level owner
              var ownerTenantId = parent?.OwnerTenantId;
              await dbContext.Tenants
                .Where(a => a.Id == dest)
                .ExecuteUpdateAsync(s => s.SetProperty(a => a.OwnerTenantId, ownerTenantId), token);
              break;
            }
            // parent's owner is a source tenant, so keep looking
            o = p;
          }
        }

        // set all children of the source tenants to be children of the new tenant
        await dbContext.Tenants
          .Where(a => a.OwnerTenantId != null && source.Contains(a.OwnerTenantId.Value) && a.Id != dest)
          .ExecuteUpdateAsync(s => s.SetProperty(a => a.OwnerTenantId, dest), token);

        await transaction.CommitAsync(token);

        // 2. delete the source tenants
        await dbContext.Tenants.Where(a => source.Contains(a.Id)).DeleteAsync(token);

        // 3. Delete source tenants Azure tenant auth details
        foreach (var principalId in sourcePrincipalIds)
        {
          if (principalId is null) continue;
          try
          {
            await _azureTenantAuthDetailsRepository
              .DeleteAzureTenantAuthDetails(principalId, token);
          }
          catch (Exception ex) when (!ex.IsCancellationException(token))
          {
            // don't let auth detail deletion failures derail bulk tenant deletion
            _logger.LogWarning(ex,
              "Failed to delete auth details for tenant with principal id {PrincipalId}",
              principalId);
          }
        }
      }, cancellationToken);
      return new CommandResult(true);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Failed to merge tenants");
      return new CommandResult(false, ex.Message);
    }
  }
}
