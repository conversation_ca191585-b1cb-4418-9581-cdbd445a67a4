using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Application.Commands;

public record CreateTenantPayload(
  string Name,
  bool? Active = null,
  string? PrincipalId = null,
  string? PartnerPrincipalId = null,
  List<string>? LimitToDomains = null,
  string? Slug = null,
  int? ParentTenantId = null,
  bool IsMsp = false,
  TenantPreferences? TenantPreferences = null) : ICreateTenantPayload;

public interface ICreateTenantPayload
{
  string Name { get; }
  string? PrincipalId { get; }
  string? PartnerPrincipalId { get; }
  bool? Active { get; }
  string? Slug { get; }
  int? ParentTenantId { get; }
  bool IsMsp { get; }
  TenantPreferences? TenantPreferences { get; }
  List<string>? LimitToDomains { get; }
}

public interface ICreateTenantCmd
{
  Task<Tenant> CreateTenant(
    ICreateTenantPayload payload,
    int? ownerTenantId,
    CancellationToken cancellationToken,
    bool throwOnDuplicateTenantName = false);
}

internal class CreateTenantCmd(
  UserBearingDbFactory<ImmybotDbContext> _ctxFactory,
  ICachedCollection<TenantPreferences> _cachedTenantPrefs,
  IUpdateAzureTenantLinkCmd _updateAzureTenantLinkCmd,
  IPendoEventManagementService pendoEventManagement)
  : ICreateTenantCmd
{
  public async Task<Tenant> CreateTenant(
    ICreateTenantPayload payload,
    int? ownerTenantId,
    CancellationToken cancellationToken,
    bool throwOnDuplicateTenantName = false)
  {
    if (!payload.IsMsp && ownerTenantId == null)
      throw new ValidationException("OwnerTenantId is required when creating a non-root tenant.");

    await using var dbContext = _ctxFactory();

    if (throwOnDuplicateTenantName && await dbContext.TenantNameAlreadyExists(payload.Name, cancellationToken))
    {
      throw new ValidationException(
        "A tenant with this name already exists.  Choose a different name.");
    }
    var strategy = dbContext.Database.CreateExecutionStrategy();
    var s = new { dbContext, payload, ownerTenantId, cachedTenantPrefs = _cachedTenantPrefs };
    return await strategy.ExecuteAsync(s, async (state, _token) =>
    {
      var ctxInner = state.dbContext;
      var payloadInner = state.payload;
      await using var transaction = await ctxInner.Database.BeginTransactionAsync(_token);
      var tenant = new Tenant
      {
        OwnerTenantId = state.ownerTenantId,
        IsMsp = payloadInner.IsMsp,
        Name = payloadInner.Name,
        Active = payloadInner.Active ?? false,
        TenantPreferences = payloadInner.TenantPreferences ?? new TenantPreferences { EnableOnboarding = true },
        ParentTenantId = payloadInner.ParentTenantId,
        Slug = payloadInner.Slug,
      };

      ctxInner.Tenants.Add(tenant);
      await ctxInner.SaveChangesAsync(_token);

      pendoEventManagement.Enqueue(
        PendoEventManagementService.PendoEvents.ImmyTenantCreatedEvent()
      );

      // TODO: make update link cmd take in an optional context so we can do the update in the same transaction
      await transaction.CommitAsync(_token);

      if (payloadInner.PrincipalId != null)
      {
        tenant.AzureTenantLink = await _updateAzureTenantLinkCmd.UpdateAzureTenantLink(
          new UpdateAzureTenantLinkPayload(tenant.Id,
            payloadInner.PrincipalId,
            payloadInner.PartnerPrincipalId,
            payloadInner.LimitToDomains),
          _token);
      }


      state.cachedTenantPrefs.Value.Add(tenant.TenantPreferences);
      return tenant;
    }, cancellationToken);
  }
}
