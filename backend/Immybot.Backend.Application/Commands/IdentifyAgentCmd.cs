using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.AgentIdentification;
using Immybot.Backend.Persistence;
using System.Linq;
using Immybot.Shared.Primitives;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Application.Commands;

public interface IIdentifyAgentCmd
{
  Task<OpResult> IdentifyAgent(HashSet<int> agentIds, int tenantId, CancellationToken token);
}

public class IdentifyAgentCmd : IIdentifyAgentCmd
{
  private readonly ILogger<IdentifyAgentCmd> _logger;
  private readonly IAgentIdentificationManager _agentIdentificationManager;
  private readonly Func<ImmybotDbContext> _ctxFactory;

  public IdentifyAgentCmd(
    ILogger<IdentifyAgentCmd> logger,
    IAgentIdentificationManager agentIdentificationManager,
    Func<ImmybotDbContext> ctxFactory)
  {
    _logger = logger;
    _agentIdentificationManager = agentIdentificationManager;
    _ctxFactory = ctxFactory;
  }

  public async Task<OpResult> IdentifyAgent(HashSet<int> agentIds, int tenantId, CancellationToken token)
  {
    try
    {
      // fetch all agents specified requiring manual identification
      await using var _dbContext = _ctxFactory();
      var agents = await _dbContext.ProviderAgents
        .AsNoTracking()
        .Where(a => agentIds.Contains(a.Id) && a.RequireManualIdentification)
        .ToListAsync(token);

      if (agents.Count is 0) return OpResult.Fail("No agents were found requiring manual identification");

      // ensure all selected agents are from the same provider link
      if (agents.GroupBy(a => a.ProviderLinkId).Count() > 1) return OpResult.Fail("All agents must be from the same provider link");

      var providerLinkId = agents[0].ProviderLinkId;

      // fetch provider client linked to the specified tenant
      var externalClientId = await _dbContext.ProviderClients
        .AsNoTracking()
        .Where(a => a.LinkedToTenantId == tenantId && a.ProviderLinkId == providerLinkId)
        .Select(a => a.ExternalClientId)
        .FirstOrDefaultAsync(token);

      if (externalClientId is null) return OpResult.Fail("No provider client was linked to the specified tenant");

      // assign the tenant to the agent and unmark require manual identification
      var agentIdsToUpdate = agents.Select(a => a.Id).ToList();
      await _dbContext.ProviderAgents
        .Where(a => agentIdsToUpdate.Contains(a.Id))
        .ExecuteUpdateAsync(a => a
          .SetProperty(b => b.RequireManualIdentification, false)
          .SetProperty(b => b.ExternalClientId, externalClientId), token);

      // enqueue for identification
      foreach (var agent in agents)
      {
        agent.RequireManualIdentification = false;
        agent.ExternalClientId = externalClientId;
        await _agentIdentificationManager.EnqueueAgentForIdentification(agent);
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Failed to identify agent");
      return OpResult.Fail(ex.Message);
    }

    return OpResult.Ok();
  }
}
