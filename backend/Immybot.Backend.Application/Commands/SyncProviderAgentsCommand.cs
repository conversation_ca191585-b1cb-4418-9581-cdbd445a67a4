using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Providers.Interfaces;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Application.Commands;

/// <summary>
/// This command should be used to create/update/delete provider agents in the database with the latest agent information
/// </summary>
public interface ISyncProviderAgentsCommand
{
  Task Sync(int providerLinkId, IList<IProviderAgentDetails> agents, IList<string>? clientIds = null, CancellationToken token = default);
}

public class SyncProviderAgentsCommand : ISyncProviderAgentsCommand
{
  private readonly ILogger<SyncProviderAgentsCommand> _logger;
  private readonly IProviderAgentEventHandler _providerAgentEventHandler;
  private readonly IProviderStore _providerStore;

  public SyncProviderAgentsCommand(
    ILogger<SyncProviderAgentsCommand> logger,
    IProviderAgentEventHandler providerAgentEventHandler,
    IProviderStore providerStore)
  {
    _logger = logger;
    _providerAgentEventHandler = providerAgentEventHandler;
    _providerStore = providerStore;
  }

  public async Task Sync(
    int providerLinkId,
    IList<IProviderAgentDetails> agents,
    IList<string>? clientIds = null,
    CancellationToken token = default)
  {
    var state = new Dictionary<string, object?> { ["ProviderLinkId"] = providerLinkId };
    using var _ = _logger.BeginScope(state);
    _logger.LogDebug("Starting {cmd}", nameof(SyncProviderAgentsCommand));

    try
    {
      if (clientIds?.Count > 0)
        _logger.LogDebug("Performing agent sync for clients {clients}", string.Join(",", clientIds));

      var enabledClients = _providerStore
        .GetClientsForProviderLink(providerLinkId).Using(q => q
          .Where(a => a.LinkedToTenantId != null)
          .ToDictionary(a => a.ExternalClientId, a => a));

      var forAllClients =
        clientIds is not null
        && enabledClients.Count == clientIds.Count
        && clientIds.All(a => enabledClients.ContainsKey(a));

      var knownAgents = _providerStore
        .GetAgentsForProviderLink(
          providerLinkId,
          clientIds: forAllClients ? null : clientIds,
          includeDeleted: true).Using(q => q
          .ToDictionary(a => (a.ExternalAgentId, a.ExternalClientId), a => a));

      _logger.LogDebug("Found {count} known agents", knownAgents.Count);

      var disconnectedAgents = new List<IProviderAgentDetails>();
      var connectedAgents = new List<IProviderAgentDetails>();
      var newAgents = new List<IProviderAgentDetails>();
      var updatedAgents = new List<IProviderAgentDetails>();

      foreach (var updatedAgent in agents)
      {
        // if we were passed in client ids, then don't consider agents that don't have a matching client id
        if (clientIds is not null && !clientIds.Contains(updatedAgent.ExternalClientId)) continue;

        // don't update agents for non enabled clients
        if (!enabledClients.ContainsKey(updatedAgent.ExternalClientId)) continue;

        if (knownAgents.TryGetValue((updatedAgent.ExternalAgentId, updatedAgent.ExternalClientId), out var knownAgent))
        {
          // set connected and disconnected agents
          switch (knownAgent.IsOnline)
          {
            case true when !updatedAgent.IsOnline:
              disconnectedAgents.Add(updatedAgent);
              break;
            case false when updatedAgent.IsOnline:
              connectedAgents.Add(updatedAgent);
              break;
          }

          // set updated agent - only update agents that have had specific details updated
          if (knownAgent.DeletedAt is not null ||
              knownAgent.SupportsRunningScripts != updatedAgent.SupportsRunningScripts ||
              knownAgent.AgentVersion != updatedAgent.AgentVersion ||
              knownAgent.DeviceDetails.DeviceName != updatedAgent.DeviceDetails.DeviceName ||
              knownAgent.DeviceDetails.Manufacturer != updatedAgent.DeviceDetails.Manufacturer ||
              knownAgent.DeviceDetails.OperatingSystemName != updatedAgent.DeviceDetails.OperatingSystemName ||
              knownAgent.DeviceDetails.SerialNumber != updatedAgent.DeviceDetails.SerialNumber)
          {
            updatedAgents.Add(updatedAgent);
          }
        }
        else
        {
          // set new agents
          newAgents.Add(updatedAgent);
        }
      }

      await HandledDeletedAgents(providerLinkId, knownAgents.Select(a => a.Value).ToList(), agents.ToList(), enabledClients, token);
      await HandleDisconnectedAgents(providerLinkId, disconnectedAgents, token);
      await HandleConnectedAgents(providerLinkId, connectedAgents, token);
      await HandleNewAgents(providerLinkId, newAgents, token);
      await HandleUpdatedAgents(providerLinkId, updatedAgents, token);
    }
    finally
    {
      _logger.LogDebug("Finished {cmd}", nameof(SyncProviderAgentsCommand));
    }
  }

  private async Task HandledDeletedAgents(
    int providerLinkId,
    IReadOnlyCollection<IProviderAgentDetails> knownAgents,
    IReadOnlyCollection<IProviderAgentDetails> latestAgents,
    IDictionary<string, ProviderClient> enabledClients,
    CancellationToken token)
  {
    // only consider deletion if we successfully retrieve at least 1 device
    if (!latestAgents.Any()) return;

    var knownIds = knownAgents.Select(a => (a.ExternalClientId, a.ExternalAgentId));
    var currentIds = latestAgents
      .Where(a => enabledClients.ContainsKey(a.ExternalClientId))
      .Select(a => (a.ExternalClientId, a.ExternalAgentId));
    var deletedDeviceIds = knownIds.Except(currentIds).ToList();

    _logger.LogDebug("Syncing {count} deleted agents", deletedDeviceIds.Count);

    if (deletedDeviceIds.Any())
    {
      // delete agents call
      await _providerAgentEventHandler.AgentsDeletedAsync(
        providerLinkId,
        deletedDeviceIds,
        null,
        token);
    }
  }

  private async Task HandleDisconnectedAgents(
    int providerLinkId,
    IReadOnlyCollection<IProviderAgentDetails> disconnectedAgents,
    CancellationToken token)
  {
    _logger.LogDebug("Syncing {count} disconnected agents", disconnectedAgents.Count);

    if (disconnectedAgents.Any())
    {
      var agents = disconnectedAgents.Select(a => (a.ExternalClientId, a.ExternalAgentId)).ToList();
      await _providerAgentEventHandler.AgentsDisconnectedAsync(
        providerLinkId,
        agents,
        null,
        token);
    }
  }

  private async Task HandleConnectedAgents(
    int providerLinkId,
    IReadOnlyCollection<IProviderAgentDetails> connectedAgents,
    CancellationToken token)
  {
    _logger.LogDebug("Syncing {count} connected agents", connectedAgents.Count);

    if (connectedAgents.Any())
    {
      var agents = connectedAgents.Select(a => (a.ExternalClientId, a.ExternalAgentId)).ToList();
      await _providerAgentEventHandler.AgentsConnectedAsync(
        providerLinkId,
        agents,
        null,
        token);
    }
  }

  private async Task HandleNewAgents(
    int providerLinkId,
    IReadOnlyCollection<IProviderAgentDetails> newAgents,
    CancellationToken token)
  {
    _logger.LogDebug("Syncing {count} new agents", newAgents.Count);

    if (newAgents.Any())
    {
      await _providerAgentEventHandler.AgentsCreatedAsync(
        true,
        providerLinkId,
        newAgents.ToList(),
        null,
        token);
    }
  }

  private async Task HandleUpdatedAgents(
    int providerLinkId,
    IReadOnlyCollection<IProviderAgentDetails> updatedAgents,
    CancellationToken token)
  {
    _logger.LogDebug("Syncing {count} updated agents", updatedAgents.Count);

    if (updatedAgents.Any())
    {
      await _providerAgentEventHandler.AgentsUpdatedAsync(
        providerLinkId,
        updatedAgents.ToList(),
        null,
        token);
    }
  }
}
