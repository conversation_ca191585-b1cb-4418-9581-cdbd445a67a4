using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Application.Commands;

public record UpdateAzureTenantLinkPayload(
  int TenantId,
  string? PrincipalId,
  string? PartnerPrincipalId,
  List<string>? LimitToDomains,
  bool RemoveSyncedUsers = false,
  bool UnlinkCustomers = false,
  bool RemoveCustomersSyncedUsers = false);

public interface IUpdateAzureTenantLinkCmd
{
  Task<AzureTenantLink?> UpdateAzureTenantLink(
    UpdateAzureTenantLinkPayload payload,
    CancellationToken cancellationToken);
}

internal class UpdateAzureTenantLinkCmd(
  UserBearingDbFactory<ImmybotDbContext> _ctxFactory) : IUpdateAzureTenantLinkCmd
{
  /// <summary>
  /// Creates/updates/deletes the AzureTenantLink for the given tenant. If no AzureTenant exists
  /// with the given <paramref name="payload.PrincipalId"/>, then one will be created. If
  /// <paramref name="payload.PrincipalId"/> is null, then the AzureTenantLink will be deleted. The
  /// AzureTenant will also be deleted if there are no more references to it
  /// </summary>
  /// <remarks>
  /// <paramref name="payload.RemoveSyncedUsers"/> will not remove the current logged-in user from the
  /// tenant. If <paramref name="payload.RemoveSyncedUsers"/> is true, then this method will throw an
  /// exception if the context does not have the current logged-in user set (i.e.
  /// ImmybotDbContext#UserId). This is to prevent a user from accidentally deleting their own
  /// account.
  /// </remarks>
  /// <exception cref="InvalidOperationException">
  /// Attempt to remove synced users from a non-user-bearing context
  /// </exception>
  /// /// <exception cref="InvalidOperationException">
  /// Attempt to update AzureTenantLink for non-existent tenant
  /// </exception>
  public async Task<AzureTenantLink?> UpdateAzureTenantLink(
    UpdateAzureTenantLinkPayload payload,
    CancellationToken cancellationToken)
  {
    await using var ctx = _ctxFactory();
    if (payload.RemoveSyncedUsers && ctx.UserId == null)
      throw new InvalidOperationException(
        "Cannot remove synced users with a context that does not have the current logged-in user set");
    var strategy = ctx.Database.CreateExecutionStrategy();
    return await strategy.ExecuteAsync(new { payload, ctx },
      async (state, token) =>
      {
        var ctx = state.ctx;
        var payload = state.payload;
        var tenant = ctx.GetTenantById(payload.TenantId, includeAzData: true);
        if (tenant == null) throw new InvalidOperationException("Tenant not found");

        ctx.Attach(tenant);

        var oldPrincipalId = tenant.AzureTenantLink?.AzTenantId;
        var transaction = await ctx.Database.BeginTransactionAsync(token);
        if (oldPrincipalId != null)
        {
          if ((payload.RemoveSyncedUsers || payload.RemoveCustomersSyncedUsers) &&
              ctx.UserId is { } currentUserId)
          {
            Expression<Func<Person, bool>> expr = payload.RemoveSyncedUsers switch
            {
              true when tenant.AzureTenantLink?.ShouldLimitDomains is true
                => t => t.Tenant!.AzureTenantLink!.AzTenantId == oldPrincipalId &&
                        t.TenantId == payload.TenantId,
              false when payload.RemoveCustomersSyncedUsers &&
                         tenant.AzureTenantLink?.ShouldLimitDomains is true
                => t => false,
              true when payload.RemoveCustomersSyncedUsers
                => t => t.Tenant!.AzureTenantLink!.AzTenantId == oldPrincipalId
                        || t.Tenant.AzureTenantLink!.AzureTenant!.PartnerPrincipalId ==
                        oldPrincipalId,
              true
                => t => t.Tenant!.AzureTenantLink!.AzTenantId == oldPrincipalId,
              false when payload.RemoveCustomersSyncedUsers
                => t => t.Tenant!.AzureTenantLink!.AzureTenant!.PartnerPrincipalId == oldPrincipalId,
              _ => t => false,
            };

            // Remove users linked to persons synced from the Azure tenant(s)
            await ctx.Persons.Where(expr).Where(p => !string.IsNullOrEmpty(p.AzurePrincipalId))
              .Select(p => p.User)
              .Where(u => u!.Id != currentUserId)
              .ExecuteDeleteAsync(token);

            // Remove persons synced from the Azure tenant(s)
            await ctx.Persons.Where(expr).Where(p => !string.IsNullOrEmpty(p.AzurePrincipalId))
              .Where(p => p.User!.Id != currentUserId)
              .ExecuteDeleteAsync(token);
          }

          // If this isn't the main tenant linked to an Azure tenant we're unlinking, then we don't
          // remove the Azure tenant itself, just the domain-limited link
          if (tenant.AzureTenantLink?.ShouldLimitDomains is false)
          {
            // Remove AzureErrors pertaining to the Azure tenant or its customers
            await ctx.AzureErrors
              .Where(a => a.TenantPrincipalId == oldPrincipalId ||
                          ctx.AzureTenants
                            .Where(aa => aa.PartnerPrincipalId == oldPrincipalId)
                            .Select(t => t.PrincipalId)
                            .Contains(a.TenantPrincipalId))
              .ExecuteDeleteAsync(token);

            if (payload.UnlinkCustomers)
            {
              await ctx.AzureTenants
                .Where(a => a.PartnerPrincipalId == oldPrincipalId)
                .ExecuteDeleteAsync(token);
            }
            else
            {
              await ctx.AzureTenants
                .Where(a => a.PartnerPrincipalId == oldPrincipalId)
                .ExecuteUpdateAsync(u => u
                    .SetProperty(a => a.AzureTenantType, AzTenantType.Standalone)
                    .SetProperty(a => a.PartnerPrincipalId, a => null),
                  token);
            }

            // If we're unlinking the main tenant from this az tenant, also unlink all split-domain
            // tenants linked to this az tenant
            await ctx.Tenants.Where(t => t.AzureTenantLink!.AzTenantId == oldPrincipalId)
              .Where(t => t.Id != payload.TenantId)
              .Select(t => t.AzureTenantLink!)
              .ExecuteDeleteAsync(token);
          }
        }

        if (payload.PrincipalId != null)
        {
          var tenantType = payload.PartnerPrincipalId switch
          {
            not null => AzTenantType.Customer,
            _ => AzTenantType.Standalone,
          };
          var azTenant =
            await ctx.AzureTenants.FindAsync([payload.PrincipalId], cancellationToken: token);
          if (azTenant == null)
          {
            // Add new AzureTenant to db
            azTenant = new AzureTenant
            {
              PrincipalId = payload.PrincipalId,
              AzureTenantType = tenantType,
              PartnerPrincipalId = payload.PartnerPrincipalId,
              ConsentDetails = new AzureTenantConsentDetails()
            };
            ctx.AzureTenants.Add(azTenant);
          }
          else if (payload.LimitToDomains == null)
          {
            // only update existing az tenant info if we're linking main tenant
            azTenant.AzureTenantType = tenantType;
            azTenant.PartnerPrincipalId = payload.PartnerPrincipalId;
            azTenant.ConsentDetails = new AzureTenantConsentDetails();
          }

          tenant.AzureTenantLink = new AzureTenantLink
          {
            AzTenantId = payload.PrincipalId,
            ImmyTenantId = tenant.Id,
            ShouldLimitDomains = payload.LimitToDomains?.Count is > 0,
            AzureTenant = azTenant,
          };

          if (payload.LimitToDomains?.Count is > 0)
          {
            tenant.AzureTenantLink.LimitToDomains.AddRange(payload.LimitToDomains.Select(d =>
              new AzureTenantLinkDomainFilter
              {
                ImmyTenantId = tenant.Id, AzTenantId = payload.PrincipalId, DomainName = d,
              }));
            // Default the parent tenant id of the immy tenant linked to specific domains of an
            // azure tenant to the immy tenant linked to the azure tenant as a whole, so that
            // child-propagating deployments propagate to split azure tenants by default
            tenant.ParentTenantId = await ctx.Tenants
              .Where(t =>
                t.AzureTenantLink != null &&
                t.AzureTenantLink.AzTenantId == payload.PrincipalId &&
                !t.AzureTenantLink.ShouldLimitDomains)
              .Select(t => t.Id)
              .FirstOrDefaultAsync(token);
          }
        }
        else
        {
          tenant.AzureTenantLink = null;
        }

        await ctx.SaveChangesAsync(token);

        // Remove AzureTenants that are not linked to any Immy tenant
        await ctx.AzureTenants
          .Where(a => a.AzureTenantLinks.Count == 0)
          .ExecuteDeleteAsync(token);

        await transaction.CommitAsync(token);

        if (tenant.AzureTenantLink?.AzureTenant != null)
        {
          // Populate the links list on the azure tenant since it is not loaded by default
          // and some parts of the app need it
          await ctx.Entry(tenant.AzureTenantLink.AzureTenant)
            .Collection(a => a.AzureTenantLinks)
            .LoadAsync(token);
        }

        return tenant.AzureTenantLink;
      },
      cancellationToken);
  }
}
