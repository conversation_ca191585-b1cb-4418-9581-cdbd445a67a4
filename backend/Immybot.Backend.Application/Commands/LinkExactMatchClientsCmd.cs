using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Application.Commands
{
  public record LinkExactMatchClientsPayload(
    int ProviderId,
    bool SyncAfterLink);

  public interface ILinkExactMatchClientsCmd
  {
    Task<List<ProviderClient>> Run(LinkExactMatchClientsPayload payload, CancellationToken cancellationToken);
  }

  internal class LinkExactMatchClientsCmd : ILinkExactMatchClientsCmd
  {
    private readonly Func<ImmybotDbContext> _ctxFactory;
    private readonly IProviderActions _providerActions;

    public LinkExactMatchClientsCmd(
        Func<ImmybotDbContext> ctxFactory,
        IProviderActions providerActions)
    {
      _ctxFactory = ctxFactory;
      _providerActions = providerActions;
    }

    public async Task<List<ProviderClient>> Run(LinkExactMatchClientsPayload payload, CancellationToken cancellationToken)
    {
      await using var ctx = _ctxFactory();
      var link = ctx.GetProviderLink(payload.ProviderId, includeClients: true);
      if (link is null) throw new ProviderLinkNotFoundException();

      var linkedClients = await LinkMatchingClientsAsync(link, cancellationToken);

      if (payload.SyncAfterLink)
      {
        await _providerActions.SyncProviderAgents(
          link,
          clientIds: link.ProviderClients.Select(c => c.ExternalClientId).ToList(),
          cancellationToken);
      }

      return linkedClients;
    }

    private async Task<List<ProviderClient>> LinkMatchingClientsAsync(ProviderLink link, CancellationToken cancellationToken)
    {
      // list of clients that we will match to tenants
      var clientsToUpdate = new List<ProviderClient>();

      // fetch all tenant names
      await using var ctx = _ctxFactory();
      var tenants = await ctx.Tenants
        .AsNoTracking()
        .Select(a => new { a.Id, a.Name })
        .ToDictionaryAsync(a => a.Name.ToLowerInvariant(), a => a.Id, cancellationToken);

      // fetch all unlinked clients
      var unlinkedClients = link.ProviderClients
        .Where(client => client.ProviderLinkId == link.Id && client.LinkedToTenantId == null)
        .Select(a => new { a.ProviderLinkId, a.LinkedToTenantId, a.ExternalClientId, a.ExternalClientName })
        .ToList();

      // loop over all unlinked clients and find matching clients
      foreach (var unlinkedClient in unlinkedClients)
      {
        if (unlinkedClient.ExternalClientName is null) continue;
        if (!tenants.TryGetValue(unlinkedClient.ExternalClientName.ToLowerInvariant(), out var tenantId)) continue;

        clientsToUpdate.Add(new ProviderClient()
        {
          ProviderLinkId = unlinkedClient.ProviderLinkId,
          ExternalClientId = unlinkedClient.ExternalClientId,
          ExternalClientName = unlinkedClient.ExternalClientName,
          LinkedToTenantId = tenantId
        });
      }

      // bulk update the client's tenant id and reset the has completed initial agent sync flag
      await ctx.ProviderClients.BulkUpdateAsync(
        clientsToUpdate,
        bulkOperation =>
        {
          bulkOperation.ColumnPrimaryKeyExpression = c => new { c.ProviderLinkId, c.ExternalClientId };
          bulkOperation.ColumnInputExpression = c => new { c.LinkedToTenantId, c.HasCompletedInitialAgentSync };
        },
        cancellationToken);

      return clientsToUpdate;
    }
  }
}
