using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Notifications;
using Immybot.Backend.Application.Notifications.Converters;
using Immybot.Backend.Domain.Infrastructure;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.Application.Infrastructure;

public static class NotificationRegistration
{
  public static IServiceCollection AddNotificationServices(this IServiceCollection services)
  {
    // add all notification converters here
    services.AddNotificationConverter<AccessRequestedEvent, AccessRequestedApplicationEventToNotificationConverter>();
    services.AddNotificationConverter<AccessRequestAcknowledgedEvent, AccessRequestAcknowledgedEventConverter>();
    services.AddNotificationConverter<AzureErrorEvent, AzureErrorNotificationConverter>();
    services.AddNotificationConverter<AzureTenantProblemsDetectedEvent, AzureTenantProblemsDetectedNotificationConverter>();
    services.AddNotificationConverter<AzureTenantProblemsClearedEvent, AzureTenantProblemsClearedEventConverter>();
    services.AddNotificationConverter<AzureCustomerPreconsentFinishedEvent, AzureCustomerPreconsentFinishedNotificationConverter>();
    services.AddNotificationConverter<AzureMultiCustomerPreconsentFinishedEvent, AzureMultiCustomerPreconsentFinishedNotificationConverter>();
    services.AddNotificationConverter<AzureMultiCustomerPreconsentFailedEvent, AzureMultiCustomerPreconsentFailedNotificationConverter>();
    services.AddNotificationConverter<IntegrationUnhealthyEvent, IntegrationUnhealthyEventConverter>();
    services.AddNotificationConverter<IntegrationHealthyEvent, IntegrationHealthyEventConverter>();
    services.AddNotificationConverter<IntegrationRecommendationEvent, IntegrationRecommendationEventConverter>();
    services.AddNotificationConverter<UnacknowledgedDeploymentsEvent, UnacknowledgedDeploymentEventsConverter>();
    services.AddNotificationConverter<LargeScriptOutputEvent, LargeScriptOutputEventConverter>();
    services.AddNotificationConverter<ProviderAgentsRequiringManualDecisionEvent, ProviderAgentsRequiringManualDecisionEventConverter>();
    services.AddNotificationConverter<ImmyBotUpdateRequestedEvent, ImmyBotUpdateRequestedEventConverter>();
    services.AddNotificationConverter<ChangeRequestCreatedOrUpdatedEvent, ChangeRequestCreatedOrUpdatedEventConverter>();
    services.AddNotificationConverter<ChangeRequestAcknowledgedEvent, ChangeRequestAcknowledgedEventConverter>();
    services.AddNotificationConverter<FeatureUsageExceededEvent, FeatureUsageExceededEventNotificationConverter>();
    services.AddNotificationConverter<ScheduleFailedEvent, ScheduleFailedEventConverter>();

    // factory for generating notifications
    services.AddSingleton<INotificationFactory, NotificationFactory>();

    // service for saving notifications
    services.AddHostedService<INotificationSaverService, NotificationSaverService>();

    // service for converting application events to notifications
    services.AddHostedService<IApplicationEventToNotificationService, ApplicationEventToNotificationService>();

    return services;
  }

  internal static void AddNotificationConverter<T, S>(
    this IServiceCollection services) where T : ApplicationEvent where S : class, IApplicationEventToNotificationConverter<T>
  {
    // The NotificationFactory needs to access the converter by type, so register it both by
    // interface and by type
    services.AddTransient<S>();
    services.AddTransient<IApplicationEventToNotificationConverter<T>, S>();
  }
}
