using Immybot.Backend.Application.Actions;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Lib;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.Application.Infrastructure;

public static class CommandDependencyInjection
{
  public static void RegisterCommands(this IServiceCollection services)
  {
    services.AddTransient<IRerunMaintenanceSessionCmd, RerunMaintenanceSessionCmd>();
    services.AddTransient<IResumeMaintenanceSessionCmd, ResumeMaintenanceSessionCmd>();
    services.AddTransient<IRerunMaintenanceActionCmd, RerunMaintenanceActionCmd>();
    services.AddTransient<IDeleteProviderLinkCmd, DeleteProviderLinkCmd>();
    services.AddTransient<IInventoryDeviceCmd, InventoryDeviceCmd>();
    services.AddTransient<IFindFunctionScriptCmd, FindFunctionScriptCmd>();
    services.AddTransient<IFindScriptByNameCmd, FindScriptByNameCmd>();
    services.AddTransient<IRunScheduleCmd, RunScheduleCmd>();
    services.AddTransient<IUnlinkProviderClientFromTenantCmd, UnlinkProviderClientFromTenantCmd>();
    services.AddScoped<IDeleteTenantsCmd, DeleteTenantsCmd>();
    services.AddScoped<IMergeTenantsCmd, MergeTenantsCmd>();
    services.AddTransient<IUpdateRecommendedApprovalCmd, UpdateRecommendedApprovalsCmd>();
    services.AddTransient<IOverrideTargetAssignmentCmd, OverrideTargetAssignmentCmd>();
    services.AddTransient<ISendTestBrandingEmailCmd, SendTestBrandingEmailCmd>();
    services.AddTransient<IDeleteComputerCmd, DeleteComputerCmd>();
    services.AddTransient<IRestoreComputersCmd, RestoreComputersCmd>();
    services.AddTransient<IDeleteProviderAgentsCmd, DeleteProviderAgentsCmd>();
    services.AddTransient<IPendingAgentResolverActions, PendingAgentResolverActions>();
    services.AddTransient<IPendingComputerResolver, PendingComputerResolver>();
    services.AddTransient<IAgentIdentificationActions, AgentIdentificationActions>();
    services.AddScoped<ICreateTenantCmd, CreateTenantCmd>();
    services.AddScoped<ILinkExactMatchClientsCmd, LinkExactMatchClientsCmd>();
    services.AddScoped<IUpdateAzureTenantLinkCmd, UpdateAzureTenantLinkCmd>();
    services.AddTransient<IRetrieveMaintenanceItemOrdersCmd, RetrieveMaintenanceItemOrdersCmd>();
    services.AddTransient<IUpdateMaintenanceItemOrderCmd, UpdateMaintenancePriorityCmd>();
    services.AddTransient<IRequestAccessCommand, RequestAccessCommand>();
    services.AddTransient<IGrantAccessCommand, GrantAccessCommand>();
    services.AddTransient<IDenyAccessCommand, DenyAccessCommand>();
    services.AddTransient<IDuplicateAssignmentCmd, DuplicateAssignmentCmd>();
    services.AddTransient<IMergeComputersCmd, MergeComputersCmd>();
    services.AddTransient<IChangeTenantForComputersCmd, ChangeTenantForComputersCmd>();
    services.AddTransient<ISyncAzureUsersAndImmyPersonsCmd, SyncAzureUsersAndImmyPersonsCmd>();
    services.AddTransient<IGetCommandListCmd, GetCommandListCmd>();
    services.AddTransient<IGetCommandSyntaxCmd, GetCommandSyntaxCmd>();
    services.AddTransient<IResolveAssignmentsForMaintenanceItemCmd, ResolveAssignmentsForMaintenanceItemCmd>();
    services.AddTransient<IMigrateToSupersedingAssignmentCmd, MigrateToSupersedingAssignmentCmd>();
    services.AddTransient<ISyncProviderAgentsCommand, SyncProviderAgentsCommand>();
    services.AddScoped<IUpdateAzureTenantAuthDetailsCmd, UpdateAzureTenantAuthDetailsCmd>();
    services.AddTransient<IIdentifyAgentCmd, IdentifyAgentCmd>();
    services.AddTransient<ISetPreflightScriptEnablementCmd, SetPreflightScriptEnablementCmd>();
    services
      .AddTransient<ITargetAssignmentCleanupMigrationCmd, TargetAssignmentCleanupMigrationCmd>();
  }
}
