using System;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Immybot.Backend.Application.Infrastructure;

public static class IServiceProviderExtensions
{
  /// <summary>
  /// Register a hosted service exposing the interface in DI
  /// </summary>
  /// <param name="services"></param>
  /// <typeparam name="THostedService"></typeparam>
  /// <typeparam name="TImplementation"></typeparam>
  /// <returns></returns>
  /// <exception cref="InvalidOperationException"></exception>
  public static IServiceCollection AddHostedService<THostedService,
    [DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors)] TImplementation>(
    this IServiceCollection services)
    where THostedService : class, IHostedService
    where TImplementation : class, THostedService
  {
    services.AddSingleton<THostedService, TImplementation>();
    return services.AddHostedService<TImplementation>(sp => sp.GetRequiredService<THostedService>() as TImplementation ?? throw new InvalidOperationException());
  }
}
