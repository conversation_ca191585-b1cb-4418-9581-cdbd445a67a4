using System;
using Hangfire.Common;
using Hangfire.States;
using Hangfire.Storage;

namespace Immybot.Backend.Application.Infrastructure;

[AttributeUsage(AttributeTargets.Method)]
public class HangfireExpirationTimeAttribute : JobFilterAttribute, IApplyStateFilter
{
  private readonly double _seconds;

  public HangfireExpirationTimeAttribute(double seconds)
  {
    _seconds = seconds;
  }

  public void OnStateApplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
  {
    context.JobExpirationTimeout = TimeSpan.FromSeconds(_seconds);
  }

  public void OnStateUnapplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
  {
    context.JobExpirationTimeout = TimeSpan.FromSeconds(_seconds);
  }
}
