using Microsoft.Extensions.Logging;
using System;

namespace Immybot.Backend.Application.Infrastructure;

// TODO: Switch to using this paradigm (the "LoggerMessage" paradigm)
// as detailed in ASP.NET Core's "High-performance logging" docs:
// https://docs.microsoft.com/en-us/aspnet/core/fundamentals/logging/loggermessage?view=aspnetcore-2.2
// Basically, anywhere we're calling, e.g., `_logger.LogDebug`, we should
// instead add that here as an action, and call the action instead
public static class LoggerExtensions
{
  private static readonly Action<ILogger, string, Exception?> _somethingHappened = LoggerMessage.Define<string>(
    LogLevel.Information,
    new EventId(1, nameof(SomethingHappened)),
    "Something happened: '{Foobar}'");

  public static void SomethingHappened(this ILogger logger, string foobar)
  {
    _somethingHappened(logger, foobar, null);
  }
}
