using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Shared.Extensions;
using Immybot.Shared.Primitives;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Immybot.Backend.Application.Stores;

public interface IProviderStore
{
  DisposableValue<IQueryable<ProviderAgent>> GetAgentsForProviderLink(int providerLinkId, IList<string>? clientIds = null, bool includeDeleted = false);
  DisposableValue<IQueryable<ProviderClient>> GetClientsForProviderLink(int providerLinkId);
  DisposableValue<IQueryable<ProviderLink>> GetProviderLinks(bool includeClients = false, bool includeClientTenants = false, bool includeDisabledLinks = false, Guid? providerTypeId = null);
  ProviderLink? GetProviderLink(int providerLinkId, bool includeClients = false, bool includeClientTenants = false, bool includeDisabledLinks = false);
  Task<ProviderAgent?> GetProviderAgent(int providerAgentId, CancellationToken token);
  DisposableValue<IQueryable<ProviderAgent>> GetAgentsInProviderLinks(IEnumerable<int> providerLinkIds);
  Task UpdateProviderAgentsWithComputerIds(IDictionary<int, int> providerAgentIdsToComputerIds, CancellationToken token);
  Task<List<string>> GetMappedClientIdsForProviderLink(int providerLinkId, CancellationToken token);
  Task<List<int>> GetComputersThatCanCurrentlyRunScripts(List<int> computerIds, CancellationToken token);
  Task UnsetProviderAgentComputerIds(IEnumerable<int> providerAgentIds, CancellationToken token);
}

public class ProviderStore(Func<ImmybotDbContext> ctxFactory) : IProviderStore
{
  public DisposableValue<IQueryable<ProviderAgent>> GetAgentsForProviderLink(int providerLinkId, IList<string>? clientIds = null, bool includeDeleted = false) =>
    ctxFactory.CreateDisposableValue(ctx => ctx
      .GetAgentsForProviderLink(providerLinkId)
      .Pipe(q => clientIds is not null ? q.Where(a => clientIds.Contains(a.ExternalClientId)) : q)
      .PipeIf(includeDeleted, q => q.IgnoreQueryFilters()));

  public DisposableValue<IQueryable<ProviderClient>> GetClientsForProviderLink(int providerLinkId) =>
    ctxFactory.CreateDisposableValue(ctx => ctx.GetClientsForProviderLink(providerLinkId));

  public DisposableValue<IQueryable<ProviderLink>> GetProviderLinks(bool includeClients = false, bool includeClientTenants = false, bool includeDisabledLinks = false, Guid? providerTypeId = null) =>
    ctxFactory.CreateDisposableValue(ctx =>
      ctx.GetProviderLinks(includeClients, includeClientTenants, includeDisabledLinks, providerTypeId));

  public ProviderLink? GetProviderLink(int providerLinkId, bool includeClients = false, bool includeClientTenants = false, bool includeDisabledLinks = false)
  {
    using var ctx = ctxFactory.Invoke();
    return ctx.GetProviderLink(providerLinkId, includeOwnerTenant: false, includeClients: includeClients, includeClientTenants: includeClientTenants, includeLinkedProviders: false, includeLinkedFromProviders: false);
  }

  public Task<ProviderAgent?> GetProviderAgent(int providerAgentId, CancellationToken token) =>
    ctxFactory.With(ctx => ctx.ProviderAgents
      .IgnoreQueryFiltersExceptSoftDelete()
      .AsNoTracking()
      .Where(x => x.Id == providerAgentId)
      .Include(x => x.ProviderLink)
      .Include(x => x.Computer)
      .FirstOrDefaultAsync(token));

  public DisposableValue<IQueryable<ProviderAgent>> GetAgentsInProviderLinks(IEnumerable<int> providerLinkIds) =>
    ctxFactory.CreateDisposableValue(ctx => ctx
      .GetProviderAgents()
      .Where(a => providerLinkIds.Contains(a.ProviderLinkId)));

  public async Task UpdateProviderAgentsWithComputerIds(IDictionary<int, int> providerAgentIdsToComputerIds, CancellationToken token)
  {
    var updates = providerAgentIdsToComputerIds
      .Select(a => new ProviderAgent { Id = a.Key, ComputerId = a.Value, ExternalClientId = "", ExternalAgentId = "" });
    await ctxFactory.With(ctx => ctx.ProviderAgents.BulkUpdateAsync(
      updates,
      bulkOperation =>
      {
        bulkOperation.ColumnPrimaryKeyExpression = c => c.Id;
        bulkOperation.ColumnInputExpression = c => new { c.ComputerId };
      },
      token));
  }

  public async Task<List<string>> GetMappedClientIdsForProviderLink(int providerLinkId, CancellationToken token) =>
    await ctxFactory.With(ctx => ctx
      .ProviderClients
      .AsNoTracking()
      .Where(a => a.ProviderLinkId == providerLinkId && a.LinkedToTenantId != null)
      .Select(a => a.ExternalClientId)
      .ToListAsync(token));

  public async Task<List<int>> GetComputersThatCanCurrentlyRunScripts(List<int> computerIds, CancellationToken token) =>
    await ctxFactory.With(ctx => ctx
      .ProviderAgents
      .AsNoTracking()
      .TagForTelemetry()
      .Where(a =>
        a.ProviderLink!.HealthStatus == HealthStatus.Healthy && !a.ProviderLink.Disabled && a.IsOnline &&
        a.ComputerId != null && computerIds.Contains(a.ComputerId.Value) && a.SupportsRunningScripts)
      .Select(a => a.ComputerId!.Value)
      .ToListAsync(token));

  public async Task UnsetProviderAgentComputerIds(IEnumerable<int> providerAgentIds, CancellationToken token)
  {
    var updates = providerAgentIds
      .Select(a => new ProviderAgent { Id = a, ComputerId = null, ExternalAgentId = "", ExternalClientId = "" });
    await ctxFactory.With(ctx => ctx.ProviderAgents.BulkUpdateAsync(
      updates,
      bulkOperation =>
      {
        bulkOperation.ColumnPrimaryKeyExpression = c => c.Id;
        bulkOperation.ColumnInputExpression = c => new { c.ComputerId };
      },
      token));
  }
}
