using System;
using System.Linq;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Application.Actions;

internal class MaintenanceSessionInitializationActions(Func<ImmybotDbContext> _ctxFactory) : IMaintenanceSessionInitializationActions
{
  public MaintenanceSession InitializeComputerMaintenanceSession(int computerId, bool isFullMaintenance = false)
  {
    var computer = GetComputer(computerId);

    var session = new MaintenanceSession()
    {
      ComputerId = computerId,
      TenantId = computer.TenantId,
      SessionStatus = SessionStatus.Created,
      FullMaintenance = isFullMaintenance
    };

    if (isFullMaintenance)
    {
      session.Stages.Add(new MaintenanceSessionStage
      {
        Type = SessionStageType.AgentUpdates,
        StageStatus = SessionStatus.Created,
      });

      if (computer.OnboardingStatus is ComputerOnboardingStatus.NeedsOnboarding or ComputerOnboardingStatus.Onboarding)
      {
        session.Onboarding = true;
        session.Stages.Add(new MaintenanceSessionStage
        {
          Type = SessionStageType.Onboarding,
          StageStatus = SessionStatus.Created,
        });
      }
    }

    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Resolution,
      StageStatus = SessionStatus.Created,
    });
    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Detection,
      StageStatus = SessionStatus.Created,
    });
    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Execution,
      StageStatus = SessionStatus.Created,
    });

    return session;
  }

  public MaintenanceSession InitializeDetectionOnlyTenantMaintenanceSession(int tenantId)
  {
    var session = new MaintenanceSession()
    {
      TenantId = tenantId,
      SessionStatus = SessionStatus.Created,
    };

    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Resolution,
      StageStatus = SessionStatus.Created,
    });

    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Detection,
      StageStatus = SessionStatus.Created,
    });

    return session;
  }

  public MaintenanceSession InitializeDetectionOnlyComputerMaintenanceSession(int computerId)
  {
    var computer = GetComputer(computerId);

    var session = new MaintenanceSession()
    {
      ComputerId = computerId,
      TenantId = computer.TenantId,
      SessionStatus = SessionStatus.Created,
    };

    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Resolution,
      StageStatus = SessionStatus.Created,
    });

    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Detection,
      StageStatus = SessionStatus.Created,
    });

    return session;
  }

  public MaintenanceSession InitializeDetectionOnlyPersonMaintenanceSession(int personId)
  {
    var session = new MaintenanceSession()
    {
      PersonId = personId,
      TenantId = GetPersonTenantId(personId),
      SessionStatus = SessionStatus.Created,
    };

    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Resolution,
      StageStatus = SessionStatus.Created,
    });

    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Detection,
      StageStatus = SessionStatus.Created,
    });

    return session;
  }

  public MaintenanceSession InitializeResolutionOnlyComputerMaintenanceSession(int computerId)
  {
    var computer = GetComputer(computerId);

    var session = new MaintenanceSession()
    {
      ComputerId = computerId,
      TenantId = computer.TenantId,
      SessionStatus = SessionStatus.Created,
    };
    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Resolution,
      StageStatus = SessionStatus.Created,
    });

    return session;
  }

  public MaintenanceSession InitializeInventoryOnlyComputerMaintenanceSession(int computerId)
  {
    var computer = GetComputer(computerId, allowInventoryOnExcluded: true);

    var session = new MaintenanceSession()
    {
      ComputerId = computerId,
      TenantId = computer.TenantId,
      SessionStatus = SessionStatus.Created,
    };
    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Inventory,
      StageStatus = SessionStatus.Created,
    });

    return session;
  }

  public MaintenanceSession InitializeExecutionOnlyComputerMaintenanceSession(int computerId)
  {
    var computer = GetComputer(computerId);

    var session = new MaintenanceSession()
    {
      ComputerId = computerId,
      TenantId = computer.TenantId,
      SessionStatus = SessionStatus.Created,
    };
    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Execution,
      StageStatus = SessionStatus.Created,
    });

    return session;
  }

  public MaintenanceSession InitializeTenantMaintenanceSession(int tenantId, bool isFullMaintenance = false)
  {
    var session = new MaintenanceSession()
    {
      TenantId = tenantId,
      SessionStatus = SessionStatus.Created,
      FullMaintenance = isFullMaintenance
    };

    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Resolution,
      StageStatus = SessionStatus.Created,
    });

    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Detection,
      StageStatus = SessionStatus.Created,
    });
    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Execution,
      StageStatus = SessionStatus.Created,
    });

    return session;
  }

  public MaintenanceSession InitializePersonMaintenanceSession(int personId, bool isFullMaintenance = false)
  {
    var session = new MaintenanceSession()
    {
      PersonId = personId,
      TenantId = GetPersonTenantId(personId),
      SessionStatus = SessionStatus.Created,
      FullMaintenance = isFullMaintenance
    };

    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Resolution,
      StageStatus = SessionStatus.Created,
    });

    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Detection,
      StageStatus = SessionStatus.Created,
    });
    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.Execution,
      StageStatus = SessionStatus.Created,
    });

    return session;
  }

  public MaintenanceSession InitializeAgentUpdatesOnlyComputerMaintenanceSession(int computerId)
  {
    var computer = GetComputer(computerId);

    var session = new MaintenanceSession()
    {
      ComputerId = computerId,
      TenantId = computer.TenantId,
      SessionStatus = SessionStatus.Created,
    };
    session.Stages.Add(new MaintenanceSessionStage
    {
      Type = SessionStageType.AgentUpdates,
      StageStatus = SessionStatus.Created,
    });

    return session;
  }

  private sealed record ComputerDto(int TenantId, bool ExcludeFromMaintenance, ComputerOnboardingStatus OnboardingStatus);

  private ComputerDto GetComputer(int computerId, bool allowInventoryOnExcluded = false)
  {
    using var ctx = _ctxFactory();
    var computer = ctx.Computers
      .AsNoTracking()
      .Where(a => a.Id == computerId)
      .Select(a => new ComputerDto(a.TenantId, a.ExcludeFromMaintenance, a.OnboardingStatus))
      .FirstOrDefault();
    if (computer is null)
      throw new ComputerNotFoundException($"Computer #{computerId} does not exist in the database.");
    if (computer.ExcludeFromMaintenance && !allowInventoryOnExcluded)
      throw new InvalidOperationException($"Computer #{computerId} is excluded from maintenance.");
    return computer;
  }

  private int GetPersonTenantId(int personId)
  {
    using var ctx = _ctxFactory();
    var tenantId = ctx.Persons
      .AsNoTracking()
      .Where(a => a.Id == personId)
      .Select(a => a.TenantId)
      .FirstOrDefault();

    if (tenantId is 0)
      throw new PersonNotFoundException($"Person #{personId} does not exist in the database.");

    return tenantId;
  }
}
