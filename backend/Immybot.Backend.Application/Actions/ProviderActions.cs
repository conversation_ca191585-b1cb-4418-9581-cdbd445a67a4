using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Management.Automation;
using System.Management.Automation.Language;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using DotNext;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DynamicProviders;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Jobs;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Policies;
using Immybot.Backend.Application.Lib.Providers;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Domain.Providers.Attributes;
using Immybot.Backend.Persistence;
using Immybot.Backend.Providers.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Registry;
using Platform = Immybot.Backend.Domain.Models.Platform;
using SemanticVersion = NuGet.Versioning.SemanticVersion;
namespace Immybot.Backend.Application.Actions;

internal class ProviderActions(
    ILogger<ProviderActions> _logger,
    Func<ImmybotDbContext> _ctxFactory,
    IProviderFactory _providerFactory,
    IProviderRegistrationService _providerRegistrationService,
    IProviderLinkDataConsistencyLocker _providerLinkLocker,
    IProviderAgentEventHandler _providerEventHandler,
    ICachedSingleton<ProviderLinkNames> _linkNamesCache,
    IServiceScopeFactory _serviceScopeFactory,
    IPolicyCacheStore _policyCacheStore,
    IPolicyRegistry<string> _policyRegistry) : IProviderActions
{
  private static ProviderTypeDto MakeProviderTypeDto(
    ProviderMetadata metadata,
    bool includeLinkFormSchema)
  {
    return new ProviderTypeDto(
        metadata.ProviderTypeId,
        metadata.Source,
        metadata.IsDynamic,
        metadata.Tag,
        metadata.DisplayName,
        includeLinkFormSchema ? metadata.ConfigurationForm : null,
        metadata.LogoSrc,
        metadata.DocsUrl,
        metadata.ScreenShareLogoSrc,
        metadata.CanManage,
        metadata.DeviceGroupMetadatas
          .Select(g => new DeviceGroupTypeDto(
            g.DeviceGroupTypeId,
            metadata.ProviderTypeId,
            g.DisplayName,
            g.Description,
            g.TargetScope))
          .ToList(),
        metadata.ClientGroupMetadatas
          .Select(g => new ClientGroupTypeDto(
            g.ClientGroupTypeId,
            metadata.ProviderTypeId,
            g.DisplayName,
            g.Description))
          .ToList(),
        metadata.SupportsDeviceUpdating,
        metadata.ProviderCapabilities,
        metadata.AgentUpdateFormSchema,
        metadata.SupportedCrossProviderClientLinkages,
        metadata.SupportedCrossProviderInitializationLinkages);
  }

  public ICollection<ProviderTypeDto> GetAllProviderTypes(
    bool includeLinkFormSchemas = false)
  {
    return _providerFactory.GetAllProviderTypes()
      .Select(t => MakeProviderTypeDto(t, includeLinkFormSchemas))
      .ToList();
  }

  /// <exception cref="MissingProviderTypeException"></exception>
  public ProviderTypeDto GetProviderType(
    Guid providerTypeId,
    bool includeLinkFormSchema = false)
  {
    var t = _providerFactory.GetProviderMetadata(providerTypeId);
    return MakeProviderTypeDto(t, includeLinkFormSchema);
  }

  public Type? GetProviderFormType(Guid providerTypeId)
  {
    var metadata = _providerFactory.GetProviderMetadata(providerTypeId);
    return metadata.ProviderAttribute is ProviderAttribute { FormType: var formType } ? formType : null;
  }

  public (ParamBlockAst, NamedBlockAst?) GetDynamicProviderParamBlock(Guid providerTypeId)
  {
    var registration = _providerRegistrationService.GetProviderRegistrations().FirstOrDefault(a => a.ProviderTypeId == providerTypeId);
    if (registration is null) throw new MissingProviderTypeException($"Provider with id {providerTypeId} was not found");
    if (registration is not IDynamicProviderRegistration dynamicProviderRegistration) throw new MissingProviderTypeException($"Provider with id {providerTypeId} is not a dynamic provider");
    return (dynamicProviderRegistration.ParamBlock, dynamicProviderRegistration.DynamicParamBlock);
  }

  public async Task SyncProviderAgents(
    ProviderLink link,
    ICollection<string>? clientIds = null,
    CancellationToken token = default)
  {
    var provider = await GetProvider(link, token);
    switch (provider)
    {
      case ISupportsSyncingAgentsOnDemand syncProvider:
        await syncProvider.SyncAgents(clientIds, token);
        break;
      case ISupportsListingAgents:
        var job = new SyncProviderAgentsJob(_serviceScopeFactory, link.Id, clientIds);
        Task.Run(async () =>
        {
          await job.Run(token);
        }, token).Forget();
        break;
    }
  }

  public async Task<string?> GetTenantInstallToken(ProviderLink link, string clientId, CancellationToken token)
  {
    var provider = await GetProvider(link, token);
    // provider will come back null when it is disabled
    if (provider is null)
    {
      throw new NotSupportedException("The integration is disabled.");
    }
    if (provider is not ISupportsTenantInstallToken installTokenProvider)
    {
      throw new NotSupportedException("Retrieving a tenant install token is not supported by this integration.");
    }
    return await installTokenProvider.GetTenantInstallToken(clientId, token);
  }

  public async Task<string?> GetTenantUninstallToken(ProviderLink link, string clientId, CancellationToken token)
  {
    var provider = await GetProvider(link, token);
    // provider will come back null when it is disabled
    if (provider is null)
    {
      throw new NotSupportedException("The integration is disabled.");
    }
    if (provider is not ISupportsTenantUninstallToken installTokenProvider)
    {
      throw new NotSupportedException("Retrieving a tenant uninstall token is not supported by this integration.");
    }
    return await installTokenProvider.GetTenantUninstallToken(clientId, token);
  }

  public async Task<string?> GetAgentUninstallToken(ProviderLink link, string agentId, CancellationToken token)
  {
    var provider = await GetProvider(link, token);
    // provider will come back null when it is disabled
    if (provider is null)
    {
      throw new NotSupportedException("The integration is disabled.");
    }
    if (provider is not ISupportsAgentUninstallToken installTokenProvider)
    {
      throw new NotSupportedException("Retrieving a agent uninstall token is not supported by this integration.");
    }
    return await installTokenProvider.GetAgentUninstallToken(agentId, token);
  }

  public async Task<DynamicVersion[]> GetDynamicVersions(ProviderLink link, string clientId, CancellationToken token)
  {
    var provider = await GetProvider(link, token);
    // provider will come back null when it is disabled
    if (provider is null)
    {
      throw new NotSupportedException("The integration is disabled.");
    }

    if (provider is not ISupportsDynamicVersions supportsDynamicVersions)
    {
      throw new NotSupportedException("Dynamic versions are not supported by this integration.");
    }
    return await supportsDynamicVersions.GetDynamicVersions(clientId, token);
  }

  public async Task<Hashtable> GetAuthHeader(ProviderLink link, CancellationToken token)
  {
    var provider = await GetProvider(link, token);
    // provider will come back null when it is disabled
    if (provider is null)
    {
      throw new NotSupportedException("The integration is disabled.");
    }

    if (provider is not ISupportsAuthenticatedDownload supportsAuthenticatedDownload)
    {
      throw new NotSupportedException("Authenticated downloads are not supported by this integration.");
    }
    return await supportsAuthenticatedDownload.GetAuthHeader(token);
  }

  public async Task<IRunScriptProvider?> GetRunScriptProvider(
    ProviderLink link,
    CancellationToken token,
    TimeSpan? timeout = null)
  {
    var provider = await _providerFactory.GetProvider(link, token, timeout: timeout);
    return provider as IRunScriptProvider;
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task<IProvider?> GetProvider(ProviderLink link,
    CancellationToken token,
    TimeSpan? timeout = null,
    bool allowUnhealthy = false,
    bool noProviderConstruction = false)
  {
    _logger.LogTrace($"{nameof(GetProvider)}");
    return await _providerFactory.GetProvider(link,
      token,
      timeout: timeout,
      allowUnhealthy: allowUnhealthy,
      noProviderConstruction: noProviderConstruction);
  }

  public async Task<IProvider?> ReloadProvider(ProviderLink link, CancellationToken token, TimeSpan? timeout = null)
  {
    _logger.LogTrace($"{nameof(ReloadProvider)}");
    await _providerFactory.RemoveProvider(link.Id);
    var provider = await GetProvider(link, token, timeout: timeout, allowUnhealthy: true);

    // invalidate the provider inventory task cache policy whenever a provider is reloaded
    if (_policyCacheStore.TryGetValue(_policyRegistry, PolicyKeys.ProviderInventoryTaskCachePolicy, out var cache))
    {
      cache.Remove(ContextKeys.ProviderInventoryTasks);
    }

    return provider;
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task RecreateProvider(ProviderLink link, CancellationToken token)
  {
    var cache = _linkNamesCache.Value;
    cache.AddOrUpdate(link.Id, _ => link.Name, (_, _) => link.Name);
    _linkNamesCache.Value = cache;
    await _providerFactory.GetProvider(link, token, forceRecreate: true);
  }

  public Task RemoveProvider(int linkId)
  {
    var cache = _linkNamesCache.Value;
    cache.Remove(linkId, out _);
    _linkNamesCache.Value = cache;
    return _providerFactory.RemoveProvider(linkId, disallowFurtherUse: true);
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task<ICollection<IDeviceGroup>> GetDeviceGroups(
    ProviderLink link,
    Guid deviceGroupTypeId,
    CancellationToken token,
    string? clientId = null)
  {
    return await _providerFactory.GetDeviceGroups(link, deviceGroupTypeId, token, clientId);
  }

  public Dictionary<Guid, string> GetAllDeviceGroupTypeNames()
  {
    return _providerFactory.GetAllDeviceGroupTypes()
      .ToDictionary(m => m.DeviceGroupTypeId, m => m.DisplayName);
  }

  public Type? GetDeviceGroupType(Guid providerTypeId, Guid deviceGroupTypeId)
  {
    return _providerFactory
      .GetAllDeviceGroupTypes()
      .FirstOrDefault(a =>
        a.ProviderTypeId == providerTypeId &&
        a.DeviceGroupTypeId == deviceGroupTypeId)
      ?.DeviceGroupType;
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task<ICollection<string>> GetDevicesInGroup(
    ProviderLink link,
    Guid deviceGroupTypeId,
    string deviceGroupId,
    CancellationToken token,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null)
  {
    IAsyncPolicy policy = Policy.NoOpAsync();
    var context = policyContext ?? [];
    if (cachePolicy != null)
    {
      context.SetCacheKey(new object[] { link.Id, deviceGroupTypeId, deviceGroupId });
      policy = cachePolicy.WrapAsync(policy);
    }
    return await policy.ExecuteAsync((ctx, t) =>
    {
      t.ThrowIfCancellationRequested();
      return _providerFactory
        .GetDevicesInGroup(link, deviceGroupTypeId, deviceGroupId, t);
    }, context, token);
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task<Dictionary<int, Dictionary<Guid, Dictionary<string, string>>>> GetGroupNamesForDeviceGroupTypesByIds(
    Dictionary<int, Dictionary<Guid, ICollection<string>>> deviceGroupTypeIdMap,
    ImmybotDbContext ctx,
    CancellationToken token)
  {
    var tasks = new List<Task<ValueTuple<string, Dictionary<string, string>>>>();
    var linkIds = deviceGroupTypeIdMap.Keys.ToArray();
    var links = await ctx.GetProviderLinks()
      .Where(l => linkIds.Contains(l.Id))
      .ToDictionaryAsync(
        l => l.Id,
        l => new
        {
          providerLink = l,
          groupTypes = deviceGroupTypeIdMap[l.Id],
        }, token);
    foreach (var kvp in links)
    {
      var link = kvp.Value.providerLink;
      var groupTypes = kvp.Value.groupTypes;
      foreach (var kvp2 in groupTypes)
      {
        var keyName = $"{kvp.Key}|{kvp2.Key}";
        var groupType = kvp2.Key;
        var groupIds = kvp2.Value;
        tasks.Add(Task.Run(async () =>
        {
          ICollection<IDeviceGroup> groups = await _providerFactory
            .GetDeviceGroupsByIds(link, groupType, groupIds, token);
          return (
            keyName,
            groups.GroupBy(s => s.DeviceGroupId).ToDictionary(
              s => s.Key,
              s => s.First().DeviceGroupDisplayName)
          );
        }));
      }
    }

    var results = new Dictionary<int, Dictionary<Guid, Dictionary<string, string>>>();

    foreach (var r in await Task.WhenAll(tasks))
    {
      var keys = r.Item1.Split("|");
      var linkId = int.Parse(keys[0]);
      var groupType = Guid.Parse(keys[1]);
      if (!results.TryGetValue(linkId, out var groups))
      {
        groups = [];
        results.Add(linkId, groups);
      }
      groups.Add(groupType, r.Item2);
    }

    return results;
  }

  /// <exception cref="MissingDeviceGroupTypeException"></exception>
  public TargetScope GetTargetScopeForDeviceGroupType(Guid deviceGroupTypeId)
  {
    return _providerFactory.GetDeviceGroupMetadata(deviceGroupTypeId).TargetScope;
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task DeleteOfflineAgentFromComputer(
    ProviderLink providerLink,
    ProviderAgent agent,
    CancellationToken token)
  {
    var provider = await GetProvider(providerLink, token);
    // provider will come back null when it is disabled
    if (provider == null)
    {
      throw new NotSupportedException("Cannot delete an agent of a disabled provider.");
    }
    if (provider is not ISupportsDeletingOfflineAgent offboardableProvider)
    {
      throw new NotSupportedException("Deleting offline agents is not supported by this provider");
    }
    token.ThrowIfCancellationRequested();
    await offboardableProvider.DeleteAgent(agent, token);
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  public async Task<AgentUpdateResult> HandleAgentUpdate(
    ProviderLink link,
    ProviderAgent agent,
    AgentUpdateDto agentUpdateDto,
    CancellationToken token)
  {
    var result = new AgentUpdateResult { AgentUpdateRequest = agentUpdateDto };
    var provider = await GetProvider(link, token);
    if (agentUpdateDto.UpdateClientTo != null)
    {
      // the update says the client needs to be changed
      var sem = _providerLinkLocker.GetProviderLinkLock(link.Id);
      var didLock = await sem.WaitAsync(TimeSpan.FromSeconds(30), token);
      if (didLock)
      {
        try
        {
          var lockKey = _providerLinkLocker.ManufactureLockKey(link.Id);
          if (provider is ISupportsUpdatingClientForAgents clientUpdatingProvider)
          {
            var updatedDevice = await clientUpdatingProvider
              .UpdateClientForAgent(agent, agentUpdateDto.UpdateClientTo, default);
            await _providerEventHandler.AgentsUpdatedAsync(link.Id,
              new[] { updatedDevice },
              lockKey,
              token);
            result.UpdateClientWasSuccessful = true;
          }
          else
          {
            result.UpdateClientWasSuccessful = false;
            result.UpdateClientFailureReason = "The integration does not support updating devices' clients";
          }
        }
        catch (DeviceUpdateFailedException ex)
        {
          result.UpdateClientWasSuccessful = false;
          result.UpdateClientFailureReason = ex.Message;
        }
        finally
        {
          _providerLinkLocker.RemoveLockKey(link.Id);
          sem.Release();
        }
      }
      else
      {
        result.UpdateClientWasSuccessful = false;
        result.UpdateClientFailureReason = "Could not get a lock on the integration link within 30 seconds";
      }
    }

    if (agentUpdateDto.AgentUpdateFormData is JsonElement agentUpdateFormData)
    {
      if (provider is ISupportsUpdatingAgents deviceUpdatingProvider)
      {
        try
        {
          await deviceUpdatingProvider.UpdateAgent(agent, agentUpdateFormData, default);
        }
        catch (DeviceUpdateFailedException ex)
        {
          result.UpdateDeviceWasSuccessful = false;
          result.UpdateDeviceFailureReason = ex.Message;
        }
      }
      else
      {
        result.UpdateDeviceWasSuccessful = false;
        result.UpdateDeviceFailureReason = "The integration does not support updating devices";
      }
    }
    return result;
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="NotSupportedException"></exception>
  public async Task<Uri> GetAgentExecutableUri(
    ProviderLink providerLink,
    GetExecutableUriParameters requestOptions,
    CancellationToken token)
  {
    if (string.IsNullOrEmpty(requestOptions.TargetExternalClientId))
      throw new ArgumentException("TargetExternalClientId is required in requestOptions");

    _logger.LogTrace("Requesting executable uri for {ProviderLinkName}", providerLink.Name);
    var provider = await GetProvider(providerLink, token);
    _logger.LogTrace("Retrieved provider for {ProviderLinkName}", providerLink.Name);

    if (provider is ISupportsAgentExecutableUri supportedProvider)
    {
      EnsureProviderClientExists(providerLink.Id, requestOptions.TargetExternalClientId, provider is ISupportsListingClients);
      return await supportedProvider.GetExecutableUri(requestOptions, token);
    }
    throw new NotSupportedException($"Integration {providerLink.Name} does not support {nameof(GetAgentExecutableUri)}.");
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="NotSupportedException"></exception>
  public async Task<Uri> GetAgentExecutableUri(
    ProviderLink providerLink,
    GetExecutableUriParametersWithOnboardingOptions requestOptions,
    CancellationToken token)
  {
    if (string.IsNullOrEmpty(requestOptions.TargetExternalClientId))
      throw new ArgumentException("TargetExternalClientId is required in requestOptions");

    _logger.LogTrace("Requesting executable uri for {ProviderLinkName}", providerLink.Name);
    var provider = await GetProvider(providerLink, token);
    _logger.LogTrace("Retrieved provider for {ProviderLinkName}", providerLink.Name);

    if (provider is ISupportsAgentExecutableUriWithOnboardingOptions supportedProvider)
    {
      EnsureProviderClientExists(providerLink.Id, requestOptions.TargetExternalClientId, provider is ISupportsListingClients);
      return await supportedProvider.GetExecutableUri(requestOptions, token);
    }
    throw new NotSupportedException($"Integration {providerLink.Name} does not support {nameof(GetAgentExecutableUri)} with onboarding options.");
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="NotSupportedException"></exception>
  public async Task<Uri> GetAgentProvisioningPackageUri(
    ProviderLink providerLink,
    GetProvisioningPackageUriParameters requestOptions,
    CancellationToken token)
  {
    if (string.IsNullOrEmpty(requestOptions.TargetExternalClientId))
      throw new ArgumentException("TargetExternalClientId is required in requestOptions");

    _logger.LogTrace("Requesting provisioning package uri for {ProviderLinkName}",
      providerLink.Name);
    var provider = await GetProvider(providerLink, token);

    _logger.LogTrace("Retrieved provider for {ProviderLinkName}", providerLink.Name);
    if (provider is ISupportsAgentProvisioningPackageUri ppkgProvider)
    {
      EnsureProviderClientExists(providerLink.Id, requestOptions.TargetExternalClientId, provider is ISupportsListingClients);
      return await ppkgProvider.GetProvisioningPackageUri(requestOptions, token);
    }
    throw new NotSupportedException($"Integration {providerLink.Name} does not support {nameof(GetAgentProvisioningPackageUri)}.");
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="NotSupportedException"></exception>
  public async Task<Uri> GetAgentProvisioningPackageUri(
    ProviderLink providerLink,
    GetProvisioningPackageUriParametersWithOnboardingOptions requestOptions,
    CancellationToken token)
  {
    if (string.IsNullOrEmpty(requestOptions.TargetExternalClientId))
      throw new ArgumentException("TargetExternalClientId is required in requestOptions");

    _logger.LogTrace("Requesting provisioning package uri for {ProviderLinkName}",
      providerLink.Name);
    var provider = await GetProvider(providerLink, token);

    _logger.LogTrace("Retrieved provider for {ProviderLinkName}", providerLink.Name);
    if (provider is ISupportsAgentProvisioningPackageUriWithOnboardingOptions onboardingPpkgProvider)
    {
      EnsureProviderClientExists(providerLink.Id, requestOptions.TargetExternalClientId, provider is ISupportsListingClients);
      return await onboardingPpkgProvider.GetProvisioningPackageUri(requestOptions, token);
    }
    throw new NotSupportedException($"Integration {providerLink.Name} does not support {nameof(GetAgentProvisioningPackageUri)} with onboarding options.");
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="NotSupportedException"></exception>
  public async Task<IScript> GetAgentPowerShellInstallScript(
    ProviderLink providerLink,
    GetPowerShellInstallScriptParameters requestOptions,
    CancellationToken token)
  {
    if (string.IsNullOrEmpty(requestOptions.TargetExternalClientId)) throw new ArgumentException("TargetExternalClientId is required in requestOptions");

    _logger.LogTrace("Requesting powershell install script for {ProviderLinkName}",
      providerLink.Name);
    var provider = await GetProvider(providerLink, token);
    _logger.LogTrace("Retrieved provider for {ProviderLinkName}", providerLink.Name);

    if (provider is ISupportsAgentPowerShellInstallScript supportedProvider)
    {
      EnsureProviderClientExists(providerLink.Id, requestOptions.TargetExternalClientId, provider is ISupportsListingClients);
      return await supportedProvider.GetPowerShellInstallScript(requestOptions, token);
    }
    throw new NotSupportedException($"Integration {providerLink.Name} does not support {nameof(GetAgentPowerShellInstallScript)}.");
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="NotSupportedException"></exception>
  public async Task<IScript> GetAgentPowerShellInstallScript(
    ProviderLink providerLink,
    GetPowerShellInstallScriptParametersWithOnboardingOptions requestOptions,
    CancellationToken token)
  {
    if (string.IsNullOrEmpty(requestOptions.TargetExternalClientId)) throw new ArgumentException("TargetExternalClientId is required in requestOptions");

    _logger.LogTrace("Requesting powershell install script for {ProviderLinkName}",
      providerLink.Name);
    var provider = await GetProvider(providerLink, token);
    _logger.LogTrace("Retrieved provider for {ProviderLinkName}", providerLink.Name);

    if (provider is ISupportsAgentPowerShellInstallScriptWithOnboardingOptions supportedProvider)
    {
      EnsureProviderClientExists(providerLink.Id, requestOptions.TargetExternalClientId, provider is ISupportsListingClients);
      return await supportedProvider.GetPowerShellInstallScript(requestOptions, token);
    }
    throw new NotSupportedException($"Integration {providerLink.Name} does not support {nameof(GetAgentPowerShellInstallScript)} with onboarding options.");
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="NotSupportedException"></exception>
  public async Task<IScript> GetAgentBashInstallScript(
    ProviderLink providerLink,
    GetBashInstallScriptParameters requestOptions,
    CancellationToken token)
  {
    if (string.IsNullOrEmpty(requestOptions.TargetExternalClientId)) throw new ArgumentException("TargetExternalClientId is required in requestOptions");

    _logger.LogTrace("Requesting bash install script for {ProviderLinkName}", providerLink.Name);
    var provider = await GetProvider(providerLink, token);
    _logger.LogTrace("Retrieved provider for {ProviderLinkName}", providerLink.Name);

    if (provider is ISupportsAgentBashInstallScript supportedProvider)
    {
      EnsureProviderClientExists(providerLink.Id, requestOptions.TargetExternalClientId, provider is ISupportsListingClients);
      return await supportedProvider.GetBashInstallScript(requestOptions, token);
    }
    throw new NotSupportedException($"Integration {providerLink.Name} does not support {nameof(GetAgentBashInstallScript)}.");
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="NotSupportedException"></exception>
  public async Task<IScript> GetAgentBashInstallScript(
    ProviderLink providerLink,
    GetBashInstallScriptParametersWithOnboardingOptions requestOptions,
    CancellationToken token)
  {
    if (string.IsNullOrEmpty(requestOptions.TargetExternalClientId)) throw new ArgumentException("TargetExternalClientId is required in requestOptions");

    _logger.LogTrace("Requesting bash install script for {ProviderLinkName}", providerLink.Name);
    var provider = await GetProvider(providerLink, token);
    _logger.LogTrace("Retrieved provider for {ProviderLinkName}", providerLink.Name);

    if (provider is ISupportsAgentBashInstallScriptWithOnboardingOptions supportedProvider)
    {
      EnsureProviderClientExists(providerLink.Id, requestOptions.TargetExternalClientId, provider is ISupportsListingClients);
      return await supportedProvider.GetBashInstallScript(requestOptions, token);
    }
    throw new NotSupportedException($"Integration {providerLink.Name} does not support {nameof(GetAgentBashInstallScript)} with onboarding options.");
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="NotSupportedException"></exception>
  public async Task<Uri> GetLatestAgentVersionDownloadUrl(
    ProviderLink providerLink,
    CancellationToken token)
  {
    _logger.LogTrace("Requesting latest agent installer download url for {ProviderLinkName}", providerLink.Name);
    var provider = await GetProvider(providerLink, token);
    _logger.LogTrace("Retrieved provider for {ProviderLinkName}", providerLink.Name);

    if (provider is ISupportsGetLatestAgentVersionDownloadUrl supportedProvider)
    {
      return supportedProvider.GetLatestAgentVersionDownloadUrl(token);
    }
    throw new NotSupportedException($"Integration {providerLink.Name} does not support {nameof(GetLatestAgentVersionDownloadUrl)}.");
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="NotSupportedException"></exception>
  public async Task<SemanticVersion> GetLatestAgentVersion(
    ProviderLink providerLink,
    CancellationToken token)
  {
    _logger.LogTrace("Requesting latest agent version for {ProviderLinkName}", providerLink.Name);
    var provider = await GetProvider(providerLink, token);
    _logger.LogTrace("Retrieved provider for {ProviderLinkName}", providerLink.Name);

    if (provider is ISupportsGetLatestAgentVersion supportedProvider)
    {
      return supportedProvider.GetLatestAgentVersion(token);
    }
    throw new NotSupportedException($"Integration {providerLink.Name} does not support {nameof(GetLatestAgentVersion)}.");
  }

  public async Task<TechnicianPageInfoFromPsaTicket> GetTechnicianPageInfoFromPsaTicket(
    ProviderLink providerLink,
    string ticketId,
    CancellationToken token)
  {
    _logger.LogTrace("Requesting psa ticket details for {ProviderLinkName}", providerLink.Name);
    var provider = await GetProvider(providerLink, token);
    _logger.LogTrace("Retrieved provider for {ProviderLinkName}", providerLink.Name);

    if (provider is ISupportsPsaTicketDetails supportedProvider)
    {
      var details = await supportedProvider.GetPsaTicketDetails(ticketId, token);

      // convert details into person and tenant
      await using var ctx = _ctxFactory();

      var emailLower = details.EmailAddress.ToLower();

      var person = await ctx
        .Persons
        .AsNoTracking()
        .Select(a => new
        {
          a.Id,
          a.TenantId,
          a.EmailAddress,
          a.FirstName,
          a.LastName
        })
        .FirstOrDefaultAsync(a =>
          a.EmailAddress.ToLower() == emailLower,
          token);

      var tenantId = person?.TenantId;

      // if we failed to retrieve the tenant id from the person, attempt to retrieve a tenant from the ticket's client id.
      if (tenantId is null)
      {
        var clientIdLower = details.ClientId.ToLower();
        tenantId = await ctx.ProviderClients
          .Where(a => a.ProviderLinkId == providerLink.Id && a.ExternalClientId.ToLower() == clientIdLower)
          .Select(a => a.LinkedToTenantId)
          .FirstOrDefaultAsync(token);
      }

      List<UnifiedComputerInfo> computers = [];

      // fetch all of this person's computers
      if (person?.Id is { } personId)
      {
        var dbComputers = await ctx.GetComputersByPrimaryPersonId(personId)
          .ToListAsync(token);

        foreach (var c in dbComputers)
        {
          var isOnline = c.GetOnlineRunScriptAgents().Count > 0;
          computers.Add(new UnifiedComputerInfo(
            c.Id,
            c.ComputerName ?? "Missing computer name (#" + c.Id + ")",
            isOnline));
        }
      }

      var personName = !string.IsNullOrEmpty(person?.FirstName) || !string.IsNullOrEmpty(person?.LastName)
        ? person.FirstName + " " + person.LastName
        : null;

      string? tenantName = null;
      if (tenantId.HasValue)
      {
        tenantName = await ctx.Tenants
          .AsNoTracking()
          .Where(a => a.Id == tenantId)
          .Select(a => a.Name)
          .FirstOrDefaultAsync(token);
      }

      return new TechnicianPageInfoFromPsaTicket(
        person?.Id,
        personName,
        tenantId,
        tenantName,
        computers,
        details.EmailAddress,
        details.ClientId);
    }

    throw new NotSupportedException(
      $"Integration {providerLink.Name} does not support {nameof(ISupportsPsaTicketDetails)}.");
  }

  public async Task<Optional<ProviderSupportFormBranding>> GetSupportBrandingDetails(
    CancellationToken token)
  {
    var provider = (await GetProvidersOfType<ISupportsSupportTicketDetailOverride>(token)).FirstOrDefault();
    if (provider is null)
      return Optional.None<ProviderSupportFormBranding>();
    var overrideBranding = await provider.NewSupportFormBranding(token);
    return overrideBranding.HasValue ? overrideBranding.Value : Optional.None<ProviderSupportFormBranding>();
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task<Script?> GetAgentInstallScript(
    ProviderLink providerLink,
    Computer computer,
    CancellationToken token)
  {
    // get install script
    var provider = await GetProvider(providerLink, token);
    token.ThrowIfCancellationRequested();
    IScript? installScript = null;

    if (provider is ISupportsAgentPowerShellInstallScriptWithOnboardingOptions)
      installScript = await GetAgentPowerShellInstallScript(providerLink, new GetPowerShellInstallScriptParametersWithOnboardingOptions()
      {
        Platform = Platform.Windows,
        TargetExternalClientId = computer.TenantId.ToString(),
        OnboardingOptions = new()
      }, token);
    else if (provider is ISupportsAgentPowerShellInstallScript)
      installScript = await GetAgentPowerShellInstallScript(providerLink, new GetPowerShellInstallScriptParameters()
      {
        Platform = Platform.Windows,
        TargetExternalClientId = computer.TenantId.ToString()
      }, token);
    else if (provider is ISupportsAgentBashInstallScriptWithOnboardingOptions)
      installScript = await GetAgentBashInstallScript(providerLink, new GetBashInstallScriptParametersWithOnboardingOptions()
      {
        Platform = Platform.Windows,
        TargetExternalClientId = computer.TenantId.ToString(),
        OnboardingOptions = new()
      }, token);
    else if (provider is ISupportsAgentBashInstallScript)
      installScript = await GetAgentBashInstallScript(providerLink, new GetBashInstallScriptParameters()
      {
        Platform = Platform.Windows,
        TargetExternalClientId = computer.TenantId.ToString()
      }, token);

    if (installScript is null)
      throw new NotSupportedException("Cannot install agent on computer because neither bash nor powershell installation is supported");

    return new Script
    {
      Name = "Install-Agent",
      Action = installScript.Script,
      Timeout = installScript.Timeout,
      ScriptLanguage = installScript.ScriptLanguage,
    };
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task<Dictionary<string, string>> GetClientsLinkedToClients(
    ProviderLink link,
    Guid psaProviderTypeId,
    ICollection<string> psaCompanyIds,
    CancellationToken token,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null)
  {
    IAsyncPolicy policy = Policy.NoOpAsync();
    var context = policyContext ?? [];
    if (cachePolicy != null)
    {
      context.SetCacheKey(new object[] { link.Id, psaProviderTypeId, string.Join(',', psaCompanyIds) });
      policy = cachePolicy.WrapAsync(policy);
    }
    return await policy.ExecuteAsync(async (ctx, t) =>
    {
      t.ThrowIfCancellationRequested();
      if ((await GetProvider(link, t)) is ISupportsCrossProviderClientLinking provider)
      {
        return await provider.GetClientsLinkedToCrossProviderClients(psaProviderTypeId, psaCompanyIds, t);
      }
      return new Dictionary<string, string>(0);
    }, context, token);
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task<JsonElement> PreprocessProviderTypeFormData(
    ProviderLink providerLink,
    JsonElement formData,
    CancellationToken token)
  {
    IProvider? provider;
    try
    {
      provider = await GetProvider(providerLink, token);
    }
    catch (Exception ex) when (!(ex is TaskCanceledException))
    {
      return formData;
    }
    token.ThrowIfCancellationRequested();
    if (provider is IRequiresProviderTypeFormDataPreprocessing preprocessor)
    {
      return await preprocessor.PreprocessProviderTypeFormData(formData, token);
    }
    return formData;
  }

  public void EnsureProviderClientExists(int providerLinkId, string externalClientId, bool supportsListingExternalClients)
  {
    using var ctx = _ctxFactory();
    if (!supportsListingExternalClients)
    {
      var tenant = ctx.GetTenantById(Convert.ToInt32(externalClientId));
      if (tenant == null) throw new ArgumentException($"Tenant with id {externalClientId} was not found.");

      var existing = ctx.GetClientsForProviderLink(providerLinkId).FirstOrDefault(c => c.ExternalClientId == externalClientId);
      if (existing == null)
      {
        var newClient = new ProviderClient()
        {
          ProviderLinkId = providerLinkId,
          ExternalClientId = externalClientId,
          LinkedToTenantId = tenant.Id,
          ExternalClientName = tenant.Name,
          HasCompletedInitialAgentSync = true,
        };
        ctx.CreateProviderClient(newClient);
      }
      else if (existing.LinkedToTenantId == null)
      {
        // A provider client exists with this tenant as its ExternalClientId, but it's not currently linked to this tenant
        existing.LinkedToTenantId = tenant.Id;
        ctx.UpdateProviderClient(existing);
      }
    }
    else
    {
      throw new NotSupportedException($"Not yet supported for integrations that implement {nameof(ISupportsListingClients)}.");
    }
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task<bool> RefreshAgentOnlineStatus(
    ProviderLink providerLink,
    ProviderAgent agent,
    CancellationToken token)
  {
    token.ThrowIfCancellationRequested();
    var provider = await GetProvider(providerLink, token);
    // provider will come back null when it is disabled
    if (provider == null)
    {
      throw new NotSupportedException("Cannot refresh connection status on a disabled Provider.");
    }

    if (provider is not ISupportsRefreshAgentOnlineStatus supportedProvider)
    {
      throw new NotSupportedException($"Integration {providerLink.Name} does not support refreshing agent connection statuses.");
    }

    token.ThrowIfCancellationRequested();
    return await supportedProvider.RefreshAgentOnlineStatus(agent, token);
  }

  /// <summary>
  /// Returns null if the provider does not have extra data accessible in metascripts
  /// </summary>
  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task<PSObject?> GetExtraDataAccessibleInMetascripts(
    ProviderLink providerLink,
    CancellationToken cancellationToken)
  {
    cancellationToken.ThrowIfCancellationRequested();
    var provider = await GetProvider(providerLink, cancellationToken);
    // provider will come back null when it is disabled
    if (provider == null)
    {
      throw new NotSupportedException($"Integration {providerLink.Name} is disabled and cannot retrieve metascript data.");
    }

    if (provider is IHasExtraDataAccessibleInMetascripts p)
    {
      cancellationToken.ThrowIfCancellationRequested();
      return await p.GetExtraDataAccessibleInMetascripts(cancellationToken);
    }
    return null;
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task<ICollection<IClientGroup>> GetClientGroups(
    ProviderLink link,
    Guid clientGroupTypeId,
    CancellationToken token,
    string? companyId = null)
  {
    return await _providerFactory.GetClientGroups(link, clientGroupTypeId, token, companyId);
  }

  public Dictionary<Guid, (string name, string description)> GetAllClientGroupTypeNamesAndDescriptions()
  {
    return _providerFactory.GetAllClientGroupTypes()
      .ToDictionary(m => m.ClientGroupTypeId, m => (m.DisplayName, m.Description));
  }

  public Type? GetClientGroupType(Guid providerTypeId, Guid clientGroupTypeId)
  {
    return _providerFactory
      .GetAllClientGroupTypes()
      .FirstOrDefault(a =>
        a.ProviderTypeId == providerTypeId &&
        a.ClientGroupTypeId == clientGroupTypeId)
      ?.ClientGroupType;
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task<ICollection<string>> GetClientIdsInGroup(
    ProviderLink link,
    Guid clientGroupTypeId,
    string clientGroupId,
    CancellationToken token,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null)
  {
    IAsyncPolicy policy = Policy.NoOpAsync();
    var context = policyContext ?? [];
    if (cachePolicy != null)
    {
      context.SetCacheKey(new object[] { link.Id, clientGroupTypeId, clientGroupId });
      policy = cachePolicy.WrapAsync(policy);
    }
    return await policy.ExecuteAsync((ctx, t) =>
    {
      t.ThrowIfCancellationRequested();
      return _providerFactory
        .GetClientIdsInGroup(link, clientGroupTypeId, clientGroupId, t);
    }, context, token);
  }

  /// <exception cref="DisallowedLinkAccessException"></exception>
  /// <exception cref="MissingProviderTypeException"></exception>
  /// <exception cref="ProviderConstructionFailedException"></exception>
  /// <exception cref="InvalidProviderFormDataException"></exception>
  public async Task<Dictionary<int, Dictionary<Guid, Dictionary<string, string>>>> GetGroupNamesForClientGroupTypesByIds(
    Dictionary<int, Dictionary<Guid, ICollection<string>>> clientGroupTypeIdMap,
    ImmybotDbContext ctx,
    CancellationToken token)
  {
    var tasks = new List<Task<ValueTuple<string, Dictionary<string, string>>>>();
    var linkIds = clientGroupTypeIdMap.Keys.ToArray();
    var links = await ctx.GetProviderLinks()
      .Where(l => linkIds.Contains(l.Id))
      .ToDictionaryAsync(
        l => l.Id,
        l => new
        {
          providerLink = l,
          groupTypes = clientGroupTypeIdMap[l.Id],
        }, token);
    foreach (var kvp in links)
    {
      var link = kvp.Value.providerLink;
      var groupTypes = kvp.Value.groupTypes;
      foreach (var kvp2 in groupTypes)
      {
        var keyName = $"{kvp.Key}|{kvp2.Key}";
        var groupType = kvp2.Key;
        var groupIds = kvp2.Value;
        tasks.Add(Task.Run(async () =>
        {
          ICollection<IClientGroup> groups = await _providerFactory
            .GetClientGroupsByIds(link, groupType, groupIds, token);
          return (
            keyName,
            groups.GroupBy(s => s.ClientGroupId).ToDictionary(
              s => s.Key,
              s => s.First().ClientGroupDisplayName)
          );
        }));
      }
    }

    var results = new Dictionary<int, Dictionary<Guid, Dictionary<string, string>>>();

    foreach (var r in await Task.WhenAll(tasks))
    {
      var keys = r.Item1.Split("|");
      var linkId = int.Parse(keys[0]);
      var groupType = Guid.Parse(keys[1]);
      if (!results.TryGetValue(linkId, out var groups))
      {
        groups = [];
        results.Add(linkId, groups);
      }
      groups.Add(groupType, r.Item2);
    }

    return results;
  }

  public async Task<IEnumerable<IClientStatus>> GetProviderClientStatuses(ProviderLink link, CancellationToken token)
  {
    token.ThrowIfCancellationRequested();
    var provider = await GetProvider(link, token);
    // provider will come back null when it is disabled
    if (provider == null)
    {
      throw new NotSupportedException($"Integration {link.Name} is disabled and cannot retrieve client statuses.");
    }

    if (provider is not ISupportsProviderClientStatus supportedProvider)
    {
      throw new NotSupportedException($"Integration {link.Name} does not support retrieving client statuses.");
    }

    token.ThrowIfCancellationRequested();
    return await supportedProvider.GetClientStatuses(token);
  }

  public async Task<IEnumerable<IClientType>> GetProviderClientTypes(ProviderLink link, CancellationToken token)
  {
    token.ThrowIfCancellationRequested();
    var provider = await GetProvider(link, token);
    // provider will come back null when it is disabled
    if (provider == null)
    {
      throw new NotSupportedException($"Integration {link.Name} is disabled and cannot retrieve client types.");
    }

    if (provider is not ISupportsProviderClientType supportedProvider)
    {
      throw new NotSupportedException($"Integration {link.Name} does not support retrieving client types.");
    }

    token.ThrowIfCancellationRequested();
    return await supportedProvider.GetClientTypes(token);
  }

  /// <summary>
  /// Gets all provider links that implement the specified interface type
  /// </summary>
  /// <typeparam name="T">The interface type to filter by</typeparam>
  /// <returns>A list of provider links that implement the specified interface</returns>
  public async Task<List<ProviderLink>> GetProviderLinksOfType<T>(CancellationToken token) where T : class
  {
    var providerLinks = await GetEnabledProviderLinks(token);

    return (await Task.WhenAll(providerLinks.Select(async link =>
        (Link: link, IsMatch: await GetProvider(link, token, noProviderConstruction: false) is T
      ))))
      .Where(x => x.IsMatch)
      .Select(x => x.Link)
      .ToList();
  }

  /// <summary>
  /// Gets all provider instances that implement the specified interface type
  /// </summary>
  /// <typeparam name="T">The interface type to filter by</typeparam>
  /// <returns>A list of provider instances that implement the specified interface</returns>
  public async Task<List<T>> GetProvidersOfType<T>(CancellationToken token) where T : class
  {
    var providerLinks = await GetEnabledProviderLinks(token);

    return (await Task.WhenAll(providerLinks.Select(async link =>
        await GetProvider(link, token, noProviderConstruction: false))))
      .OfType<T>()
      .ToList();
  }

  private async Task<List<ProviderLink>> GetEnabledProviderLinks(CancellationToken token)
  {
    using var ctx = _ctxFactory();
    return await ctx.ProviderLinks
        .Where(pl => !pl.Disabled)
        .ToListAsync(token);
  }

  public async Task<string> GetExternalProviderAgentUrl(
   ProviderLink providerLink,
   ProviderAgent agent,
   CancellationToken token)
  {
    token.ThrowIfCancellationRequested();
    var provider = await GetProvider(providerLink, token);
    // provider will come back null when it is disabled
    if (provider == null)
    {
      throw new NotSupportedException("Cannot retrieve the agent url on a disabled Provider.");
    }

    if (provider is not ISupportsExternalProviderAgentUrl supportedProvider)
    {
      throw new NotSupportedException($"Integration {providerLink.Name} does not support fetching the agent url.");
    }

    token.ThrowIfCancellationRequested();
    return await supportedProvider.GetExternalProviderAgentUrl(agent, token);
  }
}
