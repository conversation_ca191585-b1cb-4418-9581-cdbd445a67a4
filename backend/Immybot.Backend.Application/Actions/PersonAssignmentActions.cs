using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using System.Collections.Generic;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.PersonExtensions;
using Immybot.Shared.Primitives;
using Microsoft.EntityFrameworkCore;
using Guard = Immybot.Backend.Application.Lib.Guard;

namespace Immybot.Backend.Application.Actions;

public class PersonAssignmentActions(
  Func<ImmybotDbContext> ctxFactory,
  IAzureActions azureActions,
  IProviderActions providerActions)
  : IPersonAssignmentActions
{
  public async Task<DisposableValue<IQueryable<Person>>> GetPersonsInTarget(
    TargetType targetType,
    string? target = null,
    int? tenantId = null,
    bool includeChildTenants = false,
    CancellationToken cancellationToken = default)
  {
    cancellationToken.ThrowIfCancellationRequested();
    var dbContext = ctxFactory();
    IQueryable<Person>? q;

    switch (targetType)
    {
      case TargetType.Person:
        if (string.IsNullOrEmpty(target))
          return DisposableValue.Create(
            dbContext.ShortCircuitNoResults<Person>(),
            dbContext.Dispose);

        q = dbContext.GetPersonsByIds([Convert.ToInt32(target)])
          .Include(a => a.Tags);
        break;
      case TargetType.AllForTenant:
        if (!tenantId.HasValue)
          return DisposableValue.Create(
            dbContext.ShortCircuitNoResults<Person>(),
            dbContext.Dispose);

        List<int> tenantIds = [tenantId.Value];

        if (includeChildTenants)
        {
          tenantIds.AddRange(dbContext.GetDescendentTenantIds(tenantId.Value));
        }

        q = dbContext.GetPersonsAtTenants(tenantIds);
        break;
      case TargetType.All:
        q = dbContext.GetAllPersons();
        break;
      case TargetType.Tag:
        q = dbContext.GetPersonsByTag(Convert.ToInt32(target));
        break;
      case TargetType.AzureGroup:
        if (target is null
            || !tenantId.HasValue
            || dbContext.GetTenantById(tenantId.Value, includeAzData: true) is not { } tenant)
          return DisposableValue.Create(
            dbContext.ShortCircuitNoResults<Person>(),
            dbContext.Dispose);

        var personIds = await azureActions.GetPersonIdsInAzureGroupAtTenant(
          tenant,
          target,
          cancellationToken: cancellationToken);
        q = dbContext.GetPersonsByIds(personIds);
        break;
      case TargetType.TenantTag:
        q = dbContext.GetPersonsInTagForTenant(Convert.ToInt32(target), Guard.EnsureNotNull(tenantId, nameof(tenantId)),
          includeChildTenants: includeChildTenants);
        break;
      default:
        throw new NotSupportedException("Unable to evaluate persons for computer/tenant target types.");
    }

    if (TargetAssignmentHelpers.GetTargetScopeForTargetType(
          providerActions,
          targetType) is TargetScope.CrossTenant)
    {
      q = q.Where(a => a.Tenant!.TenantPreferences != null && !a.Tenant.TenantPreferences.ExcludeFromCrossTenantDeploymentsAndSchedules);
    }

    return DisposableValue.Create(q, dbContext.Dispose);
  }
}
