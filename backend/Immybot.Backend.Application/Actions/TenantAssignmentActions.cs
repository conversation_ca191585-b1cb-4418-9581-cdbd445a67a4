using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Shared.Primitives;
using Polly;

namespace Immybot.Backend.Application.Actions;

public class TenantAssignmentActions(
  Func<ImmybotDbContext> ctxFactory,
  IProviderActions providerActions)
  : ITenantAssignmentActions
{
  public async Task<DisposableValue<IQueryable<Tenant>>> GetTenantsInTarget(
    TargetType targetType,
    string? target = null,
    int? tenantId = null,
    bool includeChildTenants = false,
    bool allowAccessToParentTenant = false,
    int? providerLinkId = null,
    Guid? providerClientGroupType = null,
    CancellationToken cancellationToken = default)
  {
    var context = new Context();
    var policy = Policy.NoOpAsync();
    cancellationToken.ThrowIfCancellationRequested();
    IQueryable<Tenant>? q;
    var dbContext = ctxFactory();

    switch (targetType)
    {
      case TargetType.SpecificTenant:
        if (!tenantId.HasValue)
          return DisposableValue.Create(
            dbContext.ShortCircuitNoResults<Tenant>(),
            dbContext.Dispose);

        List<int> tenantIds = [tenantId.Value];

        if (includeChildTenants)
        {
          tenantIds.AddRange(dbContext.GetDescendentTenantIds(tenantId.Value));
        }

        return DisposableValue.Create(
          dbContext.GetTenantsByIds(tenantIds),
          dbContext.Dispose);
      case TargetType.AllTenants:
        q = dbContext.GetAllTenants();
        break;
      case TargetType.CloudTag:
        q = dbContext.GetTenantsInTag(Convert.ToInt32(target));
        break;
      case TargetType.ProviderClientGroup:
        return DisposableValue.Create(
          string.IsNullOrEmpty(target)
            ? dbContext.ShortCircuitNoResults<Tenant>()
            : await GetTenantsInProviderClientGroup(
              dbContext,
              target,
              Guard.EnsureNotNull(providerLinkId, nameof(providerLinkId)),
              Guard.EnsureNotNull(providerClientGroupType, nameof(providerClientGroupType)),
              policy,
              context,
              cancellationToken),
          dbContext.Dispose);
      case TargetType.CloudProviderClientGroup:
        throw new NotSupportedException("Unable to evaluate tenant for integration groups.");
      default:
        throw new NotSupportedException("Unable to evaluate tenant for computer target type.");
    }

    if (TargetAssignmentHelpers.GetTargetScopeForTargetType(
          providerActions,
          targetType) is TargetScope.CrossTenant)
    {
      q = q.Where(a => !a.TenantPreferences!.ExcludeFromCrossTenantDeploymentsAndSchedules);
    }

    return DisposableValue.Create(q, dbContext.Dispose);
  }

  private async Task<IQueryable<Tenant>> GetTenantsInProviderClientGroup(
    ImmybotDbContext dbContext,
    string target,
    int providerLinkId,
    Guid providerClientGroupType,
    IAsyncPolicy cachePolicy,
    Context policyContext,
    CancellationToken cancellationToken)
  {
    var providerLink = dbContext.GetProviderLink(providerLinkId, includeClients: true);
    if (providerLink is null) return dbContext.ShortCircuitNoResults<Tenant>();
    var providerClientIds = await providerActions
      .GetClientIdsInGroup(
        providerLink,
        providerClientGroupType,
        target,
        policyContext: policyContext,
        cachePolicy: cachePolicy,
        token: cancellationToken);
    var tenantIds = providerLink.ProviderClients
      .Where(a => providerClientIds.Contains(a.ExternalClientId))
      .Select(a => a.LinkedToTenantId)
      .Where(a => a.HasValue)
      .Select(a => a!.Value)
      .ToArray();
    return dbContext.GetTenantsByIds(tenantIds);
  }
}
