using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Azure.Core;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.Commands;
using Immybot.Backend.Application.Interface.Events;
using Immybot.Backend.Application.Lib.Azure;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Application.Lib.Policies;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Graph.Models;
using Polly;
using Polly.Registry;
using Refit;
using User = Microsoft.Graph.Models.User;

namespace Immybot.Backend.Application.Actions;

/// <inheritdoc cref="IAzureActions"/>
internal class AzureActions(
  ISyncAzureUsersAndImmyPersonsCmd _syncAzureUsersAndImmyPersonsCmd,
  Func<ImmybotDbContext> _ctxFactory,
  IGraphApi _graphApi,
  IPartnerCenterApi _partnerCenterApi,
  ILogger<AzureActions> _logger,
  IOptions<AzureActiveDirectoryAuthOptions> _azAdAuthOpts,
  IOptions<AppSettingsOptions> _appSettingsOpts,
  IHostApplicationLifetime _appLifetime,
  IAzureTenantTokenCredentialFactory _immyTokenCredentialFactory,
  IOauthAccessTokenStore _accessTokenStore,
  IDomainEventEmitter _eventEmitter,
  IAzureExceptionHandler _azureExceptionHandler,
  IPendoEventManagementService _pendoEventManagement,
  IReadOnlyPolicyRegistry<string> _policyRegistry) : IAzureActions
{
  private const string _msGraphResourceId = "00000003-0000-0000-c000-000000000000";
  private const string _azGraphResourceId = "00000002-0000-0000-c000-000000000000";

  // This matches the default app reg used by immybot
  private static readonly List<RequiredResourceAccess> _defaultExpectedPermissions =
  [
    new()
    {
      ResourceAppId = _msGraphResourceId,
      ResourceAccess =
      [
        // These permissions are also described in Lib\Azure\permissions-descriptions.json
        // User.Read
        new() { Id = Guid.Parse("e1fe6dd8-ba31-4d61-89e7-88639da4683d"), Type = "Scope" },

        // GroupMember.Read.All
        new() { Id = Guid.Parse("98830695-27a2-44f7-8c18-0c3ebc9698f6"), Type = "Role" },

        // User.Read.All
        new() { Id = Guid.Parse("df021288-bdef-4463-88db-98f22de89214"), Type = "Role" },

        // DelegatedAdminRelationship.Read.All
        new() { Id = Guid.Parse("f6e9e124-4586-492f-adc0-c6f96e4823fd"), Type = "Role" },

        // Directory.Read.All
        new() { Id = Guid.Parse("7ab1d382-f21e-4acd-a863-ba3e13f7da61"), Type = "Role" }
      ]
    }
  ];

  private sealed record TranslateObj(string DisplayName, string Description);

  // NB: These are not long-lived caches: they are only used to prevent multiple requests for
  // the same tenant during the same request. They are not persisted across requests.
  private readonly Dictionary<string, ICollection<string>> _partnerTenantCustomerIdsCache = [];
  private readonly SemaphoreSlim _partnerTenantCustomerIdsCacheAccessLock = new(1, 1);
  private readonly AzureActiveDirectoryAuthOptions _azAdAuth = _azAdAuthOpts.Value;
  private readonly IAsyncPolicy _defaultPolicy = Policy.Handle<HttpRequestException>().RetryAsync(3);

  private static PermissionDescriptionDicts GraphPermissionDescriptions
    => PermissionDescriptionsLoader.GetGraphPermissionDescriptions();

  public async Task<List<int>> GetPersonIdsInAzureGroupAtTenant(
    Tenant tenant,
    string azureGroupId,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null,
    CancellationToken cancellationToken = default)
  {
    var policy = _defaultPolicy;
    var context = policyContext ?? new Context().WithLogger(_logger);
    if (tenant.AzureTenantLink is not { } azTenant) return [];
    HydrationException.ThrowIfNull(azTenant.AzureTenant, nameof(azTenant.AzureTenant));
    if (cachePolicy != null)
    {
      context.SetCacheKey([azTenant.AzureTenant.PrincipalId, azureGroupId]);
      policy = cachePolicy.WrapAsync(policy);
    }

    var credential = await _immyTokenCredentialFactory
      .GetImmyTokenCredential(azTenant.AzureTenant, cancellationToken);

    return await policy.ExecuteAsync(async (ctx, t) =>
      {
        t.ThrowIfCancellationRequested();
        var seenGroupIds = new HashSet<string>();
        var groupMembers = await GetAllMembersInGroup(azureGroupId, seenGroupIds, credential, t);

        var userMemberIds = groupMembers.OfType<Microsoft.Graph.Models.User>().Select(a => a.Id).ToList();
        await using var dbContext = _ctxFactory();
        var personIds = await dbContext.Persons
          .AsNoTracking()
          .Where(a => a.TenantId == tenant.Id && userMemberIds.Contains(a.AzurePrincipalId))
          .Select(a => a.Id)
          .ToListAsync(cancellationToken);

        return personIds;
      },
      context,
      cancellationToken);
  }

  public async Task<List<int>> GetComputerIdsInAzureGroupAtTenant(
    Tenant tenant,
    string azureGroupId,
    Context? policyContext = null,
    IAsyncPolicy? cachePolicy = null,
    CancellationToken cancellationToken = default)
  {
    IAsyncPolicy policy = _defaultPolicy;
    var context = policyContext ?? new Context().WithLogger(_logger);
    if (tenant.AzureTenantLink is not { } azTenant) return [];
    HydrationException.ThrowIfNull(azTenant.AzureTenant, nameof(azTenant.AzureTenant));
    if (cachePolicy != null)
    {
      context.SetCacheKey([azTenant.AzureTenant.PrincipalId, azureGroupId]);
      policy = cachePolicy.WrapAsync(policy);
    }

    var credential = await _immyTokenCredentialFactory
      .GetImmyTokenCredential(azTenant.AzureTenant, cancellationToken);

    return await policy.ExecuteAsync(async (ctx, t) =>
      {
        t.ThrowIfCancellationRequested();
        var seenGroupIds = new HashSet<string>();
        var groupMembers = await GetAllMembersInGroup(azureGroupId, seenGroupIds, credential, t);

        var userMemberIds = groupMembers.OfType<Microsoft.Graph.Models.User>().Select(a => a.Id).ToList();
        var deviceIds = groupMembers.OfType<Device>()
          .Select(a => a.DeviceId)
          .Where(d => !string.IsNullOrEmpty(d))
          .OfType<string>()
          .ToList();

        await using var _ctx = _ctxFactory();
        // fetch computers with primary user
        var computerIdsWithPrimaryUsers = await _ctx.Computers
          .AsNoTracking()
          .Where(a => userMemberIds.Contains(a.PrimaryPerson!.AzurePrincipalId))
          .Select(a => a.Id)
          .ToListAsync(cancellationToken);

        // fetch computers with azure device id
        var computerIdWithAzureDeviceIds = await _ctx.GetComputerIdsWithAzureDeviceIds(deviceIds).ToListAsync(cancellationToken);

        return computerIdsWithPrimaryUsers.Concat(computerIdWithAzureDeviceIds).Distinct().ToList();
      },
      context,
      cancellationToken);
  }

  private async Task<List<DirectoryObject>> GetAllMembersInGroup(
    string azureGroupId,
    HashSet<string> seenGroupIds,
    TokenCredential credential,
    CancellationToken cancellationToken)
  {
    var ret = new Dictionary<string, DirectoryObject>();
    var groupMembers = await _graphApi
      .GetGroupMembers(credential, azureGroupId, cancellationToken);
    seenGroupIds.Add(azureGroupId);
    cancellationToken.ThrowIfCancellationRequested();

    foreach (var member in groupMembers)
    {
      if (member is Microsoft.Graph.Models.User { Id: string userId } user && !ret.ContainsKey(userId))
      {
        ret.TryAdd(userId, user);
      }
      else if (member is Device device && device.DeviceId is not null && !ret.ContainsKey(device.DeviceId))
      {
        ret.TryAdd(device.DeviceId, device);
      }
      else if (member is Group { Id: { } memberId } && !seenGroupIds.Contains(memberId))
      {
        var subMembers = await GetAllMembersInGroup(memberId, seenGroupIds, credential, cancellationToken);
        foreach (var subMember in subMembers)
        {
          if (subMember.Id is { } i && !ret.ContainsKey(i))
          {
            ret.TryAdd(i, subMember);
          }
        }
      }
    }

    return [.. ret.Values];
  }

  public async Task<List<Group>> GetGroupsAtTenant(
    Tenant tenant,
    CancellationToken cancellationToken)
  {
    if (tenant.AzureTenantLink?.AzureTenant is not { } azTenant) return [];
    var credential = await _immyTokenCredentialFactory
      .GetImmyTokenCredential(azTenant, cancellationToken);
    return await _graphApi.GetAllGroups(credential, cancellationToken);
  }

  public async Task<Group?> GetGroupAtTenant(Tenant tenant, string groupId, CancellationToken cancellationToken)
  {
    if (tenant.AzureTenantLink?.AzureTenant is not { } azTenant) return null;
    var credential = await _immyTokenCredentialFactory
      .GetImmyTokenCredential(azTenant, cancellationToken);
    return await _graphApi.GetAzureGroup(groupId, credential, cancellationToken);
  }

  public async Task<List<Group>> FindGroupsAtTenant(
    string search,
    Tenant tenant,
    CancellationToken cancellationToken)
  {
    if (tenant.AzureTenantLink?.AzureTenant is not { } azTenant) return [];
    var credential = await _immyTokenCredentialFactory
      .GetImmyTokenCredential(azTenant, cancellationToken);
    return await _graphApi.FindGroups(search, credential, cancellationToken);
  }

  public async Task<List<AzureTenantCustomersResult>> GetPartnerTenantsDelegatedAdminCustomers(
    AzureTenant partnerTenant,
    CancellationToken cancellationToken)
  {
    var ret = new List<AzureTenantCustomersResult>();

    var principalId = partnerTenant.PrincipalId;
    var credential = await _immyTokenCredentialFactory
      .GetImmyTokenCredential(partnerTenant, cancellationToken);

    try
    {
      List<DelegatedAdminCustomer>? customers = null;
      Dictionary<string, List<DelegatedAdminRelationship>>? relationships = null;
      Dictionary<string, UnifiedRoleDefinition>? roleDefinitions = null;
      await Task.WhenAll(
        Task.Run(async () =>
          {
            customers = await _graphApi.GetAllDelegatedAdminCustomers(credential, cancellationToken);
          },
          cancellationToken),
        Task.Run(async () =>
          {
            var rr = await _graphApi
              .GetAllDelegatedAdminRelationships(credential, cancellationToken);
            relationships = rr
              .Where(r => r.Customer != null && r.Id != null)
              .GroupBy(r => r.Customer!.TenantId)
              .ToDictionary(r => r.Key!, r => r.ToList());
          },
          cancellationToken),
        Task.Run(async () =>
          {
            roleDefinitions = (await _graphApi.GetAllRoleDefinitions(credential, cancellationToken))
              .Where(r => r.Id != null)
              .ToDictionary(r => r.Id!);
          },
          cancellationToken));

      customers ??= [];
      relationships ??= [];
      roleDefinitions ??= [];

      var customersList = customers.Aggregate(new List<AzureTenantCustomer>(),
        (agg, c) =>
        {
          if (c.TenantId != null)
          {
            var customerRelationships = relationships
              .GetValueOrDefault(c.TenantId, []);
            agg.Add(new AzureTenantCustomer(c.TenantId,
              c.DisplayName ?? c.TenantId,
              "",
              customerRelationships.Select(r =>
              {
                return new AzureGdapRelationshipDetails(
                  r.Id!,
                  r.DisplayName!,
                  r.Status?.ToString() ?? "unknown",
                  r.Duration is TimeSpan d ? d.Days : null,
                  r.CreatedDateTime is DateTimeOffset dd ? dd.UtcDateTime : null,
                  r.EndDateTime is DateTimeOffset de ? de.UtcDateTime : null,
                  r.AdditionalData.TryGetValue("autoExtendDuration", out var a) ? (string)a : null,
                  r.AccessDetails?.UnifiedRoles?.Where(u => u.RoleDefinitionId != null).Select(u =>
                    new GdapRelationshipRole(
                      u.RoleDefinitionId!,
                      roleDefinitions.TryGetValue(u.RoleDefinitionId!, out var d) ? d.DisplayName : null)
                  ).ToList() ?? [],
                  new List<GdapRelationshipAccessAssignment>()
                );
              }).ToList()
            ));
          }

          return agg;
        });
      ret.Add(new(principalId, customersList));
    }
    catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
    {
      var err = _azureExceptionHandler.HandleAzureException(ex,
        credential,
        "Error while retrieving the partner tenant's delegated admin customers");
      ret.Add(new(principalId, err.AzureError));
    }

    return ret;
  }

  public async Task<List<TenantInfoResult>> GetPartnerTenantsTenantInformations(
    CancellationToken cancellationToken)
  {
    await using var ctx = _ctxFactory();
    var partnerTenants = await ctx.GetPartnerAzureTenants().ToListAsync(cancellationToken);

    var ret = new List<TenantInfoResult>();
    foreach (var azTenant in partnerTenants)
    {
      cancellationToken.ThrowIfCancellationRequested();
      var principalId = azTenant.PrincipalId;
      var credential = await _immyTokenCredentialFactory
        .GetImmyTokenCredential(azTenant, cancellationToken);
      try
      {
        var org = await _graphApi.GetTenantInformation(credential, cancellationToken);
        if (org?.DisplayName == null || org?.Id == null)
          ret.Add(new TenantInfoResult(principalId, new AzureError("No organization info found at tenant")));
        else
          ret.Add(new TenantInfoResult(
            principalId,
            new AzureTenantInfo
            {
              TenantName = org.DisplayName,
              DefaultDomainName = org.VerifiedDomains?.Find(d => d.IsDefault == true)?.Name,
              DomainNames = org.VerifiedDomains?.Select(d => d.Name).Where(d => !string.IsNullOrEmpty(d))
                .OfType<string>().ToList() ?? []
            }));
      }
      catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
      {
        var azErr = _azureExceptionHandler.HandleAzureException(ex,
          credential,
          "Error while retrieving the partner tenant's organization information");
        ret.Add(new TenantInfoResult(principalId, azErr.AzureError));
      }
    }

    return ret;
  }

  public async Task<AzureTenantUserSyncResult> SyncUsersFromAzureTenant(
    AzureTenant azTenant,
    CancellationToken cancellationToken)
  {
    await using var ctx = _ctxFactory();

    // Attach the tenant to the context so that it is tracked and can be updated
    ctx.Attach(azTenant);

    var tenants = await ctx.GetTenantsLinkedToAzureTenant(azTenant)
      .Include(t => t.AzureTenantLink!)
      .ThenInclude(t => t.LimitToDomains)
      .ToListAsync(cancellationToken);
    var mainTenant = tenants.Find(t => t.AzureTenantLink?.ShouldLimitDomains is false);
    // This shouldn't happen. Just making the compiler happy.
    if (mainTenant == null)
      return new AzureTenantUserSyncResult(azTenant.PrincipalId,
        false,
        new AzureError("No unfiltered tenant found for the Azure tenant"),
        0,
        ImmyTenantsSyncedUsers: []);

    _logger.LogDebug(
      "Starting Azure users sync for Azure tenant {TenantName} into {NumTenants} Immy tenant(s).",
      azTenant.InfoSyncedFromAzure?.TenantName ?? azTenant.PrincipalId,
      tenants.Count);

    var credential = await _immyTokenCredentialFactory
      .GetImmyTokenCredential(azTenant, cancellationToken);
    List<Microsoft.Graph.Models.User> azureTenantUsers;

    try
    {
      azureTenantUsers = await _graphApi.GetAllUsers(credential, cancellationToken);

      // If we got this far, it means the get-users request didn't fail, so we should update
      // the tenant sync details appropriately
      if (azTenant.ConsentDetails.ConsentedWith is null)
        await ctx.UpdateAzureTenantLastGetUsersAttemptDetails(azTenant,
          null,
          DateTime.UtcNow,
          cancellationToken,
          consentedWith: credential.GotAccessTokenFrom switch
          {
            AccessTokenSource.DefaultAppRegistration => AppRegistrationType.Backend,
            AccessTokenSource.CustomAppRegistration => AppRegistrationType.Custom,
            _ => null
          });
      else
        await ctx.UpdateAzureTenantLastGetUsersAttemptDetails(azTenant,
          null,
          DateTime.UtcNow,
          cancellationToken);
    }
    catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
    {
      var azErr = _azureExceptionHandler
        .HandleAzureException(ex, credential, "Error while retrieving the tenant's Azure users");
      await ctx.UpdateAzureTenantLastGetUsersAttemptDetails(azTenant,
        azErr.Id,
        DateTime.UtcNow,
        cancellationToken);
      return new AzureTenantUserSyncResult(azTenant.PrincipalId,
        WasSuccessful: false,
        FailedReason: azErr.AzureError,
        NumUsersSynced: 0,
        ImmyTenantsSyncedUsers: []);
    }

    var domainsUsers = azureTenantUsers.Aggregate(new Dictionary<string, List<User>>(),
      (agg, u) =>
      {
        if (u.UserPrincipalName?.Split('@') is not [.., { } domain]) return agg;
        if (!agg.TryGetValue(domain, out var users))
        {
          users = new List<User>();
          agg.Add(domain, users);
        }

        users.Add(u);
        return agg;
      });

    var tenantUserCounts = new Dictionary<int, int>();

    foreach (var tenant in tenants)
    {
      if (tenant.AzureTenantLink?.ShouldLimitDomains is not true) continue;
      var domains = tenant.AzureTenantLink.LimitToDomains;
      var users = new List<User>();
      foreach (var domain in domains)
      {
        if (!domainsUsers.Remove(domain.DomainName, out var domainUsers)) continue;
        users.AddRange(domainUsers);
      }

      var count = await _syncAzureUsersAndImmyPersonsCmd.Run(tenant, users, cancellationToken);
      tenantUserCounts.Add(tenant.Id, count);
    }

    var remainingUsers = domainsUsers.Values
      .SelectMany(u => u)
      .ToList();
    var mainCount = await _syncAzureUsersAndImmyPersonsCmd.Run(mainTenant, remainingUsers, cancellationToken);
    tenantUserCounts.Add(mainTenant.Id, mainCount);

    _pendoEventManagement.Enqueue(
      PendoEventManagementService.PendoEvents.AzureUserSyncSetupCompletedEvent()
    );

    return new AzureTenantUserSyncResult(azTenant.PrincipalId,
      WasSuccessful: true,
      FailedReason: null,
      NumUsersSynced: tenantUserCounts.Values.Sum(),
      ImmyTenantsSyncedUsers: tenantUserCounts);
  }

  public async Task<AzureTenantDetailsSyncResult> SyncAzureDetailsForTenant(
    AzureTenant azTenant,
    CancellationToken cancellationToken)
  {
    _logger.LogDebug("Starting Azure tenant sync for {TenantPrincipalId} ({TenantName}).",
      azTenant.PrincipalId,
      azTenant.InfoSyncedFromAzure?.TenantName ?? "No name available");
    await using var ctx = _ctxFactory();

    // Attach the tenant to the context so that it is tracked and can be updated
    ctx.Attach(azTenant);

    var credential = await _immyTokenCredentialFactory
      .GetImmyTokenCredential(azTenant, cancellationToken);
    try
    {
      var org = await _graphApi.GetTenantInformation(credential, cancellationToken);
      if (org?.Id == null || org.DisplayName == null)
      {
        var azErr = _azureExceptionHandler.HandleAzureException(
          "No organization info found at tenant",
          azTenant.PrincipalId,
          "Error while retrieving the tenant's Azure details");
        await ctx.UpdateAzureTenantSyncedInfo(azTenant, null, azErr.Id, cancellationToken);

        _logger.LogWarning(
          "Azure Error: {AzErr} {TenantPrincipalId} ({TenantName}).",
          azErr.AzureError.FormattedErrorMessage,
          azTenant.PrincipalId,
          azTenant.InfoSyncedFromAzure?.TenantName ?? "No name available");
        return new AzureTenantDetailsSyncResult(azTenant.PrincipalId,
          WasSuccessful: false,
          FailedReason: azErr.AzureError,
          TenantInformationSynced: null);
      }

      var tenantInfo = new AzureTenantInfo
      {
        DefaultDomainName = org.VerifiedDomains?.Find(d => d.IsDefault == true)?.Name,
        TenantName = org.DisplayName,
        DomainNames = org.VerifiedDomains?.Select(d => d.Name).Where(d => !string.IsNullOrEmpty(d)).OfType<string>()
          .ToList() ?? [],
      };
      await ctx.UpdateAzureTenantSyncedInfo(azTenant, tenantInfo, null, cancellationToken);
      return new AzureTenantDetailsSyncResult(azTenant.PrincipalId,
        WasSuccessful: true,
        FailedReason: null,
        TenantInformationSynced: tenantInfo);
    }
    catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
    {
      var azErr = _azureExceptionHandler
        .HandleAzureException(ex,
          credential,
          "Error while retrieving the tenant's Azure details");
      await ctx.UpdateAzureTenantSyncedInfo(azTenant, null, azErr.Id, cancellationToken);
      _logger.LogWarning(ex,
        "An error occurred while attempting to retrieve Azure tenant information from tenant {TenantPrincipalId} ({TenantName}).",
        azTenant.PrincipalId,
        azTenant.InfoSyncedFromAzure?.TenantName ?? "No name available");
      return new AzureTenantDetailsSyncResult(azTenant.PrincipalId,
        WasSuccessful: false,
        FailedReason: azErr.AzureError,
        TenantInformationSynced: null);
    }
  }

  /// <summary>
  /// Attempts to access the AdminAgents group for the given tenant. If that succeeds, updates
  /// the Azure Tenant in the db to have isPartner = true and returns true; else returns false.
  /// </summary>
  private async Task<(bool IsPartner, AzureError? FailedReason)> CheckIsTenantAPartnerTenant(
    AzureTenant azTenant,
    CancellationToken cancellationToken)
  {
    var credential = await _immyTokenCredentialFactory
      .GetImmyTokenCredential(azTenant, cancellationToken);
    try
    {
      var allGroups = await _graphApi.GetAllGroups(credential, cancellationToken);
      return (allGroups.Exists(g => g.DisplayName == "AdminAgents"), null);
    }
    catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
    {
      var azError = _azureExceptionHandler.HandleAzureException(ex,
        credential,
        "Error while attempting to check if tenant is a partner tenant");
      return (false, azError.AzureError);
    }
  }

  public async Task SyncAllTenantsAzureData(
    bool syncUsers,
    CancellationToken cancellationToken)
  {
    await using (var ctx = _ctxFactory())
    {
      cancellationToken.ThrowIfCancellationRequested();

      // get linked tenants
      var azTenants = await ctx.AzureTenants.ToListAsync(cancellationToken);

      foreach (var azTenant in azTenants)
      {
        await DisambiguateAzureTenantType(ctx,
          azTenant,
          azTenants,
          allowResettingCustomerToPartner: false,
          cancellationToken: cancellationToken);
      }
    }

    if (syncUsers)
    {
      _logger.LogDebug("Starting Azure users sync.");
      await using var ctx = _ctxFactory();

      // Re-fetch the tenants between the first and second syncs because the first
      // sync may have updated the tenant's partner status
      var azTenants = await ctx.AzureTenants.ToListAsync(cancellationToken);

      foreach (var azTenant in azTenants)
      {
        cancellationToken.ThrowIfCancellationRequested();
        await SyncUsersFromAzureTenant(azTenant, cancellationToken);
      }
    }
  }

  public async Task<GetPartnerCenterOrgResult> GetPartnerCenterOrganizationDetails(
    Oauth2AccessToken accessToken,
    CancellationToken cancellationToken)
  {
    try
    {
      var profile = await _partnerCenterApi
        .GetPartnerOrganizationProfile(accessToken.Id, cancellationToken);
      return new GetPartnerCenterOrgResult(profile);
    }
    catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
    {
      var azErr = _azureExceptionHandler.HandlePartnerCenterException(ex,
        "Error while using an OAuth access token to connect to partner center",
        oauth2AccessTokenId: accessToken.Id);
      return new GetPartnerCenterOrgResult(azErr.AzureError);
    }
  }

  public async Task<MultiCustomerPreconsentResult> PreconsentCustomerTenants(
    AzureTenant partnerAzTenant,
    ICollection<AzureTenant> customerAzTenants,
    CancellationToken cancellationToken)
  {
    // Tell the azure exception handler not to create notification for the AzureErrorEvent events
    // because the AzureCustomerPreconsentFinishedEvent will already reference the AzureErrors
    _azureExceptionHandler.DisableNotificationEventEmitting();

    if (await _immyTokenCredentialFactory.GetImmyTokenCredential(partnerAzTenant, cancellationToken)
          is not { } partnerCredential
        || partnerCredential is { TenantPreferredAzureClientId: null } or
          { TenantAzurePermissionLevel: null })
    {
      // If the partner doesn't have an azure permission level, we can't preconsent
      var msg = "Partner does not have an Azure permission level";
      _eventEmitter.EmitEvent(
        new AzureMultiCustomerPreconsentFailedEvent(partnerAzTenant.PrincipalId, msg));
      return new MultiCustomerPreconsentResult(partnerAzTenant.PrincipalId, new AzureError(msg));
    }

    var appRegAppId = partnerCredential.TenantPreferredAzureClientId;

    try
    {
      // Before we get to the customer preconsents, make sure we have a refreshable partner center
      // token for the partner tenant
      var scope = _appSettingsOpts.Value.PartnerCenterUserImpersonationScope;

      // This will throw a MissingAccessTokenException if the partner tenant doesn't have a
      // refreshable partner center token
      await _accessTokenStore.GetAccessTokenForScopes(
        partnerAzTenant.PrincipalId,
        [scope],
        _azAdAuth.ClientId,
        cancellationToken,
        refreshable: true);
    }
    catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
    {
      var err = _azureExceptionHandler.HandleAzureException(ex,
        partnerAzTenant.PrincipalId,
        "Error while getting access token for accessing the Partner Center API as the partner tenant");
      _eventEmitter.EmitEvent(
        new AzureMultiCustomerPreconsentFailedEvent(partnerAzTenant.PrincipalId, err));
      return new MultiCustomerPreconsentResult(partnerAzTenant.PrincipalId, err.AzureError);
    }

    var results = new List<AzureCustomerPreconsentResult>();
    foreach (var customerAzTenant in customerAzTenants)
    {
      IList<AzureMessage> azureMessages;
      _eventEmitter.EmitEvent(new AzureCustomerPreconsentStartedEvent(
        partnerAzTenant.PrincipalId,
        customerAzTenant.PrincipalId));
      var list = new ObservableCollection<AzureMessage>();
      list.CollectionChanged += (sender, args) =>
      {
        if (args is
            {
              Action: NotifyCollectionChangedAction.Add or NotifyCollectionChangedAction.Replace,
              NewItems: { } items,
              NewStartingIndex: int index
            })
        {
          int i = index;
          foreach (AzureMessage message in items)
          {
            _eventEmitter.EmitEvent(
              new AzureCustomerPreconsentProgressMessageAddedEvent(
                partnerAzTenant.PrincipalId,
                customerAzTenant.PrincipalId,
                message,
                i));
            i++;
          }
        }
      };
      azureMessages = list;
      AzureCustomerPreconsentResult? result = null;
      try
      {
        result = await PreconsentCustomerTenant(
          partnerCredential,
          customerAzTenant,
          appRegAppId,
          azureMessages,
          cancellationToken);
        results.Add(result);
      }
      finally
      {
        _eventEmitter.EmitEvent(new AzureCustomerPreconsentFinishedEvent(
          partnerAzTenant.PrincipalId,
          customerAzTenant.PrincipalId,
          result));
      }
    }

    _eventEmitter.EmitEvent(
      new AzureMultiCustomerPreconsentFinishedEvent(partnerAzTenant.PrincipalId));

    return new MultiCustomerPreconsentResult(partnerAzTenant.PrincipalId, results);
  }

  private async Task<AzureCustomerPreconsentResult> PreconsentCustomerTenant(
    AzureTenantTokenCredential partnerCredential,
    AzureTenant customerAzTenant,
    string appRegAppId,
    IList<AzureMessage> azureMessages,
    CancellationToken cancellationToken)
  {
    var wrappedResult = new DoPreconsentResult(
      new AzureCustomerPreconsentResult(customerAzTenant.PrincipalId, azureMessages));

    if (customerAzTenant.PartnerPrincipalId is null)
    {
      azureMessages.Add(new("Tenant is not a customer of an Azure Partner"));
      return wrappedResult.Result;
    }

    var credential = await _immyTokenCredentialFactory
      .GetImmyTokenCredential(customerAzTenant,
        cancellationToken,
        onlyUsePartnerCenterRefresh: true);

    wrappedResult = await DoPreconsentCustomerTenant(
      customerAzTenant,
      appRegAppId,
      wrappedResult,
      partnerCredential,
      credential,
      cancellationToken);

    if (wrappedResult.Result.Messages is
        [.., { Error.AzureError.ODataError.Code: "Authorization_RequestDenied" } lastMsg])
    {
      // mark that RequestDenied error as non-fatal so it doesn't get
      // interpreted as failure if our retry succeeds
      wrappedResult.Result.Messages[^1] = lastMsg with { IsErrorNonFatal = true };

      wrappedResult.Result.Messages.Add(new(
        $"The ImmyBot Backend Principal does not have the right permissions. " +
        $"Removing the existing consent for the ImmyBot Backend Principal and retrying."));

      // Remove the existing default consent and try once more
      try
      {
        await _partnerCenterApi.RemoveCustomerConsent(
          customerAzTenant.PartnerPrincipalId,
          customerAzTenant.PrincipalId,
          _azAdAuth.ClientId,
          cancellationToken);
      }
      catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
      {
        var msg = $"Error occurred while removing the existing consent for " +
                  "the ImmyBot Backend Principal from the customer tenant";
        var azErr = _azureExceptionHandler
          .HandlePartnerCenterException(ex, msg, customerAzTenant.PartnerPrincipalId);
        wrappedResult.Result.Messages.Add(new(msg, azErr));
        return wrappedResult.Result;
      }

      wrappedResult = wrappedResult with { DidRemoveExistingConsent = true };
      wrappedResult.Result.Messages.Add(new(
        $"Successfully removed the existing consent for the ImmyBot Backend Principal " +
        "from the customer tenant. Waiting 30 seconds for propagation and then retrying"));

      await Task.Delay(30000, cancellationToken);

      wrappedResult = await DoPreconsentCustomerTenant(
        customerAzTenant,
        appRegAppId,
        wrappedResult,
        partnerCredential,
        credential,
        cancellationToken);
    }

    return wrappedResult.Result;
  }

  /// <summary>
  /// Internal - used for holding data during the preconsent process
  /// </summary>
  private sealed record DoPreconsentResult(
    AzureCustomerPreconsentResult Result,
    bool DidBackendConsentAlreadyExist = false,
    bool DidRemoveExistingConsent = false);

  private async Task<DoPreconsentResult> DoPreconsentCustomerTenant(
    AzureTenant customerAzTenant,
    string appRegAppId,
    DoPreconsentResult wrappedResult,
    AzureTenantTokenCredential partnerCredential,
    AzureTenantTokenCredential credential,
    CancellationToken cancellationToken)
  {
    var messages = wrappedResult.Result.Messages;
    if (customerAzTenant.PartnerPrincipalId is null)
    {
      messages.Add(new("Tenant is not a customer of an Azure Partner"));
      return wrappedResult;
    }

    // Sometimes when messing around with service principals / consents, different api calls fail
    // with an authorization error that indicates that the app registration hasn't propagated yet.
    // This function will retry the given call once after 30 seconds if it fails with that error.
    async Task<T> WithAutoRetry<T>(Func<Task<T>> fn)
    {
      try
      {
        return await fn();
      }
      catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
      {
        if (_azureExceptionHandler.GetFormattedErrorMessage(ex).Contains("AADSTS650051"))
        {
          // this occurs when app creation hasn't propagated yet. Wait 30 seconds and try again
          messages.Add(new(
            "Application hasn't propagated yet. Trying again in 30 seconds"));
          await Task.Delay(30000, cancellationToken);
          return await fn();
        }

        throw;
      }
    }

    ServicePrincipal? immyServicePrincipal = null;

    ServicePrincipal? ourServicePrincipal = null;
    List<AppRoleAssignment> currentRoles;
    List<OAuth2PermissionGrant> currentDelegatedScopes;

    // Step 1: Add preconsent of the default ImmyBot app registration to the customer tenant
    try
    {
      // Put the immybot backend service principal in the customer tenant and give it permission to
      // create new service principals and consent to delegated and application permissions on
      // behalf of the customer
      await _partnerCenterApi
        .AddCustomerConsent(customerAzTenant.PartnerPrincipalId,
          _azAdAuth.ClientId,
          customerAzTenant.PrincipalId,
          new(
            ApplicationId: _azAdAuth.ClientId, // CpvApplicationId
            ApplicationGrants:
            [
              // These are the delegated permissions we need to carry out preconsent
              new ApplicationGrant(
                EnterpriseApplicationId: _msGraphResourceId,
                Scope:
                $"AppRoleAssignment.ReadWrite.All,Application.ReadWrite.All,DelegatedPermissionGrant.ReadWrite.All,Directory.ReadWrite.All"),
            ]),
          cancellationToken);
      messages.Add(new(
        "Successfully added the ImmyBot Backend Principal to the customer tenant " +
        "with appropriate permissions to carry out preconsent (any unnecessary consent " +
        "will be removed when we're done)"));
    }
    catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
    {
      if (ex is not ApiException { StatusCode: HttpStatusCode.Conflict })
      {
        var msg = "Error occurred while adding the ImmyBot Backend Principal to the customer tenant";
        var azErr = _azureExceptionHandler
          .HandlePartnerCenterException(ex, msg, customerAzTenant.PartnerPrincipalId);
        messages.Add(new(msg, azErr));
        return wrappedResult;
      }

      // if already exists, move on
      messages.Add(new(
        "The ImmyBot Backend Principal already exists in the customer tenant; we'll use " +
        "it to carry out preconsent"));
      wrappedResult = wrappedResult with { DidBackendConsentAlreadyExist = true };
    }

    try
    {
      var triedTimes = 0;
      do
      {
        await Task.Delay(triedTimes == 0 ? 0 : 5000, cancellationToken);
        immyServicePrincipal = await WithAutoRetry(
          () => _graphApi.GetServicePrincipal(credential, _azAdAuth.ClientId, cancellationToken));
        if (immyServicePrincipal != null) break;
        else triedTimes++;
      } while (triedTimes < 5);

      if (immyServicePrincipal?.Id == null)
      {
        var azErr = _azureExceptionHandler.HandleAzureException(
          "ImmyBot service principal still missing in tenant after using the partner center api to preconsent",
          customerAzTenant.PrincipalId,
          "Error occurred while retrieving the ImmyBot Backend Principal from the customer tenant");
        messages.Add(
          new("Failed to add the ImmyBot Backend Principal to the customer tenant", azErr));
        return wrappedResult;
      }
    }
    catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
    {
      var msg = "Error occurred while retrieving the ImmyBot Backend Principal from the customer tenant";
      var azErr = _azureExceptionHandler.HandleAzureException(ex, credential, msg);
      messages.Add(new(msg, azErr));
      return wrappedResult;
    }

    if (appRegAppId == _azAdAuth.ClientId)
    {
      // Preconsent is being given to the default app registration
      ourServicePrincipal = immyServicePrincipal;
    }
    else
    {
      // Preconsent is being given to a custom app registration
      try
      {
        ourServicePrincipal = await WithAutoRetry(
          () => _graphApi.GetServicePrincipal(credential, appRegAppId, cancellationToken));
      }
      catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
      {
        var msg = $"Error occurred while retrieving the service principal for " +
                  $"the app registration {appRegAppId} from the customer tenant";
        var azErr = _azureExceptionHandler.HandleAzureException(ex, credential, msg);
        messages.Add(new(msg, azErr));
        return wrappedResult;
      }

      if (ourServicePrincipal?.Id == null)
      {
        // Service principal for the custom app reg doesn't exist yet, need to create it
        messages.Add(new(
          $"Service Principal for the app registration {appRegAppId} does not exist in the " +
          "customer tenant. Creating it now"));
        try
        {
          ourServicePrincipal = await WithAutoRetry(
            () => _graphApi.CreateServicePrincipal(credential, appRegAppId, cancellationToken));
          if (ourServicePrincipal?.Id == null)
          {
            var azErr = _azureExceptionHandler.HandleAzureException(
              "Graph API return null response when creating service principal",
              customerAzTenant.PrincipalId,
              $"Error occurred while creating the service principal for the app registration {appRegAppId} in the customer tenant");
            messages.Add(new(
              $"Failed to add the Service Principal for {appRegAppId} to the customer tenant",
              azErr));
            return wrappedResult;
          }
        }
        catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
        {
          var msg = $"Error occurred while creating the service principal for the " +
                    $"app registration {appRegAppId} in the customer tenant";
          var azErr = _azureExceptionHandler.HandleAzureException(ex, credential, msg);
          messages.Add(new(msg, azErr));
          return wrappedResult;
        }

        messages.Add(new(
          $"Successfully created Service Principal {ourServicePrincipal.DisplayName} in " +
          $"the customer tenant"));
      }
      else
      {
        messages.Add(new(
          $"Service Principal {ourServicePrincipal.DisplayName} already exists " +
          $"in the customer tenant"));
      }
    }

    // Get app roles assigned to the sp in the customer
    try
    {
      currentRoles = await WithAutoRetry(
        () => _graphApi.GetServicePrincipalAppRoleAssignments(
          credential,
          ourServicePrincipal.Id,
          cancellationToken));
      messages.Add(new(
        $"Retrieved {currentRoles.Count} roles already assigned to " +
        $"{ourServicePrincipal.DisplayName} in the customer tenant"));
    }
    catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
    {
      var msg = $"Error occurred while retrieving roles assigned to " +
                $"{ourServicePrincipal.DisplayName} in the customer tenant";
      var azErr = _azureExceptionHandler.HandleAzureException(ex, credential, msg);
      messages.Add(new(msg, azErr));
      return wrappedResult;
    }

    // Get delegated scopes assigned to the sp in the customer
    try
    {
      currentDelegatedScopes = await WithAutoRetry(
        () => _graphApi.GetOauth2PermissionGrants(credential, ourServicePrincipal.Id, cancellationToken));
      messages.Add(new(
        $"Retrieved {currentDelegatedScopes.Count} resources with delegated permission " +
        $"scopes already assigned to {ourServicePrincipal.DisplayName}:\n" +
        $"{string.Join('\n',
          currentDelegatedScopes.Select(s => $"Resource={s.ResourceId} | Scope = {s.Scope}"))}"));
    }
    catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
    {
      var msg = $"Error occurred while retrieving delegated scopes assigned " +
                $"to {ourServicePrincipal.DisplayName} in the customer tenant";
      var azErr = _azureExceptionHandler.HandleAzureException(ex, credential, msg);
      messages.Add(new(msg, azErr));
      return wrappedResult;
    }


    AppRegistrationType consentingTo;
    List<RequiredResourceAccess> requiredResourceAccess;
    if (credential.TenantAzurePermissionLevel == AzurePermissionLevel2.DefaultAppRegistration)
    {
      consentingTo = AppRegistrationType.Backend;
      requiredResourceAccess = _defaultExpectedPermissions;
    }
    else
    {
      consentingTo = AppRegistrationType.Custom;
      try
      {
        messages.Add(new(
          $"Looking up the app registration {appRegAppId} in the " +
          $"partner tenant to determine required resources"));
        var appReg = await WithAutoRetry(
          () => _graphApi.GetAppRegistration(partnerCredential, appRegAppId, cancellationToken));
        if (appReg?.RequiredResourceAccess == null)
        {
          var azErr = _azureExceptionHandler.HandleAzureException(
            $"App Registration with app id {appRegAppId} not found in the partner tenant",
            partnerCredential.TenantPrincipalId,
            $"Error occurred while retrieving the required resources for the app registration {appRegAppId} in the partner tenant");
          messages.Add(new(
            "Error occurred while determining the permissions to apply to the customer",
            azErr));
          return wrappedResult;
        }

        requiredResourceAccess = appReg.RequiredResourceAccess;
      }
      catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
      {
        var msg = $"Error occurred while retrieving the partner's app " +
                  $"registration with app id {appRegAppId} from the partner tenant";
        var azErr = _azureExceptionHandler.HandleAzureException(ex, partnerCredential, msg);
        messages.Add(new(msg, azErr));
        return wrappedResult;
      }

      messages.Add(new(
        $"Successfully retrieved the {requiredResourceAccess.Count} required resources " +
        $"from the app registration in the partner tenant"));
    }

    List<(ServicePrincipal ServicePrincipal, RequiredResourceAccess RequiredResourceAccess)> expectedPermissions = [];

    foreach (var app in requiredResourceAccess)
    {
      messages.Add(new(
        $"Required resource: {Translate(resourceAppId: app.ResourceAppId!).DisplayName}" +
        $" | Roles: {string.Join(' ', (app.ResourceAccess ?? []).Where(a => a.Type == "Role").Select(a => Translate(appRoleId: a.Id.ToString()).DisplayName))}" +
        $" | Scopes: {string.Join(' ', (app.ResourceAccess ?? []).Where(a => a.Type == "Scope").Select(a => Translate(delegatedScopeId: a.Id.ToString()).DisplayName))}"));

      ServicePrincipal? sp;
      try
      {
        sp = await WithAutoRetry(
          () => _graphApi.GetServicePrincipal(credential, app.ResourceAppId!, cancellationToken));
        if (sp?.Id == null)
        {
          // If the app does not exist, we can't add permissions for it. E.g. Defender etc.
          messages.Add(new($"Service Principal for " +
                           $"{Translate(resourceAppId: app.ResourceAppId!).DisplayName} not found in customer " +
                           $"tenant"));
          continue;
        }

        expectedPermissions.Add((sp, app));
      }
      catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
      {
        var msg = $"Error occurred while retrieving the service principal for " +
                  $"{Translate(resourceAppId: app.ResourceAppId!).DisplayName} from the customer tenant";
        var azErr = _azureExceptionHandler.HandleAzureException(ex, credential, msg);
        messages.Add(new(msg, azErr));
        return wrappedResult;
      }
    }

    // Step 2: Add all required Delegated permissions

    foreach (var (sp, app) in expectedPermissions)
    {
      if (sp.Id is null) continue;
      var existingScope = currentDelegatedScopes.Find(cds => cds.ResourceId == sp.Id);
      var expectedScopes = app.ResourceAccess!
        .Where(ra => ra.Type == "Scope")
        .Select(ra => GraphPermissionDescriptions.DelegatedScopesDict
          .GetValueOrDefault(ra.Id.ToString()!)?.Value ?? ra.Id.ToString())
        .ToList();
      var newScope = string.Join(' ', expectedScopes);
      if (existingScope?.Id == null)
      {
        try
        {
          await WithAutoRetry(() => _graphApi.CreateOauth2PermissionGrant(
            credential,
            ourServicePrincipal.Id,
            sp.Id,
            "AllPrincipals",
            newScope,
            cancellationToken));
          messages.Add(new(
            $"Successfully granted {newScope} delegated permissions for {sp.DisplayName} to " +
            $"{ourServicePrincipal.DisplayName}"));
        }
        catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
        {
          var msg = $"Error occurred while granting {newScope} delegated permissions to " +
                    $"{ourServicePrincipal.DisplayName}";
          var azErr = _azureExceptionHandler.HandleAzureException(ex, credential, msg);
          messages.Add(new(msg, azErr));
          return wrappedResult;
        }
      }
      else
      {
        var existingScopes = existingScope.Scope?.Split(' ') ?? [];
        var difference = expectedScopes.Except(existingScopes).ToList();
        if (difference.Count == 0)
        {
          messages.Add(new(
            $"All expected delegated permissions already granted for {sp.DisplayName}: {newScope}"));
          continue;
        }

        try
        {
          await WithAutoRetry(async () =>
          {
            await _graphApi.UpdateOauth2PermissionGrantScope(
              credential,
              existingScope.Id,
              newScope,
              cancellationToken);
            return (object?)null;
          });
          var alreadyGranted = existingScopes.Intersect(expectedScopes).ToList();
          messages.Add(new(
            $"Successfully updated the granted delegated permissions for {sp.DisplayName} to " +
            $"{ourServicePrincipal.DisplayName}: " +
            $"{string.Join(' ', difference)}" +
            (alreadyGranted.Count != 0
              ? $" (some were already granted: {string.Join(' ', alreadyGranted)})"
              : "")));
        }
        catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
        {
          var msg = $"Error occurred while updating the granted delegated permissions for " +
                    $"{sp.DisplayName} to {ourServicePrincipal.DisplayName}: " +
                    $"{string.Join(' ', difference)}";
          var azErr = _azureExceptionHandler.HandleAzureException(ex, credential, msg);
          messages.Add(new(msg, azErr));
          return wrappedResult;
        }
      }
    }

    // Step 3: Add all required Application permissions
    var permissionsToAdd = new List<GrantAppRoleRequest>();
    foreach (var (sp, app) in expectedPermissions)
    {
      if (sp.Id is null) continue;
      var roleResources = app.ResourceAccess
        !.Where(ra => ra.Type == "Role").Select(ra => ra.Id.ToString()!);
      foreach (var singleResource in roleResources)
      {
        if (currentRoles.Exists(cr => cr.AppRoleId?.ToString() == singleResource))
        {
          messages.Add(new(
            $"Role {Translate(appRoleId: singleResource).DisplayName} already assigned to " +
            $"{ourServicePrincipal.DisplayName} in customer tenant"));
          continue;
        }

        permissionsToAdd.Add(new GrantAppRoleRequest(
          Guid.Parse(ourServicePrincipal.Id),
          Guid.Parse(sp.Id),
          Guid.Parse(singleResource)));
      }
    }

    if (permissionsToAdd.ToArray() is [var firstPermission, .. var lastPermissions])
    {
      // do the first role grant separately so we can retry the auth error if it occurs
      var grantedRoles = await WithAutoRetry(() => _graphApi.GrantAppRolesToServicePrincipal(
        credential,
        ourServicePrincipal.Id,
        new[] { firstPermission },
        cancellationToken));

      if (grantedRoles.ToArray() is [var g, ..])
      {
        switch (g.Result.Value)
        {
          case Exception e:
            var msg = $"Error occurred while adding role " +
                      $"{Translate(appRoleId: g.AppRoleId.ToString()).DisplayName} to " +
                      $"{ourServicePrincipal.DisplayName}";
            var azErr = _azureExceptionHandler.HandleAzureException(e, credential, msg);
            messages.Add(new(msg, azErr));
            return wrappedResult;
          case AppRoleAssignment:
            messages.Add(new($"Successfully added role {Translate(appRoleId: g.AppRoleId.ToString()).DisplayName} to " +
                             $"{ourServicePrincipal.DisplayName}"));
            break;
        }
      }

      grantedRoles = await WithAutoRetry(() => _graphApi.GrantAppRolesToServicePrincipal(
        credential,
        ourServicePrincipal.Id,
        lastPermissions,
        cancellationToken));
      var msgs = grantedRoles.Select(r =>
      {
        switch (r.Result.Value)
        {
          case Exception a:
            var msg = $"Error occurred while adding role " +
                      $"{Translate(appRoleId: r.AppRoleId.ToString()).DisplayName} to " +
                      $"{ourServicePrincipal.DisplayName}";
            var azErr = _azureExceptionHandler.HandleAzureException(a, credential, msg);
            return new AzureMessage(msg, azErr);
          case AppRoleAssignment:
            return new AzureMessage(
              $"Successfully added role {Translate(appRoleId: r.AppRoleId.ToString()).DisplayName} to " +
              $"{ourServicePrincipal.DisplayName}");
          default: throw new NotImplementedException();
        }
      });

      foreach (var msg in msgs) messages.Add(msg);
    }

    if (messages.All(m => m.Error == null || m.IsErrorNonFatal))
    {
      await using var ctx = _ctxFactory();

      // Attach the object to the context so that it is tracked and can be updated
      ctx.Attach(customerAzTenant);

      // update consent details for the customer
      await ctx.UpdateTenantConsentDate(customerAzTenant, DateTime.UtcNow, consentingTo, cancellationToken);

      messages.Add(new($"Customer is now consented to {consentingTo} app registration " +
                       $"({ourServicePrincipal.DisplayName})"));
    }

    if (appRegAppId != _azAdAuth.ClientId)
    {
      // we consented to Immy Backend SP so we could add consent for a different app reg, so now
      // we can clean up the Immy Backend SP
      try
      {
        await _partnerCenterApi.RemoveCustomerConsent(
          customerAzTenant.PartnerPrincipalId,
          customerAzTenant.PrincipalId,
          _azAdAuth.ClientId,
          cancellationToken);

        messages.Add(new(
          $"Successfully removed consent we gave the ImmyBot Backend Principal in the " +
          $"customer tenant since it is no longer needed."));
      }
      catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
      {
        var msg = $"Error occurred while removing the existing consent for " +
                  "the ImmyBot Backend Principal from the customer tenant";
        var azErr = _azureExceptionHandler
          .HandlePartnerCenterException(ex, msg, customerAzTenant.PartnerPrincipalId);
        messages.Add(new(msg, azErr, IsErrorNonFatal: true));
        return wrappedResult;
      }

      if (wrappedResult.DidBackendConsentAlreadyExist)
      {
        // the service principal consent previously existed - we need to give it at least
        // User.Read so that users at the customer can still log in
        await _partnerCenterApi
          .AddCustomerConsent(customerAzTenant.PartnerPrincipalId,
            _azAdAuth.ClientId,
            customerAzTenant.PrincipalId,
            new(
              ApplicationId: _azAdAuth.ClientId, // CpvApplicationId
              ApplicationGrants:
              [
                new ApplicationGrant(
                  EnterpriseApplicationId: _msGraphResourceId,
                  Scope: $"User.Read"),
              ]),
            cancellationToken);

        messages.Add(new(
          $"Successfully reinstated User.Read consent for the ImmyBot " +
          $"Backend Principal in the customer tenant."));
      }
    }

    return wrappedResult;
  }

  public async Task DisambiguateAzureTenantType(
    AzureTenant azTenant,
    bool allowResettingCustomerToPartner,
    CancellationToken cancellationToken)
  {
    await using var ctx = _ctxFactory();
    cancellationToken.ThrowIfCancellationRequested();

    // Attach now before we retrieve the full list so that our tenant is tracked and we can update it
    ctx.Attach(azTenant);

    // get linked tenants
    var azTenants = await ctx.AzureTenants.ToListAsync(cancellationToken);

    await DisambiguateAzureTenantType(ctx, azTenant, azTenants, allowResettingCustomerToPartner, cancellationToken);
  }

  private async Task<AzureError?> DisambiguateAzureTenantType(
    ImmybotDbContext ctx,
    AzureTenant azTenant,
    List<AzureTenant> allAzTenants,
    bool allowResettingCustomerToPartner,
    CancellationToken cancellationToken)
  {
    cancellationToken.ThrowIfCancellationRequested();
    if (azTenant is { AzureTenantType: AzTenantType.Standalone } or
        { AzureTenantType: AzTenantType.Customer, PartnerPrincipalId: null })
    {
      // Either we have a customer with no partner principal id or we have a standalone
      // See if any of the partner tenants have this tenant as a customer and link it to this one
      var parentPartnerPrincipalId =
        (await FindParentPartnerTenant(azTenant, allAzTenants, cancellationToken))?.PrincipalId;
      await ctx.UpdateAzureTenantPartnerPrincipalId(azTenant,
        parentPartnerPrincipalId,
        cancellationToken);
    }

    if ((allowResettingCustomerToPartner || azTenant.AzureTenantType == AzTenantType.Standalone))
    {
      var ll = await CheckIsTenantAPartnerTenant(azTenant, cancellationToken);
      if (ll.FailedReason is not null) return ll.FailedReason;
      if (ll.IsPartner)
      {
        await ctx.SetAzureTenantIsPartner(azTenant, cancellationToken);
      }
    }

    var syncResult = await SyncAzureDetailsForTenant(azTenant, cancellationToken);
    await DetectAzureTenantProblems(azTenant, syncResult, cancellationToken);
    return null;
  }

  private async Task DetectAzureTenantProblems(
    AzureTenant azTenant,
    AzureTenantDetailsSyncResult syncResult,
    CancellationToken cancellationToken)
  {
    // Emit a "AzureTenantProblemsDetected" notification event if any problems are detected
    // Clear existing "AzureTenantProblemsDetected" notification if no problems are detected

    // Checks to perform:
    // Partner missing gdap relationships (i.e.relationships list is empty for a partner)
    // Missing consents (you have an immy tenant mapped but not consented, go here to consent)
    // TODO: GDAP relationship expiring soon (i.e. less than 30 days) and not set to auto-extend

    var wasNotificationEventEmittingEnabled = _azureExceptionHandler.NotificationEventEmittingEnabled;

    // We'll attach any azure errors to the problems if they occur, so don't emit az err
    // notification events
    _azureExceptionHandler.DisableNotificationEventEmitting();

    try
    {
      var credential = await _immyTokenCredentialFactory
        .GetImmyTokenCredential(azTenant, cancellationToken);

      var problems = new List<AzureTenantProblem>();
      if (azTenant.AzureTenantType == AzTenantType.Partner)
      {
        try
        {
          var rr = await _graphApi.GetAllDelegatedAdminRelationships(credential, cancellationToken);
          if (rr.Count == 0)
          {
            problems.Add(new AzureTenantProblem(
              "Partner tenant has no delegated admin relationships",
              "Without delegated admin relationships, ImmyBot cannot access the partner's customers"));
          }
        }
        catch (Exception ex) when (_azureExceptionHandler.IsAzureError(ex))
        {
          var azErr = _azureExceptionHandler.HandleAzureException(ex,
            credential,
            "Error occurred while getting delegated admin relationships");
          problems.Add(new AzureTenantProblem(
            "Unable to retrieve partner tenant's delegated admin relationships",
            "Without delegated admin relationships, ImmyBot cannot access the partner's customers",
            AzureErrorId: azErr.Id));
        }
      }

      if (azTenant.ConsentDetails is not { ConsentedWith: not null, ConsentDateUtc: not null })
      {
        problems.Add(new AzureTenantProblem(
          "Tenant has not been consented-to",
          "Without consent, ImmyBot cannot access the tenant's Azure resources"));
      }

      if (problems.Count != 0 || syncResult.FailedReason is not null)
      {
        _eventEmitter.EmitEvent(
          new AzureTenantProblemsDetectedEvent(
            azTenant.PrincipalId,
            [.. problems],
            syncResult.FailedReason is not null));
      }
      else
      {
        _eventEmitter.EmitEvent(new AzureTenantProblemsClearedEvent(azTenant.PrincipalId));
      }
    }
    finally
    {
      if (wasNotificationEventEmittingEnabled)
        _azureExceptionHandler.EnableNotificationEventEmitting();
    }
  }

  // Loops over all tenants and finds the first partner tenant that has this tenant as a customer
  public async Task<AzureTenant?> FindParentPartnerTenant(
    AzureTenant azTenant,
    List<AzureTenant> allAzTenants,
    CancellationToken cancellationToken)
  {
    foreach (var p in allAzTenants.Where(t => t.AzureTenantType == AzTenantType.Partner))
    {
      var customers = await GetAllCustomerIdsForPartnerTenant(p, cancellationToken);
      if (customers.Contains(azTenant.PrincipalId))
      {
        return p;
      }
    }

    return null;
  }

  public async Task<List<TenantGroupsDisplayNameResult>> GetGroupDisplayNamesAtTenantsByIds(
    ICollection<GetTenantGroupDisplayNamesPayload> tenantGroups,
    CancellationToken cancellationToken)
  {
    var tasks = new List<Task<TenantGroupsDisplayNameResult>>();
    foreach (var tenantGroup in tenantGroups)
    {
      cancellationToken.ThrowIfCancellationRequested();
      tasks.Add(Task.Run(async () =>
        {
          var (policy, context) =
            _policyRegistry.GetTenantAzureGroupNamesCachePolicyAndContext(tenantGroup.PrincipalId,
              tenantGroup.GroupIds);
          return await policy.ExecuteAsync(async (ctx, t) =>
            {
              var credential = await _immyTokenCredentialFactory
                .GetImmyTokenCredential(tenantGroup, t);
              return new TenantGroupsDisplayNameResult(
                tenantGroup.PrincipalId,
                await _graphApi
                  .GetGroupDisplayNames(credential, tenantGroup.GroupIds, t));
            },
            context,
            cancellationToken);
        },
        cancellationToken));
    }

    var results = await Task.WhenAll(tasks);
    return [.. results];
  }

  public async Task<AzureAuthParameters> GetAzureTenantAuthParameters(
    Tenant tenant,
    string resourceUri)
  {
    if (tenant.AzureTenantLink?.AzureTenant is not { } azTenant)
      throw new ArgumentException("Tenant must have Azure tenant details", nameof(tenant));
    var credential = await _immyTokenCredentialFactory
      .GetImmyTokenCredential(azTenant, _appLifetime.ApplicationStopping);
    var accessToken = await credential.GetTokenAsync(
      new TokenRequestContext([$"{resourceUri}/.default"], tenantId: azTenant.PrincipalId),
      _appLifetime.ApplicationStopping);
    var clientId = credential.ResolvedClientId!;
    return new AzureAuthParameters
    {
      Token = accessToken.Token, ClientId = clientId, TenantId = azTenant.PrincipalId
    };
  }

  public async Task<AzureAuthParameters> GetAzureTenantAuthParameters(
    Tenant tenant,
    AzureResourceAlias endpoint = AzureResourceAlias.AzureAD)
  {
    string resourceUri = string.Empty;
    switch (endpoint)
    {
      case AzureResourceAlias.AzureAD:
        resourceUri = "https://graph.windows.net";
        break;
      case AzureResourceAlias.MSGraph:
        resourceUri = "https://graph.microsoft.com";
        break;
      case AzureResourceAlias.KeyVault:
        resourceUri = "https://vault.azure.net";
        break;
    }

    return await GetAzureTenantAuthParameters(tenant, resourceUri);
  }

  public async Task<ICollection<string>> GetAllCustomerIdsForPartnerTenant(
    AzureTenant azTenant,
    CancellationToken cancellationToken)
  {
    if (azTenant.AzureTenantType != AzTenantType.Partner) return Array.Empty<string>();
    await _partnerTenantCustomerIdsCacheAccessLock.WaitAsync(cancellationToken);
    try
    {
      if (_partnerTenantCustomerIdsCache.TryGetValue(azTenant.PrincipalId, out var customerIds))
        return customerIds;
      var gdapCustomers = await GetPartnerTenantsDelegatedAdminCustomers(azTenant, cancellationToken);
      customerIds = gdapCustomers.SelectMany(a => a.Result.Value is ICollection<AzureTenantCustomer> cust
          ? cust.Select(c => c.TenantId?.ToString())
          : Array.Empty<string>())
        .Where(p => !string.IsNullOrEmpty(p))
        .OfType<string>()
        .ToHashSet();
      _partnerTenantCustomerIdsCache[azTenant.PrincipalId] = customerIds;
      return customerIds;
    }
    finally
    {
      _partnerTenantCustomerIdsCacheAccessLock.Release();
    }
  }


  private static TranslateObj Translate(string? resourceAppId = null,
    string? appRoleId = null,
    string? delegatedScopeId = null)
  {
    if (resourceAppId != null)
    {
      if (resourceAppId == _azGraphResourceId)
      {
        return new TranslateObj("Azure AD Graph", "Azure AD Graph");
      }

      if (resourceAppId == _msGraphResourceId)
      {
        return new TranslateObj("Microsoft Graph", "Microsoft Graph");
      }

      return new TranslateObj(resourceAppId, resourceAppId);
    }

    if (appRoleId != null)
    {
      if (GraphPermissionDescriptions.ApplicationScopesDict.TryGetValue(appRoleId, out var role))
      {
        return new TranslateObj(role.Value, role.ConsentDisplayName);
      }

      return new TranslateObj(appRoleId, appRoleId);
    }

    if (delegatedScopeId != null)
    {
      if (GraphPermissionDescriptions.DelegatedScopesDict.TryGetValue(delegatedScopeId, out var scope))
      {
        return new TranslateObj(scope.Value, scope.ConsentDisplayName);
      }

      return new TranslateObj(delegatedScopeId, delegatedScopeId);
    }

    throw new NotImplementedException("Must provide either resourceAppId or appRoleId");
  }
}
