using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Interface.MetaScripts;
using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Shared.Primitives;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Polly;

namespace Immybot.Backend.Application.Actions;

internal class ComputerAssignmentActions(
  IProviderActions _providerActions,
  IAzureActions _azureActions,
  ISoftwareActions _softwareActions,
  IScriptActions _scriptActions,
  Func<ImmybotDbContext> _ctxFactory,
  IMetascriptInvoker _metascriptInvoker) : IComputerAssignmentActions
{
  public Task<DisposableValue<IQueryable<Computer>>> GetComputersInTarget(
    TargetType targetType,
    TargetGroupFilter targetGroupFilter,
    string? target = null,
    int? tenantId = null,
    bool includeChildTenants = false,
    bool allowAccessToParentTenant = false,
    int? providerLinkId = null,
    Guid? providerDeviceGroupType = null,
    Guid? providerClientGroupType = null,
    Guid? cacheGroupId = null,
    IAsyncPolicy? cachePolicy = null,
    Context? policyContext = null,
    bool excludeOnboarding = false,
    bool excludeOnboarded = false,
    bool excludeOffline = false,
    bool excludeUnlinked = false,
    int? filterScriptComputerId = null,
    bool withPrimaryPerson = false,
    CancellationToken cancellationToken = default,
    bool withAgents = false,
    bool withTenant = false,
    bool withTags = false,
    ICollection<string>? withInventoryKeyResults = null,
    bool asNoTracking = false,
    bool isForScheduleOrDeploymentResolution = true) => _ctxFactory.CreateDisposableValue(async dbContext =>
  {
    var context = policyContext ?? [];
    var policy = cachePolicy ?? Policy.NoOpAsync();

    var q = targetType switch
    {
      TargetType.All
        => dbContext.GetAllComputers(tenantId, includeChildTenants: includeChildTenants),
      TargetType.Computer
        => dbContext.GetComputersByIds(new[] { Convert.ToInt32(target) }),
      TargetType.Person
        => dbContext.GetComputersByPrimaryPersonIds([
          int.Parse(target ?? throw new InvalidOperationException("Azure group target is null"))
        ]),
      TargetType.AzureGroup
        => await GetComputersInAzureGroup(
          dbContext,
          target ?? throw new InvalidOperationException("Azure group target is null"),
          Guard.EnsureNotNull(tenantId, nameof(tenantId)),
          includeChildTenants: includeChildTenants,
          policy,
          context,
          cancellationToken),
      TargetType.AllForTenant
        => dbContext.GetAllComputers(Guard.EnsureNotNull(tenantId, nameof(tenantId)), includeChildTenants: includeChildTenants),
      TargetType.ProviderDeviceGroup
        => await GetComputersInProviderDeviceGroup(
          dbContext,
          target ?? throw new InvalidOperationException("Device group target is null"),
          Guard.EnsureNotNull(providerLinkId, nameof(providerLinkId)),
          Guard.EnsureNotNull(providerDeviceGroupType, nameof(providerDeviceGroupType)),
          policy,
          context,
          cancellationToken,
          tenantId: tenantId,
          includeChildTenants: includeChildTenants),
      TargetType.ProviderClientGroup
        => await GetComputersInProviderClientGroup(
          dbContext,
          target ?? throw new InvalidOperationException("Client group target is null"),
          Guard.EnsureNotNull(providerLinkId, nameof(providerLinkId)),
          Guard.EnsureNotNull(providerClientGroupType, nameof(providerClientGroupType)),
          policy,
          context,
          cancellationToken),
      TargetType.FilterScript
        when await RunFilterScriptTarget(
          target ?? throw new InvalidOperationException("Filter script target is null"),
          cancellationToken,
          computerId: filterScriptComputerId,
          cachePolicy: policy,
          policyContext: context,
          cacheId: cacheGroupId) is ICollection<int> computerIds
        => dbContext.GetComputersByIds(computerIds),
      TargetType.TenantFilterScript
        when await RunFilterScriptTarget(
          target ?? throw new InvalidOperationException("Tenant filter script target is null"),
          cancellationToken,
          tenantId: tenantId,
          includeChildTenants: includeChildTenants,
          allowAccessToParentTenant: allowAccessToParentTenant,
          computerId: filterScriptComputerId,
          cachePolicy: policy,
          policyContext: context,
          cacheId: cacheGroupId) is ICollection<int> computerIds
        => dbContext.GetComputersByIds(computerIds),
      TargetType.Metascript
        => throw new NotSupportedException("Previewing metascripts is not supported"),
      TargetType.TenantMetascript
        => throw new NotSupportedException("Previewing metascripts is not supported"),
      TargetType.Tag
        => dbContext.GetComputersInTag(Convert.ToInt32(target)),
      TargetType.TenantTag
        => dbContext.GetComputersInTagForTenant(Convert.ToInt32(target), Guard.EnsureNotNull(tenantId, nameof(tenantId)),
          includeChildTenants: includeChildTenants),
      _ => throw new NotSupportedException($"Previewing TargetType: {targetType} with target {target} not implemented"),
    };

    // do not consider computers excluded from maintenance
    if (isForScheduleOrDeploymentResolution)
    {
      // do not consider computers excluded from maintenance for schedule or deployment resolution
      q = q.Where(a => !a.ExcludeFromMaintenance);
    }

    // do not consider computers excluded from cross tenant deployments and schedules
    if (isForScheduleOrDeploymentResolution && TargetAssignmentHelpers.GetTargetScopeForTargetType(
      _providerActions,
      targetType,
      providerDeviceGroupType: providerDeviceGroupType) is TargetScope.CrossTenant)
    {
      q = q.Where(a => !a.Tenant!.TenantPreferences!.ExcludeFromCrossTenantDeploymentsAndSchedules);
    }
    if (excludeOnboarding) q = q.WhereIsOnboarded();
    if (excludeOnboarded) q = q.WhereIsNotOnboarded();
    if (excludeOffline) q = q.WhereIsOnline();
    if (excludeUnlinked) q = q.WhereIsLinked();
    if (withPrimaryPerson) q = q.Include(a => a.PrimaryPerson);
    if (withAgents) q = q.Include(a => a.Agents).ThenInclude(a => a.ProviderLink);
    if (withTenant) q = q.IncludeTenantAndAzData();
    if (withTags)
    {
      q = q.Include(a => a.ComputerTags).ThenInclude(a => a.Tag);
      q = q.Include(a => a.Tenant!.TenantTags).ThenInclude(a => a.Tag);
      q = q.Include(a => a.PrimaryPerson!.PersonTags).ThenInclude(a => a.Tag);
    }
    if (withInventoryKeyResults is { Count: > 0 } keysToInclude)
      q = q.Include(a => a.LatestInventoryScriptResults
        .Where(r => keysToInclude.Contains(r.InventoryKey)));
    if (asNoTracking) q = q.AsNoTracking();

    return q.AsExpandable().ApplyTargetGroupFilter(targetGroupFilter);
  });

  private async Task<ICollection<int>> RunFilterScriptTarget(
    string target,
    CancellationToken cancellationToken,
    int? tenantId = null,
    bool includeChildTenants = false,
    bool allowAccessToParentTenant = false,
    int? computerId = null,
    IAsyncPolicy? cachePolicy = null,
    Context? policyContext = null,
    Guid? cacheId = null)
  {
    var tenantFilterScriptIdentifier = ScriptIdentifier.FromJson(target);
    if (tenantFilterScriptIdentifier is null) return Array.Empty<int>();
    var script = await _softwareActions.GetScript(
      tenantFilterScriptIdentifier.ScriptType,
      tenantFilterScriptIdentifier.ScriptId.ToString(),
      cachePolicy: cachePolicy,
      policyContext: policyContext,
      cancellationToken: cancellationToken);

    if (script is null) return Array.Empty<int>();
    if (script.ScriptCategory != ScriptCategory.FilterScriptDeploymentTarget)
      return Array.Empty<int>();

    var canAccessMspResources = true;
    if (script.ScriptType is DatabaseType.Local)
    {
      // If the local script is a local script, we need to check if it can access msp resources.
      // A local script can access msp resources if any of the following are true:
      // 1. The script has no authorizations (e.g. all tenants can use it)
      // 2. The script has multiple authorizations. (Only a msp can authorize a script for multiple tenants)
      // 3. The script is owned by an MSP tenant
      var authorizations = await _scriptActions.GetLocalScriptAuthorizations(
        script.Id,
        cancellationToken,
        policyContext: policyContext,
        cachePolicy: cachePolicy);

      canAccessMspResources = authorizations.Count switch
      {
        0 => true,
        > 1 => true,
        _ => authorizations.FirstOrDefault()?.IsMsp is true
      };
    }

    var res = await _metascriptInvoker.GetComputersForFilterScript(
      // This method will only ever be called for deployment resolution so I believe it is safe (hopefully) (for now)
      canAccessMspResources: canAccessMspResources,
      canAccessParentTenant: allowAccessToParentTenant,
      script,
      cancellationToken,
      limitToTenantId: tenantId,
      includeChildTenants: includeChildTenants,
      computerId: computerId,
      cachePolicy: cachePolicy,
      policyContext: policyContext,
      cacheId: cacheId);
    // TODO: pulling all ps computer objects returned by the filter script into memory
    // could cause memory spikes / OOM exceptions
    return res.OutputAsCollection.Select(a => a.Id).ToArray();
  }

  private async Task<IQueryable<Computer>> GetComputersInProviderClientGroup(
    ImmybotDbContext dbContext,
    string target,
    int providerLinkId,
    Guid providerClientGroupType,
    IAsyncPolicy cachePolicy,
    Context policyContext,
    CancellationToken cancellationToken)
  {
    var providerLink = dbContext.GetProviderLink(providerLinkId, includeClients: true);
    if (providerLink is null) return dbContext.ShortCircuitNoResults<Computer>();
    var providerClientIds = await _providerActions
      .GetClientIdsInGroup(providerLink, providerClientGroupType, target,
        policyContext: policyContext,
        cachePolicy: cachePolicy,
        token: cancellationToken);
    var applicableTenantIds = providerLink.ProviderClients.Where(a => providerClientIds.Contains(a.ExternalClientId)).Select(a => a.LinkedToTenantId);
    var computersInTenants = dbContext.GetAllComputers().Where(a => applicableTenantIds.Contains(a.TenantId));
    return computersInTenants;
  }

  private async Task<IQueryable<Computer>> GetComputersInProviderDeviceGroup(
    ImmybotDbContext dbContext,
    string target,
    int providerLinkId,
    Guid providerDeviceGroupType,
    IAsyncPolicy cachePolicy,
    Context policyContext,
    CancellationToken cancellationToken,
    int? tenantId = null,
    bool includeChildTenants = false)
  {
    var providerLink = dbContext.GetProviderLink(providerLinkId);
    if (providerLink is null) return dbContext.ShortCircuitNoResults<Computer>();
    var agentIds = await _providerActions
      .GetDevicesInGroup(providerLink, providerDeviceGroupType, target,
        policyContext: policyContext,
        cachePolicy: cachePolicy,
        token: cancellationToken);
    return dbContext
      .GetComputersByProviderAgentIds(providerLinkId, agentIds,
        tenantId: tenantId,
        includeChildTenants: includeChildTenants);
  }

  private async Task<IQueryable<Computer>> GetComputersInAzureGroup(
    ImmybotDbContext dbContext,
    string target,
    int tenantId,
    bool includeChildTenants,
    IAsyncPolicy cachePolicy,
    Context policyContext,
    CancellationToken cancellationToken)
  {
    var tenant = dbContext.GetTenantById(tenantId, includeAzData: true);
    if (tenant is null) return dbContext.ShortCircuitNoResults<Computer>();
    var computerIds = await _azureActions.GetComputerIdsInAzureGroupAtTenant(
      tenant,
      target,
      policyContext,
      cachePolicy,
      cancellationToken: cancellationToken);

    if (!includeChildTenants) return dbContext.GetComputersByIds(computerIds);

    var descendentTenantIds = dbContext.GetDescendentTenantIds(tenantId);
    var descendentTenants = await dbContext.GetTenantsByIds(descendentTenantIds, includeAzData: true).ToListAsync(cancellationToken);
    foreach (var descendentTenant in descendentTenants)
    {
      var computerIdsInDescendentTenants = await _azureActions.GetComputerIdsInAzureGroupAtTenant(
        descendentTenant,
        target,
        policyContext,
        cachePolicy,
        cancellationToken: cancellationToken);
      computerIds.AddRange(computerIdsInDescendentTenants);
    }
    return dbContext.GetComputersByIds(computerIds);
  }
}
