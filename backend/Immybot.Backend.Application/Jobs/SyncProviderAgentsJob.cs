using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Commands;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Persistence;
using Immybot.Backend.Providers.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.Application.Jobs;

public class SyncProviderAgentsJob : IImmyProviderJob
{
  private readonly IServiceScopeFactory _serviceScopeFactory;
  private readonly int _providerLinkId;
  private readonly IEnumerable<string>? _clientIds;
  public string JobName { get; }

  public SyncProviderAgentsJob(IServiceScopeFactory serviceScopeFactory, int providerLinkId, IEnumerable<string>? clientIds = null)
  {
    _serviceScopeFactory = serviceScopeFactory;
    _providerLinkId = providerLinkId;
    _clientIds = clientIds;
    JobName = $"sync-provider-agents-for-link-{providerLinkId}";
  }

  public async Task Run(CancellationToken token)
  {
    using var scope = _serviceScopeFactory.CreateScope();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<SyncProviderAgentsJob>>();
    using var _ =  logger.BeginScope(new Dictionary<string, object?>
    {
      ["ProviderLinkId"] = _providerLinkId
    });

    logger.LogDebug("Starting {job}", nameof(SyncProviderAgentsJob));
    try
    {
      // fetch agents from provider
      var providerActions = scope.ServiceProvider.GetRequiredService<IProviderActions>();
      await using var ctx = scope.ServiceProvider.GetRequiredService<ImmybotDbContext>();
      var link = ctx.GetProviderLink(_providerLinkId);
      if (link is null) return;

      List<string> linkedClientIds;
      if (_clientIds is null)
      {
        linkedClientIds = await ctx.GetClientsForProviderLink(link.Id)
          .Where(a => a .LinkedToTenantId != null)
          .Select(a => a.ExternalClientId)
          .ToListAsync(token);
      }
      else
      {
        linkedClientIds = _clientIds.ToList();
      }

      var provider = await providerActions.GetProvider(link, token, TimeSpan.FromSeconds(60));
      if (provider is not ISupportsListingAgents supportsListingAgents) return;
      var agents = await supportsListingAgents.GetAgents(
        clientIds: linkedClientIds,
        token: token);
      var cmd = scope.ServiceProvider.GetRequiredService<ISyncProviderAgentsCommand>();
      await cmd.Sync(_providerLinkId, agents.ToList(),  clientIds: _clientIds?.ToList(), token: token);
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "{job} failed", nameof(SyncProviderAgentsJob));
    }
    finally
    {
      logger.LogDebug("Ending {job}", nameof(SyncProviderAgentsJob));
    }
  }
}
