using System;
using System.Collections.Generic;
using System.Linq;
using Immybot.Backend.Application.DbContextExtensions.Preferences;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

namespace Immybot.Backend.Application.DbContextExtensions;

public static class BrandingExtensions
{
  public static Branding GetDefaultBranding(this ImmybotDbContext context) =>
    context.Brandings.First(a => a.Id == context.GetApplicationPreferences().DefaultBrandingId);

  public static IQueryable<Branding> GetAllBrandings(this ImmybotDbContext context, bool includeTenant = false)
  {
    var brandings = context.Brandings.AsNoTracking();
    if (includeTenant)
    {
      brandings = brandings.Include(b => b.Tenant);
    }
    return brandings;
  }

  public static IQueryable<Branding> GetAllBrandingsForTenant(this ImmybotDbContext context, int tenantId, bool includeTenant = false)
  {
    var brandings = context.Brandings.Where(b => b.TenantId == tenantId).AsNoTracking();
    if (includeTenant)
    {
      brandings = brandings.Include(a => a.Tenant);
    }
    return brandings;
  }

  public static Branding? GetBrandingById(this ImmybotDbContext context, int brandingId) => context.Brandings.Find(brandingId);

  public static Branding CreateBranding(this ImmybotDbContext context, ICreateBrandingPayload payload)
  {
    var branding = new Branding();
    context.Entry(branding).CurrentValues.SetValues(payload);
    context.Brandings.Add(branding);
    context.SaveChanges();
    return branding;
  }

  public static Branding? UpdateBranding(this ImmybotDbContext context, IUpdateBrandingPayload payload)
  {
    var existing = context.Brandings.Find(payload.Id);
    if (existing == null) return null;

    // for some reason, existing.tenantId is still not null after calling CurrentValues.SetValues(payload)
    // and the state would get marked as Deleted instead of Modified.
    if (!payload.TenantId.HasValue) existing.TenantId = null;

    context.Entry(existing).CurrentValues.SetValues(payload);
    context.SaveChanges();
    return existing;
  }
  public static void DeleteBranding(this ImmybotDbContext context, Branding branding)
  {
    var applicationPreferences = context.GetApplicationPreferences();
    // disallow deleting of global default branding
    if (applicationPreferences != null && applicationPreferences.DefaultBrandingId == branding.Id)
    {
      throw new DeleteDefaultBrandingException();
    }
    context.Brandings.Remove(branding);
    context.SaveChanges();
  }

  /// <summary>
  /// Get the branding that applies to the given tenant for the provided date
  /// </summary>
  public static Branding GetBrandingForTenantAtDate(this ImmybotDbContext context, int tenantId, DateTime date)
  {
    var brands = context.Brandings.AsNoTracking().ToList();
    var applicationPreferences = context.GetApplicationPreferences();

    // get current branding from tenants specific brandings
    var tenantBrandings = brands
      .Where(a => a.TenantId == tenantId)
      .ToList();

    if (tenantBrandings.Any())
    {
      var applicableBrandings = GetApplicableBrandingsFromList(tenantBrandings, date);
      if (applicableBrandings.Any())
        return applicableBrandings.First();
    }

    // get current global brandings that is not the default
    var globalBrandings = brands
      .Where(a =>
        a.TenantId == null &&
        applicationPreferences.DefaultBrandingId != a.Id)
      .ToList();

    if (globalBrandings.Any())
    {
      var applicableBrandings = GetApplicableBrandingsFromList(globalBrandings, date);
      if (applicableBrandings.Any())
        return applicableBrandings.First();
    }

    // return the default branding at this point
    return context.Brandings.First(b =>
      b.Id == applicationPreferences.DefaultBrandingId);
  }

  /// <summary>
  /// Get the branding that applies to the given tenant for the current date
  /// </summary>
  /// <returns>The branding to use for the given tenant id, and the from
  /// address to use for the given tenant (the from address may be different
  /// from the returned branding's fromAddress when the branding does not
  /// have a fromAddress)</returns>
  public static (Branding branding, string fromAddress) GetCurrentBrandingForTenant(this ImmybotDbContext context, int tenantId)
  {
    var branding = context.GetBrandingForTenantAtDate(tenantId, DateTime.UtcNow);
    var fromAddress = branding.FromAddress;
    return (branding, fromAddress);
  }

  public static void SetGlobalDefaultBranding(this ImmybotDbContext context, int brandingId)
  {
    var branding = context.GetBrandingById(brandingId);
    if (branding == null)
      throw new ArgumentException($"Branding with id {brandingId} not found.");
    context.ApplicationPreferences.First().DefaultBrandingId = brandingId;
    context.SaveChanges();
  }

  /// <summary>
  /// Given a list of brandings, return a sorted list of the most applicable brandings.
  /// </summary>
  private static IEnumerable<Branding> GetApplicableBrandingsFromList(List<Branding> brandings, DateTime date)
  {
    List<Branding> applicableBrandings = [];
    List<Branding> nonDateBrandings = [];
    foreach (var branding in brandings)
    {
      if (branding.StartDate == null || branding.EndDate == null)
      {
        nonDateBrandings.Add(branding);
        continue;
      }

      DateTime start = branding.StartDate.Value;
      DateTime end = branding.EndDate.Value;

      // e.g. static holidays.  Christmas, 4th of July, etc
      if (branding.IgnoreYear != null && branding.IgnoreYear.Value)
      {
        // update start and end date to be the same as the current year
        start = new DateTime(date.Year, start.Month, start.Day, 0, 0, 0, DateTimeKind.Unspecified);
        end = new DateTime(date.Year, end.Month, end.Day, 0, 0, 0, DateTimeKind.Unspecified);

        // branding range spans over the new year and the current date is in the new year
        if (date.Month < start.Month && start.Month > end.Month)
        {
          start = start.AddYears(-1);
        }
        // branding range spans over the new year and the current date is not in the new year
        else if (start.Month > end.Month)
        {
          end = end.AddYears(1);
        }
      }
      if (date >= start && date <= end)
      {
        branding.StartDate = start;
        branding.EndDate = end;
        applicableBrandings.Add(branding);

      }
    }

    if (applicableBrandings.Any())
    {
      // order by whatever is closest to the current day
      return applicableBrandings
        .Where(a => a.StartDate != null)
        .OrderBy(a => (date - a.StartDate!.Value).Duration());
    }

    return nonDateBrandings;
  }

  public static bool IsBrandingTimeFormatValid(string timeFormat)
  {
    try
    {
      var formattedTime = DateTime.Now.ToString(timeFormat);
      return !String.IsNullOrEmpty(formattedTime);
    }
    catch
    {
      return false;
    }
  }
}
