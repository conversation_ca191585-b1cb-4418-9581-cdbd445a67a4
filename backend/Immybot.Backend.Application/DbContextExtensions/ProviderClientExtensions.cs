using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Providers;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Z.EntityFramework.Plus;

namespace Immybot.Backend.Application.DbContextExtensions;

public static class ProviderClientExtensions
{
  public static ProviderClient? GetProviderClient(this ImmybotDbContext ctx, int providerLinkId, int tenantId)
  => ctx.ProviderClients.AsNoTracking().FirstOrDefault(a => a.ProviderLinkId == providerLinkId && a.LinkedToTenantId == tenantId);

  public static string? GetProviderClientExternalClientId(this ImmybotDbContext ctx, int providerLinkId, int tenantId)
    => ctx.ProviderClients
      .AsNoTracking().Where(a => a.ProviderLinkId == providerLinkId && a.LinkedToTenantId == tenantId)
      .Select(a => a.ExternalClientId)
      .FirstOrDefault();

  public static IQueryable<ProviderClient> GetProviderClients(this ImmybotDbContext ctx)
    => ctx.ProviderClients.AsNoTracking();
  public static IQueryable<ProviderClient> GetClientsForProviderLink(
    this ImmybotDbContext ctx,
    int providerLinkId)
    => ctx.ProviderClients.AsNoTracking().Where(a => a.ProviderLinkId == providerLinkId);

  public static ProviderClient CreateProviderClient(
    this ImmybotDbContext ctx,
    ProviderClient providerClient)
  {
    ctx.ProviderClients.Add(providerClient);
    ctx.SaveChanges();
    return providerClient;
  }

  public static void DeleteClientsForProviderLink(this ImmybotDbContext ctx, int providerLinkId)
    => ctx.ProviderClients.AsNoTracking().IgnoreQueryFilters().Where(c => c.ProviderLinkId == providerLinkId).Delete();

  public static void UpdateProviderClient(this ImmybotDbContext ctx, ProviderClient client, bool doSave = true)
  {
    ctx.ProviderClients.Update(client);
    if (doSave) ctx.SaveChanges();
  }

  public static ICollection<ProviderClient> AddRemoveAndUpdateClientsForProviderLink(
    this ImmybotDbContext ctx,
    int providerLinkId,
    ICollection<IProviderClientDetails> clients)
  {
    var existingClients = ctx.ProviderClients
      .Where(c => c.ProviderLinkId == providerLinkId)
      .ToDictionary(c => c.ExternalClientId);

    var ret = new List<EntityEntry<ProviderClient>>(clients.Count);
    foreach (var newOrUpdated in clients)
    {
      if (existingClients.Remove(newOrUpdated.ExternalClientId, out var existing))
      {
        // we have an updated client
        var entry = ctx.Entry(existing);
        entry.CurrentValues.SetValues(newOrUpdated);
        ret.Add(entry);
      }
      else
      {
        // we have a new client
        var entry = ctx.ProviderClients.Add(new ProviderClient
        {
          ProviderLinkId = providerLinkId,
          ExternalClientId = newOrUpdated.ExternalClientId,
          ExternalClientName = newOrUpdated.ExternalClientName,
          Status = newOrUpdated.Status,
          Types = newOrUpdated.Types,
          InternalData = newOrUpdated.InternalData,
        });
        ret.Add(entry);
      }
    }

    // only delete clients if we retrieved some (safe guard against bugs returning 0 clients)
    if (clients.Any())
    {
      // anything left in the existingClients dictionary needs to be deleted
      foreach (var existing in existingClients.Values)
      {
        ctx.Remove(existing);
      }
    }
    ctx.SaveChanges();
    return ret.Select(e =>
    {
      e.State = EntityState.Detached;
      return e.Entity;
    })
      .ToList();
  }

  public static Task LinkProviderClientsToTenant(
    this ImmybotDbContext ctx,
    int providerLinkId,
    ICollection<string> externalClientIds,
    int tenantId)
  {
    return ctx.ProviderClients
      .Where(c => c.ProviderLinkId == providerLinkId && externalClientIds.Contains(c.ExternalClientId))
      .ExecuteUpdateAsync(a =>
        a.SetProperty(b => b.LinkedToTenantId, tenantId)
          .SetProperty(b => b.HasCompletedInitialAgentSync, false));
  }

  public static Task UnlinkProviderClients(
    this ImmybotDbContext ctx,
    int providerLinkId,
    ICollection<string> externalClientIds)
  {
    return ctx.ProviderClients
      .Where(c => c.ProviderLinkId == providerLinkId && externalClientIds.Contains(c.ExternalClientId))
      .ExecuteUpdateAsync(a =>
        a.SetProperty(
            b => b.LinkedToTenantId,
            (int?)null)
          .SetProperty(b => b.HasCompletedInitialAgentSync, false));
  }
}
