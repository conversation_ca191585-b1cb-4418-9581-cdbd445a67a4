using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.Application.Interface.Extensions;
using Immybot.Shared.Primitives;
using LinqKit;
using Microsoft;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Npgsql;

namespace Immybot.Backend.Application.DbContextExtensions;

public class ExpandedComputer
{
  public bool? IsPortable { get; set; }
  public bool? IsServer { get; set; }
  public bool? IsDesktop { get; set; }
  public bool? IsDomainController { get; set; }
  public bool? IsPrimaryDomainController { get; set; }
  public required Computer Computer { get; set; }
  public bool IsMissingSomeRequiredInventoryResults { get; set; }
  public int? PrimaryPersonId { get; set; }
  public string? PrimaryPersonName { get; set; }
  public string? PrimaryPersonOnPremisesSecurityIdentifier { get; set; }
}

public class ComputerSearch
{
  public int Id { get; set; }
  public required string Name { get; set; }
  public required string Tenant { get; set; }
  public int TenantId { get; set; }
  public bool Online { get; set; }
  public DateTime UpdatedDate { get; set; }
  public bool ExcludeFromMaintenance { get; set; }
}
public static class ComputerExtensions
{
  private const string FEATURE_USAGE_EXCEEDED_PG_EXCEPTION_PREFIX = "FeatureUsageExceeded:";
  private static Computer? GetById(this IQueryable<Computer> q, int computerId)
    => q.FirstOrDefault(c => c.Id == computerId);

  public static IQueryable<ComputerSearch> AsComputerSearch(this IQueryable<Computer> @this)
  {
    return @this.AsExpandable().Select(x => new ComputerSearch
    {
      Id = x.Id,
      Name = x.ComputerName ?? string.Empty,
      Tenant = x.Tenant!.Name,
      TenantId = x.TenantId,
      Online = x.Agents.Any(a => a.SupportsRunningScripts && a.IsOnline),
      UpdatedDate = x.UpdatedDate,
      ExcludeFromMaintenance = x.ExcludeFromMaintenance
    });
  }

  public static IQueryable<ExpandedComputer> AsExpandedComputers(this IQueryable<Computer> @this)
  {
    var count = InventoryKeys.SystemRequiredInventoryKeys.Length;
    return @this.AsExpandable()
      .Select(x => new ExpandedComputer()
      {
        Computer = x,
        IsServer = IsServer.Invoke(x),
        IsPortable = IsPortable.Invoke(x),
        IsDesktop = IsDesktop.Invoke(x),
        IsDomainController = IsDomainController.Invoke(x),
        IsPrimaryDomainController = IsPrimaryDomainController.Invoke(x),
        IsMissingSomeRequiredInventoryResults = x.LatestInventoryScriptResults
          .Select(r => r.InventoryKey)
          .Count(k => InventoryKeys.SystemRequiredInventoryKeys.Contains(k)) != count,
        PrimaryPersonId = x.PrimaryPersonId,
        PrimaryPersonName = x.PrimaryPerson!.DisplayName,
        PrimaryPersonOnPremisesSecurityIdentifier = x.PrimaryPerson.OnPremisesSecurityIdentifier
      });
  }

  private const string _latestFeatureUsagesCte = @"latest_feature_usages AS (
  SELECT feature_id, MAX(feature_track_start_date_utc) AS feature_track_start_date_utc
  FROM feature_usages
  WHERE enabled = true
  GROUP BY feature_id
)";

  private const string _licensedComputersCte = @"licensed_computers as (
	SELECT fui.value, true AS licensed
    FROM latest_feature_usages fu
    INNER JOIN feature_usage_items fui
      ON fu.feature_id = fui.feature_id
      AND fu.feature_track_start_date_utc = fui.feature_track_start_date_utc
    WHERE fu.feature_id = 'devices-with-scheduled-maintenance'
    and fui.value::int in (select id from matching_computers)
    GROUP BY fui.value)";

  private const string _activeSessionsCte = @"active_session AS (
  SELECT ms.computer_id, ms.session_status, ms.id AS maintenance_session_id
  FROM maintenance_sessions ms
  INNER JOIN (
    SELECT computer_id, max(maintenance_session_id) AS id
    FROM maintenance_sessions ms2
    INNER JOIN active_sessions as2 ON as2.maintenance_session_id = ms2.id and as2.session_status = 1
    GROUP BY ms2.computer_id
  ) t ON ms.id = t.id
)";
  private const string BaseComputerListViewModelQuery = @$"SELECT c.created_date,
  c.id,
  c.is_sandbox,
  c.device_id,
  c.deleted_at,
  c.onboarding_status,
  c.computer_name,
  c.chassis_types,
  c.operating_system,
  c.manufacturer,
  c.model,
  c.serial_number,
  c.domain_role,
  c.has_pending_reboot,
  c.domain,
  c.last_boot_time_utc,
  c.internal_ip_address,
  c.external_ip_address,
  c.last_logged_on_user,
  c.exclude_from_maintenance,
  c.dev_lab_vm_claim_expiration_date_utc,
  c.dev_lab_vm_unclaimed,
  c.dev_lab_vm_name,
  cn.content as notes,
  te.last_provider_agent_event_date_utc,
  p.first_name AS primary_user_first_name,
  p.last_name AS primary_user_last_name,
  p.email_address AS primary_user_email,
  p.id AS primary_person_id,
  t.name AS tenant_name,
  t.id AS tenant_id,
  (SELECT array_agg(ct.tag_id) AS array_agg
    FROM computer_tags ct
    WHERE ct.entity_id = c.id) AS computer_tag_ids,
  (SELECT array_agg(rc2.provider_link_id) AS array_agg
    FROM provider_agents rc2
    WHERE rc2.computer_id = c.id and rc2.deleted_at is null) AS provider_link_ids,
	as1.session_status AS active_session_status,
  as1.maintenance_session_id AS active_session_id,
  r.is_online AS is_online,
  licensed_computers.licensed
FROM matching_computers mc
inner join computers c on c.id = mc.id
left outer join active_session as1 on as1.computer_id=c.id
LEFT OUTER JOIN computer_notes cn ON cn.computer_id = c.id
INNER JOIN tenants t ON t.id = c.tenant_id
LEFT OUTER JOIN LATERAL (
	SELECT is_online
    FROM provider_agents pa
    INNER JOIN provider_links pl ON pa.provider_link_id = pl.id
    WHERE c.id = pa.computer_id AND pa.supports_running_scripts AND pa.supports_online_status AND pa.is_online AND pl.disabled = false
    LIMIT 1
) r ON TRUE
LEFT OUTER JOIN persons p ON c.primary_person_id = p.id
LEFT OUTER JOIN computer_latest_provider_events te ON te.computer_id = c.id
LEFT OUTER join licensed_computers ON licensed_computers.value::int = c.id";

  #region expressions

  public static readonly Expression<Func<Computer, bool>> IsComputerOnline
    = c => c.Agents.Any(r =>
      r.SupportsRunningScripts && r.IsOnline && !r.ProviderLink!.Disabled &&
      r.ProviderLink.HealthStatus == HealthStatus.Healthy);

  public static readonly Expression<Func<ExpandedComputer, bool>> IsExpandedComputerOnline
    = c => c.Computer.Agents.Any(r =>
      r.SupportsRunningScripts && r.IsOnline && !r.ProviderLink!.Disabled &&
      r.ProviderLink.HealthStatus == HealthStatus.Healthy);

  private static readonly Expression<Func<Computer, bool>> IsServer
    = computer => computer.OperatingSystem!.ToLower().Contains("server");

  private static readonly Expression<Func<Computer, bool>> IsPortable
    = computer => computer.ChassisTypes != null && computer.ChassisTypes
    .Any(i => ComputerInventoryConstants.PortableChassisTypes.Contains(i));

  private static readonly Expression<Func<Computer, bool>> IsDesktop
    = computer => computer.ChassisTypes != null && IsNotPortable.Invoke(computer) && IsNotServer.Invoke(computer);

  private static readonly Expression<Func<Computer, bool>> IsNotServer = x => !IsServer.Invoke(x);

  private static readonly Expression<Func<Computer, bool>> IsNotPortable = x => !IsPortable.Invoke(x);

  private static readonly Expression<Func<Computer, bool>> IsDomainController
    = computer => computer.DomainRole != null &&
                  ComputerInventoryConstants.WindowsDomainControllerRoles.Contains(computer.DomainRole.Value);

  private static readonly Expression<Func<Computer, bool>> IsPrimaryDomainController
    = computer => computer.DomainRole == ComputerInventoryConstants.PrimaryDomainControllerRole;
  #endregion

  public static IQueryable<Computer> IncludeAgents(
    this IQueryable<Computer> q)
    => q.Include(a => a.Agents)
        .ThenInclude(a => a.ProviderLink)
        .ThenInclude(l => l!.ProvidersLinkedFromThisProvider)
      .Include(a => a.Agents)
        .ThenInclude(a => a.ProviderClient);

  public static IQueryable<Computer> WhereIsOnboarded(this IQueryable<Computer> @this)
    => @this.Where(c => c.OnboardingStatus == ComputerOnboardingStatus.Onboarded);
  public static IQueryable<Computer> WhereIsNotOnboarded(this IQueryable<Computer> @this)
    => @this.Where(c => c.OnboardingStatus != ComputerOnboardingStatus.Onboarded);
  public static IQueryable<Computer> WhereIsOnline(this IQueryable<Computer> @this)
    => @this.Where(IsComputerOnline);
  public static IQueryable<Computer> WhereIsLinked(this IQueryable<Computer> @this)
    => @this.Where(c => c.Agents.Any(a => a.ProviderClient!.LinkedToTenantId != null));
  public static IQueryable<Computer> WhereIsServer(
    this IQueryable<Computer> @this)
    => @this.Where(IsServer);
  public static IQueryable<Computer> WhereIsNotServer(
    this IQueryable<Computer> @this)
    => @this.Where(IsNotServer);
  public static IQueryable<Computer> WhereHasChassisTypes(
    this IQueryable<Computer> @this) => @this.Where(a => a.ChassisTypes != null);
  public static IQueryable<Computer> WhereIsPortable(
    this IQueryable<Computer> @this)
    => @this.Where(IsPortable).Where(IsNotServer);
  public static IQueryable<Computer> WhereIsWorkstation(
    this IQueryable<Computer> @this)
    => @this.Where(IsDesktop);
  public static IQueryable<Computer> WhereIsDomainController(
    this IQueryable<Computer> @this,
    int? tenantId = null)
    => @this.Where(IsDomainController).LimitToTenant(tenantId);
  public static IQueryable<Computer> WhereIsPrimaryDomainController(
    this IQueryable<Computer> @this,
    int? tenantId = null)
    => @this.Where(IsPrimaryDomainController).LimitToTenant(tenantId);
  public static IQueryable<Computer> LimitToTenant(
    this IQueryable<Computer> q,
    ImmybotDbContext ctx,
    int? tenantId = null,
    bool includeChildTenants = false)
  {
    if (tenantId is not { } tenantIdVal)
    {
      return q;
    }
    // Note: We can clean this up when/if EF Core supports recursive CTEs
    return includeChildTenants
      ? q.LimitToTenants([tenantIdVal, .. ctx.GetDescendentTenantIds(tenantIdVal)])
      : q.Where(a => a.TenantId == tenantIdVal);
  }

  public static IQueryable<Computer> LimitToTenant(
    this IQueryable<Computer> q,
    int? tenantId = null)
    => tenantId != null ? q.Where(a => a.TenantId == tenantId) : q;
  public static IQueryable<Computer> LimitToTenants(
    this IQueryable<Computer> q,
    List<int> tenantIds)
    => q.Where(a => tenantIds.Contains(a.TenantId));

  public static IQueryable<Computer> ApplyTargetGroupFilter(
    this IQueryable<Computer> @this,
    TargetGroupFilter targetGroupFilter)
    => targetGroupFilter switch
    {
      TargetGroupFilter.All => @this,
      TargetGroupFilter.Servers => @this.WhereIsServer(),
      TargetGroupFilter.Workstations => @this.WhereIsWorkstation(),
      TargetGroupFilter.PortableDevices => @this.WhereIsPortable(),
      TargetGroupFilter.WorkstationsAndPortableDevices => @this.WhereIsNotServer().WhereHasChassisTypes(),
      TargetGroupFilter.DomainControllers => @this.WhereIsDomainController(),
      TargetGroupFilter.PrimaryDomainControllers => @this.WhereIsPrimaryDomainController(),
      _ => @this,
    };

  private static string BuildMatchingComputersCte(
    bool hasSearchFilter = false,
    bool onboardingOnly = false,
    bool staleOnly = false,
    int? staleAgeDays = null,
    bool devLabOnly = false,
    bool includeOffline = true,
    bool licensedOnly = false,
    bool deletedOnly = false,
    int? limit = null,
    int? offset = null,
    int? tenantId = null,
    string? rbacFilter = null)
  {
    string? sql =
      "matching_computers AS (" +
      "\n  SELECT DISTINCT c.id::integer as id" +
      "\nFROM computers c";

    if (!hasSearchFilter && limit.HasValue && offset.HasValue)
    {
      sql +=
        $"\nLIMIT {limit.Value} OFFSET {offset.Value}";
    }

    if (staleOnly)
    {
      sql += "\nLEFT OUTER JOIN computer_latest_provider_events te ON te.computer_id = c.id";
    }

    if (licensedOnly)
    {
      sql += @$"
          LEFT OUTER JOIN(
          SELECT fui.value, true AS licensed
          FROM latest_feature_usages fu
          INNER JOIN feature_usage_items fui
          ON fu.feature_id = fui.feature_id
          AND fu.feature_track_start_date_utc = fui.feature_track_start_date_utc
          WHERE fu.feature_id = '{SubscriptionFeatures.TrackedDevicesFeatureId}'
          GROUP BY fui.value) licensing ON licensing.value::int = c.id";
    }

    // Apply RBAC filtering
    var whereKeyword = "WHERE";
    if (!string.IsNullOrEmpty(rbacFilter))
    {
      sql += $"\n{whereKeyword} ({rbacFilter})";
      whereKeyword = "AND";

      if (tenantId.HasValue)
      {
        // add tenantId filter after rbac filter
        sql += "AND (c.tenant_id = @tenantId)";
      }
    }
    else if (tenantId.HasValue)
    {
      sql += $"\n{whereKeyword} (c.tenant_id = @tenantId)";
      whereKeyword = "AND";
    }

    if (staleOnly)
    {
      sql+=
        // has at least one agent
        $"\n{whereKeyword} c.deleted_at is null and (NOT EXISTS(SELECT 1 FROM provider_agents r WHERE r.supports_running_scripts AND c.id = r.computer_id)" +
        // and none of the agents are online
        "\nOR (NOT EXISTS(SELECT 1 FROM provider_agents r INNER JOIN provider_links pl ON r.provider_link_id = pl.id WHERE c.id = r.computer_id AND r.is_online AND pl.disabled = false)" +
        // and the last time an agent event was received was over the stale-age threshold
        $"\nAND (te.last_provider_agent_event_date_utc IS NULL OR te.last_provider_agent_event_date_utc < now() - INTERVAL '{staleAgeDays ?? 14} days')))";
    }
    else if (deletedOnly)
    {
      sql += $"\n{whereKeyword} c.deleted_at is not null";
    }
    else if (includeOffline)
    {
      // has at least one agent
      sql += $"\n{whereKeyword} c.deleted_at is null";
    }
    else
    {
      // has at least one agent that is online
      sql +=
        $"\n{whereKeyword} c.deleted_at is null and EXISTS(SELECT 1 FROM provider_agents r INNER JOIN provider_links l on l.id = r.provider_link_id WHERE l.disabled = false AND l.health_status != {Convert.ToInt32(HealthStatus.Unhealthy)} AND c.id = r.computer_id AND r.is_online and r.supports_online_status)";
    }

    if (onboardingOnly)
    {
      sql += $"\nAND (c.onboarding_status = {(int)ComputerOnboardingStatus.NeedsOnboarding} OR c.onboarding_status = {(int)ComputerOnboardingStatus.Onboarding})";
    }

    if (devLabOnly)
    {
      sql += $"\nAND (c.dev_lab_vm_name is not NULL)";
    }

    if (licensedOnly)
    {
      sql += $"\nAND (licensing.licensed is true)";
    }

    if (hasSearchFilter)
    {
      sql += @"
  AND c.id IN (
  SELECT c.id FROM computers c
  WHERE ((COALESCE(c.computer_name, '') || ' ' ||
    COALESCE(c.manufacturer, '') || ' ' ||
    COALESCE(c.serial_number, '') || ' ' ||
    COALESCE(c.model, '') || ' ' ||
    COALESCE(c.domain, '') || ' ' ||
    COALESCE(c.last_boot_time_utc, '') || ' ' ||
    COALESCE(c.internal_ip_address, '') || ' ' ||
    COALESCE(c.external_ip_address, '') || ' ' ||
    COALESCE(c.last_logged_on_user, '') || ' ' ||
    COALESCE(c.operating_system, '') || ' ' ||
    c.device_id) ILIKE @filter)
  UNION SELECT DISTINCT c.id FROM persons p
  INNER JOIN computers c ON c.primary_person_id = p.id
  WHERE (COALESCE(p.first_name, '') || ' ' || COALESCE(p.last_name, '')) ILIKE @filter
  UNION SELECT DISTINCT c.id FROM tenants t
  INNER JOIN computers c ON t.id = c.tenant_id
  WHERE t.name ILIKE @filter
  UNION SELECT DISTINCT c.id FROM computers c
  WHERE EXISTS (
  SELECT 1
  FROM computer_tags ct
  JOIN tags tg ON ct.tag_id = tg.id
  WHERE ct.entity_id = c.id
  AND tg.name ILIKE @filter
))";
    }

    sql += ")\n";
    return sql;
  }

  private static string? BuildComputerListViewModelOrderBySql(string sort, bool sortDesc = true)
  {
    var dir = sortDesc ? "desc" : "asc";

    return sort switch
    {
      "createdDate" => $"\nORDER BY c.created_date {dir}",
      "computerName" => $"\nORDER BY c.computer_name {dir}",
      "operatingSystem" => $"\nORDER BY c.operating_system {dir}",
      "manufacturer" => $"\nORDER BY c.manufacturer {dir}",
      "model" => $"\nORDER BY c.model {dir}",
      "serialNumber" => $"\nORDER BY c.serial_number {dir}",
      "domain" => $"\nORDER BY c.domain {dir}",
      "primaryUser" => $"\nORDER BY p.last_name, p.first_name, p.email_address {dir}",
      "tenant" => $"\nORDER BY t.name {dir}",
      "isDomainController" => $"\nORDER BY c.domain_role IN ({string.Join(',', ComputerInventoryConstants.WindowsDomainControllerRoles)}) {dir}",
      "isPortable" => $"\nORDER BY c.chassis_types && '{{{string.Join(',', ComputerInventoryConstants.PortableChassisTypes)}}}' {dir}",
      "isDesktop" => "",
      "lastAgentCheckinDate" => $"\nORDER BY te.last_provider_agent_event_date_utc {dir} NULLS {(dir == "desc" ? "LAST" : "FIRST")}",
      _ => null,
    };
  }

  private const string BuildComputerListViewModelTakeSql = "\nLIMIT @take";
  private const string BuildComputerListViewModelOffsetSql = "\nOFFSET @offset";

  /// <summary>
  /// Generates the SQL query and parameters for computer list view models without executing the query.
  /// This allows for testing the SQL generation logic independently of database execution.
  /// </summary>
  public static (string sql, object[] parameters) GenerateComputerListViewModelQuery(
    string? filter = null,
    int? take = null,
    int? skip = null,
    string? sort = null,
    bool sortDesc = true,
    bool onboardingOnly = false,
    bool staleOnly = false,
    int? staleAgeDays = null,
    bool devLabOnly = false,
    bool includeOffline = true,
    bool licensedOnly = false,
    bool deletedOnly = false,
    string? rbacFilter = null,
    int? tenantId = null,
    bool forCount = false)
  {
    var filterVar = filter?.ToLower() is { Length: > 0 } s ? $"%{s}%" : null;

    var parameters = new List<object>();
    if (filterVar != null) parameters.Add(new NpgsqlParameter("@filter", filterVar));
    if (take.HasValue) parameters.Add(new NpgsqlParameter("@take", take));
    if (skip.HasValue) parameters.Add(new NpgsqlParameter("@offset", skip));
    if (tenantId.HasValue) parameters.Add(new NpgsqlParameter("@tenantId", tenantId.Value));


    var matchingComputersCte = BuildMatchingComputersCte(
      hasSearchFilter: filterVar != null,
      onboardingOnly: onboardingOnly,
      staleOnly: staleOnly,
      staleAgeDays: staleAgeDays,
      devLabOnly: devLabOnly,
      includeOffline: includeOffline,
      licensedOnly: licensedOnly,
      deletedOnly: deletedOnly,
      tenantId: tenantId,
      rbacFilter: rbacFilter);

    var sql = "WITH ";

    if (licensedOnly)
    {
      // if licensed only, we need the latest feature usages cte
      sql += _latestFeatureUsagesCte + "," + matchingComputersCte;
    }
    else
    {
      sql += matchingComputersCte;
    }

    // if for count, we only care about the matching computers cte
    if (forCount)
    {
      sql += "SELECT COUNT(1) FROM matching_computers" +
             "\n INNER JOIN computers c ON c.id = matching_computers.id";
    }
    else
    {
      var latestFeatureUsagesCte = !licensedOnly ? _latestFeatureUsagesCte + "," : string.Empty;
      sql += "," + _activeSessionsCte + "," + latestFeatureUsagesCte +
             _licensedComputersCte + BaseComputerListViewModelQuery;
    }

    // build order by sql
    if (!string.IsNullOrEmpty(sort))
      sql += BuildComputerListViewModelOrderBySql(sort, sortDesc);

    // build limit sql
    if (take.HasValue)
      sql += BuildComputerListViewModelTakeSql;

    // build offset sql
    if (skip.HasValue)
      sql += BuildComputerListViewModelOffsetSql;

    return (sql, parameters.ToArray());
  }

  public static async Task<int> GetComputerListViewModelCount(this ImmybotDbContext @this,
    string? filter = null,
    bool onboardingOnly = false,
    bool staleOnly = false,
    int? staleAgeDays = null,
    bool devLabOnly = false,
    bool includeOffline = true,
    bool licensedOnly = false,
    bool deletedOnly = false,
    string? rbacFilter = null,
    int? tenantId = null,
    bool forCount = false)
  {
    var (sql, parameters) = GenerateComputerListViewModelQuery(
      filter,
      onboardingOnly: onboardingOnly,
      staleOnly: staleOnly,
      staleAgeDays: staleAgeDays,
      devLabOnly: devLabOnly,
      includeOffline: includeOffline,
      licensedOnly: licensedOnly,
      deletedOnly: deletedOnly,
      rbacFilter: rbacFilter,
      tenantId: tenantId,
      forCount: forCount);
    var res = await @this.ComputerListViewModelCount.FromSqlRaw(sql, parameters).TagForTelemetry()
      .FirstOrDefaultAsync();
    return res?.Count ?? 0;
  }

  public static IQueryable<ComputerListViewModel> GetComputerListViewModels(
    this ImmybotDbContext @this,
    string? filter = null,
    int? take = null,
    int? skip = null,
    string? sort = null,
    bool sortDesc = true,
    bool onboardingOnly = false,
    bool staleOnly = false,
    int? staleAgeDays = null,
    bool devLabOnly = false,
    bool includeOffline = true,
    bool licensedOnly = false,
    bool deletedOnly = false,
    int? tenantId = null,
    string? rbacFilter = null
  )
  {
    var (sql, parameters) = GenerateComputerListViewModelQuery(
      filter,
      take,
      skip,
      sort,
      sortDesc,
      onboardingOnly,
      staleOnly, staleAgeDays, devLabOnly, includeOffline, licensedOnly,
      deletedOnly,
      rbacFilter,
      tenantId: tenantId);

    return @this.ComputerListViewModels.FromSqlRaw(sql, parameters).TagForTelemetry();
  }

  public static IQueryable<Computer> GetOnlineComputers(
    this ImmybotDbContext ctx,
    bool includeProviderAgents = false,
    bool includeProviderLinks = false)
  {
    var q = ctx.Computers.AsNoTracking().TagForTelemetry().WhereIsOnline();
    if (includeProviderAgents)
    {
      if (includeProviderLinks)
        q = q.Include(r => r.Agents).ThenInclude(c => c.ProviderLink);
      else
        q = q.Include(r => r.Agents);
    }
    return q;
  }

  public static IEnumerable<ComputerInfo> GetComputerInfoForComputerIds(
    this ImmybotDbContext @this,
    ICollection<int> computerIds)
  {
    return GetComputersByIds(@this, computerIds).TagForTelemetry()
      .Select(c => new ComputerInfo
      {
        Id = c.Id,
        Name = c.ComputerName ?? c.DeviceId.ToString(),
        IsOnline = c.Agents.Any(a => a.SupportsRunningScripts && a.IsOnline)
      });
  }

  public static IQueryable<Computer> IncludeTenantAndAzData(this IQueryable<Computer> q)
  {
    return q.Include(t => t.Tenant!.AzureTenantLink!.LimitToDomains)
      .Include(t => t.Tenant!.AzureTenantLink!.AzureTenant);
  }

  public static IQueryable<Computer> GetAllComputers(
    this ImmybotDbContext ctx,
    int? tenantId = null,
    bool includePrimaryPerson = false,
    bool includeTenant = false,
    bool excludeOnboarding = false,
    bool excludeOffline = false,
    bool includeAgents = false,
    bool includeComputersForDisabledProviderLinks = false,
    bool includeChildTenants = false)
  {
    var q = ctx.Computers.AsNoTracking().TagForTelemetry()
      .LimitToTenant(ctx, tenantId, includeChildTenants: includeChildTenants);

    if (includeAgents)
      q = q.IncludeAgents();
    if (includePrimaryPerson)
      q = q.Include(a => a.PrimaryPerson);
    if (includeTenant)
      q = q.IncludeTenantAndAzData();
    if (excludeOnboarding)
      q = q.WhereIsOnboarded();
    if (excludeOffline)
      q = q.WhereIsOnline();
    if (includeComputersForDisabledProviderLinks)
    {
      q = q.IgnoreQueryFiltersExceptSoftDelete();
      // manually re-add the filter to not return orphaned computers
      q = q.Where(a => a.Agents.Any(a => a.SupportsRunningScripts));
    }

    return q;
  }

  public static ExpandedComputer? GetExpandedComputerById(
    this ImmybotDbContext ctx,
    int computerId,
    bool includeSessions = false,
    bool includeAdditionalPersons = false,
    bool includeActions = false,
    bool includeTenant = false,
    bool includePrimaryPerson = false,
    bool includeAgents = false,
    bool excludeOnboarding = false,
    bool excludeOffline = false,
    bool asNoTracking = false,
    bool includeComputersForDisabledProviderLinks = false,
    bool includeLatestInventoryResults = false,
    bool includeTags = false,
    bool ignoreQueryFilters = false,
    bool includeDeleted = false)
  {
    var computers = ctx.Computers.AsQueryable().TagForTelemetry();

    if (ignoreQueryFilters || includeComputersForDisabledProviderLinks)
      computers = includeDeleted ? computers.IgnoreQueryFilters() : computers.IgnoreQueryFiltersExceptSoftDelete();
    if (includeSessions)
      computers = computers.Include(a => a.Sessions).ThenInclude(b => b.Stages);
    if (includeActions)
      computers = computers.Include(a => a.Sessions).ThenInclude(a => a.MaintenanceActions);
    if (includeAdditionalPersons)
      computers = computers.Include(a => a.AdditionalPersons).ThenInclude(b => b.Person);
    if (includeTenant)
      computers = computers.IncludeTenantAndAzData();
    if (includePrimaryPerson)
      computers = computers.Include(a => a.PrimaryPerson);
    if (includeAgents)
      computers = computers.IncludeAgents();
    if (excludeOnboarding)
      computers = computers.WhereIsOnboarded();
    if (excludeOffline)
      computers = computers.WhereIsOnline();
    if (asNoTracking)
      computers = computers.AsNoTracking();
    if (includeLatestInventoryResults)
      computers = computers.Include(c => c.LatestInventoryScriptResults);
    if (includeTags)
      computers = computers.Include(a => a.Tags);

    computers = computers.Include(a => a.ComputerNote);

    var ret = computers
      .AsSplitQuery()
      .AsExpandedComputers()
      .FirstOrDefault(c => c.Computer.Id == computerId);

    if (ret is not null)
      ret.Computer.Agents = ret.Computer.Agents.Where(a => a.DeletedAt == null).ToList();

    return ret;
  }

  public static Task<int> SetExcludedByUserAffinity(this ImmybotDbContext ctx, int computerId, bool value) =>
    ctx.Computers
      .TagForTelemetry()
      .Where(a => a.Id == computerId)
      .ExecuteUpdateAsync(a => a.SetProperty(c => c.ExcludedFromUserAffinity, value));

  public static Task<int> BatchSetExcludedByUserAffinity(this ImmybotDbContext ctx, List<int> computerIds, bool value) =>
    ctx.Computers
      .TagForTelemetry()
      .Where(a => computerIds.Contains(a.Id))
      .ExecuteUpdateAsync(a => a.SetProperty(c => c.ExcludedFromUserAffinity, value));

  public static Computer? GetComputerById(
    this ImmybotDbContext ctx,
    int computerId,
    bool includeSessions = false,
    bool includeAdditionalPersons = false,
    bool includeActions = false,
    bool includeTenant = false,
    bool includePrimaryPerson = false,
    bool includeAgents = false,
    bool excludeOnboarding = false,
    bool excludeOnboarded = false,
    bool excludeOffline = false,
    bool asNoTracking = false,
    bool includeComputersForDisabledProviderLinks = false,
    bool includeTags = false,
    ICollection<string>? withInventoryKeyResults = null,
    bool includeDeleted = false)
  {
    var computers = ctx.Computers.AsQueryable().TagForTelemetry();

    if (includeSessions)
      computers = computers.Include(a => a.Sessions).ThenInclude(b => b.Stages);
    if (includeActions)
      computers = computers.Include(a => a.Sessions).ThenInclude(a => a.MaintenanceActions);
    if (includeAdditionalPersons)
      computers = computers.Include(a => a.AdditionalPersons).ThenInclude(b => b.Person);
    if (includeTenant)
    {
      computers = computers.IncludeTenantAndAzData();
      if (includeTags)
      {
        computers = computers.Include(a => a.Tenant!.TenantTags).ThenInclude(a => a.Tag);
      }
    }

    if (includePrimaryPerson)
    {
      computers = computers.Include(a => a.PrimaryPerson);
      if (includeTags)
      {
        computers = computers.Include(a => a.PrimaryPerson!.PersonTags).ThenInclude(a => a.Tag);
      }
    }
    if (includeAgents)
      computers = computers.IncludeAgents();
    if (excludeOnboarding)
      computers = computers.WhereIsOnboarded();
    if (excludeOnboarded)
      computers = computers.WhereIsNotOnboarded();
    if (excludeOffline)
      computers = computers.WhereIsOnline();
    if (asNoTracking)
      computers = computers.AsNoTracking();
    if (includeComputersForDisabledProviderLinks)
      computers = computers.IgnoreQueryFiltersExceptSoftDelete();
    if (withInventoryKeyResults is { Count: > 0 } keysToInclude)
      computers = computers.Include(c => c.LatestInventoryScriptResults
        .Where(r => keysToInclude.Contains(r.InventoryKey)));
    if (includeTags)
      computers = computers.Include(a => a.ComputerTags).ThenInclude(a => a.Tag);
    if (includeDeleted)
      computers = computers.IgnoreQueryFilters();

    computers = computers.Include(a => a.ComputerNote);

    return computers.GetById(computerId);
  }

  public static Task SetComputerSuccessor(this ImmybotDbContext ctx,
    int oldComputerId,
    int newComputerId)
    => ctx.Computers
      .AsNoTracking()
      .IgnoreQueryFilters()
      .Where(a => a.Id == oldComputerId)
      .ExecuteUpdateAsync(
        a => a.SetProperty(c => c.SuccessorComputerId, newComputerId));

  public static Computer? GetComputerByDeviceId(
    this ImmybotDbContext ctx,
    Guid deviceId,
    bool includeAgents = false,
    bool includeLatestInventoryResults = false)
  {
    var computers = ctx.Computers.AsNoTracking().IgnoreQueryFilters().TagForTelemetry();
    if (includeAgents)
      computers = computers.IncludeAgents();
    if (includeLatestInventoryResults)
      computers = computers.Include(c => c.LatestInventoryScriptResults);
    return computers.FirstOrDefault(c => c.DeviceId == deviceId);
  }

  public static IQueryable<Computer> GetDeletedComputers(this ImmybotDbContext ctx)
  {
    var computers = ctx.Computers.AsNoTracking().IgnoreQueryFilters().TagForTelemetry();
    return computers.Where(c => c.DeletedAt != null);
  }

  public static IQueryable<ComputerInventoryTaskScriptResult> GetLatestInventoryResultsForComputer(
    this ImmybotDbContext ctx,
    int computerId)
  {
    return ctx.ComputerInventoryTaskScriptResults
      .TagForTelemetry()
      .AsNoTracking()
      .Where(r => r.ComputerId == computerId);
  }

  public static Task<ComputerInventoryTaskScriptResult?> GetLatestInventoryResultForComputer(
    this ImmybotDbContext ctx,
    int computerId,
    string inventoryKey)
  {
    return ctx.GetLatestInventoryResultsForComputer(computerId)
      .TagForTelemetry()
      .FirstOrDefaultAsync(r => r.InventoryKey == inventoryKey);
  }

  public static ICollection<ProviderAgent>? GetAgentsForComputer(
    this ImmybotDbContext ctx,
    int computerId)
  {
    return ctx.Computers
      .TagForTelemetry()
      .IncludeAgents()
      .AsNoTracking()
      .GetById(computerId)?.Agents;
  }

  public static int? GetComputerTenantId(
    this ImmybotDbContext ctx,
    int computerId) =>
    ctx.Computers.IgnoreQueryFilters().TagForTelemetry()
      .Where(c => c.Id == computerId)
      .Select(c => c.TenantId)
      .FirstOrDefault() is int t and not 0 ? t : null;

  public static void SetComputerInventoryKeyValue(
    this ImmybotDbContext ctx,
    int computerId,
    string inventoryKey,
    string? rawJsonString,
    bool withHistorical)
  {
    if (rawJsonString == null)
    {
      // unset the key
      ctx.Database.ExecuteSqlInterpolated(
        $"CALL sp_set_computer_inventory_script_result({computerId}, {inventoryKey}, false, {withHistorical});");
    }
    else
    {
      // set the key to the value
      // string non-ascii
      var sanitizedJson = rawJsonString.Replace("\\u0000", string.Empty);
      ctx.Database.ExecuteSqlInterpolated(
        $"CALL sp_set_computer_inventory_script_result({computerId}, {inventoryKey}, false, {withHistorical}, {sanitizedJson});");
    }
  }

  public static void SetComputerInventoryKeyError(
    this ImmybotDbContext ctx,
    int computerId,
    string inventoryKey,
    string errorStringOrRawJsonString,
    bool withHistorical)
  {
    ctx.Database.ExecuteSqlInterpolated(
      $"CALL sp_set_computer_inventory_script_result({computerId}, {inventoryKey}, true, {withHistorical}, {errorStringOrRawJsonString});");
  }

  public static bool IsComputerOwnedByTenant(
    this ImmybotDbContext ctx,
    int computerId,
    int tenantId) =>
    ctx.Computers.TagForTelemetry().Any(c => c.Id == computerId && c.TenantId == tenantId);

  public static IQueryable<MaintenanceAction> GetActionsNeedingAttentionForComputer(
    this ImmybotDbContext ctx,
    int computerId)
  {
    return ctx.MaintenanceActions.FromSqlRaw(@$"select ma2.*
from (
  select max(ma.id) as id
  from maintenance_actions ma
  inner join target_assignments ta on ta.id = ma.assignment_id
  where ma.computer_id = {computerId}
  group by ma.maintenance_identifier, ma.maintenance_type
) as attention
inner join maintenance_actions ma2 on ma2.id = attention.id
where ma2.action_result = 2 and ma2.created_date > now() - '1 week'::interval").TagForTelemetry();
  }
  public static IQueryable<Computer> GetComputersForProviderLink(
    this ImmybotDbContext ctx,
    int providerLinkId) =>
     ctx.Computers.Where(a => a.Agents.Any(b => b.ProviderLinkId == providerLinkId)).AsNoTracking().TagForTelemetry();

  public static Computer? GetComputerForProviderAgent(
    this ImmybotDbContext ctx,
    string agentId,
    string clientId,
    int providerLinkId)
    => ctx.Computers
      .AsNoTracking()
      .TagForTelemetry()
      .Include(a => a.Agents).ThenInclude(a => a.ProviderLink)
      .FirstOrDefault(a => a.Agents.Any(b =>
        b.ExternalAgentId == agentId &&
        b.ExternalClientId == clientId &&
        b.ProviderLinkId == providerLinkId));

  public static IQueryable<Computer> GetComputersByPrimaryPersonIds(
    this ImmybotDbContext ctx,
    int[] personIds,
    bool excludeOnboarding = false,
    bool excludeOffline = false)
  {
    var q = ctx.Computers.AsNoTracking().TagForTelemetry()
      .Where(c => c.PrimaryPersonId.HasValue && personIds.Contains(c.PrimaryPersonId.Value));
    if (excludeOnboarding)
      q = q.WhereIsOnboarded();
    if (excludeOffline)
      q = q.WhereIsOnline();

    return q;
  }

  public static IQueryable<Computer> GetAllComputersForPrimaryPerson(
    this ImmybotDbContext ctx,
    int personId,
    bool excludeOnboarding = false,
    bool excludeOffline = false)
    => ctx.GetComputersByPrimaryPersonIds([personId],
      excludeOnboarding: excludeOnboarding,
      excludeOffline: excludeOffline).TagForTelemetry();

  public static IQueryable<Computer> GetComputersByProviderAgentIds(
    this ImmybotDbContext ctx,
    int providerLinkId,
    ICollection<string> externalAgentIds,
    bool excludeOnboarding = false,
    bool excludeOffline = false,
    int? tenantId = null,
    bool includeChildTenants = false)
  {
    var q = ctx.GetAgentsForProviderLink(providerLinkId)
      .TagForTelemetry()
      .Where(a => externalAgentIds.Contains(a.ExternalAgentId))
      .Include(a => a.Computer)
      .ThenInclude(b => b!.Agents)
      .Select(a => a.Computer!)
      .LimitToTenant(ctx, tenantId, includeChildTenants: includeChildTenants);

    if (excludeOnboarding)
      q = q.WhereIsOnboarded();
    if (excludeOffline)
      q = q.WhereIsOnline();

    return q;
  }

  public static IQueryable<Computer> GetComputersByIds(
    this ImmybotDbContext ctx,
    ICollection<int> computerIds,
    bool includeAgents = false,
    bool includeSoftDeleted = false)
  {
    var q = ctx.Computers.Where(c => computerIds.Contains(c.Id)).AsNoTracking().TagForTelemetry();
    if (includeAgents)
      q = q.IncludeAgents();

    if (includeSoftDeleted)
    {
      q = q.IgnoreQueryFilters();
    }

    return q;
  }

  public static IQueryable<Computer> GetComputersInTag(
    this ImmybotDbContext ctx, int tagId)
  {
    var tag = ctx.Tags.AsNoTracking().FirstOrDefault(a => a.Id == tagId);
    if (tag is null) return ctx.ShortCircuitNoResults<Computer>().TagForTelemetry();

    return ctx.Computers
      .TagForTelemetry()
      .AsNoTracking()
      .Where(a =>
        a.ComputerTags.Any(b => b.TagId == tagId)
        || a.Tenant!.TenantTags.Any(b => b.TagId == tagId)
        || a.PrimaryPerson!.PersonTags.Any(b => b.TagId == tagId));
  }

  public static IQueryable<Computer> GetComputersInTagForTenant(
    this ImmybotDbContext ctx, int tagId, int tenantId, bool includeChildTenants = false)
  {
    return GetComputersInTag(ctx, tagId).LimitToTenant(ctx, tenantId, includeChildTenants: includeChildTenants);
  }

  public static int ComputerOnboardingCount(this ImmybotDbContext ctx) =>
    ctx.Computers.AsNoTracking().TagForTelemetry()
      .Where(a => a.Agents.Any(b => b.IsOnline))
      .Count(a => a.OnboardingStatus != ComputerOnboardingStatus.Onboarded);

  public static int ComputerCountForTenant(this ImmybotDbContext ctx, int tenantId) =>
    ctx.Computers.AsNoTracking().TagForTelemetry()
      .Where(a => a.Agents.Any(b => b.IsOnline))
      .Count(a => a.TenantId == tenantId);

  public static int ComputerOnboardingCountForTenant(this ImmybotDbContext ctx, int tenantId) =>
    ctx.Computers.AsNoTracking().TagForTelemetry()
      .Where(a => a.Agents.Any(b => b.IsOnline))
      .Count(a => a.OnboardingStatus != ComputerOnboardingStatus.Onboarded && a.TenantId == tenantId);

  public static void UpdateComputer(this ImmybotDbContext ctx, Computer computer)
  {
    var existingComputer = ctx.Computers.GetById(computer.Id);
    if (existingComputer is null) return;
    var entry = ctx.Entry(existingComputer);
    entry.CurrentValues.SetValues(computer);
    ctx.SaveChanges();
    entry.State = EntityState.Detached;
  }

  // this method accepts the definitive list of persons that should be assigned to a computer
  public static void UpdateComputerPersonsForComputer(
    this ImmybotDbContext ctx,
    int computerId,
    ICollection<int> personIds)
  {
    // remove existing associations first
    ctx.ComputerPersons.RemoveRange(ctx.ComputerPersons.Where(p => p.ComputerId == computerId));
    // add new associations
    ctx.ComputerPersons.AddRange(personIds
      .Select(a => new ComputerPerson() { ComputerId = computerId, PersonId = a }));
    ctx.SaveChanges();
  }

  public static IQueryable<Computer> GetOnboardingComputersForTenant(
    this ImmybotDbContext ctx,
    int tenantId,
    bool includeAgents = false,
    bool includeTenant = false)
    => ctx.GetOnboardingComputers(
      includeTenant: includeTenant,
      includeAgents: includeAgents)
    .LimitToTenant(tenantId).TagForTelemetry();

  public static IQueryable<Computer> GetOnboardingComputers(
    this ImmybotDbContext ctx,
    bool includeTenant = false,
    bool includeAgents = false,
    bool includeSessions = false)
  {
    var query = ctx.Computers.AsNoTracking().TagForTelemetry().WhereIsNotOnboarded();
    if (includeAgents)
      query = query.Include(a => a.Agents);
    if (includeTenant)
      query = query.IncludeTenantAndAzData();
    if (includeSessions)
      query = query.Include(a => a.Sessions);

    return query;
  }

  public static Computer? GetDomainControllerForTenant(
    this ImmybotDbContext ctx,
    int tenantId) =>
    GetDomainControllers(ctx, tenantId).FirstOrDefault();

  public static IQueryable<Computer> GetDomainControllers(
    this ImmybotDbContext ctx,
    int? tenantId = null) =>
    ctx.Computers
    .WhereIsDomainController()
    .WhereIsOnline()
    .LimitToTenant(tenantId)
    .IncludeAgents()
    .Include(c => c.PrimaryPerson)
    .AsNoTracking()
    .TagForTelemetry();

  public static void SetComputerOnboardingStatus(
    this ImmybotDbContext ctx,
    int computerId,
    ComputerOnboardingStatus status)
  {
    // Should not change onboarding date unless we're updating to onboarded and the date is not
    // already set. This is to prevent user from setting a previously-onboarded computer to
    // needs-onboarding then back to onboarded in order to reset the onboarded date and get around
    // feature restrictions
    ctx.Computers
      .Where(c => c.Id == computerId)
      .TagForTelemetry()
      .ExecuteUpdate(u => u
        .SetProperty(c => c.OnboardingStatus, c => status)
        .SetProperty(c => c.OnboardedDateUtc,
          c => status == ComputerOnboardingStatus.Onboarded && c.OnboardedDateUtc == null
            ? DateTime.UtcNow
            : c.OnboardedDateUtc));
  }

  public static async Task SetComputerPrimaryPersonId(
    this ImmybotDbContext ctx,
    int computerId,
    int? primaryPersonId,
    AuditUserDetails auditUserDetails,
    bool ignoreExistenceCheck = false,
    int? oldPrimaryPersonId = null)
  {
    // ensure primary person exists before updating
    if (!ignoreExistenceCheck && primaryPersonId.HasValue &&
        !await ctx.Persons.AsNoTracking().TagForTelemetry().AnyAsync(a => a.Id == primaryPersonId))
    {
      return;
    }

    // fetch the old primary person id if it wasn't supplied
    var oldValue = oldPrimaryPersonId ?? await ctx.Computers
      .AsNoTracking()
      .TagForTelemetry()
      .Where(c => c.Id == computerId)
      .Select(a => a.PrimaryPersonId)
      .FirstOrDefaultAsync();

    // short-circuit if the value hasn't changed
    if (oldValue == primaryPersonId)
    {
      return;
    }

    // update primary person
    var rowsUpdated = await ctx.Computers
      .AsNoTracking()
      .TagForTelemetry()
      .Where(c => c.Id == computerId)
      .ExecuteUpdateAsync(u =>
        u.SetProperty(c => c.PrimaryPersonId, primaryPersonId));

    var computerName = await ctx.Computers
      .AsNoTracking()
      .TagForTelemetry()
      .Where(c => c.Id == computerId)
      .Select(c => c.ComputerName)
      .FirstOrDefaultAsync();

    if (rowsUpdated > 0)
    {
      // add audit log if we actually updated the row
      await ctx.CreateAudit(
        new CreateAuditRequest(auditUserDetails,
          AuditType.Update,
          AuditObjectType.Computer,
          new { PrimaryPersonId = oldValue },
          new { PrimaryPersonId = primaryPersonId },
          ["PrimaryPersonId"],
          computerId,
          ObjectName: computerName ?? string.Empty));
    }

    // delete all user affinities after a primary person is chnaged
    await ctx.UserAffinities
      .AsNoTracking()
      .TagForTelemetry()
      .Where(a => a.ComputerId == computerId)
      .ExecuteDeleteAsync();
  }

  public static void AssignAgentToComputer(
    this ImmybotDbContext ctx,
    int agentId,
    int computerId)
  {
    ctx.Database.ExecuteSqlInterpolated(
      $"CALL \"sp_assign_agent_to_computer\"({agentId}, {computerId});");
  }

  public static void AssignAgentToNewComputer(
    this ImmybotDbContext ctx,
    int agentId,
    Guid deviceId,
    bool isTenantOnboardingEnabled,
    string operatingSystemName,
    string deviceName,
    string serialNumber,
    int? domainRole,
    bool isSandbox,
    int? primaryPersonId = null,
    bool isDevLab = false)
  {
    var devLabVmName = isDevLab ? deviceName : null;
    if (isDevLab) isTenantOnboardingEnabled = true;
    try
    {
      ctx.Database.ExecuteSqlInterpolated(
        $"CALL \"sp_create_computer_from_agent\"({agentId}, {deviceId}, {!isTenantOnboardingEnabled}, {operatingSystemName}, {deviceName}, {serialNumber}, {domainRole}, {primaryPersonId}, {isSandbox}, {devLabVmName});");
    }
    catch (PostgresException ex) when (ex.MessageText.StartsWith(FEATURE_USAGE_EXCEEDED_PG_EXCEPTION_PREFIX))
    {
      // messageText looks like 'FeatureUsageExceeded:some-feature-id'
      throw new FeatureUsageExceededException()
      {
        SubscriptionFeatureId = ex.MessageText[FEATURE_USAGE_EXCEEDED_PG_EXCEPTION_PREFIX.Length..]
      }
        .AddData("agentId", agentId)
        .AddData("deviceId", deviceId);
    }
  }

  public static IQueryable<(Guid DeviceId, int ComputerId)> GetOnlineComputerDeviceIdsWithOutdatedInventoryTasks(
    this ImmybotDbContext ctx,
    ICollection<InventoryTask> tasks,
    ICollection<string>? limitToInventoryKeys = null)
  {
    return ctx.GetOnlineComputersWithOutdatedInventoryTasks(tasks, limitToInventoryKeys)
      .Select(i => new Tuple<Guid, int>(i.DeviceId, i.ComputerId).ToValueTuple())
      .Distinct()
      .TagForTelemetry();
  }

  public static IQueryable<ComputerInventoryKeyViewModel> GetOnlineComputersWithOutdatedInventoryTasks(
    this ImmybotDbContext ctx,
    ICollection<InventoryTask> tasks,
    ICollection<string>? limitToInventoryKeys = null)
  {
    var taskItems = tasks.Aggregate(new List<string>(), (agg, t) =>
    {
      var timespan = t.FrequencyTimespan;
      var intervalStr = $"{Convert.ToInt32(timespan.TotalSeconds)} seconds";
      return t.Scripts.Aggregate(agg, (agg2, s) =>
      {
        if (limitToInventoryKeys?.Contains(s.InventoryKey) ?? true)
        {
          agg2.Add($"('{s.InventoryKey}', interval '{intervalStr}')");
        }
        return agg2;
      });
    });
    return ctx.ComputerInventoryKeys.FromSqlRaw(@$"SELECT
  c.id AS computer_id,
  c.device_id,
  script.inventory_key
FROM (
  VALUES
    {string.Join(",\n    ", taskItems)}
  )
  AS script (inventory_key, inventory_interval)
FULL OUTER JOIN computers c ON true
WHERE
  c.deleted_at IS NULL
  AND c.inventory_started_date IS NULL
  AND EXISTS (
    SELECT 1 FROM provider_agents r
    INNER JOIN provider_links rl
      ON r.provider_link_id = rl.id
    INNER JOIN provider_clients rc
      ON r.provider_link_id = rc.provider_link_id AND r.external_client_id = rc.external_client_id
    WHERE
      NOT rl.disabled
      AND rl.health_status = 2
      AND r.computer_id = c.id
      AND r.is_online
      AND r.supports_running_scripts
      AND rc.linked_to_tenant_id IS NOT NULL
  )
  AND NOT EXISTS (
    SELECT 1 FROM computer_inventory_task_script_results i
    WHERE i.computer_id = c.id
      AND i.inventory_key = script.inventory_key
      AND i.timestamp > NOW() AT TIME ZONE 'UTC' - script.inventory_interval
  )").TagForTelemetry();
  }

  public static void SetComputerInventoryStartedForComputers(
    this ImmybotDbContext ctx,
    ICollection<Guid> deviceIds)
  {
    ctx.Computers.TagForTelemetry()
      .Where(c => deviceIds.Contains(c.DeviceId))
      .ExecuteUpdate(u => u.SetProperty(c => c.InventoryStartedDate, DateTime.UtcNow));
  }

  public static void SetComputerInventoryEndedForComputers(
    this ImmybotDbContext ctx,
    ICollection<Guid> deviceIds)
  {
    ctx.Computers.TagForTelemetry()
      .Where(c => deviceIds.Contains(c.DeviceId))
      .ExecuteUpdate(u => u.SetProperty(c => c.InventoryStartedDate, (DateTime?)null));
  }

  public static IQueryable<Computer> GetComputersByPrimaryPersonId(
    this ImmybotDbContext ctx,
    int personId)
    => ctx.Computers.TagForTelemetry().AsNoTracking()
    .Include(a => a.Agents)
    .ThenInclude(a => a.ProviderLink)
    .Where(a => a.PrimaryPersonId == personId);

  public static void SetDetectionOutdated(
    this ImmybotDbContext _ctx,
    IEnumerable<int> computerIds)
    => _ctx.Computers.TagForTelemetry()
      .Where(a => computerIds.Contains(a.Id))
      .ExecuteUpdate(u => u.SetProperty(a => a.DetectionOutdated, true));

  public static IQueryable<Computer> GetComputersExcludedFromMaintenance(
    this ImmybotDbContext ctx,
    int tenantId)
    => ctx.Computers.AsNoTracking().TagForTelemetry()
      .IgnoreQueryFilters()
      .Where(a => a.TenantId == tenantId
                  && a.ExcludeFromMaintenance
                  && a.DeletedAt == null);

  public static IQueryable<int> GetComputerIdsWithAzureDeviceIds(
    this ImmybotDbContext ctx, List<string> azureDeviceIds)
  {
    if (!azureDeviceIds.Any())
      return ctx.ShortCircuitNoResults<ComputerInventoryTaskScriptResult>().TagForTelemetry().Where(a => false).Select(a => a.ComputerId);
    return ctx.Computers
      .FromSqlRaw($@"
select computer_id as Id
from computer_inventory_task_script_results citsr
inner join computers c on c.id = citsr.computer_id
where c.deleted_at is null and inventory_key = 'WindowsSystemInfo' and
latest_success_result -> 'Output' -> 'DSRegStatus' -> 'DeviceDetails' ->> 'DeviceId' in ({string.Join(",", azureDeviceIds.Select(a => $"'{a}'"))})")
      .IgnoreQueryFilters()
      .TagForTelemetry()
      .Select(a => a.Id);

  }

  public static IQueryable<Computer> GetClaimedExpiredDevLabComputers(
    this ImmybotDbContext ctx,
    DateTime expiredAfterDate)
    => ctx.Computers
      .TagForTelemetry()
      .AsNoTracking()
      .IgnoreQueryFilters()
      .Where(a => a.DevLabVmName != null && !a.DevLabVmUnclaimed &&
                  a.DevLabVmClaimExpirationDateUtc < expiredAfterDate);

  public static IQueryable<Computer> GetClaimedDevLabComputersWithNoExpiration(
    this ImmybotDbContext ctx)
    => ctx.Computers
      .TagForTelemetry()
      .AsNoTracking()
      .IgnoreQueryFilters()
      .Where(a => a.DevLabVmName != null && !a.DevLabVmUnclaimed && a.DevLabVmClaimExpirationDateUtc == null);

  /// <summary>
  /// This is a hilarious fix for (in my opinion) a bug where calls to `.ToListAsync`, `.CountAsync`, `.FirstOrDefaultAsync`, etc.
  /// chained from an `IQueryable` will fail at runtime if the `IQueryable` did not come directly from EF Core. e.g. `new List().AsQueryable()`.
  /// The runtime error is: "The source 'IQueryable' doesn't implement 'IAsyncEnumerable'. Only sources that implement 'IAsyncEnumerable' can be used for Entity Framework asynchronous operations."
  /// The least intrusive way to fix this is to add a method to the DbContext class that returns an empty `IQueryable` of the correct type.
  /// </summary>
  /// <typeparam name="T">The entity type</typeparam>
  /// <param name="ctx">The DbContext instance</param>
  /// <returns>An empty EF Core IQueryable of type T</returns>
  public static IQueryable<T> ShortCircuitNoResults<T>(this DbContext ctx) where T : class
    => ctx.Set<T>().Where(x => false);
}
public class ComputerInfo
{
  public int Id { get; set; }
  public string? Name { get; set; }
  public bool IsOnline { get; set; }
}
