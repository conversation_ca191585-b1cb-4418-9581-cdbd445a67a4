using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Application.DbContextExtensions.PersonExtensions;

public static class PersonExtensions
{
  public static Person? GetPersonById(this ImmybotDbContext context, int id)
    => context.Persons
      .AsNoTracking()
      .Include(a => a.Tags)
      .FirstOrDefault(a => a.Id == id);

  public static Person CreatePerson(this ImmybotDbContext context, CreatePersonPayload payload)
  {
    var person = new Person { EmailAddress = payload.EmailAddress };
    context.Entry(person).CurrentValues.SetValues(payload);
    context.Persons.Add(person);
    context.SaveChanges();
    return person;
  }

  public static Person CreatePerson(this ImmybotDbContext context, CreatePersonWithRequestedAccessPayload payload)
  {
    var person = new Person { EmailAddress = payload.EmailAddress };
    context.Entry(person).CurrentValues.SetValues(payload);
    context.Persons.Add(person);
    context.SaveChanges();
    return person;
  }

  public static async Task CreatePerson(this ImmybotDbContext context, IList<Person> persons)
  {
    await using var transaction = await context.Database.BeginTransactionAsync();
    try
    {
      await context.BulkInsertAsync(persons);
      await transaction.CommitAsync();
    }
    catch
    {
      await transaction.RollbackAsync();
      throw;
    }
  }

  public static async Task DeleteComputerPersonsByPersonIds(this ImmybotDbContext ctx, IList<int> personIds, CancellationToken token)
  {
    await ctx.ComputerPersons
     .AsNoTracking()
     .Where(a => personIds.Contains(a.PersonId))
     .DeleteFromQueryAsync(token);
  }

  public static async Task DeletePersons(this ImmybotDbContext ctx, IList<int> personIds, CancellationToken cancellationToken)
  {
    await ctx.Persons
      .AsNoTracking()
      .Where(a => personIds.Contains(a.Id))
      .DeleteFromQueryAsync(cancellationToken);
  }

  public static async Task UpdatePerson(this ImmybotDbContext context, IList<Person> persons)
  {
    await using var transaction = await context.Database.BeginTransactionAsync();
    try
    {
      await context.BulkUpdateAsync(persons);
      await transaction.CommitAsync();
    }
    catch
    {
      await transaction.RollbackAsync();
      throw;
    }
  }

  public static Person? UpdatePerson(this ImmybotDbContext context, UpdatePersonPayload payload)
  {
    var existingPerson = context.Persons.Find(payload.Id);
    if (existingPerson == null) return null;

    context.Entry(existingPerson).CurrentValues.SetValues(payload);
    context.SaveChanges();

    return existingPerson;
  }

  public static void DeletePerson(this ImmybotDbContext context, Person person)
  {
    context.Persons.Remove(person);
    context.SaveChanges();
  }

  public static IQueryable<Person> GetAllPersons(this ImmybotDbContext context, bool includeTenant = false, bool includeUserData = false)
  {
    var persons = context.Persons.AsNoTracking().AsQueryable();

    if (includeTenant)
      persons = persons.Include(a => a.Tenant);

    if (includeUserData)
    {
      persons = persons
        .Include(a => a.User)
        .ThenInclude(a => a!.UserRoles)
        .ThenInclude(a => a.Role);
    }

    return persons;
  }

  public static IQueryable<Person> GetPersonsByIds(this ImmybotDbContext ctx, IList<int> personIds)
    => ctx.Persons.Where(c => personIds.Contains(c.Id)).AsNoTracking();

  public static IQueryable<Person> GetPersonsByTag(this ImmybotDbContext context, int tagId)
    => context.Persons
    .AsNoTracking()
    .Include(a => a.Tags)
    .Where(a =>
      a.Tags.Any(b => b.Id == tagId)
      || a.Tenant!.TenantTags.Any(b => b.TagId == tagId));

  public static IQueryable<Person> GetPersonsInTagForTenant(
    this ImmybotDbContext ctx, int tagId, int tenantId, bool includeChildTenants = false)
  {
    return GetPersonsByTag(ctx, tagId).LimitToTenant(ctx, tenantId, includeChildTenants: includeChildTenants);
  }

  public static IQueryable<Person> LimitToTenant(
    this IQueryable<Person> q,
    ImmybotDbContext ctx,
    int tenantId,
    bool includeChildTenants = false)
  {
    return includeChildTenants
      ? q.LimitToTenants([tenantId, .. ctx.GetDescendentTenantIds(tenantId)])
      : q.Where(a => a.TenantId == tenantId);
  }

  public static IQueryable<Person> LimitToTenants(
    this IQueryable<Person> q,
    List<int> tenantIds)
    => q.Where(a => tenantIds.Contains(a.TenantId));

  public static Person? GetPersonByPrincipalId(this ImmybotDbContext context, string principalId)
    => context.Persons.FirstOrDefault(a => a.AzurePrincipalId == principalId);

  public static Person? GetPerson(this ImmybotDbContext context, int personId, bool includeTags = false, bool includeUserData = false)
  {
    var q = context.Persons.AsQueryable();
    if (includeTags)
      q = q.Include(a => a.Tags);

    if (includeUserData)
    {
      // using null forgiving operator as EF Core should handle the nullability of User
      q = q
        .Include(a => a.User)
        .ThenInclude(a => a!.UserRoles)
        .ThenInclude(a => a.Role);
    }

    return q.FirstOrDefault(a => a.Id == personId);
  }
  public static Person? GetPersonAtTenant(this ImmybotDbContext context, int personId, int tenantId, bool includeTags = false, bool includeUserData = false)
  {
    var q = context.Persons.Where(a => a.TenantId == tenantId);
    if (includeTags)
      q = q.Include(a => a.Tags);

    if (includeUserData)
    {
      // using null forgiving operator as EF Core should handle the nullability of User
      q = q
        .Include(a => a.User)
        .ThenInclude(a => a!.UserRoles)
        .ThenInclude(a => a.Role);
    }

    return q.FirstOrDefault(a => a.Id == personId && a.TenantId == tenantId);
  }

  public static IQueryable<Person> GetPersonsAtTenant(this ImmybotDbContext context, int tenantId, bool includeTags = false)
  {
    var persons = context.Persons
      .AsNoTracking()
      .Where(a => a.TenantId == tenantId);
    if (includeTags)
      persons = persons.Include(a => a.Tags);
    return persons;
  }

  public static IQueryable<Person> GetPersonsAtTenants(this ImmybotDbContext context, List<int> tenantIds, bool includeTags = false)
  {
    var persons = context.Persons
      .AsNoTracking()
      .Where(a => tenantIds.Contains(a.TenantId));
    if (includeTags)
      persons = persons.Include(a => a.Tags);
    return persons;
  }

  public static bool IsPersonAtTenant(this ImmybotDbContext context, int personId, int tenantId)
  {
    return context.Persons.Any(p => p.Id == personId && p.TenantId == tenantId);
  }

  public static bool HasPersonWithPrincipalIdRequestedAccess(this ImmybotDbContext context, string principalId)
    => context.Persons.AsNoTracking()
    .Include(a => a.AccessRequests)
    .Any(a => a.AzurePrincipalId == principalId && a.AccessRequests.Any(b => b.DateAcknowledgedUTC == null));
}
