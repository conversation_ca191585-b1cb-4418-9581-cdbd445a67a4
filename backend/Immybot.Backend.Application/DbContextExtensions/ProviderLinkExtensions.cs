using System;
using System.Collections.Generic;
using System.Linq;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using Z.EntityFramework.Plus;

namespace Immybot.Backend.Application.DbContextExtensions;

public static class ProviderLinkExtensions
{
  public static void PrioritizeProviderLinkRunScriptPriorities(
    this ImmybotDbContext ctx,
    List<IPrioritizeProviderLinkPayload> payload)
  {
    var links = ctx.ProviderLinks.IgnoreQueryFilters().ToList();
    foreach (var link in links)
    {
      var priorityPayload = payload.Find(p => p.Id == link.Id);
      if (priorityPayload == null) continue;
      link.RunScriptPriority = priorityPayload.Priority;
    }

    ctx.SaveChanges();
  }

  public static void DeleteProviderLink(
    this ImmybotDbContext ctx,
    ProviderLink link)
  {
    ctx.ProviderLinks.AsNoTracking().IgnoreQueryFilters().Where(l => l.Id == link.Id).Delete();
  }

  public static ProviderLinkInternalData? GetProviderLinkInternalData(this ImmybotDbContext ctx, int providerLinkId)
   => ctx.ProviderLinkInternalData
    .IgnoreQueryFilters()
    .AsNoTracking()
    .FirstOrDefault(a => a.ProviderLinkId == providerLinkId);

  public static ProviderLink? GetProviderLink(
    this ImmybotDbContext ctx,
    int id,
    bool includeOwnerTenant = false,
    bool includeClients = false,
    bool includeClientTenants = false,
    bool includeLinkedProviders = false,
    bool includeLinkedFromProviders = false)
  {
    var links = ctx.ProviderLinks.IgnoreQueryFilters().AsNoTracking();
    if (includeOwnerTenant)
      links = links.Include(a => a.OwnerTenant);
    if (includeClients)
      links = links.Include(a => a.ProviderClients);
    if (includeClientTenants)
      links = links.Include(a => a.ProviderClients).ThenInclude(a => a.LinkedToTenant);
    if (includeLinkedProviders)
      links = links.Include(a => a.ProvidersLinkedFromThisProvider).ThenInclude(s => s.ProviderLink2);
    if (includeLinkedFromProviders)
      links = links.Include(a => a.LinkedFromProviders).ThenInclude(s => s.ProviderLink1);

    links = links.Include(a => a.UpdatedByUser).ThenInclude(a => a!.Person);

    return links.FirstOrDefault(r => r.Id == id);
  }

  public static IQueryable<ProviderLink> GetProviderLinks(
    this ImmybotDbContext ctx,
    bool includeClients = false,
    bool includeClientTenants = false,
    bool includeDisabledLinks = false,
    Guid? providerTypeId = null)
  {
    var links = ctx.ProviderLinks.AsNoTracking();

    if (includeClients)
      links = links.Include(a => a.ProviderClients);
    if (includeClientTenants)
      links = links.Include(a => a.ProviderClients)
        .ThenInclude(a => a.LinkedToTenant);
    if (includeDisabledLinks)
      links = links.IgnoreQueryFilters();
    if (providerTypeId != null)
      links = links.Where(a => a.ProviderTypeId == providerTypeId);
    return links;
  }

  public static ProviderLink CreateProviderLink(
    this ImmybotDbContext ctx,
    ProviderLink providerLink)
  {
    providerLink.ProviderInternalData = new ProviderLinkInternalData();
    ctx.ProviderLinks.Add(providerLink);
    ctx.SaveChanges();
    return providerLink;
  }

  public static ProviderLink? UpdateProviderLink(
    this ImmybotDbContext ctx,
    IUpdateProviderLinkPayload providerLink)
  {
    var existing = ctx.ProviderLinks.IgnoreQueryFilters()
      .FirstOrDefault(a => a.Id == providerLink.Id);
    if (existing == null) return null;
    ctx.Entry(existing).CurrentValues.SetValues(providerLink);
    ctx.SaveChanges();
    return existing;
  }

  public static ProviderLinkCrossReference? DisableProviderLinkExternalReferenceClientLinking(
    this ImmybotDbContext ctx,
    ProviderLinkCrossReference externalRef)
  {
    var currentReference = ctx.ProviderLinks
      .Where(l => l.Id == externalRef.ProviderLink1Id)
      .Where(l => l.ProvidersLinkedFromThisProvider.Any(r => r.ProviderLink2Id == externalRef.ProviderLink2Id))
      .Select(l => l.ProvidersLinkedFromThisProvider.FirstOrDefault(r => r.ProviderLink2Id == externalRef.ProviderLink2Id))
      .Single();

    if (currentReference is null) return null;
    currentReference.IsExternalClientLinkingEnabled = false;
    ctx.SaveChanges();
    return currentReference;
  }

  public static ProviderLinkCrossReference? EnableProviderLinkExternalReferenceClientLinking(
    this ImmybotDbContext ctx,
    ProviderLinkCrossReference externalRef)
  {
    var currentReference = ctx.ProviderLinks
      .Where(l => l.Id == externalRef.ProviderLink1Id)
      .Where(l => l.ProvidersLinkedFromThisProvider.Any(r => r.ProviderLink2Id == externalRef.ProviderLink2Id))
      .Select(l => l.ProvidersLinkedFromThisProvider.FirstOrDefault(r => r.ProviderLink2Id == externalRef.ProviderLink2Id))
      .Single();
    if (currentReference is null) return null;
    currentReference.IsExternalClientLinkingEnabled = true;
    ctx.SaveChanges();
    return currentReference;
  }
  public static void DeleteProviderLinkExternalReference(
    this ImmybotDbContext ctx,
    ProviderLinkCrossReference externalRef)
  {
    _ = ctx.ProviderLinks
      .AsNoTracking()
      .IgnoreQueryFilters()
      .Where(l => l.Id == externalRef.ProviderLink1Id)
      .Where(l => l.ProvidersLinkedFromThisProvider.Any(r => r.ProviderLink2Id == externalRef.ProviderLink2Id))
      .Select(l => l.ProvidersLinkedFromThisProvider.FirstOrDefault(r => r.ProviderLink2Id == externalRef.ProviderLink2Id))
      .Delete();
  }
  public static ProviderLinkCrossReference CreateProviderLinkExternalReference(
    this ImmybotDbContext ctx,
    int providerLinkId1,
    int providerLinkId2,
    bool isExternalClientLinkingEnabled = false,
    bool isProviderLink2InitializedFromProviderLink1 = false)
  {
    var providerLink = ctx.ProviderLinks
      .IgnoreQueryFilters()
      .Include(r => r.ProvidersLinkedFromThisProvider)
      .First(l => l.Id == providerLinkId1);
    var externalRef = new ProviderLinkCrossReference
    {
      ProviderLink2Id = providerLinkId2,
      IsExternalClientLinkingEnabled = isExternalClientLinkingEnabled,
      IsProviderLink2InitializedFromProviderLink1 = isProviderLink2InitializedFromProviderLink1,
    };
    providerLink.ProvidersLinkedFromThisProvider.Add(externalRef);
    ctx.SaveChanges();
    return externalRef;
  }
}
