using Immybot.Backend.Application.Interface.Models;
using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Models;
using Immybot.Domain.Infrastructure;

namespace Immybot.Backend.Application.DbContextExtensions.MaintenanceSessionExtensions;

public static class MaintenanceSessionExtensions
{
  public static IQueryable<MaintenanceSession> IncludeComputerTenantAndAzData(this IQueryable<MaintenanceSession> q)
  {
    return q.Include(t => t.Computer!.Tenant!.AzureTenantLink!.LimitToDomains)
      .Include(t => t.Computer!.Tenant!.AzureTenantLink!.AzureTenant);
  }
  public static IQueryable<MaintenanceSession> IncludeTenantAndAzData(this IQueryable<MaintenanceSession> q)
  {
    return q.Include(t => t.Tenant!.AzureTenantLink!.LimitToDomains)
      .Include(t => t.Tenant!.AzureTenantLink!.AzureTenant);
  }
  public static IQueryable<MaintenanceSession> GetAllMaintenanceSessions(
    this ImmybotDbContext ctx,
    int? tenantId = null,
    bool includeComputers = false,
    bool includeStages = false,
    bool ignoreQueryFilters = true)
  {
    var q = ctx.MaintenanceSessions.AsNoTracking();

    if (ignoreQueryFilters)
    {
      q = q.IgnoreQueryFilters();
    }

    // only include computers that are not deleted
    q = q.Where(a => a.Computer!.DeletedAt == null);

    if (includeComputers)
      q = q.IncludeComputerTenantAndAzData();
    if (includeStages)
      q = q.Include(a => a.Stages);
    if (tenantId != null)
      q = q.Where(a => a.TenantId == tenantId);
    return q;
  }

  public static void DeleteMaintenanceActions(
    this ImmybotDbContext ctx,
    int sessionId)
  {
    ctx.RemoveRange(ctx.MaintenanceActions.Where(a => a.MaintenanceSessionId == sessionId));
    ctx.SaveChanges();
  }

  public static IQueryable<MaintenanceSession> GetAllMaintenanceSessionsForComputer(
    this ImmybotDbContext ctx,
    Computer computer)
  {
    return ctx.MaintenanceSessions
      .Where(a => a.ComputerId == computer.Id)
      .AsNoTracking();
  }

  public static int? GetMaintenanceSessionTenantId(this ImmybotDbContext ctx, int sessionId)
  {
    return ctx.MaintenanceSessions.AsNoTracking().Where(s => s.Id == sessionId).Select(s => s.TenantId).FirstOrDefault();
  }

  public static IQueryable<MaintenanceSession> GetAllActiveMaintenanceSessions(this ImmybotDbContext ctx)
  {
    return ctx.MaintenanceSessions
      .AsNoTracking()
      .Where(a =>
        a.SessionStatus == SessionStatus.Created ||
        a.SessionStatus == SessionStatus.Running ||
        a.SessionStatus == SessionStatus.Pending ||
        a.SessionStatus == SessionStatus.PendingConnectivity ||
        a.SessionStatus == SessionStatus.PendingPreflight ||
        a.SessionStatus == SessionStatus.PendingEphemeralAgentConnection);
  }

  public static MaintenanceSession? GetMaintenanceSessionById(
    this ImmybotDbContext ctx,
    int sessionId,
    bool includeComputer = false,
    bool includeTenant = false,
    bool includeStages = false,
    bool includeActions = false,
    bool includeLogs = false,
    bool asTracking = false)
  {
    var sessions = ctx.MaintenanceSessions.AsQueryable();
    if (!asTracking)
      sessions = sessions.AsNoTracking();
    if (includeComputer)
    {
      sessions = sessions.Include(s => s.Computer!.PrimaryPerson);
      sessions = sessions.Include(s => s.Computer!.AdditionalPersons).ThenInclude(p => p.Person);
      sessions = sessions.Include(s => s.Computer!.Agents).ThenInclude(a => a.ProviderLink);

      if (includeTenant)
        sessions = sessions.Include(s => s.Tenant);
    }
    if (includeStages)
      sessions = sessions.Include(s => s.Stages).ThenInclude(a => a.Phases);
    if (includeActions)
    {
      sessions = sessions.Include(s => s.MaintenanceActions).ThenInclude(a => a.Phases);
      sessions = sessions.Include(s => s.MaintenanceActions).ThenInclude(a => a.DependsOn).ThenInclude(d => d.DependsOn);
      sessions = sessions.Include(s => s.MaintenanceActions).ThenInclude(a => a.Dependents).ThenInclude(d => d.Dependent);
    }

    if (includeLogs)
      sessions = sessions.Include(s => s.Logs);
    return sessions.FirstOrDefault(b => b.Id == sessionId);
  }

  public static async Task<MaintenanceSession?> GetMaintenanceSessionByIdAsync(
    this ImmybotDbContext ctx,
    int sessionId,
    bool includeComputer = false,
    bool includeTenant = false,
    bool includeStages = false,
    bool includeActions = false,
    bool includeLogs = false,
    bool asTracking = false)
  {
    var sessions = ctx.MaintenanceSessions.AsQueryable();
    if (!asTracking)
      sessions = sessions.AsNoTracking();

    if (includeComputer)
    {
      sessions = sessions.Include(s => s.Computer!.PrimaryPerson);
      sessions = sessions.Include(s => s.Computer!.AdditionalPersons).ThenInclude(p => p.Person);
      sessions = sessions.Include(s => s.Computer!.Agents).ThenInclude(a => a.ProviderLink);

      if (includeTenant)
        sessions = sessions.IncludeComputerTenantAndAzData().IncludeTenantAndAzData();
    }

    var session = await sessions.FirstOrDefaultAsync(b => b.Id == sessionId);
    if (session is null) return null;

    if (includeStages)
    {
      if (asTracking)
      {
        // include stages explicitly
        await ctx.Entry(session)
          .Collection(a => a.Stages)
          .Query()
          .Include(a => a.Phases)
          .LoadAsync();
      }
      else
      {
        session.Stages = await ctx.MaintenanceSessionStages
          .AsNoTracking()
          .Include(a => a.Phases)
          .Where(a => a.MaintenanceSessionId == session.Id)
          .ToListAsync();
      }
    }

    if (includeActions)
    {
      if (asTracking)
      {
        // include actions explicitly
        await ctx.Entry(session)
          .Collection(a => a.MaintenanceActions)
          .Query()
          .Include(a => a.Phases)
          .Include(a => a.DependsOn).ThenInclude(a => a.DependsOn)
          .Include(a => a.Dependents).ThenInclude(a => a.Dependent)
          .AsSplitQuery()
          .LoadAsync();
      }
      else
      {
        session.MaintenanceActions = await ctx.MaintenanceActions
          .AsNoTracking()
          .Include(a => a.Phases)
          .Include(a => a.DependsOn).ThenInclude(a => a.DependsOn)
          .Include(a => a.Dependents).ThenInclude(a => a.Dependent)
          .Where(a => session.Id == a.MaintenanceSessionId)
          .AsSplitQuery()
          .ToListAsync();
      }
    }

    if (includeLogs)
    {
      // no FK relationship anymore to logs
      session.Logs = await ctx.SessionLogs
        .AsNoTracking()
        .Where(a => a.MaintenanceSessionId == session.Id)
        .ToListAsync();
    }

    return session;
  }

  public static IQueryable<MaintenanceSessionStage> GetStagesForSession(
    this ImmybotDbContext ctx,
    int sessionId) => ctx.MaintenanceSessionStages
      .Where(s => s.MaintenanceSessionId == sessionId)
      .AsNoTracking();

  public static IQueryable<SessionPhase> GetPhasesForSession(
    this ImmybotDbContext ctx,
    int sessionId) => ctx.SessionPhases
      .Where(s => s.MaintenanceSessionId == sessionId)
      .AsNoTracking();

  public static MaintenanceSession? GetLastSessionForComputer(
    this ImmybotDbContext ctx,
    int computerId) => ctx.MaintenanceSessions
      .OrderByDescending(a => a.Id)
      // then by created date makes the query use the computerId index instead of a pk backwards scan
      .ThenBy(a => a.CreatedDate)
      .AsNoTracking()
      .FirstOrDefault(a => a.ComputerId == computerId);

  public static ActiveSession? GetActiveSessionForComputer(
    this ImmybotDbContext ctx,
    int computerId) => ctx.ActiveSessions
    .AsNoTracking()
    .Where(a => a.MaintenanceSession != null
                && a.MaintenanceSession.ComputerId == computerId &&
                a.SessionStatus == SessionStatus.Running)
    .Select(a => new ActiveSession { MaintenanceSessionId = a.MaintenanceSessionId, SessionStatus = a.SessionStatus })
    .FirstOrDefault();


  public static ActiveSession? GetActiveCloudSessionForTenant(
    this ImmybotDbContext ctx,
    int tenantId) => ctx.ActiveSessions
    .AsNoTracking()
    .Where(a => a.MaintenanceSession != null
                && a.MaintenanceSession.TenantId == tenantId
                && a.MaintenanceSession.ComputerId == null
                && a.MaintenanceSession.PersonId == null
                && a.SessionStatus == SessionStatus.Running)
    .Select(a => new ActiveSession { MaintenanceSessionId = a.MaintenanceSessionId, SessionStatus = a.SessionStatus })
    .FirstOrDefault();

  public static ActiveSession? GetActivePersonSessionForPerson(
    this ImmybotDbContext ctx,
    int personId) => ctx.ActiveSessions
    .AsNoTracking()
    .Where(a => a.MaintenanceSession != null
                && a.MaintenanceSession.PersonId == personId
                && a.MaintenanceSession.ComputerId == null
                && a.SessionStatus == SessionStatus.Running)
    .Select(a => new ActiveSession { MaintenanceSessionId = a.MaintenanceSessionId, SessionStatus = a.SessionStatus })
    .FirstOrDefault();

  public static MaintenanceSession? GetLastSessionForTenant(
    this ImmybotDbContext ctx,
    int tenantId) => ctx.MaintenanceSessions
      .OrderByDescending(a => a.Id)
      .AsNoTracking()
      .FirstOrDefault(a => a.TenantId == tenantId);

  public static MaintenanceSession CreateMaintenanceSession(
    this ImmybotDbContext ctx,
    MaintenanceSession session)
  {
    if (session.JobArgs.MaintenanceItem == null &&
      session.JobArgs.MaintenanceAgentUpdatesConfiguration == null &&
      session.Stages.Any(a => a.Type == SessionStageType.Detection) &&
      session.Stages.Any(a => a.Type == SessionStageType.Execution))
      session.FullMaintenance = true;

    if (session.JobArgs.UseWinningDeployment)
    {
      // remove unnecessary data
      if (session.JobArgs.MaintenanceItem is SoftwareMaintenanceItem softwareItem)
      {
        softwareItem.TaskConfigurationDetails = null;
        softwareItem.Details = null;
      } else if (session.JobArgs.MaintenanceItem is TaskMaintenanceItem taskItem)
      {
        taskItem.Details = null;
      }
    }
    ctx.MaintenanceSessions.Add(session);
    ctx.SaveChanges();
    return session;
  }

  public static void UpdateMaintenanceSession(
    this ImmybotDbContext ctx,
    MaintenanceSession session)
  {
    var existing = ctx.MaintenanceSessions.Find(session.Id);
    if (existing == null)
      throw new EntityNotFoundException($"Unable to update session (Id={session.Id}) because it does not exist");
    ctx.Entry(existing).CurrentValues.SetValues(session);
    ctx.SaveChanges();
  }

  public static void UpdateMaintenanceSessionStage(
    this ImmybotDbContext ctx,
    MaintenanceSessionStage stage)
  {
    var existing = ctx.MaintenanceSessionStages.Find(stage.Id);
    if (existing == null)
      throw new EntityNotFoundException($"Unable to update stage (Id={stage.Id}) because it does not exist");
    ctx.Entry(existing).CurrentValues.SetValues(stage);
    ctx.SaveChanges();
  }

  public static SessionPhase CreateSessionPhase(
    this ImmybotDbContext ctx,
    SessionPhase phase)
  {
    ctx.SessionPhases.SingleInsert(phase);
    return phase;
  }

  public static void UpdateSessionPhase(
    this ImmybotDbContext ctx,
    SessionPhase phase)
  {
    var existing = ctx.SessionPhases.Find(phase.Id);
    if (existing == null)
      throw new EntityNotFoundException($"Unable to find phase (Id={phase.Id}) because it does not exist");
    ctx.Entry(existing).CurrentValues.SetValues(phase);
    ctx.SaveChanges();
  }

  public static MaintenanceSession? GetMaintenanceSessionByJobId(
    this ImmybotDbContext ctx,
    string jobId) => ctx.MaintenanceSessions.AsNoTracking().FirstOrDefault(a => a.JobId == jobId);

  public static Dictionary<string, int> GetAllSessionStatusCounts(
    this ImmybotDbContext ctx)
  {
    var sessions = ctx.MaintenanceSessions.AsNoTracking();
    return ctx.GetSessionStatusCountsForSessions(sessions);
  }

  public static Dictionary<string, int> GetAllSessionStatusCountsForTenant(
    this ImmybotDbContext ctx,
    int tenantId)
  {
    var sessions = ctx.GetAllMaintenanceSessions(tenantId);
    return ctx.GetSessionStatusCountsForSessions(sessions);
  }

  private static Dictionary<string, int> GetSessionStatusCountsForSessions(
    this ImmybotDbContext ctx,
    IQueryable<MaintenanceSession> sessions)
  {
    var statusCounts = sessions
      .GroupBy(s => s.SessionStatus)
      .Select(g => new { key = g.Key, count = g.Count() })
      .ToDictionary(g => g.key.ToString(), g => g.count);

    statusCounts.Add("All", sessions.Count());

    // represent all session count
    return statusCounts;
  }

  public static IQueryable<MaintenanceSession> GetMaintenanceSessionsByIds(
    this ImmybotDbContext ctx,
    ICollection<int> sessionIds,
    bool includeComputer = false,
    bool includeTenant = false,
    bool includeStages = false,
    bool includeActions = false,
    bool includeLogs = false)
  {
    var q = ctx.MaintenanceSessions
      .AsNoTracking()
      .AsSplitQuery()
      .Where(s => sessionIds.Contains(s.Id));

    if (includeComputer)
    {
      if (includeTenant)
      {
        q = q.IncludeComputerTenantAndAzData();
      }
      else
      {
        q = q.Include(s => s.Computer);
      }
    }

    if (includeStages)
    {
      q = q.Include(s => s.Stages);
    }

    if (includeActions)
    {
      q = q.Include(s => s.MaintenanceActions);
    }

    if (includeLogs)
    {
      q = q.Include(s => s.Logs);
    }

    return q;
  }

  public static IQueryable<SessionDetails> GetSessionDetailsByIds(
    this ImmybotDbContext ctx,
    ICollection<int> sessionIds)
  {
    var q = ctx.MaintenanceSessions
      .AsNoTracking()
      .Where(s => sessionIds.Contains(s.Id))
      .Select(s => new SessionDetails
      {
        SessionId = s.Id,
        TenantId = s.TenantId,
        SessionStatus = s.SessionStatus,
      });
    return q;
  }
}
