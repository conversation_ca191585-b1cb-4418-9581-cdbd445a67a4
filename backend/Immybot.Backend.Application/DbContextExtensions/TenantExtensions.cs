using Immybot.Backend.Application.DbContextExtensions.TagExtensions;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Application.Lib.Exceptions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Shared.Primitives;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.Application.DbContextExtensions;

public static class TenantExtensions
{
  // Recursively get all child tenants of a given tenant
  public static List<int> GetDescendentTenantIds(
    this ImmybotDbContext ctx,
    int tenantId,
    HashSet<int>? visitedTenants = null)
  {
    visitedTenants ??= [tenantId];
    var descendentTenants = new List<int>();
    var childTenantIds = ctx.Tenants.Where(t => t.ParentTenantId == tenantId).Select(t => t.Id).ToList();
    foreach (var childTenantId in childTenantIds.Where(t => visitedTenants.Add(t)))
    {
      descendentTenants.Add(childTenantId);
      descendentTenants.AddRange(ctx.GetDescendentTenantIds(childTenantId, visitedTenants));
    }
    return descendentTenants;
  }

  /// <summary>
  /// Ensures that the parent tenant we are setting for a list of tenants is not an ancestor of them.
  /// </summary>
  /// <param name="ctx"></param>
  /// <param name="tenantIds"></param>
  /// <param name="parentTenantId"></param>
  /// <param name="token"></param>
  /// <returns></returns>
  public static async Task<OpResult> CheckForCyclicalAncestor(
    this ImmybotDbContext ctx,
    IList<int> tenantIds,
    int parentTenantId,
    CancellationToken token)
  {
    // for performance, just create a map of all tenant ids to their parent tenant ids
    var tenantParentMap = await ctx.Tenants
      .AsNoTracking()
      .Select(a => new { a.Id, a.ParentTenantId })
      .ToDictionaryAsync(a => a.Id, a => a.ParentTenantId, token);

    // Update the tenant parent map with the new parent tenant id for all the tenants we are updating.
    // Doing this before checking for cyclical ancestors allows us to fix existing cyclical ancestors if present.
    foreach (var tenantId in tenantIds)
    {
      tenantParentMap[tenantId] = parentTenantId;
    }

    // check every tenant we are updating to ensure that the parent tenant id is not an ancestor of them
    foreach (var tenantId in tenantIds)
    {
      HashSet<int> visited = [tenantId];
      for (int? currentParentId = parentTenantId;
           currentParentId.HasValue;
           tenantParentMap.TryGetValue(currentParentId.Value, out currentParentId))
      {
        if (visited.Add(currentParentId.Value))
          continue;

        // fetch the names of these tenants for a more informative error message
        var tenantIdsToFetch = visited.Union(new HashSet<int> { tenantId, parentTenantId }).ToHashSet().ToList();
        var tenantNameDict = await ctx.GetTenantsByIds(tenantIdsToFetch)
          .Select(a => new { a.Id, a.Name })
          .ToDictionaryAsync(a => a.Id, a => a.Name, token);

        var currentTenantName = tenantNameDict.TryGetValue(tenantId, out var name)
          ? name
          : $"Tenant #{tenantId.ToString()}";

        var ancestors = visited
          .Select(id =>
            tenantNameDict.TryGetValue(id, out var ancestorName) ? ancestorName : $"Tenant #{id.ToString()}")
          .ToHashSet();

        var parentTenantName = tenantNameDict.TryGetValue(parentTenantId, out var parentName)
          ? parentName
          : $"Tenant #{parentTenantId.ToString()}";

        ancestors.Add(parentTenantName);

        return OpResult.Fail(
          $"Cyclical parent detected: Setting {parentTenantName} as the parent of {currentTenantName} would create a cyclical reference where the tenant becomes its own ancestor. \nAncestors leading back to {currentTenantName}: " +
          string.Join(", ", ancestors));
      }
    }

    return OpResult.Ok();
  }

  public static List<int> GetAncestorTenantIds(
    this ImmybotDbContext ctx,
    int tenantId)
  {
    var ancestorTenants = new HashSet<int>();
    var currentTenant = ctx.Tenants
      .Select(t => new { t.Id, t.ParentTenantId })
      .FirstOrDefault(t => t.Id == tenantId);
    while (currentTenant?.ParentTenantId is { } parentId)
    {
      // tenant cannot be an ancestor of itself
      if (parentId == tenantId) break;

      // handle cyclical parent-child relationships
      if (!ancestorTenants.Add(parentId)) break;

      currentTenant = ctx.Tenants
        .Select(t => new { t.Id, t.ParentTenantId })
        .FirstOrDefault(t => t.Id == parentId);
    }

    return ancestorTenants.ToList();
  }

  public static bool IsTenantDescendentOf(this ImmybotDbContext ctx, int descendentTenantId, int ancestorTenantId)
  {
    // TODO: This could be more efficient, e.g. via caching a tree representation of the tenant hierarchy
    return ctx.GetDescendentTenantIds(ancestorTenantId).Contains(descendentTenantId);
  }
  public static IQueryable<Tenant> GetAllTenants(
    this ImmybotDbContext ctx,
    bool includeTags = false,
    bool includeAzData = false)
  {
    var q = ctx.Tenants.AsNoTracking();

    if (includeTags)
    {
      q = q.Include(a => a.Tags);
    }

    if (includeAzData)
    {
      q = q.IncludeAzData();
    }

    return q;
  }

  public static IQueryable<Tenant> GetAllTenantsLinkedToAzure(
    this ImmybotDbContext ctx,
    string? principalId = null)
  {
    return principalId != null
      ? ctx.Tenants.AsNoTracking().Where(t => t.AzureTenantLink != null && t.AzureTenantLink.AzTenantId == principalId)
      : ctx.Tenants.AsNoTracking().Where(a => a.AzureTenantLink != null);
  }

  public static Tenant? GetTenantById(
    this ImmybotDbContext ctx,
    int id,
    bool includeTags = false,
    bool includeAzData = false)
  {
    var q = ctx.Tenants.AsNoTracking();

    if (includeTags)
    {
      q = q.Include(a => a.Tags);
    }

    if (includeAzData) q = q.IncludeAzData();

    return q.FirstOrDefault(a => a.Id == id);
  }

  public static IQueryable<AzureTenant> GetPartnerAzureTenants(this ImmybotDbContext ctx)
    => ctx.AzureTenants
      .Where(t => t.AzureTenantType == AzTenantType.Partner)
      .AsNoTracking();

  public static IQueryable<AzureTenant> GetAzureTenantsByPrincipalIds(
    this ImmybotDbContext ctx,
    List<string> principalIds)
    => ctx.AzureTenants
      .Where(t => principalIds.Contains(t.PrincipalId))
      .AsNoTracking();

  /// <summary>
  /// Gets the MSP Tenant that owns the Tenant with the given id.
  /// This is not to be used to find a parent tenant, but to find the MSP that owns the tenant.
  /// </summary>
  /// <param name="ctx"></param>
  /// <param name="tenantId"></param>
  /// <param name="mspOnly"></param>
  /// <returns></returns>
  /// <exception cref="InvalidOperationException"></exception>
  /// <exception cref="EntityNotFoundException"></exception>
  public static Tenant GetOwnerTenant(
    this ImmybotDbContext ctx,
    int tenantId,
    bool mspOnly)
  {
    var seenTenantIds = new HashSet<int>();
    while (true)
    {
      if (!seenTenantIds.Add(tenantId))
        throw new InvalidOperationException("Cannot get owner tenant due to cyclical tenant owners");
      var tenant = ctx.GetTenantById(tenantId, includeAzData: true) ?? throw new EntityNotFoundException(
        $"Cannot get owner of Tenant with id={tenantId} because that tenant does not exist in the database");

      if (tenant.IsMsp) return tenant;
      if (!mspOnly && tenant.AzureTenantLink?.AzureTenant?.AzureTenantType == AzTenantType.Partner)
        return tenant;
      if (tenant.OwnerTenantId is not { } o)
        throw new EntityNotFoundException(
          $"Cannot get owner of Tenant with id={tenantId} because that tenant does not have an owner that is an msp");
      tenantId = o;
    }
  }

  /// <summary>
  /// Gets the parent tenant of the tenant with the given id.
  /// By default, it returns only the immediate parent.
  /// Set getRootParent to true to retrieve the root parent in the hierarchy.
  /// </summary>
  /// <param name="ctx"></param>
  /// <param name="tenant"></param>
  /// <param name="getRootParent">Whether to retrieve the root parent or just the immediate parent.</param>
  /// <returns>The parent tenant, or null if no parent exists.</returns>
  public static Tenant? GetParentTenant(
    this ImmybotDbContext ctx,
    Tenant tenant,
    bool getRootParent = false)
  {
    return GetParentTenant(ctx, tenant, [], getRootParent);
  }

  private static Tenant? GetParentTenant(
    ImmybotDbContext ctx,
    Tenant tenant,
    HashSet<int> visitedTenants,
    bool getRootParent)
  {
    if (tenant.ParentTenantId is null || visitedTenants.Contains(tenant.ParentTenantId.Value))
    {
      return null;
    }

    visitedTenants.Add(tenant.ParentTenantId.Value);
    var parentTenant = ctx.GetTenantById(tenant.ParentTenantId.Value, includeAzData: true);

    if (!getRootParent || parentTenant == null || parentTenant.ParentTenantId is null)
    {
      return parentTenant;
    }

    // continue recursion to find the root parent if getRootParent is true
    return GetParentTenant(ctx, parentTenant, visitedTenants, getRootParent);
  }

  public static IQueryable<Tenant> GetTenantsLinkedToAzureTenant(
    this ImmybotDbContext ctx,
    AzureTenant tenant)
  {
    return ctx.Tenants.Where(t => t.AzureTenantLink!.AzTenantId == tenant.PrincipalId);
  }

  public static IQueryable<Tenant> IncludeAzData(this IQueryable<Tenant> q)
  {
    return q.Include(t => t.AzureTenantLink)
      .ThenInclude(l => l!.LimitToDomains)
      .Include(t => t.AzureTenantLink)
      .ThenInclude(l => l!.AzureTenant);
  }

  public static async Task LoadAzData(this ImmybotDbContext ctx, Tenant tenant, CancellationToken token)
  {
    await ctx.Entry(tenant).Reference(t => t.AzureTenantLink).LoadAsync(token);
    if (tenant.AzureTenantLink != null)
    {
      await ctx.Entry(tenant.AzureTenantLink).Reference(l => l.AzureTenant).LoadAsync(token);
      await ctx.Entry(tenant.AzureTenantLink).Collection(l => l.LimitToDomains).LoadAsync(token);
    }
  }

  public static IQueryable<Tenant> GetTenantsByIds(
    this ImmybotDbContext ctx,
    IList<int> ids,
    bool includeAzData = false)
  {
    var q = ctx.Tenants.Where(t => ids.Contains(t.Id)).AsNoTracking();

    if (includeAzData)
    {
      q = q.IncludeAzData();
    }

    return q;
  }

  public static async Task<Tenant?> GetTenantByName(
    this ImmybotDbContext ctx,
    string name,
    CancellationToken cancellationToken,
    bool includeTags = false,
    bool includeAzData = false)
  {
    var q = (includeTags
        ? ctx.Tenants.Include(t => t.Tags)
        : ctx.Tenants.AsQueryable());

    if (includeAzData) q = q.IncludeAzData();

    return await q.FirstOrDefaultAsync(t => t.Name == name, cancellationToken);
  }

  public static string? GetTenantName(
    this ImmybotDbContext ctx,
    int id)
    => ctx.Tenants.Where(t => t.Id == id).Select(a => a.Name).FirstOrDefault();

  public static Tenant GetRootTenant(this ImmybotDbContext ctx, bool includeAzData = false)
  {
    var q = ctx.Tenants.AsNoTracking();
    if (includeAzData) q = q.IncludeAzData();
    return q.OrderBy(t => t.Id).FirstOrDefault(t => t.IsMsp)
           ?? throw new EntityNotFoundException("Root tenant not found.");
  }

  public static async Task<bool> TenantNameAlreadyExists(
    this ImmybotDbContext ctx,
    string name,
    CancellationToken cancellationToken,
    int? exclusionTenantId = null)
  {
    var q = ctx.Tenants.AsQueryable();
    if (exclusionTenantId.HasValue)
      q = q.Where(a => a.Id != exclusionTenantId);

    var lowerName = name.ToLower();
#pragma warning disable CA1862 // Use the 'StringComparison' method overloads to perform case-insensitive string comparisons
    return await q.AnyAsync(a => a.Name.ToLower() == lowerName, cancellationToken);
#pragma warning restore CA1862 // Use the 'StringComparison' method overloads to perform case-insensitive string comparisons
  }

  /// <summary>
  /// Updates the provided Azure Tenant
  /// </summary>
  /// <remarks>
  /// <paramref name="azTenant"/> should be attached to the context before calling this method
  /// </remarks>
  /// <param name="ctx"></param>
  /// <param name="azTenant"></param>
  /// <param name="partnerPrincipalId"></param>
  /// <param name="cancellationToken"></param>
  /// <returns></returns>
  public static async Task<AzTenantType> UpdateAzureTenantPartnerPrincipalId(
    this ImmybotDbContext ctx,
    AzureTenant azTenant,
    string? partnerPrincipalId,
    CancellationToken cancellationToken)
  {
    var azTenantType = partnerPrincipalId == null ? AzTenantType.Standalone : AzTenantType.Customer;
    azTenant.PartnerPrincipalId = partnerPrincipalId;
    azTenant.AzureTenantType = azTenantType;

    await ctx.SaveChangesAsync(cancellationToken);

    return azTenantType;
  }

  /// <summary>
  /// Updates the Azure Tenant to be a Partner Tenant
  /// </summary>
  /// <remarks>
  /// <paramref name="azTenant"/> should be attached to the context before calling this method
  /// </remarks>
  /// <param name="ctx"></param>
  /// <param name="azTenant"></param>
  /// <param name="cancellationToken"></param>
  public static async Task SetAzureTenantIsPartner(
    this ImmybotDbContext ctx,
    AzureTenant azTenant,
    CancellationToken cancellationToken)
  {
    azTenant.AzureTenantType = AzTenantType.Partner;
    azTenant.PartnerPrincipalId = null;
    await ctx.SaveChangesAsync(cancellationToken);
  }

  /// <summary>
  /// Updates the provided Azure Tenant
  /// </summary>
  /// <remarks>
  /// <paramref name="azTenant"/> should be attached to the context before calling this method
  /// </remarks>
  /// <exception cref="EntityNotFoundException"></exception>
  public static async Task UpdateAzureTenantSyncedInfo(
    this ImmybotDbContext ctx,
    AzureTenant azTenant,
    AzureTenantInfo? azInfo,
    Guid? azErrorLogItemId,
    CancellationToken cancellationToken)
  {
    if (azErrorLogItemId != null)
    {
      azTenant.LastGetTenantInfoSyncResult = new AzureSyncResult
      {
        AttemptDateUtc = DateTime.UtcNow, AttemptFailedErrorId = azErrorLogItemId,
      };
    }
    else
    {
      azTenant.InfoSyncedFromAzure = azInfo;
      azTenant.LastGetTenantInfoSyncResult =
        new AzureSyncResult() { AttemptDateUtc = DateTime.UtcNow, };
    }

    var numUpdated = await ctx.SaveChangesAsync(cancellationToken);
    if (numUpdated == 0)
      throw new EntityNotFoundException(
        $"Cannot update Azure Tenant with id={azTenant.PrincipalId} because it does not exist in the database");
  }

  /// <summary>
  /// Updates the provided Azure Tenant
  /// </summary>
  /// <remarks>
  /// <paramref name="azTenant"/> should be attached to the context before calling this method
  /// </remarks>
  /// <exception cref="EntityNotFoundException"></exception>
  public static async Task UpdateAzureTenantLastGetUsersAttemptDetails(
    this ImmybotDbContext ctx,
    AzureTenant azTenant,
    Guid? azErrorLogItemId,
    DateTime attemptDateUtc,
    CancellationToken cancellationToken,
    AppRegistrationType? consentedWith = null)
  {
    azTenant.LastGetUsersSyncResult = new AzureSyncResult
    {
      AttemptDateUtc = attemptDateUtc, AttemptFailedErrorId = azErrorLogItemId,
    };
    if (consentedWith != null)
    {
      azTenant.ConsentDetails.ConsentedWith = consentedWith;
    }

    var numUpdated = await ctx.SaveChangesAsync(cancellationToken);
    if (numUpdated == 0)
      throw new EntityNotFoundException(
        $"Cannot update Azure-linked Tenant with az-tenant-id={azTenant.PrincipalId} because it does not exist in the database");
  }
  /// <summary>
  /// Updates the provided Azure Tenant
  /// </summary>
  /// <remarks>
  /// <paramref name="azTenant"/> should be attached to the context before calling this method
  /// </remarks>
  /// <exception cref="EntityNotFoundException"></exception>
  public static async Task UpdateTenantConsentDate(
    this ImmybotDbContext ctx,
    AzureTenant azTenant,
    DateTime consentDateUtc,
    AppRegistrationType consentedWith,
    CancellationToken cancellationToken)
  {
    azTenant.ConsentDetails =
      new AzureTenantConsentDetails { ConsentedWith = consentedWith, ConsentDateUtc = consentDateUtc };
    var numUpdated = await ctx.SaveChangesAsync(cancellationToken);
    if (numUpdated == 0)
      throw new EntityNotFoundException(
        $"Cannot update Azure-linked Tenant with az-tenant-id={azTenant.PrincipalId} because it does not exist in the database");
  }

  private static readonly SemaphoreSlim _tenantMspChangeLock = new(1, 1);

  public static async Task<Tenant> UpdateTenant(
    this ImmybotDbContext ctx,
    UpdateTenantPayload payload,
    int executingUserTenantId,
    CancellationToken token)
  {
    var existing = await ctx.Tenants
                     .FindAsync(keyValues: new object[] { payload.Id }, cancellationToken: token)
                   ?? throw new EntityNotFoundException(
                     $"Cannot update payload with id={payload.Id} because it does not exist in the database");

    if (await TenantNameAlreadyExists(ctx, payload.Name, token, exclusionTenantId: existing.Id))
    {
      throw new ValidationException(
        "A tenant with this name already exists. Choose a different name.");
    }

    bool didLock = false;
    if (payload.IsMsp is false && existing.IsMsp)
    {
      if (executingUserTenantId == payload.Id)
      {
        throw new ValidationException(
          "Cannot change MSP status of this tenant because you are currently logged in to it. " +
          "You must first log in to a different tenant, then you can change this tenant to non-MSP.");
      }

      // if changing msp status, we need to lock before we verify there's another msp tenant
      await _tenantMspChangeLock.WaitAsync(token);
      didLock = true;
    }


    try
    {
      if (didLock)
      {
        // verify there's still another msp tenant to prevent msp lockout
        var otherMspTenantsExist = await ctx.Tenants.AnyAsync(t => t.IsMsp && t.Id != existing.Id,
          cancellationToken: token);
        if (!otherMspTenantsExist)
        {
          throw new ValidationException(
            "Cannot change MSP status of this tenant because there are no other MSP tenants. " +
            "You must first create another MSP tenant, then you can change this tenant to non-MSP.");
        }
      }

      // if payload.IsMsp is null, we don't want to change the value
      ctx.Entry(existing).CurrentValues.SetValues(payload with { IsMsp = payload.IsMsp ?? existing.IsMsp });

      await ctx.SaveChangesAsync(token);
      return existing;
    }
    finally
    {
      if (didLock)
      {
        _tenantMspChangeLock.Release();
      }
    }
  }

  public static Tenant DeactivateTenant(this ImmybotDbContext ctx, int tenantId)
  {
    var existing = ctx.Tenants.Find(tenantId)
                   ?? throw new EntityNotFoundException(
                     $"Cannot deactivate Tenant with id={tenantId} because it does not exist in the database");
    if (!existing.Active)
      throw new ValidationException("Tenant is already not active");
    existing.Active = false;
    ctx.Tenants.Update(existing);
    ctx.SaveChanges();
    ctx.Entry(existing).State = EntityState.Detached;
    return existing;
  }

  public static Tenant ActivateTenant(this ImmybotDbContext ctx, int tenantId)
  {
    var existing = ctx.Tenants.Find(tenantId)
                   ?? throw new EntityNotFoundException(
                     $"Cannot activate Tenant with id={tenantId} because it does not exist in the database");
    if (existing.Active)
      throw new ValidationException("Tenant is already active");
    existing.Active = true;
    ctx.Tenants.Update(existing);
    ctx.SaveChanges();
    ctx.Entry(existing).State = EntityState.Detached;
    return existing;
  }

  public static async Task SetParentTenant(
    this ImmybotDbContext ctx,
    IList<int> tenantIds,
    int? parentTenantId,
    CancellationToken token)
  {
    await ctx.Tenants.Where(t => tenantIds.Contains(t.Id))
      .ExecuteUpdateAsync(
        u => u.SetProperty(t => t.ParentTenantId, parentTenantId),
        token);
  }

  public static IQueryable<ProviderLink> GetProviderLinksForTenant(
    this ImmybotDbContext ctx,
    int tenantId)
  {
    return ctx.GetProviderClients()
      .Where(a => a.LinkedToTenantId == tenantId)
      .Select(a => a.ProviderLink!)
      .Distinct();
  }

  public static IQueryable<Tenant> GetTenantsInTag(this ImmybotDbContext ctx, int tagId)
  {
    return ctx.Tenants.AsNoTracking().Where(a => a.TenantTags.Any(b => b.TagId == tagId));
  }

  public static async Task AddTenantTags(
    this ImmybotDbContext ctx,
    ICollection<int> tagIds,
    ICollection<int> tenantIds)
  {
    await ctx.AddEntityTag<TenantTag>(tagIds, tenantIds);
  }

  public static Task<List<TenantDeletion>> GetIncompleteTenantDeletions(this ImmybotDbContext ctx,
    CancellationToken token) =>
    ctx.TenantDeletions
      .AsNoTracking()
      .TagForTelemetry()
      .Where(a =>
        a.Status == TenantDeletion.DeletionStatus.Pending || a.Status == TenantDeletion.DeletionStatus.InProgress)
      .ToListAsync(token);

  public static Task SetTenantDeletionStatus(this ImmybotDbContext ctx,
    int tenantId,
    TenantDeletion.DeletionStatus status,
    CancellationToken token) =>
    ctx.TenantDeletions
      .Where(a => a.TenantId == tenantId)
      .ExecuteUpdateAsync(a =>
          a.SetProperty(b => b.Status, status),
        token);

  public static Task SetTenantDeletionError(this ImmybotDbContext ctx,
    int tenantId,
    string message,
    CancellationToken token) =>
    ctx.TenantDeletions
      .Where(a => a.TenantId == tenantId)
      .ExecuteUpdateAsync(a => a
          .SetProperty(b => b.CompletionMessage, message)
          .SetProperty(b => b.Status, TenantDeletion.DeletionStatus.Errored),
        token);
}
