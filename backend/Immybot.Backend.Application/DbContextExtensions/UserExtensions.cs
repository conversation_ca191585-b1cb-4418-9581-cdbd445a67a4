using Immybot.Backend.Persistence;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Linq.Expressions;
using Immybot.Backend.Application.Interface.Commands.Payloads;
using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.Application.DbContextExtensions.UserExtensions;

public static class UserExtensions
{
  public static IQueryable<User> GetAllUsers(
    this ImmybotDbContext ctx,
    bool populatePersons = false)
  {
    var users = ctx.Users.AsNoTracking();
    if (populatePersons)
      users = users.Include(u => u.Person);
    return users;
  }

  public static IQueryable<User> GetAllUsersAtTenant(
    this ImmybotDbContext ctx,
    int tenantId,
    bool populatePersons = false)
  {
    var users = ctx.Users.AsNoTracking();
    if (populatePersons)
      users = users.Include(u => u.Person);
    return users.Where(u => u.TenantId == tenantId);
  }

  public static User? GetUserById(this ImmybotDbContext ctx,
    int id,
    bool populatePerson = false,
    bool populateTenant = false)
  {
    var users = ctx.Users.AsNoTracking();
    if (populatePerson)
      users = users.Include(u => u.Person);
    if (populateTenant)
      users = users.Include(u => u.Tenant);
    return users.FirstOrDefault(u => u.Id == id);
  }

  public static async Task<AuthUserDto?> GetAuthUserByPrincipalId(this ImmybotDbContext ctx,
    string id,
    string upn,
    DateTime now)
  {
    var users = await ctx.Users
      .AsSplitQuery()
      .Where(a => a.ServicePrincipalId == id
                  || (a.Person != null && a.Person.AzurePrincipalId == id)
      ).Select(AuthUserDto.Projection)
      .ToListAsync();

    if (users.Count is 0) return null;

    AuthUserDto user;
    if (users.Count is 1) user = users[0];
    else
    {
      // if multiple users are found, find the oldest user that matches the UPN
      var oldestUser = users
        .Where(a => a.Email == upn)
        .OrderBy(a => a.Id)
        .FirstOrDefault();

      // if no user was found with the UPN, just return the oldest user
      user = oldestUser ?? users.OrderBy(a => a.Id).First();
    }

    // check for impersonating users
    var impersonatingUser = await ctx.UserImpersonations
      .AsSplitQuery()
      .OrderByDescending(a => a.ExpiresAtUtc)
      .Where(a => a.ImpersonatorUserId == user.Id && a.ExpiresAtUtc > now)
      .Select(a => a.ImpersonatingUser!)
      .Select(AuthUserDto.Projection)
      .FirstOrDefaultAsync();

    user.ImpersonatingUser = impersonatingUser;

    return user;
  }

  public static async Task<AuthUserDto?> GetAuthUserById(
    this ImmybotDbContext ctx,
    int id,
    DateTime now)
  {
    var user = await ctx.Users
      .AsSplitQuery()
      .Where(a => a.Id == id)
      .Select(AuthUserDto.Projection)
      .FirstOrDefaultAsync();


    if (user is null) return null;

    // check for impersonating users
    var impersonatingUser = await ctx.UserImpersonations
      .AsSplitQuery()
      .OrderByDescending(a => a.ExpiresAtUtc)
      .Where(a => a.ImpersonatorUserId == user.Id && a.ExpiresAtUtc > now)
      .Select(a => a.ImpersonatingUser!)
      .Select(AuthUserDto.Projection)
      .FirstOrDefaultAsync();

    user.ImpersonatingUser = impersonatingUser;

    return user;
  }

  public static async Task<User?> GetUserByPrincipalId(
    this ImmybotDbContext ctx,
    string id,
    bool populatePerson = false,
    bool populateTenant = false,
    bool populateRoles = false,
    bool includeUserImpersonations = false)
  {
    var users = ctx.Users.AsNoTracking();
    if (populatePerson)
    {
      if (populateTenant)
        users = users.Include(u => u.Tenant).Include(u => u.Person).ThenInclude(p => p!.Tenant);
      else
        users = users.Include(u => u.Person);
    }
    else if (populateTenant)
      users = users.Include(u => u.Tenant);

    // This will be set to false if the RBAC feature flag is disabled
    if (populateRoles)
      users = users.Include(u => u.UserRoles)
                  .ThenInclude(ur => ur.Role!) // Role existence enforced in the model configurationed
                  .ThenInclude(r => r.RoleClaims);

    if (includeUserImpersonations)
    {
      users = users.Include(u => u.UserImpersonations).ThenInclude(a => a.ImpersonatingUser)
        .ThenInclude(a => a!.Tenant);
      users = users.Include(u => u.UserImpersonations).ThenInclude(a => a.ImpersonatingUser)
        .ThenInclude(a => a!.Person).ThenInclude(a => a!.Tenant);

      // If RBAC is enabled (populateRoles is true), also include the roles of the impersonated user
      if (populateRoles)
      {
        users = users.Include(u => u.UserImpersonations).ThenInclude(a => a.ImpersonatingUser)
          .ThenInclude(a => a!.UserRoles).ThenInclude(ur => ur.Role!)
          .ThenInclude(r => r.RoleClaims);
      }
    }

    return await users
      .AsSplitQuery()
      .FirstOrDefaultAsync(ByPrincipalId(id));
  }

  public static User CreateUser(
    this ImmybotDbContext ctx,
    User user)
  {
    // Populate person for auditing
    if (user.PersonId is { } personId) user.Person = ctx.Persons.Find(personId);
    ctx.Users.Add(user);
    ctx.SaveChanges();
    return ctx.GetUserById(user.Id, true)!;
  }

  public static User? UpdateUser(
    this ImmybotDbContext ctx,
    UpdateUserPayload user)
  {
    // Populate person for auditing
    var existing = ctx.Users.Include(u => u.Person).FirstOrDefault(u => u.Id == user.Id);
    if (existing == null) return null;
    ctx.Entry(existing).CurrentValues.SetValues(user);
    ctx.SaveChanges();
    return existing;
  }

  public static void DeleteUser(this ImmybotDbContext ctx,
    User user)
  {
    // Populate person for auditing
    var existing = ctx.Users.Include(u => u.Person).FirstOrDefault(u => u.Id == user.Id);
    if (existing == null) return;
    ctx.Users.Remove(existing);
    ctx.SaveChanges();
  }

  public static async Task BulkDeleteUsers(
    this ImmybotDbContext ctx,
    List<int> userIds)
  {
    if (userIds.Count < 1) return;

    var usersToDelete = await ctx.Users
      .Where(u => userIds.Contains(u.Id))
      .ToListAsync();

    if(usersToDelete.Count < 1) return;

    ctx.Users.RemoveRange(usersToDelete);
    await ctx.SaveChangesAsync();
  }

  public static bool IsPersonAUser(
    this ImmybotDbContext ctx,
    int personId) => ctx.Users.Any(a => a.PersonId == personId);

  public static User? GetUserByPersonId(
    this ImmybotDbContext ctx,
    int personId) => ctx.Users.AsNoTracking().FirstOrDefault(a => a.PersonId == personId);

  public static bool AnyPersonUsers(this ImmybotDbContext ctx) => ctx.Users.Any(a => a.PersonId != null);

  private static Expression<Func<User, bool>> ByPrincipalId(string id)
  {
    if (string.IsNullOrEmpty(id))
      throw new ArgumentNullException(nameof(id), "Principal id cannot be null");
    return (user) => (
      user.ServicePrincipalId == id
      || user.Person!.AzurePrincipalId == id
    );
  }
}
