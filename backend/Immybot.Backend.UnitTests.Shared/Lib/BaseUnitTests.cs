using System.Collections.Immutable;
using System.Text.Json;
using Immybot.Backend.Application.DbContextExtensions;
using Immybot.Backend.Application.DbContextExtensions.UserExtensions;
using Immybot.Backend.Application.Interface;
using Immybot.Backend.Application.Interface.Actions;
using Immybot.Backend.Application.Lib;
using Immybot.Backend.Application.Lib.Policies;
using Immybot.Backend.Application.Lib.Scripts.EphemeralAgent;
using Immybot.Backend.Application.Oauth;
using Immybot.Backend.Application.Services;
using Immybot.Backend.Application.Stores;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.GlobalSoftwarePersistence;
using Immybot.Backend.Infrastructure;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.Manager.Domain;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.Providers.ImmyAgentProvider.Signalr.AgentHub;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Infrastructure.Extensions;
using Immybot.Backend.UnitTests.Shared.Lib.Logger;
using Immybot.Backend.UnitTests.Shared.Sqlite;
using Immybot.Backend.Web.Common.Infrastructure;
using Immybot.Backend.Web.Common.Lib;
using Immybot.Backend.Web.Common.Lib.SignalRHubs;
using Immybot.Shared.DataContracts.Signalr;
using Immybot.Shared.JsonConverters;
using Immybot.Shared.Primitives;
using Immybot.Shared.Scripts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NuGet.Versioning;
using Polly.Registry;
using Xunit.Abstractions;

namespace Immybot.Backend.UnitTests.Shared.Lib;

public abstract class BaseUnitTests : SqliteUnitTestBase
{
  public const string ImmenseTenantPrincipalId = "b992c3df-0c32-4cdf-abd2-f64e86513c7b";
  public const string MspTenantPrincipalId = "11111111-1111-1111-1111-111111111111";
  public const string CustomerTenantPrincipalId = "22222222-2222-2222-2222-222222222222";
  public const string MspUserPrincipalId = "062a2464-c462-4268-9a0f-76290159ffcb";

  private static readonly SemaphoreSlim _globalDbExportLock = new(1, 1);
  private static GlobalDbExportModel? _globalDbExport;

  protected BaseUnitTests() : base(MockLoggerFactory())
  {
    AddZzzEfExtensionsLicense();
  }

  /// <summary>
  /// Use this constructor when you want to log to the test output. DbContexts returned from
  /// this class will automatically use the test output logger.
  /// </summary>
  protected BaseUnitTests(ITestOutputHelper helper, bool showLogsInTestOutput = true) : base(
    showLogsInTestOutput ? new XunitLoggerFactory(helper) : MockLoggerFactory())
  {
    AddZzzEfExtensionsLicense();
  }

  protected int NumComputers { get; private set; }
  protected int NumTenants { get; private set; }
  protected int NumProviderAgents { get; private set; }
  protected int NumGlobalSoftwares { get; private set; }
  protected int NumLocalSoftwares { get; private set; }

  protected string GetNewComputerName() => $"Test Computer{NumComputers++}";
  protected string GetNewTenantName() => $"Test Tenant{NumTenants++}";
  protected string GetNewProviderAgentExternalId() => $"testprovideragentid{NumProviderAgents++}";
  protected int GetNextLocalSoftwareId() => ++NumLocalSoftwares;
  protected int GetNextGlobalSoftwareId() => ++NumGlobalSoftwares;

  protected Tenant? ImmenseTenant { get; private set; }
  protected Tenant? MspTenant { get; private set; }
  protected Tenant? CustomerTenant { get; private set; }
  protected User? ImmenseAdminUser { get; private set; }
  protected readonly Dictionary<int, ProviderLink> DefaultProviderLinks = [];
  protected readonly Dictionary<int, ProviderClient> DefaultProviderClients = [];

  public enum UserType
  {
    ImmenseAdmin,
    ImmenseNonAdmin,
    MspAdmin,
    MspNonAdmin,
    CustomerAdmin,
    CustomerNonAdmin
  }

  public static IPolicyRegistry<string> GetPolicyRegistry()
  {
    var registry = new PolicyRegistry();
    registry.AddScheduleCachePolicy();
    registry.AddDeploymentGroupCachePolicy();
    registry.AddInventoryTaskCachePolicy();
    registry.AddChocoCachePolicy();
    registry.AddAgentIdentificationProviderLinkCachePolicy();
    registry.AddImmenseChocoApiPolicy(string.Empty);
    registry.AddChocoOrgApiPolicy(string.Empty);
    registry.AddProviderNamePolicy();
    registry.AddShowCommandInfoCachePolicy();
    registry.AddFindGlobalSoftwareCachePolicy();
    return registry;
  }

  protected User GetOrCreateUser(UserType userType)
  {
    // arrange
    using var ctx = GetSqliteDbContext();
    Tenant tenant;
    if (userType == UserType.ImmenseAdmin || userType == UserType.ImmenseNonAdmin)
      tenant = GetOrCreateImmenseTenant();
    else if (userType == UserType.MspAdmin || userType == UserType.MspNonAdmin)
      tenant = GetOrCreateMspTenant();
    else
      tenant = GetOrCreateCustomerTenant();

    var person = CreatePerson(
      tenantId: tenant.Id,
      emailAddress: "<EMAIL>",
      principalId: "some id",
      firstName: "first",
      lastName: "last"
    );

    var user = new User()
    {
      TenantId = tenant.Id,
      IsAdmin = (userType == UserType.ImmenseAdmin || userType == UserType.MspAdmin ||
                 userType == UserType.CustomerAdmin),
      PersonId = person.Id
    };

    // act
    return ctx.CreateUser(user);
  }

  protected static async Task<string> GetRawSoftwareInventoryOutput()
  {
    var assembly = typeof(BaseUnitTests).Assembly;
    var resourceName = assembly.GetManifestResourceNames().First(x => x.EndsWith("RawSoftwareInventoryOutput.json"));

    using var mrs = assembly.GetManifestResourceStream(resourceName);
    using var reader = new StreamReader(mrs!);
    return await reader.ReadToEndAsync();
  }

  protected static async Task<DetectedComputerSoftware[]> GetDetectedSoftwareFactoryData()
  {
    var assembly = typeof(BaseUnitTests).Assembly;
    var resourceName = assembly.GetManifestResourceNames().First(x => x.EndsWith("DetectedSoftwareSet.json"));

    using var mrs = assembly.GetManifestResourceStream(resourceName);
    using var reader = new StreamReader(mrs!);

    var content = await reader.ReadToEndAsync();
    var result = JsonSerializer.Deserialize<DetectedComputerSoftware[]>(content);
    if (result is null)
    {
      throw new Exception("Deserialized DetectedComputerSoftware[] is null.");
    }

    return result;
  }

  protected static async Task ImportDetectedSoftware(ImmybotDbContext dbContext, int computerId, int tenantId)
  {
    var software = await GetDetectedSoftwareFactoryData();

    foreach (var item in software)
    {
      item.ComputerId = computerId;
      item.TenantId = tenantId;
    }

    await dbContext.DetectedComputerSoftware.AddRangeAsync(software);
    await dbContext.SaveChangesAsync();
  }

  protected static async Task ImportGlobalFactoryData(Func<SoftwareDbContext> softwareDbFactory)
  {
    await _globalDbExportLock.WaitAsync();
    await using var dbContext = softwareDbFactory();

    try
    {
      if (_globalDbExport is null)
      {
        var assembly = typeof(BaseUnitTests).Assembly;
        var resourceName = assembly.GetManifestResourceNames().First(x => x.EndsWith("GlobalDb.json"));

        using var mrs = assembly.GetManifestResourceStream(resourceName);
        using var reader = new StreamReader(mrs!);

        var options = new JsonSerializerOptions();
        options.Converters.Add(new SemanticVersionConverterStj());

        var content = await reader.ReadToEndAsync();
        _globalDbExport = JsonSerializer.Deserialize<GlobalDbExportModel>(content, options);

        if (_globalDbExport is null)
        {
          throw new Exception("Failed to deserialize the global DB export.");
        }
      }

      dbContext.RemoveRange(dbContext.Software);
      dbContext.RemoveRange(dbContext.SoftwareVersions);
      dbContext.RemoveRange(dbContext.InventoryTasks);
      dbContext.RemoveRange(dbContext.MaintenanceTaskParameters);
      dbContext.RemoveRange(dbContext.MaintenanceTaskParameterValues);
      dbContext.RemoveRange(dbContext.MaintenanceTasks);
      dbContext.RemoveRange(dbContext.Media);
      dbContext.RemoveRange(dbContext.Scripts);
      dbContext.RemoveRange(dbContext.TargetAssignments);
      await dbContext.SaveChangesAsync();

      await dbContext.AddRangeAsync(_globalDbExport.Software);
      await dbContext.AddRangeAsync(_globalDbExport.SoftwareVersions);
      await dbContext.AddRangeAsync(_globalDbExport.InventoryTasks);
      await dbContext.AddRangeAsync(_globalDbExport.MaintenanceTaskParameters);
      await dbContext.AddRangeAsync(_globalDbExport.MaintenanceTaskParameterValues);
      await dbContext.AddRangeAsync(_globalDbExport.MaintenanceTasks);
      await dbContext.AddRangeAsync(_globalDbExport.Media);
      await dbContext.AddRangeAsync(_globalDbExport.Scripts);
      await dbContext.AddRangeAsync(_globalDbExport.TargetAssignments);
      await dbContext.SaveChangesAsync();
    }
    finally
    {
      _globalDbExportLock.Release();
    }
  }

  protected Computer CreateBlankComputer(
    int? tenantId = null,
    int? linkId = null,
    string? externalClientId = null,
    ComputerOnboardingStatus onboardingStatus = ComputerOnboardingStatus.Onboarded,
    ImmybotDbContext? ctx = null)
  {
    tenantId ??= CreateTenant(ctx: ctx).Id;
    linkId ??= CreateProviderLink(tenantId.Value, ctx: ctx).Id;
    externalClientId ??= CreateProviderClient(linkId.Value, tenantId.Value, ctx: ctx).ExternalClientId;
    return CreateComputer(
      linkId.Value,
      externalClientId,
      GetNewProviderAgentExternalId(),
      tenantId.Value,
      GetNewComputerName(),
      Guid.NewGuid(),
      isOnline: true,
      onboardingStatus: onboardingStatus,
      ctx: ctx);
  }

  protected ComputerPerson CreateComputerPerson(int computerId, int personId)
  {
    using var dbCtx = GetSqliteDbContext();
    var cp = new ComputerPerson { ComputerId = computerId, PersonId = personId };
    dbCtx.ComputerPersons.Add(cp);
    dbCtx.SaveChanges();
    return cp;
  }

  protected async Task<UserAffinity> CreateUserAffinity(int computerId, int personId)
  {
    await using var dbCtx = GetSqliteDbContext();
    var ua = new UserAffinity { ComputerId = computerId, PersonId = personId };
    dbCtx.UserAffinities.Add(ua);
    await dbCtx.SaveChangesAsync();
    return ua;
  }

  protected Computer CreateComputer(string computerName = "test", int? primaryPersonId = null)
  {
    var tenant = GetOrCreateMspTenant();
    var computer = new Computer()
    {
      TenantId = tenant.Id,
      DeviceId = Guid.NewGuid(),
      ComputerName = computerName,
      PrimaryPersonId = primaryPersonId
    };

    using var dbCtx = GetSqliteDbContext();
    dbCtx.Computers.Add(computer);
    dbCtx.SaveChanges();
    return computer;
  }

  protected Computer CreateComputer(
    int providerLinkId,
    string externalClientId,
    string externalAgentId,
    int tenantId,
    string computerName,
    Guid deviceId,
    bool isOnline = false,
    bool isPortable = false,
    bool isServer = false,
    int? primaryPersonId = null,
    ComputerOnboardingStatus onboardingStatus = ComputerOnboardingStatus.Onboarded,
    DateTime? onboardedDate = null,
    bool excludeFromMaintenance = false,
    DateTime? devLabExpirationDate = null,
    bool isDevLabUnclaimed = false,
    bool isDevLabVm = false,
    ImmybotDbContext? ctx = null,
    string? serial = null)
  {
    var runScriptAgent = new ProviderAgent()
    {
      ProviderLinkId = providerLinkId,
      ExternalClientId = externalClientId,
      ExternalAgentId = externalAgentId,
      IsOnline = isOnline,
      SupportsRunningScripts = true,
      DeviceDetails = new(),
    };

    var computer = new Computer()
    {
      TenantId = tenantId,
      DeviceId = deviceId,
      PrimaryPersonId = primaryPersonId,
      OnboardingStatus = onboardingStatus,
      OnboardedDateUtc = onboardedDate,
      ComputerName = computerName,
      ExcludeFromMaintenance = excludeFromMaintenance,
      DevLabVmClaimExpirationDateUtc = devLabExpirationDate,
      DevLabVmName = isDevLabVm ? computerName : null,
      DevLabVmUnclaimed = isDevLabUnclaimed,
      SerialNumber = serial
    };

    computer.Agents.Add(runScriptAgent);
    // ReSharper disable once NotDisposedResource
    var dbCtx = ctx ?? GetSqliteDbContext();
    dbCtx.Computers.Add(computer);
    dbCtx.SaveChanges();
    return dbCtx.Computers
      .Include(it => it.Tenant)
      .Include(it => it.Agents)
      .ThenInclude(it => it.ProviderLink)
      .First(it => it.Id == computer.Id);
  }

  protected ProviderAgent CreateProviderAgent(
    int providerLinkId,
    string externalClientId,
    string externalAgentId,
    int? computerId = null,
    bool supportsRunningScripts = false,
    bool isOnline = true,
    ImmybotDbContext? ctx = null)
  {
    // ReSharper disable once NotDisposedResource
    var dbCtx = ctx ?? GetSqliteDbContext();
    var agent = new ProviderAgent()
    {
      ProviderLinkId = providerLinkId,
      ExternalClientId = externalClientId,
      ExternalAgentId = externalAgentId,
      IsOnline = isOnline,
      ComputerId = computerId,
      SupportsRunningScripts = supportsRunningScripts,
      DeviceDetails = new() { DeviceId = Guid.NewGuid() },
      // Z.EntityFramework complains about null owned references
      OnboardingOptions = new(),
    };
    dbCtx.CreateProviderAgents(new[] { agent });
    return agent;
  }

  protected ProviderAgent CreatePendingProviderAgent(
    int providerLinkId,
    string externalClientId,
    string externalAgentId)
    => CreateProviderAgent(providerLinkId, externalClientId, externalAgentId);

  protected ProviderAgent IdentifyPendingAgent(
    ProviderAgent pending,
    Guid deviceId,
    DateTime installDate,
    string machineId,
    bool isSandbox)
  {
    pending.DeviceDetails.DeviceId = deviceId;
    pending.DeviceDetails.OSInstallDateUTC = installDate;
    pending.DeviceDetails.MachineId = machineId;
    pending.DeviceDetails.IsSandbox = isSandbox;

    using var ctx = GetSqliteDbContext();
    ctx.UpdateProviderAgent(pending);
    return pending;
  }

  protected AgentIdentificationFailure CreateAgentIdentificationFailure(int pendingId)
  {
    using var ctx = GetSqliteDbContext();
    var failure = new AgentIdentificationFailure() { PendingAgentId = pendingId, };
    ctx.AgentIdentificationFailures.Add(failure);
    ctx.SaveChanges();
    return failure;
  }

  protected TargetAssignment CreateGlobalTargetAssignment(TargetAssignment assignment, bool? approved = null)
  {
    using var ctx = GetSqliteSoftwareDbContext();
    ctx.TargetAssignments.Add(assignment);
    ctx.SaveChanges();
    if (approved is { } aPpRoVeD)
    {
      CreateRecommendedTargetAssignmentApproval(new()
      {
        GlobalTargetAssignmentId = assignment.Id,
        Approved = aPpRoVeD
      });
      ctx.SaveChanges();
    }

    return assignment;
  }

  protected RecommendedTargetAssignmentApproval CreateRecommendedTargetAssignmentApproval(
    RecommendedTargetAssignmentApproval a)
  {
    using var ctx = GetSqliteDbContext();
    ctx.RecommendedTargetAssignmentApprovals.Add(a);
    ctx.SaveChanges();
    return a;
  }

  protected MaintenanceTask CreateMaintenanceTask(MaintenanceTask task)
  {
    if (task.DatabaseType is DatabaseType.Local)
    {
      using var ctx = GetSqliteDbContext();
      task.DatabaseType = DatabaseType.Local;
      if (string.IsNullOrEmpty(task.Name)) task.Name = "Foobar";
      ctx.MaintenanceTasks.Add(task);
      ctx.SaveChanges();
      return task;
    }

    return CreateGlobalMaintenanceTask(task);
  }

  protected Schedule CreateSchedule(Schedule schedule)
  {
    using var ctx = GetSqliteDbContext();
    ctx.Schedules.Add(schedule);
    ctx.SaveChanges();
    return schedule;
  }

  protected ProviderClient GetOrCreateDefaultProviderClient(int providerLinkId,
    string? name = null,
    bool linkedToDefaultTenant = true)
  {
    if (DefaultProviderClients.TryGetValue(providerLinkId, out var client)) return client;
    client = new ProviderClient
    {
      ProviderLinkId = providerLinkId,
      ExternalClientId = $"DefaultClientForLink{providerLinkId}",
      ExternalClientName = name ?? "",
      LinkedToTenantId = linkedToDefaultTenant
        ? DefaultProviderLinks.FirstOrDefault(kvp => kvp.Value.Id == providerLinkId).Key
        : null,
    };
    using var dbContext = GetSqliteDbContext();
    dbContext.CreateProviderClient(client);
    DefaultProviderClients.Add(providerLinkId, client);
    return client;
  }

  protected async Task<AuthUserDto?> GetAuthUserDtoFromUserId(int userId, DateTime? now = null)
  {
    var nowTime = now ?? DateTime.UtcNow;
    await using var dbContext = GetSqliteDbContext();
    return await dbContext.GetAuthUserById(userId, nowTime);
  }

  protected User GetOrCreateDefaultUser(int tenantId)
  {
    if (ImmenseAdminUser != null) return ImmenseAdminUser;

    var person = CreatePerson(tenantId, "<EMAIL>", "immy", "bot", "testUserPrincipalId");

    var user = new User { TenantId = tenantId, PersonId = person.Id };
    using var dbContext = GetSqliteDbContext();
    dbContext.Users.Add(user);
    dbContext.Users.Include(it => it.Person);
    dbContext.SaveChanges();

    ImmenseAdminUser = dbContext.Users
      .Include(it => it.Tenant)
      .Include(it => it.Person)
      .First(u => u.Id == user.Id);
    return ImmenseAdminUser;
  }

  protected ProviderLink GetOrCreateDefaultProviderLink(int tenantId, bool disabled = false)
  {
    if (DefaultProviderLinks.TryGetValue(tenantId, out var providerLink)) return providerLink;
    providerLink = new ProviderLink
    {
      Name = $"link {tenantId}",
      OwnerTenantId = tenantId,
      ProviderTypeFormData = JsonSerializer.Deserialize<JsonElement>("{}"),
      Disabled = disabled
    };
    using var dbContext = GetSqliteDbContext();
    dbContext.CreateProviderLink(providerLink);
    DefaultProviderLinks.Add(tenantId, providerLink);
    return providerLink;
  }

  protected ProviderLink CreateProviderLink(
    int tenantId,
    ImmybotDbContext? ctx = null,
    Guid? providerTypeId = null)
  {
    var link = new ProviderLink
    {
      Name = Guid.NewGuid().ToString(),
      OwnerTenantId = tenantId,
      ProviderTypeFormData = JsonSerializer.Deserialize<JsonElement>("{}"),
      ProviderTypeId = providerTypeId ?? Guid.NewGuid()
    };
    // ReSharper disable once NotDisposedResource
    var dbContext = ctx ?? GetSqliteDbContext();
    dbContext.CreateProviderLink(link);
    return link;
  }

  protected DynamicIntegrationType CreateGlobalDynamicIntegrationType(
    string script,
    int? integrationId = null,
    Guid? integrationTypeId = null,
    IntegrationTag integrationTag = IntegrationTag.Production,
    string scriptName = "IntegrationScript"
  )
  {
    // Create the script
    var integrationScript = CreateGlobalScript(
      name: scriptName,
      action: script,
      scriptLanguage: ScriptLanguage.PowerShell,
      category: ScriptCategory.Integration
    );

    var dynamicIntegrationType = new DynamicIntegrationType
    {
      Id = integrationId ?? 1,
      Name = "TestIntegration",
      IntegrationTypeId = integrationTypeId ?? Guid.NewGuid(),
      DatabaseType = DatabaseType.Global,
      Tag = integrationTag,
      Enabled = true,
      ScriptId = integrationScript.Id,
      LogoId = 1,
      DocsUrl = "https://immense.net"
    };

    using var ctx = GetSqliteSoftwareDbContext();
    ctx.DynamicIntegrationTypes.Add(dynamicIntegrationType);
    ctx.SaveChanges();

    return ctx.DynamicIntegrationTypes
      .Include(it => it.Script)
      .First(it => it.Id == dynamicIntegrationType.Id);
  }

  protected Tenant GetOrCreateImmenseTenant()
  {
    if (ImmenseTenant != null) return ImmenseTenant;
    ImmenseTenant = CreateTenant(ownerTenantId: null, principalId: ImmenseTenantPrincipalId, isMsp: true);
    return ImmenseTenant;
  }

  protected Tenant GetOrCreateMspTenant(ImmybotDbContext? ctx = null)
  {
    if (MspTenant != null) return MspTenant;
    MspTenant = CreateTenant(ownerTenantId: null,
      principalId: Guid.Parse(MspTenantPrincipalId).ToString(),
      ctx: ctx,
      isMsp: true);
    return MspTenant;
  }

  protected Tenant GetOrCreateCustomerTenant(int? parentTenantId = null, string? tenantName = null)
  {
    if (CustomerTenant != null) return CustomerTenant;
    var mspTenant = GetOrCreateMspTenant();
    CustomerTenant = CreateTenant(
      ownerTenantId: mspTenant.Id,
      parentTenantId: parentTenantId,
      principalId: Guid.Parse(CustomerTenantPrincipalId).ToString(),
      partnerPrincipalId: mspTenant.AzureTenantLink?.AzureTenant?.PrincipalId,
      tenantName: tenantName);
    return CustomerTenant;
  }

  protected Tenant CreateTenant(int? ownerTenantId = null,
    string? principalId = null,
    string? partnerPrincipalId = null,
    List<string>? limitToDomains = null,
    ImmybotDbContext? ctx = null,
    bool isMsp = false,
    int? parentTenantId = null,
    string? tenantName = null)
  {
    var name = tenantName ?? GetNewTenantName();
    // ReSharper disable once NotDisposedResource
    var dbContext = ctx ?? GetSqliteDbContext();
    var azTenant = principalId == null
      ? null
      : dbContext.AzureTenants.FirstOrDefault(a => a.PrincipalId == principalId) ??
        new AzureTenant()
        {
          AzureTenantType =
            partnerPrincipalId == null ? AzTenantType.Standalone : AzTenantType.Customer,
          PartnerPrincipalId = partnerPrincipalId,
          ConsentDetails = new AzureTenantConsentDetails(),
          PrincipalId = principalId
        };
    var azLink = principalId == null
      ? null
      : new AzureTenantLink()
      {
        AzTenantId = principalId,
        ImmyTenantId = 0,
        ShouldLimitDomains = limitToDomains != null,
        AzureTenant = azTenant!,
      };
    if (azLink != null && limitToDomains != null)
    {
      azLink.LimitToDomains.AddRange(limitToDomains.Select(d => new AzureTenantLinkDomainFilter()
      {
        DomainName = d,
        ImmyTenantId = 0,
        AzTenantId = azLink.AzTenantId
      }));
    }
    var tenant = new Tenant
    {
      Active = true,
      Name = name,
      AzureTenantLink = azLink,
      OwnerTenantId = ownerTenantId,
      ParentTenantId = parentTenantId,
      IsMsp = isMsp
    };
    dbContext.Tenants.Add(tenant);
    dbContext.SaveChanges();
    return tenant;
  }

  protected AzureErrorLogItem CreateAzureError(ImmybotDbContext ctx, string? tenantPrincipalId)
  {
    var item = new AzureErrorLogItem
    {
      SourceMessage = "foo",
      AzureError = new AzureError("some error"),
      TenantPrincipalId = tenantPrincipalId,
      Id = Guid.NewGuid()
    };
    ctx.AzureErrors.SingleInsert(item);
    return item;
  }

  protected ProviderClient CreateProviderClient(
    int providerLinkId,
    int? tenantId = null,
    string? name = null,
    ImmybotDbContext? ctx = null,
    bool useTenantIdAsClientId = false)
  {
    var client = new ProviderClient
    {
      ProviderLinkId = providerLinkId,
      ExternalClientId = useTenantIdAsClientId && tenantId.HasValue ? tenantId.ToString()! : Guid.NewGuid().ToString(),
      ExternalClientName = name ?? "",
      LinkedToTenantId = tenantId,
    };
    // ReSharper disable once NotDisposedResource
    var dbCtx = ctx ?? GetSqliteDbContext();
    dbCtx.CreateProviderClient(client);
    return client;
  }

  protected ChangeRequest CreateChangeRequest(
    AuthUserDto user,
    ChangeRequestObjectType objectType,
    JsonElement newValues,
    int? scriptId = null,
    int? deploymentId = null
  )
  {
    var cr = new ChangeRequest
    {
      ObjectType = objectType,
      NewValuesJson = newValues,
      ScriptId = scriptId,
      TargetAssignmentId = deploymentId,
    };

    using var dbContext = GetSqliteDbContext();
    dbContext.SetUser(user);
    dbContext.ChangeRequests.Add(cr);
    dbContext.SaveChanges();
    return cr;
  }

  protected Script CreateLocalScript(
    string name = "script",
    string action = "choco -v",
    List<TenantScript>? tenants = null,
    ScriptLanguage scriptLanguage = ScriptLanguage.PowerShell,
    ScriptCategory category = ScriptCategory.MaintenanceTaskSetter
  )
  {
    var script = new Script
    {
      Name = name,
      Action = action,
      TenantRelationships = tenants ?? [],
      ScriptLanguage = scriptLanguage,
      ScriptType = DatabaseType.Local,
      ScriptCategory = category,
    };
    using var dbContext = GetSqliteDbContext();
    dbContext.Scripts.Add(script);
    dbContext.SaveChanges();
    return script;
  }

  protected void AddFunctionScript(string name, string script)
  {
    CreateGlobalScript(
      name,
      action: script,
      category: ScriptCategory.Function,
      executionContext: ScriptExecutionContext.Metascript);
  }

  protected Script CreateGlobalScript(
    string name = "script",
    string action = "choco -v",
    List<TenantScript>? tenants = null,
    ScriptLanguage scriptLanguage = ScriptLanguage.PowerShell,
    ScriptCategory category = ScriptCategory.MaintenanceTaskSetter,
    ScriptExecutionContext executionContext = ScriptExecutionContext.Metascript
  )
  {
    var script = new Script
    {
      Name = name,
      Action = action,
      TenantRelationships = tenants ?? [],
      ScriptLanguage = scriptLanguage,
      ScriptType = DatabaseType.Global,
      ScriptCategory = category,
      ScriptExecutionContext = executionContext
    };
    using var dbContext = GetSqliteSoftwareDbContext();
    dbContext.Scripts.Add(script);
    dbContext.SaveChanges();
    return script;
  }

  protected Tag CreateTag(string name, List<TenantTagAuthorization> authorizations)
  {
    var tag = new Tag { Name = name, Description = "test tag", TenantRelationships = authorizations };
    using var dbContext = GetSqliteDbContext();
    dbContext.Tags.Add(tag);
    dbContext.SaveChanges();
    return tag;
  }

  protected Tag CreateTagWithTenant(string name, int tenantId)
  {
    var tag = new Tag { Name = name, Description = "test tag" };
    using var dbContext = GetSqliteDbContext();
    dbContext.Tags.Add(tag);
    dbContext.SaveChanges();

    var tenantTag = new TenantTagAuthorization { TenantId = tenantId, TagId = tag.Id };
    dbContext.TenantTagAuthorizations.Add(tenantTag);
    dbContext.SaveChanges();

    return tag;
  }

  protected Media CreateMediaWithTenant(string name, string fileName, int tenantId)
  {
    var media = new Media { Name = name, FileName = fileName, BlobReference = $"blob/{fileName}" };
    using var dbContext = GetSqliteDbContext();
    dbContext.Media.Add(media);
    dbContext.SaveChanges();

    var tenantMedia = new TenantMedia { TenantId = tenantId, MediaId = media.Id };
    dbContext.TenantMedia.Add(tenantMedia);
    dbContext.SaveChanges();

    return media;
  }

  protected Script CreateScriptWithTenant(string name, int tenantId)
  {
    var script = new Script { Name = name, Action = "echo 'test'", ScriptLanguage = ScriptLanguage.PowerShell, ScriptType = DatabaseType.Local, ScriptCategory = ScriptCategory.MaintenanceTaskSetter };
    using var dbContext = GetSqliteDbContext();
    dbContext.Scripts.Add(script);
    dbContext.SaveChanges();

    var tenantScript = new TenantScript { TenantId = tenantId, ScriptId = script.Id };
    dbContext.TenantScripts.Add(tenantScript);
    dbContext.SaveChanges();

    return script;
  }

  protected Tag CreateTagWithMultipleTenants(string name, params int[] tenantIds)
  {
    var tag = new Tag { Name = name, Description = "test tag with multiple tenants" };
    using var dbContext = GetSqliteDbContext();
    dbContext.Tags.Add(tag);
    dbContext.SaveChanges();

    foreach (var tenantId in tenantIds)
    {
      var tenantTag = new TenantTagAuthorization { TenantId = tenantId, TagId = tag.Id };
      dbContext.TenantTagAuthorizations.Add(tenantTag);
    }
    dbContext.SaveChanges();

    return tag;
  }

  protected Media CreateMediaWithMultipleTenants(string name, string fileName, params int[] tenantIds)
  {
    var media = new Media { Name = name, FileName = fileName, BlobReference = $"blob/{fileName}" };
    using var dbContext = GetSqliteDbContext();
    dbContext.Media.Add(media);
    dbContext.SaveChanges();

    foreach (var tenantId in tenantIds)
    {
      var tenantMedia = new TenantMedia { TenantId = tenantId, MediaId = media.Id };
      dbContext.TenantMedia.Add(tenantMedia);
    }
    dbContext.SaveChanges();

    return media;
  }

  protected Script CreateScriptWithMultipleTenants(string name, params int[] tenantIds)
  {
    var script = new Script { Name = name, Action = "echo 'test'", ScriptLanguage = ScriptLanguage.PowerShell, ScriptType = DatabaseType.Local, ScriptCategory = ScriptCategory.MaintenanceTaskSetter };
    using var dbContext = GetSqliteDbContext();
    dbContext.Scripts.Add(script);
    dbContext.SaveChanges();

    foreach (var tenantId in tenantIds)
    {
      var tenantScript = new TenantScript { TenantId = tenantId, ScriptId = script.Id };
      dbContext.TenantScripts.Add(tenantScript);
    }
    dbContext.SaveChanges();

    return script;
  }

  protected User CreateUser(
    int tenantId,
    int? personId = null,
    string? principalId = null,
    bool isAdmin = false)
  {
    var user = new User
    {
      IsAdmin = isAdmin,
      PersonId = personId,
      TenantId = tenantId,
      ServicePrincipalId = principalId,
    };
    using var dbContext = GetSqliteDbContext();
    dbContext.Users.Add(user);
    dbContext.SaveChanges();
    ImmenseAdminUser = dbContext.Users
      .Include(it => it.Person)
      .Include(it => it.Tenant)
      .First(it => it.Id == user.Id);
    return ImmenseAdminUser;
  }

  protected ScheduledEmail CreateScheduledEmail(int sessionId)
  {
    var email = new ScheduledEmail() { SessionId = sessionId, };

    using var ctx = GetSqliteDbContext();
    ctx.ScheduledEmails.Add(email);
    ctx.SaveChanges();
    return email;
  }

  protected Person CreatePerson(
    int tenantId,
    string emailAddress,
    string? firstName = null,
    string? lastName = null,
    string? principalId = null,
    ICollection<Tag>? tags = null
  )
  {
    var person = new Person()
    {
      TenantId = tenantId,
      EmailAddress = emailAddress,
      FirstName = firstName,
      LastName = lastName,
      AzurePrincipalId = principalId,
      UpdatedDate = DateTime.UtcNow,
      Tags = tags ?? new List<Tag>(),
    };

    using var ctx = GetSqliteDbContext();
    ctx.Persons.Add(person);
    ctx.SaveChanges();
    return person;
  }

  protected ComputerInventoryTaskScriptResult CreateInventoryTaskScriptResult(
    ComputerInventoryTaskScriptResult result)
  {
    using var ctx = GetSqliteDbContext();
    ctx.ComputerInventoryTaskScriptResults.Add(result);
    ctx.SaveChanges();

    return result;
  }

  protected ComputerInventoryTaskScriptResult CreateWindowsSystemInfoInventoryResult(
    int computerId,
    string? machineId = null)
  {
    var result = new ComputerInventoryTaskScriptResult
    {
      ComputerId = computerId,
      InventoryKey = InventoryKeys.WindowsSystemInfo,
      LatestResultIsError = false,
      Timestamp = DateTime.UtcNow,
      LatestErrorResult = null,
      LatestSuccessResult = JsonDocument.Parse($@"{{
  ""Output"": {{
    ""Model"": ""Virtual Machine"",
    ""Domain"": ""WORKGROUP"",
    ""OsName"": ""Microsoft Windows 10 Enterprise"",
    ""DomainRole"": 0,
    ""ChassisTypes"": ""3"",
    ""ComputerName"": ""3AA5B2DB-1D1B-4"",
    ""LastBootTime"": ""2020-09-28T15:46:13.266746"",
    ""SerialNumber"": ""**************-1871-2396-0113-37"",
    ""OsInstallDate"": ""2020-04-17T20:17:36"",
    ""MachineID"": ""{machineId}""
  }},
  ""ConsoleText"": """"
}}
")
    };

    return CreateInventoryTaskScriptResult(result);
  }

  protected GlobalSoftware CreateGlobalSoftware(
    string? name = null,
    Script? detectionScript = null,
    int? id = null,
    Guid? agentIntegrationTypeId = null)
  {
    var softwareId = id ?? GetNextGlobalSoftwareId();
    var software = new GlobalSoftware
    {
      Id = softwareId,
      Name = name ?? $"Global-{softwareId}",
      AgentIntegrationTypeId = agentIntegrationTypeId,
      DetectionScript = detectionScript
    };
    using var sftContext = GetSqliteSoftwareDbContext();
    sftContext.Software.Add(software);
    sftContext.SaveChanges();
    return software;
  }

  protected GlobalSoftwareVersion CreateGlobalSoftwareVersion(
    GlobalSoftware software,
    string semanticVersion,
    Script? installScript = null,
    Script? testScript = null,
    Script? uninstallScript = null,
    Script? upgradeScript = null)
  {
    var ver = NuGetVersion.Parse(semanticVersion);
    var softwareId = software.Id;
    var version = new GlobalSoftwareVersion
    {
      SoftwareId = softwareId,
      SemanticVersion = ver,
      InstallScript = installScript,
      InstallScriptId = installScript?.Id,
      InstallScriptType = DatabaseType.Global,
      TestScript = testScript,
      TestScriptId = testScript?.Id,
      TestScriptType = DatabaseType.Global,
      UninstallScript = uninstallScript,
      UninstallScriptId = uninstallScript?.Id,
      UninstallScriptType = DatabaseType.Global,
      UpgradeScript = upgradeScript,
      UpgradeScriptId = upgradeScript?.Id,
      UpgradeScriptType = DatabaseType.Global,
    };
    using var dbContext = GetSqliteSoftwareDbContext();
    dbContext.SoftwareVersions.Add(version);
    dbContext.SaveChanges();
    return version;
  }

  protected LocalSoftware CreateLocalSoftware(
    string? name = null,
    Script? detectionScript = null,
    Script? installScript = null,
    Script? uninstallScript = null,
    Script? postInstallScript = null,
    Script? postUninstallScript = null,
    Script? testScript = null,
    Script? upgradeScript = null,
    Script? dynamicVersionsScript = null,
    ICollection<int>? tenantSoftware = null,
    MaintenanceTask? task = null,
    string? niniteProviderSoftwareId = null,
    string? chocolateySoftwareProviderId = null,
    Guid? agentIntegrationTypeId = null,
    int? ownerTenantId = null)
  {
    int id = GetNextLocalSoftwareId();
    var software = new LocalSoftware
    {
      Id = id,
      Name = name ?? $"Local-{id}",
      InstallScript = installScript,
      InstallScriptId = installScript?.Id,
      InstallScriptType = installScript?.ScriptType,
      UninstallScript = uninstallScript,
      UninstallScriptId = uninstallScript?.Id,
      UninstallScriptType = uninstallScript?.ScriptType,
      PostInstallScript = postInstallScript,
      PostInstallScriptId = postInstallScript?.Id,
      PostInstallScriptType = postInstallScript?.ScriptType,
      PostUninstallScript = postUninstallScript,
      PostUninstallScriptId = postUninstallScript?.Id,
      PostUninstallScriptType = postUninstallScript?.ScriptType,
      TestScript = testScript,
      TestRequired = testScript != null,
      TestScriptId = testScript?.Id,
      TestScriptType = testScript?.ScriptType,
      UpgradeScript = upgradeScript,
      UpgradeScriptId = upgradeScript?.Id,
      UpgradeScriptType = upgradeScript?.ScriptType,
      DetectionScript = detectionScript,
      DetectionScriptId = detectionScript?.Id,
      DetectionScriptType = detectionScript?.ScriptType,
      UseDynamicVersions = dynamicVersionsScript != null,
      DynamicVersionsScriptId = dynamicVersionsScript?.Id,
      DynamicVersionsScriptType = dynamicVersionsScript?.ScriptType,
      MaintenanceTask = task,
      MaintenanceTaskId = task?.Id,
      MaintenanceTaskType = task?.DatabaseType,
      NiniteProviderSoftwareId = niniteProviderSoftwareId,
      ChocoProviderSoftwareId = chocolateySoftwareProviderId,
      AgentIntegrationTypeId = agentIntegrationTypeId,
      OwnerTenantId = ownerTenantId
    };
    if (tenantSoftware != null)
    {
      foreach (var t in tenantSoftware)
        software.TenantSoftware.Add(new TenantSoftware { TenantId = t });
    }

    using var dbContext = GetSqliteDbContext();
    dbContext.Software.Add(software);
    dbContext.SaveChanges();
    return software;
  }

  protected LocalSoftwareVersion CreateLocalSoftwareVersion(
    LocalSoftware software,
    string semanticVersion,
    Script? installScript = null,
    Script? testScript = null,
    Script? uninstallScript = null,
    Script? upgradeScript = null,
    Script? postInstallScript = null,
    Script? postUninstallScript = null,
    string? blobName = null)
  {
    var ver = NuGetVersion.Parse(semanticVersion);
    var softwareId = software.Id;
    var version = new LocalSoftwareVersion
    {
      SoftwareId = softwareId,
      SemanticVersion = ver,
      PostInstallScript = postInstallScript,
      PostInstallScriptId = postInstallScript?.Id,
      PostInstallScriptType = postInstallScript?.ScriptType,
      PostUninstallScript = postUninstallScript,
      PostUninstallScriptId = postUninstallScript?.Id,
      PostUninstallScriptType = postUninstallScript?.ScriptType,
      InstallScript = installScript,
      InstallScriptId = installScript?.Id,
      InstallScriptType = installScript?.ScriptType,
      TestScript = testScript,
      TestScriptId = testScript?.Id,
      TestScriptType = testScript?.ScriptType,
      UninstallScript = uninstallScript,
      UninstallScriptId = uninstallScript?.Id,
      UninstallScriptType = uninstallScript?.ScriptType,
      UpgradeScript = upgradeScript,
      UpgradeScriptId = upgradeScript?.Id,
      UpgradeScriptType = upgradeScript?.ScriptType,
      BlobName = blobName
    };
    using var dbContext = GetSqliteDbContext();
    dbContext.SoftwareVersions.Add(version);
    dbContext.SaveChanges();
    return version;
  }

  protected Software CreateSoftware(
    SoftwareType softwareType = SoftwareType.LocalSoftware,
    Script? detectionScript = null,
    ICollection<int>? tenantAssignments = null,
    int? configurationTaskId = null,
    DatabaseType? configurationTaskType = null)
  {
    if (softwareType == SoftwareType.LocalSoftware)
    {
      int id = GetNextLocalSoftwareId();
      var software = new LocalSoftware
      {
        Id = id,
        Name = $"Local-{id}",
        DetectionScript = detectionScript,
        MaintenanceTaskId = configurationTaskId,
        MaintenanceTaskType = configurationTaskType,
      };
      if (tenantAssignments != null)
      {
        foreach (var t in tenantAssignments)
          software.TenantSoftware.Add(new TenantSoftware { TenantId = t });
      }

      using var dbContext = GetSqliteDbContext();
      dbContext.Software.Add(software);
      dbContext.SaveChanges();
      return software;
    }

    if (softwareType == SoftwareType.GlobalSoftware)
    {
      int id = GetNextGlobalSoftwareId();
      var software = new GlobalSoftware
      {
        Id = id,
        Name = $"Global-{id}",
        DetectionScript = detectionScript,
        MaintenanceTaskId = configurationTaskId,
        MaintenanceTaskType = configurationTaskType,
      };
      using var sftContext = GetSqliteSoftwareDbContext();
      sftContext.Software.Add(software);
      sftContext.SaveChanges();
      return software;
    }
    else throw new NotImplementedException();
  }

  protected SoftwareVersion CreateSoftwareVersion(
    Software software,
    string semanticVersion,
    Script? installScript = null,
    Script? testScript = null,
    Script? uninstallScript = null,
    Script? upgradeScript = null)
  {
    if (software is LocalSoftware l)
    {
      var ver = NuGetVersion.Parse(semanticVersion);
      var softwareId = l.Id;
      var version = new LocalSoftwareVersion
      {
        SoftwareId = softwareId,
        SemanticVersion = ver,
        InstallScript = installScript,
        TestScript = testScript,
        UninstallScript = uninstallScript,
        UpgradeScript = upgradeScript
      };
      using var dbContext = GetSqliteDbContext();
      dbContext.SoftwareVersions.Add(version);
      dbContext.SaveChanges();
      return version;
    }
    else if (software is GlobalSoftware g)
    {
      var ver = NuGetVersion.Parse(semanticVersion);
      var softwareId = g.Id;
      var version = new GlobalSoftwareVersion
      {
        SoftwareId = softwareId,
        SemanticVersion = ver,
        InstallScript = installScript,
        TestScript = testScript,
        UninstallScript = uninstallScript,
        UpgradeScript = upgradeScript
      };
      using var dbContext = GetSqliteSoftwareDbContext();
      dbContext.SoftwareVersions.Add(version);
      dbContext.SaveChanges();
      return version;
    }
    else throw new NotImplementedException();
  }

  protected TargetAssignment CreateTargetAssignment(TargetAssignment assignment)
  {
    using var ctx = GetSqliteDbContext();
    ctx.TargetAssignments.Add(assignment);
    ctx.SaveChanges();
    return assignment;
  }

  protected TargetAssignment CreateTargetAssignment(
    int? targetAssignmentId = null,
    string? target = null,
    int? tenantId = null,
    string maintenanceIdentifier = "git",
    MaintenanceType maintenanceType = MaintenanceType.ChocolateySoftware,
    MaintenanceTaskMode maintenanceTaskMode = MaintenanceTaskMode.Enforce,
    int? providerLinkId = null,
    int? providerLinkIdForMaintenanceItem = null,
    Guid? providerDeviceGroupType = null,
    Guid? providerClientGroupType = null,
    DesiredSoftwareState desiredSoftwareState = DesiredSoftwareState.LatestVersion,
    TargetCategory targetCategory = TargetCategory.Computer,
    TargetGroupFilter targetGroupFilter = TargetGroupFilter.All,
    bool onboardingOnly = false,
    TargetType targetType = TargetType.Computer,
    ImmutableDictionary<string, DeploymentParameterValue>? taskParameterValues = null,
    bool isGlobal = false,
    TargetEnforcement targetEnforcement = TargetEnforcement.Required,
    TargetAssignmentVisibility? visibility = null)
  {
    var tenant = GetOrCreateMspTenant();
    var user = GetOrCreateDefaultUser(tenant.Id);
    var authUser = GetAuthUser(user);
    var assignment = new TargetAssignment()
    {
      Id = targetAssignmentId ?? 0,
      Target = target,
      TenantId = tenantId,
      MaintenanceIdentifier = maintenanceIdentifier,
      MaintenanceTaskMode = maintenanceTaskMode,
      MaintenanceType = maintenanceType,
      ProviderLinkId = providerLinkId,
      ProviderLinkIdForMaintenanceItem = providerLinkIdForMaintenanceItem,
      ProviderDeviceGroupType = providerDeviceGroupType,
      ProviderClientGroupType = providerClientGroupType,
      DesiredSoftwareState = desiredSoftwareState,
      TargetType = targetType,
      TargetGroupFilter = targetGroupFilter,
      TargetCategory = targetCategory,
      TargetName = target,
      TaskParameterValues = taskParameterValues,
      TargetEnforcement = targetEnforcement,
      Visibility = visibility
    };

    if (isGlobal)
    {
      using var sftContext = GetSqliteSoftwareDbContext();
      sftContext.TargetAssignments.Add(assignment);
      sftContext.SaveChanges();
      return assignment;
    }

    using var dbContext = GetSqliteDbContext();
    dbContext.SetUser(authUser);
    dbContext.TargetAssignments.Add(assignment);
    dbContext.SaveChanges();
    return assignment;
  }

  protected ApplicationPreferences CreateApplicationPreferences(
    DefaultEmailBccList? defaultEmailBccList = null,
    int? defaultBrandingId = null,
    bool enableOnboarding = false,
    bool enableHistoricalInventory = false,
    bool enableComputerSync = false,
    bool enableNewComputerSync = false,
    bool enableComputerOnlineStatusSync = false,
    bool enableAzureUserSync = false,
    bool enableProviderClientSync = false,
    bool enableNiniteIntegration = false,
    bool enableUserAffinitySync = false,
    bool enableSessionEmails = false,
    bool enableNonEssentialDeviceInventory = false,
    string? immyScriptPath = null,
    bool enableBetaDynamicIntegrationMigrations = false)
  {
    var pref = new ApplicationPreferences()
    {
      DefaultEmailBccList = defaultEmailBccList,
      DefaultBrandingId = defaultBrandingId,
      EnableOnboarding = enableOnboarding,
      EnableHistoricalInventory = enableHistoricalInventory,
      EnableAzureUserSync = enableAzureUserSync,
      EnableNiniteIntegration = enableNiniteIntegration,
      EnableUserAffinitySync = enableUserAffinitySync,
      EnableNonEssentialDeviceInventory = enableNonEssentialDeviceInventory,
      EnableSessionEmails = enableSessionEmails,
      DefaultScriptTimeouts = new DefaultScriptTimeouts(),
      ImmyScriptPath = immyScriptPath ?? Guid.NewGuid().ToString().Replace("-", ""),
      EnableBetaDynamicIntegrationMigrations = enableBetaDynamicIntegrationMigrations
    };

    using var dbContext = GetSqliteDbContext();
    dbContext.ApplicationPreferences.Add(pref);
    dbContext.SaveChanges();
    return pref;
  }

  protected TenantPreferences CreateTenantPreferences(
    int tenantId,
    DefaultEmailBccList? defaultEmailBccList = null,
    bool enableOnboarding = false,
    bool enableSessionEmails = false,
    bool enableOnboardPatching = false,
    bool excludeFromCrossTenantDeploymentsAndSchedules = false)
  {
    var pref = new TenantPreferences()
    {
      TenantId = tenantId,
      DefaultEmailBccList = defaultEmailBccList,
      EnableOnboarding = enableOnboarding,
      EnableSessionEmails = enableSessionEmails,
      EnableOnboardingPatching = enableOnboardPatching,
      ExcludeFromCrossTenantDeploymentsAndSchedules = excludeFromCrossTenantDeploymentsAndSchedules
    };

    using var dbContext = GetSqliteDbContext();
    dbContext.TenantPreferences.Add(pref);
    dbContext.SaveChanges();
    return pref;
  }

  protected SmtpConfig CreateSmtpConfig(
    int tenantId)
  {
    var smtp = new SmtpConfig { TenantId = tenantId, Host = "Foo" };
    using var dbContext = GetSqliteDbContext();
    dbContext.SmtpConfigs.Add(smtp);
    dbContext.SaveChanges();
    return smtp;
  }

  protected MaintenanceTask CreateLocalMaintenanceTask(
    string name = "Local Task",
    List<MaintenanceTaskParameter>? parameters = null,
    Script? SetScript = null,
    List<TenantMaintenanceTask>? tenants = null
  )
  {
    var task = new MaintenanceTask
    {
      Name = name,
      Parameters = parameters ?? [],
      DatabaseType = DatabaseType.Local,
      SetScript = SetScript,
      SetScriptId = SetScript?.Id,
      SetScriptType = SetScript?.ScriptType,
      TenantRelationships = tenants ?? [],
    };

    using var dbContext = GetSqliteDbContext();
    dbContext.MaintenanceTasks.Add(task);
    dbContext.SaveChanges();
    return task;
  }

  protected MaintenanceTask CreateGlobalMaintenanceTask(MaintenanceTask task)
  {
    using var ctx = GetSqliteSoftwareDbContext();
    task.DatabaseType = DatabaseType.Global;
    if (string.IsNullOrEmpty(task.Name)) task.Name = "Foobar";
    ctx.MaintenanceTasks.Add(task);
    ctx.SaveChanges();
    return task;
  }

  protected MaintenanceTask CreateGlobalMaintenanceTask(
    string name = "Global Task",
    List<MaintenanceTaskParameter>? parameters = null
  )
  {
    var task = new MaintenanceTask { Name = name, Parameters = parameters ?? [], DatabaseType = DatabaseType.Global };

    using var dbContext = GetSqliteSoftwareDbContext();
    dbContext.MaintenanceTasks.Add(task);
    dbContext.SaveChanges();
    return task;
  }


  protected Media CreateMedia(int? ownerTenantId = null, int? id = null)
  {
    var tenants = new List<TenantMedia>();
    if (ownerTenantId.HasValue)
      tenants.Add(new TenantMedia { TenantId = ownerTenantId.Value, Relationship = Relationship.Owned });

    var media = new Media
    {
      Id = id ?? 0,
      Name = "foo",
      FileName = "foo",
      BlobReference = "foo",
    };
    tenants.ForEach(t => media.TenantRelationships.Add(t));

    using var ctx = GetSqliteDbContext();
    ctx.Media.Add(media);
    ctx.SaveChanges();
    return media;
  }

  protected Media CreateGlobalMedia(int? id = null)
  {
    var media = new Media
    {
      Id = id ?? 1,
      Name = "foo",
      FileName = "foo",
      BlobReference = "foo",
    };

    using var ctx = GetSqliteSoftwareDbContext();
    ctx.Media.Add(media);
    ctx.SaveChanges();

    return media;
  }

  protected Branding CreateBranding(
    User createdBy,
    int? tenantId = null,
    DateTime? startDate = null,
    DateTime? endDate = null,
    bool? ignoreYear = null,
    string fromAddress = "test.immy.bot",
    string? mascotImgUri = null,
    string? mascotName = null,
    string? logoUri = null,
    string? logoAltText = null,
    string backgroundColor = "#000000",
    string foregroundColor = "#000000",
    string tableHeaderColor = "#000000",
    string? description = null)
  {
    var branding = new Branding()
    {
      TenantId = tenantId,
      StartDate = startDate,
      EndDate = endDate,
      IgnoreYear = ignoreYear,
      FromAddress = fromAddress,
      MascotImgUri = mascotImgUri,
      MascotName = mascotName,
      LogoUri = logoUri,
      LogoAltText = logoAltText,
      BackgroundColor = backgroundColor,
      ForegroundColor = foregroundColor,
      TableHeaderColor = tableHeaderColor,
      Description = description ?? "some description"
    };

    using var dbContext = GetSqliteDbContext();
    var authUser = GetAuthUser(createdBy);
    dbContext.SetUser(authUser);
    dbContext.Brandings.Add(branding);
    dbContext.SaveChanges();
    return branding;
  }

  protected MaintenanceSession CreateSession(
    int? computerId = null,
    int? tenantId = null,
    int? scheduleId = null,
    SessionStatus sessionStatus = SessionStatus.Created,
    string? jobId = null,
    bool onboarding = false,
    bool rerunningAction = false,
    DateTime? scheduledExecutionDate = null,
    EnqueuedSessionType? enqueuedSessionType = null,
    ImmybotDbContext? ctx = null,
    int? personId = null)
  {
    var session = new MaintenanceSession()
    {
      ComputerId = computerId,
      TenantId = tenantId,
      PersonId = personId,
      ScheduledId = scheduleId,
      SessionStatus = sessionStatus,
      JobId = jobId,
      Onboarding = onboarding,
      ScheduledExecutionDate = scheduledExecutionDate.HasValue ? scheduledExecutionDate.Value : DateTime.UtcNow,
      JobArgs = new SessionJobArgs { RerunningAction = rerunningAction, EnqueuedSessionType = enqueuedSessionType },
    };

    session.MaintenanceActions.Add(new MaintenanceAction
    {
      MaintenanceIdentifier = "test",
      ComputerId = computerId,
      MaintenanceType = MaintenanceType.LocalSoftware,
      TenantId = tenantId
    });

    session.Stages.Add(new() { Type = SessionStageType.Onboarding });
    session.Stages.Add(new() { Type = SessionStageType.Resolution });
    session.Stages.Add(new() { Type = SessionStageType.Detection });
    session.Stages.Add(new() { Type = SessionStageType.Execution });

    ctx ??= GetSqliteDbContext();
    ctx.MaintenanceSessions.Add(session);
    ctx.SaveChanges();
    return session;
  }

  protected ActiveSession CreateActiveSession(
    int sessionId,
    SessionStatus sessionStatus)
  {
    var activeSession = new ActiveSession() { MaintenanceSessionId = sessionId, SessionStatus = sessionStatus };

    using var dbContext = GetSqliteDbContext();
    dbContext.ActiveSessions.Add(activeSession);
    dbContext.SaveChanges();
    return activeSession;
  }

  protected Role CreateRole(string name)
  {
    var roleType = GetOrCreateCustomRoleType();
    var role = new Role { Name = name, Description = "This is a test role", RoleTypeId = roleType.Id };

    using var dbContext = GetSqliteDbContext();
    dbContext.Roles.Add(role);
    dbContext.SaveChanges();
    return role;
  }

  protected RoleType GetOrCreateCustomRoleType()
  {
    using var dbContext = GetSqliteDbContext();
    var roleType = dbContext.RoleTypes
      .AsNoTracking()
      .FirstOrDefault(a => a.Name == "Custom");

    if (roleType is not null) return roleType;

    roleType = new RoleType { Name = "Custom", Id = RoleType.Custom };
    dbContext.RoleTypes.Add(roleType);
    dbContext.SaveChanges();
    return roleType;
  }

  protected License CreateLicense(
    string name,
    string softwareIdentifier,
    SoftwareType softwareType,
    string licenseValue,
    LicenseType licenseType,
    SemanticVersion? version,
    int? tenantId)
  {
    if (name is null)
    {
      throw new ArgumentNullException(nameof(name));
    }

    if (softwareIdentifier is null)
    {
      throw new ArgumentNullException(nameof(softwareIdentifier));
    }

    if (licenseValue is null)
    {
      throw new ArgumentNullException(nameof(licenseValue));
    }

    var license = new License()
    {
      Name = name,
      SoftwareIdentifier = softwareIdentifier,
      SoftwareName = softwareIdentifier,
      SoftwareType = softwareType,
      LicenseValue = licenseValue,
      SemanticVersion = version,
      TenantId = tenantId,
    };

    using var dbContext = GetSqliteDbContext();
    dbContext.Licenses.Add(license);
    dbContext.SaveChanges();
    return license;
  }

  protected AccessRequest CreateAccessRequest(int personId)
  {
    var ar = new AccessRequest { PersonId = personId, DateRequestedUTC = DateTime.UtcNow, };

    using var dbContext = GetSqliteDbContext();
    dbContext.AccessRequests.Add(ar);
    dbContext.SaveChanges();
    dbContext.Entry(ar).State = EntityState.Detached;
    return ar;
  }

  private static void AddZzzEfExtensionsLicense()
  {
    var builder = new HostApplicationBuilder(new HostApplicationBuilderSettings
    {
      EnvironmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development"
    });
    builder.AddJsonFilesConfiguration(reloadOnChange: false);
    builder.Configuration.AddEnvironmentVariables();
    var opts = new ZEntityFrameworkExtensionsOptions();

    builder.Configuration
      .GetSection(ZEntityFrameworkExtensionsOptions.SectionKey)
      .Bind(opts);

    // Add the entity framework extensions license from appsettings file
    if (string.IsNullOrEmpty(opts.LicenseName) || string.IsNullOrEmpty(opts.LicenseKey)) return;

    Z.EntityFramework.Extensions.LicenseManager.AddLicense(opts.LicenseName, opts.LicenseKey);

    if (!Z.EntityFramework.Extensions.LicenseManager.ValidateLicense(out var licenseErrorMessage))
    {
      throw new Exception(licenseErrorMessage);
    }
  }

  public static ILoggerFactory MockLoggerFactory()
  {
    var loggerFactory = new Mock<ILoggerFactory>();
    var loggerMock = new Mock<ILogger>();
    loggerFactory.Setup(a => a.CreateLogger(It.IsAny<string>())).Returns(loggerMock.Object);
    return loggerFactory.Object;
  }

  /// <summary>
  /// Returns a host with mocked services to be used in unit tests.
  /// </summary>
  /// <returns></returns>
  protected IHost CreateHost(Action<HostApplicationBuilder>? configure = null, IInterceptor[]? dbInterceptors = null)
  {
    var builder = Host.CreateApplicationBuilder();
    Environment.SetEnvironmentVariable("DOTNET_USE_POLLING_FILE_WATCHER", "1");

    var userService = new Mock<IUserService>();
    var localSoftwareActions = new Mock<ILocalSoftwareActions>();
    var providerActions = new Mock<IProviderActions>();
    var featureManager = new Mock<IFeatureManager>();
    var appPrefs = new CachedSingleton<ApplicationPreferences> { Value = new ApplicationPreferences() };
    var tenantPrefs = new CachedCollection<TenantPreferences> { Value = new List<TenantPreferences>() };
    var pendoEventManagementService = new Mock<IPendoEventManagementService>();
    var policyCacheStore = new Mock<IPolicyCacheStore>();
    var policyRegistry = new Mock<IPolicyRegistry<string>>();
    var userImpersonationStore = new Mock<IUserImpersonationStore>();

    builder.Services.AddSingleton<ApplicationSieveProcessor>();
    builder.Services.AddSingleton((_) => pendoEventManagementService.Object);
    builder.Services.AddTransient<IUserService>((_) => userService.Object);
    builder.Services.AddTransient<ILocalSoftwareActions>((_) => localSoftwareActions.Object);
    builder.Services.AddTransient<IProviderActions>((_) => providerActions.Object);
    builder.Services.AddSingleton<IFeatureManager>((_) => featureManager.Object);
    builder.Services.AddSingleton<IFeatureTracker>((_) => featureManager.Object);
    builder.Services.AddSingleton<ICachedSingleton<ApplicationPreferences>>(appPrefs);
    builder.Services.AddSingleton<ICachedCollection<TenantPreferences>>(tenantPrefs);
    builder.Services.AddOptions<AppSettingsOptions>();
    builder.Services.AddOptions<AzureActiveDirectoryAuthOptions>();
    builder.Services.AddSingleton(_ => policyCacheStore.Object);
    builder.Services.AddTransient(_ => userImpersonationStore.Object);
    builder.Services.AddSingleton(_ => policyRegistry.Object);
    builder.Services.AddSingleton(Mock.Of<IOauthHooksRepository>());
    builder.Services.AddSingleton(Mock.Of<IProviderActions>());
    builder.Services.AddSingleton(Mock.Of<IAgentHubSessionCache>());
    builder.Services.AddSingleton(Mock.Of<IManagerProvidedSettings>());
    builder.Services.AddSingleton(Mock.Of<IEphemeralAgentAcquisition>());
    builder.Services.AddSingleton(Mock.Of<IHubContext<AgentHub, IAgentHubClient>>());
    builder.Services.AddSingleton(Mock.Of<IWebHostEnvironment>());
    builder.Services.AddSingleton(Mock.Of<IUserHubDatabaseActions>());
    builder.Services.AddSingleton(Mock.Of<IDomainEventReceiver>());
    builder.Services.AddSingleton(Mock.Of<IDomainEventEmitter>());
    builder.Services.AddSingleton(Mock.Of<IResourceAuthorizerFlow>());
    builder.Services.AddHttpContextAccessor();

    // db setup
    builder.Services.AddTransient<SoftwareDbContext>(_ => GetSqliteSoftwareDbContext());
    builder.Services.AddSingleton<Func<SoftwareDbContext>>(_ => GetSqliteSoftwareDbContextFactory());
    builder.Services.AddTransient<ImmybotDbContext>(_ => GetSqliteDbContext(interceptors: dbInterceptors ?? []));
    builder.Services.AddSingleton<Func<ImmybotDbContext>>(_ => GetSqliteDbContextFactory(interceptors: dbInterceptors));
    var ctxFactory = new Mock<IDbContextFactory<ImmybotDbContext>>();
    ctxFactory.Setup(a => a.CreateDbContext()).Returns(() => GetNewSqliteDbContext(interceptors: dbInterceptors ?? []));
    ctxFactory.Setup(a => a.CreateDbContextAsync(It.IsAny<CancellationToken>()))
      .ReturnsAsync(() => GetNewSqliteDbContext(interceptors: dbInterceptors ?? []));
    builder.Services.AddSingleton(ctxFactory.Object);
    builder.Services.AddScoped<UserBearingDbFactory<ImmybotDbContext>>(serviceProvider =>
    {
      var us = serviceProvider.GetRequiredService<IUserService>();
      return () =>
      {
        var ctxFactory = serviceProvider.GetRequiredService<Func<ImmybotDbContext>>();
        var ctx = ctxFactory();
        if (us.TryGetCurrentUser(out var user)) ctx.SetUser(user);
        else if (us.GetCurrentPrincipal(strict: false) is { } principal)
          ctx.SetUser(userDisplayName: principal.GetDisplayName(),
            userAzureId: principal.GetObjectId());
        return ctx;
      };
    });
    builder.Services.AddScoped<UserBearingDbFactory<SoftwareDbContext>>(serviceProvider =>
    {
      var us = serviceProvider.GetRequiredService<IUserService>();
      return () =>
      {
        var ctxFactory = serviceProvider.GetRequiredService<Func<SoftwareDbContext>>();
        var ctx = ctxFactory();
        if (us.TryGetCurrentUser(out var user)) ctx.SetUser(user);
        else if (us.GetCurrentPrincipal(strict: false) is { } principal)
          ctx.SetUser(userDisplayName: principal.GetDisplayName(),
            userAzureId: principal.GetObjectId());
        return ctx;
      };
    });

    builder.Services.AddRouting();
    builder.Services.AddAuthorization();
    builder.Services.AddAuthorizationBuilder();
    builder.Services.AddRBACServices();
    builder.Services.AddTransient<IResourceAuthorizer>(_ => Mock.Of<IResourceAuthorizer>());

    configure?.Invoke(builder);

    return builder.Build();
  }

  public static AuthUserDto GetAuthUser(User user)
  {
    return new AuthUserDto
    {
      Id = user.Id,
      TenantId = user.Person != null ? user.Person.TenantId : user.TenantId,
      IsMsp = user.Tenant?.IsMsp ?? false,
      IsAdmin = user.IsAdmin,
      PersonId = user.PersonId,
      IsSupportTechnician = user.IsSupportTechnician,
      HasManagementAccess = user.HasManagementAccess,
      CanManageCrossTenantDeployments = user.CanManageCrossTenantDeployments,
      ServicePrincipalId = user.ServicePrincipalId,
      FirstName = user.Person?.FirstName,
      LastName = user.Person?.LastName,
      TenantName = user.Tenant?.Name,
      DisplayName = user.DisplayName,
      PrincipalId = user.Person?.AzurePrincipalId,
      Email = user.Person?.EmailAddress,
      ExpirationDateUtc = user.ExpirationDateUTC,
      Roles = []
    };
  }
}
