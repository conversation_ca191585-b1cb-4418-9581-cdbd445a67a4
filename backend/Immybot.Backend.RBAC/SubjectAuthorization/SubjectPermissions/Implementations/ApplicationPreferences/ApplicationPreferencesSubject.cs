using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.ApplicationPreferences;

/// <summary>
/// Subject definition for Application Preferences.
/// </summary>
public class ApplicationPreferencesSubject : SubjectMetadata<IApplicationPreferencesSubjectPermission>, IApplicationPreferencesSubject
{
  /// <summary>
  /// Initializes a new instance of the <see cref="ApplicationPreferencesSubject"/> class.
  /// </summary>
  /// <param name="permissionFactory">Factory delegate that creates the permissions for this subject.</param>
  public ApplicationPreferencesSubject(PermissionFactory<IApplicationPreferencesSubjectPermission> permissionFactory)
    : base(permissionFactory)
  {
  }

  /// <inheritdoc/>
  public override string Name => "application_preferences";

  /// <inheritdoc/>
  public override string DisplayName => "Application Preferences";

  /// <inheritdoc/>
  public override string Description => "Configure application-specific settings and preferences";

  /// <inheritdoc/>
  public override int SortOrder => 30;
}
