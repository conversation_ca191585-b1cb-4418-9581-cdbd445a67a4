using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;

/// <summary>
/// Permission to override deployment settings.
/// </summary>
public class DeploymentsOverridePermission(SubjectFactory<DeploymentsSubject> subjectFactory)
  : PermissionMetadata(subjectFactory), IDeploymentsOverridePermission
{
  /// <inheritdoc/>
  public override string PermissionName => "override";

  /// <inheritdoc/>
  public override string DisplayName => "Override Deployment Settings";

  /// <inheritdoc/>
  public override string Description => "Override deployment settings during maintenance sessions";

  /// <inheritdoc/>
  public override PermissionCategory Category => PermissionCategory.Advanced;
}
