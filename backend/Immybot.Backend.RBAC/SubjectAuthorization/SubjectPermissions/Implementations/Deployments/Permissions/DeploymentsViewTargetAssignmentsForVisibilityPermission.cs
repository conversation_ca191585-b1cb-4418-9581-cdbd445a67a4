using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;

/// <summary>
/// Permission to view target assignments for the given visibility
/// </summary>
public class DeploymentsViewTargetAssignmentsForVisibilityPermission(
  SubjectFactory<DeploymentsSubject> subjectFactory)
  : PermissionMetadata(subjectFactory), IDeploymentsViewTargetAssignmentsForVisibilityPermission
{
  /// <inheritdoc/>
  public override string PermissionName => "view_deployments_for_visibility";

  /// <inheritdoc/>
  public override string DisplayName => "View deployments for self-service or technician pod";

  /// <inheritdoc/>
  public override string Description => "Used in conjunction with the self-service or technician pod feature";

  /// <inheritdoc/>
  public override PermissionCategory Category => PermissionCategory.View;

  public override bool IsMspOnly => true;
}
