using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Computers.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Licenses.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.MaintenanceTasks.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Persons.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Software.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Tags.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Tenants.Permissions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;

/// <summary>
/// Permission to view individual scoped deployments.
/// </summary>
public class DeploymentsViewIndividualPermission(
  SubjectFactory<DeploymentsSubject> subjectFactory,
  SoftwareViewPermission softwareViewPermission,
  MaintenanceTasksViewPermission maintenanceTasksViewPermission,
  TenantsViewPermission tenantsViewPermission,
  ComputersViewPermission computersViewPermission,
  LicensesViewPermission licensesViewPermission,
  TagsViewPermission tagsViewPermission,
  PersonsViewPermission personsViewPermission)
  : PermissionMetadata(subjectFactory), IDeploymentsViewIndividualPermission
{
  /// <inheritdoc/>
  public override string PermissionName => "view_individual";

  /// <inheritdoc/>
  public override string DisplayName => "View Individual Scoped Deployments";

  /// <inheritdoc/>
  public override string Description => "View deployments targeting specific people or computers";

  public override IEnumerable<IPermissionMetadata> Dependencies =>
  [
    softwareViewPermission,
    maintenanceTasksViewPermission,
    tenantsViewPermission,
    computersViewPermission,
    licensesViewPermission,
    tagsViewPermission,
    personsViewPermission
  ];
}
