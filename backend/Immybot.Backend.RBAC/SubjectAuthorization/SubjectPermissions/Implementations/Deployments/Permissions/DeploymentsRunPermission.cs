using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;

/// <summary>
/// Permission to run deployments.
/// </summary>
public class DeploymentsRunPermission : PermissionMetadata, IDeploymentsRunPermission
{
  /// <summary>
  /// Initializes a new instance of the <see cref="DeploymentsRunPermission"/> class.
  /// </summary>
  /// <param name="subjectFactory">Factory to retrieve the deployments subject.</param>
  public DeploymentsRunPermission(
    SubjectFactory<DeploymentsSubject> subjectFactory)
      : base(subjectFactory)
  {
  }

  /// <inheritdoc/>
  public override string PermissionName => "run";

  /// <inheritdoc/>
  public override string DisplayName => "Run Deployments";

  /// <inheritdoc/>
  public override PermissionCategory Category => PermissionCategory.CoreCapability;
}
