using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;

/// <summary>
/// Permission to supersede deployment settings.
/// </summary>
public class DeploymentsSupersedePermission(SubjectFactory<DeploymentsSubject> subjectFactory)
  : PermissionMetadata(subjectFactory), IDeploymentsSupersedePermission
{
  /// <inheritdoc/>
  public override string PermissionName => "supersede";

  /// <inheritdoc/>
  public override string DisplayName => "Supersede Deployment Settings";

  /// <inheritdoc/>
  public override string Description => "Supersede deployment settings with custom values";

  /// <inheritdoc/>
  public override PermissionCategory Category => PermissionCategory.Advanced;

  /// <inheritdoc/>
  public override bool HasGreatPower => true;
}
