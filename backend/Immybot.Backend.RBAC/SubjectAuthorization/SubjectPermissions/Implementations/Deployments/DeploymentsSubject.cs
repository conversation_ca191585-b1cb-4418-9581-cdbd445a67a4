using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Delegates;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Abstractions;

namespace Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments;

/// <summary>
/// Subject definition for Deployments.
/// </summary>
public class DeploymentsSubject : SubjectMetadata<IDeploymentsSubjectPermission>, IDeploymentsSubject
{
  /// <summary>
  /// Initializes a new instance of the <see cref="DeploymentsSubject"/> class.
  /// </summary>
  /// <param name="permissionFactory">Factory delegate that creates the permissions for this subject.</param>
  public DeploymentsSubject(PermissionFactory<IDeploymentsSubjectPermission> permissionFactory)
      : base(permissionFactory)
  { }

  /// <inheritdoc/>
  public override string Name => "deployments";

  /// <inheritdoc/>
  public override string DisplayName => "Deployments";

  /// <inheritdoc/>
  public override string Description => "Manage deployment configurations";

  /// <inheritdoc/>
  public override int SortOrder => 10;
}
