# SubjectAuthorization

## Table of Contents
- [Context](#context)
- [Summary](#summary)
- [Architecture](#architecture)
- [Components](#components)
- [File Organization](#file-organization)
- [Additional Information](#additional-information)
- [Extending this Feature](#extending-this-feature)

## Context

Role-Based Access Control (RBAC) systems manage user permissions by assigning roles containing permission collections, evaluated during authorization requests. In ASP.NET Core applications, authorization uses policies and handlers to determine user actions. This SubjectAuthorization feature extends standard ASP.NET Core authorization through a metadata-driven approach where business domains (subjects) and actions (permissions) are strongly-typed classes rather than string-based policies. This leverages .NET's dependency injection and factory patterns for compile-time safety, automatic discovery, and extensible authorization logic. The feature addresses ImmyBot's complex multi-tenant authorization requirements across dozens of functional areas requiring different permission granularities and dependency relationships.

## Summary

The SubjectAuthorization feature implements a comprehensive metadata-driven RBAC system for ImmyBot. It provides a flexible framework for defining subjects (functional areas like Deployments, Computers, Users) and their associated permissions, enabling fine-grained access control throughout the application. The system operates through three core layers: a definition layer declaring subjects and permissions, a metadata layer providing discovery capabilities, and an authorization layer enforcing access policies. Currently operational and actively used across ImmyBot's backend, this feature solves complex authorization requirements across multiple business domains by providing consistent, type-safe, declarative approach where permissions are automatically discovered and registered through reflection-based dependency injection.

## Architecture

```mermaid
graph TB
    subgraph "Authorization Layer"
        AuthService[ISubjectPermissionAuthorizationService]
        AuthHandler[SubjectPermissionAuthorizationHandler]
        AuthRequirement[SubjectPermissionAuthorizationRequirement]
        AuthAttribute[SubjectPermissionAuthorizeAttribute]
    end
    
    subgraph "Metadata Layer"
        MetadataService[ISubjectPermissionMetadataService]
        PermissionProvider[IPermissionProvider]
    end
    
    subgraph "Definition Layer"
        SubjectBase[SubjectMetadata<T>]
        PermissionBase[PermissionMetadata]
        ConcreteSubjects[Concrete Subjects]
        ConcretePermissions[Concrete Permissions]
    end
    
    subgraph "Registration Layer"
        ServiceRegistry[SubjectPermissionsServiceRegistration]
        Factories[Permission & Subject Factories]
    end
    
    AuthService --> AuthHandler
    AuthHandler --> MetadataService
    AuthHandler --> PermissionProvider
    AuthAttribute --> AuthService
    
    MetadataService --> ConcreteSubjects
    PermissionProvider --> ConcretePermissions
    
    ConcreteSubjects --> SubjectBase
    ConcretePermissions --> PermissionBase
    
    ServiceRegistry --> Factories
    Factories --> ConcreteSubjects
    Factories --> ConcretePermissions
```

This architecture enables automatic discovery of subjects and permissions through reflection, type-safe permission references, and consistent claim-based authorization evaluation across all ImmyBot features.

## Components

1. **[`SubjectPermissions/Abstractions/SubjectMetadata.cs`](SubjectPermissions/Abstractions/SubjectMetadata.cs)** - Base class for all subject definitions
   
   Provides common functionality for subjects including lazy-loaded permissions via factory patterns, automatic ID generation, and default property implementations. Each subject inherits from `SubjectMetadata<T>` where T represents the permission interface type, enabling type-safe permission discovery and strongly-typed relationships between subjects and their permissions.

2. **[`SubjectPermissions/Abstractions/PermissionMetadata.cs`](SubjectPermissions/Abstractions/PermissionMetadata.cs)** - Base class for all permission definitions
   
   Implements common permission behavior including claim generation (subject:permission:allow/deny format), dependency management, and subject relationship handling. Supports lazy-loaded subject references through factory delegates and provides default categorization, sorting, and metadata properties that can be overridden in concrete implementations.

3. **[`Authorization/Implementations/SubjectPermissionAuthorizationService.cs`](Authorization/Implementations/SubjectPermissionAuthorizationService.cs)** - Core authorization service
   
   Provides type-safe authorization methods for global, tenant-scoped, and resource-based permission checks. Integrates with ASP.NET Core's IAuthorizationService to evaluate permissions against user claims, supporting both imperative authorization calls and declarative attribute-based authorization throughout the application.

4. **[`Authorization/Implementations/AuthorizationHandlers/SubjectPermissionAuthorizationHandler.cs`](Authorization/Implementations/AuthorizationHandlers/SubjectPermissionAuthorizationHandler.cs)** - ASP.NET Core authorization handler
   
   Evaluates authorization requirements by checking user claims against permission requirements, handling deny-first logic where explicit deny claims override allow claims. Recursively evaluates permission dependencies and includes special handling for MSP-only permissions and no-authorization scenarios, with comprehensive telemetry integration for authorization audit trails.

5. **[`Metadata/Implementations/SubjectPermissionMetadataService.cs`](Metadata/Implementations/SubjectPermissionMetadataService.cs)** - Metadata discovery and querying service
   
   Provides cached access to all registered subjects and permissions, enabling runtime queries by subject name or permission name. Uses memory caching to optimize performance and supports the administrative interfaces that display available permissions and subjects to users during role configuration.

6. **[`SubjectPermissions/Extensions/SubjectPermissionsServiceRegistration.cs`](SubjectPermissions/Extensions/SubjectPermissionsServiceRegistration.cs)** - Dependency injection orchestration
   
   Orchestrates the complex registration of all subjects, permissions, and their factory dependencies using reflection to discover implementations across assemblies. Handles the three-phase registration process: concrete implementations first, then interface registrations, and finally collection registrations, ensuring proper dependency resolution for the factory pattern architecture.

7. **[`SubjectPermissions/Implementations/PermissionProvider/PermissionProvider.cs`](SubjectPermissions/Implementations/PermissionProvider/PermissionProvider.cs)** - Permission lookup service
   
   Provides high-performance cached lookups of permission metadata by type, supporting both concrete types and interface types. Builds a comprehensive cache during initialization that maps all permission types to their instances, enabling fast resolution during authorization operations.

## File Organization

**SubjectAuthorization/** - Feature root directory
- **[Authorization/](Authorization/)** - Runtime authorization engine
  - **[Extensions/](Authorization/Extensions/)** - Service registration for authorization components
  - **[Implementations/](Authorization/Implementations/)** - Core authorization logic
    - [`SubjectPermissionAuthorizationService.cs`](Authorization/Implementations/SubjectPermissionAuthorizationService.cs) - Main authorization service
    - **[AuthorizationAttributes/](Authorization/Implementations/AuthorizationAttributes/)** - Controller attributes for declarative authorization
    - **[AuthorizationHandlers/](Authorization/Implementations/AuthorizationHandlers/)** - ASP.NET Core policy handlers
    - **[AuthorizationRequirements/](Authorization/Implementations/AuthorizationRequirements/)** - Authorization requirement definitions

- **[Metadata/](Metadata/)** - Permission metadata services
  - **[Extensions/](Metadata/Extensions/)** - Service registration for metadata services
  - **[Implementations/](Metadata/Implementations/)** - Metadata service implementations

- **[SubjectPermissions/](SubjectPermissions/)** - Permission and subject definitions
  - **[Abstractions/](SubjectPermissions/Abstractions/)** - Base classes providing common functionality
  - **[Extensions/](SubjectPermissions/Extensions/)** - Service registration with automatic discovery
  - **[Implementations/](SubjectPermissions/Implementations/)** - Concrete implementations organized by business domain
    - **[SubjectName/](SubjectPermissions/Implementations/)** - Each business area (Deployments, Computers, etc.)
      - `[SubjectName]Subject.cs` - Subject definition with metadata
      - **Permissions/** - All permissions for this subject
        - `[Subject][Action]Permission.cs` - Individual permission definitions

## Additional Information

### Subject and Permission Examples
ImmyBot implements dozens of functional areas as subjects including **Computers** (view, manage, remote control), **Software** (view, create, update, delete), **Deployments** (view, create, execute, cancel), **Users** (view, create, impersonate, delete), **Scripts** (view, develop, execute), **MaintenanceTasks** (view, create, schedule), **Licenses** (view, manage, assign), **Tenants** (view, create, configure), **Reports** (view, generate, export), and **Integrations** (configure, manage, sync). Each subject can have 5-15 specific permissions with automatic dependency resolution.

### Reflection-Based Discovery
The system uses assembly scanning to automatically discover all classes implementing [`ISubjectMetadata`](SubjectPermissions/Abstractions/ISubjectMetadata.cs) and [`IPermissionMetadata`](SubjectPermissions/Abstractions/IPermissionMetadata.cs) interfaces. The [`SubjectPermissionsServiceRegistration`](SubjectPermissions/Extensions/SubjectPermissionsServiceRegistration.cs) performs three-phase registration: concrete implementations first, interface mappings second, and collection registrations third. This ensures proper dependency injection resolution for the complex factory pattern architecture.

### Factory Pattern Implementation
The factory pattern enables lazy loading of permission collections and resolves circular dependencies between subjects and permissions. Subjects receive `Func<IEnumerable<IPermissionMetadata>>` factories that are resolved at runtime, preventing initialization order issues. This pattern provides performance benefits by deferring expensive reflection operations until permissions are actually accessed, typically during authorization evaluation or administrative UI rendering.

### Implementation Patterns
New subjects should inherit from [`SubjectMetadata<TPermissionInterface>`](SubjectPermissions/Abstractions/SubjectMetadata.cs) and define a corresponding permission interface (e.g., `IComputerPermission`). Permissions implement both the subject-specific interface and [`IPermissionMetadata`](SubjectPermissions/Abstractions/IPermissionMetadata.cs), enabling type-safe relationships. Use the `[SubjectPermissionAuthorize(typeof(SpecificPermission))]` attribute for declarative authorization on controllers and actions, or inject [`ISubjectPermissionAuthorizationService`](Authorization/Implementations/SubjectPermissionAuthorizationService.cs) for imperative checks.

## Extending this Feature

For adding new components or extending functionality, refer to:
- **[DDD Instructions](../../../.instructions/common/ddd.md)** - Domain-driven design patterns
- **[Feature Instructions](../../../.instructions/common/feature.md)** - Implementation guide
