using System.Diagnostics;
using System.Security.Claims;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationRequirements;
using Immybot.Backend.RBAC.Telemetry;
using Immybot.Shared.Telemetry;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Implementations.AuthorizationHandlers;

/// <summary>
/// Handles authorization of subject-based permissions by checking for deny claims first
/// (which override allow claims) and then checking for allow claims. Succeeds if allow
/// claim exists and no deny claim exists. Also enforces permission dependencies.
/// </summary>
public class SubjectPermissionAuthorizationHandler : AuthorizationHandler<SubjectPermissionAuthorizationRequirement>
{
  private readonly ILogger<SubjectPermissionAuthorizationHandler>? _logger;

  /// <summary>
  /// Creates a new instance of the handler.
  /// </summary>
  /// <param name="logger">Optional logger for diagnostic information.</param>
  public SubjectPermissionAuthorizationHandler(
    ILogger<SubjectPermissionAuthorizationHandler>? logger = null)
  {
    _logger = logger;
  }

  /// <inheritdoc/>
  protected override async Task HandleRequirementAsync(
    AuthorizationHandlerContext context,
    SubjectPermissionAuthorizationRequirement requirement)
  {
    try
    {
      using var _ = Activity.Current?.StartEvent(TelemetryEvent.HandleSubjectRequirement);

      Activity.Current?.AddTag(TelemetryTag.UserId,
        context.User.GetClaimValue(ClaimConstants.ImmyUserId));
      Activity.Current?.AddTag(TelemetryTag.TenantId,
        context.User.GetClaimValue(ClaimConstants.ImmyTenantId));

      List<(bool granted, string reason)> grantedResults = [];

      foreach (var permission in requirement.AnyPermissions)
      {
        Activity.Current?.AddTag(TelemetryTag.Subject, permission.Subject.Name);
        Activity.Current?.AddTag(TelemetryTag.Permission, permission.DisplayName);

        // handle special no authorization subject
        if (permission is INoAuthorizationPermission)
        {
          Activity.Current?.AddTag(TelemetryTag.Result, PermissionResult.NoSubjectAuthorizationRequired);

          context.Succeed(requirement);
          return;
        }

        // handle msp only permissions
        if (permission.IsMspOnly && !context.User.HasClaim(a =>
              a.Type == ClaimConstants.IsMsp && string.Equals(a.Value, "true", StringComparison.OrdinalIgnoreCase)))
        {
          Activity.Current?.AddTag(TelemetryTag.Result, PermissionResult.SubjectPermissionIsMspOnly);

          grantedResults.Add((false, $"The permission '{permission.DisplayName}' is only available to MSP users"));
          continue;
        }

        // Check for deny claim first
        if (context.User.HasClaim(c => c.Value == permission.DenyClaim))
        {
          Activity.Current?.AddTag(TelemetryTag.Result, PermissionResult.SubjectExplicitlyDenied);

          grantedResults.Add((false,
            $"The permission '{permission.DisplayName}' is explicitly denied by a deny claim"));
          continue;
        }

        // Check for allow claim
        var hasDirectPermission = context.User.HasClaim(c => c.Value == permission.AllowClaim);

        if (!hasDirectPermission)
        {
          grantedResults.Add((false, $"The permission '{permission.DisplayName}' is not granted by any allow claim"));
          continue;
        }

        // Check all dependencies recursively
        if (!CheckDependencies(context.User, permission))
        {
          Activity.Current?.AddTag(TelemetryTag.Result, PermissionResult.DependentSubjectNotGranted);

          grantedResults.Add(
            (false, $"The permission '{permission.DisplayName}' has dependencies that are not granted"));
          continue;
        }

        // If we reach here, the user has the permission
        Activity.Current?.AddTag(TelemetryTag.Result, PermissionResult.SubjectImplicitlyAllowed);
        context.Succeed(requirement);
        return;
      }

      // we succeed if at least one permission was granted
      if (grantedResults.Any(r => r.granted))
      {
        context.Succeed(requirement);
        return;
      }

      // if we reach here, no permissions were granted
      var reasons = grantedResults
        .Where(a => !a.granted)
        .Select(r => r.reason)
        .Distinct()
        .ToList();
      context.Fail(new AuthorizationFailureReason(this, string.Join(", ", reasons)));
    }
    catch (Exception ex)
    {
      _logger?.LogError(ex, "Error handling authorization requirement");
      context.Fail(new AuthorizationFailureReason(this, ex.Message));
    }
  }

  private bool CheckDependencies(ClaimsPrincipal user, IPermissionMetadata permission)
  {
    foreach (var dependency in permission.Dependencies)
    {
      // Check if the user has the dependency
      if (!user.HasClaim(c => c.Value == dependency.AllowClaim))
        return false;

      // Recursively check dependencies of this dependency
      if (!CheckDependencies(user, dependency))
        return false;
    }

    return true;
  }
}
