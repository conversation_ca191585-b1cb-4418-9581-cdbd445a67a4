using System.Collections.Generic;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Models
{
    /// <summary>
    /// Contains pre-evaluated permission information for building SQL expressions.
    /// This data is extracted from user claims and transformed into concrete values
    /// that can be used in Entity Framework queries.
    /// </summary>
    public sealed class EvaluatedPermissions
    {
        /// <summary>
        /// Whether the user has access to all tenants via wildcard claims.
        /// </summary>
        public bool HasAllTenantAccess { get; set; }

        /// <summary>
        /// Set of tenant IDs that the user is explicitly allowed to access.
        /// </summary>
        public HashSet<int> AllowedTenantIds { get; } = new();

        /// <summary>
        /// Set of tenant IDs that the user is explicitly denied access to.
        /// </summary>
        public HashSet<int> DeniedTenantIds { get; } = new();

        /// <summary>
        /// Set of resource keys (typically IDs) that the user is explicitly allowed to access.
        /// </summary>
        public HashSet<string> AllowedResourceKeys { get; } = new();

        /// <summary>
        /// Set of resource keys (typically IDs) that the user is explicitly denied access to.
        /// </summary>
        public HashSet<string> DeniedResourceKeys { get; } = new();
    }
}