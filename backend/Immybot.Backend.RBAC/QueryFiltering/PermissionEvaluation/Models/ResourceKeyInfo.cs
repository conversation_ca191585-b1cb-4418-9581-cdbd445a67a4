using System;
using System.Linq.Expressions;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Models
{
    /// <summary>
    /// Contains resource key expression information including the actual key type.
    /// This allows providers to return type-safe expressions for optimal SQL generation.
    /// </summary>
    /// <typeparam name="TResource">The resource type.</typeparam>
    public class ResourceKeyInfo<TResource>
        where TResource : class
    {
        /// <summary>
        /// The expression to extract the resource key. The return type matches KeyType.
        /// </summary>
        public LambdaExpression KeyExpression { get; set; } = null!;

        /// <summary>
        /// The actual type of the resource key (e.g., int, string, Guid).
        /// </summary>
        public Type KeyType { get; set; } = null!;

        /// <summary>
        /// Creates a ResourceKeyInfo for integer keys.
        /// </summary>
        public static ResourceKeyInfo<TResource> ForInteger(Expression<Func<TResource, int>> expression)
        {
            return new ResourceKeyInfo<TResource>
            {
                KeyExpression = expression,
                KeyType = typeof(int)
            };
        }

        /// <summary>
        /// Creates a ResourceKeyInfo for string keys.
        /// </summary>
        public static ResourceKeyInfo<TResource> ForString(Expression<Func<TResource, string>> expression)
        {
            return new ResourceKeyInfo<TResource>
            {
                KeyExpression = expression,
                KeyType = typeof(string)
            };
        }
    }
}