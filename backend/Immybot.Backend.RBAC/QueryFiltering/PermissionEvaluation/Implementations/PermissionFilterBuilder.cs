using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Claims;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Models;
using Microsoft.Extensions.Logging;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations
{
    /// <summary>
    /// Builds SQL-translatable filter expressions for resource authorization using permission metadata.
    /// Implements the same claim precedence hierarchy as ResourceAuthorization.
    /// Pre-evaluates user permissions to generate database-level filtering.
    /// </summary>
    public class PermissionFilterBuilder(
        IUserService userService,
        IResourceClaimProvider claimProvider,
        IServiceProvider serviceProvider) : IPermissionFilterBuilder
    {
        /// <summary>
        /// Creates a SQL-translatable filter expression for the specified resource type using permission metadata.
        /// Pre-evaluates all user permissions and builds an expression using concrete values.
        /// Implements claim precedence hierarchy:
        /// 1. Resource deny claims (highest precedence)
        /// 2. Tenant deny claims
        /// 3. Resource allow claims
        /// 4. Tenant allow claims
        /// 5. "My tenant" allow claims (transformed on-demand)
        /// 6. All-tenant allow claims (lowest precedence)
        /// </summary>
        public Expression<Func<TResource, bool>> BuildFilterExpression<TResource, TPermission>(ILogger? logger = null)
            where TResource : class
            where TPermission : class, IPermissionMetadata, IResourceBased
        {
            // Resolve dependencies from service provider
            var permission = serviceProvider.GetRequiredService<TPermission>();
            var sqlResourceKeyProvider = serviceProvider.GetRequiredService<ISqlTranslatableResourceKeyProvider<TResource>>();
            var sqlTenantRelationshipProvider = serviceProvider.GetRequiredService<ISqlTranslatableTenantRelationshipProvider<TResource>>();

            // Pre-evaluate user claims to concrete values that can be used in SQL
            var permissionEvaluation = EvaluateUserPermissions(permission, logger);

            logger?.LogTrace("Building filter expression for {ResourceType} with evaluation: HasAllTenantAccess={HasAllTenantAccess}, AllowedTenantIds={AllowedTenantIds}, DeniedTenantIds={DeniedTenantIds}, AllowedResourceKeys={AllowedResourceKeys}, DeniedResourceKeys={DeniedResourceKeys}",
                typeof(TResource).Name, permissionEvaluation.HasAllTenantAccess,
                string.Join(",", permissionEvaluation.AllowedTenantIds),
                string.Join(",", permissionEvaluation.DeniedTenantIds),
                string.Join(",", permissionEvaluation.AllowedResourceKeys),
                string.Join(",", permissionEvaluation.DeniedResourceKeys));

            // Build SQL-translatable expression using specialized providers and expression builder
            return SqlExpressionBuilder.BuildFilterExpression(
                permissionEvaluation,
                sqlResourceKeyProvider,
                sqlTenantRelationshipProvider,
                permission);
        }

        /// <summary>
        /// Evaluates the current user's permissions and extracts concrete values.
        /// </summary>
        private EvaluatedPermissions EvaluateUserPermissions(IPermissionMetadata permission, ILogger? logger)
        {
            // Pre-evaluate user authentication and permissions
            var user = userService.GetCurrentPrincipal(strict: false);
            if (user == null)
            {
                logger?.LogDebug("User is not authenticated, denying access to {EntityType}", permission.Subject.Name);
                return new EvaluatedPermissions(); // Return empty evaluation (denies all access)
            }

            if (!userService.TryGetCurrentUser(out var currentUser))
            {
                logger?.LogDebug("No current user found, denying access to {EntityType}", permission.Subject.Name);
                return new EvaluatedPermissions(); // Return empty evaluation (denies all access)
            }

            var evaluation = new EvaluatedPermissions();

            // Filter tenant claims by category first, then check values
            var tenantClaims = user.Claims.Where(c => c.Type == permission.TenantClaimType);
            
            // Check for all-tenant wildcard allow claim: "tenant:*:computers:view:allow"
            string allTenantsAllow = claimProvider.GetAllTenantAllowClaim(permission);
            if (tenantClaims.Any(c => c.Value == allTenantsAllow))
            {
                evaluation.HasAllTenantAccess = true;
                logger?.LogTrace("User has all-tenant wildcard allow claim: {Claim}", allTenantsAllow);
            }

            // Check for "my tenant" allow claims: "tenant:my:computers:view:allow"
            string myTenantClaim = claimProvider.GetMyTenantAllowClaim(permission);
            if (tenantClaims.Any(c => c.Value == myTenantClaim))
            {
                evaluation.AllowedTenantIds.Add(currentUser.TenantId);
                logger?.LogTrace("User has 'my tenant' allow claim for tenant {TenantId}: {Claim}", currentUser.TenantId, myTenantClaim);
            }

            // Extract specific tenant allow/deny claims - filter by category first
            foreach (var claim in tenantClaims)
            {
                // Parse tenant allow claims: "tenant:123:computers:view:allow"
                if (TryParseTenantAllowClaim(claim.Value, permission, out int allowTenantId))
                {
                    evaluation.AllowedTenantIds.Add(allowTenantId);
                    logger?.LogTrace("User has tenant allow claim for tenant {TenantId}: {Claim}", allowTenantId, claim.Value);
                }
                // Parse tenant deny claims: "tenant:123:computers:view:deny"
                else if (TryParseTenantDenyClaim(claim.Value, permission, out int denyTenantId))
                {
                    evaluation.DeniedTenantIds.Add(denyTenantId);
                    logger?.LogTrace("User has tenant deny claim for tenant {TenantId}: {Claim}", denyTenantId, claim.Value);
                }
            }

            // Extract resource claims - filter by category first
            var resourceClaims = user.Claims.Where(c => c.Type == permission.ResourceClaimType);
            foreach (var claim in resourceClaims)
            {
                // Parse resource allow claims: "computers:computer-123:view:allow"
                if (TryParseResourceAllowClaim(claim.Value, permission, out string allowResourceKey))
                {
                    evaluation.AllowedResourceKeys.Add(allowResourceKey);
                    logger?.LogTrace("User has resource allow claim for resource {ResourceKey}: {Claim}", allowResourceKey, claim.Value);
                }
                // Parse resource deny claims: "computers:computer-123:view:deny"
                else if (TryParseResourceDenyClaim(claim.Value, permission, out string denyResourceKey))
                {
                    evaluation.DeniedResourceKeys.Add(denyResourceKey);
                    logger?.LogTrace("User has resource deny claim for resource {ResourceKey}: {Claim}", denyResourceKey, claim.Value);
                }
            }

            return evaluation;
        }


        // Helper methods for parsing claim values - simplified since we filter by claim type first
        private static bool TryParseTenantAllowClaim(string claimValue, IPermissionMetadata permission, out int tenantId)
        {
            tenantId = 0;
            // Format: "tenant:123:computers:view:allow"
            var expectedSuffix = $":{permission.Subject.Name}:{permission.PermissionName}:allow";
            if (claimValue.StartsWith("tenant:") && claimValue.EndsWith(expectedSuffix))
            {
                var tenantPart = claimValue.Substring(7, claimValue.Length - 7 - expectedSuffix.Length);
                // Skip wildcard "*" and "my" - those are handled separately
                if (tenantPart == "*" || tenantPart == "my") return false;
                return int.TryParse(tenantPart, out tenantId);
            }
            return false;
        }

        private static bool TryParseTenantDenyClaim(string claimValue, IPermissionMetadata permission, out int tenantId)
        {
            tenantId = 0;
            // Format: "tenant:123:computers:view:deny"
            var expectedSuffix = $":{permission.Subject.Name}:{permission.PermissionName}:deny";
            if (claimValue.StartsWith("tenant:") && claimValue.EndsWith(expectedSuffix))
            {
                var tenantPart = claimValue.Substring(7, claimValue.Length - 7 - expectedSuffix.Length);
                // Skip wildcard "*" and "my" - deny doesn't use these
                if (tenantPart == "*" || tenantPart == "my") return false;
                return int.TryParse(tenantPart, out tenantId);
            }
            return false;
        }

        private static bool TryParseResourceAllowClaim(string claimValue, IPermissionMetadata permission, out string resourceKey)
        {
            resourceKey = string.Empty;
            // Format: "computers:computer-123:view:allow"
            var expectedPrefix = $"{permission.Subject.Name}:";
            var expectedSuffix = $":{permission.PermissionName}:allow";
            if (claimValue.StartsWith(expectedPrefix) && claimValue.EndsWith(expectedSuffix))
            {
                var startIndex = expectedPrefix.Length;
                var endIndex = claimValue.Length - expectedSuffix.Length;
                var length = endIndex - startIndex;

                // If length is <= 0, this is a feature-level claim (e.g., "computers:view:allow")
                // rather than a resource-specific claim (e.g., "computers:123:view:allow")
                if (length <= 0)
                {
                    return false;
                }
                resourceKey = claimValue.Substring(startIndex, length);
                return !string.IsNullOrEmpty(resourceKey);
            }
            return false;
        }

        private static bool TryParseResourceDenyClaim(string claimValue, IPermissionMetadata permission, out string resourceKey)
        {
            resourceKey = string.Empty;
            // Format: "computers:computer-123:view:deny"
            var expectedPrefix = $"{permission.Subject.Name}:";
            var expectedSuffix = $":{permission.PermissionName}:deny";
            if (claimValue.StartsWith(expectedPrefix) && claimValue.EndsWith(expectedSuffix))
            {
                var startIndex = expectedPrefix.Length;
                var endIndex = claimValue.Length - expectedSuffix.Length;
                var length = endIndex - startIndex;

                // If length is <= 0, this is a feature-level claim (e.g., "computers:view:deny")
                // rather than a resource-specific claim (e.g., "computers:123:view:deny")
                if (length <= 0)
                {
                    return false;
                }
                resourceKey = claimValue.Substring(startIndex, length);
                return !string.IsNullOrEmpty(resourceKey);
            }
            return false;
        }
    }
}
