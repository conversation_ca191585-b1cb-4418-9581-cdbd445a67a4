using System;
using System.Text.RegularExpressions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations
{
    /// <summary>
    /// Builds raw SQL WHERE clause fragments for permission-based filtering by leveraging
    /// the existing LINQ-based permission filter builder and extracting SQL from Entity Framework.
    /// </summary>
    public class RawSqlPermissionFilterBuilder(
        IPermissionFilterBuilder permissionFilterBuilder,
        Func<ImmybotDbContext> contextFactory) : IRawSqlPermissionFilterBuilder
    {
        /// <summary>
        /// Generates a raw SQL WHERE clause fragment by using the LINQ permission filter
        /// and extracting the WHERE clause from Entity Framework's generated SQL.
        /// </summary>
        public string BuildSqlFilter<TResource, TPermission>(string resourceTableAlias = "c")
            where TResource : class
            where TPermission : class, IPermissionMetadata, IResourceBased
        {
            // Use the existing LINQ-based permission filter builder
            var filter = permissionFilterBuilder.BuildFilterExpression<TResource, TPermission>();

            // Create and dispose context immediately after SQL generation to minimize resource usage
            using var context = contextFactory();
            var query = context.Set<TResource>()
                .IgnoreQueryFilters() // Ignore global query filters (like soft delete) to avoid duplication
                .Where(filter);
            
            var generatedSql = query.ToQueryString();

            // Extract the WHERE clause portion
            var whereClause = ExtractWhereClause(generatedSql);

            // Adapt table aliases if needed
            if (!string.IsNullOrEmpty(whereClause) && resourceTableAlias != "t")
                whereClause = AdaptTableAlias(whereClause, resourceTableAlias);

            return whereClause;
        }

        /// <summary>
        /// Extracts the WHERE clause from a complete SQL query generated by Entity Framework.
        /// </summary>
        private static string ExtractWhereClause(string sql)
        {
            // Look for WHERE clause in the generated SQL
            // EF typically generates SQL like: SELECT ... FROM ... WHERE (conditions)
            var whereMatch = Regex.Match(sql,
              @"WHERE\s+(.+?)(?:\s+ORDER\s+BY|\s+LIMIT|\s+OFFSET|$)",
                RegexOptions.IgnoreCase | RegexOptions.Singleline);

            if (whereMatch.Success)
            {
                var whereClause = whereMatch.Groups[1].Value.Trim();

                // Remove any outer parentheses that EF might add
                if (whereClause.StartsWith('(') && whereClause.EndsWith(')'))
                    whereClause = whereClause[1..^1];
                
                return whereClause;
            }

            return string.Empty;
        }

        /// <summary>
        /// Adapts the table alias in the WHERE clause to match the target query's alias.
        /// Entity Framework typically uses 't' as the default alias, but we might need different aliases.
        /// </summary>
        private static string AdaptTableAlias(string whereClause, string targetAlias)
        {
            // Replace EF's default table alias with our target alias
            // This is a simple replacement - may need to be more sophisticated for complex queries
            return Regex.Replace(whereClause, @"\bt\.", $"{targetAlias}.", RegexOptions.IgnoreCase);
        }
    }
}
