using System.Linq.Expressions;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations.SqlTranslatableProviders
{
  /// <summary>
  /// Abstract base class for SQL-translatable tenant relationship providers that use join tables.
  /// Provides common logic for entities that establish tenant relationships through join tables
  /// with relationship-based filtering (ownership vs. all relationships).
  /// </summary>
  /// <typeparam name="TEntity">The main entity type (e.g., Tag, Media)</typeparam>
  /// <typeparam name="TRelationship"></typeparam>
  public class JoinTableSqlTranslatableTenantRelationshipProvider<TEntity, TRelationship>
    : ISqlTranslatableTenantRelationshipProvider<TEntity>
    where TEntity : class, ITenantRelationships<TRelationship>
    where TRelationship : class, ITenantRelationship
  {
    /// <summary>
    /// Generates a tenant filter expression for entities using join table tenant relationships.
    /// Supports relationship-based filtering: ownership-only vs. all relationships.
    /// </summary>
    /// <param name="tenantIds">Collection of tenant IDs to filter by</param>
    /// <param name="permission">Permission metadata indicating relationship requirements</param>
    /// <returns>SQL-translatable expression filtering entities by tenant relationship</returns>
    public Expression<Func<TEntity, bool>> GetTenantFilterExpression(
      IReadOnlyCollection<int> tenantIds,
      IPermissionMetadata permission)
    {
      if (tenantIds.Count is 0) return entity => entity.TenantRelationships.Count == 0;

      // non-view permissions require ownership relationship
      if (permission.Category != PermissionCategory.View)
        return (e) =>
          e.TenantRelationships.Any(t => t.Relationship == Relationship.Owned && tenantIds.Contains(t.TenantId));

      // otherwise, view permissions allow all relationships
      return (e) => e.TenantRelationships.Count == 0 || e.TenantRelationships.Any(t => tenantIds.Contains(t.TenantId));
    }
  }
}
