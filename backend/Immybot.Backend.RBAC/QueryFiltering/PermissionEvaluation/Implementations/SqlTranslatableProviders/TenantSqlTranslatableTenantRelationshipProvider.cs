using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations.SqlTranslatableProviders;

internal class TenantSqlTranslatableTenantRelationshipProvider : ISqlTranslatableTenantRelationshipProvider<Tenant>
{
  public Expression<Func<Tenant, bool>> GetTenantFilterExpression(
    IReadOnlyCollection<int> tenantIds,
    IPermissionMetadata permission) =>
    tenant => tenantIds.Contains(tenant.Id);
}
