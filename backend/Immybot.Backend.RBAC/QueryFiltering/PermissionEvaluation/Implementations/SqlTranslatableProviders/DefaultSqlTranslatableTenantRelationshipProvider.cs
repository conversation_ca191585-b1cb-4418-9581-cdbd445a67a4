using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations.SqlTranslatableProviders
{
    /// <summary>
    /// Default SQL-translatable tenant relationship provider that uses the TenantId property.
    /// This mirrors the behavior of DefaultTenantRelationshipProvider but generates SQL-translatable expressions.
    /// Supports entities with direct TenantId properties using the streamlined single-method approach.
    /// </summary>
    /// <typeparam name="TResource">The type of resource being filtered.</typeparam>
    internal class DefaultSqlTranslatableTenantRelationshipProvider<TResource> : ISqlTranslatableTenantRelationshipProvider<TResource>
        where TResource : class
    {
        /// <inheritdoc/>
        public Expression<Func<TResource, bool>> GetTenantFilterExpression(
            IReadOnlyCollection<int> tenantIds,
            IPermissionMetadata permission)
        {
            // Check if the resource type has a TenantId property
            var tenantIdProperty = typeof(TResource).GetProperty("TenantId");
            if (tenantIdProperty == null)
            {
                // Return expression that always evaluates to false for resources without tenant relationships
                return resource => false;
            }

            // Build expression tree manually: resource => tenantIds.Contains(resource.TenantId.Value)
            var parameter = Expression.Parameter(typeof(TResource), "resource");
            var tenantIdAccess = Expression.Property(parameter, tenantIdProperty);
            
            // Handle nullable conversion if needed
            Expression tenantIdExpression;
            if (tenantIdProperty.PropertyType == typeof(int?))
            {
                // For nullable int, use HasValue && tenantIds.Contains(TenantId.Value)
                var hasValueProperty = Expression.Property(tenantIdAccess, "HasValue");
                var valueProperty = Expression.Property(tenantIdAccess, "Value");
                
                // Convert to List for better EF translation
                var tenantIdsList = tenantIds.ToList();
                var tenantIdsConstant = Expression.Constant(tenantIdsList);
                var containsMethod = typeof(List<int>).GetMethod("Contains", new[] { typeof(int) });
                var containsCall = Expression.Call(tenantIdsConstant, containsMethod!, valueProperty);
                
                // Combine: HasValue && Contains
                tenantIdExpression = Expression.AndAlso(hasValueProperty, containsCall);
            }
            else if (tenantIdProperty.PropertyType == typeof(int))
            {
                // For non-nullable int, directly check contains
                var tenantIdsList = tenantIds.ToList();
                var tenantIdsConstant = Expression.Constant(tenantIdsList);
                var containsMethod = typeof(List<int>).GetMethod("Contains", new[] { typeof(int) });
                tenantIdExpression = Expression.Call(tenantIdsConstant, containsMethod!, tenantIdAccess);
            }
            else
            {
                throw new InvalidOperationException($"TenantId property on {typeof(TResource).Name} must be of type int or int?, but found {tenantIdProperty.PropertyType.Name}.");
            }
            
            return Expression.Lambda<Func<TResource, bool>>(tenantIdExpression, parameter);
        }
    }
}