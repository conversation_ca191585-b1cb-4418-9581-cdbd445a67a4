using System;
using System.Linq.Expressions;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Models;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations.SqlTranslatableProviders
{
    /// <summary>
    /// Default SQL-translatable resource key provider that returns integer Id expressions
    /// to avoid inefficient CAST operations in SQL queries.
    /// </summary>
    /// <typeparam name="TResource">The type of resource being filtered.</typeparam>
    internal class DefaultSqlTranslatableResourceKeyProvider<TResource> : ISqlTranslatableResourceKeyProvider<TResource>
        where TResource : class
    {
        /// <inheritdoc/>
        public ResourceKeyInfo<TResource> GetResourceKeyInfo()
        {
            // Validate that the resource type has an Id property
            var idProperty = typeof(TResource).GetProperty("Id");
            if (idProperty == null)
                throw new InvalidOperationException($"Resource type {typeof(TResource).Name} does not have an Id property. Use a specialized SQL-translatable resource key provider.");

            // Validate that Id is an integer (never nullable)
            if (idProperty.PropertyType != typeof(int))
                throw new InvalidOperationException($"Resource type {typeof(TResource).Name} has Id property of type {idProperty.PropertyType.Name}. Expected int.");

            var parameter = Expression.Parameter(typeof(TResource), "resource");
            var idAccess = Expression.Property(parameter, idProperty);
            var intExpression = Expression.Lambda<Func<TResource, int>>(idAccess, parameter);

            return ResourceKeyInfo<TResource>.ForInteger(intExpression);
        }

        public bool SupportsResourceKey => true;
    }
}
