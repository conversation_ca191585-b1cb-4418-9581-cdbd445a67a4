using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations.SqlTranslatableProviders;

internal class MaintenanceSessionSqlTranslatableTenantRelationshipProvider : ISqlTranslatableTenantRelationshipProvider<
  MaintenanceSession>
{
  public Expression<Func<MaintenanceSession, bool>> GetTenantFilterExpression(
    IReadOnlyCollection<int> tenantIds,
    IPermissionMetadata permission) =>
    session => (session.TenantId != null && tenantIds.Contains(session.TenantId.Value)) ||
               (session.Computer != null && tenantIds.Contains(session.Computer.TenantId)) ||
               (session.Person != null && tenantIds.Contains(session.Person.TenantId));
}
