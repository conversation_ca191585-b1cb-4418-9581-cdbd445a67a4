using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations.SqlTranslatableProviders;

internal class ProviderLinkSqlTranslatableTenantRelationshipProvider
  : ISqlTranslatableTenantRelationshipProvider<ProviderLink>
{
  public Expression<Func<ProviderLink, bool>> GetTenantFilterExpression(
    IReadOnlyCollection<int> tenantIds,
    IPermissionMetadata permission) =>
    providerLink => tenantIds.Contains(providerLink.OwnerTenantId);
}
