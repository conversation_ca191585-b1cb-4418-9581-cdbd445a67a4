using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations.SqlTranslatableProviders
{
    /// <summary>
    /// Provides SQL-translatable tenant relationship filtering for LocalSoftware entities.
    /// LocalSoftware uses TenantSoftware join table without relationship-based filtering.
    /// Note: TenantSoftware doesn't have a Relationship property, so all permissions
    /// are treated equally - only tenant membership is checked.
    /// </summary>
    public class LocalSoftwareSqlTranslatableTenantRelationshipProvider
        : ISqlTranslatableTenantRelationshipProvider<LocalSoftware>
    {
      /// <summary>
      /// Generates a tenant filter expression for LocalSoftware entities using TenantSoftware join table.
      /// Since TenantSoftware doesn't have a Relationship property, this only filters by tenant membership.
      /// </summary>
      /// <param name="tenantIds">Collection of tenant IDs to filter by</param>
      /// <param name="permission">Permission metadata (not used for relationship filtering since TenantSoftware lacks Relationship property)</param>
      /// <returns>SQL-translatable expression filtering LocalSoftware entities by tenant membership</returns>
      public Expression<Func<LocalSoftware, bool>> GetTenantFilterExpression(
        IReadOnlyCollection<int> tenantIds,
        IPermissionMetadata permission)
      {
        var tenantIdList = tenantIds.ToList();
        if (permission.Category is not PermissionCategory.View)
          return (s) => s.OwnerTenantId.HasValue && tenantIdList.Contains(s.OwnerTenantId.Value);

        return (s) => (s.OwnerTenantId.HasValue && tenantIdList.Contains(s.OwnerTenantId.Value)) ||
                      (s.TenantSoftware.Any(t => tenantIdList.Contains(t.TenantId))) ||
                      (s.OwnerTenantId == null && s.TenantSoftware.Count == 0);
        }
    }
}
