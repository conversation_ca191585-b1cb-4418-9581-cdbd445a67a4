using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations.SqlTranslatableProviders;

internal class
  ProviderClientSqlTranslatableTenantRelationshipProvider : ISqlTranslatableTenantRelationshipProvider<ProviderClient>
{
  public Expression<Func<ProviderClient, bool>> GetTenantFilterExpression(
    IReadOnlyCollection<int> tenantIds,
    IPermissionMetadata permission) =>
    client => client.LinkedToTenantId != null && tenantIds.Contains(client.LinkedToTenantId.Value);
}
