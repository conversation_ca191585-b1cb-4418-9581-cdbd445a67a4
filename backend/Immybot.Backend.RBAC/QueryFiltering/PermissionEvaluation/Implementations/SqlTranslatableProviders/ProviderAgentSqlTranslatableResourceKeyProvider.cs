using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Models;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations.SqlTranslatableProviders;

public class ProviderAgentSqlTranslatableResourceKeyProvider : ISqlTranslatableResourceKeyProvider<ProviderAgent>
{
  public ResourceKeyInfo<ProviderAgent> GetResourceKeyInfo()
  {
    throw new NotImplementedException();
  }

  public bool SupportsResourceKey => false;
}
