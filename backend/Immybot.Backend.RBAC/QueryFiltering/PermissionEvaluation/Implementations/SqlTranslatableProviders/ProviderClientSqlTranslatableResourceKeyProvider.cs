using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Models;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations.SqlTranslatableProviders;

public class ProviderClientSqlTranslatableResourceKeyProvider : ISqlTranslatableResourceKeyProvider<ProviderClient>
{
  public ResourceKeyInfo<ProviderClient> GetResourceKeyInfo()
  {
    throw new NotImplementedException();
  }

  public bool SupportsResourceKey => false;
}
