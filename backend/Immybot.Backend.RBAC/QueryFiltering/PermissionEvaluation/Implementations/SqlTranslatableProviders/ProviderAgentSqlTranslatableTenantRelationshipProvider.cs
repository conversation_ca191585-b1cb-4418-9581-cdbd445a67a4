using System.Linq.Expressions;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations.SqlTranslatableProviders;

internal class
  ProviderAgentSqlTranslatableTenantRelationshipProvider : ISqlTranslatableTenantRelationshipProvider<ProviderAgent>
{
  public Expression<Func<ProviderAgent, bool>> GetTenantFilterExpression(
    IReadOnlyCollection<int> tenantIds,
    IPermissionMetadata permission) =>
    agent => agent.ProviderClient != null && agent.ProviderClient.LinkedToTenantId != null &&
             tenantIds.Contains(agent.ProviderClient.LinkedToTenantId.Value);
}
