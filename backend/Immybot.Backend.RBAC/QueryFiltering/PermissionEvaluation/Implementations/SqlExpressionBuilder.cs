using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Models;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations
{
    /// <summary>
    /// Builds SQL-translatable filter expressions by combining resource and tenant expressions
    /// with pre-evaluated permission data.
    /// </summary>
    internal class SqlExpressionBuilder
    {
        /// <summary>
        /// Builds a complete SQL-translatable filter expression using provider expressions and evaluation results.
        /// </summary>
        public static Expression<Func<TResource, bool>> BuildFilterExpression<TResource>(
            EvaluatedPermissions evaluation,
            ISqlTranslatableResourceKeyProvider<TResource> resourceKeyProvider,
            ISqlTranslatableTenantRelationshipProvider<TResource> tenantRelationshipProvider,
            IPermissionMetadata permission)
            where TResource : class
        {
            // Get the tenant filter expression from the provider using the streamlined approach
            var tenantFilterExpression = tenantRelationshipProvider.GetTenantFilterExpression(
                evaluation.AllowedTenantIds,
                permission);
    
            // Handle resource-level filtering if supported
            if (!resourceKeyProvider.SupportsResourceKey)
            {
                // If no resource key support, use tenant filter with global access check
                var globalAccess = Expression.Constant(evaluation.HasAllTenantAccess);
                var finalFilter = Expression.OrElse(tenantFilterExpression.Body, globalAccess);
                return Expression.Lambda<Func<TResource, bool>>(finalFilter, tenantFilterExpression.Parameters[0]);
            }
    
            // Get resource key information for resource-level filtering
            var resourceKeyInfo = resourceKeyProvider.GetResourceKeyInfo();
            
            // Create parameter for the final expression
            var parameter = Expression.Parameter(typeof(TResource), "resource");
    
            // Replace parameters in provider expressions with our parameter
            var tenantFilterBody = ReplaceParameter(tenantFilterExpression.Body,
                tenantFilterExpression.Parameters[0], parameter);
            
            var resourceKeyBody = ReplaceParameter(resourceKeyInfo.KeyExpression.Body,
                resourceKeyInfo.KeyExpression.Parameters[0], parameter);
    
            // Build the filter conditions based on resource key type
            var denyConditions = BuildDenyConditions<TResource>(evaluation, resourceKeyBody, resourceKeyInfo.KeyType, parameter, tenantRelationshipProvider, permission);
            var allowConditions = BuildAllowConditions(evaluation, resourceKeyBody, resourceKeyInfo.KeyType, tenantFilterBody);
    
            // Final expression: !denied && allowed
            var finalExpression = Expression.AndAlso(
                Expression.Not(denyConditions),
                allowConditions);
    
            return Expression.Lambda<Func<TResource, bool>>(finalExpression, parameter);
        }

        /// <summary>
        /// Builds deny conditions: resource is denied OR tenant is denied.
        /// </summary>
        private static Expression BuildDenyConditions<TResource>(
            EvaluatedPermissions evaluation,
            Expression resourceKeyExpression,
            Type keyType,
            ParameterExpression parameter,
            ISqlTranslatableTenantRelationshipProvider<TResource> tenantRelationshipProvider,
            IPermissionMetadata permission)
            where TResource : class
        {
            // Resource-level denies
            var resourceDenied = BuildResourceContainsExpression(evaluation.DeniedResourceKeys, resourceKeyExpression, keyType);
            
            // Tenant-level denies: use the tenant relationship provider to build deny expression
            Expression tenantDenied = Expression.Constant(false);
            if (evaluation.DeniedTenantIds.Any())
            {
                // Get the tenant deny filter expression from the provider
                var tenantDenyExpression = tenantRelationshipProvider.GetTenantFilterExpression(
                    evaluation.DeniedTenantIds,
                    permission);
                
                // Replace the parameter in the tenant deny expression
                tenantDenied = ReplaceParameter(tenantDenyExpression.Body,
                    tenantDenyExpression.Parameters[0], parameter);
            }
            
            // Combine: resource denied OR tenant denied
            return Expression.OrElse(resourceDenied, tenantDenied);
        }
    
        /// <summary>
        /// Builds allow conditions: resource is allowed OR tenant is allowed OR all-tenant access.
        /// </summary>
        private static Expression BuildAllowConditions(
            EvaluatedPermissions evaluation,
            Expression resourceKeyExpression,
            Type keyType,
            Expression tenantFilterExpression)
        {
            // Resource-level allows
            var resourceAllowed = BuildResourceContainsExpression(evaluation.AllowedResourceKeys, resourceKeyExpression, keyType);
            
            // Global access check
            var allTenantAccess = Expression.Constant(evaluation.HasAllTenantAccess);
    
            // Combine: resource allowed OR tenant allowed OR global access
            return Expression.OrElse(
                Expression.OrElse(resourceAllowed, tenantFilterExpression),
                allTenantAccess);
        }

        /// <summary>
        /// Builds a Contains expression for resource keys, converting string keys to the appropriate type.
        /// </summary>
        private static Expression BuildResourceContainsExpression(HashSet<string> stringCollection, Expression valueExpression, Type keyType)
        {
            // Convert string collection to the appropriate type based on the actual resource key type
            if (keyType == typeof(int))
            {
                // Convert strings to integers for direct comparison with integer IDs
                var integerCollection = new HashSet<int>();
                foreach (var stringValue in stringCollection)
                {
                    if (int.TryParse(stringValue, out int intValue))
                    {
                        integerCollection.Add(intValue);
                    }
                }

                var collectionConstant = Expression.Constant(integerCollection);
                var containsMethod = typeof(HashSet<int>).GetMethod("Contains", new[] { typeof(int) });
                return Expression.Call(collectionConstant, containsMethod!, valueExpression);
            }
            else
            {
                // For other types (string, Guid, etc.), use string comparison
                var collectionConstant = Expression.Constant(stringCollection);
                var containsMethod = typeof(HashSet<string>).GetMethod("Contains", new[] { typeof(string) });
                return Expression.Call(collectionConstant, containsMethod!, valueExpression);
            }
        }


        /// <summary>
        /// Replaces a parameter in an expression tree with a different parameter.
        /// </summary>
        private static Expression ReplaceParameter(Expression expression, ParameterExpression oldParameter, ParameterExpression newParameter)
        {
            return new ParameterReplacer(oldParameter, newParameter).Visit(expression);
        }

        /// <summary>
        /// Expression visitor that replaces parameter references.
        /// </summary>
        private class ParameterReplacer : ExpressionVisitor
        {
            private readonly ParameterExpression _oldParameter;
            private readonly ParameterExpression _newParameter;

            public ParameterReplacer(ParameterExpression oldParameter, ParameterExpression newParameter)
            {
                _oldParameter = oldParameter;
                _newParameter = newParameter;
            }

            protected override Expression VisitParameter(ParameterExpression node)
            {
                return node == _oldParameter ? _newParameter : base.VisitParameter(node);
            }
        }
    }
}
