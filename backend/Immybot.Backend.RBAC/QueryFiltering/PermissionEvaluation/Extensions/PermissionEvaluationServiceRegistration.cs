using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.QueryFiltering.Interfaces;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Implementations.SqlTranslatableProviders;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Extensions;

/// <summary>
/// Extension methods for registering permission evaluation services with the dependency injection container.
/// </summary>
public static class PermissionEvaluationServiceRegistration
{
    /// <summary>
    /// Registers permission evaluation services with the service collection.
    /// </summary>
    /// <param name="services">The service collection to configure.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddPermissionEvaluationServices(this IServiceCollection services)
    {
        // Register the permission filter builders
        services.AddScoped<IPermissionFilterBuilder, PermissionFilterBuilder>();
        services.AddScoped<IRawSqlPermissionFilterBuilder, RawSqlPermissionFilterBuilder>();

        // Register SQL-translatable providers (default implementations)
        services.AddSingleton(typeof(ISqlTranslatableResourceKeyProvider<>), typeof(DefaultSqlTranslatableResourceKeyProvider<>));

        services
          .AddSingleton<ISqlTranslatableResourceKeyProvider<SmtpConfig>,
            SmtpConfigSqlTranslatableResourceKeyProvider>();
        services
          .AddSingleton<ISqlTranslatableResourceKeyProvider<ProviderAgent>,
            ProviderAgentSqlTranslatableResourceKeyProvider>();
        services
          .AddSingleton<ISqlTranslatableResourceKeyProvider<ProviderClient>,
            ProviderClientSqlTranslatableResourceKeyProvider>();

        services.AddSingleton(typeof(ISqlTranslatableTenantRelationshipProvider<>), typeof(DefaultSqlTranslatableTenantRelationshipProvider<>));

        // Register custom tenant relationship providers for join table scenarios
        services
          .AddSingleton<ISqlTranslatableTenantRelationshipProvider<Tag>,
            JoinTableSqlTranslatableTenantRelationshipProvider<Tag, TenantTagAuthorization>>();
        services
          .AddSingleton<ISqlTranslatableTenantRelationshipProvider<Media>,
            JoinTableSqlTranslatableTenantRelationshipProvider<Media, TenantMedia>>();
        services
          .AddSingleton<ISqlTranslatableTenantRelationshipProvider<Script>,
            JoinTableSqlTranslatableTenantRelationshipProvider<Script, TenantScript>>();
        services
          .AddSingleton<ISqlTranslatableTenantRelationshipProvider<MaintenanceTask>,
            JoinTableSqlTranslatableTenantRelationshipProvider<MaintenanceTask, TenantMaintenanceTask>>();

        services.AddSingleton<ISqlTranslatableTenantRelationshipProvider<LocalSoftware>, LocalSoftwareSqlTranslatableTenantRelationshipProvider>();
        services
          .AddSingleton<ISqlTranslatableTenantRelationshipProvider<Tenant>,
            TenantSqlTranslatableTenantRelationshipProvider>();
        services
          .AddSingleton<ISqlTranslatableTenantRelationshipProvider<ProviderLink>,
            ProviderLinkSqlTranslatableTenantRelationshipProvider>();
        services
          .AddSingleton<ISqlTranslatableTenantRelationshipProvider<MaintenanceSession>,
            MaintenanceSessionSqlTranslatableTenantRelationshipProvider>();
        services
          .AddSingleton<ISqlTranslatableTenantRelationshipProvider<ProviderClient>,
            ProviderClientSqlTranslatableTenantRelationshipProvider>();
        services
          .AddSingleton<ISqlTranslatableTenantRelationshipProvider<ProviderAgent>,
            ProviderAgentSqlTranslatableTenantRelationshipProvider>();

        return services;
    }
}
