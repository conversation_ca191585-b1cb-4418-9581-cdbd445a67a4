using System;
using System.Linq.Expressions;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Models;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces
{
    /// <summary>
    /// Provides SQL-translatable expressions for extracting resource keys from entities.
    /// This is the query filtering counterpart to IResourceKeyProvider that generates
    /// expressions instead of executing methods.
    /// </summary>
    /// <typeparam name="TResource">The type of resource being filtered.</typeparam>
    public interface ISqlTranslatableResourceKeyProvider<TResource>
        where TResource : class
    {
        /// <summary>
        /// Creates an expression that extracts the resource key from the resource entity.
        /// This expression must be translatable to SQL by Entity Framework.
        /// Returns type information to allow for optimal SQL generation.
        /// </summary>
        /// <returns>Resource key information including the expression and key type.</returns>
        ResourceKeyInfo<TResource> GetResourceKeyInfo();

        /// <summary>
        /// Determines whether the provider supports resource keys.
        /// </summary>
        bool SupportsResourceKey { get; }
    }
}
