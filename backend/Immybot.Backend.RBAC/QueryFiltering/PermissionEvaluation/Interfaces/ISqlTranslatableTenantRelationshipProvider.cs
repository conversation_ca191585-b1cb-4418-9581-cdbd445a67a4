using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;

namespace Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Interfaces
{
    /// <summary>
    /// Provides SQL-translatable expressions for filtering entities based on tenant relationships.
    /// This is the query filtering counterpart to ITenantRelationshipProvider that generates
    /// expressions instead of executing methods. Supports both direct tenant relationships
    /// and complex join table scenarios.
    /// </summary>
    /// <typeparam name="TResource">The type of resource being filtered.</typeparam>
    public interface ISqlTranslatableTenantRelationshipProvider<TResource>
        where TResource : class
    {
        /// <summary>
        /// Creates a SQL-translatable expression that filters resources based on tenant access
        /// and permission requirements. This method handles all tenant filtering scenarios
        /// including direct TenantId properties and join table relationships.
        /// </summary>
        /// <param name="tenantIds">The collection of tenant IDs the user has access to.</param>
        /// <param name="permission">The permission metadata used to determine relationship requirements.</param>
        /// <returns>An expression that evaluates to true if the user has access to the resource.</returns>
        Expression<Func<TResource, bool>> GetTenantFilterExpression(
            IReadOnlyCollection<int> tenantIds,
            IPermissionMetadata permission);
    }
}