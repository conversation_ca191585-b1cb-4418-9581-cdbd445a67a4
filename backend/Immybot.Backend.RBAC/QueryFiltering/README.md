# Query Filtering

## Table of Contents
- [Context](#context)
- [Summary](#summary)
- [Architecture](#architecture)
- [Components](#components)
- [File Organization](#file-organization)
- [Additional Information](#additional-information)
- [Extending this Feature](#extending-this-feature)

## Context

Entity Framework queries in web applications typically require runtime permission filtering to ensure users only access authorized resources. Traditional approaches often result in N+1 database queries or inefficient post-processing of results in memory. ASP.NET Core's RBAC (Role-Based Access Control) system uses claims-based authorization, where user permissions are stored as string claims that need to be evaluated at runtime. Query filtering bridges this gap by pre-evaluating user claims and converting them into SQL-translatable expressions that Entity Framework can incorporate directly into database queries, eliminating the need for post-query filtering and reducing database round trips.

## Summary

The Query Filtering feature provides SQL-translatable permission filtering for Entity Framework queries within Immybot's RBAC system. It pre-evaluates user permission claims and generates database-level filter expressions that implement the same claim precedence hierarchy used by ResourceAuthorization. The feature supports both LINQ expression generation for Entity Framework integration and raw SQL generation for custom queries. It handles complex scenarios including direct tenant relationships, join table scenarios, and different resource key types while maintaining optimal database performance through SQL-level filtering rather than memory-based post-processing.

## Architecture

```mermaid
graph TB
    Client[Client Request] --> PFB[PermissionFilterBuilder]
    Client --> RSPFB[RawSqlPermissionFilterBuilder]
    
    PFB --> UserService[IUserService]
    PFB --> ClaimProvider[IResourceClaimProvider]
    PFB --> EP[EvaluatedPermissions]
    
    EP --> SEB[SqlExpressionBuilder]
    SEB --> SRKP[ISqlTranslatableResourceKeyProvider]
    SEB --> STRP[ISqlTranslatableTenantRelationshipProvider]
    
    SRKP --> DefaultSRKP[DefaultSqlTranslatableResourceKeyProvider]
    SRKP --> SmtpSRKP[SmtpConfigSqlTranslatableResourceKeyProvider]
    
    STRP --> DefaultSTRP[DefaultSqlTranslatableTenantRelationshipProvider]
    STRP --> TagSTRP[TagSqlTranslatableTenantRelationshipProvider]
    STRP --> MediaSTRP[MediaSqlTranslatableTenantRelationshipProvider]
    
    RSPFB --> PFB
    RSPFB --> EFContext[Entity Framework Context]
    RSPFB --> SQLExtraction[SQL Extraction & Alias Adaptation]
```

The architecture centers around two main filter builders that leverage a provider pattern for different entity types. The PermissionFilterBuilder pre-evaluates claims into concrete values, then uses specialized providers to generate SQL-translatable expressions. The RawSqlPermissionFilterBuilder reuses the LINQ approach but extracts SQL through Entity Framework for use in custom queries.

## Components

1. **[`PermissionEvaluation/Interfaces/IPermissionFilterBuilder.cs`](PermissionEvaluation/Interfaces/)** - Core interface for building permission-based filter expressions

   Defines the contract for creating SQL-translatable filter expressions using permission metadata. Automatically resolves dependencies through the service provider and implements the complete claim precedence hierarchy. Returns expressions compatible with Entity Framework translation to SQL.

2. **[`PermissionEvaluation/Interfaces/IRawSqlPermissionFilterBuilder.cs`](PermissionEvaluation/Interfaces/)** - Interface for generating raw SQL WHERE clause fragments

   Provides the contract for building SQL WHERE clause fragments that can be appended to existing queries. Designed for scenarios where LINQ expressions aren't suitable but permission filtering is still required at the database level.

3. **[`PermissionEvaluation/Implementations/PermissionFilterBuilder.cs`](PermissionEvaluation/Implementations/)** - Main implementation of permission filter building

   Pre-evaluates user claims by parsing tenant and resource claims according to the claim precedence hierarchy. Extracts concrete values from string claims and delegates expression building to SqlExpressionBuilder. Handles authentication validation and claim parsing for multiple claim types.

4. **[`PermissionEvaluation/Implementations/RawSqlPermissionFilterBuilder.cs`](PermissionEvaluation/Implementations/)** - Raw SQL generation implementation

   Leverages the existing LINQ-based PermissionFilterBuilder to generate Entity Framework queries, then extracts the WHERE clause using regex parsing. Adapts table aliases to match target query requirements for seamless integration with existing SQL.

5. **[`PermissionEvaluation/Implementations/SqlExpressionBuilder.cs`](PermissionEvaluation/Implementations/)** - Expression tree construction utility

   Combines evaluated permissions with provider expressions to build complete filter expressions. Handles parameter replacement, resource key type conversion, and the logical combination of deny and allow conditions. Creates optimized expressions that translate efficiently to SQL.

6. **[`PermissionEvaluation/Interfaces/ISqlTranslatableResourceKeyProvider.cs`](PermissionEvaluation/Interfaces/)** - Contract for resource key expression generation

   Defines how to extract resource keys from entities using SQL-translatable expressions. Provides type information to enable optimal SQL generation and supports entities with different key types (int, string, Guid).

7. **[`PermissionEvaluation/Interfaces/ISqlTranslatableTenantRelationshipProvider.cs`](PermissionEvaluation/Interfaces/)** - Contract for tenant relationship filtering

   Defines how to filter entities based on tenant relationships using SQL-translatable expressions. Supports both direct TenantId properties and complex join table scenarios while maintaining SQL translatability.

8. **[`PermissionEvaluation/Implementations/SqlTranslatableProviders/DefaultSqlTranslatableResourceKeyProvider.cs`](PermissionEvaluation/Implementations/SqlTranslatableProviders/)** - Default resource key provider

   Provides expressions for extracting integer Id properties from entities. Validates that the resource type has an Id property of type int and generates optimized expressions that avoid SQL CAST operations.

9. **[`PermissionEvaluation/Implementations/SqlTranslatableProviders/DefaultSqlTranslatableTenantRelationshipProvider.cs`](PermissionEvaluation/Implementations/SqlTranslatableProviders/)** - Default tenant relationship provider

   Handles entities with direct TenantId properties (both nullable and non-nullable int). Generates Contains expressions using List<int> for optimal Entity Framework translation to SQL IN clauses.

10. **[`PermissionEvaluation/Models/EvaluatedPermissions.cs`](PermissionEvaluation/Models/)** - Container for pre-evaluated permission data

    Stores the concrete results of claim evaluation including allowed/denied tenant IDs and resource keys. Enables SQL-level filtering by converting runtime claims into static values that can be embedded in database queries.

11. **[`PermissionEvaluation/Models/ResourceKeyInfo.cs`](PermissionEvaluation/Models/)** - Resource key expression metadata

    Contains typed information about resource key expressions including the lambda expression and actual key type. Provides factory methods for common key types and enables type-safe expression building.

12. **[`PermissionEvaluation/Extensions/PermissionEvaluationServiceRegistration.cs`](PermissionEvaluation/Extensions/)** - Dependency injection configuration

    Registers all permission evaluation services with the DI container including default and specialized providers. Configures providers for specific entity types that require custom tenant relationship handling or resource key extraction.

## File Organization

**QueryFiltering/** - Query filtering feature root directory
- **[PermissionEvaluation/](PermissionEvaluation/)** - Permission evaluation component
  - **[Abstractions/](PermissionEvaluation/Abstractions/)** - Abstract base classes for specialized scenarios
    - **[SqlTranslatableProviders/](PermissionEvaluation/Abstractions/SqlTranslatableProviders/)** - Abstract providers for join table scenarios
  - **[Extensions/](PermissionEvaluation/Extensions/)** - Service registration extensions
  - **[Implementations/](PermissionEvaluation/Implementations/)** - Concrete implementations
    - **[SqlTranslatableProviders/](PermissionEvaluation/Implementations/SqlTranslatableProviders/)** - Entity-specific provider implementations
  - **[Interfaces/](PermissionEvaluation/Interfaces/)** - Contracts and abstractions
  - **[Models/](PermissionEvaluation/Models/)** - Data structures for permission evaluation

## Additional Information

### Performance Considerations
Query filtering provides significant performance benefits by moving permission evaluation from application memory to the database. SQL-level filtering eliminates N+1 query problems and reduces memory usage by filtering results before they're loaded into the application. The pre-evaluation of claims into concrete values enables optimal SQL generation with parameterized queries that can be cached by the database engine.

### Entity Framework Integration
All generated expressions must be SQL-translatable to avoid client-side evaluation warnings. The system validates that resource key providers use supported LINQ operations and avoid complex C# logic that cannot be converted to SQL. When Entity Framework cannot translate an expression, it will fall back to client-side evaluation, negating the performance benefits.

### Known Limitations
Complex LINQ operations involving method calls or non-SQL-translatable expressions will cause Entity Framework to evaluate the query in memory. The system currently supports basic equality, contains operations, and simple property access patterns. Custom providers must avoid using methods that are not supported by the Entity Framework SQL translation layer.

### Troubleshooting
Common issues include missing provider registrations for new entity types, SQL translation failures due to complex expressions, and incorrect claim precedence evaluation. Enable Entity Framework logging to identify client-side evaluation warnings and verify that generated SQL includes the expected WHERE clause conditions.

## Extending this Feature

For adding new components or extending functionality, refer to:
- **[DDD Instructions](../../.instructions/common/ddd.md)** - Domain-driven design patterns
- **[Feature Instructions](../../.instructions/common/feature.md)** - Implementation guide

To add support for new entity types, implement `ISqlTranslatableResourceKeyProvider<T>` and/or `ISqlTranslatableTenantRelationshipProvider<T>` and register them in the service registration extension. For join table scenarios, inherit from the abstract base classes in the Abstractions folder.