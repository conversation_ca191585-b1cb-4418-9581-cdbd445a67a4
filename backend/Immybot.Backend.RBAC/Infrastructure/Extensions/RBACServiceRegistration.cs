using Immybot.Backend.RBAC.ClaimsTransformations.Extensions;
using Immybot.Backend.RBAC.QueryFiltering.PermissionEvaluation.Extensions;
using Immybot.Backend.RBAC.ResourceAuthorization.Extensions;
using Immybot.Backend.RBAC.RoleClaimMetadataService.Extensions;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltinRoleAssignment.Extensions;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Extensions;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.SeedData.Extensions;
using Immybot.Backend.RBAC.RolesManagement.CustomRoles.Extensions;
using Immybot.Backend.RBAC.SubjectAuthorization.Authorization.Extensions;
using Immybot.Backend.RBAC.SubjectAuthorization.Metadata.Extensions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Extensions;
using Immybot.Backend.RBAC.UserCacheManagement.Extensions;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.RBAC.Infrastructure.Extensions;

/// <summary>
/// Service registration for RBAC.
/// </summary>
public static class RBACServiceRegistration
{
  /// <summary>
  /// Registers all services for the RBAC domain.
  /// </summary>
  public static IServiceCollection AddRBACServices(this IServiceCollection services)
  {
    // Register custom roles operation services
    services.AddCustomRoleOperations();

    // Register cache management services
    services.AddUserCacheManagement();

    // Register metadata services
    services.AddMetadataServices();

    // Register claims transformation services
    services.AddClaimsTransformationServices();

    // Register built-in roles services
    services.AddBuiltinRolesServices();

    // Register subject permissions services
    services.AddSubjectPermissionsServices();

    // Register subject authorization services
    services.AddSubjectPermissionAuthorization();

    // Register built-in role assignment services
    services.AddBuiltInRoleAssignmentServices();

    // Register role seed data services
    services.AddRoleSeedDataServices();

    // Register resource authorization services
    services.AddResourceAuthorization();

    // Register query filtering services (database-level permission filtering)
    services.AddPermissionEvaluationServices();

    services.AddRoleClaimMetadataService();

    return services;
  }
}
