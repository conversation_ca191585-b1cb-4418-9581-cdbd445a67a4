# ClaimsTransformations

## Context

Claims transformations are essential in Role-Based Access Control (RBAC) systems to bridge the gap between external identity providers and application-specific authorization requirements. In enterprise applications using Azure AD authentication, raw tokens contain standard identity claims but lack the enriched context needed for fine-grained authorization decisions. The ClaimsTransformations feature provides a structured approach to transform and enrich user identities with tenant-specific roles, permissions, and contextual claims required for multi-tenant authorization scenarios. This transformation layer operates seamlessly within the ASP.NET Core authentication pipeline, ensuring claims are available throughout the application lifecycle for authorization decisions and audit trails.

## Summary

The ClaimsTransformations feature implements a composite pattern-based claims enrichment pipeline that transforms Azure AD authentication tokens into application-specific authorization claims. This system orchestrates multiple specialized transformations through [`CompositeClaimsTransformation`](Implementations/CompositeClaimsTransformation.cs:11) to systematically add tenant identifiers, user roles, impersonation audit trails, and permission-based claims. The feature handles complex scenarios including user impersonation with full audit capabilities, multi-tenant permission isolation, and system account authorization. Each transformation component focuses on a specific aspect of authorization: core identity claims, global permissions for system users, and manager-level system access rights. The pipeline integrates with OpenTelemetry for observability and maintains security through comprehensive impersonation tracking and role-based claim validation.

## Architecture

```mermaid
graph TD
    A[Azure AD Token] --> B[ASP.NET Core Authentication Pipeline]
    B --> C[CompositeClaimsTransformation]
    C --> D[UserRoleClaimsTransformation]
    C --> E[ImmenseUserClaimsTransformation]
    C --> F[ManagerClaimsTransformation]
    
    D --> G[Add Core Identity Claims<br/>ImmyTenantId, ImmyUserId, IsMsp]
    D --> H[Handle Impersonation<br/>Audit Trail Claims]
    D --> I[Add Role-Based Claims<br/>User Roles & Permissions]
    
    E --> J[Add Global Management<br/>Permission Claims]
    
    F --> K[Add Manager System<br/>Account Claims]
    
    G --> L[Enriched ClaimsPrincipal]
    H --> L
    I --> L
    J --> L
    K --> L
    
    L --> M[Authorization Handlers]
    L --> N[Resource Authorization]
    L --> O[Query Filtering]
```

The transformation pipeline processes claims sequentially through the composite pattern. Each transformation component receives the principal from the previous step, applies its specific enrichments, and passes the enhanced principal to the next transformation. This sequential approach ensures proper claim precedence and allows for complex scenarios like impersonation where claims may be replaced or augmented based on security context.

## Components

### CompositeClaimsTransformation
The [`CompositeClaimsTransformation`](Implementations/CompositeClaimsTransformation.cs:11) class serves as the orchestrator implementing the composite pattern to manage the transformation pipeline. It accepts an enumerable collection of [`IClaimsTransformation`](Implementations/CompositeClaimsTransformation.cs:6) implementations and processes them sequentially. This design provides flexibility for adding new transformations without modifying existing code and ensures consistent processing order. The composite approach decouples individual transformation logic while maintaining a unified interface for the authentication system. Each transformation receives the output of the previous transformation, enabling complex claim manipulation scenarios. The class implements [`ICompositeClaimsTransformation`](Implementations/CompositeClaimsTransformation.cs:6) for dependency injection integration and service registration.

### UserRoleClaimsTransformation
The [`UserRoleClaimsTransformation`](Implementations/UserRoleClaimsTransformation.cs:14) handles core user identity claims and complex impersonation scenarios. It adds essential claims including [`ImmyTenantId`](Implementations/UserRoleClaimsTransformation.cs:41), [`ImmyUserId`](Implementations/UserRoleClaimsTransformation.cs:42), and [`IsMsp`](Implementations/UserRoleClaimsTransformation.cs:43) from the authenticated user context. The transformation manages impersonation by adding comprehensive audit trail claims for both the impersonator and impersonated user, including Azure principal IDs, email addresses, and full names. Role claims are processed by removing existing role claims and adding fresh ones from the user's assigned roles, including all nested role claims. The component ensures claim consistency during impersonation scenarios and provides the foundation for tenant-based authorization decisions throughout the application.

### ImmenseUserClaimsTransformation  
The [`ImmenseUserClaimsTransformation`](Implementations/ImmenseUserClaimsTransformation.cs:14) manages global permissions for users within the Immense tenant who have elevated system access. It validates that users belong to the correct Azure AD tenant and are members of the global contributor group before adding management permissions. The transformation includes safety checks to prevent privilege escalation during impersonation scenarios, ensuring that global permissions are never granted when a user is being impersonated. OpenTelemetry integration provides observability into permission grants through activity tagging. This component is critical for system administration scenarios where users need cross-tenant management capabilities while maintaining proper security boundaries.

### ManagerClaimsTransformation
The [`ManagerClaimsTransformation`](Implementations/ManagerClaimsTransformation.cs:15) handles authorization for special system manager accounts that require elevated privileges for system operations. It validates the user's object ID against configured manager principal IDs and ensures they belong to the Immense tenant with the appropriate manager role claim. The transformation adds manager-specific permission claims that enable system-level operations and administrative functions. OpenTelemetry integration provides comprehensive logging of manager authentication events including tenant validation and role verification. This component ensures that only properly configured and authenticated system accounts can perform critical system management operations while maintaining full audit trails.

## File Organization

```
ClaimsTransformations/
├── Extensions/
│   └── ClaimsTransformationServiceRegistration.cs    # DI service registration
├── Implementations/
│   ├── CompositeClaimsTransformation.cs              # Main orchestrator using composite pattern
│   ├── UserRoleClaimsTransformation.cs               # Core identity and impersonation handling
│   ├── ImmenseUserClaimsTransformation.cs            # Global permissions for system users
│   └── ManagerClaimsTransformation.cs                # Manager system account permissions
└── README.md                                         # This documentation
```

## Additional Information

### Security Model
The ClaimsTransformations feature implements a comprehensive security model designed for multi-tenant SaaS environments. **Impersonation Security**: When impersonation occurs, the system creates detailed audit trails with claims prefixed by `impersonator:` and `impersonated:` to track both the original and target users. The [`IsImpersonated`](Implementations/UserRoleClaimsTransformation.cs:66) claim marks sessions for special handling in authorization policies. **Privilege Escalation Prevention**: Global permissions are automatically revoked during impersonation scenarios to prevent privilege escalation attacks. **Tenant Isolation**: The [`ImmyTenantId`](Implementations/UserRoleClaimsTransformation.cs:41) claim ensures strict tenant-based authorization boundaries, preventing cross-tenant data access.

### Integration Patterns
The feature integrates seamlessly with ASP.NET Core's authentication pipeline through [`IClaimsTransformation`](Implementations/CompositeClaimsTransformation.cs:6) interface implementation. **Service Registration**: All transformations are registered in [`ClaimsTransformationServiceRegistration`](Extensions/ClaimsTransformationServiceRegistration.cs:9) and included in the main RBAC service registration at [`RBACServiceRegistration.cs:37`](../Infrastructure/Extensions/RBACServiceRegistration.cs:37). **Dependency Injection**: Each transformation can inject required services like configuration options, permission providers, and HTTP context accessors. **Database Integration**: Transformations access user roles and permissions through the application's database context via HttpContext items.

### Multi-Tenant Considerations
The system supports complex multi-tenant scenarios with **MSP (Managed Service Provider) Architecture**: The [`IsMsp`](Implementations/UserRoleClaimsTransformation.cs:43) claim differentiates between end-client users and MSP administrators, enabling hierarchical permission models. **Tenant-Scoped Permissions**: All role-based claims are automatically scoped to the user's tenant context, preventing unauthorized cross-tenant access. **Global vs Tenant-Specific Rights**: System administrators can have global permissions across all tenants while regular users are restricted to their assigned tenant. **Impersonation Boundaries**: Impersonation is limited within tenant boundaries and includes comprehensive audit logging for compliance requirements.

## Extending this Feature

### Adding New Transformations

To add a new claims transformation to the pipeline:

1. **Create the Transformation Class**: Implement [`IClaimsTransformation`](Implementations/CompositeClaimsTransformation.cs:6) in a new class under `Implementations/`:

```csharp
public class CustomClaimsTransformation : IClaimsTransformation
{
    public Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
    {
        if (principal.Identity is not ClaimsIdentity identity) 
            return Task.FromResult(principal);
        
        // Add your custom claims logic here
        identity.AddClaim(new Claim("custom:permission", "value"));
        
        return Task.FromResult(principal);
    }
}
```

2. **Register the Service**: Add your transformation to [`ClaimsTransformationServiceRegistration.cs`](Extensions/ClaimsTransformationServiceRegistration.cs:9):

```csharp
services.AddScoped<IClaimsTransformation, CustomClaimsTransformation>();
```

3. **Consider Order**: Transformations execute in registration order, so consider dependencies between transformations.

### Custom Claim Logic

**Conditional Claims**: Use [`EnsureClaim`](Implementations/ImmenseUserClaimsTransformation.cs:28) helper method for conditional claim addition based on business logic. **OpenTelemetry Integration**: Add activity tags using [`Activity.Current?.AddTag`](Implementations/ImmenseUserClaimsTransformation.cs:49) for observability. **Configuration Integration**: Inject [`IOptions<T>`](Implementations/ImmenseUserClaimsTransformation.cs:15) for configuration-driven claim logic. **Security Validation**: Always validate user context and implement proper security checks before adding privileged claims. **Performance Considerations**: Keep transformation logic lightweight as it executes on every authenticated request.