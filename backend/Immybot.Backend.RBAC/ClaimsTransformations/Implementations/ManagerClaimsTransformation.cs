using System.Diagnostics;
using System.Security.Claims;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.Infrastructure.Configuration.AppSettingsBinders;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Shared.Primitives;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;

namespace Immybot.Backend.RBAC.ClaimsTransformations.Implementations;

/// <summary>
/// Transforms claims for the manager
/// </summary>
public class ManagerClaimsTransformation(
  IOptions<AzureActiveDirectoryAuthOptions> azureActiveDirectoryAuthOpts,
  IOptions<AppSettingsOptions> appSettingsOpts,
  IManagerManagePermission managerManagePermission) : IClaimsTransformation
{
  public Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
  {
    if (principal.Identity is not ClaimsIdentity claimsIdentity) return Task.FromResult(principal);

    claimsIdentity.EnsureClaim(
      isSet: ShouldHaveManagerManagePermission(principal),
      claimType: managerManagePermission.SubjectClaimType,
      claimValue: managerManagePermission.AllowClaim);

    return Task.FromResult(principal);
  }

  private bool ShouldHaveManagerManagePermission(ClaimsPrincipal principal)
  {
    var isManagerObjectId = string.Equals(principal.GetObjectId(),
      appSettingsOpts.Value.ImmyBotManagerAuthPrincipalObjectId,
      StringComparison.OrdinalIgnoreCase);

    var isInImmenseTenant = string.Equals(principal.GetTenantId(),
      azureActiveDirectoryAuthOpts.Value.ImmenseTenantId,
      StringComparison.OrdinalIgnoreCase);

    var hasRole = principal.IsInRole(AppConstants.ManagerRoleClaim);

    Activity.Current?.AddTag("auth.immy_manager.tenant_id", azureActiveDirectoryAuthOpts.Value.ImmenseTenantId);
    Activity.Current?.AddTag("auth.immy_manager.tenant_id_match", isInImmenseTenant);
    Activity.Current?.AddTag("auth.immy_manager.has_role", hasRole);

    return isManagerObjectId && isInImmenseTenant && hasRole;
  }
}
