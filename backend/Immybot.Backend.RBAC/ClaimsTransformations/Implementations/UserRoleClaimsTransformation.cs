using System.Security.Claims;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Models;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;

namespace Immybot.Backend.RBAC.ClaimsTransformations.Implementations;

/// <summary>
/// Claims transformation that handles user role claims and impersonation.
/// This transformation adds role claims from the user's roles and handles impersonation
/// by replacing the impersonator's claims with the impersonated user's claims.
/// </summary>
public class UserRoleClaimsTransformation(IHttpContextAccessor httpContextAccessor) : IClaimsTransformation
{
  private readonly IHttpContextAccessor _httpContextAccessor =
    httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));

  /// <summary>
  /// Transforms the principal by adding role claims and handling impersonation if applicable.
  /// </summary>
  /// <param name="principal">The current principal</param>
  /// <returns>The transformed principal with role claims and impersonation claims if applicable</returns>
  public Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
  {
    // Early return if principal is null or has no identity
    if (principal.Identity is not { IsAuthenticated: true })
      return Task.FromResult(principal);

    // Get the first claims identity (we assume there's only one in our authentication flow)
    if (principal.Identity is not ClaimsIdentity identity)
      return Task.FromResult(principal);

    // Get the current user from the HttpContext items
    var httpContext = _httpContextAccessor.HttpContext;
    if (httpContext == null || !httpContext.Items.TryGetValue("CurrentUser", out var userObj) ||
        userObj is not AuthUserDto user)
      return Task.FromResult(principal);

    // used in authorization handlers to get the user id and tenant id
    identity.AddClaim(new Claim(ClaimConstants.ImmyTenantId, user.TenantId.ToString()));
    identity.AddClaim(new Claim(ClaimConstants.ImmyUserId, user.Id.ToString()));
    identity.AddClaim(new Claim(ClaimConstants.IsMsp, user.IsMsp.ToString()));

    // If this is an impersonation scenario, add impersonation claims
    if (user.ImpersonatorUser != null)
      AddImpersonationClaims(identity, user.ImpersonatorUser, user);

    // Add role claims from the user's roles
    AddRoleClaims(identity, user);

    return Task.FromResult(principal);
  }

  /// <summary>
  /// Adds impersonation audit claims to the identity.
  /// </summary>
  /// <param name="identity">The claims identity to modify</param>
  /// <param name="impersonator">The impersonator user (the original authenticated user)</param>
  /// <param name="impersonatedUser">The user being impersonated (the current user)</param>
  private static void AddImpersonationClaims(ClaimsIdentity identity,
    AuthUserDto impersonator,
    AuthUserDto impersonatedUser)
  {
    // Add a claim to indicate this is an impersonated session
    identity.AddClaim(new Claim(ClaimConstants.IsImpersonated, "true"));

    // Add impersonator information for audit purposes
    identity.AddClaim(new Claim("impersonator:id", impersonator.Id.ToString()));

    if (!string.IsNullOrEmpty(impersonator.PrincipalId))
      identity.AddClaim(new Claim("impersonator:azure_principal_id", impersonator.PrincipalId));

    if (!string.IsNullOrEmpty(impersonator.Email))
      identity.AddClaim(new Claim("impersonator:email", impersonator.Email));

    if (!string.IsNullOrEmpty(impersonator.FirstName) && !string.IsNullOrEmpty(impersonator.LastName))
      identity.AddClaim(new Claim("impersonator:name",
        $"{impersonator.FirstName} {impersonator.LastName}"));


    // Add impersonated user information
    identity.AddClaim(new Claim("impersonated:id", impersonatedUser.Id.ToString()));

    if (!string.IsNullOrEmpty(impersonatedUser.PrincipalId))
      identity.AddClaim(new Claim("impersonated:azure_principal_id", impersonatedUser.PrincipalId));

    if (!string.IsNullOrEmpty(impersonatedUser.Email))
      identity.AddClaim(new Claim("impersonated:email", impersonatedUser.Email));

    if (!string.IsNullOrEmpty(impersonatedUser.FirstName) &&
        !string.IsNullOrEmpty(impersonatedUser.LastName))
      identity.AddClaim(new Claim("impersonated:name",
        $"{impersonatedUser.FirstName} {impersonatedUser.LastName}"));
  }

  /// <summary>
  /// Adds role claims from the user's roles to the identity.
  /// </summary>
  /// <param name="identity">The claims identity to modify</param>
  /// <param name="user">The user whose roles to add</param>
  private static void AddRoleClaims(ClaimsIdentity identity, AuthUserDto user)
  {
    // Early return if user has no roles
    if (user.Roles.Count is 0) return;

    // Remove any existing role claims to ensure we don't have duplicates
    // or leftover claims from the impersonator.
    // There shouldn't be any claims in here, but this is more of a precautionary measure.
    foreach (var roleClaim in identity.FindAll(ClaimTypes.Role).ToList())
      if (!roleClaim.Issuer.Equals(identity.AuthenticationType, StringComparison.OrdinalIgnoreCase))
        identity.RemoveClaim(roleClaim);

    // Add role claims from the passed in user's roles
    foreach (var role in user.Roles)
    {
      // Add role name as a role claim
      identity.AddClaim(new Claim(ClaimTypes.Role, role.Name));

      // Add all claims from the role
      foreach (var roleClaim in role.Claims)
      {
        identity.AddClaim(new Claim(roleClaim.Type, roleClaim.Value));
      }
    }
  }
}
