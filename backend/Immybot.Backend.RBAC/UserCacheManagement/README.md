# User Cache Management

## Context

In distributed RBAC (Role-Based Access Control) systems, user policy data is frequently cached to improve performance and reduce database load. However, when user permissions, roles, or related entities change, cached data can become stale, leading to security vulnerabilities and incorrect access decisions. Cache invalidation becomes critical to maintain data consistency and security integrity. The challenge lies in efficiently identifying when cached user policies need to be refreshed and ensuring all relevant cache entries are properly invalidated across the distributed system without impacting performance.

## Summary

The UserCacheManagement feature provides an event-driven cache invalidation system that maintains consistency of cached user policy data within the Immybot RBAC system. It automatically detects when user permissions, roles, or related entities change and proactively invalidates affected cache entries. The system handles both bulk cache invalidation for system-wide changes and granular single-user cache invalidation for specific user modifications. Additionally, it manages complex scenarios such as user impersonation, where changes to one user may affect multiple cache entries, ensuring comprehensive cache coherency across the entire authentication and authorization pipeline.

## Architecture

The system follows an event-driven architecture with Observer and Background Service patterns:

```mermaid
graph TD
    A[External Event Sources] --> B[Domain Events]
    B --> C[UserCacheInvalidationService]
    C --> D[UserPolicyCacheService]
    D --> E[IPolicyCacheStore]
    
    F[Role Changes] --> B
    G[User Assignments] --> B
    H[System Initialization] --> B
    
    I[InvalidateUserCacheRequestEvent] --> C
    J[InvalidateSingleUserCacheRequestEvent] --> C
    
    C --> K[Query Impersonators]
    K --> L[ImmybotDbContext]
    
    subgraph "Cache Invalidation Flow"
        D --> M[Cache Key Resolution]
        M --> N[Azure Principal ID]
        M --> O[Service Principal ID]
        M --> P[Email Address]
        
        D --> Q[Policy Cache Removal]
        Q --> E
    end
```

This architecture ensures loose coupling between event sources and cache invalidation logic. The background service continuously listens for domain events, while the cache service handles the actual invalidation operations. The system supports both immediate single-user invalidation and bulk operations for system-wide changes.

## Components

### [`UserCacheInvalidationService`](Implementations/UserCacheInvalidationService.cs:18)

**Type:** Background Service (Hosted Service)  
**Lifecycle:** Singleton

The orchestration service that coordinates cache invalidation operations. It subscribes to domain events using the Observer pattern and handles both bulk and granular cache invalidation requests. The service manages complex scenarios including user impersonation, where invalidating one user's cache requires identifying and invalidating all active impersonators' caches as well. It maintains event subscriptions throughout the application lifecycle and properly disposes of resources during shutdown to prevent memory leaks.

### [`UserPolicyCacheService`](Implementations/UserPolicyCacheService.cs:8)

**Type:** Cache Management Service  
**Lifecycle:** Singleton

The core cache invalidation engine responsible for executing cache removal operations. It implements intelligent cache key resolution using a hierarchical approach: Azure Principal ID, Service Principal ID, or Email Address. The service integrates directly with the IPolicyCacheStore to remove specific cache entries and can perform bulk cache clearing operations. It handles edge cases such as null users and missing identifiers gracefully, ensuring system stability during complex invalidation scenarios.

## File Organization

```
UserCacheManagement/
├── README.md                                    # This documentation
├── Extensions/
│   └── UserCacheManagementServiceConfiguration.cs  # DI container configuration
└── Implementations/
    ├── UserCacheInvalidationService.cs         # Background service for event handling
    └── UserPolicyCacheService.cs               # Cache invalidation operations
```

**Domain Layer:**
```
Immybot.Backend.RBAC.Domain/UserCacheManagement/
├── Events/
│   └── InvalidateUserCacheRequestEvent.cs      # Domain events for cache invalidation
└── Interfaces/
    └── IUserPolicyCacheService.cs               # Service interface contract
```

## Additional Information

### Cache Key Strategy

The system uses a composite cache key strategy based on available user identifiers:

1. **Primary:** [`user.Person.AzurePrincipalId`](Implementations/UserPolicyCacheService.cs:21) - Azure AD principal identifier
2. **Fallback:** [`user.ServicePrincipalId`](Implementations/UserPolicyCacheService.cs:23) - Service principal identifier  
3. **Suffix:** [`user.Person?.EmailAddress`](Implementations/UserPolicyCacheService.cs:31) - Email for additional uniqueness

**Cache Key Format:** `{ContextKeys.UserPrincipalId}:{userIdentifier}:{emailAddress}`

### Impersonation Handling

The system automatically handles user impersonation scenarios by:

1. Identifying active impersonators through [`UserImpersonations`](Implementations/UserCacheInvalidationService.cs:55) table
2. Filtering by [`ExpiresAtUtc > DateTime.UtcNow`](Implementations/UserCacheInvalidationService.cs:58) to find current sessions
3. Invalidating cache entries for both the impersonated user and all active impersonators
4. Using split queries for optimal performance with related entity loading

### Event Sources

Domain events are triggered from multiple sources within the system:

- **Role Management:** Changes to user roles and permissions
- **Assignment Operations:** Modifications to user-role assignments  
- **System Initialization:** Application startup and configuration changes
- **Administrative Actions:** Manual cache invalidation requests
- **Security Events:** Password changes, account lockouts, privilege escalations

## Extending this Feature

### Adding New Invalidation Triggers

To add new cache invalidation triggers:

1. **Create Domain Event:**
   ```csharp
   public record NewCacheInvalidationEvent(int AffectedUserId) : ApplicationEvent;
   ```

2. **Subscribe in [`UserCacheInvalidationService`](Implementations/UserCacheInvalidationService.cs:18):**
   ```csharp
   _newEventHandle = domainEventReceiver.Subscribe<NewCacheInvalidationEvent>(async ev =>
   {
       await InvalidateCacheForUser(ev.AffectedUserId, stoppingToken);
   });
   ```

3. **Dispose Subscription:**
   ```csharp
   _newEventHandle?.Dispose();
   ```

### Custom Cache Strategies

To implement custom cache invalidation strategies:

1. **Extend [`IUserPolicyCacheService`](../../../Immybot.Backend.RBAC.Domain/UserCacheManagement/Interfaces/IUserPolicyCacheService.cs:5):**
   ```csharp
   public interface IUserPolicyCacheService
   {
       void InvalidateUserPolicyCache(User? user);
       void InvalidateEntireCache();
       void InvalidateByCustomCriteria(CustomCriteria criteria); // New method
   }
   ```

2. **Implement in [`UserPolicyCacheService`](Implementations/UserPolicyCacheService.cs:8):**
   ```csharp
   public void InvalidateByCustomCriteria(CustomCriteria criteria)
   {
       // Custom invalidation logic
       var cacheKeys = ResolveCacheKeys(criteria);
       foreach (var key in cacheKeys)
       {
           policyCacheStore.RemoveCacheEntry(policyRegistry, PolicyKeys.AuthenticatedUserCachePolicy, key);
       }
   }
   ```

3. **Register Custom Implementation:**
   ```csharp
   services.AddSingleton<IUserPolicyCacheService, CustomUserPolicyCacheService>();