using Immybot.Backend.RBAC.Domain.UserCacheManagement.Interfaces;
using Immybot.Backend.RBAC.UserCacheManagement.Implementations;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.RBAC.UserCacheManagement.Extensions;

public static class UserCacheManagementServiceConfiguration
{
  public static IServiceCollection AddUserCacheManagement(this IServiceCollection services)
  {
    services.AddSingleton<IUserPolicyCacheService, UserPolicyCacheService>();
    services.AddHostedService<UserCacheInvalidationService>();

    return services;
  }
}
