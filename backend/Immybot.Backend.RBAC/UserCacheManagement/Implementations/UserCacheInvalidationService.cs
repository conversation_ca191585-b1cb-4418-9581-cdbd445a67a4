using Immybot.Backend.Application.DbContextExtensions.UserExtensions;
using Immybot.Backend.Domain.Events;
using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.UserCacheManagement.Events;
using Immybot.Backend.RBAC.Domain.UserCacheManagement.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;

namespace Immybot.Backend.RBAC.UserCacheManagement.Implementations;

/// <summary>
/// Service responsible for clearing the user policy cache when a request comes in.
/// </summary>
/// <param name="domainEventReceiver"></param>
/// <param name="userPolicyCacheService"></param>
/// <param name="contextFactory"></param>
public class UserCacheInvalidationService(
  IDomainEventReceiver domainEventReceiver,
  IUserPolicyCacheService userPolicyCacheService,
  IDbContextFactory<ImmybotDbContext> contextFactory) : BackgroundService
{
  private IDisposable? _invalidateUserCacheHandle;
  private IDisposable? _invalidateSingleUserCacheHandle;

  protected override async Task ExecuteAsync(CancellationToken stoppingToken)
  {
    await Task.Yield();

    _invalidateUserCacheHandle = domainEventReceiver.Subscribe<InvalidateUserCacheRequestEvent>(async ev =>
    {
      await InvalidateCacheForAllUsers(stoppingToken);
    });

    _invalidateSingleUserCacheHandle = domainEventReceiver.Subscribe<InvalidateSingleUserCacheRequestEvent>(async ev =>
    {
      await InvalidateCacheForUser(ev.UserId, stoppingToken);
    });
  }

  private async Task InvalidateCacheForAllUsers(CancellationToken _)
  {
    userPolicyCacheService.InvalidateEntireCache();
  }

  private async Task InvalidateCacheForUser(int userId, CancellationToken cancellation)
  {
    await using var dbContext = await contextFactory.CreateDbContextAsync(cancellation);
    var user = dbContext.GetUserById(userId, populatePerson: true);
    if (user is null) return;

    userPolicyCacheService.InvalidateUserPolicyCache(user);

    var impersonators = await dbContext
      .UserImpersonations
      .AsSplitQuery()
      .AsNoTracking()
      .Where(a => a.ImpersonatingUserId == user.Id && a.ExpiresAtUtc > DateTime.UtcNow)
      .Include(a => a.ImpersonatorUser)
      .ThenInclude(a => a!.Person)
      .Select(a => a.ImpersonatorUser!)
      .ToListAsync(cancellation);

    foreach (var impersonator in impersonators)
    {
      userPolicyCacheService.InvalidateUserPolicyCache(impersonator);
    }
  }

  public override Task StopAsync(CancellationToken cancellationToken)
  {
    try
    {
      _invalidateUserCacheHandle?.Dispose();
      _invalidateSingleUserCacheHandle?.Dispose();
    }
    catch (Exception)
    {
      // ignore
    }

    return Task.CompletedTask;
  }
}
