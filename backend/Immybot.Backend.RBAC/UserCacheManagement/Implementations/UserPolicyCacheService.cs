using Immybot.Backend.Application.Lib.Policies;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.UserCacheManagement.Interfaces;
using Polly.Registry;

namespace Immybot.Backend.RBAC.UserCacheManagement.Implementations
{
  public class UserPolicyCacheService(
    IPolicyCacheStore policyCacheStore,
    IPolicyRegistry<string> policyRegistry)
    : IUserPolicyCacheService
  {
    public void InvalidateUserPolicyCache(User? user)
    {
      // Check if we can obtain an identifier from either the Person record or ServicePrincipalId.
      if (user == null)
        return;

      string? userIdentifier = null;
      if (user.Person != null && user.Person.AzurePrincipalId != null)
        userIdentifier = user.Person.AzurePrincipalId;
      else if (user.ServicePrincipalId != null)
        userIdentifier = user.ServicePrincipalId;

      if (userIdentifier == null)
        return;

      policyCacheStore.RemoveCacheEntry(
        policyRegistry,
          PolicyKeys.AuthenticatedUserCachePolicy,
        $"{ContextKeys.UserPrincipalId}:{userIdentifier}:{user.Person?.EmailAddress}");
    }

    public void InvalidateEntireCache()
    {
      policyCacheStore.TryClear(policyRegistry, PolicyKeys.AuthenticatedUserCachePolicy);
    }
  }
}
