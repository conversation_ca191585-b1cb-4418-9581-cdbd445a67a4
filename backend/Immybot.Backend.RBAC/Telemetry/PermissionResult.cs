namespace Immybot.Backend.RBAC.Telemetry;

public static class PermissionResult
{
  public const string ResourceExplicitlyDenied = "resource_explicitly_denied";
  public const string TenantResourceDenied = "tenant_resource_denied";
  public const string ResourceAvailableToAll = "resource_available_to_all";
  public const string ResourceAllowed = "resource_allowed";
  public const string TenantResourceAllowed = "tenant_resource_allowed";
  public const string MyTenantResourceAllowed = "my_tenant_resource_allowed";
  public const string AllTenantResourceAllowed = "all_tenant_resource_allowed";
  public const string MissingUserIdClaim = "missing_user_id_claim";
  public const string ResourceCreatedByUserAllowed = "resource_created_by_user_allowed";
  public const string ResourceImplicitlyDenied = "resource_implicitly_denied";
  public const string NoSubjectAuthorizationRequired = "no_subject_authorization_required";
  public const string SubjectExplicitlyDenied = "subject_explicitly_denied";
  public const string SubjectPermissionIsMspOnly = "subject_permission_is_msp_only";
  public const string SubjectExplicitlyAllowed = "subject_explicitly_allowed";
  public const string DependentSubjectNotGranted = "dependent_subject_not_granted";
  public const string SubjectImplicitlyAllowed = "subject_implicitly_allowed";
}
