using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.RoleClaimMetadataService.Interfaces;
using Immybot.Backend.RBAC.Domain.RoleManagement.Interfaces;
using Immybot.Backend.RBAC.Domain.RoleManagement.Models;
using Immybot.Backend.RBAC.RolesManagement.Extensions;

namespace Immybot.Backend.RBAC.RolesManagement.CustomRoles.Implementations;

public class CreateRoleOperation(
  UserBearingDbFactory<ImmybotDbContext> ctxFactory,
  IRoleClaimMetadataService roleClaimMetadataService,
  TimeProvider timeProvider)
  : ICreateRoleOperation
{
  public async Task<Role> ExecuteAsync(
      CreateOrUpdateRoleRequest request,
      int? builtinRoleId = null,
      CancellationToken cancellationToken = default)
  {
    // Create context once outside the execution strategy
    await using var dbContext = ctxFactory();

    // --- Update Role Claims (Permissions) ---
    var roleClaims = new List<RoleClaim>();
    var roleClaimAudits = new HashSet<string>();
    var timestamp = timeProvider.GetUtcNow().UtcDateTime;
    foreach (var claimValue in request.GrantedPermissionIds)
    {
      var metadata = await roleClaimMetadataService.GetRoleClaimMetadata(claimValue, cancellationToken);

      // this needs to be updated to account for tenant permissions
      if (metadata is null) continue;

      roleClaimAudits.Add(metadata.PermissionMetadata.DisplayName);

      roleClaims.Add(new RoleClaim
      {
        ClaimType = metadata.ClaimType,
        ClaimValue = claimValue,
        CreatedDate = timestamp,
        UpdatedDate = timestamp,
      });
    }

    Role role = new()
    {
      Id = builtinRoleId ?? 0,
      Name = request.Name,
      NormalizedName = request.Name.ToUpper(),
      ConcurrencyStamp = Guid.NewGuid().ToString(),
      RoleTypeId = builtinRoleId.HasValue ? RoleType.BuiltIn : RoleType.Custom,
      Description = request.Description,
      CreatedDate = timestamp,
      UpdatedDate = timestamp,
      RoleClaims = roleClaims,
    };

    RoleAuditHelpers.ApplyAddedPermissionsCustomAudit(role, roleClaimAudits);

    dbContext.Roles.Add(role);

    await dbContext.SaveChangesAsync(cancellationToken);

    return role;
  }
}
