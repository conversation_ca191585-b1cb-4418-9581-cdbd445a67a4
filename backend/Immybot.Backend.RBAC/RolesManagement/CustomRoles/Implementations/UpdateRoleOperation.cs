using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.RoleClaimMetadataService.Interfaces;
using Immybot.Backend.RBAC.Domain.RoleManagement.Exceptions;
using Immybot.Backend.RBAC.Domain.RoleManagement.Interfaces;
using Immybot.Backend.RBAC.Domain.RoleManagement.Models;
using Immybot.Backend.RBAC.Domain.UserCacheManagement.Events;
using Immybot.Backend.RBAC.RolesManagement.Extensions;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.RBAC.RolesManagement.CustomRoles.Implementations
{
  /// <summary>
  /// Operation for updating an existing role.
  /// </summary>
  public class UpdateRoleOperation(
    UserBearingDbFactory<ImmybotDbContext> ctxFactory,
    IRoleClaimMetadataService roleClaimMetadataService,
    IDomainEventEmitter domainEventEmitter,
    TimeProvider timeProvider)
    : IUpdateRoleOperation
  {
    /// <summary>
    /// Executes the operation to update a role.
    /// </summary>
    /// <param name="roleId">The role ID.</param>
    /// <param name="request">The update request.</param>
    /// <param name="isCustomRole"></param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The updated role response.</returns>
    public async Task<Role> ExecuteAsync(
      int roleId,
      CreateOrUpdateRoleRequest request,
      bool isCustomRole = true,
      CancellationToken cancellationToken = default)
    {
      await using var dbContext = ctxFactory();

      // --- Load Role ---
      Role? role = await dbContext.Roles
        .AsSplitQuery()
        .Include(r => r.RoleClaims)
        .Include(r => r.UserRoles)
        .FirstOrDefaultAsync(r => r.Id == roleId, cancellationToken);

      if (role == null)
        throw new RoleNotFoundException(roleId);

      if (role.RoleTypeId == RoleType.BuiltIn && isCustomRole)
        throw new BuiltInRoleModificationException();

      if (string.IsNullOrEmpty(request.Name))
        throw new RoleArgumentNullException(nameof(request.Name));

      if (await dbContext.Roles.AnyAsync(r => r.Name == request.Name && r.Id != roleId, cancellationToken))
        throw new RoleNameExistsException(request.Name);

      // --- Update Scalar Properties ---
      var timestamp = timeProvider.GetUtcNow().UtcDateTime;
      role.Name = request.Name;
      role.NormalizedName = request.Name.ToUpperInvariant();
      role.Description = request.Description;

      // --- Update Role Claims (Permissions) ---
      var addedRoleClaimAudits = new HashSet<string>();
      var updatedRoleClaims = new List<RoleClaim>();

      // Build lookup of new claims by (ClaimType, ClaimValue) for efficient comparison
      var newClaimLookup = new Dictionary<(string ClaimType, string ClaimValue), string>();
      foreach (var claimValue in request.GrantedPermissionIds)
      {
        // The claimValue here is sort of like a PK in the claims listing since it should be unique
        // The claimValue is used in the frontend to select permissions to assign to the role
        var metadata = await roleClaimMetadataService.GetRoleClaimMetadata(claimValue, cancellationToken);
        if (metadata is null)
          continue;

        var key = (metadata.ClaimType, claimValue);
        if (!newClaimLookup.ContainsKey(key))
          newClaimLookup[key] = metadata.PermissionMetadata.DisplayName;
      }

      // Build lookup of existing claims by (ClaimType, ClaimValue) for efficient comparison
      var existingClaimLookup = new Dictionary<(string ClaimType, string ClaimValue), RoleClaim>();
      foreach (var existingClaim in role.RoleClaims)
      {
        var key = (existingClaim.ClaimType ?? string.Empty, existingClaim.ClaimValue ?? string.Empty);
        if (!existingClaimLookup.ContainsKey(key))
          existingClaimLookup[key] = existingClaim;
      }

      // Identify claims to add (exist in new but not in existing)
      foreach (var newClaim in newClaimLookup)
      {
        if (!existingClaimLookup.ContainsKey(newClaim.Key))
          addedRoleClaimAudits.Add(newClaim.Value);

        updatedRoleClaims.Add(new RoleClaim
        {
          RoleId = role.Id,
          ClaimType = newClaim.Key.ClaimType,
          ClaimValue = newClaim.Key.ClaimValue,
          CreatedDate = timestamp,
          UpdatedDate = timestamp,
        });
      }

      // Identify claims to remove (exist in existing but not in new)
      var oldRoleClaimAudits = new HashSet<string>();
      foreach (var existingClaim in existingClaimLookup)
      {
        if (!newClaimLookup.ContainsKey(existingClaim.Key))
        {
          try
          {
            // During role reconciliation, we need to audit what claims are being removed.
            // However, invalid claims (corrupted data, manual DB changes, etc.) may have null/empty values
            // that can't be parsed by the metadata service. We handle these gracefully to ensure
            // the reconciliation process completes successfully while still providing audit visibility.
            
            if (existingClaim.Key.ClaimValue is not null)
            {
              var metadata = await roleClaimMetadataService.GetRoleClaimMetadata(existingClaim.Key.ClaimValue, cancellationToken);
              if (metadata is not null)
                oldRoleClaimAudits.Add(metadata.PermissionMetadata.DisplayName);
            }
            else
            {
              // Null claim values can't be parsed, but we still want to audit their removal
              var claimType = existingClaim.Key.ClaimType ?? "(null)";
              oldRoleClaimAudits.Add($"Invalid claim [{claimType}:(null)]");
            }
          }
          catch (Exception)
          {
            // Catch any parsing exceptions from malformed claims and provide descriptive audit info
            // This ensures the reconciliation process doesn't fail due to invalid database state
            var claimType = existingClaim.Key.ClaimType ?? "(null)";
            var claimValue = existingClaim.Key.ClaimValue ?? "(null)";
            oldRoleClaimAudits.Add($"Invalid claim [{claimType}:{claimValue}]");
          }
        }
      }

      RoleAuditHelpers.ApplyRemovePermissionsCustomAudit(role, oldRoleClaimAudits);
      RoleAuditHelpers.ApplyAddedPermissionsCustomAudit(role, addedRoleClaimAudits);

      // clear existing role claims and add updated ones
      role.RoleClaims.Clear();
      foreach (var updatedRoleClaim in updatedRoleClaims)
        role.RoleClaims.Add(updatedRoleClaim);

      // used to trigger audit logging when only role claims have changed
      role.UpdatedDate = timestamp;

      // --- Save Changes ---
      await dbContext.SaveChangesAsync(cancellationToken);

      // Invalidate cached policies for all users.
      domainEventEmitter.EmitEvent(new InvalidateUserCacheRequestEvent());

      return role;
    }
  }
}
