using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.Persistence;
using Immybot.Backend.Persistence.Shared;
using Immybot.Backend.RBAC.Domain.RoleClaimMetadataService.Interfaces;
using Immybot.Backend.RBAC.Domain.RoleManagement.Exceptions;
using Immybot.Backend.RBAC.Domain.RoleManagement.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.Extensions;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.RBAC.RolesManagement.CustomRoles.Implementations;

public class CloneRoleOperation(
  UserBearingDbFactory<ImmybotDbContext> dbContextFactory,
  IRoleClaimMetadataService roleClaimMetadataService,
  TimeProvider timeProvider) : ICloneRoleOperation
{
  public async Task<Role> ExecuteAsync(
    int roleId,
    string newName,
    CancellationToken cancellationToken = default)
  {
    await using var dbContext = dbContextFactory();

    Role? sourceRole = await dbContext.Roles
      .Include(role => role.RoleType)
      .Include(role => role.RoleClaims)
      .Include(role => role.UserRoles)
      .FirstOrDefaultAsync(role => role.Id == roleId, cancellationToken);

    if (sourceRole is null)
      throw new RoleNotFoundException(roleId);

    bool roleNameExists = await dbContext.Roles.AnyAsync(r => r.Name == newName, cancellationToken);

    if (roleNameExists)
      throw new RoleNameExistsException(newName);

    var timestamp = timeProvider.GetUtcNow().UtcDateTime;

    var newRoleClaims = sourceRole.RoleClaims.Select(roleClaim => new RoleClaim
      {
        ClaimType = roleClaim.ClaimType,
        ClaimValue = roleClaim.ClaimValue,
        CreatedDate = timestamp,
        UpdatedDate = timestamp,
      })
      .ToList();

    var roleClaimAudits = new HashSet<string>();
    foreach (var roleClaim in newRoleClaims)
    {
      if (roleClaim.ClaimValue is null) continue;

      var metadata = await roleClaimMetadataService.GetRoleClaimMetadata(roleClaim.ClaimValue, cancellationToken);
      if (metadata is null) continue;
      roleClaimAudits.Add(metadata.PermissionMetadata.DisplayName);
    }

    Role newRole = new()
    {
      Name = newName,
      NormalizedName = newName.ToUpper(),
      ConcurrencyStamp = Guid.NewGuid().ToString(),
      RoleTypeId = RoleType.Custom,
      Description = sourceRole.Description,
      RoleClaims = newRoleClaims
    };

    RoleAuditHelpers.ApplyAddedPermissionsCustomAudit(newRole, roleClaimAudits);

    dbContext.Roles.Add(newRole);
    await dbContext.SaveChangesAsync(cancellationToken);
    return newRole;
  }
}
