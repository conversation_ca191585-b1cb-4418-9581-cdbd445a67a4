using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.RoleManagement.Exceptions;
using Immybot.Backend.RBAC.Domain.RoleManagement.Interfaces;
using Immybot.Backend.RBAC.Domain.UserCacheManagement.Events;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.RBAC.RolesManagement.CustomRoles.Implementations;

public class DeleteRoleOperation(
  Func<ImmybotDbContext> dbContextFactory,
  IDomainEventEmitter domainEventEmitter)
  : IDeleteRoleOperation
{
  public async Task ExecuteAsync(int roleId, CancellationToken cancellationToken = default)
  {
    await using var dbContext = dbContextFactory();

    var role = await dbContext.Roles
      .FirstOrDefaultAsync(a => a.Id == roleId, cancellationToken);

    if (role is null)
      throw new RoleNotFoundException(roleId);

    if (role.RoleTypeId == RoleType.BuiltIn)
      throw new BuiltInRoleModificationException(); // Prevent deletion of built-in roles.

    dbContext.Roles.Remove(role);
    await dbContext.SaveChangesAsync(cancellationToken);
    domainEventEmitter.EmitEvent(new InvalidateUserCacheRequestEvent());
  }
}
