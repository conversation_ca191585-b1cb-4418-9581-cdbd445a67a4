using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.RoleManagement.Interfaces;
using Immybot.Backend.RBAC.Domain.RoleManagement.Models;
using Immybot.Shared.Extensions;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.RBAC.RolesManagement.CustomRoles.Implementations;

public class GetRolesOperation(Func<ImmybotDbContext> dbContextFactory)
  : IGetRolesOperation
{
  public async Task<List<GetRoleResponse>> ExecuteAsync(CancellationToken cancellationToken = default)
  {
    return await dbContextFactory.With(async db =>
    {
      // Get all roles with their type information in a single query.
      // Load related RoleType
      return await db.Roles
        .Include(r => r.RoleType)
        .Include(r => r.UserRoles) // Include UserRoles for counting users
        .AsNoTracking() // Use AsNoTracking for read-only operations to improve performance
        .AsSplitQuery()
        .Select(a => new GetRoleResponse
        {
          Id = a.Id,
          Name = a.Name,
          Description = a.Description,
          RoleTypeId = a.RoleTypeId,
          RoleTypeName = a.RoleType!.Name,
          UserCount = a.UserRoles.Count,
          UpdatedDate = a.UpdatedDate,
          CreatedDate = a.CreatedDate,
          UpdatedBy = a.UpdatedByUser != null && a.UpdatedByUser.Person != null
            ? a.UpdatedByUser.Person.DisplayName
            : null,
          RoleClaims = a.RoleClaims.Select(r => new GetRoleClaimResponse
          {
            ClaimType = r.ClaimType, ClaimValue = r.ClaimValue
          }).ToList(),
        })
        .ToListAsync(cancellationToken);
    });
  }
}
