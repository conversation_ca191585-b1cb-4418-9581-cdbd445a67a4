using Immybot.Backend.RBAC.Domain.RoleManagement.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.CustomRoles.Implementations;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Immybot.Backend.RBAC.RolesManagement.CustomRoles.Extensions;

public static class RoleManagementServiceConfiguration
{
  public static IServiceCollection AddCustomRoleOperations(this IServiceCollection services)
  {
    services.TryAddSingleton(TimeProvider.System);
    services.AddScoped<ICreateRoleOperation, CreateRoleOperation>();
    services.AddScoped<IUpdateRoleOperation, UpdateRoleOperation>();
    services.AddScoped<IDeleteRoleOperation, DeleteRoleOperation>();
    services.AddScoped<IGetRoleOperation, GetRoleOperation>();
    services.AddScoped<IGetRolesOperation, GetRolesOperation>();
    services.AddScoped<ICloneRoleOperation, CloneRoleOperation>();
    return services;
  }
}
