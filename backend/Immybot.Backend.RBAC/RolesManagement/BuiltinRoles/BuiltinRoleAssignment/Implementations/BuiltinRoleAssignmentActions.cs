using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.Domain.Models.Preferences;
using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.UserCacheManagement.Events;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltinRoleAssignment.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;
using Microsoft.EntityFrameworkCore;

#pragma warning disable CS0618 // Type or member is obsolete

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltinRoleAssignment.Implementations;

/// <inheritdoc />
/// todo: this can be removed when the rbac feature flag is removed
public class BuiltinRoleAssignmentActions(
  ICachedSingleton<ApplicationPreferences> applicationPreferences,
  IDbContextFactory<ImmybotDbContext> dbContextFactory,
  IDomainEventEmitter domainEventEmitter,
  IFeatureTracker featureTracker,
  MspAdminBuiltinRole mspAdminBuiltinRole,
  MspUserBuiltinRole mspUserBuiltinRole,
  TenantAdminBuiltinRole tenantAdminBuiltinRole,
  TenantUserBuiltinRole tenantUserBuiltinRole,
  ScriptManagerBuiltinRole scriptManagerBuiltinRole,
  DeploymentManagerBuiltinRole deploymentManagerBuiltinRole,
  CrossTenantDeploymentManagerBuiltinRole crossTenantDeploymentManagerBuiltinRole,
  CrossTenantDeploymentChangeRequesterBuiltinRole crossTenantDeploymentChangeRequesterBuiltinRole,
  ComputerTerminalUserBuiltinRole computerTerminalUserBuiltinRole) : IBuiltinRoleAssignmentActions
{
  public async Task AssignBuiltinRoleToUserAsync(
    User user,
    bool emitInvalidateCacheEvent = true,
    CancellationToken cancellationToken = default)
  {
    await using var ctx = await dbContextFactory.CreateDbContextAsync(cancellationToken);

    var hasAtLeastOneRole =
      await ctx.UserRoles.AnyAsync(a => a.UserId == user.Id, cancellationToken);

    // if the user has at least one role assigned and the rbac feature is enabled,
    // we can assume the user has already been assigned the correct roles
    if (featureTracker.IsEnabled(FeatureEnum.RBACFeature) && hasAtLeastOneRole) return;

    // remove built-in role from user if it exists
    await ctx.UserRoles
      .Where(ur => ur.UserId == user.Id && ur.RoleId < 0)
      .ExecuteDeleteAsync(cancellationToken);

    var isMsp = await ctx.Tenants.AsNoTracking()
      .Where(a => a.Id == user.TenantId)
      .Select(a => a.IsMsp)
      .FirstOrDefaultAsync(cancellationToken);

    await AssignMainBuiltinRole(user: user, cancellationToken: cancellationToken, isMsp: isMsp, ctx: ctx);
    await HandleDeploymentManagerRole(user: user, cancellationToken: cancellationToken, ctx: ctx);
    await HandleScriptManagerAndComputerTerminalUserRole(user: user,
      isMsp: isMsp,
      cancellationToken: cancellationToken,
      ctx: ctx);
    await HandleCrossTenantDeploymentManagerRole(user: user, cancellationToken: cancellationToken, ctx: ctx);
    await HandleCrossTenantDeploymentChangeRequesterRole(user: user,
      cancellationToken: cancellationToken,
      isMsp: isMsp,
      ctx: ctx);

    if (emitInvalidateCacheEvent)
    {
      // emit invalidate cache event
      domainEventEmitter.EmitEvent(new InvalidateSingleUserCacheRequestEvent(user.Id));
    }
  }

  public async Task UpdateBuiltinRolesForAllUsersAsync(CancellationToken cancellationToken = default)
  {
    // only update built-in roles when the rbac feature flag is disabled
    if (featureTracker.IsEnabled(FeatureEnum.RBACFeature)) return;

    await using var ctx = await dbContextFactory.CreateDbContextAsync(cancellationToken);
    var users = await ctx.Users
      .AsNoTracking()
      .ToListAsync(cancellationToken);

    foreach (var user in users)
      await AssignBuiltinRoleToUserAsync(user, emitInvalidateCacheEvent: false, cancellationToken);

    // emit invalidate cache event
    domainEventEmitter.EmitEvent(new InvalidateUserCacheRequestEvent());
  }

  private async Task HandleCrossTenantDeploymentChangeRequesterRole(
    User user,
    bool isMsp,
    ImmybotDbContext ctx,
    CancellationToken cancellationToken)
  {
    // cross-tenant deployment change requester - msp only - when application preference is enabled, assign to non-admins
    if (isMsp && !user.IsAdmin &&
        applicationPreferences.Value.MspNonAdminsRequireChangeRequestsForCrossTenantDeployments)
    {
      await ctx.UserRoles.AddAsync(new UserRole
      {
        UserId = user.Id,
        RoleId = crossTenantDeploymentChangeRequesterBuiltinRole.Role.Id
      },
        cancellationToken);
      await ctx.SaveChangesAsync(cancellationToken);
    }
    else
    {
      await ctx.UserRoles
        .Where(a => a.UserId == user.Id && a.RoleId == crossTenantDeploymentChangeRequesterBuiltinRole.Role.Id)
        .ExecuteDeleteAsync(cancellationToken);
    }
  }

  private async Task HandleCrossTenantDeploymentManagerRole(
    User user,
    ImmybotDbContext ctx,
    CancellationToken cancellationToken)
  {
    // cross-tenant deployment manager - when application preference is enabled, assign to all users
    if (user is { IsAdmin: false, CanManageCrossTenantDeployments: true })
    {
      await ctx.UserRoles.AddAsync(new UserRole
      {
        UserId = user.Id,
        RoleId = crossTenantDeploymentManagerBuiltinRole.Role.Id
      },
        cancellationToken);
      await ctx.SaveChangesAsync(cancellationToken);
    }
    else
    {
      await ctx.UserRoles.Where(a => a.UserId == user.Id && a.RoleId == crossTenantDeploymentManagerBuiltinRole.Role.Id)
        .ExecuteDeleteAsync(cancellationToken);
    }
  }

  private async Task HandleScriptManagerAndComputerTerminalUserRole(
    User user,
    bool isMsp,
    ImmybotDbContext ctx,
    CancellationToken cancellationToken)
  {
    // script manager and computer terminal access  - when application preference is enabled, assign to all users
    // todo: remove when the new rbac ui is fully implemented
    if ((!isMsp || !user.IsAdmin) &&
        applicationPreferences.Value.AllowNonAdminsAndNonMspUsersToUseTerminalsAndEditScripts)
    {
      await ctx.UserRoles.AddAsync(new UserRole { UserId = user.Id, RoleId = scriptManagerBuiltinRole.Role.Id },
        cancellationToken);
      await ctx.UserRoles.AddAsync(new UserRole { UserId = user.Id, RoleId = computerTerminalUserBuiltinRole.Role.Id },
        cancellationToken);
      await ctx.SaveChangesAsync(cancellationToken);
    }
    else
    {
      await ctx.UserRoles.Where(a => a.UserId == user.Id && (a.RoleId == scriptManagerBuiltinRole.Role.Id ||
                                                             a.RoleId == computerTerminalUserBuiltinRole.Role.Id))
        .ExecuteDeleteAsync(cancellationToken);
    }
  }

  private async Task HandleDeploymentManagerRole(
    User user,
    ImmybotDbContext ctx,
    CancellationToken cancellationToken)
  {
    // deployment manager - non-admin user and can manage deployments preference is enabled
    // todo: remove when the new rbac ui is fully implemented
    if (!user.IsAdmin && applicationPreferences.Value.AllowNonAdminsToManageAssignments)
    {
      await ctx.UserRoles.AddAsync(new UserRole { UserId = user.Id, RoleId = deploymentManagerBuiltinRole.Role.Id },
        cancellationToken);
      await ctx.SaveChangesAsync(cancellationToken);
    }
    else
      await ctx.UserRoles.Where(a => a.UserId == user.Id && a.RoleId == deploymentManagerBuiltinRole.Role.Id)
        .ExecuteDeleteAsync(cancellationToken);
  }

  private async Task AssignMainBuiltinRole(
    User user,
    bool isMsp,
    ImmybotDbContext ctx,
    CancellationToken cancellationToken)
  {
    // assign one of the 4 built-in roles to the user

    var role = isMsp switch
    {
      true when user.IsAdmin => mspAdminBuiltinRole.Role,
      true => mspUserBuiltinRole.Role,
      false when user.IsAdmin => tenantAdminBuiltinRole.Role,
      false => tenantUserBuiltinRole.Role,
    };

    await ctx.UserRoles.AddAsync(new UserRole { UserId = user.Id, RoleId = role.Id }, cancellationToken);
    await ctx.SaveChangesAsync(cancellationToken);
  }
}
