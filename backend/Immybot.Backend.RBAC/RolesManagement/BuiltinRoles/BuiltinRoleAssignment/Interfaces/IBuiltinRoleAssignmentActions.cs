using Immybot.Backend.Domain.Models;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltinRoleAssignment.Interfaces;

/// <summary>
/// Assigns built-in roles to users.
/// </summary>
public interface IBuiltinRoleAssignmentActions
{
  /// <summary>
  /// Assigns the correct built-in role to a user.
  /// </summary>
  /// <param name="user"></param>
  /// <param name="emitInvalidateCacheEvent"></param>
  /// <param name="cancellationToken"></param>
  /// <returns></returns>
  Task AssignBuiltinRoleToUserAsync(
    User user,
    bool emitInvalidateCacheEvent = true,
    CancellationToken cancellationToken = default);

  Task UpdateBuiltinRolesForAllUsersAsync(
    CancellationToken cancellationToken = default);
}
