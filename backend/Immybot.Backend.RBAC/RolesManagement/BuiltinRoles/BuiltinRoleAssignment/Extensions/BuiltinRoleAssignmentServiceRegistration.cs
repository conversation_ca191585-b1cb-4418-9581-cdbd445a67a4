using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltinRoleAssignment.Implementations;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltinRoleAssignment.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltinRoleAssignment.Extensions;

public static class BuiltinRoleAssignmentServiceRegistration
{
  public static IServiceCollection AddBuiltInRoleAssignmentServices(this IServiceCollection services)
  {
    services.AddSingleton<IBuiltinRoleAssignmentActions, BuiltinRoleAssignmentActions>();
    services.AddHostedService<BuiltinRoleAssignmentService>();
    return services;
  }
}
