using Microsoft.Extensions.DependencyInjection;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations.Factories;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Interfaces;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Extensions;

/// <summary>
/// Extension methods for <see cref="IServiceCollection"/> to register built-in roles.
/// </summary>
public static class BuiltinRoleDefinitionsServiceRegistration
{
  /// <summary>
  /// Adds built-in roles to the service collection.
  /// </summary>
  /// <param name="services">The service collection.</param>
  /// <returns>The service collection.</returns>
  public static IServiceCollection AddBuiltinRolesServices(this IServiceCollection services)
  {
    if (services == null)
      throw new ArgumentNullException(nameof(services));

    // Register the built-in role factory
    services.AddSingleton<BuiltinRoleFactory>();

    // Automatically register all IBuiltinRole implementations
    var assembly = typeof(MspAdminBuiltinRole).Assembly;
    var builtinRoleTypes = assembly.GetTypes()
        .Where(t => !t.IsAbstract && !t.IsInterface && typeof(IBuiltinRole).IsAssignableFrom(t))
        .ToList();

    foreach (var builtinRoleType in builtinRoleTypes)
    {
      // Register by interface type
      services.AddSingleton(typeof(IBuiltinRole), builtinRoleType);

      // Also register by concrete type
      services.AddSingleton(builtinRoleType);
    }

    // additionally register the interface for msp admin built in role
    services.AddSingleton<IMspAdminBuiltinRole, MspAdminBuiltinRole>();

    return services;
  }
}
