using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Abstractions;

/// <summary>
/// Base abstract class for all built-in roles in ImmyBot.
/// Built-in roles are predefined in code and cannot be modified by users.
/// </summary>
public abstract class ImmyBotBuiltinRole(IServiceProvider serviceProvider, IResourceClaimProvider resourceClaimProvider) : IBuiltinRole
{
  protected readonly List<IPermissionMetadata> GrantedPermissions = new();

  /// <summary>
  /// Gets the Role domain model for this built-in role.
  /// This must be implemented by derived classes.
  /// </summary>
  public abstract Role Role { get; }

  /// <summary>
  /// Default creation date for built-in roles.
  /// </summary>
  protected static readonly DateTime DefaultDate = new DateTime(1900, 1, 1, 0, 0, 0, DateTimeKind.Utc);

  /// <summary>
  /// Adds a permission to this built-in role.
  /// </summary>
  protected void GrantPermission<T>() where T : class, IPermissionMetadata
  {
    var permission = serviceProvider.GetRequiredService<T>();
    if (!GrantedPermissions.Contains(permission))
      GrantedPermissions.Add(permission);

    foreach (var dependency in permission.Dependencies)
    {
      GrantPermissionDependencies(dependency);
    }
  }

  private void GrantPermissionDependencies(IPermissionMetadata dependency)
  {
    if (serviceProvider.GetRequiredService(dependency.GetType()) is not IPermissionMetadata permission) return;

    // do nothing if we already granted this permission
    if (GrantedPermissions.Contains(permission)) return;

    GrantedPermissions.Add(permission);
    foreach (var newDependency in permission.Dependencies)
    {
      GrantPermissionDependencies(newDependency);
    }
  }

  /// <summary>
  /// Builds the role claims based on the granted permissions.
  /// </summary>
  /// <param name="role">The role to add claims to.</param>
  /// <param name="useGlobalAccess">
  /// If true, grants global access (tenant:*:subject:permission:allow) for resource-based permissions.
  /// If false (default), grants tenant-scoped access (tenant:my:subject:permission:allow) that will be
  /// transformed to the user's actual tenant ID at runtime.
  /// </param>
  protected void BuildRoleClaims(Role role, bool useGlobalAccess = false)
  {
    // Add standard permission claims for feature access
    foreach (var permission in GrantedPermissions)
    {
      role.RoleClaims.Add(new RoleClaim
      {
        ClaimType = permission.SubjectClaimType,  // "subject:computers"
        ClaimValue = permission.AllowClaim,       // "computers:view:allow"
        CreatedDate = DefaultDate,
        UpdatedDate = DefaultDate
      });
    }

    // Add resource-based claims for resource access
    foreach (var permission in GrantedPermissions.Where(p => p is IResourceBased))
    {
      // By default, assume the role is scoped to a specific tenant.
      // If useGlobalAccess is set to true, then configure the more permissive global access
      var resourceClaim = useGlobalAccess
        ? resourceClaimProvider.GetAllTenantAllowClaim(permission)
        : resourceClaimProvider.GetMyTenantAllowClaim(permission);

      role.RoleClaims.Add(new RoleClaim
      {
        ClaimType = permission.TenantClaimType,   // "tenant:computers"
        ClaimValue = resourceClaim,               // "tenant:*:computers:view:allow" or "tenant:my:computers:view:allow"
        CreatedDate = DefaultDate,
        UpdatedDate = DefaultDate
      });
    }
  }

  /// <summary>
  /// Gets the permissions that have been granted to this built-in role.
  /// This includes both explicitly granted permissions and their dependencies.
  /// </summary>
  public IReadOnlyList<IPermissionMetadata> GetGrantedPermissions() => GrantedPermissions.AsReadOnly();
}
