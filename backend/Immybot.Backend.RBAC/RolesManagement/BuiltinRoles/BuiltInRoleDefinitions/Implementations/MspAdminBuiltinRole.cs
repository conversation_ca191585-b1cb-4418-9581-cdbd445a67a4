using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Scripts.Permissions;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Abstractions;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.ApplicationLocks.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.ApplicationPreferences.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Audit.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.AzureOperations.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Billing.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Branding.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Computers.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.DevLab.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.DynamicIntegrationTypes.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.GettingStarted.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Integrations.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.InventoryTasks.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Licenses.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.MaintenanceSessions.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.MaintenanceTasks.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Media.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Metrics.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Notifications.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.OAuth.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Persons.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Rbac.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Schedules.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.SmtpConfigurations.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Software.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Syncs.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.SystemOperations.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Tags.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.TenantPreferences.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Tenants.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Users.Permissions;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;

public interface IMspAdminBuiltinRole : IBuiltinRole;


/// <summary>
/// Built-in role for MSP Administrators.
/// MSP Administrators have full access to all features within their MSP.
/// </summary>
public class MspAdminBuiltinRole : ImmyBotBuiltinRole, IMspAdminBuiltinRole
{
  /// <inheritdoc/>
  public sealed override Role Role { get; } = new Role
  {
    Id = -1,
    Name = "MSP Administrator",
    NormalizedName = "MSP ADMINISTRATOR",
    Description = "Full access to all features within the MSP",
    RoleTypeId = RoleType.BuiltIn,
    CreatedDate = DefaultDate,
    UpdatedDate = DefaultDate
  };

  /// <summary>
  /// Initializes a new instance of the <see cref="MspAdminBuiltinRole"/> class.
  /// </summary>
  /// <param name="serviceProvider">Service provider for dependency injection.</param>
  /// <param name="resourceClaimProvider">Provider for generating resource-based claims.</param>
  public MspAdminBuiltinRole(IServiceProvider serviceProvider, IResourceClaimProvider resourceClaimProvider) : base(serviceProvider, resourceClaimProvider)
  {
    GrantPermission<ApplicationLocksViewPermission>();
    GrantPermission<ApplicationLocksManagePermission>();
    GrantPermission<AuditViewPermission>();
    GrantPermission<AzureOperationsViewPermission>();
    GrantPermission<AzureOperationsSyncContractsPermission>();
    GrantPermission<AzureOperationsUpdateContractLinksPermission>();
    GrantPermission<AzureOperationsUpdatePermissionLevelsPermission>();
    GrantPermission<BillingViewPermission>();
    GrantPermission<BillingManagePermission>();
    GrantPermission<BrandingViewPermission>();
    GrantPermission<BrandingManagePermission>();
    GrantPermission<BrandingSendTestEmailPermission>();
    GrantPermission<BrandingGlobalManagePermission>();
    GrantPermission<ComputersViewAgentStatusReportPermission>();
    GrantPermission<ComputersViewInventoryReportPermission>();
    GrantPermission<ComputersViewPermission>();
    GrantPermission<ComputersManagePermission>();
    GrantPermission<ComputerOnboardingPermission>();
    GrantPermission<ComputersManagePrimaryPersonPermission>();
    GrantPermission<ComputersRemoteAccessPermission>();
    GrantPermission<ComputersInventorySearchPermission>();
    GrantPermission<TriggerUserAffinitySyncPermission>();
    GrantPermission<TriggerAzureUserSyncPermission>();
    GrantPermission<ComputersUserAffinityPermission>();
    GrantPermission<ComputersExportPermission>();
    GrantPermission<ComputersViewRegistryPermission>();
    GrantPermission<ComputersAccessTerminalPermission>();
    GrantPermission<ComputersManageRegistryPermission>();
    GrantPermission<ComputerChangeTenantPermission>();
    GrantPermission<ComputerIdentifyAgentsPermission>();

    GrantPermission<DeploymentsViewIndividualPermission>();
    GrantPermission<DeploymentsViewSingleTenantPermission>();
    GrantPermission<DeploymentsViewCrossTenantPermission>();
    GrantPermission<DeploymentsManageIndividualPermission>();
    GrantPermission<DeploymentsManageSingleTenantPermission>();
    GrantPermission<DeploymentsManageCrossTenantPermission>();

    GrantPermission<DeploymentsOverridePermission>();
    GrantPermission<DeploymentsSupersedePermission>();
    GrantPermission<ChangeRequestManagePermission>();
    GrantPermission<DevLabManagePermission>();
    GrantPermission<DeploymentsManageCrossTenantPermission>();
    GrantPermission<DeploymentsManageMaintenanceItemOrderingPermission>();
    GrantPermission<DeploymentsManageRecommendationsPermission>();
    GrantPermission<DeploymentsViewTargetAssignmentsForVisibilityPermission>();
    GrantPermission<DeploymentsMigrateToIntegrationPermission>();
    GrantPermission<DeploymentsRunPermission>();

    GrantPermission<IntegrationsViewPermission>();
    GrantPermission<IntegrationsManagePermission>();
    GrantPermission<IntegrationsLinkClientsPermission>();
    GrantPermission<IntegrationsSyncAgentsPermission>();
    GrantPermission<IntegrationsViewPsaTicketsPermission>();

    GrantPermission<GettingStartedViewPermission>();
    GrantPermission<SoftwareViewPermission>();
    GrantPermission<SoftwareManagePermission>();
    GrantPermission<SoftwareUploadVersionsPermission>();

    GrantPermission<MaintenanceTasksManagePermission>();
    GrantPermission<MaintenanceTasksViewPermission>();

    GrantPermission<InventoryTasksViewPermission>();
    GrantPermission<InventoryTasksManagePermission>();

    GrantPermission<LicensesViewPermission>();
    GrantPermission<LicensesManagePermission>();
    GrantPermission<LicensesDownloadPermission>();

    GrantPermission<MaintenanceSessionsViewPermission>();
    GrantPermission<MaintenanceSessionsManagePermission>();

    GrantPermission<MaintenanceSessionsRerunPermission>();
    GrantPermission<MaintenanceSessionsResumePermission>();

    GrantPermission<MediaViewPermission>();
    GrantPermission<MediaManagePermission>();

    GrantPermission<MetricsViewPermission>();
    GrantPermission<MetricsManagePermission>();

    GrantPermission<OAuthViewPermission>();
    GrantPermission<OAuthManagePermission>();

    GrantPermission<PersonsViewPermission>();
    GrantPermission<PersonsManagePermission>();
    GrantPermission<UsersManageAccessRequestsPermission>();

    GrantPermission<ApplicationPreferencesViewPermission>();
    GrantPermission<ApplicationPreferencesManagePermission>();

    GrantPermission<TenantPreferencesViewPermission>();
    GrantPermission<TenantPreferencesManagePermission>();

    GrantPermission<ScheduleViewPermission>();
    GrantPermission<SchedulessManagePermission>();
    GrantPermission<ScriptsViewPermission>();
    GrantPermission<ScriptsManagePermission>();
    GrantPermission<ScriptsRunArbitraryPermission>();
    GrantPermission<ScriptCanAccessParentTenantPermission>();
    GrantPermission<SmtpConfigurationsViewPermission>();
    GrantPermission<SmtpConfigurationsManagePermission>();

    GrantPermission<SystemOperationsViewPermission>();
    GrantPermission<SystemOperationsRestartBackendPermission>();
    GrantPermission<SystemOperationsGetReleasesPermission>();
    GrantPermission<SystemOperationsPullUpdatesPermission>();
    GrantPermission<SystemOperationsFetchIpAddressesPermission>();
    GrantPermission<SystemOperationsManageSupportAccessPermission>();
    GrantPermission<SystemOperationsViewOpenScriptEditorsPermission>();

    GrantPermission<TagsViewPermission>();
    GrantPermission<TagsManagePermission>();

    GrantPermission<BrandingSetGlobalDefaultPermission>();

    GrantPermission<TenantsViewPermission>();
    GrantPermission<TenantsManagePermission>();
    GrantPermission<TenantsActivationControlPermission>();

    GrantPermission<UsersViewPermission>();
    GrantPermission<UsersManagePermission>();
    GrantPermission<UsersImpersonatePermission>();
    GrantPermission<UsersAssignRolesContainingCrossTenantPermissions>();

    GrantPermission<RbacViewPermission>();
    GrantPermission<RbacManagePermission>();

    GrantPermission<DynamicIntegrationTypesViewPermission>();
    GrantPermission<DynamicIntegrationTypesManagePermission>();

    GrantPermission<NotificationsViewAdminNotificationsPermission>();

    // Build the role claims with global access (useGlobalAccess = true)
    BuildRoleClaims(Role, useGlobalAccess: true);
  }
}
