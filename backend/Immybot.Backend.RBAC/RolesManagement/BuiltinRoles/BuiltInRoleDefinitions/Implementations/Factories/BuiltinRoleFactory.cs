using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations.Factories;

/// <summary>
/// Factory for creating built-in roles.
/// </summary>
public class BuiltinRoleFactory
{
  private readonly IServiceProvider _serviceProvider;

  /// <summary>
  /// Initializes a new instance of the <see cref="BuiltinRoleFactory"/> class.
  /// </summary>
  /// <param name="serviceProvider">The service provider.</param>
  public BuiltinRoleFactory(IServiceProvider serviceProvider)
  {
    _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
  }

  /// <summary>
  /// Gets all built-in roles.
  /// </summary>
  /// <returns>A collection of all built-in roles.</returns>
  public IEnumerable<Role> GetAllBuiltinRoles()
  {
    // Get all implementations of IBuiltinRole from the DI container
    var builtinRoles = _serviceProvider.GetServices<IBuiltinRole>();

    // Return the Role models
    return builtinRoles.Select(r => r.Role);
  }

  /// <summary>
  /// Gets all built-in role implementations.
  /// </summary>
  /// <returns>A collection of all built-in role implementations.</returns>
  public IEnumerable<IBuiltinRole> GetAllBuiltinRoleImplementations()
  {
    // Get all implementations of IBuiltinRole from the DI container
    return _serviceProvider.GetServices<IBuiltinRole>();
  }

  /// <summary>
  /// Gets a built-in role by type.
  /// </summary>
  /// <typeparam name="T">The type of built-in role to get.</typeparam>
  /// <returns>The built-in role.</returns>
  /// <exception cref="InvalidOperationException">Thrown when the built-in role is not found.</exception>
  public Role GetBuiltinRole<T>() where T : class, IBuiltinRole
  {
    // Get the specific implementation of IBuiltinRole from the DI container
    var builtinRole = _serviceProvider.GetService<T>();

    if (builtinRole == null)
    {
      throw new InvalidOperationException(
          $"Built-in role of type {typeof(T).Name} not found. " +
          "Make sure it is registered in the DI container.");
    }

    return builtinRole.Role;
  }
}
