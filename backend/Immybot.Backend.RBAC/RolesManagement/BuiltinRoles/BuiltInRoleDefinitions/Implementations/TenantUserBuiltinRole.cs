using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Scripts.Permissions;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Abstractions;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Computers.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Licenses.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.MaintenanceSessions.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.MaintenanceTasks.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Media.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.OAuth.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Persons.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Software.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Tags.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.TenantPreferences.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Tenants.Permissions;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;

/// <summary>
/// Built-in role for MSP Administrators.
/// MSP Administrators have full access to all features within their MSP.
/// </summary>
public class TenantUserBuiltinRole : ImmyBotBuiltinRole
{
  /// <inheritdoc/>
  public sealed override Role Role { get; } = new Role
  {
    Id = -4,
    Name = "Tenant User",
    NormalizedName = "TENANT USER",
    Description = "Limited access to features within your own tenant",
    RoleTypeId = RoleType.BuiltIn,
    CreatedDate = DefaultDate,
    UpdatedDate = DefaultDate
  };

  /// <summary>
  /// Initializes a new instance of the <see cref="TenantUserBuiltinRole"/> class.
  /// </summary>
  /// <param name="serviceProvider">Service provider for dependency injection.</param>
  /// <param name="resourceClaimProvider">Provider for generating resource-based claims.</param>
  public TenantUserBuiltinRole(IServiceProvider serviceProvider, IResourceClaimProvider resourceClaimProvider) : base(serviceProvider, resourceClaimProvider)
  {
    GrantPermission<ComputersViewPermission>();
    GrantPermission<ComputersManagePermission>();
    GrantPermission<ComputerOnboardingPermission>();
    GrantPermission<ComputersManagePrimaryPersonPermission>();
    GrantPermission<ComputersRemoteAccessPermission>();
    GrantPermission<ComputersViewInventoryReportPermission>();
    GrantPermission<ComputersViewAgentStatusReportPermission>();
    GrantPermission<DeploymentsViewIndividualPermission>();
    GrantPermission<DeploymentsViewSingleTenantPermission>();
    GrantPermission<DeploymentsRunPermission>();
    GrantPermission<DeploymentsOverridePermission>();
    GrantPermission<SoftwareViewPermission>();
    GrantPermission<SoftwareManagePermission>();
    GrantPermission<SoftwareUploadVersionsPermission>();
    GrantPermission<MaintenanceTasksManagePermission>();
    GrantPermission<MaintenanceTasksViewPermission>();
    GrantPermission<LicensesViewPermission>();
    GrantPermission<LicensesManagePermission>();
    GrantPermission<LicensesDownloadPermission>();
    GrantPermission<MaintenanceSessionsViewPermission>();
    GrantPermission<MaintenanceSessionsManagePermission>();
    GrantPermission<MaintenanceSessionsRerunPermission>();
    GrantPermission<MaintenanceSessionsResumePermission>();
    GrantPermission<MediaViewPermission>();
    GrantPermission<MediaManagePermission>();
    GrantPermission<OAuthViewPermission>();
    GrantPermission<PersonsViewPermission>();
    GrantPermission<PersonsManagePermission>();
    GrantPermission<TenantPreferencesViewPermission>();
    GrantPermission<TenantPreferencesManagePermission>();
    GrantPermission<ScriptsViewPermission>();
    GrantPermission<TagsViewPermission>();
    GrantPermission<TagsManagePermission>();
    GrantPermission<TenantsViewPermission>();

    // Build the role claims with tenant-scoped access (useGlobalAccess = false)
    BuildRoleClaims(Role, useGlobalAccess: false);
  }
}
