using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Abstractions;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;

/// <summary>
/// Built-in role for cross-tenant deployment change requesters.
/// </summary>
public class CrossTenantDeploymentChangeRequesterBuiltinRole : ImmyBotBuiltinRole
{
  /// <inheritdoc/>
  public sealed override Role Role { get; } = new Role
  {
    Id = -8,
    Name = "Cross-Tenant Deployment Change Requester",
    NormalizedName = "CROSS-TENANT DEPLOYMENT CHANGE REQUESTER",
    Description = "Requires change requests to manage (create, update, delete) cross-tenant deployments.",
    RoleTypeId = RoleType.BuiltIn,
    CreatedDate = DefaultDate,
    UpdatedDate = DefaultDate
  };

  /// <summary>
  /// Initializes a new instance of the <see cref="CrossTenantDeploymentChangeRequesterBuiltinRole"/> class.
  /// </summary>
  /// <param name="serviceProvider">Service provider for dependency injection.</param>
  /// <param name="resourceClaimProvider">Provider for generating resource-based claims.</param>
  public CrossTenantDeploymentChangeRequesterBuiltinRole(IServiceProvider serviceProvider, IResourceClaimProvider resourceClaimProvider) :
    base(
      serviceProvider, resourceClaimProvider)
  {
    // users with this role are required to submit change requests for cross-tenant deployments
    GrantPermission<DeploymentsManageCrossTenantWithChangeRequestsPermission>();

    // Build the role claims with global access (useGlobalAccess = true) for cross-tenant operations
    BuildRoleClaims(Role, useGlobalAccess: true);
  }
}
