using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Abstractions;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Computers.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Scripts.Permissions;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;

/// <summary>
/// Built-in role for computer terminal users.
/// </summary>
public class ComputerTerminalUserBuiltinRole : ImmyBotBuiltinRole
{
  /// <inheritdoc/>
  public sealed override Role Role { get; } = new Role
  {
    Id = -9,
    Name = "Computer Terminal User",
    NormalizedName = "COMPUTER TERMINAL USER",
    Description = "Can use the computer terminal on the computer details page",
    RoleTypeId = RoleType.BuiltIn,
    CreatedDate = DefaultDate,
    UpdatedDate = DefaultDate
  };

  /// <summary>
  /// Initializes a new instance of the <see cref="ComputerTerminalUserBuiltinRole"/> class.
  /// </summary>
  /// <param name="serviceProvider">Service provider for dependency injection.</param>
  public ComputerTerminalUserBuiltinRole(IServiceProvider serviceProvider, IResourceClaimProvider resourceClaimProvider) : base(
    serviceProvider, resourceClaimProvider)
  {
    GrantPermission<ComputersAccessTerminalPermission>();
    // Build the role claims with tenant-scoped access (useGlobalAccess = false)
    BuildRoleClaims(Role, useGlobalAccess: false);
  }
}
