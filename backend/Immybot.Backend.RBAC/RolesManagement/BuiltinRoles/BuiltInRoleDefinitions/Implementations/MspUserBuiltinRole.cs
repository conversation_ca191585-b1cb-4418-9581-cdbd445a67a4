using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Scripts.Permissions;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Abstractions;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.ApplicationPreferences.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.AzureOperations.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Computers.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.DynamicIntegrationTypes.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.GettingStarted.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Integrations.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Licenses.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.MaintenanceSessions.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.MaintenanceTasks.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Media.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Metrics.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.OAuth.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Persons.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Schedules.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Software.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.SystemOperations.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Tags.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.TenantPreferences.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Tenants.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Users.Permissions;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;

/// <summary>
/// Built-in role for MSP Users.
/// MSP Users have limited access to features within their MSP.
/// </summary>
public class MspUserBuiltinRole : ImmyBotBuiltinRole
{
  /// <inheritdoc/>
  public sealed override Role Role { get; } = new Role
  {
    Id = -2,
    Name = "MSP User",
    NormalizedName = "MSP USER",
    Description = "Limited access to features within the MSP",
    RoleTypeId = RoleType.BuiltIn,
    CreatedDate = DefaultDate,
    UpdatedDate = DefaultDate
  };

  /// <summary>
  /// Initializes a new instance of the <see cref="MspUserBuiltinRole"/> class.
  /// </summary>
  /// <param name="serviceProvider">Service provider for dependency injection.</param>
  /// <param name="resourceClaimProvider">Provider for generating resource-based claims.</param>
  public MspUserBuiltinRole(IServiceProvider serviceProvider, IResourceClaimProvider resourceClaimProvider) : base(serviceProvider, resourceClaimProvider)
  {
    GrantPermission<AzureOperationsSyncContractsPermission>();
    GrantPermission<AzureOperationsUpdateContractLinksPermission>();
    GrantPermission<ComputersViewPermission>();
    GrantPermission<ComputersManagePermission>();
    GrantPermission<ComputerOnboardingPermission>();
    GrantPermission<ComputersManagePrimaryPersonPermission>();
    GrantPermission<ComputersViewInventoryReportPermission>();
    GrantPermission<ComputersViewAgentStatusReportPermission>();
    GrantPermission<ComputersRemoteAccessPermission>();
    GrantPermission<DeploymentsOverridePermission>();
    GrantPermission<IntegrationsViewPermission>();
    GrantPermission<IntegrationsManagePermission>();
    GrantPermission<IntegrationsSyncAgentsPermission>();
    GrantPermission<IntegrationsViewPsaTicketsPermission>();
    GrantPermission<GettingStartedViewPermission>();
    GrantPermission<SoftwareViewPermission>();
    GrantPermission<SoftwareManagePermission>();
    GrantPermission<SoftwareUploadVersionsPermission>();
    GrantPermission<MaintenanceTasksManagePermission>();
    GrantPermission<MaintenanceTasksViewPermission>();
    GrantPermission<LicensesViewPermission>();
    GrantPermission<LicensesManagePermission>();
    GrantPermission<LicensesDownloadPermission>();
    GrantPermission<MaintenanceSessionsViewPermission>();
    GrantPermission<MaintenanceSessionsManagePermission>();
    GrantPermission<MaintenanceSessionsRerunPermission>();
    GrantPermission<MaintenanceSessionsResumePermission>();
    GrantPermission<MediaViewPermission>();
    GrantPermission<MediaManagePermission>();
    GrantPermission<MetricsViewPermission>();
    GrantPermission<MetricsManagePermission>();
    GrantPermission<OAuthViewPermission>();
    GrantPermission<OAuthManagePermission>();
    GrantPermission<PersonsViewPermission>();
    GrantPermission<PersonsManagePermission>();
    GrantPermission<UsersManageAccessRequestsPermission>();
    GrantPermission<ApplicationPreferencesViewPermission>();
    GrantPermission<ApplicationPreferencesManagePermission>();
    GrantPermission<TenantPreferencesViewPermission>();
    GrantPermission<TenantPreferencesManagePermission>();
    GrantPermission<ScheduleViewPermission>();
    GrantPermission<ScriptsViewPermission>();
    GrantPermission<SystemOperationsViewPermission>();
    GrantPermission<SystemOperationsFetchIpAddressesPermission>();
    GrantPermission<TagsViewPermission>();
    GrantPermission<TagsManagePermission>();
    GrantPermission<TenantsViewPermission>();
    GrantPermission<TenantsManagePermission>();
    GrantPermission<TenantsActivationControlPermission>();
    GrantPermission<DynamicIntegrationTypesViewPermission>();
    GrantPermission<DynamicIntegrationTypesManagePermission>();
    GrantPermission<DeploymentsViewTargetAssignmentsForVisibilityPermission>();
    GrantPermission<DeploymentsViewIndividualPermission>();
    GrantPermission<DeploymentsViewSingleTenantPermission>();
    GrantPermission<DeploymentsViewCrossTenantPermission>();
    GrantPermission<DeploymentsRunPermission>();

    // Build the role claims with global access (useGlobalAccess = true)
    BuildRoleClaims(Role, useGlobalAccess: true);
  }
}
