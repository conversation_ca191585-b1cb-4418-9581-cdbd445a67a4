using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Scripts.Permissions;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Abstractions;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Branding.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Computers.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Licenses.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.MaintenanceSessions.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.MaintenanceTasks.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Media.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.OAuth.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Persons.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.SmtpConfigurations.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Software.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Tags.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.TenantPreferences.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Tenants.Permissions;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Users.Permissions;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;

/// <summary>
/// Built-in role for MSP Administrators.
/// MSP Administrators have full access to all features within their MSP.
/// </summary>
public class TenantAdminBuiltinRole : ImmyBotBuiltinRole
{
  /// <inheritdoc/>
  public sealed override Role Role { get; } = new Role
  {
    Id = -3,
    Name = "Tenant Administrator",
    NormalizedName = "TENANT ADMINISTRATOR",
    Description = "Full access to all features within the scope of your own tenant",
    RoleTypeId = RoleType.BuiltIn,
    CreatedDate = DefaultDate,
    UpdatedDate = DefaultDate
  };

  /// <summary>
  /// Initializes a new instance of the <see cref="TenantAdminBuiltinRole"/> class.
  /// </summary>
  /// <param name="serviceProvider">Service provider for dependency injection.</param>
  /// <param name="resourceClaimProvider">Provider for generating resource-based claims.</param>
  public TenantAdminBuiltinRole(IServiceProvider serviceProvider, IResourceClaimProvider resourceClaimProvider) : base(serviceProvider, resourceClaimProvider)
  {
    GrantPermission<BrandingViewPermission>();
    GrantPermission<ComputersViewPermission>();
    GrantPermission<ComputersManagePermission>();
    GrantPermission<ComputerOnboardingPermission>();
    GrantPermission<ComputersManagePrimaryPersonPermission>();
    GrantPermission<ComputersRemoteAccessPermission>();
    GrantPermission<ComputersViewInventoryReportPermission>();
    GrantPermission<ComputersViewAgentStatusReportPermission>();
    GrantPermission<ComputersExportPermission>();
    GrantPermission<ComputersViewRegistryPermission>();
    GrantPermission<ComputersManageRegistryPermission>();
    GrantPermission<DeploymentsViewIndividualPermission>();
    GrantPermission<DeploymentsViewSingleTenantPermission>();
    GrantPermission<DeploymentsManageIndividualPermission>();
    GrantPermission<DeploymentsManageSingleTenantPermission>();
    GrantPermission<DeploymentsOverridePermission>();
    GrantPermission<DeploymentsSupersedePermission>();
    GrantPermission<DeploymentsRunPermission>();
    GrantPermission<SoftwareViewPermission>();
    GrantPermission<SoftwareManagePermission>();
    GrantPermission<SoftwareUploadVersionsPermission>();
    GrantPermission<MaintenanceTasksManagePermission>();
    GrantPermission<MaintenanceTasksViewPermission>();
    GrantPermission<LicensesViewPermission>();
    GrantPermission<LicensesManagePermission>();
    GrantPermission<LicensesDownloadPermission>();
    GrantPermission<MaintenanceSessionsViewPermission>();
    GrantPermission<MaintenanceSessionsManagePermission>();
    GrantPermission<MaintenanceSessionsRerunPermission>();
    GrantPermission<MaintenanceSessionsResumePermission>();
    GrantPermission<MediaViewPermission>();
    GrantPermission<MediaManagePermission>();
    GrantPermission<OAuthViewPermission>();
    GrantPermission<PersonsViewPermission>();
    GrantPermission<PersonsManagePermission>();
    GrantPermission<TenantPreferencesViewPermission>();
    GrantPermission<TenantPreferencesManagePermission>();
    GrantPermission<ScriptsViewPermission>();
    GrantPermission<SmtpConfigurationsViewPermission>();
    GrantPermission<SmtpConfigurationsManagePermission>();
    GrantPermission<TagsViewPermission>();
    GrantPermission<TagsManagePermission>();
    GrantPermission<TenantsViewPermission>();
    GrantPermission<UsersViewPermission>();
    GrantPermission<UsersManagePermission>();

    // Build the role claims with tenant-scoped access (useGlobalAccess = false)
    BuildRoleClaims(Role, useGlobalAccess: false);
  }
}
