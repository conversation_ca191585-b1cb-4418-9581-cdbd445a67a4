using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Abstractions;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Scripts.Permissions;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;

/// <summary>
/// Built-in role for script managers.
/// </summary>
public class ScriptManagerBuiltinRole : ImmyBotBuiltinRole
{
  /// <inheritdoc/>
  public sealed override Role Role { get; } = new Role
  {
    Id = -6,
    Name = "Script Manager",
    NormalizedName = "SCRIPT MANAGER",
    Description = "Can manage (create, update, delete) scripts.",
    RoleTypeId = RoleType.BuiltIn,
    CreatedDate = DefaultDate,
    UpdatedDate = DefaultDate
  };

  /// <summary>
  /// Initializes a new instance of the <see cref="ScriptManagerBuiltinRole"/> class.
  /// </summary>
  /// <param name="serviceProvider">Service provider for dependency injection.</param>
  /// <param name="resourceClaimProvider">Provider for generating resource-based claims.</param>
  public ScriptManagerBuiltinRole(IServiceProvider serviceProvider, IResourceClaimProvider resourceClaimProvider) : base(serviceProvider, resourceClaimProvider)
  {
    GrantPermission<ScriptsManagePermission>();
    // Build the role claims with tenant-scoped access (useGlobalAccess = false)
    BuildRoleClaims(Role, useGlobalAccess: false);
  }
}
