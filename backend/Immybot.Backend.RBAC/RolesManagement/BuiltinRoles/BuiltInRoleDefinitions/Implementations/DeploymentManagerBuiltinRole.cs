using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Abstractions;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;


namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;

/// <summary>
/// Built-in role for Deployment Manager.
/// </summary>
public class DeploymentManagerBuiltinRole : ImmyBotBuiltinRole
{
  /// <inheritdoc/>
  public sealed override Role Role { get; } = new Role
  {
    Id = -5,
    Name = "Deployment Manager",
    NormalizedName = "DEPLOYMENT MANAGER",
    Description = "Can manage (create, update, delete) deployments.",
    RoleTypeId = RoleType.BuiltIn,
    CreatedDate = DefaultDate,
    UpdatedDate = DefaultDate
  };

  /// <summary>
  /// Initializes a new instance of the <see cref="DeploymentManagerBuiltinRole"/> class.
  /// </summary>
  /// <param name="serviceProvider">Service provider for dependency injection.</param>
  /// <param name="resourceClaimProvider">Provider for generating resource-based claims.</param>
  public DeploymentManagerBuiltinRole(IServiceProvider serviceProvider, IResourceClaimProvider resourceClaimProvider) : base(
    serviceProvider, resourceClaimProvider)
  {
    GrantPermission<DeploymentsManageIndividualPermission>();
    GrantPermission<DeploymentsManageSingleTenantPermission>();

    // Build the role claims with tenant-scoped access (useGlobalAccess = false)
    BuildRoleClaims(Role, useGlobalAccess: false);
  }
}
