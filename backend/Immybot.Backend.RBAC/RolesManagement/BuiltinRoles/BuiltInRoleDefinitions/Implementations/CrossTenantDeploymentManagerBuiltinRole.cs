using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Abstractions;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.SubjectAuthorization.SubjectPermissions.Implementations.Deployments.Permissions;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations
{
  /// <summary>
  /// Built-in role for cross-tenant deployment managers.
  /// </summary>
  public class CrossTenantDeploymentManagerBuiltinRole : ImmyBotBuiltinRole
  {
    /// <inheritdoc/>
    public sealed override Role Role { get; } = new Role
    {
      Id = -7,
      Name = "Cross-Tenant Deployment Manager",
      NormalizedName = "CROSS-TENANT DEPLOYMENT MANAGER",
      Description = "Can manage (create, update, delete) all cross-tenant deployments.",
      RoleTypeId = RoleType.BuiltIn,
      CreatedDate = DefaultDate,
      UpdatedDate = DefaultDate
    };

    /// <summary>
    /// Initializes a new instance of the <see cref="CrossTenantDeploymentManagerBuiltinRole"/> class.
    /// </summary>
    /// <param name="serviceProvider">Service provider for dependency injection.</param>
    /// <param name="resourceClaimProvider">Provider for generating resource-based claims.</param>
    public CrossTenantDeploymentManagerBuiltinRole(IServiceProvider serviceProvider, IResourceClaimProvider resourceClaimProvider) : base(
      serviceProvider, resourceClaimProvider)
    {
      // users with this role can manage cross-tenant deployments (create, update, delete) without requiring change requests
      GrantPermission<DeploymentsManageCrossTenantPermission>();

      // Build the role claims with global access (useGlobalAccess = true) for cross-tenant operations
      BuildRoleClaims(Role, useGlobalAccess: true);
    }
  }
}
