using Immybot.Backend.Domain.Infrastructure;
using Immybot.Backend.Domain.Models.RBAC;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.Domain.RoleManagement.Interfaces;
using Immybot.Backend.RBAC.Domain.RoleManagement.Models;
using Immybot.Backend.RBAC.Domain.UserCacheManagement.Events;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltinRoleAssignment.Interfaces;
using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.BuiltInRoleDefinitions.Implementations;
using Immybot.Backend.RBAC.RolesManagement.CustomRoles.Implementations;
using Immybot.Shared.Services.Startup;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.SeedData.Implementations;

/// <summary>
/// This startup task is responsible for seeding the built-in roles and their claims.
/// On startup, this task will create or update the built-in roles in the database if any
/// of the data has changed. Note, the role claims are identified based on the claim value, not the role claim's id.
/// </summary>
/// <param name="ctxFactory"></param>
/// <param name="builtinRoleAssignmentActions"></param>
/// <param name="mspAdminBuiltinRole"></param>
/// <param name="mspUserBuiltinRole"></param>
/// <param name="tenantAdminBuiltinRole"></param>
/// <param name="tenantUserBuiltinRole"></param>
/// <param name="scriptManagerBuiltinRole"></param>
/// <param name="deploymentManagerBuiltinRole"></param>
/// <param name="crossTenantDeploymentManagerBuiltinRole"></param>
/// <param name="crossTenantDeploymentChangeRequesterBuiltinRole"></param>
/// <param name="computerTerminalUserBuiltinRole"></param>
public class RoleSeedDataStartupTask(
  IDbContextFactory<ImmybotDbContext> ctxFactory,
  IServiceScopeFactory serviceScopeFactory,
  IBuiltinRoleAssignmentActions builtinRoleAssignmentActions,
  IDomainEventEmitter domainEventEmitter,
  MspAdminBuiltinRole mspAdminBuiltinRole,
  MspUserBuiltinRole mspUserBuiltinRole,
  TenantAdminBuiltinRole tenantAdminBuiltinRole,
  TenantUserBuiltinRole tenantUserBuiltinRole,
  ScriptManagerBuiltinRole scriptManagerBuiltinRole,
  DeploymentManagerBuiltinRole deploymentManagerBuiltinRole,
  CrossTenantDeploymentManagerBuiltinRole crossTenantDeploymentManagerBuiltinRole,
  CrossTenantDeploymentChangeRequesterBuiltinRole crossTenantDeploymentChangeRequesterBuiltinRole,
  ComputerTerminalUserBuiltinRole computerTerminalUserBuiltinRole) : IStartupTask
{
  public async Task ExecuteAsync(CancellationToken cancellationToken = default)
  {
    await CreateOrUpdateRoleType(RoleType.BuiltIn, "Built-in", cancellationToken);
    await CreateOrUpdateRoleType(RoleType.Custom, "Custom", cancellationToken);
    _ = await CreateOrUpdateRole(mspAdminBuiltinRole.Role, cancellationToken);
    _ = await CreateOrUpdateRole(mspUserBuiltinRole.Role, cancellationToken);
    _ = await CreateOrUpdateRole(tenantAdminBuiltinRole.Role, cancellationToken);
    _ = await CreateOrUpdateRole(tenantUserBuiltinRole.Role, cancellationToken);
    _ = await CreateOrUpdateRole(scriptManagerBuiltinRole.Role, cancellationToken);
    _ = await CreateOrUpdateRole(deploymentManagerBuiltinRole.Role, cancellationToken);
    _ = await CreateOrUpdateRole(crossTenantDeploymentManagerBuiltinRole.Role, cancellationToken);
    _ = await CreateOrUpdateRole(crossTenantDeploymentChangeRequesterBuiltinRole.Role, cancellationToken);
    _ = await CreateOrUpdateRole(computerTerminalUserBuiltinRole.Role, cancellationToken);

    // migrate any existing pre-rbac users to one of the built-in roles
    await MigratePreRbacUsersToBuiltInRoles(cancellationToken);

    // send request to invalidate the user cache
    domainEventEmitter.EmitEvent(new InvalidateUserCacheRequestEvent());
  }

  /// <summary>
  /// handles migrating any pre-RBAC users with no roles to the built-in roles.
  /// </summary>
  /// <param name="cancellationToken"></param>
  private async Task MigratePreRbacUsersToBuiltInRoles(CancellationToken cancellationToken)
  {
    await using var ctx = await ctxFactory.CreateDbContextAsync(cancellationToken);
    var preRbacUsers = await ctx.Users
      .AsNoTracking()
      .Include(a => a.UserRoles)
      .Include(a => a.Tenant)
      .Where(a => a.HasManagementAccess && !a.UserRoles.Any())
      .ToListAsync(cancellationToken);

    foreach (var user in preRbacUsers)
    {
      await builtinRoleAssignmentActions.AssignBuiltinRoleToUserAsync(
        user,
        emitInvalidateCacheEvent: false,
        cancellationToken);
    }
  }

  private async Task CreateOrUpdateRoleType(int roleTypeId, string name, CancellationToken cancellationToken)
  {
    await using var ctx = await ctxFactory.CreateDbContextAsync(cancellationToken);
    var builtinRoleType = await ctx.RoleTypes.FirstOrDefaultAsync(a => a.Id == roleTypeId, cancellationToken);
    if (builtinRoleType is not null)
    {
      // nothing to do if name is the same
      if (builtinRoleType.Name == name) return;

      // update the name
      builtinRoleType.Name = name;
      ctx.Entry(builtinRoleType).State = EntityState.Modified;
    }
    else
    {
      // add the role type
      ctx.RoleTypes.Add(new RoleType { Id = roleTypeId, Name = name, });
    }

    await ctx.SaveChangesAsync(cancellationToken);
  }

  private async Task<Role> CreateOrUpdateRole(
    Role role,
    CancellationToken cancellationToken)
  {
    await using var scope = serviceScopeFactory.CreateAsyncScope();
    var createRoleOperation = scope.ServiceProvider.GetRequiredService<ICreateRoleOperation>();
    var updateRoleOperation = scope.ServiceProvider.GetRequiredService<IUpdateRoleOperation>();
    await using var ctx = await ctxFactory.CreateDbContextAsync(cancellationToken);

    var doesRoleExist = await ctx.Roles
      .AsNoTracking()
      .AnyAsync(a => a.Id == role.Id, cancellationToken);

    var request = new CreateOrUpdateRoleRequest
    {
      Name = role.Name ?? string.Empty,
      Description = role.Description,
      GrantedPermissionIds = role.RoleClaims.Select(a => a.ClaimValue ?? string.Empty).ToHashSet()
    };

    return doesRoleExist
      ? await updateRoleOperation.ExecuteAsync(
        role.Id,
        request,
        isCustomRole: false,
        cancellationToken: cancellationToken)
      : await createRoleOperation.ExecuteAsync(request, builtinRoleId: role.Id, cancellationToken: cancellationToken);
  }
}

