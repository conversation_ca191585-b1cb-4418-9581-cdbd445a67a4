using Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.SeedData.Implementations;
using Immybot.Shared.Services.Startup;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.RBAC.RolesManagement.BuiltinRoles.SeedData.Extensions;

/// <summary>
/// Extension methods for registering role seed data services.
/// </summary>
public static class SeedDataServiceConfiguration
{
  /// <summary>
  /// Adds role seed data services to the service collection.
  /// </summary>
  /// <param name="services">The service collection to add to.</param>
  /// <returns>The service collection for chaining.</returns>
  public static IServiceCollection AddRoleSeedDataServices(this IServiceCollection services)
  {
    // Needed for unit tests
    // Otherwise, only referenced as a startup task in host configuration
    services.AddTransient<RoleSeedDataStartupTask>();

    return services;
  }
}
