using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Implementations.Authorizers;
using Immybot.Backend.RBAC.ResourceAuthorization.Implementations.ClaimProviders;
using Immybot.Backend.RBAC.ResourceAuthorization.Implementations.CreatedByRelationshipProviders;
using Immybot.Backend.RBAC.ResourceAuthorization.Implementations.Handlers;
using Immybot.Backend.RBAC.ResourceAuthorization.Implementations.ResourceAccessors;
using Immybot.Backend.RBAC.ResourceAuthorization.Implementations.ResourceKeyProviders;
using Immybot.Backend.RBAC.ResourceAuthorization.Implementations.TenantRelationshipProviders;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Immybot.Backend.RBAC.ResourceAuthorization.Extensions;

/// <summary>
/// Extension methods to register resource authorization services with dynamic operations.
/// </summary>
public static class AuthorizationServiceRegistrationExtensions
{
  /// <summary>
  /// Adds dynamic resource operations to the dependency injection container.
  /// </summary>
  /// <param name="services">The service collection.</param>
  /// <returns>The service collection for chaining.</returns>
  public static IServiceCollection AddResourceAuthorization(this IServiceCollection services)
  {
    // Register resource key providers
    RegisterResourceKeyProviders(services);

    // Register tenant relationship providers
    RegisterTenantRelationshipProviders(services);

    // Register created by relationship providers
    RegisterCreatedByRelationshipProviders(services);

    // Register specialized handlers for resources with unique requirements
    services.TryAddSingleton<IResourceClaimProvider, ResourceClaimProvider>();

    // Register the standard resource authorizer
    services.TryAddScoped<IResourceAuthorizer, ResourceAuthorizer>();

    // Ensure the resource authorizer flow is registered if it hasn't been already
    services.TryAddScoped<IResourceAuthorizerFlow, ResourceAuthorizerFlow>();

    services.AddResourceAuthorizationFor<Computer, ComputerResourceAccessor>();
    services.AddResourceAuthorizationFor<LocalSoftware, LocalSoftwareResourceAccessor>();
    services.AddResourceAuthorizationFor<Script, ScriptResourceAccessor>();
    services.AddResourceAuthorizationFor<Branding, BrandingResourceAccessor>();
    services.AddResourceAuthorizationFor<MaintenanceTask, MaintenanceTaskResourceAccessor>();
    services.AddResourceAuthorizationFor<Tenant, TenantResourceAccessor>();
    services.AddResourceAuthorizationFor<ChangeRequest, ChangeRequestResourceAccessor>();
    services.AddResourceAuthorizationFor<License, LicenseResourceAccessor>();
    services.AddResourceAuthorizationFor<Person, PersonResourceAccessor>();
    services.AddResourceAuthorizationFor<ProviderLink, ProviderLinkResourceAccessor>();
    services.AddResourceAuthorizationFor<SmtpConfig, SmtpConfigResourceAccessor>();
    services.AddResourceAuthorizationFor<User, UserResourceAccessor>();
    services.AddResourceAuthorizationFor<Tag, TagResourceAccessor>();
    services.AddResourceAuthorizationFor<TargetAssignment, TargetAssignmentResourceAccessor>();
    services.AddResourceAuthorizationFor<Media, MediaResourceAccessor>();
    services.AddResourceAuthorizationFor<Schedule, ScheduleResourceAccessor>();
    services.AddResourceAuthorizationFor<MaintenanceSession, MaintenanceSessionResourceAccessor>();
    services.AddResourceAuthorizationFor<Oauth2AccessToken, Oauth2AccessTokenResourceAccessor>();
    services.AddResourceAuthorizationFor<ProviderAgent, ProviderAgentResourceAccessor>();

    return services;
  }

  /// <summary>
  /// Registers all components required for authorization for a specific resource type.
  /// </summary>
  /// <typeparam name="TResource">The resource type.</typeparam>
  /// <typeparam name="TResourceAccessor">The resource accessor type.</typeparam>
  /// <param name="services">The service collection.</param>
  /// <returns>The service collection for chaining.</returns>
  public static IServiceCollection AddResourceAuthorizationFor<TResource, TResourceAccessor>(
    this IServiceCollection services)
    where TResource : class
    where TResourceAccessor : class, IResourceAccessor<TResource>
  {
    // Register the resource accessor
    services.AddScoped<IResourceAccessor<TResource>, TResourceAccessor>();

    services.AddSingleton<IAuthorizationHandler, DefaultResourceAuthorizationHandler<TResource>>();

    return services;
  }

  private static void RegisterCreatedByRelationshipProviders(IServiceCollection services)
  {
    // Register default created by relationship provider (for resources with standard CreatedById property)
    services.AddSingleton(typeof(ICreatedByRelationshipProvider<>), typeof(DefaultCreatedByRelationshipProvider<>));

    // Register specialized created by relationship providers for specific resource types
    services
      .AddSingleton<ICreatedByRelationshipProvider<ChangeRequest>, ChangeRequestCreatedByRelationshipProvider>();
    services
      .AddSingleton<ICreatedByRelationshipProvider<Oauth2AccessToken>,
        Oauth2AccessTokenCreatedByRelationshipProvider>();

    // Add more specialized providers for other resource types as needed
  }

  /// <summary>
  /// Registers resource key providers for all supported resource types.
  /// </summary>
  private static void RegisterResourceKeyProviders(IServiceCollection services)
  {
    // Register default resource key provider (for resources with standard Id property)
    services.AddSingleton(typeof(IResourceKeyProvider<>), typeof(DefaultResourceKeyProvider<>));

    // Register specialized resource key providers for specific resource types

    services.AddSingleton<IResourceKeyProvider<ChangeRequest>, ChangeRequestResourceKeyProvider>();
    services.AddSingleton<IResourceKeyProvider<SmtpConfig>, SmtpConfigResourceKeyProvider>();
    services.AddSingleton<IResourceKeyProvider<ProviderAgent>, ProviderAgentResourceKeyProvider>();

    // Add more specialized providers for other resource types as needed
  }

  /// <summary>
  /// Registers tenant relationship providers for all supported resource types.
  /// </summary>
  private static void RegisterTenantRelationshipProviders(IServiceCollection services)
  {
    // Register default tenant relationship provider (for resources with standard TenantId property)
    services.AddSingleton(typeof(ITenantRelationshipProvider<>), typeof(DefaultTenantRelationshipProvider<>));

    // Register specialized tenant relationship providers for specific resource types
    services.AddSingleton<ITenantRelationshipProvider<LocalSoftware>, TenantSoftwareRelationshipProvider>();

    // join table relationship providers
    services
      .AddSingleton<ITenantRelationshipProvider<MaintenanceTask>,
        JoinTableTenantRelationshipProvider<MaintenanceTask, TenantMaintenanceTask>>();
    services
      .AddSingleton<ITenantRelationshipProvider<Tag>,
        JoinTableTenantRelationshipProvider<Tag, TenantTagAuthorization>>();
    services
      .AddSingleton<ITenantRelationshipProvider<Media>, JoinTableTenantRelationshipProvider<Media, TenantMedia>>();
    services
      .AddSingleton<ITenantRelationshipProvider<Script>, JoinTableTenantRelationshipProvider<Script, TenantScript>>();

    services.AddSingleton<ITenantRelationshipProvider<Tenant>, TenantRelationshipProvider>();
    services.AddSingleton<ITenantRelationshipProvider<ProviderLink>, TenantProviderLinkRelationshipProvider>();
    services.AddSingleton<ITenantRelationshipProvider<ProviderAgent>, ProviderAgentTenantRelationshipProvider>();

    // Add more specialized providers for other resource types as needed
  }
}
