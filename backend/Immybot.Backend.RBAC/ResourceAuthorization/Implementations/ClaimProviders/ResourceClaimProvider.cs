using System;
using System.Security.Claims;
using Immybot.Backend.Domain.Constants;
using Immybot.Backend.Domain.Helpers;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;

namespace Immybot.Backend.RBAC.ResourceAuthorization.Implementations.ClaimProviders;

/// <summary>
/// Implementation of the resource claim provider that works with any permission metadata instance.
/// </summary>
internal class ResourceClaimProvider : IResourceClaimProvider
{
    /// <inheritdoc/>
    public string GetTenantAllowClaim(int tenantId, IPermissionMetadata permission)
    {
        return $"tenant:{tenantId}:{permission.Subject.Name}:{permission.PermissionName}:allow";
    }

    /// <inheritdoc/>
    public string GetTenantDenyClaim(int tenantId, IPermissionMetadata permission)
    {
        return $"tenant:{tenantId}:{permission.Subject.Name}:{permission.PermissionName}:deny";
    }

    /// <inheritdoc/>
    public string GetAllTenantAllowClaim(IPermissionMetadata permission)
    {
        return $"tenant:*:{permission.Subject.Name}:{permission.PermissionName}:allow";
    }

    /// <inheritdoc/>
    public string GetMyTenantAllowClaim(IPermissionMetadata permission)
    {
        return $"tenant:my:{permission.Subject.Name}:{permission.PermissionName}:allow";
    }

    /// <inheritdoc/>
    public string GetResourceAllowClaim(string resourceKey, IPermissionMetadata permission)
    {
        return $"{permission.Subject.Name}:{resourceKey}:{permission.PermissionName}:allow";
    }

    /// <inheritdoc/>
    public string GetResourceDenyClaim(string resourceKey, IPermissionMetadata permission)
    {
        return $"{permission.Subject.Name}:{resourceKey}:{permission.PermissionName}:deny";
    }

    public bool CheckMyTenantAllowClaimForTenantId(ClaimsPrincipal claimsPrincipal,
      IPermissionMetadata permission,
      int tenantId)
    {
      // Get the "tenant:my" claim for this permission
      string myTenantClaim = GetMyTenantAllowClaim(permission);

      // Check if the user has the "tenant:my:subject:permission:allow" claim
      if (!claimsPrincipal.HasClaim(c => c.Value == myTenantClaim))
      {
        return false;
      }

      var tenantIdClaimValue = claimsPrincipal.GetClaimValue(ClaimConstants.ImmyTenantId);
      if (!int.TryParse(tenantIdClaimValue, out int currentUserTenantId))
      {
        // this should really not happen, but if it does, we can't authorize
        // todo: use assertions to ensure this never happens
        return false;
      }

      // The "my tenant" claim is valid only if the user's tenant matches the resource's tenant
      // This effectively transforms "tenant:my:subject:permission:allow" to "tenant:{userTenantId}:subject:permission:allow"
      // and then checks if userTenantId == resourceTenantId
      return currentUserTenantId == tenantId;
    }
}
