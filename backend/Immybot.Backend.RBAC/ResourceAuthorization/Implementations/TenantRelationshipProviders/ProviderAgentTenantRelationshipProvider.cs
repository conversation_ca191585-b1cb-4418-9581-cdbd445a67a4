using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Models;

namespace Immybot.Backend.RBAC.ResourceAuthorization.Implementations.TenantRelationshipProviders;

internal class ProviderAgentTenantRelationshipProvider : ITenantRelationshipProvider<ProviderAgent>
{
  public TenantRelationshipsForResource GetTenantIds(ProviderAgent resource, IPermissionMetadata permission)
  {
    var tenantId = resource.ProviderClient?.LinkedToTenantId;
    return tenantId.HasValue
      ? new TenantRelationshipsForResource(false, [tenantId.Value])
      : new TenantRelationshipsForResource(false, []);
  }
}
