using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Enums;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Models;

namespace Immybot.Backend.RBAC.ResourceAuthorization.Implementations.TenantRelationshipProviders;

/// <summary>
/// Implementation for entities that have a join table for tenant relationships
/// such as scripts, tags, media, and maintenance tasks.
/// </summary>
/// <typeparam name="TEntity"></typeparam>
/// <typeparam name="TRelationship"></typeparam>
internal class JoinTableTenantRelationshipProvider<TEntity, TRelationship> : ITenantRelationshipProvider<TEntity>
  where TEntity : class, ITenantRelationships<TRelationship>
  where TRelationship : class, ITenantRelationship
{
  public TenantRelationshipsForResource GetTenantIds(TEntity resource, IPermissionMetadata permission)
  {
    var tenantIds = permission.Category is PermissionCategory.View
      ? resource.TenantRelationships.Select(a => a.TenantId).ToList()
      : resource.TenantRelationships.Where(a => a.Relationship == Relationship.Owned).Select(a => a.TenantId)
        .ToList();

    // if the tag has no owner and no tenants, then it is available to all tenants for viewing
    var availableToAllTenants = tenantIds.Count is 0 && permission.Category is PermissionCategory.View;

    return new TenantRelationshipsForResource(availableToAllTenants, tenantIds.ToArray());
  }
}
