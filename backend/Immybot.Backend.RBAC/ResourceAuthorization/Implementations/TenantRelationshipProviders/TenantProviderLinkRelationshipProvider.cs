using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Models;

namespace Immybot.Backend.RBAC.ResourceAuthorization.Implementations.TenantRelationshipProviders;

/// <summary>
/// Custom implementation of ITenantRelationshipProvider for provider links that references link#OwnerTenantId
/// </summary>
internal class TenantProviderLinkRelationshipProvider : ITenantRelationshipProvider<ProviderLink>
{
  public TenantRelationshipsForResource GetTenantIds(ProviderLink resource, IPermissionMetadata permission)
    => new(false, [resource.OwnerTenantId]);
}
