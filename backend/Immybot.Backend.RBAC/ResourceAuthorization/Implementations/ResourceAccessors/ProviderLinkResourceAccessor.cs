using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.Persistence;
using Immybot.Backend.RBAC.ResourceAuthorization.Abstractions;
using JetBrains.Annotations;
using Microsoft.EntityFrameworkCore;

namespace Immybot.Backend.RBAC.ResourceAuthorization.Implementations.ResourceAccessors;

/// <summary>
/// Provides access to provider link resources by primary key using resource key parameters.
/// </summary>
/// <param name="dbContext"></param>
[UsedImplicitly]
internal class ProviderLinkResourceAccessor(
  Func<ImmybotDbContext> dbContext)
  : DefaultResourceAccessor<ProviderLink>
{
  protected override async Task<ProviderLink?> GetResourceByKeysInternalAsync(DefaultKeyParameters parameters)
  {
    await using var context = dbContext();
    var l = await context.ProviderLinks
      .AsNoTracking()
      .IgnoreQueryFilters()
      .Where(a => a.Id == parameters.Id)
      .Select(a => new { a.Id, a.Name, a.OwnerTenantId })
      .FirstOrDefaultAsync();

    return l is null ? null : new ProviderLink { Id = l.Id, OwnerTenantId = l.OwnerTenantId, Name = l.Name };
  }
}
