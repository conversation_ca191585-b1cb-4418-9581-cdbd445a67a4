using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Implementations.ExceptionFactories;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace Immybot.Backend.RBAC.ResourceAuthorization.Implementations.Authorizers;

/// <summary>
/// Orchestrates resource authorization workflows, combining resource retrieval and permission verification.
/// </summary>
internal class ResourceAuthorizerFlow(
  IResourceAuthorizer resourceAuthorizer,
  IPermissionProvider permissionProvider,
  IServiceProvider serviceProvider) : IResourceAuthorizerFlow
{
  /// <inheritdoc />
  public async Task<bool> PerformAuthorizationWorkflowAsync<TResource, TPermission>(
    IResourceKeyParameters parameters,
    bool strict = false)
    where TResource : class
    where TPermission : class, IPermissionMetadata, IResourceBased
  {
    var (_, isAuthorized) = await PerformAuthorizationWorkflowCoreAsync<TResource, TPermission>(parameters, strict);
    return isAuthorized;
  }

  /// <inheritdoc />
  public async Task<TResource> GetAuthorizedResourceOrFailAsync<TResource, TPermission>(
    IResourceKeyParameters parameters)
    where TResource : class
    where TPermission : class, IPermissionMetadata, IResourceBased
  {
    var (resource, _) = await PerformAuthorizationWorkflowCoreAsync<TResource, TPermission>(parameters, strict: true);
    return resource;
  }

  /// <summary>
  /// Core authorization workflow that retrieves the resource and performs authorization check.
  /// </summary>
  private async Task<(TResource resource, bool isAuthorized)> PerformAuthorizationWorkflowCoreAsync<TResource, TPermission>(
    IResourceKeyParameters parameters,
    bool strict = false)
    where TResource : class
    where TPermission : class, IPermissionMetadata, IResourceBased
  {
    // Get the appropriate resource accessor from the service provider
    var resourceAccessor = serviceProvider.GetRequiredService<IResourceAccessor<TResource>>();

    // Always get the full resource from the accessor
    var fullResource = await resourceAccessor.GetResourceByKeysAsync(parameters);

    // Handle not found case
    if (fullResource == null)
      throw ResourceExceptionFactory.CreateResourceNotFoundException(typeof(TResource), parameters);

    var isAuthorized = await resourceAuthorizer.CanPerformOperationAsync<TPermission>(fullResource);

    if (!isAuthorized && strict)
    {
      var permission = permissionProvider.GetPermissionMetadata<TPermission>();
      throw ResourceExceptionFactory.CreateAuthorizationFailedException(
        typeof(TResource),
        permission,
        parameters);
    }

    return (fullResource, isAuthorized);
  }
}
