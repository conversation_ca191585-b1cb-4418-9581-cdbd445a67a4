using Immybot.Backend.Domain.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.SubjectAuthorization.SubjectPermissions.Interfaces;
using Immybot.Backend.RBAC.ResourceAuthorization.Models;
using Microsoft.AspNetCore.Authorization;

namespace Immybot.Backend.RBAC.ResourceAuthorization.Implementations.Authorizers;

/// <summary>
/// Authorizes operations on resources based on user permissions.
/// </summary>
internal class ResourceAuthorizer(
  IAuthorizationService authService,
  IUserService userService,
  IPermissionProvider permissionProvider) : IResourceAuthorizer
{
  /// <inheritdoc />
  public async Task<bool> CanPerformOperationAsync<TPermission>(object resource)
    where TPermission : class, IPermissionMetadata
  {
    var permission = permissionProvider.GetPermissionMetadata<TPermission>();
    var user = userService.GetCurrentPrincipal(true)!;
    var requirement = new PermissionOperationRequirement(permission);
    var result = await authService.AuthorizeAsync(user, resource, requirement);
    return result.Succeeded;
  }
}
