using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;

namespace Immybot.Backend.RBAC.ResourceAuthorization.Implementations.CreatedByRelationshipProviders;

/// <summary>
///  CreatedByRelationshipProvider is disabled on all resources by default for security.
/// </summary>
/// <typeparam name="TResource">The resource to check for created by user</typeparam>
public class DefaultCreatedByRelationshipProvider<TResource> : ICreatedByRelationshipProvider<TResource>
  where TResource : class
{
  public int? GetCreatedByUserId(TResource resource) => null;
  public bool SupportsCreatedByRelationship => false;
}
