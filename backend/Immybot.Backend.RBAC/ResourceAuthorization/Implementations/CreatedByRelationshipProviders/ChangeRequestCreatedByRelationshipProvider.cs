using Immybot.Backend.Domain.Models;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;

namespace Immybot.Backend.RBAC.ResourceAuthorization.Implementations.CreatedByRelationshipProviders;

/// <summary>
/// Change requests support authorization by the user who created them.
/// </summary>
public class ChangeRequestCreatedByRelationshipProvider : ICreatedByRelationshipProvider<ChangeRequest>
{
  public int? GetCreatedByUserId(ChangeRequest resource) => resource.CreatedBy;
  public bool SupportsCreatedByRelationship => true;
}
