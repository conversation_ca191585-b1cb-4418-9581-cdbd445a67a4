using System;
using System.Threading.Tasks;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Interfaces;
using Immybot.Backend.RBAC.Domain.ResourceAuthorization.Models;
using Immybot.Backend.RBAC.ResourceAuthorization.Interfaces;

namespace Immybot.Backend.RBAC.ResourceAuthorization.Abstractions;

/// <summary>
/// Base class for resource accessors that handle resources with integer ID keys.
/// </summary>
/// <typeparam name="TResource">The type of resource being accessed.</typeparam>
public abstract class DefaultResourceAccessor<TResource> : IResourceAccessor<TResource>
    where TResource : class
{
    /// <inheritdoc />
    public async Task<TResource?> GetResourceByKeysAsync(IResourceKeyParameters parameters)
    {
        if (parameters is not DefaultKeyParameters defaultParams)
            throw new ArgumentException($"Invalid parameter type for {typeof(TResource).Name}. Expected DefaultKeyParameters but got {parameters.GetType().Name}.", nameof(parameters));
        
        var resource = await GetResourceByKeysInternalAsync(defaultParams);
        
        return resource;
    }
    
    /// <summary>
    /// Retrieves a resource by its key parameters. Implementations should return null if the resource is not found.
    /// </summary>
    /// <param name="parameters">The parameters that uniquely identify the resource.</param>
    /// <returns>The resource if found; otherwise, null.</returns>
    protected abstract Task<TResource?> GetResourceByKeysInternalAsync(DefaultKeyParameters parameters);
}
