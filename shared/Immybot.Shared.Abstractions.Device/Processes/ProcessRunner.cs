using JetBrains.Annotations;

#pragma warning disable S2344 // Enumeration type names should not have "Flags" or "Enum" suffixes.  Reason: https://learn.microsoft.com/en-us/windows/win32/procthread/process-creation-flags
namespace Immybot.Shared.Abstractions.Device.Processes;
// Copyright (c) 2013 <PERSON>
//
// Permission is hereby granted, free of charge, to any person obtaining a copy of this
// software and associated documentation files (the "Software"), to deal in the Software
// without restriction, including without limitation the rights to use, copy, modify, merge,
// publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons
// to whom the Software is furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in all copies or
// substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
// PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE
// FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
// DEALINGS IN THE SOFTWARE.

// Modifications for User-context execution & minor changes by Nicholas Lowrey-Dufour @ Immybot 2024
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.IO.Pipes;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Exceptions;
using Microsoft.Win32.SafeHandles;

public class ProcessRunner : IDisposable
{
  #region NativeEnums
  [Flags]
  public enum PROCESS_CREATION_FLAGS
  {
    CREATE_BREAKAWAY_FROM_JOB = 0x01000000,
    CREATE_DEFAULT_ERROR_MODE = 0x04000000,
    CREATE_NEW_CONSOLE = 0x00000010,
    CREATE_NEW_PROCESS_GROUP = 0x00000200,
    CREATE_NO_WINDOW = 0x08000000,
    CREATE_PROTECTED_PROCESS = 0x00040000,
    CREATE_PRESERVE_CODE_AUTHZ_LEVEL = 0x02000000,
    CREATE_SEPARATE_WOW_VDM = 0x00000800,
    CREATE_SHARED_WOW_VDM = 0x00001000,
    CREATE_SUSPENDED = 0x00000004,
    CREATE_UNICODE_ENVIRONMENT = 0x00000400,
    DEBUG_ONLY_THIS_PROCESS = 0x00000002,
    DEBUG_PROCESS = 0x00000001,
    DETACHED_PROCESS = 0x00000008,
    EXTENDED_STARTUPINFO_PRESENT = 0x00080000,
    INHERIT_PARENT_AFFINITY = 0x00010000
  }
  #endregion
  #region SafeProcessHandle
  [SecurityCritical]
  sealed class SafeProcessHandle : SafeHandleZeroOrMinusOneIsInvalid
  {
    // this private ctor is required for SafeHandle implementations
    SafeProcessHandle() : base(true)
    {
    }

    internal SafeProcessHandle(nint handle) : base(true)
    {
      SetHandle(handle);
    }

    [SecurityCritical]
    protected override bool ReleaseHandle()
    {
      return CloseHandle(handle);
    }
  }
  #endregion

  #region Native structures
  [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
  protected struct StartupInfo
  {
    public uint cb;
    public string lpReserved;
    public string lpDesktop;
    public string lpTitle;
    public uint dwX;
    public uint dwY;
    public uint dwXSize;
    public uint dwYSize;
    public uint dwXCountChars;
    public uint dwYCountChars;
    public uint dwFillAttribute;
    public uint dwFlags;
    public short wShowWindow;
    public short cbReserved2;
    public nint lpReserved2;
    public SafePipeHandle? hStdInput;
    public SafePipeHandle? hStdOutput;
    public SafePipeHandle? hStdError;
  }
  private enum SECURITY_IMPERSONATION_LEVEL
  {
    SecurityAnonymous = 0,
    SecurityIdentification = 1,
    SecurityImpersonation = 2,
    SecurityDelegation = 3,
  }
  private enum TOKEN_TYPE
  {
    TokenPrimary = 1,
    TokenImpersonation = 2
  }
  private enum SW
  {
    SW_HIDE = 0,
    SW_SHOWNORMAL = 1,
    SW_NORMAL = 1,
    SW_SHOWMINIMIZED = 2,
    SW_SHOWMAXIMIZED = 3,
    SW_MAXIMIZE = 3,
    SW_SHOWNOACTIVATE = 4,
    SW_SHOW = 5,
    SW_MINIMIZE = 6,
    SW_SHOWMINNOACTIVE = 7,
    SW_SHOWNA = 8,
    SW_RESTORE = 9,
    SW_SHOWDEFAULT = 10,
    SW_MAX = 10
  }

  private enum WTS_CONNECTSTATE_CLASS
  {
    WTSActive,
    WTSConnected,
    WTSConnectQuery,
    WTSShadow,
    WTSDisconnected,
    WTSIdle,
    WTSListen,
    WTSReset,
    WTSDown,
    WTSInit
  }

  [StructLayout(LayoutKind.Sequential)]
  private struct WtsSessionInfo
  {
    public readonly uint SessionID;

    [MarshalAs(UnmanagedType.LPStr)]
    public readonly string pWinStationName;

    public readonly WTS_CONNECTSTATE_CLASS State;
  }

  [StructLayout(LayoutKind.Sequential)]
  protected struct ProcessInformation
  {
    public nint hProcess;
    public nint hThread;
    public int dwProcessId;
    public int dwThreadId;
  }
  #endregion

  #region Native methods
  [DllImport("kernel32.dll", EntryPoint = "CreateProcess", CharSet = CharSet.Unicode, SetLastError = true)]
  [return: MarshalAs(UnmanagedType.Bool)]
  static extern bool NativeCreateProcess(
    string? lpApplicationName,
    StringBuilder lpCommandLine,
    nint lpProcessAttributes,
    nint lpThreadAttributes,
    [MarshalAs(UnmanagedType.Bool)] bool bInheritHandles,
    uint dwCreationFlags,
    string? lpEnvironment,
    string? lpCurrentDirectory,
    [In] ref StartupInfo lpStartupInfo,
    out ProcessInformation lpProcessInformation
  );

  [DllImport("advapi32.dll", EntryPoint = "CreateProcessAsUser", SetLastError = true, CharSet = CharSet.Unicode)]
  private static extern bool NativeCreateProcessAsUser(
    SafeAccessTokenHandle hToken,
    string? lpApplicationName,
    StringBuilder lpCommandLine,
    nint lpProcessAttributes,
    nint lpThreadAttributes,
    bool bInheritHandle,
    uint dwCreationFlags,
    string lpEnvironment,
    string? lpCurrentDirectory,
    [In] ref StartupInfo lpStartupInfo,
    out ProcessInformation lpProcessInformation);

  [DllImport("kernel32.dll", SetLastError = true)]
  [return: MarshalAs(UnmanagedType.Bool)]
  static extern bool TerminateProcess(SafeProcessHandle processHandle, int exitCode);

  [DllImport("kernel32.dll", SetLastError = true)]
  [return: MarshalAs(UnmanagedType.Bool)]
  static extern bool GetExitCodeProcess(SafeProcessHandle processHandle, out int exitCode);

  [DllImport("kernel32.dll", SetLastError = true)]
  static extern nint GetStdHandle(int nStdHandle);

  [DllImport("shell32.dll", SetLastError = true, CharSet = CharSet.Unicode)]
  static extern unsafe char** CommandLineToArgvW([MarshalAs(UnmanagedType.LPWStr)] string lpCmdLine, out int pNumArgs);

  [DllImport("kernel32.dll")]
  static extern nint LocalFree(nint hMem);

  [DllImport("kernel32", SetLastError = true)]
  [return: MarshalAs(UnmanagedType.Bool)]
  static extern bool CloseHandle(nint hObject);

  [DllImport("advapi32.dll", SetLastError = true)]
  static extern bool OpenProcessToken(nint ProcessHandle, uint DesiredAccess, out SafeAccessTokenHandle TokenHandle);

  [DllImport("kernel32.dll", SetLastError = true)]
  static extern nint GetCurrentProcess(); // returns a pseudohandle, no need for SafeHandle

  [DllImport("kernel32.dll")]
  private static extern uint WTSGetActiveConsoleSessionId();

  [DllImport("Wtsapi32.dll", SetLastError = true)]
  private static extern bool WTSQueryUserToken(uint SessionId, out SafeAccessTokenHandle phToken);

  [DllImport("advapi32.dll", SetLastError = true)]
  private static extern bool DuplicateTokenEx(
    SafeAccessTokenHandle hExistingToken,
    uint dwDesiredAccess,
    nint lpThreadAttributes,
    SECURITY_IMPERSONATION_LEVEL ImpersonationLevel,
    TOKEN_TYPE TokenType,
    out SafeAccessTokenHandle phNewToken);

  [DllImport("wtsapi32.dll", SetLastError = true)]
  private static extern bool WTSEnumerateSessions(
      nint hServer,
      int Reserved,
      int Version,
      ref nint ppSessionInfo,
      ref int pCount);

  [DllImport("userenv.dll", SetLastError = true)]
  private static extern bool CreateEnvironmentBlock(out nint lpEnvironment, SafeAccessTokenHandle hToken, bool bInherit);

  [DllImport("userenv.dll", SetLastError = true)]
  private static extern bool DestroyEnvironmentBlock(nint lpEnvironment);


  private const int DUPLICATE_SAME_ACCESS = 2;
  private const uint INVALID_SESSION_ID = 0xFFFFFFFF;
  private static readonly nint WTS_CURRENT_SERVER_HANDLE = nint.Zero;
  private const uint TOKEN_ASSIGN_PRIMARY = 0x0001;
  private const uint TOKEN_DUPLICATE = 0x0002;
  private const uint TOKEN_IMPERSONATE = 0x0004;
  private const uint TOKEN_QUERY = 0x0008;

  [DllImport("kernel32.dll")]
  static extern int GetOEMCP();
  #endregion

  #region CommandLine <-> Argument Array
  /// <summary>
  /// Decodes a command line into an array of arguments according to the CommandLineToArgvW rules.
  /// </summary>
  /// <remarks>
  /// Command line parsing rules:
  /// - 2n backslashes followed by a quotation mark produce n backslashes, and the quotation mark is considered to be the end of the argument.
  /// - (2n) + 1 backslashes followed by a quotation mark again produce n backslashes followed by a quotation mark.
  /// - n backslashes not followed by a quotation mark simply produce n backslashes.
  /// </remarks>
  public static unsafe string[] CommandLineToArgumentArray(string commandLine)
  {
    if (string.IsNullOrEmpty(commandLine))
      return Array.Empty<string>();
    int numberOfArgs;
    var arr = CommandLineToArgvW(commandLine, out numberOfArgs);
    if (arr == null)
      throw new Win32Exception();
    try
    {
      var result = new string[numberOfArgs];
      for (var i = 0; i < numberOfArgs; i++)
      {
        result[i] = new string(arr[i]);
      }
      return result;
    }
    finally
    {
      // Free memory obtained by CommandLineToArgW.
      LocalFree(new nint(arr));
    }
  }

  static readonly char[] charsNeedingQuoting = { ' ', '\t', '\n', '\v', '"' };

  /// <summary>
  /// Escapes a set of arguments according to the CommandLineToArgvW rules.
  /// </summary>
  /// <remarks>
  /// Command line parsing rules:
  /// - 2n backslashes followed by a quotation mark produce n backslashes, and the quotation mark is considered to be the end of the argument.
  /// - (2n) + 1 backslashes followed by a quotation mark again produce n backslashes followed by a quotation mark.
  /// - n backslashes not followed by a quotation mark simply produce n backslashes.
  /// </remarks>
  public static string? ArgumentArrayToCommandLine(params string[] arguments)
  {
    if (arguments == null)
      return null;
    var b = new StringBuilder();
    for (var i = 0; i < arguments.Length; i++)
    {
      if (i > 0)
        b.Append(' ');
      AppendArgument(b, arguments[i]);
    }
    return b.ToString();
  }

  static void AppendArgument(StringBuilder b, string arg)
  {
    if (arg.Length > 0 && arg.IndexOfAny(charsNeedingQuoting) < 0)
    {
      b.Append(arg);
    }
    else
    {
      b.Append('"');
      for (var j = 0; ; j++)
      {
        var backslashCount = 0;
        while (j < arg.Length && arg[j] == '\\')
        {
          backslashCount++;
          j++;
        }
        if (j == arg.Length)
        {
          b.Append('\\', backslashCount * 2);
          break;
        }
        else if (arg[j] == '"')
        {
          b.Append('\\', (backslashCount * 2) + 1);
          b.Append('"');
        }
        else
        {
          b.Append('\\', backslashCount);
          b.Append(arg[j]);
        }
      }
      b.Append('"');
    }
  }
  #endregion

  #region Start Info Properties
  /// <summary>
  /// Gets or sets the process's working directory.
  /// </summary>
  public string? WorkingDirectory { get; set; }

  PROCESS_CREATION_FLAGS CreationFlags { get; set; } = PROCESS_CREATION_FLAGS.CREATE_NO_WINDOW;

  IDictionary<string, string>? environmentVariables;

  public IDictionary<string, string> EnvironmentVariables
  {
    get
    {
      if (environmentVariables == null)
      {
        environmentVariables = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
        foreach (DictionaryEntry e in Environment.GetEnvironmentVariables())
        {
          environmentVariables.Add((string)e.Key, (string)e.Value!);
        }
      }
      return environmentVariables;
    }
  }

  public string? CommandLine { get; set; }

  public bool RedirectStandardOutput { get; set; }
  public bool RedirectStandardError { get; set; }

  /// <summary>
  /// Gets whether to use a single stream for both stdout and stderr.
  /// </summary>
  public bool RedirectStandardOutputAndErrorToSingleStream { get; set; }

  public uint ProcessId { get; private set; }
  #endregion

  #region Start
  bool wasStarted;
  SafeProcessHandle? safeProcessHandle;

  public uint Start(string program, bool asUser = false, params string[] arguments)
  {
    var commandLine = new StringBuilder();
    AppendArgument(commandLine, program);
    if (arguments != null)
    {
      for (var i = 0; i < arguments.Length; i++)
      {
        commandLine.Append(' ');
        AppendArgument(commandLine, arguments[i]);
      }
    }
    return StartCommandLine(commandLine.ToString(), asUser);
  }

  public uint StartCommandLine(string commandLine, bool asUser = false)
  {
    lock (lockObj)
    {
      if (wasStarted)
        throw new InvalidOperationException();
      if (asUser)
      {
        return DoStartUser(commandLine);
      }
      else
      {
        return DoStart(commandLine);
      }
    }
  }

  /// <summary> Gets the user token from the currently active session </summary>
  [MustDisposeResource]
  protected virtual SafeAccessTokenHandle GetSessionUserToken()
  {
    var activeSessionId = INVALID_SESSION_ID;
    var pSessionInfo = nint.Zero;
    var sessionCount = 0;

    // Get a handle to the user access token for the current active session.
    if (WTSEnumerateSessions(WTS_CURRENT_SERVER_HANDLE, 0, 1, ref pSessionInfo, ref sessionCount))
    {
      var arrayElementSize = Marshal.SizeOf(typeof(WtsSessionInfo));
      var current = pSessionInfo;

      for (var i = 0; i < sessionCount; i++)
      {
        var si = Marshal.PtrToStructure<WtsSessionInfo>(current);
        current += arrayElementSize;

        if (si.State == WTS_CONNECTSTATE_CLASS.WTSActive)
        {
          activeSessionId = si.SessionID;
        }
      }
    }

    // If enumerating did not work, fall back to the old method
    if (activeSessionId == INVALID_SESSION_ID)
      activeSessionId = WTSGetActiveConsoleSessionId();

    if (activeSessionId == INVALID_SESSION_ID)
      throw new CurrentUserException("WTSGetActiveConsoleSessionId: Failed to get active session ID.");

    if (!WTSQueryUserToken(activeSessionId, out var hImpersonationToken))
    {
      var result = Marshal.GetLastPInvokeError();
      if (result is 5 /*ERROR_ACCESS_DENIED*/ or 1314 /*ERROR_PRIVILEGE_NOT_HELD*/)
      {
#if DEBUG
        // fallback for local development when agent isn't running as LocalSystem
        if (!OpenProcessToken(GetCurrentProcess(), TOKEN_DUPLICATE | TOKEN_QUERY, out hImpersonationToken))
          throw new Win32Exception();
#else
        throw new CurrentUserException("Agent must be running as LocalSystem to run process as user.");
#endif
      }
      else
        throw new NoLoggedOnUserException(
          "Could not start the process as the current user because no user is logged in.");
    }

    using (hImpersonationToken)
    {
      // Convert the impersonation token to a primary token
      if (!DuplicateTokenEx(
            hImpersonationToken,
            TOKEN_ASSIGN_PRIMARY | TOKEN_DUPLICATE | TOKEN_IMPERSONATE | TOKEN_QUERY,
            nint.Zero,
            SECURITY_IMPERSONATION_LEVEL.SecurityImpersonation,
            TOKEN_TYPE.TokenPrimary,
            out var phUserToken))
        throw new CurrentUserException("DuplicateTokenEx: " + Marshal.GetLastPInvokeErrorMessage());

      return phUserToken;
    }
  }

  protected virtual uint DoStartUser(string commandLine)
  {
    CommandLine = commandLine;

    const uint STARTF_USESTDHANDLES = 0x00000100;

    const int STD_INPUT_HANDLE = -10;
    const int STD_OUTPUT_HANDLE = -11;
    const int STD_ERROR_HANDLE = -12;

    const int CREATE_UNICODE_ENVIRONMENT = 0x00000400;

    var pEnv = nint.Zero;

    var startupInfo = new StartupInfo();
    startupInfo.cb = (uint)Marshal.SizeOf(typeof(StartupInfo));
    startupInfo.dwFlags = STARTF_USESTDHANDLES;

    startupInfo.wShowWindow = (short)SW.SW_HIDE;
    startupInfo.lpDesktop = "winsta0\\default";

    var processInfo = new ProcessInformation();
    using var hUserToken = GetSessionUserToken();

    try
    {
      if (!CreateEnvironmentBlock(out pEnv, hUserToken, false))
        throw new CurrentUserException("CreateEnvironmentBlock: " + Marshal.GetLastPInvokeErrorMessage());

      // Create pipes
      startupInfo.hStdInput = new SafePipeHandle(GetStdHandle(STD_INPUT_HANDLE), ownsHandle: false);
      if (RedirectStandardOutput || RedirectStandardOutputAndErrorToSingleStream)
      {
        standardOutput = new AnonymousPipeServerStream(PipeDirection.In, HandleInheritability.Inheritable);
        startupInfo.hStdOutput = standardOutput.ClientSafePipeHandle;
      }
      else
      {
        startupInfo.hStdOutput = new SafePipeHandle(GetStdHandle(STD_OUTPUT_HANDLE), ownsHandle: false);
      }
      if (RedirectStandardOutputAndErrorToSingleStream)
      {
        _standardError = standardOutput;
        startupInfo.hStdError = _standardError?.ClientSafePipeHandle;
      }
      else if (RedirectStandardError)
      {
        _standardError = new AnonymousPipeServerStream(PipeDirection.In, HandleInheritability.Inheritable);
        startupInfo.hStdError = _standardError.ClientSafePipeHandle;
      }
      else
      {
        startupInfo.hStdError = new SafePipeHandle(GetStdHandle(STD_ERROR_HANDLE), ownsHandle: false);
      }

      var flags = (uint)CreationFlags;

      var environmentBlock = BuildEnvironmentVarBlock(environmentVariables, pEnv);

      flags |= CREATE_UNICODE_ENVIRONMENT;

      CreateProcessAsUser(hUserToken, null, new StringBuilder(commandLine),
        nint.Zero,
        nint.Zero,
        true,
        flags, environmentBlock, WorkingDirectory, ref startupInfo, out processInfo);
      wasStarted = true;
      return ProcessId = (uint)processInfo.dwProcessId;
    }
    catch (Exception ex) when (ex is not NoLoggedOnUserException or CurrentUserException)
    {
      throw new CurrentUserException($"StartProcessAsCurrentUser: encountered an unexpected error. Is a user currently logged in? : {ex}", ex);
    }
    finally
    {
      if (processInfo.hProcess != nint.Zero && processInfo.hProcess != new nint(-1))
      {
        safeProcessHandle = new SafeProcessHandle(processInfo.hProcess);
      }

      if (pEnv != nint.Zero)
      {
        DestroyEnvironmentBlock(pEnv);
      }

      if (processInfo.hThread != nint.Zero && processInfo.hThread != new nint(-1))
      {
        CloseHandle(processInfo.hThread);
      }
      // Dispose the client side handles of the pipe.
      // They got copied into the new process, we don't need our local copies anymore.
      startupInfo.hStdInput?.Dispose();
      startupInfo.hStdOutput?.Dispose();
      startupInfo.hStdError?.Dispose();
      if (!wasStarted)
      {
        // In case of error, dispose the server side of the pipes as well
        if (standardOutput != null)
        {
          standardOutput.Dispose();
          standardOutput = null;
        }
        if (_standardError != null)
        {
          _standardError.Dispose();
          _standardError = null;
        }
      }
    }
  }

  protected virtual uint DoStart(string commandLine)
  {
    CommandLine = commandLine;

    const uint STARTF_USESTDHANDLES = 0x00000100;

    const int STD_INPUT_HANDLE = -10;
    const int STD_OUTPUT_HANDLE = -11;
    const int STD_ERROR_HANDLE = -12;

    const int CREATE_UNICODE_ENVIRONMENT = 0x00000400;

    var startupInfo = new StartupInfo();
    startupInfo.cb = (uint)Marshal.SizeOf(typeof(StartupInfo));
    startupInfo.dwFlags = STARTF_USESTDHANDLES;

    // Create pipes
    startupInfo.hStdInput = new SafePipeHandle(GetStdHandle(STD_INPUT_HANDLE), ownsHandle: false);
    if (RedirectStandardOutput || RedirectStandardOutputAndErrorToSingleStream)
    {
      standardOutput = new AnonymousPipeServerStream(PipeDirection.In, HandleInheritability.Inheritable);
      startupInfo.hStdOutput = standardOutput.ClientSafePipeHandle;
    }
    else
    {
      startupInfo.hStdOutput = new SafePipeHandle(GetStdHandle(STD_OUTPUT_HANDLE), ownsHandle: false);
    }
    if (RedirectStandardOutputAndErrorToSingleStream)
    {
      _standardError = standardOutput;
      startupInfo.hStdError = _standardError?.ClientSafePipeHandle;
    }
    else if (RedirectStandardError)
    {
      _standardError = new AnonymousPipeServerStream(PipeDirection.In, HandleInheritability.Inheritable);
      startupInfo.hStdError = _standardError.ClientSafePipeHandle;
    }
    else
    {
      startupInfo.hStdError = new SafePipeHandle(GetStdHandle(STD_ERROR_HANDLE), ownsHandle: false);
    }

    var flags = (uint)CreationFlags;

    string? environmentBlock = null;
    if (environmentVariables != null)
    {
      environmentBlock = BuildEnvironmentVarBlock(environmentVariables);
      flags |= CREATE_UNICODE_ENVIRONMENT;
    }

    var processInfo = new ProcessInformation();
    try
    {
      CreateProcess(null, new StringBuilder(commandLine),
        nint.Zero,
        nint.Zero,
        true,
        flags, environmentBlock, WorkingDirectory, ref startupInfo, out processInfo);
      wasStarted = true;
      return ProcessId = (uint)processInfo.dwProcessId;
    }
    finally
    {
      if (processInfo.hProcess != nint.Zero && processInfo.hProcess != new nint(-1))
      {
        safeProcessHandle = new SafeProcessHandle(processInfo.hProcess);
      }
      if (processInfo.hThread != nint.Zero && processInfo.hThread != new nint(-1))
      {
        CloseHandle(processInfo.hThread);
      }
      // Dispose the client side handles of the pipe.
      // They got copied into the new process, we don't need our local copies anymore.
      startupInfo.hStdInput?.Dispose();
      startupInfo.hStdOutput?.Dispose();
      startupInfo.hStdError?.Dispose();
      if (!wasStarted)
      {
        // In case of error, dispose the server side of the pipes as well
        if (standardOutput != null)
        {
          standardOutput.Dispose();
          standardOutput = null;
        }
        if (_standardError != null)
        {
          _standardError.Dispose();
          _standardError = null;
        }
      }
    }
  }

  static string BuildEnvironmentVarBlock(IEnumerable<KeyValuePair<string, string>>? environment, nint? existingEnv = null)
  {
    var envVars = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
    // Try and read in existing environment block if it exists
    if (existingEnv is not null)
    {
      // The environment block is an array of null-terminated Unicode strings.
      // The list ends with two nulls (\0\0). One to signify termination of string, and another for entire block.
      // Each string is in the form "name=value".
      // So, read pointer. if its null, we done. If not, read null terminated strings until we hit a (second) null.
      var pointer = existingEnv.Value;

      while (Marshal.ReadByte(pointer) != 0)
      {
        var envVarString = Marshal.PtrToStringUni(pointer);
        // Cast IntPtr to Int64 to ensure the math works in 32 or 64 bit for safety.
        // * 2 because each char is 2 bytes in unicode.
        // + 2 to skip the strings' null terminator (another 2 bytes in unicode)
        if (envVarString is null)
          break;
        var nextPointerLocation = (nint)((long)pointer + (envVarString.Length * 2) + 2);

        var splitPoint = envVarString.IndexOf('=', 1);
        // if we cant find an equal sign, we'll just skip this env var. Not sure if this is even possible.
        if (splitPoint == -1)
        {
          pointer = nextPointerLocation;
          continue;
        }
        var envVarName = envVarString.Substring(0, splitPoint);
        var envVarVal = envVarString.Substring(splitPoint + 1);

        envVars.Add(envVarName, envVarVal);

        pointer = nextPointerLocation;
      }
    }

    // Add the new env vars to the dictionary (overwriting existing ones if needed)
    if (environment is not null)
    {
      foreach (var pair in environment)
      {
        envVars[pair.Key] = pair.Value;
      }
    }

    var envBlock = new StringBuilder();
    foreach (var pair in envVars.OrderBy(p => p.Key, StringComparer.OrdinalIgnoreCase))
    {
      envBlock.Append(pair.Key);
      envBlock.Append('=');
      envBlock.Append(pair.Value);
      envBlock.Append('\0');
    }
    envBlock.Append('\0');
    return envBlock.ToString();
  }

  protected virtual void CreateProcess(
    string? lpApplicationName,
    StringBuilder lpCommandLine,
    nint lpProcessAttributes,
    nint lpThreadAttributes,
    bool bInheritHandles,
    uint dwCreationFlags,
    string? lpEnvironment,
    string? lpCurrentDirectory,
    ref StartupInfo lpStartupInfo, out ProcessInformation lpProcessInformation)
  {
    if (!NativeCreateProcess(lpApplicationName, lpCommandLine, lpProcessAttributes, lpThreadAttributes, bInheritHandles, dwCreationFlags,
                             lpEnvironment, lpCurrentDirectory, ref lpStartupInfo, out lpProcessInformation))
    {
      throw new Win32Exception();
    }
  }

  protected virtual void CreateProcessAsUser(
  SafeAccessTokenHandle hToken,
  string? lpApplicationName,
  StringBuilder lpCommandLine,
  nint lpProcessAttributes,
  nint lpThreadAttributes,
  bool bInheritHandles,
  uint dwCreationFlags,
  string lpEnvironment,
  string? lpCurrentDirectory,
  ref StartupInfo lpStartupInfo, out ProcessInformation lpProcessInformation)
  {
    if (!NativeCreateProcessAsUser(hToken, lpApplicationName, lpCommandLine, lpProcessAttributes, lpThreadAttributes, bInheritHandles, dwCreationFlags,
                             lpEnvironment, lpCurrentDirectory, ref lpStartupInfo, out lpProcessInformation))
    {
      throw new Win32Exception();
    }
  }
  #endregion


  #region HasExited / ExitCode / Kill
  public bool HasExited
  {
    get { return WaitForExit(0); }
  }

  /// <summary>
  /// Gets the process exit code.
  /// </summary>
  public int ExitCode
  {
    get
    {
      if (!WaitForExit(0))
        throw new InvalidOperationException("Process has not yet exited");
      return exitCode; // WaitForExit has the side effect of setting exitCode
    }
  }

  /// <summary>
  /// Sends the kill signal to the process.
  /// Does not wait for the process to complete to exit after being killed.
  /// </summary>
  public void Kill()
  {
    if (!wasStarted)
      throw new InvalidOperationException("Process was not started");
    if(safeProcessHandle is null)
      throw new InvalidOperationException("Process handle is null. Process may have already exited.");
    if (!TerminateProcess(safeProcessHandle, -1))
    {
      var err = Marshal.GetLastWin32Error();
      // If TerminateProcess fails, maybe it's because the process has already exited.
      if (!WaitForExit(0))
        throw new Win32Exception(err);
    }
  }
  #endregion

  #region WaitForExit
  sealed class ProcessWaitHandle : WaitHandle
  {
    [DllImport("kernel32.dll")]
    static extern nint GetCurrentProcess();

    [DllImport("kernel32.dll", BestFitMapping = false, CharSet = CharSet.Ansi)]
    static extern bool DuplicateHandle(HandleRef hSourceProcessHandle, SafeHandle hSourceHandle, HandleRef hTargetProcess, out SafeWaitHandle targetHandle, int dwDesiredAccess, bool bInheritHandle, int dwOptions);
    public ProcessWaitHandle(SafeProcessHandle processHandle)
    {
      var currentProcess = new HandleRef(this, GetCurrentProcess());
      SafeWaitHandle safeWaitHandle;
      if (!DuplicateHandle(currentProcess, processHandle, currentProcess, out safeWaitHandle, 0, false, DUPLICATE_SAME_ACCESS))
      {
        throw new Win32Exception();
      }
      SafeWaitHandle = safeWaitHandle;
    }
  }

  bool hasExited;
  int exitCode;

  public void WaitForExit()
  {
    WaitForExit(Timeout.Infinite);
  }

  public bool WaitForExit(int millisecondsTimeout)
  {
    if (hasExited)
      return true;
    if (!wasStarted)
      throw new InvalidOperationException("Process was not yet started");
    if(safeProcessHandle is null)
      throw new InvalidOperationException("Process handle is null. Process may have already exited.");
    if (safeProcessHandle.IsClosed)
      throw new ObjectDisposedException("Process has already been disposed");
    using (var waitHandle = new ProcessWaitHandle(safeProcessHandle))
    {
      if (waitHandle.WaitOne(millisecondsTimeout, false))
      {
        if (!GetExitCodeProcess(safeProcessHandle, out exitCode))
          throw new Win32Exception();
        hasExited = true;
      }
    }
    return hasExited;
  }

  readonly object lockObj = new object();
  TaskCompletionSource<object?>? waitForExitTCS;
  ProcessWaitHandle? waitForExitAsyncWaitHandle;
  RegisteredWaitHandle? waitForExitAsyncRegisteredWaitHandle;

  /// <summary>
  /// Asynchronously waits for the process to exit.
  /// </summary>
  public Task WaitForExitAsync(
    TimeSpan timeout,
    CancellationToken cancellationToken = default)
  {
    if (hasExited)
      return Task.FromResult(true);
    if (!wasStarted)
      throw new InvalidOperationException("Process was not yet started");
    if(safeProcessHandle is null)
      throw new InvalidOperationException("Process handle is null. Process may have already exited.");
    if (safeProcessHandle is { IsClosed: true })
      throw new ObjectDisposedException("ProcessRunner");
    lock (lockObj)
    {
      if (waitForExitTCS == null)
      {
        waitForExitTCS = new TaskCompletionSource<object?>();
        waitForExitAsyncWaitHandle = new ProcessWaitHandle(safeProcessHandle);
        waitForExitAsyncRegisteredWaitHandle = ThreadPool.RegisterWaitForSingleObject(waitForExitAsyncWaitHandle, WaitForExitAsyncCallback, null, -1, true);
      }
      return waitForExitTCS.Task.WaitAsync(timeout, cancellationToken);
    }
  }

  void WaitForExitAsyncCallback(object? context, bool wasSignaled)
  {
    waitForExitAsyncRegisteredWaitHandle?.Unregister(null);
    waitForExitAsyncRegisteredWaitHandle = null;
    waitForExitAsyncWaitHandle?.Close();
    waitForExitAsyncWaitHandle = null;

    waitForExitTCS?.SetResult(null!);
  }
  #endregion

  #region StandardOutput/StandardError
  AnonymousPipeServerStream? standardOutput;
  AnonymousPipeServerStream? _standardError;
  private bool _disposedValue;

  public Stream StandardOutput
  {
    get
    {
      if (standardOutput == null)
        throw new InvalidOperationException(wasStarted ? "stdout was not redirected" : "Process not yet started");
      return standardOutput;
    }
  }

  public Stream StandardError
  {
    get
    {
      if (_standardError == null)
        throw new InvalidOperationException(wasStarted ? "stderr was not redirected" : "Process not yet started");
      return _standardError;
    }
  }

  /// <summary>
  /// Opens a text reader around the standard output.
  /// </summary>
  public StreamReader OpenStandardOutputReader()
  {
    return new StreamReader(StandardOutput);
  }

  /// <summary>
  /// Opens a text reader around the standard error.
  /// </summary>
  public StreamReader OpenStandardErrorReader()
  {
    return new StreamReader(StandardError);
  }

  protected virtual void Dispose(bool disposing)
  {
    if (!_disposedValue)
    {
      if (disposing)
      {
        // dispose managed state (managed objects)
        safeProcessHandle?.Dispose();
        standardOutput?.Dispose();
        _standardError?.Dispose();
      }

      // free unmanaged resources (unmanaged objects) and override finalizer
      // set large fields to null
      _disposedValue = true;
    }
  }

  public void Dispose()
  {
    // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
    Dispose(disposing: true);
    GC.SuppressFinalize(this);
  }
  #endregion
}
