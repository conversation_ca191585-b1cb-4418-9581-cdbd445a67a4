using System.IO.Abstractions;
using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;
using Immybot.Shared.Abstractions.Device.FileSystem;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.Logging;

namespace Immybot.Shared.Abstractions.Device.Processes;

public interface IProcessManager
{
  Task<ProcessRunner> EnsureScriptExistsAndValidatedThenExecute(PowershellContext desiredContext, PowershellScript script, string scriptDir, string? additionalArgs = null);
  IProcess GetCurrentProcess();
  IProcess[] GetProcessesByName(string processName);
  Task<string> RunScript(string script, CancellationToken token = default, string scriptLanguage = "PowerShell", string? tmpScriptDirOverride = null, params string[] additionalArgs);
  IProcess Start(string filename, string arguments);
}

public class ProcessManager : IProcessManager
{
  private readonly IFileSystem _fileSystem;
  private readonly string _cmdBinaryPath;
  private readonly ILogger<ProcessManager> _logger;
  private readonly string _powershellBinaryPath;
  private readonly string[] _powershellBaseArgs = ["-NoProfile", "-NonInteractive", "-ExecutionPolicy", "Unrestricted"];

  public ProcessManager(ILogger<ProcessManager> logger, IFileSystem fileSystem)
  {
    _logger = logger;
    _fileSystem = fileSystem;
    _cmdBinaryPath = fileSystem.Path.Combine(Environment.SystemDirectory, "cmd.exe");
    // If running as a 32-bit process on a 64-bit machine, ensure that we use the
    // 64-bit version of PowerShell.
    if (Environment.Is64BitOperatingSystem && !Environment.Is64BitProcess)
    {
      // force run as 64 bit through sysnative because windows automatically redirects system32 to syswow64 for 32-bit processes.
      _powershellBinaryPath = Environment.ExpandEnvironmentVariables(@"%SystemRoot%\sysnative\WindowsPowerShell\v1.0\powershell.exe");
    }
    else
    {
      // run as 32 bit on machines that do not have syswow64 because
      // sysnative does not exist.
      _powershellBinaryPath = Environment.ExpandEnvironmentVariables(@"%SystemRoot%\System32\WindowsPowerShell\v1.0\powershell.exe");
    }
  }
  public async Task<ProcessRunner> EnsureScriptExistsAndValidatedThenExecute
    (PowershellContext desiredContext, PowershellScript script, string scriptDir, string? additionalArgs = null)
  {
    var (scriptName, scriptContents, expectedScriptHash) = script;

    var targetScriptPath = _fileSystem.Path.Combine(scriptDir, scriptName);
    if (!_fileSystem.File.Exists(targetScriptPath))
    {
      _logger.LogInformation("Script '{ScriptName}', doesn't exist, creating it.", scriptName);
      _fileSystem.Directory.CreateDirectory(scriptDir);
      await _fileSystem.File.WriteAllTextAsync(targetScriptPath, scriptContents);
    }

    var fileHash = await Sha256File(targetScriptPath);
    if (fileHash != expectedScriptHash)
    {
      _logger.LogWarning("Script '{ScriptName}', doesn't match expected file hash. Re-creating it.", scriptName);
      await _fileSystem.File.WriteAllTextAsync(targetScriptPath, scriptContents);
    }

    var cmdLineArgs = $"{_cmdBinaryPath} /c {_powershellBinaryPath} -NoProfile -NonInteractive -ExecutionPolicy Unrestricted -File \"{targetScriptPath}\" {additionalArgs}";

    var processRunner = new ProcessRunner() { RedirectStandardOutputAndErrorToSingleStream = true };
    try
    {
      var procId = processRunner.StartCommandLine(cmdLineArgs, asUser: desiredContext == PowershellContext.USER_CONTEXT);
      _logger.LogDebug("Process spawned via Win32API ({ProcId})", procId);
      return processRunner;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Failed to spawn process via Win32API");
      processRunner.Dispose();
      // make sure we dispose of the temp PS1 then throw again so caller can get the exception.
      throw;
    }
  }

  public IProcess GetCurrentProcess()
  {
    return new ProcessWrapper(Process.GetCurrentProcess());
  }

  public IProcess[] GetProcessesByName(string processName)
  {
    return Process
      .GetProcessesByName(processName)
      .Select(p => new ProcessWrapper(p))
      .ToArray();
  }
  public async Task<string> RunScript(string script, CancellationToken token = default, string scriptLanguage = "PowerShell", string? tmpScriptDirOverride = null, params string[] additionalArgs)
  {
    switch (scriptLanguage)
    {
      case "PowerShell":
        {
          if (script.Length < 4096)
          {
            var encodedScript = Convert.ToBase64String(Encoding.Unicode.GetBytes(script));
            var output = await RunProcessWithWin32(_powershellBinaryPath, _powershellBaseArgs.Concat(["-EncodedCommand", encodedScript
            ]).ToArray(), token);
            return output.Trim();
          }
          using (var tempScript = new ImmyTempFile(script, _fileSystem, "ps1", Environment.ExpandEnvironmentVariables(tmpScriptDirOverride ?? string.Empty)))
          {
            var output = await RunProcessWithWin32(_powershellBinaryPath, _powershellBaseArgs.Concat(["-File", tempScript.FilePath
            ]).Concat(additionalArgs).ToArray(), token);
            return output.Trim();
          }
        }
      default:
        {
          var output = await RunProcessWithWin32(_cmdBinaryPath, ["/c", script], token);
          return output.Trim();
        }
    }
  }

  public IProcess Start(string filename, string arguments)
  {
    var process = Process.Start(filename, arguments);
    return new ProcessWrapper(process);
  }

  private async Task<string> RunProcessWithWin32(string program, string[] args, CancellationToken token = default)
  {
    var processRunner = new ProcessRunner() { RedirectStandardOutputAndErrorToSingleStream = true };
    try
    {
      var procId = processRunner.Start(program, asUser: false, args);
      _logger.LogDebug("Process spawned via Win32API ({ProcId})", procId);

      while (!token.IsCancellationRequested && !processRunner.HasExited)
      {
        await Task.Delay(TimeSpan.FromMilliseconds(100), token);
      }
      var output = await processRunner.OpenStandardOutputReader().ReadToEndAsync(token);
      return output;
    }
    catch (Exception ex)
    {
      var output = await processRunner.OpenStandardOutputReader().ReadToEndAsync(token);
      _logger.LogError(ex, "An error occurred while trying to run {Program} {Args} => {Output}", program, args, output);
      return output;
    }
    finally
    {
      processRunner.Dispose();
    }
  }

  private async Task<string> Sha256File(string filePath)
  {
    await using var fileStream = _fileSystem.File.OpenRead(filePath);
    var hash = await SHA256.HashDataAsync(fileStream);
    return BitConverter.ToString(hash);
  }
}
