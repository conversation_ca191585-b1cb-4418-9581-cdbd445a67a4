using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Runtime.CompilerServices;
using System.Runtime.ExceptionServices;
using Microsoft.Extensions.Logging;

#pragma warning disable CA2254 // Template should be a static expression
// ReSharper disable ExplicitCallerInfoArgument

namespace Immybot.Shared.Primitives;

public static class Assertion
{
  public static void AssertTrue(
    [DoesNotReturnIf(false)] bool expression,
    ILogger logger,
    [CallerArgumentExpression(nameof(expression))]
    string expressionName = "",
    [CallerMemberName] string callerName = "",
    [CallerFilePath] string filePath = "",
    [CallerLineNumber] int lineNumber = 0)
  {
    if (!expression)
      LogAndThrow($"`{expressionName}` evaluated to false", logger, callerName, filePath, lineNumber);
  }

  [DoesNotReturn]
  public static void Fail(
    string message,
    ILogger logger,
    [CallerMemberName] string callerName = "",
    [CallerFilePath] string filePath = "",
    [CallerLineNumber] int lineNumber = 0)
  {
    LogAndThrow(message, logger, callerName, filePath, lineNumber);
  }

  [MethodImpl(MethodImplOptions.NoInlining)]
  [DoesNotReturn]
  private static void LogAndThrow(string message, ILogger logger, string callerName, string filePath, int lineNumber)
  {
    var exception = new AssertionFailedException(message, callerName, filePath, lineNumber);

    // we want a fancy, accurate stack trace, so use EnhancedStackTrace and pass in the caller info to guarantee we get the accurate source code location at compile time
    var stackTrace = new EnhancedStackTrace(new StackTrace(2 /* skip current and caller frames */, true));
    ExceptionDispatchInfo.SetRemoteStackTrace(exception, stackTrace.ToString());

    logger.LogCritical(exception, message);

    if (Debugger.IsAttached)
      Debugger.Break();

    ExceptionDispatchInfo.Throw(exception);
  }
}

public class AssertionFailedException : Exception
{
  public string CallerName { get; }
  public string CallerFilePath { get; }
  public int CallerLineNumber { get; }

  public AssertionFailedException(
    string message,
    [CallerMemberName] string callerName = "",
    [CallerFilePath] string filePath = "",
    [CallerLineNumber] int lineNumber = 0)
    : base($"{message} at {callerName} in {filePath}:line {lineNumber}")
  {
    CallerName = callerName;
    CallerFilePath = filePath;
    CallerLineNumber = lineNumber;
  }

  public AssertionFailedException(
    string message,
    Exception innerException,
    [CallerMemberName] string callerName = "",
    [CallerFilePath] string filePath = "",
    [CallerLineNumber] int lineNumber = 0)
    : base($"{message} at {callerName} in {filePath}:line {lineNumber}", innerException)
  {
    CallerName = callerName;
    CallerFilePath = filePath;
    CallerLineNumber = lineNumber;
  }
}
