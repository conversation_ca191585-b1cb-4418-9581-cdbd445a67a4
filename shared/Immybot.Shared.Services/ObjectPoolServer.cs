using System.Diagnostics;
using System.Threading.Channels;
using Immybot.Shared.Extensions;
using Immybot.Shared.Services.Startup;
using Immybot.Shared.Telemetry;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.VisualStudio.Threading;

namespace Immybot.Shared.Services;

/// <summary>
/// Provides configuration options for an object pool's maintenance behavior.
/// </summary>
public class ObjectPoolOptions
{
  /// <summary>
  /// Determines whether stale (unused) entries should be automatically recycled during pool maintenance.
  /// </summary>
  /// <remarks>
  /// When set to true, entries that have not been used for an extended period will be destroyed and recreated.
  /// When set to false, stale entries will be kept in the pool even if they have not been recently used.
  /// </remarks>
  public bool RecycleStaleEntries { get; set; }
}

public interface IObjectPoolRental<out TObject, out TRequest> : System.IAsyncDisposable
{
  TObject Object { get; }
  TRequest Request { get; }
}

public interface IObjectPoolServer<TObject, TRequest> : IHostedService
{
  Task<IObjectPoolRental<TObject, TRequest>> BorrowAsync(TRequest request, CancellationToken token);
}

public abstract class ObjectPoolServer<TObject, TRequest, TKey, TRentalState, TConfiguration>(
  ILogger<ObjectPoolServer<TObject, TRequest, TKey, TRentalState, TConfiguration>> logger,
  TimeProvider timeProvider,
  IOptionsMonitor<TConfiguration>? objectPoolOptions = null)
  : EarlyStartupBackgroundService, IObjectPoolServer<TObject, TRequest>
  where TKey : notnull
  where TConfiguration : ObjectPoolOptions
{
  private readonly Channel<ICoordinatorMessage> _coordinatorChannel = CreateChannel<ICoordinatorMessage>();
  private readonly Channel<EntryCreatorMessage> _creatorChannel = CreateChannel<EntryCreatorMessage>();
  private readonly Channel<EntryResetterMessage> _resetterChannel = CreateChannel<EntryResetterMessage>();
  private readonly Channel<EntryDestroyerMessage> _destroyerChannel = CreateChannel<EntryDestroyerMessage>();

  static Channel<T> CreateChannel<T>() => Channel.CreateUnbounded<T>(new() { SingleReader = true });

  protected override async Task ExecuteAsync(CancellationToken stoppingToken) =>
    await Task.WhenAll(
      RunMaintenanceTimerLoopAsync(stoppingToken),
      RunCoordinatorLoopAsync(),
      RunCreatorLoopAsync(),
      RunResetterLoopAsync(),
      RunDestroyerLoopAsync()
    );

  protected virtual Task WaitForMaintenance(CancellationToken cancellationToken) =>
    cancellationToken.AsAwaitable();

  protected void Invalidate()
  {
    _coordinatorChannel.Writer.TryWrite(new InvalidateMessage());
  }

  private async Task RunMaintenanceTimerLoopAsync(CancellationToken stoppingToken)
  {
    while (!stoppingToken.IsCancellationRequested)
    {
      using var timeoutTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(60));
      using var tokenSource = CancellationTokenSource.CreateLinkedTokenSource(timeoutTokenSource.Token, stoppingToken);
      await WaitForMaintenance(tokenSource.Token).ConfigureAwait(ConfigureAwaitOptions.SuppressThrowing);

      _coordinatorChannel.Writer.TryWrite(new MaintenanceMessage());
    }

    // commence shutdown
    // shutdown should be graceful where pending work is completed in order to cleanly release resources
    // ordering/coordination is important here and generally done by completing channels
    // look for other calls to .Complete() on other channels or where messages are sent to other channels to signal conditions
    await _coordinatorChannel.Writer.WriteAsync(new StoppingMessage(), CancellationToken.None);
  }

  private async Task RunCoordinatorLoopAsync()
  {
    Dictionary<TKey, PoolState> stateByKey = [];
    var isStopping = false;
    var currentVersion = 0L;

    PoolState GetOrCreateState(TKey key)
    {
      if (stateByKey.TryGetValue(key, out var state))
        return state;

      return stateByKey[key] = new(key);
    }

    IDisposable? BeginLoggerScope(PoolState state) =>
      logger.BeginScope(
        "`{Key}`" +
        " | {Waiters} Waiters" +
        " | {OutstandingRentals} OutstandingRentals" +
        " | {FreeEntries} FreeEntries" +
        " | {CurrentVersion} CurrentVersion",
        state.Key,
        state.Waiters.Count,
        state.OutstandingRentals,
        state.FreeEntries.Count,
        currentVersion);

    while (!isStopping || stateByKey.Values.Any(it => it.OutstandingCreations > 0 || it.OutstandingRentals > 0))
    {
      switch (await _coordinatorChannel.Reader.ReadAsync())
      {
        case EntryRequestedMessage(var key, var taskCompletionSource):
          {
            if (isStopping)
              taskCompletionSource.TrySetException(
                new ObjectDisposedException("Pool has shut down"));

            var state = GetOrCreateState(key);
            state.Waiters.Enqueue((taskCompletionSource, timeProvider.GetUtcNow()));

            using (BeginLoggerScope(state)) logger.LogInformation("Received request");
            break;
          }
        case RequestCancelledMessage(var key, var taskCompletionSource, var cancellationToken):
          {
            var state = GetOrCreateState(key);

            // rather inefficient, but it's not a hot path
            var waiters = state.Waiters.ToList();
            var waiterIndex = waiters.FindIndex(it => it.TaskCompletionSource == taskCompletionSource);

            if (waiterIndex != -1)
            {
              waiters[waiterIndex].TaskCompletionSource.TrySetCanceled(cancellationToken);
              state.Waiters.Clear();

              foreach (var waiter in waiters.Where((_, index) => index != waiterIndex))
                state.Waiters.Enqueue(waiter);
            }

            using (BeginLoggerScope(state)) logger.LogInformation("Cancelled request");
            break;
          }
        case EntryCreatedMessage(var key, var @object, var creationDuration):
          {
            var state = GetOrCreateState(key);
            state.OutstandingCreations--;
            state.FreeEntries.Push(new(@object, timeProvider.GetUtcNow()));
            state.CreationFailureEvents.Clear();
            state.CreationStats = AddTimeStat(state.CreationStats, creationDuration);
            using (BeginLoggerScope(state)) logger.LogInformation("Entry created: took {CreationDurationMs}ms", creationDuration.TotalMilliseconds);
            break;
          }
        case EntryCreationFailedMessage(var key, var exception):
          {
            var state = GetOrCreateState(key);
            state.OutstandingCreations--;
            state.CreationFailureEvents.AddLast((timeProvider.GetUtcNow(), exception));
            using (BeginLoggerScope(state)) logger.LogError(exception, "Entry creation failed");
            break;
          }
        case EntryDestructionFailedMessage(var key, var exception):
          {
            var state = GetOrCreateState(key);
            using (BeginLoggerScope(state)) logger.LogError(exception, "Entry destruction failed");
            break;
          }
        case EntryReturnedMessage(var key, var @object, var version, var isValidForReuse):
          {
            var state = GetOrCreateState(key);
            state.OutstandingRentals--;

            using (BeginLoggerScope(state))
              if (isStopping)
              {
                logger.LogInformation("Destroying entry because pool is shutting down");
                _destroyerChannel.Writer.TryWrite(new(key, @object));
              }
              else if (!isValidForReuse)
              {
                logger.LogInformation("Destroying entry because it is not valid for reuse");
                _destroyerChannel.Writer.TryWrite(new(key, @object));
              }
              else if (version != currentVersion)
              {
                logger.LogInformation("Destroying invalidated entry: version is {Version}", version);
                _destroyerChannel.Writer.TryWrite(new(key, @object));
              }
              else
              {
                logger.LogInformation("Rental returned. Resetting for reuse");
                _resetterChannel.Writer.TryWrite(new(key, @object, version));
              }

            break;
          }
        case EntryResetForReuseMessage(var key, var @object, var version):
          {
            var state = GetOrCreateState(key);
            using (BeginLoggerScope(state))
              if (version != currentVersion)
              {
                logger.LogInformation("Destroying invalidated entry: version is {Version}", version);
                _destroyerChannel.Writer.TryWrite(new(key, @object));
              }
              else
              {
                logger.LogInformation("Returning reset entry to pool");
                state.FreeEntries.Push(new(@object, timeProvider.GetUtcNow()));
              }

            break;
          }
        case EntryResetForReuseFailedMessage(var key, var exception):
          {
            var state = GetOrCreateState(key);
            using (BeginLoggerScope(state)) logger.LogError(exception, "Failed to reset entry for reuse");
            break;
          }
        case StoppingMessage:
          isStopping = true;
          logger.LogInformation("Shutting down pool");
          foreach (var waiter in stateByKey.SelectMany(it => it.Value.Waiters))
            waiter.TaskCompletionSource.TrySetException(new ObjectDisposedException("Pool has shut down"));
          break;
        case InvalidateMessage:
          currentVersion++;
          logger.LogInformation("Invalidating all free entries: {CurrentVersion}", currentVersion);
          foreach (var (key, state) in stateByKey)
            while (state.FreeEntries.Count > 0)
            {
              logger.LogInformation("Destroying invalidated free entry");
              _destroyerChannel.Writer.TryWrite(new(key, state.FreeEntries.Pop().Object));
            }

          break;
        case MaintenanceMessage:
          logger.LogInformation("Running pool maintenance");

          foreach (var (key, state) in stateByKey)
          {
            var freeEntriesToKeepWarm = new Stack<FreeEntry>();

            while (state.FreeEntries.TryPop(out var freeEntry))
              if (
                // here we have different time thresholds based on how many we want warm
                // also could want to look at time stats etc
                (freeEntriesToKeepWarm.Count < 2 && freeEntry.TimeFreed > timeProvider.GetUtcNow() - TimeSpan.FromMinutes(10)) // keep up to 2 that have been used in last 10 minutes
                || (freeEntriesToKeepWarm.Count < 10 && freeEntry.TimeFreed > timeProvider.GetUtcNow() - TimeSpan.FromMinutes(4)) // keep up to 10 that have been used in last 4 minutes
                || (freeEntriesToKeepWarm.Count < int.MaxValue && freeEntry.TimeFreed > timeProvider.GetUtcNow() - TimeSpan.FromSeconds(30)) // keep unlimited that have been used in last 30 seconds
              )
              {
                freeEntriesToKeepWarm.Push(freeEntry);
              }
              else if (objectPoolOptions?.CurrentValue.RecycleStaleEntries is true)
              {
                using (BeginLoggerScope(state)) logger.LogInformation("Destroying stale entry: last used at {TimeFreed}", freeEntry.TimeFreed);
                _destroyerChannel.Writer.TryWrite(new(key, freeEntry.Object));
              }
              else
              {
                using (BeginLoggerScope(state))
                  logger.LogInformation(
                    "Keeping stale entry since stale entry recycling is disabled: last used at {TimeFreed}",
                    freeEntry.TimeFreed);
                freeEntriesToKeepWarm.Push(freeEntry);
              }

            while (freeEntriesToKeepWarm.TryPop(out var freeEntry))
              state.FreeEntries.Push(freeEntry);
          }

          foreach (
            var key in stateByKey
              .Where(it => it.Value is
              {
                OutstandingRentals: 0,
                OutstandingCreations: 0,
                Waiters.Count: 0,
                FreeEntries.Count: 0,
              })
              .Select(it => it.Key)
              .ToList())
            stateByKey.Remove(key);

          break;
        default:
          throw new InvalidOperationException();
      }

      if (stateByKey.Count > 0)
      {
        // rent out free entries to waiters
        foreach (var (_, state) in stateByKey)
        {
          while (state.Waiters.Count > 0 && state.FreeEntries.Count > 0)
          {
            var entry = state.FreeEntries.Pop();
            var waiter = state.Waiters.Dequeue();
            var waitDuration = timeProvider.GetUtcNow() - waiter.WaitStartTime;
            state.WaitStats = AddTimeStat(state.WaitStats, waitDuration);
            state.OutstandingRentals++;

            using (BeginLoggerScope(state)) logger.LogInformation("Fulfilling request: took {WaitDurationMs}ms", waitDuration.TotalMilliseconds);
            waiter.TaskCompletionSource.TrySetResult(new BorrowResult(
              entry.Object,
              currentVersion,
              state.OutstandingRentals,
              state.Waiters.Count,
              state.FreeEntries.Count));
          }
        }

        const int totalOutstandingCreationsLimit = 5;
        const int headroomToMaintainWhenActive = 1;
        var creationThrottlePeriod = TimeSpan.FromSeconds(1);
        const int creationThrottleRateLimitPerPeriod = 10;
        var successiveFailureThrottlePeriod = TimeSpan.FromSeconds(20);
        const int successiveFailureThrottleLimit = 5;

        var queue = new Queue<KeyValuePair<TKey, PoolState>>(stateByKey.Shuffle()); // shuffle to prevent less-busy keys from starving
        while (queue.TryDequeue(out var pair) && stateByKey.Sum(it => it.Value.OutstandingCreations) < totalOutstandingCreationsLimit)
        {
          var (key, state) = pair;

          // scavenge old events
          while (state.CreationFailureEvents.First is { Value: var it } && it.FailureTime < timeProvider.GetUtcNow() - successiveFailureThrottlePeriod)
            state.CreationFailureEvents.RemoveFirst();
          while (state.CreationScheduledTimes.TryPeek(out var it) && it < timeProvider.GetUtcNow() - creationThrottlePeriod)
            state.CreationScheduledTimes.Dequeue();

          if (state.Waiters.Count == 0 && state.OutstandingRentals == 0)
            continue; // no demand, no creation needed

          if (state.CreationFailureEvents.Count >= successiveFailureThrottleLimit)
          {
            if (
              state is
              {
                OutstandingCreations: 0, // if we are still trying to create any entries, give them a chance to succeed before reporting failure
                Waiters.Count: > 0, // no waiters, no one to report failure to
                CreationFailureEvents.Last.Value.Exception: var lastException,
              }
            )
            {
              using (BeginLoggerScope(state))
                logger.LogError(lastException,
                  "Too many successive failures ({Count}) in the last {Period:N0}ms - reporting failure to waiters",
                  state.CreationFailureEvents.Count,
                  successiveFailureThrottlePeriod.TotalMilliseconds);

              // fail all waiters so they can propagate the error instead of timing out or waiting indefinitely
              while (state.Waiters.TryDequeue(out var waiter))
                waiter.TaskCompletionSource.TrySetException(lastException);
            }
          }
          else
          {
            if (state.CreationScheduledTimes.Count >= creationThrottleRateLimitPerPeriod)
              continue; // throttle creation

            void QueueCreateEntry()
            {
              state.OutstandingCreations++;
              state.CreationScheduledTimes.Enqueue(timeProvider.GetUtcNow());
              _creatorChannel.Writer.TryWrite(new(key));
              queue.Enqueue(pair); // get back in line in case more entries are needed
            }

            if (state.Waiters.Count > state.OutstandingCreations)
            {
              QueueCreateEntry();
              using (BeginLoggerScope(state)) logger.LogInformation("Creating new entry to fulfill demand");
            }
            else if (
              state is
              {
                OutstandingRentals: > 0, // active
                OutstandingCreations: 0, // only create one at a time for headroom
                FreeEntries.Count: < headroomToMaintainWhenActive,
              }
            )
            {
              QueueCreateEntry();
              using (BeginLoggerScope(state)) logger.LogInformation("Creating new entry to maintain headroom");
            }
          }
        }
      }
    }

    logger.LogInformation("Shutting down");

    foreach (var (key, state) in stateByKey)
    {
      foreach (var waiter in state.Waiters)
        waiter.TaskCompletionSource.TrySetCanceled();

      while (state.FreeEntries.Count > 0)
        _destroyerChannel.Writer.TryWrite(new(key, state.FreeEntries.Pop().Object));
    }

    _resetterChannel.Writer.Complete();
    _destroyerChannel.Writer.Complete();
    _creatorChannel.Writer.Complete();
  }

  private async Task RunCreatorLoopAsync()
  {
    await foreach (var message in _creatorChannel.Reader.ReadAllAsync())
    {
      try
      {
        var creationStartTime = timeProvider.GetUtcNow();
        var @object = await CreateObjectAsync(message.Key).WithTimeout(TimeSpan.FromSeconds(60));
        _coordinatorChannel.Writer.TryWrite(new EntryCreatedMessage(message.Key, @object, timeProvider.GetUtcNow() - creationStartTime));
      }
      catch (Exception ex)
      {
        _coordinatorChannel.Writer.TryWrite(new EntryCreationFailedMessage(message.Key, ex));
      }
    }
  }

  private async Task RunResetterLoopAsync()
  {
    await foreach (var (key, @object, version) in _resetterChannel.Reader.ReadAllAsync())
    {
      try
      {
        await ResetObjectForReuseAsync(@object).WithTimeout(TimeSpan.FromSeconds(60));
        _coordinatorChannel.Writer.TryWrite(new EntryResetForReuseMessage(key, @object, version));
      }
      catch (Exception ex)
      {
        _coordinatorChannel.Writer.TryWrite(new EntryResetForReuseFailedMessage(key, ex));
      }
    }
  }

  private async Task RunDestroyerLoopAsync()
  {
    await foreach (var (key, @object) in _destroyerChannel.Reader.ReadAllAsync())
    {
      try
      {
        await DestroyObjectAsync(@object).WithTimeout(TimeSpan.FromSeconds(60));
      }
      catch (Exception ex)
      {
        _coordinatorChannel.Writer.TryWrite(new EntryDestructionFailedMessage(key, ex));
      }
    }
  }

  static TimeStats AddTimeStat(TimeStats stats, TimeSpan duration) =>
    new(
      TotalDuration: stats.TotalDuration + duration,
      TotalCount: stats.TotalCount + 1,
      LastDuration: duration
    );

  readonly record struct EntryCreatorMessage(TKey Key);
  readonly record struct EntryDestroyerMessage(TKey Key, TObject Object);
  readonly record struct EntryResetterMessage(TKey Key, TObject Object, long Version);

  interface ICoordinatorMessage;

  readonly record struct EntryRequestedMessage(TKey Key, TaskCompletionSource<BorrowResult> TaskCompletionSource) : ICoordinatorMessage;
  readonly record struct EntryCreatedMessage(TKey Key, TObject Object, TimeSpan CreationDuration) : ICoordinatorMessage;
  readonly record struct EntryCreationFailedMessage(TKey Key, Exception Exception) : ICoordinatorMessage;
  readonly record struct EntryDestructionFailedMessage(TKey Key, Exception Exception) : ICoordinatorMessage;
  readonly record struct EntryReturnedMessage(TKey Key, TObject Object, long Version, bool IsValidForReuse) : ICoordinatorMessage;
  readonly record struct EntryResetForReuseMessage(TKey Key, TObject Object, long Version) : ICoordinatorMessage;
  readonly record struct EntryResetForReuseFailedMessage(TKey Key, Exception Exception) : ICoordinatorMessage;

  readonly record struct RequestCancelledMessage(TKey Key, TaskCompletionSource<BorrowResult> TaskCompletionSource, CancellationToken CancellationToken) : ICoordinatorMessage;

  readonly record struct StoppingMessage : ICoordinatorMessage;
  readonly record struct MaintenanceMessage : ICoordinatorMessage;
  readonly record struct InvalidateMessage : ICoordinatorMessage;

  readonly record struct FreeEntry(TObject Object, DateTimeOffset TimeFreed);

  readonly record struct TimeStats(TimeSpan TotalDuration, int TotalCount, TimeSpan LastDuration);

  class PoolState(TKey key)
  {
    public TKey Key { get; } = key;
    public TimeStats CreationStats { get; set; }
    public TimeStats WaitStats { get; set; }

    public Queue<(TaskCompletionSource<BorrowResult> TaskCompletionSource, DateTimeOffset WaitStartTime)> Waiters { get; } = new();

    // use stack for the entry pool to always hand out the most recently used entry so unused entries can be expired
    public Stack<FreeEntry> FreeEntries { get; } = new();
    public int OutstandingRentals { get; set; }

    // for creation throttling
    public int OutstandingCreations { get; set; }
    public Queue<DateTimeOffset> CreationScheduledTimes { get; } = new();
    public LinkedList<(DateTimeOffset FailureTime, Exception Exception)> CreationFailureEvents { get; } = new();
  }

  class Rental(TRequest request, TObject @object, Func<ValueTask> returner) : IObjectPoolRental<TObject, TRequest>
  {
    public TRequest Request => request;
    public TObject Object => _disposed ? throw new ObjectDisposedException(nameof(Rental)) : @object;
    private bool _disposed;

    public async ValueTask DisposeAsync()
    {
      // ensure this can only be called once to not corrupt the pool
      ObjectDisposedException.ThrowIf(Interlocked.Exchange(ref _disposed, true), typeof(Rental));
      await returner();
    }
  }

  readonly record struct BorrowResult(TObject Object, long Version, int OutstandingRentals, int Waiters, int FreeEntries);

  protected abstract TKey DeriveKey(TRequest request);

  public virtual async Task<IObjectPoolRental<TObject, TRequest>> BorrowAsync(TRequest request, CancellationToken token)
  {
    using var activity = Activity.Current?.StartEvent("object_pool.borrow");

    var key = DeriveKey(request);
    Activity.Current?.AddTag("object_pool.key", key);

    while (true)
    {
      token.ThrowIfCancellationRequested();

      var taskCompletionSource = new TaskCompletionSource<BorrowResult>(TaskCreationOptions.RunContinuationsAsynchronously);

      await _coordinatorChannel.Writer.WriteAsync(new EntryRequestedMessage(key, taskCompletionSource), token);

      await using (token.Register(() => _coordinatorChannel.Writer.TryWrite(new RequestCancelledMessage(key, taskCompletionSource, token))))
      {
        var result = await taskCompletionSource.Task;
        Activity.Current?.AddTag("object_pool.outstanding_rentals", result.OutstandingRentals);
        Activity.Current?.AddTag("object_pool.waiters", result.Waiters);
        Activity.Current?.AddTag("object_pool.free_entries", result.FreeEntries);

        try
        {
          var rentalState = await OnBorrowingAsync(request, result.Object, token);
          return new Rental(request, result.Object, () => ReturnAsync(key, result.Object, rentalState, result.Version));
        }
        catch (Exception ex)
        {
          _coordinatorChannel.Writer.TryWrite(new EntryReturnedMessage(key, result.Object, result.Version, false));
          logger.LogError(ex, "Error borrowing entry for key {Key} (ignoring)", key);
        }
      }
    }
  }

  private async ValueTask ReturnAsync(TKey key, TObject @object, TRentalState state, long version)
  {
    try
    {
      var isValidForReuse = await OnReturnedAsync(@object, state);
      await _coordinatorChannel.Writer.WriteAsync(new EntryReturnedMessage(key, @object, version, isValidForReuse));
    }
    catch (Exception ex)
    {
      await _coordinatorChannel.Writer.WriteAsync(new EntryReturnedMessage(key, @object, version, false));
      logger.LogError(ex, "Error returning entry (ignoring)");
    }
  }

  protected abstract Task<TObject> CreateObjectAsync(TKey key);
  protected abstract Task DestroyObjectAsync(TObject @object);
  protected abstract Task<TRentalState> OnBorrowingAsync(TRequest request, TObject @object, CancellationToken token);
  protected virtual Task<bool> OnReturnedAsync(TObject @object, TRentalState state) => Task.FromResult(true);
  protected virtual Task ResetObjectForReuseAsync(TObject @object) => Task.FromResult(true);
}
