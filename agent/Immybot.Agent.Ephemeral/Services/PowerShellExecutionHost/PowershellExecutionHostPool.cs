using Immybot.Agent.Configuration.Options;
using Immybot.Shared.Abstractions.Device.Exceptions;
using Immybot.Shared.Abstractions.Device.Processes;
using Immybot.Shared.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Immybot.Agent.Ephemeral.Services.PowerShellExecutionHost;

public interface IPowerShellExecutionHostPool : IObjectPoolServer<IPowerShellExecutionHost, PowershellContext>;

public class PowerShellExecutionHostPool(
  IServiceProvider serviceProvider,
  ILogger<PowerShellExecutionHostPool> logger,
  TimeProvider timeProvider,
  IOptionsMonitor<PowerShellExecutionHostPoolOptions> hostPoolOptions)
  : ObjectPoolServer<
      IPowerShellExecutionHost,
      PowershellContext,
      PowershellContext,
      object?,
      PowerShellExecutionHostPoolOptions>(logger, timeProvider, hostPoolOptions),
    IPowerShellExecutionHostPool
{
  protected override async Task<IPowerShellExecutionHost> CreateObjectAsync(PowershellContext key)
  {
    logger.LogDebug("Creating new PowerShellExecutionHost");
    var host = serviceProvider.GetRequiredService<IPowerShellExecutionHost>();

    // TODO hmmmm this can fail indefinitely. should the object pool have a kind of circuit breaker for object creation so they don't have to time out and can propagate the creation error?
    logger.LogInformation("Host is uninitialized, initializing it now.");
    await host.InitializeHostAsync(key, CancellationToken.None);

    var hostStatus = await host.GetStatusAsync(CancellationToken.None);
    logger.LogInformation("Host is initialized, status: {HostStatus}", hostStatus);

    if (hostStatus != PowerShellHostStatus.Ready)
      throw new PSExecutionHostNotReadyException($"Host status request returned a non-usable status: {hostStatus}");

    return host;
  }

  protected override PowershellContext DeriveKey(PowershellContext request) =>
    request;

  protected override async Task DestroyObjectAsync(IPowerShellExecutionHost host) =>
    await host.DisposeAsync();

  protected override async Task<object?> OnBorrowingAsync(PowershellContext request, IPowerShellExecutionHost host, CancellationToken cancellationToken)
  {
    var hostStatus = await host.GetStatusAsync(cancellationToken);
    if (hostStatus != PowerShellHostStatus.Ready)
      throw new PSExecutionHostNotReadyException($"Host status request returned a non-usable status: {hostStatus}");

    return null;
  }

  protected override async Task<bool> OnReturnedAsync(IPowerShellExecutionHost host, object? state)
  {
    var hostStatus = await host.GetStatusAsync(CancellationToken.None);
    if (hostStatus == PowerShellHostStatus.Ready)
      logger.LogInformation("{HostPid} will be returned to pool.", host.PipeHostPID);
    else
      logger.LogWarning("{HostPid} will not be returned to pool - not in a reusable state ({@HostInfo}). Disposing.", host.PipeHostPID, host);

    return hostStatus == PowerShellHostStatus.Ready;
  }
}
