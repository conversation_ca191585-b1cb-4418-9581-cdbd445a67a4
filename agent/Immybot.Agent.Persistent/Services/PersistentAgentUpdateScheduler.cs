using CommunityToolkit.Mvvm.Messaging;
using DotNext.Threading;
using Immybot.Agent.Ephemeral.Extensions;
using Immybot.Agent.Persistent.Messages;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Immybot.Agent.Persistent.Services;

/// <summary> Responsible for background checks for new agent versions. </summary>
internal class PersistentAgentUpdateScheduler(
  IPersistentAgentUpdater updater,
  IMessenger messenger,
  ILogger<PersistentAgentUpdateScheduler> logger) : BackgroundService
{
  protected override async Task ExecuteAsync(CancellationToken stoppingToken)
  {
    logger.LogInformation("Starting persistent agent update background service.");

    var agentHubReconnectTrigger = new AsyncAutoResetEvent(initialState: false);
    var reconnectionWaitTimeout = TimeSpan.FromHours(1).WithJitter();

    logger.LogInformation("Registering handler for hub connection changes.");
    messenger.Register<HubConnectionStateChangedMessage>(this,
      (_, message) =>
      {
        if (message.NewState == HubConnectionState.Connected)
        {
          logger.LogInformation("Agent reconnected to hub. Signaling Reconnect trigger.");
          agentHubReconnectTrigger.Set();
        }
      });

    try
    {
      while (true)
      {
        stoppingToken.ThrowIfCancellationRequested();

        logger.LogInformation(
          "Waiting for agent hub reconnection event with a timeout of {TimeoutPeriod:N0} second(s).",
          reconnectionWaitTimeout.TotalSeconds);

        if (await agentHubReconnectTrigger.WaitAsync(reconnectionWaitTimeout, stoppingToken))
          logger.LogInformation("Agent hub connection state changed.  Checking for updated version.");
        else
          logger.LogInformation(
            "No hub reconnection event occurred within the {TimeoutPeriod:N0} second timeout period.  Continuing with scheduled update check.",
            reconnectionWaitTimeout.TotalSeconds);

        try
        {
          await updater.EnsureLatestVersion(stoppingToken);
        }
        catch (HttpRequestException ex)
        {
          logger.LogWarning(
            ex,
            "Exception while checking for new agent version.  HTTP Status Code: {StatusCode}",
            ex.StatusCode);
        }
        catch (Exception ex)
        {
          logger.LogWarning(ex, "Exception while checking for new agent version.");
        }
      }
    }
    catch (OperationCanceledException)
    {
      logger.LogInformation("Persistent agent update background service stoppingToken has been cancelled.");
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "An unexpected error occurred in the persistent agent update background service.");
    }
    finally
    {
      logger.LogInformation("Unregistering handler for hub connection changes.");
      messenger.Unregister<HubConnectionStateChangedMessage>(this);
    }
  }
}
