using Immybot.Agent.Configuration.Options;
using Immybot.Agent.Configuration;
using Immybot.Shared.Primitives;
using Microsoft.Extensions.Hosting;
using Immybot.Shared.Abstractions.Device;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Immybot.Shared.Abstractions.Device.FileSystem;
using Immybot.Shared.Abstractions.Device.Processes;
using Immybot.Shared.Abstractions.Device.Timers;
using System.IO.Abstractions;
using System.Net;
using Immybot.Agent.Startup.Shared.Services;
using Immybot.Shared.Abstractions.Device.Windows;
using Immybot.Shared.Abstractions.Device.Windows.Native;
using CommunityToolkit.Mvvm.Messaging;
using Immybot.Shared.Abstractions.Device.Windows.Threading;
using Microsoft.Extensions.Logging;
using Serilog.Events;
using Serilog;
using Serilog.Formatting.Compact;
using Microsoft.Extensions.Logging.Console;
using Immybot.Agent.Startup.Shared.Logging;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Refit;
using Immybot.Agent.Http;
using Immybot.Shared.Extensions;
using Microsoft.Extensions.Hosting.WindowsServices;
using Microsoft.Extensions.Logging.EventLog;

namespace Immybot.Agent.Startup.Shared;

public static class HostConfigurationExtensions
{
  private static bool _serviceDefaultsAdded;

  public static IHostApplicationBuilder AddBackendApiClient(
    this IHostApplicationBuilder builder,
    Uri immyBackendBaseUrl)
  {
    builder.Services
      .AddRefitClient<IBackendApi>()
      .ConfigureHttpClient(client =>
      {
        client.BaseAddress = immyBackendBaseUrl.ToHttpUri().GetOrigin();
      });

    return builder;
  }

  public static IHostApplicationBuilder AddSharedConfiguration(this IHostApplicationBuilder builder, StartupMode startupMode)
  {
    if (WindowsServiceHelpers.IsWindowsService())
      builder.Services.AddSingleton<IHostLifetime, AgentServiceLifetime>();

    var appDir = SystemEnvironment.Instance.StartupDirectory ?? AppContext.BaseDirectory;
    var logsDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "ImmyBot", "Logs");

    builder.Configuration
      .AddJsonFile(Path.Join(appDir, "appsettings.json"), optional: true, reloadOnChange: true)
      .AddJsonFile(Path.Join(appDir, $"appsettings.{builder.Environment.EnvironmentName}.json"), optional: true, reloadOnChange: true)
      .AddEnvironmentVariables(AppConstants.AgentEnvironmentOptionsPrefix)
      .AddInMemoryCollection(new Dictionary<string, string?>()
      {
        [$"{LoggingOptions.SectionKey}:{nameof(LoggingOptions.LogPath)}"] = logsDir,
      })
      .AddInMemoryCollection(new Dictionary<string, string?>()
      {
        [$"{AgentStartupOptions.SectionKey}:{nameof(AgentStartupOptions.StartupMode)}"] = startupMode.ToString(),
      });

    builder.Services.Configure<DeveloperOptions>(
      builder.Configuration.GetSection(DeveloperOptions.SectionKey));

    builder.Services.Configure<AgentStartupOptions>(
      builder.Configuration.GetSection(AgentStartupOptions.SectionKey));

    var loggingOptions = builder.Configuration
      .GetSection(LoggingOptions.SectionKey)
      .Get<LoggingOptions>();

    if (string.IsNullOrWhiteSpace(loggingOptions?.LogPath))
    {
      throw new InvalidOperationException($"{nameof(LoggingOptions)}.{nameof(LoggingOptions.LogPath)} must be set.");
    }

    Log.Logger = new LoggerConfiguration()
      .Enrich.FromLogContext()
      .Enrich.WithProcessName()
      .Enrich.WithProcessId()
      .Enrich.WithThreadId()
      .Enrich.With<AgentVersionEnricher>()
      .Enrich.WithProperty(nameof(StartupMode), startupMode.ToString())
      .MinimumLevel.Debug() // match the default Debug level in appsettings.json so serilog doesn't filter out logs after Microsoft.Extensions.Logging lets them through
      .WriteTo.Map(
        keyPropertyName: "FromPipeHostPID",
        defaultKey: "",
        (pipePid, wt) =>
        {
          var isNormalLog = string.IsNullOrWhiteSpace(pipePid);
          wt.File(new CompactJsonFormatter(),
            isNormalLog ? $"{loggingOptions.LogPath}\\Log.clef" : $"{loggingOptions.LogPath}\\PSPipeHostLog.clef",
            rollingInterval: RollingInterval.Day,
            retainedFileTimeLimit: TimeSpan.FromDays(7),
            fileSizeLimitBytes: 10_000_000,
            rollOnFileSizeLimit: true,
            shared: true);
        },
        sinkMapCountLimit: 2)
      .CreateLogger();

    builder.Logging.AddSerilog();

    builder.Logging
      .AddEventLog(eventLogSettings =>
      {
        // If we don't specify this, SourceName will default to ".NET Runtime"
        // We can specify our own source name here as long as we are running as an admin. Which, for rest of agent to work properly, we should be.
        eventLogSettings.SourceName = "ImmyBot";
      });

    builder.Logging
      .AddSimpleConsole(config =>
      {
        config.TimestampFormat = "yyyy-MM-ddTHH:mm:ss.fffffffZ";
        config.UseUtcTimestamp = true;
        config.IncludeScopes = true;
        config.ColorBehavior = LoggerColorBehavior.Enabled;
      });

    return builder;
  }

  public static IHostApplicationBuilder AddSharedServices(this IHostApplicationBuilder builder)
  {
    var services = builder.Services;

    if (!_serviceDefaultsAdded)
    {
      _serviceDefaultsAdded = true;
      if (builder.Environment.IsAspire())
      {
        builder.AddServiceDefaults();
      }
    }

    services.TryAddSingleton<IFileSystem, FileSystem>();
    services.TryAddSingleton<IProcessManager, ProcessManager>();
    services.TryAddSingleton<ISystemEnvironment, SystemEnvironment>();
    services.TryAddSingleton(TimeProvider.System);
    services.TryAddSingleton<IAgentVersionProvider, AgentVersionProvider>();
    services.TryAddSingleton<IWebProxy, MitmSystemWebProxy>();
    services.TryAddSingleton<IElevationChecker, ElevationChecker>();
    services.TryAddSingleton<IPowerControl, PowerControl>();
    services.TryAddSingleton<IRegistryAccessor, RegistryAccessor>();
    services.TryAddSingleton<IRemoteRegistryProvider, RemoteRegistryProvider>();
    services.TryAddSingleton<IServiceController, ServiceControllerImpl>();
    services.TryAddSingleton<IWin32Interop, Win32Interop>();
    services.TryAddSingleton<IFileVersionProvider, FileVersionProvider>();
    services.TryAddSingleton<ITimerFactory, TimerFactory>();
    services.TryAddSingleton<IMessenger>(StrongReferenceMessenger.Default);
    services.TryAddSingleton<IGlobalEventWaitProvider, GlobalEventWaitProvider>();
    services.TryAddSingleton<IGlobalSemaphoreProvider, GlobalSemaphoreProvider>();
    services.TryAddSingleton<IGlobalMutexProvider, GlobalMutexProvider>();
    services.TryAddSingleton<IDelayer, Delayer>();

    return builder;
  }
}
